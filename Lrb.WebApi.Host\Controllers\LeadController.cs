﻿using Lrb.Application.Campaigns.web.Request;
using Lrb.Application.ChannelPartner.Web.Request;
using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Lead;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Export;
using Lrb.Application.Lead.Web.Requests;
using Lrb.Application.Lead.Web.Requests.AssignmentRequests;
using Lrb.Application.Lead.Web.Requests.CreationRequests;
using Lrb.Application.Lead.Web.Requests.DeletionRequests;
using Lrb.Application.Lead.Web.Requests.GetRequests;
using Lrb.Application.Lead.Web.Requests.OtherRequests;
using Lrb.Application.Lead.Web.Requests.UpdationRequests;
using Lrb.Application.LeadGenRequests;
using Lrb.Domain.Entities;
using Lrb.Domain.Enums;
using Mapster;
using Newtonsoft.Json;
using static Lrb.Application.Lead.Web.Requests.GetMatchingPropertiesByLeadIdRequestHandler;

namespace Lrb.WebApi.Host.Controllers
{
    [Authorize]
    public class LeadController : VersionedApiController
    {
        private readonly ITenantIndependentRepository _repository;
        private readonly Serilog.ILogger _logger;
        private readonly Lrb.Application.Common.ServiceBus.IServiceBus _serviceBus;
        private readonly ICurrentUser _currentUser;

        public LeadController(ITenantIndependentRepository repository, Serilog.ILogger logger, Lrb.Application.Common.ServiceBus.IServiceBus serviceBus, ICurrentUser currentUser)
        {
            _repository = repository;
            _logger = logger;
            _serviceBus = serviceBus;
            _currentUser = currentUser;
        }
        [HttpGet]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Search leads using available filters.", "")]
        public async Task<PagedResponse<ViewLeadDto, LeadCountDto>> SearchAsync([FromQuery] GetAllLeadsRequest request)
        {
            var response = await Mediator.Send(request);
            //_logger.Information($"LeadController -> GetAllLeads, Leads:" + JsonConvert.SerializeObject(response));
            return response;
        }
        [AllowAnonymous]
        [HttpGet("anonymous")]
        [TenantIdHeader]
        [OpenApiOperation("Get all leads using available filters.", "")]
        public async Task<PagedResponse<PullViewLeadDto, string>> SearchAsync([FromQuery] GetAllLeadsAnonymousRequest request)
        {
            var response = await Mediator.Send(request);
            if (HttpContext.Request.Headers.TryGetValue(Lrb.Shared.Multitenancy.MultitenancyConstants.TenantIdName, out var tenantIdValues))
            {
               string? tenantId = tenantIdValues.FirstOrDefault();
                _logger.Information("GetAllLeadsAnonymousRequest info: PageNumber={PageNumber}, PageSize={PageSize}, TenantId={TenantId}",request.PageNumber, request.PageSize, tenantId);

            }
            return response;
        }
        [HttpGet("additionalProperties/values")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get additionalProperties values", "")]
        public async Task<Response<List<string>>> GetValuesAsync([FromQuery] string key)
        {
            return await Mediator.Send(new GetAdditionalPropertyValuesRequest(key));
        }
        [HttpGet("additionalProperties/Keys")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get additionalProperties keys", "")]
        public async Task<Response<List<string>>> GetKeysAsync()
        {
            return await Mediator.Send(new GetAdditionalPropertyKeysRequest());
        }

        [HttpGet("new")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Search leads using available filters New Requirements.", "")]
        public async Task<PagedResponse<ViewLeadDto, LeadCountsByNewFilterDto>> SearchAsync([FromQuery] GetAllLeadsByNewFiltersRequest request)
        {
            var response = await Mediator.Send(request);
            //_logger.Information($"LeadController -> GetAllLeads, Leads:" + JsonConvert.SerializeObject(response));
            return response;
        }


        [HttpGet("counts")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Search leads using available filters.", "")]
        public async Task<Response<LeadCountDto>> GetCountAsync([FromQuery] GetAllLeadCountsRequest request)
        {
            var response = await Mediator.Send(request);
            //_logger.Information($"LeadController -> GetAllLeads, Leads:" + JsonConvert.SerializeObject(response));
            return response;
        }
        [HttpGet("counts/basefilter")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get Leads Count By BaseLeadFilter types.", "")]
        public async Task<Response<LeadCountsByBaseFiltersDto>> GetBaseCountAsync([FromQuery] GetAllLeadCountsByBaseFilterRequest request)
        {
            var response = await Mediator.Send(request);
            return response;
        }
        [HttpGet("all")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Search leads using available filters.", "")]
        public async Task<PagedResponse<ViewLeadDto, string>> GetAllAsync([FromQuery] GetAllLeadsOnlyRequest request)
        {

            var response = await Mediator.Send(request);
            //_logger.Information($"LeadController -> GetAllLeads, Leads:" + JsonConvert.SerializeObject(response));
            return response;
        }
        //new Implimentions
        [HttpGet("{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get lead details.", "")]
        public Task<Response<ViewLeadDto>> GetAsync(Guid id)
        {
            return Mediator.Send(new GetLeadByIdRequest(id));
        }

        [HttpGet("getUnAssignLeads")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.ViewUnAssignedLead, LrbResource.Leads)]
        [OpenApiOperation("Get all unassign leads details.", "")]
        public async Task<PagedResponse<ViewLeadDto, string>> GetAsync([FromQuery] GetUnAssignLeadsRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("getLeads/{accountId:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.ViewUnAssignedLead, LrbResource.Leads)]
        [OpenApiOperation("Get all leads by account id.", "")]
        public async Task<PagedResponse<ViewLeadDto, string>> GetLeadsAsync(Guid accountId)
        {
            return await Mediator.Send(new GetLeadsByAccountIdRequest(accountId));
        }

        [HttpPost]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Leads)]
        [OpenApiOperation("Create a new lead.", "")]
        public Task<Response<Guid>> CreateAsync(CreateLeadDto dto)
        {
                CreateLeadGenRequest leadGenRequest = new(LeadSource.Direct, dto);
                var req = Mediator.Send(leadGenRequest).Result;
                CreateLeadRequest request = dto.Adapt<CreateLeadRequest>();
                return Mediator.Send(request);
        }

        [HttpPost("QR")]
        [AllowAnonymous]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Leads)]
        [OpenApiOperation("Create a new lead from QR Code.", "")]
        public Task<Response<Guid>> CreateFromQRAsync(CreateLeadDto dto, Guid templateId)
        {
            CreateLeadGenRequest leadGenRequest = new(LeadSource.QRCode, dto);
            var req = Mediator.Send(leadGenRequest).Result;
            CreateLeadRequest request = dto.Adapt<CreateLeadRequest>();
            request.Enquiry ??= new();
            request.Enquiry.LeadSource = LeadSource.QRCode;
            request.TemplateId = templateId;
            return Mediator.Send(request);
        }

        [HttpPost("excel")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.BulkUpload, LrbResource.Leads)]
        [OpenApiOperation("Upload excel File")]
        public async Task<ActionResult<Response<Application.Lead.Web.FileColumnDto>>> UploadExcelFileAsync(IFormFile file)
        {
            try
            {
                //   _logger.Information("LeadController -> UploadExcelFileAsync, File: " + JsonConvert.SerializeObject(file));
                return await Mediator.Send(new Application.Lead.Web.Requests.Bulk_upload_new_implementation.GetExcelColumnsUsingEPPlusRequest(file));
            }
            catch (Exception e)
            {
                _logger.Error("LeadController -> UploadExcelFileAsync, Error: " + JsonConvert.SerializeObject(e));
                throw;
            }

        }
        //notused
        [HttpPost("bulk")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Leads)]
        [OpenApiOperation("Create new leads by excel.", "")]
        public Task<Response<BulkLeadDetail>> CreateBulkAsync(CreateBulkLeadRequest request)
        {
            _logger.Information("LeadController -> createLeadRequest, File: " + JsonConvert.SerializeObject(request));
            return Mediator.Send(request);
        }
        //notused
        [HttpPost("bulk/lambda")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Leads)]
        [OpenApiOperation("Create new leads by excel.", "")]
        public Task<Response<BulkLeadUploadTracker>> CreateBulkAsync(CreateBulkLeadFromExcelRequest request)
        {
            _logger.Information("LeadController -> createLeadRequest, File: " + JsonConvert.SerializeObject(request));
            return Mediator.Send(request);
        }

        //[HttpPost("bulk")]
        //[TenantIdHeader]
        //[MustHavePermission(LrbAction.Create, LrbResource.Leads)]
        //[OpenApiOperation("Create new leads by excel.", "")]
        //public Task<Response<BulkLeadUploadTracker>> CreateBulkAsync(CreateBulkLeadRequestUsingEPPlus request)
        //{
        //    _logger.Information("LeadController -> createLeadRequest, File: " + JsonConvert.SerializeObject(request));
        //    return Mediator.Send(request);
        //}

        [HttpPost("batch")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.BulkUpload, LrbResource.Leads)]
        [OpenApiOperation("Create new leads by excel.", "")]
        public Task<Response<BulkLeadUploadTracker>> CreateBulkAsync(Application.Lead.Web.Requests.Bulk_upload_new_implementation.RunAWSBatchForBulkleadUploadRequest request)
        {
            _logger.Information("LeadController -> createLeadRequest, File: " + JsonConvert.SerializeObject(request));
            return Mediator.Send(request);
        }

        [HttpPost("migrate/batch")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.BulkUpload, LrbResource.Leads)]
        [OpenApiOperation("Create new leads by excel.", "")]
        public Task<Response<LeadMigrateTracker>> MigrateBulkAsync(Application.Lead.Web.Requests.Bulk_upload_new_implementation.RunAWSBatchForBulkleadMigrateRequest request)
        {
            _logger.Information("LeadController -> createLeadRequest, File: " + JsonConvert.SerializeObject(request));
            return Mediator.Send(request);
        }

        [HttpGet("bulk/trackers")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.BulkUpload, LrbResource.Leads)]
        [OpenApiOperation("Get all bulk lead upload trackers", "")]
        public async Task<PagedResponse<BulkLeadUploadTracker, string>> GetAllTrackers([FromQuery] Application.Lead.Web.Requests.BulkUploadTracker.GetAllBulkUploadTrackersRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("migrate/bulk/trackers")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.BulkUpload, LrbResource.Leads)]
        [OpenApiOperation("Get all bulk lead migrate trackers", "")]
        public async Task<PagedResponse<LeadMigrateTracker, string>> GetAllTrackers([FromQuery] Application.Lead.Web.Requests.BulkUploadTracker.GetAllBulkMigrateTrackersRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPut("{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Leads)]
        [OpenApiOperation("Update a lead.", "")]
        public async Task<ActionResult<Guid>> UpdateAsync(UpdateLeadDto dto, Guid id)
        {
            return id != dto.Id
                ? BadRequest()
                : Ok(await Mediator.Send(dto.Adapt<UpdateLeadRequest>()));
        }
        [HttpPut("notes/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.UpdateNotes, LrbResource.Leads)]
        [OpenApiOperation("Update notes of a lead.", "")]
        public async Task<ActionResult<bool>> UpdateNoteAsync(UpdateLeadNoteRequest request, Guid id)
        {
            return id != request.Id
                ? BadRequest()
                : Ok(await Mediator.Send(request));
        }
        [HttpPut("status/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.UpdateLeadStatus, LrbResource.Leads)]
        [OpenApiOperation("Update status of a lead.", "")] 
        public async Task<ActionResult<bool>> UpdateStatusAsync(UpdateLeadStatusRequest request, Guid id)
        {
            return id != request.Id
                ? BadRequest()
                : 
                
                Ok( await Mediator.Send(request.Adapt<ProcessBulkStatusUpdateRequest>()));
        }
        [HttpPut("status/bulk")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.BulkUpdateStatus, LrbResource.Leads)]
        [OpenApiOperation("Update status of a lead.", "")]
        public async Task<PagedResponse<DuplicateLeadAssigmentResponseDto, CountDto>> UpdateBulkStatusAsync(ProcessBulkStatusUpdateRequest request)
        {
            request.BulkCategory = BulkType.BulkUpdateStatus;
            if (request?.LeadIds?.Count >= 25)
            {
                var tenantId = _currentUser.GetTenant();
                var currentUser = _currentUser.GetUserId();
                request.CurrentUserId = currentUser;
                request.TenantId = tenantId;
                var payload = new InputPayload(tenantId ?? string.Empty, currentUser, request);
                await _serviceBus.RunBulkStatusUpdateJobAsync(payload);
            }
            else
            {
                await Mediator.Send(request);
            }
            return new() { Succeeded = true};
        }

        [HttpPut("projects/bulk")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.BulkProjectAssignment, LrbResource.Leads)]
        [OpenApiOperation("Update Projects of a lead.", "")]
        //public async Task<ActionResult<Response<Guid>>> UpdateBulkStatusAsync(UpdateBulkProjectsListRequest request)
        //{
        //    return Ok(await Mediator.Send(request));
        //}
        public async Task<PagedResponse<DuplicateLeadAssigmentResponseDto, CountDto>> UpdateBulkStatusAsync(ProcessBulkProjectUpdateRequest request)
        {
            request.BulkCategory = BulkType.BulkUpdateStatus;
            if (request?.Ids?.Count >= 25)
            {
                var tenantId = _currentUser.GetTenant();
                var currentUser = _currentUser.GetUserId();
                request.CurrentUserId = currentUser;
                request.TenantId = tenantId;
                var payload = new InputPayload(tenantId ?? string.Empty, currentUser, request);
                await _serviceBus.RunBulkProjectUpdateJobAsync(payload);
            }
            else
            {
                await Mediator.Send(request);
            }
            return new() {Succeeded = true };
        }

        [HttpPut("shareCount/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Leads)]
        [OpenApiOperation("Update shareCount of a lead.", "")]
        public async Task<ActionResult<bool>> UpdateShareCountAsync(UpdateLeadShareCountRequest request, Guid id)
        {
            return id != request.Id
                ? BadRequest()
                : Ok(await Mediator.Send(request));
        }
        [HttpPut("tag/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.UpdateTags, LrbResource.Leads)]
        [OpenApiOperation("Update tags of a lead.", "")]
        public async Task<ActionResult<bool>> UpdateTagAsync(UpdateLeadTagRequest request, Guid id)
        {
            return id != request.Id
                ? BadRequest()
                : Ok(await Mediator.Send(request));
        }
        [HttpPost("assign")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Assign, LrbResource.Leads)]
        [OpenApiOperation("Assign leads to a user.", "")]
        public async Task<Response<DuplicateLeadAssigmentResponseDto>> AssignLeadsAsync(AssignLeadRequest request)
        {
            var res = await Mediator.Send(request);
            return res;
        }
        [HttpPost("assign/user")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Assign, LrbResource.Leads)]
        [OpenApiOperation("Assign leads of a user to multiple user.", "")]
        public async Task<Response<bool>> AssignLeadsAsync(AssignLeadsOfUserRequest request)
        {
            var res = await Mediator.Send(request);
            return res;
        }
        [HttpGet("assigned/{userId}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all assigned leads of an user.", "")]
        public async Task<PagedResponse<ViewLeadDto, string>> GetAssignedLeadsAsync(Guid userId)
        {
            var res = await Mediator.Send(new GetLeadsByUserIdRequest(userId));
            return res;
        }
        [HttpGet("history/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get LeadHistory of a lead.", "")]
        public async Task<Response<Dictionary<DateTime, List<LeadHistoryDto>>>> GetHistoriesAsync(Guid id)
        {
            var res = await Mediator.Send(new GetLeadHsitoryByIdRequest(id));
            return res;
        }
        [HttpGet("contactNo")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get Lead Id.", "")]
        public async Task<Response<LeadContactDto>> GetAsync(string contactNo,string countryCode)
        {
            var response = await Mediator.Send(new GetLeadIdByContactNoRequest(contactNo,countryCode));
            return response;
        }
        
        [HttpGet("getAll")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Search leads using available filters.", "")]
        public async Task<PagedResponse<ViewLeadDto, string>> GetAllAsync([FromQuery] GetAllLeadRequestDapper request)
        {
            var response = await Mediator.Send(request);
            _logger.Information($"LeadController -> GetAllLeads, Leads:" + JsonConvert.SerializeObject(response));
            return response;
        }
        [HttpPut("document/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.UpdateDocuments, LrbResource.Leads)]
        [OpenApiOperation("Upload a document in lead", "")]
        public async Task<ActionResult<Response<bool>>> PutAsync(UploadLeadDocumentRequest request, Guid id)
        {
            return id != request.Id
                ? BadRequest()
                : Ok(await Mediator.Send(request));
        }
        [HttpDelete("Softdelete")]
        [TenantIdHeader]
        [OpenApiOperation("Delete Lead")]
        public async Task<ActionResult<Response<bool>>> SoftDeleteAsync(SoftDeleteLeadRequest request)
        {
            return Ok(await Mediator.Send(request));
        }
        [HttpGet("SoftDeletedleads")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Delete, LrbResource.Leads)]
        [OpenApiOperation("get deleted leads")]
        public async Task<ActionResult<PagedResponse<ViewLeadDto, string>>> GetAsync([FromQuery] GetArchiveLeadsRequest request)
        {
            var response = await Mediator.Send(request);
            return response;
        }
        [HttpGet("properties")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get all properties.", "")]
        //Todo: Move this to Properties Controller
        public async Task<Response<List<string>>> GetPropertiesAsync()
        {
            return await Mediator.Send(new GetAllPropertiesRequest(_currentUser?.GetTenant() ?? string.Empty));
        }
        [AllowAnonymous]
        [HttpGet("QR/properties")]
        [TenantIdHeader]
        [OpenApiOperation("Get all properties.", "")]
        //Todo: Move this to Properties Controller
        public async Task<Response<List<string>>> GetQRPropertiesAsync()
        {
            var tenantId = HttpContext.Request.Headers["tenant"].ToString() ?? _currentUser?.GetTenant() ?? string.Empty;
            return await Mediator.Send(new GetAllPropertiesRequest(tenantId));
        }
        [HttpGet("projects")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get all projects.", "")]
        //Todo: Move this to Project Controller
        public async Task<Response<List<string>>> GetProjectsAsync()
        {
            return await Mediator.Send(new GetAllProjectsRequest(_currentUser?.GetTenant() ?? string.Empty));
        }
        [AllowAnonymous]
        [HttpGet("QR/projects")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get all projects.", "")]
        //Todo: Move this to Project Controller
        public async Task<Response<List<string>>> GetQRProjectsAsync()
        {
            var tenantId = HttpContext.Request.Headers["tenant"].ToString() ?? _currentUser?.GetTenant() ?? string.Empty;

            return await Mediator.Send(new GetAllProjectsRequest(tenantId));
        }
        //[HttpPost("bulkLeadNotification")]
        //[TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Leads)]
        //[OpenApiOperation("Send notification for Bulk Lead Upload.", "")]
        //public async Task<Response<bool>> SendPushNotificationAsync(SendNotificationForBulkUploadRequest request)
        //{
        //    return await Mediator.Send(request);
        //}
        [HttpPut("contactCount/{id:guid}")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Update, LrbResource.Leads)]
        [OpenApiOperation("Update contact count of a lead", "")]
        public async Task<ActionResult<Response<bool>>> PutAsync(UpdateLeadContactCountRequest request, Guid id)
        {
            return id != request.Id
                ? BadRequest()
                : Ok(await Mediator.Send(request)); 
        }

        [HttpGet("addresses")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all leads address.", "")]
        public async Task<Response<List<string>>> GetAddressesAsync()
        {
            return await Mediator.Send(new GetAllLeadAddressRequest());
        }
        [HttpPut("restoreLeads")]
        [TenantIdHeader]
        [OpenApiOperation("Restore deleted leads", "")]
        public async Task<Response<bool>> PutAsync(UpdateArchiveLeadsRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("documents/{leadId:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all documents by lead id", "")]
        public async Task<Response<List<LeadDocumentDto>>> GetDocumentsAsync(Guid leadId)
        {
            return await Mediator.Send(new GetAllDocumentByLeadIdRequest(leadId));
        }
        [HttpPut("MeetingOrSiteVisitDone")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.UpdateLeadStatus, LrbResource.Leads)]
        [OpenApiOperation("Update Meeting Or Site Visit Done", "")]
        public async Task<Response<bool>> PutAsync(UpdateSiteVisitOrMeetingDoneRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("basicInfo/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get lead basic info by lead id", "")]
        public async Task<Response<ViewLeadDto>> GetLeadBasicInfoAsync(Guid id)
        {
            return await Mediator.Send(new GetLeadBasicInfoByIdRequest(id));
        }
        [HttpGet("leadSearch")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Search lead details.", "")]
        public async Task<PagedResponse<ViewLeadDto, string>> GetAsync([FromQuery] LeadSearchRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("matchingProperties")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get Matching Properties.", "")]
        public async Task<PagedResponse<PropertyWithDegreeMatched, string>> GetMatchingPropertiesAsync([FromQuery] GetMatchingPropertiesByLeadIdRequest request)
        {
            return (await Mediator.Send(request));
        }
        [HttpPost("documents")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Leads)]
        [OpenApiOperation("Add Lead Documents")]

        public async Task<Response<bool>> AddAsync(AddDocumentsRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpDelete("documents")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Leads)]
        [OpenApiOperation("Delete Lead Documents")]

        public async Task<Response<bool>> DeleteAsync(DeleteDocumentRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("export")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Export, LrbResource.Leads)]
        [OpenApiOperation("Export Leads.", "")]
        public async Task<Response<string>> GetAsync([FromQuery] ExportLeadsRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("export/batch")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Export, LrbResource.Leads)]
        [OpenApiOperation("export leads by excel.", "")]
        public Task<Response<Guid>> ExportLeadsAsync(RunAWSBatchForExportLeadsRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpGet("export/trackers")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all export lead trackers", "")]
        public async Task<PagedResponse<ExportLeadTrackerDto, string>> GetAllTrackers([FromQuery] GetAllExportLeadTracker request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("subsource")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all leads subsource.", "")]
        public async Task<Response<Dictionary<LeadSource, List<string?>?>>> GetAllSubSourceAsync()
        {
            return await Mediator.Send(new GetAllLeadsSubSourceRequest());
        }
        [HttpPut("source/bulk")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.BulkUpdateSource, LrbResource.Leads)]
        [OpenApiOperation("Update leads source.", "")]
        public async Task<Response<bool>> UpdateBulkSourceAsync(ProcessLeadSourceUpdateRequest request)
        {
            if (request.LeadIds?.Count > 1)
            {
                request.BulkCategory = BulkType.BulkUpdateStatus;
            }
            if (request?.LeadIds?.Count >= 25)
            {
                var tenantId = _currentUser.GetTenant();
                var currentUser = _currentUser.GetUserId();
                request.CurrentUserId = currentUser;
                request.TenantId = tenantId;
                var payload = new InputPayload(tenantId ?? string.Empty, currentUser, request);
                await _serviceBus.RunBulkLeadSourceUpdateJobAsync(payload);
            }
            else
            {
                return await Mediator.Send(request);
            }
            return new(true);
        }
        [HttpGet("agencynames")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get assigned agency names in leads", "")]
        public async Task<Response<List<string>>> GetAgencyAsync()
        {
            return await Mediator.Send(new GetAgencyNamesRequest());
        }


        #region duplicateLead
        //[HttpPost("duplicate/assign")]
        //[TenantIdHeader]
        //[MustHavePermission(LrbAction.Assign, LrbResource.Leads)]
        //[OpenApiOperation("Assign Leads")]
        //public async Task<Response<bool>> AssigLeads(AssignLeadsRequest request)
        //{
        //    return await Mediator.Send(request);
        //}


        [HttpGet("duplicate/feature")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Assign, LrbResource.Leads)]
        [OpenApiOperation("Get Duplicate lead feature Info")]

        public async Task<Response<bool>> GetDuplicateLeadFeatureInfo()
        {
            var res = await Mediator.Send(new GetDuplicateLeadFeatureInfo());
            return res;
        }
        [HttpPost("duplicate/assign")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Assign, LrbResource.Leads)]
        [OpenApiOperation("Assign Leads")]
        public async Task<PagedResponse<DuplicateLeadAssigmentResponseDto, CountDto>> AssigLeads(AssignAndCreateDuplicateLeadsRequest request)
        {
            return await Mediator.Send(request);
        }
        #endregion

        #region Lead Section Api's

        [HttpGet("counts/active")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get Active Lead Counts.", "")]
        public async Task<Response<ActiveLeadCountsDto>> GetCountsAsync([FromQuery] GetActiveLeadCountsRequest request)
        {
            var response = await Mediator.Send(request);
            return new(response);
        }
        [HttpGet("counts/notinterested")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get Not Interested Lead Counts.", "")]
        public async Task<Response<NotInterestedLeadsCountDto>> GetCountsAsync([FromQuery] GetNotInterestedLeadCountsRequest request)
        {
            var response = await Mediator.Send(request);
            return new(response);
        }
        [HttpGet("counts/Statuses")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get Lead Counts by Statuses .", "")]
        public async Task<Response<PagedResponse<LeadStatusFilterDto, string>>> GetCountsAsync([FromQuery] LeadsCountByStatusRequest request)
        {
            var response = await Mediator.Send(request);
            return new(response);
        }
        [HttpGet("counts/flags")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get Lead Counts by Flags.", "")]
        public async Task<Response<LeadCountsByFlagDto>> GetCountsAsync([FromQuery] GetLeadCountsByFlagsRequest request)
        {
            var response = await Mediator.Send(request);
            return new(response);
        }
        [HttpGet("new/all")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get All Leads Only by New Filters.", "")]
        public async Task<PagedResponse<ViewLeadDto, string>> SearchAsync([FromQuery] GetAllLeadsOnlyByNewFiltersRequest request)
        {
            var response = await Mediator.Send(request);
            return response;
        }
        [HttpGet("new/counts/basefilter")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get Lead Counts By BaseLeadFilter types By New Filters.", "")]
        public async Task<Response<LeadCountsByNewBaseFiltersDto>> GetCountsAsync([FromQuery] GetAllLeadCountsByBaseFiltersNewRequest request)
        {
            var response = await Mediator.Send(request);
            return response;
        }

        #endregion

        [HttpPost("export/new/batch")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Export, LrbResource.Leads)]
        [OpenApiOperation("export leads by excel.", "")]
        public async Task<Response<Guid>> ExportLeadsAsync(RunAWSBatchForExportLeadsByNewFiltersRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("excelvalidation")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Leads)]
        [OpenApiOperation("validate lead excel.", "")]

        public async Task<Response<string>> ValidateLeadsExcel(ValidateLeadExcelRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("validate/batch")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Leads)]
        [OpenApiOperation("validate lead excel.", "")]
        public Task<Response<ValidatedExcel>> ValidateLeadExcel(Application.Lead.Web.Requests.Bulk_upload_new_implementation.RunAWSBatchForValidateLeadExcelRequest request)
        {
            _logger.Information("LeadController -> createLeadRequest, File: " + JsonConvert.SerializeObject(request));
            return Mediator.Send(request);
        }
        [HttpPost("assign/multipleusers")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.BulkReassign, LrbResource.Leads)]
        [OpenApiOperation("Assign leads to multiple users.", "")]
        public async Task<PagedResponse<DuplicateLeadAssigmentResponseDto, CountDto>> AssignLeadsAsyncV2(ProcessBulkLeadAssignmentRequest request)
        {
            if (request.LeadIds.Count() > 1)
            {
                request.BulkCategory = BulkType.BulkAssignment;
            }
            if (request.LeadIds.Count >= 50)
            {
                var tenantId = _currentUser.GetTenant();
                var currentUser = _currentUser.GetUserId(); 
                request.CurrentUserId = currentUser;
                request.TenantId = tenantId;
                var payload = new InputPayload(tenantId ?? string.Empty, currentUser, request);
                await _serviceBus.RunBulkOpeartionJobAsync(payload);
                return new() { Succeeded = true };
            }
            else
            {                
                    var res = await Mediator.Send(request.Adapt<ProcessBulkLeadAssignmentRequest>());
                    return res;
            }

        }
        [HttpPost("message")]
        [TenantIdHeader]
        [OpenApiOperation("save lead message.", "")]
        //[MustHavePermission(LrbAction.Update, LrbResource.Leads)]
        public async Task<Response<bool>> SaceWhatsAppMessage(SaveLeadCommunicationRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("bulk/message")]
        [TenantIdHeader]
        [OpenApiOperation("save leads message.", "")]
        //[MustHavePermission(LrbAction.BulkEmail, LrbResource.Leads)]
        public async Task<Response<bool>> BulkMessageAsync(BulkUpdateLeadsCommunicationRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPut("bulk/contactCount")]
        [TenantIdHeader]
       // [MustHavePermission(LrbAction.BulkEmail, LrbResource.Leads)]
        [OpenApiOperation("Update contact count of a leads", "")]
        public async Task<ActionResult<Response<bool>>> PutAsync(BulkUpdateLeadsContactCountRequest request)
        {
            return await Mediator.Send(request);
        }

        #region mock
        #endregion

        [HttpPost("add-projects")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Create, LrbResource.Projects)]
        [OpenApiOperation("Add projects in lead.", "")]
        public async Task<Response<bool>> AddProjectsInLeadAsync(AddProjectsInLeadRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("getAppointmentsByProjects")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("get appointments by projects.", "")]
        public async Task<Response<List<AppointmentProjectDto>>> GetAppointmentsByProjectsAsync([FromQuery] GetAppointmentsByProjectsRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPut("assign/secondary-users")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.BulkSecondaryReassign, LrbResource.Leads)]
        [OpenApiOperation("Assign leads to multiple secondary users.", "")]
        public async Task<Response<List<DuplicateLeadAssigmentResponseDto>>> AssignLeadsAsync(ProcessSecondaryLeadAsssignmentRequest request)
        {
            if (request.LeadIds.Count() > 1)
            {
                request.BulkCategory = BulkType.BulkSecondaryAssignment;
            }
            if (request.LeadIds.Count >= 50)
            {
                var tenantId = _currentUser.GetTenant();
                var currentUser = _currentUser.GetUserId();
                request.CurrentUserId = currentUser;
                request.TenantId = tenantId;
                var payload = new InputPayload(tenantId ?? string.Empty, currentUser, request);
                await _serviceBus.RunBulkSecondaryLeadAssignmentJobAsync(payload);
                return new() { Succeeded = true };
            }
            else
            {
                return await Mediator.Send(request);
            }
        }
        [HttpPut("assign/new")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Assign, LrbResource.Leads)]
        [OpenApiOperation("Assign leads to a user.", "")]
        public async Task<Response<bool>> AssignLeadsAsync(V2AssignLeadRequest request)
        {
            var res = await Mediator.Send(request);
            return res;
        }

        [HttpGet("communications")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get lead communications details.", "")]
        public async Task<Response<Dictionary<Guid, Dictionary<ContactType, int>>>?> GetLeadCommunicationsAsync([FromQuery] GetLeadCommunicationsRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPut("booked/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Leads)]
        [OpenApiOperation("Update booked details by id.", "")]
        public async Task<Response<Guid>> BookLeadsAsync(Guid id, UpdateBookedDetailsRequest request)
        {
            var res = await Mediator.Send(request);
            return res;
        }
        [HttpGet("booked-details/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("View Booked details of  Lead.", "")]
        public async Task<Response<ViewBookedLeadDto>> GetBookLeadsAsync(Guid id)
        {
         return await Mediator.Send(new GetLeadBookedDetailsByIdRequest(id));
        }

        [HttpGet("cities")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all leads cities.", "")]
        public async Task<Response<List<string>>> GetCitiesAsync()
        {
            return await Mediator.Send(new GetAllLeadCitiesRequest());
        }

        [HttpGet("localites")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all leads localites.", "")]
        public async Task<Response<List<string>>> GetLocalitesAsync()
        {
            return await Mediator.Send(new GetAllLeadLocalitesRequest());
        }

        [HttpGet("states/new")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all leads States.", "")]
        public async Task<Response<List<string>>> GetStatesAsync()
        {
            return await Mediator.Send(new V2GetAllLeadStatesRequest());
        }

        [HttpGet("zones")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all leads zones.", "")]
        public async Task<Response<List<string>>> GetZonesAsync()
        {
            return await Mediator.Send(new GetAllLeadZonesRequest());
        }
        [HttpPut("customflag/{id:guid}")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.UpdateTags, LrbResource.Leads)]
        [OpenApiOperation("Update custom flag of a lead.", "")]
        public async Task<ActionResult<bool>> UpdateCustomFlagAsync(UpdateLeadCustomFlagRequest request, Guid id)
        {
            return id != request.Id
                ? BadRequest()
                : Ok(await Mediator.Send(request));
        }

        [HttpGet("counts/custom-flags")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get leads counts by custom flags.", "")]
        public async Task<Response<List<FlagsCountDto>>> GetCustomCountsAsync([FromQuery] GetLeadCountsByCustomFlagsRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("custom-filters-count")]
        [TenantIdHeader]
        [OpenApiOperation("Get leads count by custom filters.", "")]
        public async Task<Response<List<CustomFiltersDto>>> GetAsync([FromQuery] GetAllLeadsCountByCustomFiltersRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("custom-filters-count-level1")]
        [TenantIdHeader]
        [OpenApiOperation("Get leads count by custom filters.", "")]
        public async Task<Response<List<CustomFiltersDto>>> GetLevel1DataAsync([FromQuery] GetAllLeadsCountByCustomFiltersLevel1Request request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("custom-filters-count-level2")]
        [TenantIdHeader]
        [OpenApiOperation("Get leads count by custom filters.", "")]
        public async Task<Response<List<CustomFiltersDto>>> GetLevel2DataAsync([FromQuery] GetAllLeadsCountByCustomFiltersLevel2Request request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("custom-filters")]
        [TenantIdHeader]
        [OpenApiOperation("Get leads by custom filters.", "")]
        public async Task<PagedResponse<ViewLeadDto, string>> GetAsync([FromQuery] GetAllLeadsByCustomFiltersRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("custom-filter-export")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Export, LrbResource.Leads)]
        [OpenApiOperation("export leads by excel.", "")]
        public async Task<Response<Guid>> ExportLeadsAsync(RunAWSBatchForExportLeadsByCustomFiltersRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("Currency")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get All Currency.", "")]
        public async Task<Response<List<string>>> GetAllCuurencyy()
        {
            return await Mediator.Send(new GetAllCurrencyRequest());
        }

        [HttpGet("assignment/history")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get Lead Assignment Hsitories")]
        public async Task<Response<Dictionary<DateTime, LeadAssignmentHistoryVM>>> GetLeadAssignmentHistory(Guid leadId)
        {
            return await Mediator.Send(new GetLeadAssignmentHistoryByLeadIdRequest(leadId));
        }
        [HttpPut("status/new/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.UpdateLeadStatus, LrbResource.Leads)]
        [OpenApiOperation("Update status of a lead.", "")]
        public async Task<ActionResult<Response<bool>>> UpdateStatusAsync(V2UpdateLeadStatusRequest request, Guid id)
        {
            return id != request.Id
                ? BadRequest()
                : Ok(await Mediator.Send(request));
        }

        [HttpDelete("delete")]
        [TenantIdHeader]
        [OpenApiOperation("Delete Lead")]
        public async Task<Response<bool>> DeleteAsync(BulkDeleteLeadsRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("common-tracker")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Assign, LrbResource.Leads)]
        [OpenApiOperation("Get all common tracker", "")]
        public async Task<PagedResponse<BulkOperationCommonTrackerDto, string>> GetAllAsync([FromQuery]GetAllBulkOperationCommonTrackerRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("matchingProjects")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get Matching Projects.", "")]
        public async Task<PagedResponse<ProjectWithDegreeMatched, string>> GetMatchingProjectsAsync([FromQuery] GetMatchingProjectsByLeadIdRequest request)
        {
            return (await Mediator.Send(request));
        }
        [HttpGet("UploadTypeName")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get All UploadTypeName.", "")]
        public async Task<Response<List<string>>> GetAllUploadTypes()
        {
            return await Mediator.Send(new GetAllUploadTypeNameRequest());
        }

        [HttpPut("dreamyard-test")]
        [TenantIdHeader]
        public async Task<Response<bool>> UpdateDreamYard(LeadStatusBiasAutoAssignmentWithNewHistoryRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("channelPartners")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get channelPartners name", "")]
        public async Task<Response<List<string>>> GetChannelPartnersAsync()
        {
            return await Mediator.Send(new GetChannelPartnerNamesRequest());
        }
        [HttpGet("histories/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get LeadHistory of a lead.", "")]
        public async Task<Response<List<LeadHistoryDto>>> GetLeadHistoriesAsync(Guid id)
        {
            var res = await Mediator.Send(new GetLeadHistoriesByIdRequest(id));
            return res;
        }
        [HttpGet("Leadnotehistory/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get LeadnoteHistory of a lead.", "")]
        public async Task<Response<List<LeadHistoryDto>>> GetLeadNoteHistoriesAsync(Guid id)
        {
            var res = await Mediator.Send(new GetLeadNoteHistoryByIdRequest(id));
            return res;
        }
        [HttpGet("campaigns")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get assigned campaign names in leads", "")]
        public async Task<Response<List<string>>> GetCampaignsPartnersAsync()
        {
            return await Mediator.Send(new GetCampaignNamesRequest());
        }


        [HttpPut("click-link")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Leads)]
        [OpenApiOperation("Get All UploadTypeName.", "")]
        public async Task<Response<bool>> UpdateLink(UpdateLeadLinkClickCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("assignto/{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Check Lead Assignment by Lead Id.", "")]
        public async Task<bool> CheckAssignToByLeadIdAsync(Guid id, bool? canAccessAllLeads)
        {
            return await Mediator.Send(new CheckLeadAssignmentByIdRequest(id, canAccessAllLeads));
        }

        [HttpGet("countries")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all leads countries.", "")]
        public async Task<Response<List<string>>> GetCountriesAsync()
        {
            return await Mediator.Send(new GetAllLeadCountriesRequest());
        }
        [HttpGet("subCommunities")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all leads subCommunities.", "")]
        public async Task<Response<List<string>>> GetSubCommunitiesAsync()
        {
            return await Mediator.Send(new GetAllLeadSubCommunitiesRequest());
        }
        [HttpGet("communities")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all leads communities.", "")]
        public async Task<Response<List<string>>> GetCommunitiesAsync()
        {
            return await Mediator.Send(new GetAllLeadCommunitiesRequest());
        }
        [HttpGet("towerName")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all leads towerName.", "")]
        public async Task<Response<List<string>>> GetTowerNameAsync()
        {
            return await Mediator.Send(new GetAllLeadTowerNamesRequest());
        }
        [HttpGet("postalCode")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all leads postalCode.", "")]
        public async Task<Response<List<string>>> GetPostalAsync()
        {
            return await Mediator.Send(new GetAllLeadPostalCodeRequest());
        }
        [AllowAnonymous]
        [HttpGet("contactInfo")]
        [TenantIdHeader]
        [OpenApiOperation("Get Lead Info.", "")]
        public async Task<Response<ViewLeadDto>> GetLeadByContactNoAsync([FromQuery] GetLeadInfoByContactNoRequest request)
        {
            if (string.IsNullOrEmpty(request.ContactNo)) { throw new Exception("Contact No field can not be empty."); }
            var response = await Mediator.Send(request);
            return response;
        }
        [AllowAnonymous]
        [HttpGet("channelpartner/contactno")]
        [TenantIdHeader]
        [OpenApiOperation("Get Lead By ChannelPartner ContactNo.", "")]
        public async Task<Response<List<ViewLeadDto>>> GetLeadByChannelPartnerContactNoAsync([FromQuery] GetLeadsByChannelPartnerContactNoRequest request)
        {
            if (string.IsNullOrEmpty(request.ContactNo)) { throw new Exception("Channel Partner ContactNo field can not be empty."); }
            var response = await Mediator.Send(request);
            return response;
        }
        [AllowAnonymous]
        [HttpGet("check/channelpartner/contactno")]
        [TenantIdHeader]
        [OpenApiOperation("Check Lead Info By ChannelPartner ContactNo.", "")]
        public async Task<Response<bool>> CheckLeadByChannelPartnerContactNoAsync([FromQuery] CheckLeadsByChannelPartnerContactNoRequest request)
        {
            if (string.IsNullOrEmpty(request.ContactNo)) { throw new Exception(" Channel Partner ContactNo field can not be empty."); }
            var response = await Mediator.Send(request);
            return response;
        }
        [HttpGet("nationality")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get all lead natinalities.", "")]
        public async Task<Response<List<string>>> GetNationalitiesAsync()
        {
            return await Mediator.Send(new GetAllLeadNationalitiesRequest());
        }
        [HttpGet("unitname")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get all lead unitname.", "")]
        public async Task<Response<List<string>>> GetAsync()
        {
            return await Mediator.Send(new GetAllLeadUnitsRequest());
        }
        [HttpGet("clustername")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get all lead clustername.", "")]
        public async Task<Response<List<string>>> GetclusternameAsync()
        {
            return await Mediator.Send(new GetAllLeadClusterRequest());
        }
        [ApiKey]
        [AllowAnonymous]
        [HttpGet("CheckContact")]
        [OpenApiOperation("Check ContactNo.", "")]
        public async Task<Response<bool>> CheckLeadPresenceAsync(string contactNo, string countryCode)
        {
            var apiKey = this.HttpContext.Request.Headers["API-Key"];
            var response = await Mediator.Send(new VerifyLeadAvailabilityAsync(contactNo, countryCode));
            return response;
        }

        [HttpGet("all/parentLeads")]
        [TenantIdHeader]
        [OpenApiOperation("Get all lead parent lead.", "")]
        public async Task<Response<List<ParentLeadDto>>> GetAllParentLeadAsync( [FromQuery] GetParentLeadRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("parentLead/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get lead details.", "")]
        public Task<Response<ViewLeadDto>> GetParentLeadAsync(Guid id)
        {
            return Mediator.Send(new GetParentLeadByIdRequest(id));
        }
        [HttpGet("all/properties")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all lead properties.", "")]
        public async Task<Response<List<LeadPropertyInfoDto>>> GetAllPropertiesAsync()
        {
            return await Mediator.Send(new GetAllLeadProperyInfoRequest());
        }
        [HttpGet("all/projects")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all lead projects.", "")]
        public async Task<Response<List<LeadProjectInfoDto>>> GetAllProjectsAsync()
        {
            return await Mediator.Send(new GetAllLeadProjectInfoRequest());
        }

        [AllowAnonymous]
        [HttpGet("qr/channelPartners")]
        [TenantIdHeader]
        [OpenApiOperation("Get channelPartners name", "")]
        public async Task<Response<List<string>>> GetQRChannelPartnersAsync()
        {
            var tenantId = HttpContext.Request.Headers["tenant"].ToString() ?? _currentUser?.GetTenant() ?? string.Empty;
            return await Mediator.Send(new GetChannelPartnerNamesRequest() { TenantId = tenantId});
        }

        [AllowAnonymous]    
        [HttpGet("qr/agencynames")]
        [TenantIdHeader]
        [OpenApiOperation("Get assigned agency names in leads", "")]
        public async Task<Response<List<string>>> GetQRAgencyAsync()
        {
            var tenantId = HttpContext.Request.Headers["tenant"].ToString() ?? _currentUser?.GetTenant() ?? string.Empty;
            return await Mediator.Send(new GetAgencyNamesRequest() { TenantId = tenantId });
        }

        [AllowAnonymous]
        [HttpGet("qr/campaigns")]
        [TenantIdHeader]
        [OpenApiOperation("Get assigned campaign names in leads", "")]
        public async Task<Response<List<string>>> GetQRCampaignsPartnersAsync()
        {
            var tenantId = HttpContext.Request.Headers["tenant"].ToString() ?? _currentUser?.GetTenant() ?? string.Empty;
            return await Mediator.Send(new GetCampaignNamesRequest() { TenantId = tenantId});
        }
        [HttpGet("landline")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all leads LandLine Dropdown.", "")]
        public async Task<Response<List<string>>> GetAllLeadLandLineRequest()
        {
            return await Mediator.Send(new GetAllLeadLandLineRequest());
        }
        [HttpGet("properties/modulewise")]
        [TenantIdHeader]
        [OpenApiOperation("Get all properties modulewise.", "")]
        public async Task<Response<List<PropertyListsDto>>> GetPropertiesModulewiseAsync([FromQuery] GetAllPropertiesModuleWiseRequest request)
        {
            return await Mediator.Send(request);

        }

        [HttpGet("countrycode")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get All countrycode.", "")]
        public async Task<Response<List<string>>> Getasync()
        {
            return await Mediator.Send(new GetCountryCodeRequest());
        }
        [HttpGet("altCountrycode")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get All Alternative countrycode.", "")]
        public async Task<Response<List<string>>> GetaltAsyncAsync()
        {
            return await Mediator.Send(new GetAltCountryCodeRequest());
        }
        [HttpPut("agencyname/bulk")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.BulkUpdateAgency, LrbResource.Leads)]
        [OpenApiOperation("Update agencyName of a lead.", "")]
        public async Task<PagedResponse<DuplicateLeadAssigmentResponseDto, CountDto>> UpdateBulkAgencyAsync(ProcessBulkAgencyNameUpdateRequest request)
        {
            request.BulkCategory = BulkType.BulkUpdateStatus;
            if (request?.Ids?.Count >= 25)
            {
                var tenantId = _currentUser.GetTenant();
                var currentUser = _currentUser.GetUserId();
                request.CurrentUserId = currentUser;
                request.TenantId = tenantId;
                var payload = new InputPayload(tenantId ?? string.Empty, currentUser, request);
                await _serviceBus.RunBulkAgencyUpdateJobAsync(payload);
            }
            else
            {
                await Mediator.Send(request);
            }
            return new() { Succeeded = true };
        }
        [HttpPut("channelparterner/bulk")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.BulkUpdateChannelPartner, LrbResource.Leads)]
        [OpenApiOperation("Update ChannelPartener of a lead.", "")]
        public async Task<PagedResponse<DuplicateLeadAssigmentResponseDto, CountDto>> UpdateBulkChannelPartenerAsync(ProcessBulkChannelPartenerUpdateRequest request)
        {
            request.BulkCategory = BulkType.BulkUpdateStatus;
            if (request?.Ids?.Count >= 25)
            {
                var tenantId = _currentUser.GetTenant();
                var currentUser = _currentUser.GetUserId();
                request.CurrentUserId = currentUser;
                request.TenantId = tenantId;
                var payload = new InputPayload(tenantId ?? string.Empty, currentUser, request);
                await _serviceBus.RunBulkChannelPartenerUpdateJobAsync(payload);
            }
            else
            {
                await Mediator.Send(request);
            }
            return new() { Succeeded = true };
        }
        [HttpPut("campaign/bulk")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.BulkUpdateCampaign, LrbResource.Leads)]
        [OpenApiOperation("Update Campaigns of a lead.", "")]
        public async Task<PagedResponse<DuplicateLeadAssigmentResponseDto, CountDto>> UpdateBulkcampaignAsync(ProcessBulkCampaignUpdateRequest request)
        {
            request.BulkCategory = BulkType.BulkUpdateStatus;
            if (request?.Ids?.Count >= 25)
            {
                var tenantId = _currentUser.GetTenant();
                var currentUser = _currentUser.GetUserId();
                request.CurrentUserId = currentUser;
                request.TenantId = tenantId;
                var payload = new InputPayload(tenantId ?? string.Empty, currentUser, request);
                await _serviceBus.RunBulkCampaignUpdateJobAsync(payload);
            }
            else
            {
                await Mediator.Send(request);
            }
            return new() { Succeeded = true };
        }
        [HttpGet("paymentmodes")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all leads paymentmodes.", "")]
        public async Task<Response<Dictionary<PaymentMode, string>>> GetAllLeadsPaymentModesAsync()
        {
            return await Mediator.Send(new GetAllLeadsPaymentModesRequest());
        }
        [HttpPost("appointment")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get lead appointment details by id.", "")]
        public Task<Response<List<LeadAppointmentsByIdDto>>> GetAppointmentDetailsAsync([FromBody] GetLeadAppointmentsByIdRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpPost("shortenUrl")]
        [TenantIdHeader]
        [OpenApiOperation("Create a ShortenUrl.", "")]
        public async Task<Response<string>> CreateShortenUrlAsync([FromBody] CreateShortenUrlRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("code")]
        [AllowAnonymous]
        public async Task<IActionResult> RedirectToLongUrl([FromQuery] GetOriginalUrlfromShortenUrlRequest request)
        {
            var reDirectUrl = await Mediator.Send(request);
            return Redirect(reDirectUrl);
        }

    }

    public record InputPayload(string TenantId, Guid CurrentUserId, object Entity);

}
