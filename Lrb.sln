﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.2.32516.85
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "SolutionItems", "SolutionItems", "{E9125634-DA35-4858-9AC0-A20C0BC3FBFA}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{B469037F-E55D-4B83-956F-ED8B126FE082}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{597D4892-2B18-49CF-95F4-F4390942F242}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Core", "Core", "{F6E82DFD-886B-4AC7-A4BE-212A5662CEAB}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Migrators", "Migrators", "{7A42C10D-1B9C-4F29-80EB-F3DC7DE0F213}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Hosts", "Hosts", "{AB7C49EA-2A08-4CE3-AB9B-AF9E130B4C76}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Infrastructure", "Infrastructure", "{F969DE11-AD70-4F18-AF59-4659F12E7757}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Lrb.Application", "Lrb.Application\Lrb.Application.csproj", "{0D8D9F7B-77E5-476D-8342-88A097476CBE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Lrb.Domain", "Lrb.Domain\Lrb.Domain.csproj", "{3FDD571F-AD48-433C-B32F-B14085E63F1B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Lrb.Shared", "Lrb.Shared\Lrb.Shared.csproj", "{3404DCEC-AF32-4D5F-BD00-39744478CBF0}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Lrb.Migrators.PostgreSQL", "Lrb.Migrators.PostgreSQL\Lrb.Migrators.PostgreSQL.csproj", "{2886F5FC-D99A-433A-9193-F0348B0CCB0B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Lrb.Infrastructure", "Lrb.Infrastructure\Lrb.Infrastructure.csproj", "{93A3AE20-6EB0-48C1-ABC5-06DBD5C89C84}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Lrb.Identity.Host", "Lrb.Identity.Host\Lrb.Identity.Host.csproj", "{0B0CF9C9-90B2-4C09-9BCF-73F4E4F3194B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Lrb.WebApi.Host", "Lrb.WebApi.Host\Lrb.WebApi.Host.csproj", "{4F21347B-C290-41CA-9F9D-474B99E6B683}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Lrb.MobileApi.Host", "Lrb.MobileApi.Host\Lrb.MobileApi.Host.csproj", "{001EB15E-03F4-4BE2-B6B5-0AA111386051}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Lambda", "Lambda", "{764F0348-83AC-498C-BA48-7F53D35CAE9E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Lrb.IntegrationTrigger.Lambda", "Lrb.IntegrationTrigger.Lambda\Lrb.IntegrationTrigger.Lambda.csproj", "{9B7724BC-4BF0-4D57-8274-3886291B3E2C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Console", "Console", "{AE05D011-318E-483E-8DC4-35DECC180C30}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ExcelUpload", "ExcelUpload\ExcelUpload.csproj", "{EFBF6954-93EC-4B0B-99C7-550ED053EAD9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PlayGround", "PlayGround\PlayGround.csproj", "{7488EB0C-0683-4E87-A9C9-FFB3E740F9E8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BulkOperations", "BulkOperations\BulkOperations.csproj", "{6B53BDB1-930F-46EC-ADE7-BEE7A88B08FE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "LeadRotationExecutor", "LeadRotationExecutor\LeadRotationExecutor.csproj", "{83FD1B23-DB4A-497A-B704-EA0FEF14D4BD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Lrb.WhatsApp.Host", "Lrb.WhatsApp.Host\Lrb.WhatsApp.Host.csproj", "{4D62AB75-B534-46BC-960D-8A651C8BB23F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "LrbIntegrationJobs", "LrbIntegrationJobs\LrbIntegrationJobs.csproj", "{B783FF3D-EACE-469D-ADFE-5401533C8782}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "LrbIntegrationTriggers", "LrbIntegrationTriggers\LrbIntegrationTriggers.csproj", "{28980176-1EA3-4205-B2BD-1A9B1CE339D7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BackgroundBulkOperation", "BackgroundBulkOperation\BackgroundBulkOperation.csproj", "{F58EA5DE-9D03-404A-A86E-5371F56F1CCC}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "AzureFunctions", "AzureFunctions", "{A45CF3DC-8921-4B3F-A5F2-990238B96ED6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BackgroundTaskExecutors", "BackgroundTaskExecutors\BackgroundTaskExecutors.csproj", "{A09B6E77-A6A6-41D4-A418-3D0D9BACD17F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ExcelUploadExecutors", "ExcelUploadExecutors\ExcelUploadExecutors.csproj", "{6632772B-3DF2-45D3-BBB3-238B651E11CA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "LrbIntegrationBackgroundJobs", "LrbIntegrationBackgroundJobs\LrbIntegrationBackgroundJobs.csproj", "{554A9AE7-5B93-46C6-9D78-8414D1A6597A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AuditOperations", "AuditOperations\AuditOperations.csproj", "{CA1C92F8-0BBE-4683-8F60-B784E7DB0823}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "LrbExcelExport", "LrbExcelExport\LrbExcelExport.csproj", "{3BD0E852-4256-46E6-8D2A-63649E6374E7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ListingBackgroundJobs", "ListingBackgroundJobs\ListingBackgroundJobs.csproj", "{7115B8DE-16F0-47B0-9271-1BA1D9F87DED}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CallDetectionBackgroundTask", "CallDetectionBackgroundTask\CallDetectionBackgroundTask.csproj", "{AB37B864-6B98-4AFC-BBDC-B55F35B2F0E1}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{0D8D9F7B-77E5-476D-8342-88A097476CBE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0D8D9F7B-77E5-476D-8342-88A097476CBE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0D8D9F7B-77E5-476D-8342-88A097476CBE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0D8D9F7B-77E5-476D-8342-88A097476CBE}.Release|Any CPU.Build.0 = Release|Any CPU
		{3FDD571F-AD48-433C-B32F-B14085E63F1B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3FDD571F-AD48-433C-B32F-B14085E63F1B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3FDD571F-AD48-433C-B32F-B14085E63F1B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3FDD571F-AD48-433C-B32F-B14085E63F1B}.Release|Any CPU.Build.0 = Release|Any CPU
		{3404DCEC-AF32-4D5F-BD00-39744478CBF0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3404DCEC-AF32-4D5F-BD00-39744478CBF0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3404DCEC-AF32-4D5F-BD00-39744478CBF0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3404DCEC-AF32-4D5F-BD00-39744478CBF0}.Release|Any CPU.Build.0 = Release|Any CPU
		{2886F5FC-D99A-433A-9193-F0348B0CCB0B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2886F5FC-D99A-433A-9193-F0348B0CCB0B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2886F5FC-D99A-433A-9193-F0348B0CCB0B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2886F5FC-D99A-433A-9193-F0348B0CCB0B}.Release|Any CPU.Build.0 = Release|Any CPU
		{93A3AE20-6EB0-48C1-ABC5-06DBD5C89C84}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{93A3AE20-6EB0-48C1-ABC5-06DBD5C89C84}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{93A3AE20-6EB0-48C1-ABC5-06DBD5C89C84}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{93A3AE20-6EB0-48C1-ABC5-06DBD5C89C84}.Release|Any CPU.Build.0 = Release|Any CPU
		{0B0CF9C9-90B2-4C09-9BCF-73F4E4F3194B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0B0CF9C9-90B2-4C09-9BCF-73F4E4F3194B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0B0CF9C9-90B2-4C09-9BCF-73F4E4F3194B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0B0CF9C9-90B2-4C09-9BCF-73F4E4F3194B}.Release|Any CPU.Build.0 = Release|Any CPU
		{4F21347B-C290-41CA-9F9D-474B99E6B683}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4F21347B-C290-41CA-9F9D-474B99E6B683}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4F21347B-C290-41CA-9F9D-474B99E6B683}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4F21347B-C290-41CA-9F9D-474B99E6B683}.Release|Any CPU.Build.0 = Release|Any CPU
		{001EB15E-03F4-4BE2-B6B5-0AA111386051}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{001EB15E-03F4-4BE2-B6B5-0AA111386051}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{001EB15E-03F4-4BE2-B6B5-0AA111386051}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{001EB15E-03F4-4BE2-B6B5-0AA111386051}.Release|Any CPU.Build.0 = Release|Any CPU
		{9B7724BC-4BF0-4D57-8274-3886291B3E2C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9B7724BC-4BF0-4D57-8274-3886291B3E2C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9B7724BC-4BF0-4D57-8274-3886291B3E2C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9B7724BC-4BF0-4D57-8274-3886291B3E2C}.Release|Any CPU.Build.0 = Release|Any CPU
		{EFBF6954-93EC-4B0B-99C7-550ED053EAD9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EFBF6954-93EC-4B0B-99C7-550ED053EAD9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EFBF6954-93EC-4B0B-99C7-550ED053EAD9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EFBF6954-93EC-4B0B-99C7-550ED053EAD9}.Release|Any CPU.Build.0 = Release|Any CPU
		{7488EB0C-0683-4E87-A9C9-FFB3E740F9E8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7488EB0C-0683-4E87-A9C9-FFB3E740F9E8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7488EB0C-0683-4E87-A9C9-FFB3E740F9E8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7488EB0C-0683-4E87-A9C9-FFB3E740F9E8}.Release|Any CPU.Build.0 = Release|Any CPU
		{6B53BDB1-930F-46EC-ADE7-BEE7A88B08FE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6B53BDB1-930F-46EC-ADE7-BEE7A88B08FE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6B53BDB1-930F-46EC-ADE7-BEE7A88B08FE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6B53BDB1-930F-46EC-ADE7-BEE7A88B08FE}.Release|Any CPU.Build.0 = Release|Any CPU
		{83FD1B23-DB4A-497A-B704-EA0FEF14D4BD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{83FD1B23-DB4A-497A-B704-EA0FEF14D4BD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{83FD1B23-DB4A-497A-B704-EA0FEF14D4BD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{83FD1B23-DB4A-497A-B704-EA0FEF14D4BD}.Release|Any CPU.Build.0 = Release|Any CPU
		{4D62AB75-B534-46BC-960D-8A651C8BB23F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4D62AB75-B534-46BC-960D-8A651C8BB23F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4D62AB75-B534-46BC-960D-8A651C8BB23F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4D62AB75-B534-46BC-960D-8A651C8BB23F}.Release|Any CPU.Build.0 = Release|Any CPU
		{B783FF3D-EACE-469D-ADFE-5401533C8782}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B783FF3D-EACE-469D-ADFE-5401533C8782}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B783FF3D-EACE-469D-ADFE-5401533C8782}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B783FF3D-EACE-469D-ADFE-5401533C8782}.Release|Any CPU.Build.0 = Release|Any CPU
		{28980176-1EA3-4205-B2BD-1A9B1CE339D7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{28980176-1EA3-4205-B2BD-1A9B1CE339D7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{28980176-1EA3-4205-B2BD-1A9B1CE339D7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{28980176-1EA3-4205-B2BD-1A9B1CE339D7}.Release|Any CPU.Build.0 = Release|Any CPU
		{F58EA5DE-9D03-404A-A86E-5371F56F1CCC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F58EA5DE-9D03-404A-A86E-5371F56F1CCC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F58EA5DE-9D03-404A-A86E-5371F56F1CCC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F58EA5DE-9D03-404A-A86E-5371F56F1CCC}.Release|Any CPU.Build.0 = Release|Any CPU
		{A09B6E77-A6A6-41D4-A418-3D0D9BACD17F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A09B6E77-A6A6-41D4-A418-3D0D9BACD17F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A09B6E77-A6A6-41D4-A418-3D0D9BACD17F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A09B6E77-A6A6-41D4-A418-3D0D9BACD17F}.Release|Any CPU.Build.0 = Release|Any CPU
		{6632772B-3DF2-45D3-BBB3-238B651E11CA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6632772B-3DF2-45D3-BBB3-238B651E11CA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6632772B-3DF2-45D3-BBB3-238B651E11CA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6632772B-3DF2-45D3-BBB3-238B651E11CA}.Release|Any CPU.Build.0 = Release|Any CPU
		{554A9AE7-5B93-46C6-9D78-8414D1A6597A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{554A9AE7-5B93-46C6-9D78-8414D1A6597A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{554A9AE7-5B93-46C6-9D78-8414D1A6597A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{554A9AE7-5B93-46C6-9D78-8414D1A6597A}.Release|Any CPU.Build.0 = Release|Any CPU
		{CA1C92F8-0BBE-4683-8F60-B784E7DB0823}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CA1C92F8-0BBE-4683-8F60-B784E7DB0823}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CA1C92F8-0BBE-4683-8F60-B784E7DB0823}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CA1C92F8-0BBE-4683-8F60-B784E7DB0823}.Release|Any CPU.Build.0 = Release|Any CPU
		{3BD0E852-4256-46E6-8D2A-63649E6374E7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3BD0E852-4256-46E6-8D2A-63649E6374E7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3BD0E852-4256-46E6-8D2A-63649E6374E7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3BD0E852-4256-46E6-8D2A-63649E6374E7}.Release|Any CPU.Build.0 = Release|Any CPU
		{7115B8DE-16F0-47B0-9271-1BA1D9F87DED}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7115B8DE-16F0-47B0-9271-1BA1D9F87DED}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7115B8DE-16F0-47B0-9271-1BA1D9F87DED}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7115B8DE-16F0-47B0-9271-1BA1D9F87DED}.Release|Any CPU.Build.0 = Release|Any CPU
		{AB37B864-6B98-4AFC-BBDC-B55F35B2F0E1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AB37B864-6B98-4AFC-BBDC-B55F35B2F0E1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AB37B864-6B98-4AFC-BBDC-B55F35B2F0E1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AB37B864-6B98-4AFC-BBDC-B55F35B2F0E1}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{F6E82DFD-886B-4AC7-A4BE-212A5662CEAB} = {B469037F-E55D-4B83-956F-ED8B126FE082}
		{7A42C10D-1B9C-4F29-80EB-F3DC7DE0F213} = {B469037F-E55D-4B83-956F-ED8B126FE082}
		{AB7C49EA-2A08-4CE3-AB9B-AF9E130B4C76} = {B469037F-E55D-4B83-956F-ED8B126FE082}
		{F969DE11-AD70-4F18-AF59-4659F12E7757} = {B469037F-E55D-4B83-956F-ED8B126FE082}
		{0D8D9F7B-77E5-476D-8342-88A097476CBE} = {F6E82DFD-886B-4AC7-A4BE-212A5662CEAB}
		{3FDD571F-AD48-433C-B32F-B14085E63F1B} = {F6E82DFD-886B-4AC7-A4BE-212A5662CEAB}
		{3404DCEC-AF32-4D5F-BD00-39744478CBF0} = {F6E82DFD-886B-4AC7-A4BE-212A5662CEAB}
		{2886F5FC-D99A-433A-9193-F0348B0CCB0B} = {7A42C10D-1B9C-4F29-80EB-F3DC7DE0F213}
		{93A3AE20-6EB0-48C1-ABC5-06DBD5C89C84} = {F969DE11-AD70-4F18-AF59-4659F12E7757}
		{0B0CF9C9-90B2-4C09-9BCF-73F4E4F3194B} = {AB7C49EA-2A08-4CE3-AB9B-AF9E130B4C76}
		{4F21347B-C290-41CA-9F9D-474B99E6B683} = {AB7C49EA-2A08-4CE3-AB9B-AF9E130B4C76}
		{001EB15E-03F4-4BE2-B6B5-0AA111386051} = {AB7C49EA-2A08-4CE3-AB9B-AF9E130B4C76}
		{764F0348-83AC-498C-BA48-7F53D35CAE9E} = {B469037F-E55D-4B83-956F-ED8B126FE082}
		{9B7724BC-4BF0-4D57-8274-3886291B3E2C} = {764F0348-83AC-498C-BA48-7F53D35CAE9E}
		{AE05D011-318E-483E-8DC4-35DECC180C30} = {B469037F-E55D-4B83-956F-ED8B126FE082}
		{EFBF6954-93EC-4B0B-99C7-550ED053EAD9} = {AE05D011-318E-483E-8DC4-35DECC180C30}
		{7488EB0C-0683-4E87-A9C9-FFB3E740F9E8} = {AE05D011-318E-483E-8DC4-35DECC180C30}
		{6B53BDB1-930F-46EC-ADE7-BEE7A88B08FE} = {AE05D011-318E-483E-8DC4-35DECC180C30}
		{83FD1B23-DB4A-497A-B704-EA0FEF14D4BD} = {A45CF3DC-8921-4B3F-A5F2-990238B96ED6}
		{4D62AB75-B534-46BC-960D-8A651C8BB23F} = {AB7C49EA-2A08-4CE3-AB9B-AF9E130B4C76}
		{B783FF3D-EACE-469D-ADFE-5401533C8782} = {AE05D011-318E-483E-8DC4-35DECC180C30}
		{28980176-1EA3-4205-B2BD-1A9B1CE339D7} = {AE05D011-318E-483E-8DC4-35DECC180C30}
		{F58EA5DE-9D03-404A-A86E-5371F56F1CCC} = {A45CF3DC-8921-4B3F-A5F2-990238B96ED6}
		{A45CF3DC-8921-4B3F-A5F2-990238B96ED6} = {AE05D011-318E-483E-8DC4-35DECC180C30}
		{A09B6E77-A6A6-41D4-A418-3D0D9BACD17F} = {A45CF3DC-8921-4B3F-A5F2-990238B96ED6}
		{6632772B-3DF2-45D3-BBB3-238B651E11CA} = {A45CF3DC-8921-4B3F-A5F2-990238B96ED6}
		{554A9AE7-5B93-46C6-9D78-8414D1A6597A} = {A45CF3DC-8921-4B3F-A5F2-990238B96ED6}
		{CA1C92F8-0BBE-4683-8F60-B784E7DB0823} = {A45CF3DC-8921-4B3F-A5F2-990238B96ED6}
		{3BD0E852-4256-46E6-8D2A-63649E6374E7} = {A45CF3DC-8921-4B3F-A5F2-990238B96ED6}
		{7115B8DE-16F0-47B0-9271-1BA1D9F87DED} = {A45CF3DC-8921-4B3F-A5F2-990238B96ED6}
		{AB37B864-6B98-4AFC-BBDC-B55F35B2F0E1} = {A45CF3DC-8921-4B3F-A5F2-990238B96ED6}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {181952CF-2920-4AB1-A80F-AAC94F73EBDC}
	EndGlobalSection
EndGlobal
