﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.Project.Web.Dtos;
using Lrb.Application.Utils;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using Serilog;

namespace Lrb.Application.Project.Web.Requests.Bulk_Upload
{
    public class GetUnitExcelUploadFileRequest : IRequest<Response<FileColumnUnitDto>>
    {
        public IFormFile? File { get; set; }
        public GetUnitExcelUploadFileRequest(IFormFile file)
        {
            File = file;
        }
    }

    public class GetUnitExcelUploadFileRequestHandler : IRequestHandler<GetUnitExcelUploadFileRequest, Response<FileColumnUnitDto>>
    {
        private readonly IBlobStorageService _blobStorageService;
        private readonly ILogger _logger;

        public GetUnitExcelUploadFileRequestHandler(IBlobStorageService blobStorageService, ILogger logger)
        {
            _blobStorageService = blobStorageService;
            _logger = logger;
        }
        public async Task<Response<FileColumnUnitDto>> Handle(GetUnitExcelUploadFileRequest request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.Information("GetExcelUploadRequestHandler -> Handle: " + JsonConvert.SerializeObject(request));
                var file = request.File;
                if (file == null)
                {
                    throw new ArgumentNullException(nameof(file));
                }
                var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", "Projects", file);
                List<string> columns = new List<string>();
                Dictionary<string, List<string>> multiSheetColumn = new();
                if (key.Split(".").LastOrDefault() == "csv")
                {
                    columns = await CSVHelper.GetCSVColumns(file);
                    multiSheetColumn.Add("Default", columns);
                }
                else
                {
                    columns = EPPlusExcelHelper.GetFileColumns(file);
                    multiSheetColumn = EPPlusExcelHelper.GetFileColumnsOfMultiSheets(file);
                }
                FileColumnUnitDto excelColumnsViewModel = new()
                {
                    S3BucketKey = key,
                    ColumnNames = columns,
                    MultiSheetColumnNames = multiSheetColumn,
                };
                return new(excelColumnsViewModel);
            }
            catch (Exception ex) 
            {
                _logger.Error("GetExcelColumnsUsingEPPlusRequestHandler -> Error: " + JsonConvert.SerializeObject(ex));
                throw;
            }
        }
    }
}
