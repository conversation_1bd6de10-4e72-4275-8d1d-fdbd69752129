﻿using Lrb.Application.Identity.Users;
using Lrb.Application.Team.Web.Dtos;
using Lrb.Application.Team.Web.Requests;
using Mapster;

namespace Lrb.Application.Team.Web
{
    public static class TeamMapping
    {
        public static void Configure()
        {
            TypeAdapterConfig<UserDetailsDto, UserDto>.NewConfig()
            .Map(dest => dest.Name, src1 => string.Format("{0} {1}", src1.FirstName, src1.LastName))
            .Map(dest => dest.ContactNo, scr2 => string.Format("{0}", scr2.PhoneNumber));

            TypeAdapterConfig<CreateRetentionTeamRequest, TeamConfiguration>.NewConfig()
            .Map(dest => dest.TeamId, src => src.Id)
            .Map(dest => dest.Id, src => src.Id != Guid.Empty && src.Id != null ? Guid.Empty : default);

            TypeAdapterConfig<Domain.Entities.Team, ViewRetentionTeamsDto>.NewConfig()
            .Map(dest => dest.Statuses, src => src.Statuses != null ? src.Statuses.Where(i => i.BaseId == null) : default)
            .Map(dest => dest.SubStatuses, src => src.Statuses != null ? src.Statuses.Where(i => i.BaseId != null) : default);

            TypeAdapterConfig<UserDetailsDto, UserListDto>.NewConfig()
            .Map(dest => dest.UserName, src1 => string.Format("{0} {1}", src1.FirstName, src1.LastName));

            TypeAdapterConfig<UpdateTeamRequest, Domain.Entities.Team>.NewConfig()
             .Ignore(dest => dest.Configuration)
             .Ignore(dest => dest.Configuration.Id);

            TypeAdapterConfig<TeamConfigurationDto, TeamConfiguration>.NewConfig()
            .Map(dest => dest.IsForRetention, src => false)
            .Map(dest => dest.NoOfRotation, src => src.NoOfRotation > 0 ? src.NoOfRotation : 0)
            .Map(dest => dest.RotationTime, src => src.RotationTime != null ? src.RotationTime : default)
            .Map(dest => dest.ShiftStartTime, src => src.ShiftStartTime != null ? src.ShiftStartTime : default)
            .Map(dest => dest.ShiftStartTime, src => src.ShiftEndTime != null ? src.ShiftEndTime : default)
            .Map(dest => dest.DayOfWeeks, src => src.DayOfWeeks != null ? src.DayOfWeeks : default)
            .Map(dest => dest.StartTime, src => src.StartTime != null ? src.StartTime : default)
            .Map(dest => dest.EndTime, src => src.EndTime != null ? src.EndTime : default)
            .Map(dest => dest.LeadSources, src => src.LeadSources != null ? src.LeadSources : default)
            .Map(dest => dest.IsSourceLevel, src => src.IsSourceLevel != null ? src.IsSourceLevel :null)
            .Ignore(dest => dest.Id)
            .Ignore(dest => dest.TeamId);

            TypeAdapterConfig<Domain.Entities.Team, ViewTeamLeadRotationInfoDto>.NewConfig()
    .Map(dest => dest.Locations,
         src => src.Configurations != null &&
                src.Configurations.Any(c => c.IsForRetention == false) &&
                src.Configurations.First(c => c.IsForRetention == false).Locations != null
             ? src.Configurations
                   .First(c => c.IsForRetention == false)
                   .Locations
                   .Select(l => l.NormalizedName)
                   .ToList()
             : new List<string>())
    .Map(dest => dest.Cities,
         src => src.Configurations != null &&
                src.Configurations.Any(c => c.IsForRetention == false) &&
                src.Configurations.First(c => c.IsForRetention == false).Cities != null
             ? src.Configurations
                   .First(c => c.IsForRetention == false)
                   .Cities
                   .Select(l => l.NormalizedName)
                   .ToList()
             : new List<string>())
    .Map(dest => dest.Zones,
         src => src.Configurations != null &&
                src.Configurations.Any(c => c.IsForRetention == false) &&
                src.Configurations.First(c => c.IsForRetention == false).Zones != null
             ? src.Configurations
                   .First(c => c.IsForRetention == false)
                   .Zones
                   .Select(l => l.NormalizedName)
                   .ToList()
             : new List<string>());
        }
    }

}
