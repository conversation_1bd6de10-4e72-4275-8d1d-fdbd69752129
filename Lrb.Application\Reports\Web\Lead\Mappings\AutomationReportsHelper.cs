using Lrb.Application.Reports.Web.Data.Requests.Campaign;
using Lrb.Application.Reports.Web.Data.Requests.ChannelPartner;
using Lrb.Application.Reports.Web.Data.Requests.User;
using Lrb.Application.Reports.Web.Lead.Requests.Revenue;
using Lrb.Application.Reports.Web.Lead.Requests.Custom.Campaign;
using Lrb.Application.Reports.Web.Lead.Requests.Custom.ChannelPartner;
using Lrb.Application.Reports.Web.Requests.DatewiseSourceCount;
using Lrb.Application.Reports.Web.Requests.ProjectvsSubStatus;
using Lrb.Application.Reports.Web.UserVsSource.Requests;
using Newtonsoft.Json;
using System.Text.Json;

namespace Lrb.Application.Reports.Web.Lead.Mappings
{
    public class AutomationReportsHelper
    {
        public static string GetReportRequestString(string type, int frequency, string fileName, string? timeZoneInfo = null)
        {
            string requestBody = "";
            DateTime fromDate, toDate;

            string effectiveTimeZoneId = "Asia/Calcutta";

            if (!string.IsNullOrWhiteSpace(timeZoneInfo))
            {
                try
                {
                    var tz = System.Text.Json.JsonSerializer.Deserialize<TimezoneInfo>(timeZoneInfo);
                    if (!string.IsNullOrWhiteSpace(tz?.TimeZoneId))
                    {
                        effectiveTimeZoneId = tz.TimeZoneId;
                    }
                }
                catch
                {
                    effectiveTimeZoneId = "Asia/Calcutta";
                }
            }

            TimeZoneInfo tzInfo;
            try
            {
                tzInfo = TimeZoneInfo.FindSystemTimeZoneById(effectiveTimeZoneId);
            }
            catch (TimeZoneNotFoundException)
            {
                tzInfo = TimeZoneInfo.FindSystemTimeZoneById("Asia/Calcutta");
            }

            // Current IST date
            DateTime nowIst = TimeZoneInfo.ConvertTime(DateTime.Now, tzInfo);
            DateTime istToday = nowIst.Date;

            // Compute IST-based from and to times
            DateTime fromIst, toIst;

            if (frequency == 0)
            {
                fromIst = istToday; 
                toIst = istToday;   
            }
            else
            {
                toIst = istToday;
                fromIst = toIst.AddDays(-frequency);
            }

            // Convert to UTC
            fromDate = TimeZoneInfo.ConvertTimeToUtc(fromIst, tzInfo);
            toDate = TimeZoneInfo.ConvertTimeToUtc(toIst, tzInfo);


            switch (type)
            {
                case "substatusreportbyuser":
                    {
                        RunAWSBatchForSubStatusReportRequest request = new()
                        {
                            FromDate = fromDate,
                            ToDate = toDate,
                            DateType = DateType.All,
                            UserStatus = UserStatus.Active,
                            ExportPermission = ReportPermission.All,
                            FileName = fileName,
                            TimeZoneId = tzInfo.Id,
                            BaseUTcOffset = tzInfo.BaseUtcOffset,
                        };
                        requestBody = JsonConvert.SerializeObject(request);
                    }
                    break;
                case "statusreportbyagency":
                    {
                        RunAWSBatchForLeadStatusReportByAgencyRequest request = new()
                        {
                            FromDateForAgency = fromDate,
                            ToDateForAgency = toDate,
                            DateType = DateType.All,
                            ExportPermission = ReportPermission.All,
                            FileName = fileName,
                            TimeZoneId = tzInfo.Id,
                            BaseUTcOffset = tzInfo.BaseUtcOffset,
                        };
                        requestBody = JsonConvert.SerializeObject(request);
                    }
                    break;
                case "dataprojectreportbystatus":
                    {
                        RunAWSBatchForDataStatusReportByProjectRequest request = new()
                        {
                            FromDate = fromDate,
                            ToDate = toDate,
                            DateType = DataManagement.Web.Request.ProspectDateType.All,
                            UserStatus = UserStatus.Active,
                            ReportPermission = ReportPermission.All,
                            FileName = fileName,
                            TimeZoneId = tzInfo.Id,
                            BaseUTcOffset = tzInfo.BaseUtcOffset,
                        };
                        requestBody = JsonConvert.SerializeObject(request);
                    }
                    break;
                case "datasourcereportbystatus":
                    {
                        RunAWSBatchForDataStatusReportBySourceRequest request = new()
                        {
                            FromDate = fromDate,
                            ToDate = toDate,
                            DateType = DataManagement.Web.Request.ProspectDateType.All,
                            UserStatus = UserStatus.Active,
                            ReportPermission = ReportPermission.All,
                            FileName = fileName,
                            TimeZoneId = tzInfo.Id,
                            BaseUTcOffset = tzInfo.BaseUtcOffset,
                        };
                        requestBody = JsonConvert.SerializeObject(request);
                    }
                    break;
                case "userdataactivityreport":
                    {
                        RunAWSBatchForDataActivityReportRequest request = new()
                        {
                            FromDate = fromDate,
                            ToDate = toDate,
                            ReportPermission = ReportPermission.All,
                            UserStatus = UserStatus.Active,
                            FileName = fileName,
                            TimeZoneId = tzInfo.Id,
                            BaseUTcOffset = tzInfo.BaseUtcOffset,
                        };
                        requestBody = JsonConvert.SerializeObject(request);
                    }
                    break;
                case "exportdatacalllogreportbyuser":
                    {
                        RunAWSBatchForDataCallLogReportRequest request = new()
                        {
                            CallLogFromDate = fromDate,
                            CallLogToDate = toDate,
                            ExportPermission = ReportPermission.All,
                            FileName = fileName,
                            TimeZoneId = tzInfo.Id,
                            BaseUTcOffset = tzInfo.BaseUtcOffset,
                        };
                        requestBody = JsonConvert.SerializeObject(request);
                    }
                    break;
                case "datastatusreportbyuser":
                    {
                        RunAWSBatchForDataStatusReportByUsersRequest request = new()
                        {
                            FromDate = fromDate,
                            ToDate = toDate,
                            DateType = DataManagement.Web.Request.ProspectDateType.All,
                            UserStatus = UserStatus.Active,
                            ReportPermission = ReportPermission.All,
                            FileName = fileName,
                            TimeZoneId = tzInfo.Id,
                            BaseUTcOffset = tzInfo.BaseUtcOffset,
                        };
                        requestBody = JsonConvert.SerializeObject(request);
                    }
                    break;
                case "lead_statusreportbyagency":
                    {
                        RunAWSBatchForAgencyReportByStatusRequest request = new()
                        {
                            FromDateForAgency = fromDate,
                            ToDateForAgency = toDate,
                            DateType = DateType.All,
                            ReportPermission = ReportPermission.All,
                            FileName = fileName,
                            TimeZoneId = tzInfo.Id,
                            BaseUTcOffset = tzInfo.BaseUtcOffset,
                        };
                        requestBody = JsonConvert.SerializeObject(request);
                    }
                    break;
                case "lead_statusreportbyproject":
                    {
                        RunAWSBatchForProjectReportByStatusRequest request = new()
                        {
                            FromDateForProject = fromDate,
                            ToDateForProject = toDate,
                            ReportPermission = ReportPermission.All,
                            FileName = fileName,
                            TimeZoneId = tzInfo.Id,
                            BaseUTcOffset = tzInfo.BaseUtcOffset,
                        };
                        requestBody = JsonConvert.SerializeObject(request);
                    }
                    break;
                case "lead_statusreportbysource":
                    {
                        RunAWSBatchForSourceReportByStatusRequest request = new()
                        {
                            FromDateForSource = fromDate,
                            ToDateForSource = toDate,
                            ReportPermission= ReportPermission.All,
                            FileName = fileName,
                            TimeZoneId = tzInfo.Id,
                            BaseUTcOffset = tzInfo.BaseUtcOffset,
                        };
                        requestBody = JsonConvert.SerializeObject(request);
                    }
                    break;
                case "lead_statusreportbysubsource":
                    {
                        RunAWSBatchForSubSourceReportByLeadStatusRequest request = new()
                        {
                            FromDateForSubSource = fromDate,
                            ToDateForSubSource = toDate,
                            ReportPermission= ReportPermission.All,
                            FileName = fileName,
                            TimeZoneId = tzInfo.Id,
                            BaseUTcOffset = tzInfo.BaseUtcOffset,
                        };
                        requestBody = JsonConvert.SerializeObject(request);
                    }
                    break;
                case "lead_statusreportbyuser":
                    {
                        RunAWSBatchForStatusReportByUsersRequest request = new()
                        {
                            FromDate = fromDate,
                            ToDate = toDate,
                            DateType = DateType.All,
                            ShouldShowAll = true,
                            ReportPermission = ReportPermission.All,
                            UserStatus = UserStatus.Active,
                            FileName = fileName,
                            TimeZoneId = tzInfo.Id,
                            BaseUTcOffset = tzInfo.BaseUtcOffset,
                        };
                        requestBody = JsonConvert.SerializeObject(request);
                    }
                    break;
                case "statusreportbyproject":
                    {
                        RunAWSBatchForLeadStatusReportByProjectsRequest request = new()
                        {
                            FromDateForProject = fromDate,
                            ToDateForProject = toDate,
                            ExportPermission = ReportPermission.All,
                            FileName = fileName,
                            TimeZoneId = tzInfo.Id,
                            BaseUTcOffset = tzInfo.BaseUtcOffset,
                        };
                        requestBody = JsonConvert.SerializeObject(request);
                    }
                    break;
                case "projectreportbysubstatus":
                    {
                        RunAWSBatchForProjectReportBySubStatusRequest request = new()
                        {
                            FromDate = fromDate,
                            ToDate = toDate,
                            DateType= DateType.All,
                            ExportPermission = ReportPermission.All,
                            FileName = fileName,
                            TimeZoneId = tzInfo.Id,
                            BaseUTcOffset = tzInfo.BaseUtcOffset,
                        };
                        requestBody = JsonConvert.SerializeObject(request);
                    }
                    break;
                case "statusreportbysource":
                    {

                        RunAWSBatchForLeadStatusReportBySourceRequest request = new()
                        {
                            FromDateForSource = fromDate,
                            ToDateForSource = toDate,
                            ExportPermission = ReportPermission.All,
                            FileName = fileName,
                            TimeZoneId = tzInfo.Id,
                            BaseUTcOffset = tzInfo.BaseUtcOffset,
                        };
                        requestBody = JsonConvert.SerializeObject(request);
                    }
                    break;
                case "statusreportbysubsource":
                    {

                        RunAWSBatchForLeadStatusReportBySubSourceRequest request = new()
                        {
                            FromDateForSubSource = fromDate,
                            ToDateForSubSource = toDate,
                            ExportPermission = ReportPermission.All,
                            FileName = fileName,
                            TimeZoneId = tzInfo.Id,
                            BaseUTcOffset = tzInfo.BaseUtcOffset,
                        };
                        requestBody = JsonConvert.SerializeObject(request);
                    }
                    break;
                case "substatusreportbysubsource":
                    {
                        RunAWSBatchForSubStatusReportBySubSourceRequest request = new()
                        {
                            FromDate = fromDate,
                            ToDate = toDate,
                            DateType = DateType.All,
                            ExportPermission = ReportPermission.All,
                            FileName = fileName,
                            TimeZoneId = tzInfo.Id,
                            BaseUTcOffset = tzInfo.BaseUtcOffset,
                        };
                        requestBody = JsonConvert.SerializeObject(request);
                    }
                    break;
                case "useractivityreport":
                    {
                        RunAWSBatchForActivityReportRequest request = new()
                        {
                            FromDate = fromDate,
                            ToDate = toDate,
                            ShouldShowDataReport = false,
                            UserStatus = UserStatus.Active,
                            ReportPermission = ReportPermission.All,
                            FileName = fileName,
                            TimeZoneId = tzInfo.Id,
                            BaseUTcOffset = tzInfo.BaseUtcOffset,
                        };
                        requestBody = JsonConvert.SerializeObject(request);
                    }
                    break;
                case "calllogreportbyuser":
                    {
                        RunAWSBatchForCallLogReportByUserReportRequest request = new()
                        {
                            CallLogFromDate = fromDate,
                            CallLogToDate = toDate,
                            ExportPermission = ReportPermission.All,
                            FileName = fileName,
                            TimeZoneId = tzInfo.Id,
                            BaseUTcOffset = tzInfo.BaseUtcOffset,
                        };
                        requestBody = JsonConvert.SerializeObject(request);
                    }
                    break;
                case "meetingandvisitreport":
                    {
                        RunAWSBatchForVisitAndMeetingReportByUserRequest request = new()
                        {
                            FromDateForMeetingOrVisit = fromDate,
                            ToDateForMeetingOrVisit = toDate,
                            UserStatus = UserStatus.Active,
                            ExportPermission = ReportPermission.All,
                            FileName = fileName,
                            TimeZoneId = tzInfo.Id,
                            BaseUTcOffset = tzInfo.BaseUtcOffset,
                        };
                        requestBody = JsonConvert.SerializeObject(request);
                    }
                    break;
                case "statusreportbyuser":
                    {
                        RunAWSBatchForLeadStatusReportByUsersRequest request = new()
                        {
                            FromDate = fromDate,
                            ToDate = toDate,
                            ShouldShowAll = true,
                            UserStatus = UserStatus.Active,
                            ExportPermission = ReportPermission.All,
                            DateType = DateType.All,
                            FileName = fileName,
                            TimeZoneId = tzInfo.Id,
                            BaseUTcOffset = tzInfo.BaseUtcOffset,
                        };
                        requestBody = JsonConvert.SerializeObject(request);
                    }
                    break;
                case "uservssource":
                    {
                        RunAWSBatchForUserVsSourceReportRequest request = new()
                        {
                            FromDate = fromDate,
                            ToDate = toDate,
                            DateType = DateType.All,
                            UserStatus = UserStatus.Active,
                            ExportPermission = ReportPermission.All,
                            FileName = fileName,
                            TimeZoneId = tzInfo.Id,
                            BaseUTcOffset = tzInfo.BaseUtcOffset,
                        };
                        requestBody = JsonConvert.SerializeObject(request);
                    }
                    break;
                case "uservssubsource":
                    {
                        RunAWSBatchForUserVsSubSourceReportRequest request = new()
                        {
                            FromDate = fromDate,
                            ToDate = toDate,
                            DateType = DateType.All,
                            UserStatus = UserStatus.Active,
                            ExportPermission = ReportPermission.All,
                            FileName = fileName,
                            TimeZoneId = tzInfo.Id,
                            BaseUTcOffset = tzInfo.BaseUtcOffset,
                        };
                        requestBody = JsonConvert.SerializeObject(request);
                    }
                    break;
                case "recieveddatebysource":
                    {
                        RunAWSBatchForDatewiseSourceReportRequest request = new()
                        {
                            FromDateForLeadReceived = fromDate,
                            ToDateForLeadReceived = toDate,
                            DateType = DateType.ReceivedDate,
                            ReportPermission = ReportPermission.All,
                            FileName = fileName,
                            TimeZoneId = tzInfo.Id,
                            BaseUTcOffset = tzInfo.BaseUtcOffset,
                        };
                        requestBody = JsonConvert.SerializeObject(request);
                    }
                    break;
                case "datasubsourcereportbystatus":
                    {
                        RunAWSBatchForDataStatusReportBySubSourceRequest request = new()
                        {
                            FromDate = fromDate,
                            ToDate = toDate,
                            DateType = DataManagement.Web.Request.ProspectDateType.All,
                            ReportPermission = ReportPermission.All,
                            UserStatus = UserStatus.Active,
                            FileName = fileName,
                            TimeZoneId = tzInfo.Id,
                            BaseUTcOffset = tzInfo.BaseUtcOffset,
                        };
                        requestBody = JsonConvert.SerializeObject(request);
                    }
                    break;
                case "datachannelpartnerreportbystatus":
                    {
                        RunAWSBatchForDataStatusReportByChannelPartnerRequest request = new()
                        {
                            FromDate = fromDate,
                            ToDate = toDate,
                            DateType = DataManagement.Web.Request.ProspectDateType.All,
                            UserStatus = UserStatus.Active,
                            ReportPermission = ReportPermission.All,
                            FileName = fileName,
                            TimeZoneId = tzInfo.Id,
                            BaseUTcOffset = tzInfo.BaseUtcOffset,
                        };
                        requestBody = JsonConvert.SerializeObject(request);
                    }
                    break;
                case "datacampaignreportbystatus":
                    {
                        RunAWSBatchForDataStatusReportByCampaignRequest request = new()
                        {
                            FromDate = fromDate,
                            ToDate = toDate,
                            DateType = DataManagement.Web.Request.ProspectDateType.All,
                            UserStatus = UserStatus.Active,
                            ReportPermission = ReportPermission.All,
                            FileName = fileName,
                            TimeZoneId = tzInfo.Id,
                            BaseUTcOffset = tzInfo.BaseUtcOffset,
                        };
                        requestBody = JsonConvert.SerializeObject(request);
                    }
                    break;
                case "channelpartnerreportbysubstatus":
                    {
                        RunAWSBatchForChannelPartnerReportBySubStatusRequest request = new()
                        {
                            FromDate = fromDate,
                            ToDate = toDate,
                            DateType = DateType.All,
                            ExportPermission = ReportPermission.All,
                            FileName = fileName,
                            TimeZoneId = tzInfo.Id,
                            BaseUTcOffset = tzInfo.BaseUtcOffset,
                        };
                        requestBody = JsonConvert.SerializeObject(request);
                    }
                    break;
                case "campaignreportbysubstatus":
                    {
                        RunAWSBatchForCampaignReportBySubStatusRequest request = new()
                        {
                            FromDate = fromDate,
                            ToDate = toDate,
                            DateType = DateType.All,
                            ExportPermission = ReportPermission.All,
                            FileName = fileName,
                            TimeZoneId = tzInfo.Id,
                            BaseUTcOffset = tzInfo.BaseUtcOffset,
                        };
                        requestBody = JsonConvert.SerializeObject(request);
                    }
                    break;
                case "dataagencyreportbystatus":
                    {
                        RunAWSBatchForDataStatusReportByAgencyRequest request = new()
                        {
                            FromDate = fromDate,
                            ToDate = toDate,
                            DateType = DataManagement.Web.Request.ProspectDateType.All,
                            ReportPermission = ReportPermission.All,
                            UserStatus = UserStatus.Active,
                            FileName = fileName,
                            TimeZoneId = tzInfo.Id,
                            BaseUTcOffset = tzInfo.BaseUtcOffset,
                        };
                        requestBody = JsonConvert.SerializeObject(request);
                    }
                    break;
                case "revenueuservssource":
                    {
                        RunAWSBatchForRevenueUserVsSourceReportRequest request = new()
                        {
                            FromDate = fromDate,
                            ToDate = toDate,
                            DateType = DateType.All,
                            UserStatus = UserStatus.Active,
                            ExportPermission = ReportPermission.All,
                            FileName = fileName,
                            TimeZoneId = tzInfo.Id,
                            BaseUTcOffset = tzInfo.BaseUtcOffset,
                        };
                        requestBody = JsonConvert.SerializeObject(request);
                    }
                    break;
                case "revenueuservssubsource":
                    {
                        RunAWSBatchForRevenueUserVsSubSourceSourceReportRequest request = new()
                        {
                            FromDate = fromDate,
                            ToDate = toDate,
                            DateType = DateType.All,
                            UserStatus = UserStatus.Active,
                            ExportPermission = ReportPermission.All,
                            FileName = fileName,
                            TimeZoneId = tzInfo.Id,
                            BaseUTcOffset = tzInfo.BaseUtcOffset,
                        };
                        requestBody = JsonConvert.SerializeObject(request);
                    }
                    break;
            }
            return requestBody;
        }
    }
    public class TimezoneInfo
    {
        public string? TimeZoneId { get; set; }
        public string? TimeZoneDisplay { get; set; }
        public string? TimeZoneName { get; set; }
        public TimeSpan BaseUTcOffset { get; set; }
    }
}
