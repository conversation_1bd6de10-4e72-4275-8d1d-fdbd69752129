﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.Integration;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Integration.Web.Requests
{
    public class CreateGoogleAdLeadFormAccountRequest : IRequest<Response<Guid>>
    {
        public string? AccountName { get; set; }
        public List<string>? ToRecipients { get; set; }
        public List<string>? CcRecipients { get; set; }
        public List<string>? BccRecipients { get; set; }
        public string? LoginEmail { get; set; }

    }
    public class CreateGoogleAdLeadFormIntegrationHandler : IRequestHandler<CreateGoogleAdLeadFormAccountRequest, Response<Guid>>
    {
        private readonly IRepositoryWithEvents<GoogleAdLeadFormIntegrationInfo> _googleAdLeadFormIntegrationInfo;
        private readonly IBlobStorageService _blobStorageService;
        private readonly ICurrentUser _currentUser;
        IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccInfoRepo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        public CreateGoogleAdLeadFormIntegrationHandler(IRepositoryWithEvents<GoogleAdLeadFormIntegrationInfo> googleAdLeadFormIntegrationInfo,
            ICurrentUser currentUser,
            IBlobStorageService blobStorageService,
            IRepositoryWithEvents<IntegrationAccountInfo> integrationAccInfoRepo,
            ILeadRepositoryAsync leadRepositoryAsync)
        {
            _googleAdLeadFormIntegrationInfo = googleAdLeadFormIntegrationInfo;
            _blobStorageService = blobStorageService;
            _currentUser = currentUser;
            _integrationAccInfoRepo = integrationAccInfoRepo;
            _leadRepositoryAsync = leadRepositoryAsync;
        }
        public async Task<Response<Guid>> Handle(CreateGoogleAdLeadFormAccountRequest request, CancellationToken cancellationToken)
        {
            IDictionary<string, string> data = null;
            GoogleAdLeadFormIntegrationInfo? googleAdIntegrationInfo = new();
            googleAdIntegrationInfo = CreateIntegrationEntity(request);
            googleAdIntegrationInfo.AccountName = request.AccountName;
            var tenantId = _currentUser.GetTenant();
            var apiKey = ApiKeyHelper.GenerateApiKey(googleAdIntegrationInfo.Id);
            googleAdIntegrationInfo.ApiKey = apiKey;
            await _googleAdLeadFormIntegrationInfo.AddAsync(googleAdIntegrationInfo);
            data = new Dictionary<string, string>(IntegrationTemplateBuilder.CreateGoogleAdIntegrationTemplate(googleAdIntegrationInfo, tenantId));
            string key = string.Empty;
            byte[] bytes = IntegrationExcelHelper.CreateExcelData(data).ToArray();
            string fileName = $"{tenantId}-{googleAdIntegrationInfo.Id}-{DateTime.Now.ToString("yyyy_MM_dd-HH_mm_ss")}.xlsx";
            string folder = "Integration";
            key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", folder, fileName, bytes);
            googleAdIntegrationInfo.FileUrl = key;
            await _googleAdLeadFormIntegrationInfo.UpdateAsync(googleAdIntegrationInfo);
            string fileUrl = await _blobStorageService.GetPreSignedURL(_blobStorageService?.BucketName ?? "prd-leadratblack", googleAdIntegrationInfo.FileUrl);
            IntegrationAccountInfo integrationAccInfo = null;
            #region Add to IntegrationAccountInfo Table
            try
            {
                integrationAccInfo = new IntegrationAccountInfo()
                {
                    Id = Guid.NewGuid(),
                    AccountName = request.AccountName,
                    LoginEmail = request.LoginEmail,
                    LeadSource = LeadSource.GoogleAds,
                    LicenseId = Guid.NewGuid(),
                    JsonTemplate = "",
                    GoogleadLeadFormId = googleAdIntegrationInfo.Id,
                    ToRecipients = request.ToRecipients,
                    CcRecipients = request.CcRecipients,
                    BccRecipients = request.BccRecipients,
                };
              await _integrationAccInfoRepo.AddAsync(integrationAccInfo);
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CreateGoogleAdLeadFormIntegrationHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            #endregion
             
            return new(integrationAccInfo.Id, default);
        }
        private GoogleAdLeadFormIntegrationInfo CreateIntegrationEntity(CreateGoogleAdLeadFormAccountRequest command)
        {
            return new GoogleAdLeadFormIntegrationInfo()
            {
                Id = Guid.NewGuid(),
                AccountName = command.AccountName,
            };
        }

    }
}
