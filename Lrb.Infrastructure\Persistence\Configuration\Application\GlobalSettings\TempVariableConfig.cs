﻿using Finbuckle.MultiTenant.EntityFrameworkCore;
using Lrb.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Lrb.Infrastructure.Persistence.Configuration.Application
{
    public class TempVariableConfig : IEntityTypeConfiguration<TempVariable>
    {
        public void Configure(EntityTypeBuilder<TempVariable> builder)
        {
            builder.IsMultiTenant();
            builder.Property(i => i.LeadVariables).Metadata.SetProviderClrType(null);
            builder.Property(i => i.LeadEnquiryVariables).Metadata.SetProviderClrType(null);
            builder.Property(i => i.UserVariables).Metadata.SetProviderClrType(null);
            builder.Property(i => i.IVRVariables).Metadata.SetProviderClrType(null);
            builder.Property(i => i.InvoiceConstantVariables).Metadata.SetProviderClrType(null);
        }
    }
}
