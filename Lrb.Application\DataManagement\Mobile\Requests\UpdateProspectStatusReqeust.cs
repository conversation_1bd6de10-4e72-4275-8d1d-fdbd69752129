﻿using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.DataManagement.Mobile.Dtos;
using Lrb.Application.DataManagement.Mobile.Mapping;
using Lrb.Application.DataManagement.Mobile.Specs;
using Lrb.Application.GlobalSettings.Mobile;
using Lrb.Application.Identity.Users;
using Lrb.Domain.Entities;

namespace Lrb.Application.DataManagement.Mobile.Requests
{
    public class UpdateProspectStatusReqeust : IRequest<Response<bool>>
    {
        public Guid Id { get; set; }
        public Guid StatusId { get; set; }
        public DateTime? ScheduleDate { get; set; }
        public string? Notes { get; set; }
    }

    public class UpdateProspectStatusReqeustHandler : IRequestHandler<UpdateProspectStatusReqeust, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Prospect> _prospectRepo;
        private readonly IRepositoryWithEvents<CustomProspectStatus> _prospectStatusRepo;
        private readonly IRepositoryWithEvents<ProspectHistory> _prospcetHistoryRepo;
        private readonly IUserService _userService;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.MasterProspectSource> _prospectSourceRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.MasterData.MasterPropertyType> _propertyTypeRepo;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<ProspectHistory> _prospectHistoryRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalsettingRepo;
        private readonly INotificationSenderService _notificationSenderService;
        public UpdateProspectStatusReqeustHandler(
            IRepositoryWithEvents<Prospect> prospectRepo,
            IRepositoryWithEvents<CustomProspectStatus> prospectStatusRepo,
            IRepositoryWithEvents<ProspectHistory> prospcetHistoryRepo,
            IUserService userService,
            IRepositoryWithEvents<Lrb.Domain.Entities.MasterProspectSource> prospectSourceRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.MasterData.MasterPropertyType> propertyTypeRepo,
            ICurrentUser currentUser,
            IRepositoryWithEvents<ProspectHistory> prospectHistoryRepo,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalsettingRepo,
             INotificationSenderService notificationSenderService
            )
        {
            _prospectRepo = prospectRepo;
            _prospectStatusRepo = prospectStatusRepo;
            _propertyTypeRepo = propertyTypeRepo;
            _userService = userService;
            _prospcetHistoryRepo = prospcetHistoryRepo;
            _prospectSourceRepo = prospectSourceRepo;
            _currentUser = currentUser;
            _prospectHistoryRepo = prospectHistoryRepo;
            _globalsettingRepo = globalsettingRepo;
            _notificationSenderService = notificationSenderService;
        }
        public async Task<Response<bool>> Handle(UpdateProspectStatusReqeust request, CancellationToken cancellationToken)
        {
            var existingProspect = await _prospectRepo.FirstOrDefaultAsync(new GetProspectByIdSpecs(request.Id), cancellationToken);
            var oldProspect = existingProspect.Adapt<ViewProspectDto>();
            if (existingProspect == null)
            {
                throw new NotFoundException("No Prospect Found By This Id");
            }
            if (request.StatusId == default)
            {
                throw new InvalidOperationException("The Status Id is not valid");
            }
            var chaildStatuses = await _prospectStatusRepo.ListAsync(new GetProspectStatusByBaseIdSpecs(request.StatusId));
            if (chaildStatuses?.Any() ?? false)
            {
                throw new ArgumentException("Please provide child status id.");
            }
            var status = await _prospectStatusRepo.GetByIdAsync(request.StatusId, cancellationToken);
            existingProspect.ScheduleDate = request.ScheduleDate;
            existingProspect.Notes = request.Notes;
            existingProspect.Status = status;
            var currentUser  = _currentUser.GetUserId();
            if (status?.Status == "qualified")
            {
                existingProspect.QualifiedDate = DateTime.UtcNow;
                existingProspect.QualifiedBy = _currentUser.GetUserId();
            }
            await _prospectRepo.UpdateAsync(existingProspect);
            var currentUserId = _currentUser.GetUserId();
            var statuses = await _prospectStatusRepo.ListAsync();
            var prospectVM = existingProspect.Adapt<ViewProspectDto>();
            var userIds = new List<string?>
            {
                prospectVM.AssignTo.ToString(),
                prospectVM.LastModifiedBy.ToString(),
                prospectVM.AssignedFrom.ToString(),
                prospectVM.SourcingManager.ToString(),
                prospectVM.ClosingManager.ToString(),
                currentUserId.ToString() ,
            };
            #region History
            var userDetails = await _userService.GetListOfUsersByIdsAsync(userIds, cancellationToken);

            prospectVM = await ProspectHistoryHelper.SetUserViewForProspectV1(prospectVM, userDetails, cancellationToken);
            oldProspect = await ProspectHistoryHelper.SetUserViewForProspectV1(oldProspect, userDetails, cancellationToken);
            var histories = await ProspectHistoryHelper.UpdateProspectHistoryForVM(prospectVM, oldProspect, currentUserId, 1, statuses, null, null, _userService, cancellationToken);
            await _prospectHistoryRepo.AddRangeAsync(histories);
            #endregion
            var globalSetting = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec());
            if (request.ScheduleDate != null)
            {
                await SendProspectStatusChangeNotificationsAsync(existingProspect, prospectVM, globalSetting, currentUserId: currentUser);
            }
            return new(true);
        }
        public async Task<bool> SendProspectStatusChangeNotificationsAsync(Domain.Entities.Prospect prospect, ViewProspectDto prospectDto, Domain.Entities.GlobalSettings? globalSettings, Guid currentUserId = default, List<UserDetailsDto>? allUserDetails = null, List<NotificationContent>? contents = null)
        {
            try
            {

                switch (prospectDto.Status?.Status)
                {
                    case "Not Interested":
                        var response = await _notificationSenderService.DeleteScheduledNotificationsAsync(prospect);
                        if (prospectDto.AssignTo != Guid.Empty && prospectDto.AssignTo != currentUserId)
                        {
                            List<string> callbackStatusEvent1JobIds = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.DataStatusToNotInterested, prospect, prospectDto.AssignTo, prospectDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { prospect.AssignTo.ToString(), prospect.CreatedBy.ToString(), prospect.LastModifiedBy.ToString() }, currentUserId: currentUserId, globalSettings: globalSettings, allUserDetails: allUserDetails, contents: contents);
                        }
                        await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotInterestedReminder, prospect, prospectDto?.AssignTo, prospectDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { prospect.AssignTo.ToString(), prospect.CreatedBy.ToString(), prospect.LastModifiedBy.ToString() }, currentUserId: currentUserId, globalSettings: globalSettings, allUserDetails: allUserDetails, contents: contents);

                        break;
                    case "Not Reachable":
                        response = await _notificationSenderService.DeleteScheduledNotificationsAsync(prospect);
                        if (prospectDto.AssignTo != Guid.Empty && prospectDto.AssignTo != currentUserId)
                        {
                            List<string> callbackStatusEvent1JobIds = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.DataStatusToNotReachable, prospect, prospectDto.AssignTo, prospectDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { prospect.AssignTo.ToString(), prospect.CreatedBy.ToString(), prospect.LastModifiedBy.ToString() }, currentUserId: currentUserId, globalSettings: globalSettings, allUserDetails: allUserDetails, contents: contents);
                        }
                        await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotReachableReminder, prospect, prospectDto?.AssignTo, prospectDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { prospect.AssignTo.ToString(), prospect.CreatedBy.ToString(), prospect.LastModifiedBy.ToString() }, currentUserId: currentUserId, globalSettings: globalSettings, allUserDetails: allUserDetails, contents: contents);
                        break;
                    case "Follow Up":
                        response = await _notificationSenderService.DeleteScheduledNotificationsAsync(prospect);
                        if (prospectDto.AssignTo != Guid.Empty && prospectDto.AssignTo != currentUserId)
                        {
                            List<string> callbackStatusEvent1JobIds = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.DataStatusToFollowUp, prospect, prospectDto.AssignTo, prospectDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { prospect.AssignTo.ToString(), prospect.CreatedBy.ToString(), prospect.LastModifiedBy.ToString() }, currentUserId: currentUserId, globalSettings: globalSettings, allUserDetails: allUserDetails, contents: contents);
                        }
                        await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.FollowUpReminder, prospect, prospectDto?.AssignTo, prospectDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { prospect.AssignTo.ToString(), prospect.CreatedBy.ToString(), prospect.LastModifiedBy.ToString() }, currentUserId: currentUserId, globalSettings: globalSettings, allUserDetails: allUserDetails, contents: contents);
                        break;

                }

                return new();
            }
            catch { return false; }
        }

    }
}
