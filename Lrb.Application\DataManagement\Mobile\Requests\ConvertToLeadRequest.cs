﻿using Lrb.Application.Common.PushNotification;
using Lrb.Application.DataManagement.Mobile.Dtos;
using Lrb.Application.DataManagement.Mobile.Mapping;
using Lrb.Application.DataManagement.Mobile.Specs;
using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Mobile;
using Lrb.Application.Lead.Mobile.Specs.v1;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;

namespace Lrb.Application.DataManagement.Mobile.Requests
{
    public class ConvertToLeadRequest : IRequest<Response<bool>>
    {
        public Guid Id { get; set; }
        public Guid? AssignTo { get; set; }
        public Guid? LeadStatusId { get; set; }
        public string? Notes { get; set; }
        public DateTime? ScheduledDate { get; set; }
        public List<string>? Projects { get; set; }
        public string? ConvertedDateTime { get; set; }
    }

    public class ConvertToLeadRequestHander : IRequestHandler<ConvertToLeadRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Prospect> _prospectRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Lead> _leadRepo;
        private readonly IRepositoryWithEvents<MasterProspectSource> _prospectSourceRepo;
        private readonly IRepositoryWithEvents<CustomProspectStatus> _prospectStatusRepo;
        //private readonly IRepositoryWithEvents<MasterLeadStatus> _masterLeadStatusRepo;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.LeadHistory> _leadHistoryRepo;
        private readonly INotificationSenderService _notificationSenderService;
        private readonly IUserService _userService;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<MasterPropertyType> _propertyTypeRepo;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customLeadStatusRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.TempProjects> _tempProjectRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingsRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.ProspectHistory> _prospcetHistoryRepo;

        public ConvertToLeadRequestHander(
            IRepositoryWithEvents<Prospect> prospectRepo,
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepo,
            IRepositoryWithEvents<MasterProspectSource> prospectSourceRepo,
            IRepositoryWithEvents<CustomProspectStatus> prospectStatusRepo,
            ICurrentUser currentUser,
            //IRepositoryWithEvents<MasterLeadStatus> masterLeadStatusRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.LeadHistory> leadHistoryRepo,
            INotificationSenderService notificationSenderService,
            IUserService userService,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<MasterPropertyType> propertyTypeRepo,
            IRepositoryWithEvents<CustomMasterLeadStatus> customLeadStatusRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.TempProjects> tempProjectRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.Project> projectRepo,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingsRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.ProspectHistory> prospcetHistoryRepo

            )
        {
            _prospectRepo = prospectRepo;
            _leadRepo = leadRepo;
            _prospectSourceRepo = prospectSourceRepo;
            _prospectStatusRepo = prospectStatusRepo;
            _currentUser = currentUser;
            //_masterLeadStatusRepo = masterLeadStatusRepo;
            _leadHistoryRepo = leadHistoryRepo;
            _notificationSenderService = notificationSenderService;
            _userService = userService;
            _leadRepositoryAsync = leadRepositoryAsync;
            _propertyTypeRepo = propertyTypeRepo;
            _customLeadStatusRepo = customLeadStatusRepo;
            _tempProjectRepo = tempProjectRepo;
            _projectRepo = projectRepo;
            _globalSettingsRepo = globalSettingsRepo;
            _prospcetHistoryRepo = prospcetHistoryRepo;
        }

        public async Task<Response<bool>> Handle(ConvertToLeadRequest request, CancellationToken cancellationToken)
        {
            var response = new Response<AssignmentProspectDto>();
            Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
            var existingProspect = await _prospectRepo.FirstOrDefaultAsync(new GetProspectByIdSpecs(request.Id));
            var oldProspect = existingProspect?.Adapt<Dtos.ViewProspectDto>();
            if (existingProspect == null)
            {
                throw new NotFoundException("Data not Found");
            }
            if (existingProspect.IsConvertedToLead)
            {
                return new()
                {
                    Message = "Selected Data already converted to lead.",
                    Data = false,
                    Succeeded = false
                };
            }
            var lead = CreateLeadFromProspect(existingProspect);
            var currentUserId = _currentUser.GetUserId(); ;
            string createdBy = null;
            try
            {
                var userDetails = await _userService.GetAsync(currentUserId.ToString(), cancellationToken);
                createdBy = $"{userDetails.FirstName} {userDetails.LastName}";
            }
            catch
            {

            }
            if (!string.IsNullOrEmpty(request.Notes))
            {
                lead.Notes = $"{request.Notes}\nQualifiedBy: {createdBy}" +
               (request.ConvertedDateTime != null ? $"\nConvertedDatetime: {request.ConvertedDateTime}" : string.Empty);

            }
            else
            {
                lead.Notes = $"{lead.Notes ?? string.Empty}\nQualifiedBy: {createdBy}{(request.ConvertedDateTime != null ? $"\nConvertedDatetime: {request.ConvertedDateTime}" : "")}";
            }
            var newStatus = (await _customLeadStatusRepo.ListAsync(cancellationToken));

            if (request.LeadStatusId != null && request.LeadStatusId != Guid.Empty)
            {
                var customLeadStatus = newStatus.Where(i => i.Id == request.LeadStatusId).FirstOrDefault();
                if (customLeadStatus?.Level < 1)
                {
                    throw new ArgumentException("Provide Child Id of this Status");
                }
                lead.CustomLeadStatus = customLeadStatus ?? newStatus?.FirstOrDefault(i => i.IsDefault) ?? newStatus?.FirstOrDefault(i => i.Status == "new");
                lead.ScheduledDate = request.ScheduledDate;
            }
            else
            {
                lead.CustomLeadStatus = newStatus?.FirstOrDefault(i => i.IsDefault) ?? newStatus?.FirstOrDefault(i => i.Status == "new");
            }

            #region Projects
            try
            {
                List<Lrb.Domain.Entities.Project> tempProjects = new();
                request.Projects = (request.Projects?.Any() ?? false) ? request.Projects.Where(i => !string.IsNullOrWhiteSpace(i)).ToList() : null;
                if (request.Projects?.Any() ?? false)
                {
                    foreach (var newProject in request.Projects)
                    {
                        Lrb.Domain.Entities.Project? existingProject = (await _projectRepo.ListAsync(new GetProjectByIdSpecsV2(newProject), cancellationToken)).FirstOrDefault();
                        if (existingProject != null)
                        {
                            tempProjects.Add(existingProject);
                        }
                        else
                        {
                            Domain.Entities.Project tempProject = new() { Name = newProject };
                            tempProject = await _projectRepo.AddAsync(tempProject, cancellationToken);
                            tempProjects.Add(tempProject);
                        }
                    }
                    lead.Projects = tempProjects;
                }

            }
            catch (Exception ex)
            {
                throw;
            }
            #endregion

            lead.TagInfo = new LeadTag();

            #region Enquiry
            var enquiry = existingProspect?.Enquiries?.Where(i => i.IsPrimary).FirstOrDefault();
            MasterPropertyType? propertyType = null;
            if (enquiry?.PropertyType != null)
            {
                propertyType = await _propertyTypeRepo.GetByIdAsync(enquiry.PropertyType.Id, cancellationToken);
                if (propertyType == null)
                {
                    throw new InvalidDataException("Property type Id does not belong to master data");
                }
            }

            if (enquiry != null)
            {
                var leadEnquiry = CreateLeadEnquiryFromProspectEnquiry(enquiry, existingProspect?.PossesionDate);
                leadEnquiry.PropertyType = propertyType;
                lead.Enquiries = new List<LeadEnquiry>();
                lead.Enquiries.Add(leadEnquiry);
            }
            #endregion

            if (request.AssignTo == Guid.Empty || request.AssignTo == default)
            {
                lead.AssignTo = currentUserId;
                //lead.AssignedFrom = currentUserId;
            }
            else
            {
                lead.AssignTo = request?.AssignTo ?? Guid.Empty;
                //lead.AssignedFrom = currentUserId;
            }
            // Set OriginalOwner to the assigned user when first assigned
            if (lead.AssignTo != Guid.Empty && lead.OriginalOwner == null)
            {
                lead.OriginalOwner = lead.AssignTo;
            }
            try
            {
                Guid? parentLeadId = null;
                var rootLead = await _leadRepo.FirstOrDefaultAsync(new Lrb.Application.DataManagement.Mobile.Specs.GetContcactNoSpec(lead.ContactNo, lead.AlternateContactNo), cancellationToken);
                if (rootLead != null)
                {
                    if (globalSettings.IsStickyAgentEnabled == true)
                    {
                        lead.RootId = rootLead.Id;
                        lead.DuplicateLeadVersion = "D" + (rootLead.ChildLeadsCount + 1);
                        lead.ParentLeadId = parentLeadId != null ? parentLeadId : rootLead.Id;
                        rootLead.ChildLeadsCount += 1;
                        try
                        {
                            await _leadRepo.UpdateAsync(rootLead);
                        }
                        catch (Exception ex)
                        {
                            var error = new LrbError()
                            {
                                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                ErrorSource = ex?.Source,
                                StackTrace = ex?.StackTrace,
                                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                ErrorModule = "ConvertToLeadRequestHander -> Handle() -> UpdateParentLead()"
                            };
                            await _leadRepositoryAsync.AddErrorAsync(error);
                        }
                    }
                    else if (rootLead?.AssignTo != null)
                    {
                        response.Data = new AssignmentProspectDto
                        {
                            ProspectId = request.Id,
                            AssignTo = rootLead.AssignTo
                        };

                        if (response.Data == null)
                        {
                            response.Data = new AssignmentProspectDto();
                        }
                    }
                    if ((response.Data.AssignTo != null && response.Data.AssignTo != lead.AssignTo) || response.Data == null)
                    {
                        lead.RootId = rootLead.Id;
                        lead.DuplicateLeadVersion = "D" + (rootLead.ChildLeadsCount + 1);
                        lead.ParentLeadId = parentLeadId != null ? parentLeadId : rootLead.Id;
                        rootLead.ChildLeadsCount += 1;
                        try
                        {
                            await _leadRepo.UpdateAsync(rootLead);
                        }
                        catch (Exception ex)
                        {
                            var error = new LrbError()
                            {
                                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                ErrorSource = ex?.Source,
                                StackTrace = ex?.StackTrace,
                                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                ErrorModule = "ConvertToLeadRequestHander -> Handle() -> UpdateParentLead()"
                            };
                            await _leadRepositoryAsync.AddErrorAsync(error);
                        }
                    }

                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "ConvertToLeadRequestHander -> Handle() -> ScheduleNotificationsAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }

            try
            {
                /*  if (response.Data == null)
                  {
                      response.Data = new AssignmentProspectDto();
                  }*/
                if (response.Data == null || (response.Data.AssignTo != null && response.Data.AssignTo != lead.AssignTo))
                {
                    lead = await _leadRepo.AddAsync(lead);
                    var prospectstatus = (await _prospectStatusRepo.ListAsync(new GetQualifiedStatusSpecs(), cancellationToken)).FirstOrDefault();
                    existingProspect.IsConvertedToLead = true;
                    existingProspect.ConvertedBy = currentUserId;
                    existingProspect.IsQualified = true;
                    existingProspect.ConvertedDate = DateTime.UtcNow;
                    existingProspect.Status = prospectstatus;
                    if (existingProspect.QualifiedDate == null)
                    {
                        existingProspect.QualifiedDate = DateTime.UtcNow;
                    }
                    if (existingProspect.QualifiedBy == null)
                    {
                        existingProspect.QualifiedBy = currentUserId;
                    }
                    await _prospectRepo.UpdateAsync(existingProspect);
                    #region History
                    var statuses = await _prospectStatusRepo.ListAsync();
                    var propertyTypes = await _propertyTypeRepo.ListAsync();
                    var sources = await _prospectSourceRepo.ListAsync();
                    var prospectVM = existingProspect.Adapt<Dtos.ViewProspectDto>();
                    var userIds = new List<string?>
                    {
                         prospectVM.LastModifiedBy.ToString(),
                         prospectVM.SourcingManager.ToString(),
                         prospectVM.ClosingManager.ToString(),
                    };
                    var userDetails = await _userService.GetListOfUsersByIdsAsync(userIds, cancellationToken);
                    prospectVM = await ProspectHistoryHelper.SetUserViewForProspectV1(prospectVM, userDetails, cancellationToken);
                    oldProspect = await ProspectHistoryHelper.SetUserViewForProspectV1(oldProspect, userDetails, cancellationToken);
                    var histories = await ProspectHistoryHelper.UpdateProspectHistoryForVM(prospectVM, oldProspect, currentUserId, 1, statuses, propertyTypes, sources, _userService, cancellationToken);
                    await _prospcetHistoryRepo.AddRangeAsync(histories);
                    #endregion
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "ConvertToLeadRequestHander -> Handle() -> Add Lead()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw new Exception(ex?.Message);
            }
            if (response.Data == null || (response.Data.AssignTo != null && response.Data.AssignTo != lead.AssignTo))
            {
                var leadDto = lead.Adapt<ViewLeadDto>();
                await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken);
                var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                try
                {
                    await _leadHistoryRepo.AddAsync(leadHistory);
                }
                catch (Exception ex)
                {
                    var error = new LrbError()
                    {
                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                        ErrorSource = ex?.Source,
                        StackTrace = ex?.StackTrace,
                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                        ErrorModule = "ConvertToLeadRequestHander -> Handle() -> Add History()"
                    };
                    await _leadRepositoryAsync.AddErrorAsync(error);
                    throw new Exception(ex?.Message);
                }
                if (lead.AssignTo != default && lead.AssignTo != Guid.Empty && lead.AssignTo != _currentUser.GetUserId())
                {
                    try
                    {
                        List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadAssignment, lead, lead.AssignTo, leadDto.AssignedUser?.Name, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, status: leadDto.Status?.DisplayName);
                    }
                    catch (Exception ex)
                    {
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "ConvertToLeadRequestHander -> Handle() -> ScheduleNotificationsAsync()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                        throw new Exception(ex?.Message);
                    }
                }
            }
            //return new(true);
            if (response.Data != null && response?.Data?.AssignTo == lead.AssignTo)
            {
                return new Response<bool>
                {
                    /*  Data = new AssignmentProspectDto
                      {
                          // Initialize properties of AssignmentProspectDto
                          ProspectId = request.Id,
                          AssignTo = request.AssignTo
                      },*/
                    Data = false,
                    Succeeded = true,
                    Message = "Prospect could not be Converted to lead"
                };
            }
            else
            {
                return new Response<bool>
                {

                    Data = true,
                    Succeeded = true,
                    Message = "successfully Converted to lead"
                };

            }
        }

        public Lrb.Domain.Entities.Lead CreateLeadFromProspect(Prospect prospect)
        {
            var currentUserId = _currentUser.GetUserId();
            Lrb.Domain.Entities.Lead lead = new();
            lead.Name = prospect?.Name?.Trim() ?? string.Empty;
            lead.ContactNo = prospect?.ContactNo?.Trim() ?? string.Empty;
            lead.AlternateContactNo = prospect?.AlternateContactNo?.Trim();
            lead.Notes = prospect?.Notes;
            lead.Email = prospect?.Email;
            lead.Address = new Address()
            {
                SubLocality = prospect?.Address?.SubLocality,
                Locality = prospect?.Address?.Locality,
                PlaceId = prospect?.Address?.PlaceId,
                City = prospect?.Address?.City,
                District = prospect?.Address?.District,
                State = prospect?.Address?.State,
                Country = prospect?.Address?.Country,
                TowerName = prospect?.Address?.TowerName,
                Community = prospect?.Address?.Community,
                SubCommunity = prospect?.Address?.SubCommunity,
            };
            lead.LeadNumber = lead?.Name?[0].ToString().ToUpper() + new Random().Next(1000, 9999).ToString() + DateTime.UtcNow.ToString("FFFFFFF");
            lead.AgencyName = prospect?.AgencyName?.Trim();
            lead.Agencies = prospect?.Agencies;
            lead.ChannelPartners = prospect?.ChannelPartners;
            lead.ClosingManager = prospect?.ClosingManager;
            lead.SourcingManager = prospect?.SourcingManager;
            lead.ContactRecords = prospect?.ContactRecords;
            lead.CompanyName = prospect?.CompanyName;
            lead.Properties = prospect?.Properties;
            lead.Projects = prospect?.Projects;
            lead.Profession = prospect?.Profession ?? Profession.None;
            lead.IsConvertedFromData = true;
            lead.QualifiedBy = currentUserId;
            lead.Designation = prospect?.Designation;
            lead.ChannelPartnerContactNo = prospect?.ExecutiveContactNo;
            lead.ChannelPartnerName = prospect?.ExecutiveName;
            lead.ReferralContactNo = prospect?.ReferralContactNo;
            lead.ReferralName = prospect?.ReferralName;
            lead.ReferralEmail = prospect?.ReferralEmail;
            lead.Nationality = prospect?.Nationality;
            lead.Campaigns = prospect?.Campaigns;
            lead.PossesionType = prospect?.Enquiries?.FirstOrDefault()?.PossesionType;
            lead.LandLine = prospect?.LandLine;
            lead.CountryCode = prospect?.CountryCode;
            lead.AltCountryCode = prospect?.AltCountryCode;
            lead.Gender = prospect?.Gender;
            lead.MaritalStatus = prospect?.MaritalStatus;
            lead.DateOfBirth = prospect?.DateOfBirth;
            lead.AnniversaryDate = prospect?.AnniversaryDate;

            return lead;
        }

        #region Create Lead Enquiry

        public Lrb.Domain.Entities.LeadEnquiry CreateLeadEnquiryFromProspectEnquiry(ProspectEnquiry enquiry, DateTime? possionDate)
        {
            var leadEnquiry = new LeadEnquiry();
            LeadSource? source = EnumFromDescription.GetValueFromDescription<LeadSource>(enquiry?.Source.DisplayName ?? string.Empty);
            leadEnquiry.LeadSource = source ?? LeadSource.Direct;
            leadEnquiry.IsPrimary = true;
            var address = new Address()
            {
                SubLocality = enquiry?.Address?.SubLocality,
                Locality = enquiry?.Address?.Locality,
                PlaceId = enquiry?.Address?.PlaceId,
                City = enquiry?.Address?.City,
                District = enquiry?.Address?.District,
                State = enquiry?.Address?.State,
                Country = enquiry?.Address?.Country,
                TowerName = enquiry?.Address?.TowerName,
                Community = enquiry?.Address?.Community,
                SubCommunity = enquiry?.Address?.SubCommunity,
            };
            leadEnquiry.BHKs = enquiry?.BHKs;
            leadEnquiry.BHKTypes = enquiry?.BHKTypes;
            leadEnquiry.EnquiryTypes = enquiry?.EnquiryTypes;
            leadEnquiry.EnquiredFor = enquiry?.EnquiryType ?? EnquiryType.None;
            leadEnquiry.SubSource = enquiry?.SubSource?.ToLower();
            leadEnquiry.LowerBudget = enquiry?.LowerBudget;
            leadEnquiry.UpperBudget = enquiry?.UpperBudget;
            leadEnquiry.CarpetArea = enquiry?.CarpetArea;
            leadEnquiry.CarpetAreaInSqMtr = enquiry?.CarpetAreaInSqMtr;
            leadEnquiry.CarpetAreaUnitId = enquiry?.CarpetAreaUnitId ?? Guid.Empty;
            leadEnquiry.NoOfBHKs = enquiry?.NoOfBhks ?? default;
            leadEnquiry.Address = address;
            leadEnquiry.PossessionDate = possionDate;
            leadEnquiry.BuiltUpArea = enquiry?.BuiltUpArea;
            leadEnquiry.BuiltUpAreaUnitId = enquiry?.BuiltUpAreaUnitId ?? Guid.Empty;
            leadEnquiry.BuiltUpAreaInSqMtr = enquiry?.BuiltUpAreaInSqMtr;
            leadEnquiry.SaleableArea = enquiry?.SaleableArea;
            leadEnquiry.SaleableAreaInSqMtr = enquiry?.SaleableAreaInSqMtr;
            leadEnquiry.SaleableAreaUnitId = enquiry?.SaleableAreaUnitId ?? Guid.Empty;
            leadEnquiry.Beds = enquiry?.Beds;
            leadEnquiry.Baths = enquiry?.Baths;
            leadEnquiry.Floors = enquiry?.Floors;
            leadEnquiry.Furnished = enquiry?.Furnished;
            leadEnquiry.OfferType = enquiry?.OfferType;
            leadEnquiry.Currency = enquiry?.Currency;
            leadEnquiry.NetArea = enquiry?.NetArea;
            leadEnquiry.NetAreaInSqMtr = enquiry?.NetAreaInSqMtr;
            leadEnquiry.NetAreaUnitId = enquiry?.NetAreaUnitId ?? Guid.Empty;
            leadEnquiry.PropertyArea = enquiry?.PropertyArea;
            leadEnquiry.PropertyAreaInSqMtr = enquiry?.PropertyAreaInSqMtr;
            leadEnquiry.PropertyAreaUnitId = enquiry?.PropertyAreaUnitId ?? Guid.Empty;
            leadEnquiry.UnitName = enquiry?.UnitName;
            leadEnquiry.ClusterName = enquiry?.ClusterName;
            leadEnquiry.PropertyTypes = enquiry?.PropertyTypes;
            leadEnquiry.Purpose = enquiry?.Purpose;



            if (enquiry?.Addresses?.Any() ?? false)
            {
                var addresses = new List<Address>();
                foreach (var item in enquiry.Addresses)
                {
                    addresses.Add(new Address()
                    {
                        SubLocality = item?.SubLocality,
                        Locality = item?.Locality,
                        PlaceId = item?.PlaceId,
                        City = item?.City,
                        District = item?.District,
                        State = item?.State,
                        Country = item?.Country,
                        TowerName = item?.TowerName,
                        Community = item?.Community,
                        SubCommunity = item?.SubCommunity,
                    });
                }
                leadEnquiry.Addresses = addresses;
            }
            return leadEnquiry;
        }

        #endregion
    }
}
