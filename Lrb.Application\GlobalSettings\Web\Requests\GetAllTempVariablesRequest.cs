﻿using DocumentFormat.OpenXml.ExtendedProperties;
using Lrb.Application.Integration.Web;
using System.Text.RegularExpressions;

namespace Lrb.Application.GlobalSettings.Web
{
    public class GetAllTempVariablesRequest : IRequest<Response<TempVariableDto>>
    {

    }
    public class GetAllTempVariablesRequestHandler : IRequestHandler<GetAllTempVariablesRequest, Response<TempVariableDto>>
    {
        private readonly IReadRepository<TempVariable> _tempVariableRepo;

        public GetAllTempVariablesRequestHandler(IReadRepository<TempVariable> tempVariableRepo)
        {
            _tempVariableRepo = tempVariableRepo;
        }

        public async Task<Response<TempVariableDto>> Handle(GetAllTempVariablesRequest request, CancellationToken cancellationToken)
        {
            var tempVariable = await _tempVariableRepo.FirstOrDefaultAsync(new GetTempVariableSpec(), cancellationToken);
            var tempVariableDto = tempVariable?.Adapt<TempVariableDto>();
            List<TempVarWithDisplayNameDto> ivrVarsWithDisplayName = new();
            tempVariableDto?.IVRVariables?.ForEach(i =>
            {
                ivrVarsWithDisplayName.Add(new()
                {
                    Value = i,
                    DisplayName = GetFormattedDisplayName(i)
                });
            });
            List<TempVarWithDisplayNameDto> webhookVariablesWithDisplayName = new();
            tempVariableDto?.WebhookConstantVariables?.ForEach(i =>
            {
                webhookVariablesWithDisplayName.Add(new()
                {
                    Value = i,
                    DisplayName = GetFormattedDisplayName(i)
                });
            });
            List<TempVarWithDisplayNameDto> invoiceVariablesWithDisplayName = new();
            tempVariableDto?.InvoiceConstantVariables?.ForEach(i =>
            {
                invoiceVariablesWithDisplayName.Add(new()
                {
                    Value = i,
                    DisplayName = GetFormattedDisplayName(i)
                });
            });
            if (tempVariableDto != null)
            {
                tempVariableDto.IVRVariablesWithDisplayName = ivrVarsWithDisplayName;
                tempVariableDto.WebhookVariablesWithDisplayName = webhookVariablesWithDisplayName;
                tempVariableDto.invoiceVariablesWithDisplayName = invoiceVariablesWithDisplayName;
            }
            return new(tempVariableDto ?? new());
        }
        public static string? GetFormattedDisplayName(string? value)
        {
            if (string.IsNullOrWhiteSpace(value))
            {
                return value;
            }
            value = Regex.Replace(value, @"[-_ .#]", string.Empty);
            var words = Regex.Matches(value, @"([A-Z][a-z]+)")
                        .Cast<Match>()
                        .Select(m => m.Value);
            value = string.Join(" ", words);
            return value;
        }
    }
}
