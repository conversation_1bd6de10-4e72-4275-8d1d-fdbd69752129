﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Lrb.Application.Property.Mobile.Requests;

namespace Lrb.Application.Property.Mobile.Specs
{
    public class GetPropertyForGalleryDropdownSpec : EntitiesByPaginationFilterSpec<Domain.Entities.Property>
    {
        public GetPropertyForGalleryDropdownSpec(GetGalleryDropdownDataRequest filter) : base(filter)
        {
            Query.Include(i => i.Galleries);
            Query.Where(i => !i.IsDeleted);
        }
    }
}