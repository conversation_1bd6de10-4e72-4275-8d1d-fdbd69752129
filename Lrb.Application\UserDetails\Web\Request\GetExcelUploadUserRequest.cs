﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.UserDetails.Web.Dtos;
using Lrb.Application.Utils;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;

namespace Lrb.Application.UserDetails.Web.Request
{
    public class GetExcelUploadUserRequest : IRequest<Response<FileColumnUserDto>>
    {
        public IFormFile? File { get; set; }
        public string Origin { get; set; }

        public GetExcelUploadUserRequest(IFormFile file, string origin)
        {
            File = file;
            Origin = origin;  

        }
    }

    public class GetExcelUploadUserRequestHandler : IRequestHandler<GetExcelUploadUserRequest, Response<FileColumnUserDto>>
    {
        private readonly IBlobStorageService _blobStorageService;
        private readonly Serilog.ILogger _logger;
        public GetExcelUploadUserRequestHandler(IBlobStorageService blobStorageService, Serilog.ILogger logger)
        {
            _blobStorageService = blobStorageService;
            _logger = logger;
        }
        public async Task<Response<FileColumnUserDto>> Handle(GetExcelUploadUserRequest request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.Information("GetExcelUploadRequestHandler -> Handle: " + JsonConvert.SerializeObject(request));
                var file = request.File;
                if (file == null)
                {
                    throw new ArgumentNullException(nameof(file));
                }
                var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", "User", file);
                List<string> columns = new List<string>();
                Dictionary<string, List<string>> multiSheetColumn = new();
                if (key.Split(".").LastOrDefault() == "csv")
                {
                    columns = await CSVHelper.GetCSVColumns(file);
                    multiSheetColumn.Add("Default", columns);
                }
                else
                {
                    columns = EPPlusExcelHelper.GetFileColumns(file);
                    multiSheetColumn = EPPlusExcelHelper.GetFileColumnsOfMultiSheets(file);
                }
                FileColumnUserDto excelColumnsViewModel = new()
                {
                    S3BucketKey = key,
                    ColumnNames = columns,
                    MultiSheetColumnNames = multiSheetColumn,
                    
                };
                return new(excelColumnsViewModel);
            }
            catch (Exception ex)
            {
                _logger.Error("GetExcelColumnsUsingEPPlusRequestHandler -> Error: " + JsonConvert.SerializeObject(ex));
                throw;
            }
        }
    }
}
