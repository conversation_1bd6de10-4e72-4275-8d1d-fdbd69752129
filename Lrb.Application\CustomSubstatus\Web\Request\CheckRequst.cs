﻿using Lrb.Application.Common;
using Lrb.Application.CustomMasterLeadSubStatus.Web.Request;
using Lrb.Domain.Entities.MasterData;
using MediatR;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Lrb.Application.CustomSubstatus.Web.Request
{
    public class CheckRequst : IRequest<Response<bool>>
    {
        public string? Status { get; set; }

        public CheckRequst(string status)
        {
            Status = status;
        }

        public class CheckRequstHandler : IRequestHandler<CheckRequst, Response<bool>>
        {
            private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customRepo;
            private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepository;

            public CheckRequstHandler(
                IRepositoryWithEvents<CustomMasterLeadStatus> customRepo,
                IRepositoryWithEvents<Domain.Entities.Lead> leadRepository)
            {
                _customRepo = customRepo;
                _leadRepository = leadRepository;
            }

            public async Task<Response<bool>> Handle(CheckRequst request, CancellationToken cancellationToken)
            {
                var customStatuses = await _customRepo.ListAsync();

                customStatuses.ForEach(c =>
                {
                    if (c.Status != null)
                        c.Status = c.Status.ToLowerInvariant().Trim();
                    if (c.DisplayName != null)
                        c.DisplayName = c.DisplayName.ToLowerInvariant().Trim();
                });
                string lowerCaseStatus = request.Status?.ToLowerInvariant().Trim();

                if (customStatuses.Any(c => c.Status == lowerCaseStatus) ||(customStatuses.Any(c => c.DisplayName == lowerCaseStatus)))
                {
                    return new Response<bool>(true);
                }
                return new Response<bool>(false);
            }
        }
    }
}


