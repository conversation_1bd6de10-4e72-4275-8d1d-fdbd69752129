﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Reports.Web;
using Lrb.Application.Reports;
using Lrb.Application.Utils;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Lrb.Domain.Entities.MasterData;
using Lrb.Application.DailyStatusUpdates.Dtos;

namespace Lrb.Application.Reports.Web.Requests
{
    public class CreateExcelForSubStatusnewReportRequest: IRequest<Response<string>>
    {
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public DateType? DateType { get; set; }
    public bool IsWithTeam { get; set; }
    public List<Guid>? UserIds { get; set; }
    public string? SearchText { get; set; }
    public List<LeadSource>? Sources { get; set; }
    public List<string>? Projects { get; set; }
    public List<string>? SubSources { get; set; }
    public UserStatus? UserStatus { get; set; }
    public ReportPermission? ExportPermission { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; } = int.MaxValue;
    public List<string>? Localites { get; set; }
    public List<string>? States { get; set; }
    public List<string>? Cities { get; set; }
        public string? TimeZoneId { get; set; }
        public TimeSpan BaseUTcOffset { get; set; }
    }
public class CreateExcelForSubStatusnewReportRequestHandler : IRequestHandler<CreateExcelForSubStatusnewReportRequest, Response<string>>
{
    private readonly IDapperRepository _dapperRepository;
    private readonly ICurrentUser _currentUser;
    private readonly IBlobStorageService _blobStorageService;
    private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customMastereadStatus;

        public CreateExcelForSubStatusnewReportRequestHandler(
        IDapperRepository dapperRepository,
        ICurrentUser currentUser,
        IBlobStorageService blobStorageService, IRepositoryWithEvents<CustomMasterLeadStatus> customMastereadStatus)
        {
        _dapperRepository = dapperRepository;
        _currentUser = currentUser;
        _blobStorageService = blobStorageService;
         _customMastereadStatus = customMastereadStatus;

        }
        public async Task<Response<string>> Handle(CreateExcelForSubStatusnewReportRequest request, CancellationToken cancellationToken)
    {
        var tenantId = _currentUser.GetTenant();
        var userId = _currentUser.GetUserId();
        List<Guid> teamUserIds = new();
        List<Guid> permittedUserIds = new();
        var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
        if (isAdmin)
        {
            permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
        }
        else if (request.ExportPermission != null)
        {
            switch (request.ExportPermission)
            {
                case ReportPermission.All:
                    permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                    break;
                case ReportPermission.Reportees:
                    permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                    break;
            }
        }
        if (request?.UserIds?.Any() ?? false)
        {
            if (request?.IsWithTeam ?? false)
            {
                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
            }
            else
            {
                teamUserIds = request?.UserIds ?? new List<Guid>();
            }
        }
        else
        {
            if (!isAdmin)
            {
                //request.UserIds = new List<Guid>() { userId };
                //teamUserIds = (await _dapperRepository.GetSubordinateIdsAsync(request.UserIds, tenantId ?? string.Empty)).ToList();

                teamUserIds = permittedUserIds;
            }
        }
        if (teamUserIds.Any())
        {
            teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
        }
        else
        {
            teamUserIds = permittedUserIds;
        }
        request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
        request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
        var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<LeadsReportDto>("LeadratBlack", "GetLeadSubStatusReportByUser", new
        {
            fromdate = request.FromDate,
            todate = request.ToDate,
            datetype = request.DateType,
            tenantid = tenantId,
            userids = teamUserIds,
            searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
            sources = request?.Sources?.ConvertAll(i => (int)i),
            projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
            subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
            userstatus = (request?.UserStatus ?? 0),
            pagesize = request.PageSize,
            pagenumber = request.PageNumber,
        })).ToList();
            List<SubStatusReportDto> subStatusDtos = new List<SubStatusReportDto>();
            res.ForEach(i => subStatusDtos.Add(JsonConvert.DeserializeObject<SubStatusReportDto>(i.Report ?? string.Empty) ?? new()));
            var groupedResult = subStatusDtos.GroupBy(i => i.User ?? new Web.User()).ToDictionary(i => i.Key, j => j.SelectMany(i => i.Status ?? new()).ToList());
            var customStatuses = await _customMastereadStatus.ListAsync();
            List<ModifiedSubStatusReportDto> modifiedSubStatusReportDtos = new();
            ModifiedSubStatusReportDto reportDto = new();




            foreach (var group in groupedResult)
            {

                reportDto.UserId = group.Key.UserId;
                reportDto.FirstName = group.Key.FirstName;
                reportDto.LastName = group.Key.LastName;

                var baseStatusWithSubStatusCount = await GetGroupedStatusAsync(group, customStatuses);

                reportDto.BaseStatusWithSubStatusCount = baseStatusWithSubStatusCount ?? new();
                reportDto.BaseStatusWithSubStatusCount.Add("All", group.Value?.Sum(i => i.Count) ?? 0);
                reportDto.BaseStatusWithSubStatusCount.Add("Active", group.Value?.Where(i => i.BaseStatus != "dropped" && i.BaseStatus != "not_interested" && i.BaseStatus != "booked" && i.BaseStatus != "booking_cancel").Sum(i => i.Count) ?? 0);
                reportDto.BaseStatusWithSubStatusCount.Add("overdue", group.Value?.Sum(i => i.OverdueCount) ?? 0);
                modifiedSubStatusReportDtos.Add(reportDto);
    

            }
            var totalCount = (await _dapperRepository.QueryStoredProcedureCountFromReadReplicaAsync("LeadratBlack", "GetLeadSubStatusReportCountByUser", new
            {
                fromdate = request.FromDate,
                todate = request.ToDate,
                datetype = request.DateType,
                tenantid = tenantId,
                userids = teamUserIds,
                searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                sources = request?.Sources?.ConvertAll(i => (int)i),
                projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                userstatus = (request?.UserStatus ?? 0),
            }));
                var fileBytes = ExcelHelper.CreateExcelFromList(new List<ModifiedSubStatusReportDto> { reportDto }, new List<string>(), new List<string>() { "UserId" }, request.TimeZoneId, request.BaseUTcOffset).ToArray();

           // var fileBytes = ExcelHelper.CreateExcelFromList(modifiedSubStatusReportDtos, new List<string>(), new List<string>() { "UserId" }).ToArray();
        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", $"Reports/{tenantId ?? "Default"}", $"Report-{DateTime.Now}.xlsx", fileBytes);
        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
        return new(presignedUrl);
    }

        private async Task<Dictionary<string, object>?> GetGroupedStatusAsync(KeyValuePair< Lrb.Application.Reports.Web.User, List<StatusDto>> group, List<CustomMasterLeadStatus> customStatuses)
        {
            var groupedValues = group.Value?.GroupBy(i => i.BaseStatus)?.ToDictionary(i => i.Key, j => j.ToList());
            Dictionary<string, object> baseStatusWithSubStatusCount = new();
            if (groupedValues == null)
            {
                return null;
            }
            foreach (var baseStatus in groupedValues)
            {
                Dictionary<string, int> subStatus = new();
                foreach (var status in baseStatus.Value)
                {
                    var customStatus = customStatuses.FirstOrDefault(i => i.Id == status.Id);
                    if (customStatus?.DisplayName != null)
                    {
                        string subStatusDisplayName = char.ToLower(customStatus.DisplayName[0]) + customStatus?.DisplayName?[1..];
                        subStatus.Add(subStatusDisplayName.Replace(" ", ""), status.Count);
                    }
                }
                if (baseStatus.Key != null)
                {
                    string baseKey = baseStatus.Key.Replace(" ", "").Replace("_", "").ToLower();
                    if (baseKey == subStatus.FirstOrDefault().Key)
                    {
                        baseStatusWithSubStatusCount.Add(baseStatus.Key, subStatus.FirstOrDefault().Value);
                    }
                    else
                    {
                        baseStatusWithSubStatusCount.Add(baseStatus.Key, subStatus);
                    }
                }
            }
            return baseStatusWithSubStatusCount;
        }
    }

}

    
