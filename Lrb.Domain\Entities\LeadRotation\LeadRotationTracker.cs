﻿using System.ComponentModel.DataAnnotations.Schema;

namespace Lrb.Domain.Entities
{
    public class LeadRotationTracker : AuditableEntity, IAggregateRoot
    {
        public Guid? LeadId { get; set; } 
        public string? JobId { get; set; }
        public UploadStatus Status { get; set; }

        [Column(TypeName = "jsonb")]
        public IDictionary<DateTime, Guid>? AssignedUsers { get; set; }
        public string? Message { get; set; }
        public string? Error { get; set; }
        public int NoOfRotation { get; set; }
        public int RotationCount { get; set; }
        public string? ClassType { get; set; }
        public int ReShuffleCount { get; set; }

    }
}
