﻿using Lrb.Application.Identity.Users;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.UserDetails.Web.Request
{
    public class GetAllAdminUsersRequest : IRequest<PagedResponse<ReportUserDto, int>>
    {

    }
    public class GetAllAdminUsersRequestHandler : IRequestHandler<GetAllAdminUsersRequest, PagedResponse<ReportUserDto, int>>
    {
        private readonly IUserService _userService;
        private readonly ICurrentUser _currentUser;
        private readonly IDapperRepository _dapperRepository;
        public GetAllAdminUsersRequestHandler(IUserService userService, ICurrentUser currentUser, IDapperRepository dapperRepository)
        {
            _userService = userService;
            _currentUser = currentUser;
            _dapperRepository = dapperRepository;
        }
        public async Task<PagedResponse<ReportUserDto, int>> Handle(GetAllAdminUsersRequest request, CancellationToken cancellationToken)
        {
            var tenantId = _currentUser.GetTenant();
            var adminUserIds = (await _dapperRepository.GetAllAdminIdsAsync(tenantId ?? string.Empty)).ToList();
            var userIds = adminUserIds.Select(i => i.ToString()).ToList();
            if (userIds?.Any() ?? false)
            {
                var users = await _userService.GetListOfUsersByIdsAsync(userIds, cancellationToken);
                List<ReportUserDto> viewUserDtos = users.Adapt<List<ReportUserDto>>();
                return new PagedResponse<ReportUserDto, int>((viewUserDtos.OrderBy(i => (i.FirstName + i.LastName)).ToList()), users.Count());
            }
            return new PagedResponse<ReportUserDto, int>();
        }
    }
}
