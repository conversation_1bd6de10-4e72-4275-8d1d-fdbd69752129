﻿using Lrb.Application.Reports.Web.Lead.Dtos.ChannelPartnerVsSubStatus;
using Lrb.Application.Utils;
using Lrb.Domain.Entities.MasterData;
using Lrb.Shared.Extensions;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Reports.Web.Lead.Requests.Custom.ChannelPartner
{
    public class GetChannelPartnerVsSubStatusReportRequest : IRequest<PagedResponse<ChannelPartnervsSubStatusReportDto, string>>
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public DateType? DateType { get; set; }
        public bool IsWithTeam { get; set; }
        public List<Guid>? UserIds { get; set; }
        public string? SearchText { get; set; }
        public List<LeadSource>? Sources { get; set; }
        public List<string>? ChannelPartners { get; set; }
        public List<string>? SubSources { get; set; }
        public ReportPermission? ReportPermission { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = int.MaxValue;
        public List<string>? Localites { get; set; }
        public List<string>? States { get; set; }
        public List<string>? Cities { get; set; }
        public List<string>? Countries { get; set; }
    }
    public class GetChannelPartnerReportvsSubStatusRequestHandler : IRequestHandler<GetChannelPartnerVsSubStatusReportRequest, PagedResponse<ChannelPartnervsSubStatusReportDto, string>>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customMastereadStatus;
        public GetChannelPartnerReportvsSubStatusRequestHandler(IDapperRepository dapperRepository, ICurrentUser currentUser,
            IRepositoryWithEvents<CustomMasterLeadStatus> customMastereadStatus)
        {
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
            _customMastereadStatus = customMastereadStatus;
        }
        public async Task<PagedResponse<ChannelPartnervsSubStatusReportDto, string>> Handle(GetChannelPartnerVsSubStatusReportRequest request, CancellationToken cancellationToken)
        {
            var tenantId = _currentUser.GetTenant();
            var userId = _currentUser.GetUserId();
            List<Guid> teamUserIds = new();
            List<Guid> permittedUserIds = new();
            var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
            if (isAdmin)
            {
                permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
            }
            else if (request.ReportPermission != null)
            {
                switch (request.ReportPermission)
                {
                    case ReportPermission.All:
                        permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        break;
                    case ReportPermission.Reportees:
                        permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                        break;
                }
            }
            if (request?.UserIds?.Any() ?? false)
            {
                if (request?.IsWithTeam ?? false)
                {
                    teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                }
                else
                {
                    teamUserIds = request?.UserIds ?? new List<Guid>();
                }
            }
            else
            {
                if (!isAdmin)
                {
                    teamUserIds = permittedUserIds;
                }
            }
            if (teamUserIds.Any())
            {
                teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
            }
            else
            {
                teamUserIds = permittedUserIds;
            }
            request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
            request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
            var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<LeadChannelPartnerBySubStatusDto>("LeadratBlack", "GetLeadChannelPartnerReportBySubStatus", new
            {
                fromdate = request.FromDate,
                todate = request.ToDate,
                datetype = request.DateType,
                tenantid = tenantId,
                userids = teamUserIds,
                searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                sources = request?.Sources?.ConvertAll(i => (int)i),
                channelpartners = request?.ChannelPartners?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                pagesize = request.PageSize,
                pagenumber = request.PageNumber,
                localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                states = request?.States?.ConvertAll<string>(i => i.LrbNormalize()),
                countries = request?.Countries?.ConvertAll<string>(i => i.LrbNormalize())
            })).ToList();
            res.ForEach(i => i.StatusDtos = JsonConvert.DeserializeObject<List<StatusDto>>(i?.Status ?? string.Empty));
            var groupedResult = res.GroupBy(i => i?.ChannelPartnerFirmName ?? string.Empty).ToDictionary(i => i.Key, j => j.SelectMany(i => i?.StatusDtos ?? new()).ToList());
            var customStatuses = await _customMastereadStatus.ListAsync();
            List<ChannelPartnervsSubStatusReportDto> channelPartnervsSubStatusReportDto = new();
            foreach (var group in groupedResult)
            {
                ChannelPartnervsSubStatusReportDto reportDto = new();

                reportDto.ChannelPartnerFirmName = group.Key;
                var baseStatusWithSubStatusCount = await GetGroupedStatusAsync(group, customStatuses);

                reportDto.BaseStatusWithSubStatusCount = baseStatusWithSubStatusCount ?? new();
                reportDto.BaseStatusWithSubStatusCount.Add("All", group.Value?.Sum(i => i.Count) ?? 0);
                reportDto.BaseStatusWithSubStatusCount.Add("Active", group.Value?.Where(i => i.BaseStatus != "dropped" && i.BaseStatus != "not_interested" && i.BaseStatus != "booked" && i.BaseStatus != "booking_cancel" && i.BaseStatus != "invoiced").Sum(i => i.Count) ?? 0);
                reportDto.BaseStatusWithSubStatusCount.Add("Overdue", group.Value?.Sum(i => i.OverdueCount) ?? 0);
                reportDto.BaseStatusWithSubStatusCount.Add("Meeting Done", group.Value?.Sum(i => i.MeetingDoneCount) ?? 0);
                reportDto.BaseStatusWithSubStatusCount.Add("Meeting Done Unique", group.Value?.Sum(i => i.MeetingDoneUniqueCount) ?? 0);
                reportDto.BaseStatusWithSubStatusCount.Add("Meeting Not Done", group.Value?.Sum(i => i.MeetingNotDoneCount) ?? 0);
                reportDto.BaseStatusWithSubStatusCount.Add("Meeting Not Done Unique", group.Value?.Sum(i => i.MeetingNotDoneUniqueCount) ?? 0);
                reportDto.BaseStatusWithSubStatusCount.Add("Site Visit Done", group.Value?.Sum(i => i.SiteVisitDoneCount) ?? 0);
                reportDto.BaseStatusWithSubStatusCount.Add("Site Visit Done Unique", group.Value?.Sum(i => i.SiteVisitDoneUniqueCount) ?? 0);
                reportDto.BaseStatusWithSubStatusCount.Add("Site Visit Not Done", group.Value?.Sum(i => i.SiteVisitNotDoneCount) ?? 0);
                reportDto.BaseStatusWithSubStatusCount.Add("Site Visit Not Done Unique", group.Value?.Sum(i => i.SiteVisitNotDoneUniqueCount) ?? 0);
                channelPartnervsSubStatusReportDto.Add(reportDto);
            }

            return new(channelPartnervsSubStatusReportDto, 0);

        }
        private async Task<Dictionary<string, object>?> GetGroupedStatusAsync(KeyValuePair<string, List<StatusDto>> group, List<CustomMasterLeadStatus> customStatuses)
        {
            var groupedValues = group.Value?.GroupBy(i => i.BaseStatus)?.ToDictionary(i => i.Key, j => j.ToList());
            Dictionary<string, object> baseStatusWithSubStatusCount = new();
            if (groupedValues == null)
            {
                return null;
            }
            foreach (var baseStatus in groupedValues)
            {
                Dictionary<string, int> subStatus = new();
                foreach (var status in baseStatus.Value)
                {
                    var customStatus = customStatuses.FirstOrDefault(i => i.Id == status.Id);
                    if (customStatus?.DisplayName != null)
                    {
                        string subStatusDisplayName = customStatus.DisplayName.ToLower();
                        subStatus.Add(subStatusDisplayName.Replace(" ", ""), status.Count);
                    }
                }
                if (baseStatus.Key != null)
                {
                    string baseKey = baseStatus.Key.Replace(" ", "").Replace("_", "").ToLower();
                    if (baseKey == subStatus.FirstOrDefault().Key)
                    {
                        baseStatusWithSubStatusCount.Add(baseStatus.Key, subStatus.FirstOrDefault().Value);
                    }
                    else
                    {
                        baseStatusWithSubStatusCount.Add(baseStatus.Key, subStatus);
                    }
                }
            }
            return baseStatusWithSubStatusCount;
        }
    }
}
