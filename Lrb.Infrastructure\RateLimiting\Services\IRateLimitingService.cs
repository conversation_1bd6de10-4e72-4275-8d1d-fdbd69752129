using Lrb.Infrastructure.RateLimiting.Models;

namespace Lrb.Infrastructure.RateLimiting.Services;

/// <summary>
/// Service for managing rate limiting logic
/// </summary>
public interface IRateLimitingService
{
    /// <summary>
    /// Check if a request should be allowed or rate limited
    /// </summary>
    /// <param name="clientId">Client identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Rate limit result</returns>
    Task<RateLimitResult> CheckRateLimitAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Reset rate limit for a specific client
    /// </summary>
    /// <param name="clientId">Client identifier</param>
    Task ResetRateLimitAsync(string clientId);

    /// <summary>
    /// Clean up expired rate limit entries
    /// </summary>
    Task CleanupExpiredEntriesAsync();
}

/// <summary>
/// Result of a rate limit check
/// </summary>
public class RateLimitResult
{
    /// <summary>
    /// Whether the request is allowed
    /// </summary>
    public bool IsAllowed { get; set; }

    /// <summary>
    /// Whether the request was queued
    /// </summary>
    public bool IsQueued { get; set; }

    /// <summary>
    /// Current rate limit information
    /// </summary>
    public RateLimitInfo RateLimitInfo { get; set; } = new();

    /// <summary>
    /// Retry after time in seconds (for 429 responses)
    /// </summary>
    public int? RetryAfterSeconds { get; set; }

    /// <summary>
    /// Queue position if request was queued
    /// </summary>
    public int? QueuePosition { get; set; }

    /// <summary>
    /// Estimated wait time if request was queued
    /// </summary>
    public TimeSpan? EstimatedWaitTime { get; set; }

    /// <summary>
    /// Task to await if request was queued
    /// </summary>
    public Task<bool>? QueueTask { get; set; }
}

/// <summary>
/// Statistics about rate limiting
/// </summary>
public class RateLimitStatistics
{
    /// <summary>
    /// Total number of tracked clients
    /// </summary>
    public int TotalClients { get; set; }

    /// <summary>
    /// Number of clients currently rate limited
    /// </summary>
    public int RateLimitedClients { get; set; }

    /// <summary>
    /// Total number of queued requests
    /// </summary>
    public int QueuedRequests { get; set; }

    /// <summary>
    /// Average queue wait time
    /// </summary>
    public TimeSpan AverageQueueWaitTime { get; set; }

    /// <summary>
    /// Total requests processed in the last minute
    /// </summary>
    public int RequestsLastMinute { get; set; }

    /// <summary>
    /// Total requests rejected in the last minute
    /// </summary>
    public int RejectedRequestsLastMinute { get; set; }
}