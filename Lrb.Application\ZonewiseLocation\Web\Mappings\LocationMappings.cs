﻿using Lrb.Application.Automation.Dtos;
using Lrb.Application.Common.GooglePlaces;
using Lrb.Application.ZonewiseLocation.Web.Dtos;
using Lrb.Application.ZonewiseLocation.Web.Requests;

namespace Lrb.Application.ZonewiseLocation.Web.Mappings
{
    public static class LocationMappings
    {
        public static void Configure(IServiceProvider serviceProvider)
        {
            TypeAdapterConfig<Location, Address>
                .NewConfig()
                .Ignore(dest => dest.Locality)
                .Ignore(dest => dest.Location)
                .Map(dest => dest.SubLocality, src => src.Locality)
                .Map(dest => dest.City, src => src.City != null ? src.City.Name : string.Empty)
                .Map(dest => dest.State, src => src.State != null ? src.State.Name : string.Empty)
                .Map(dest => dest.Country, src => src.Country != null ? src.Country.Name : string.Empty)
                .Map(dest => dest.City, src => src.City != null ? src.City.Name : string.Empty);
            TypeAdapterConfig<AddLocationRequest, Location>
                .NewConfig()
                .Ignore(i => i.DomainEvents)
                .Ignore(i => i.City)
                .Ignore(i => i.State)
                .Ignore(i => i.Zone)
                .Ignore(i => i.Country);
            TypeAdapterConfig<UpdateLocationRequest, Location>
                .NewConfig()
                .Ignore(i => i.DomainEvents)
                .Ignore(i => i.Id)
                .Ignore(i => i.City)
                .Ignore(i => i.State)
                .Ignore(i => i.Zone)
                .Ignore(i => i.Country);
            TypeAdapterConfig<UpdateCityRequest, City>
                .NewConfig()
                .Ignore(i => i.Id);
            TypeAdapterConfig<Location, LocationLeadDto>
                .NewConfig()
                .Map(dest => dest.Location, src => src.ToString());
            TypeAdapterConfig<Location, BaseAssignedEntityDto>
                .NewConfig()
                .Map(dest => dest.Name, src => src.ToString());
            TypeAdapterConfig<AutocompleteLocationModel, LocationLeadDto>
                .NewConfig()
                .Map(dest => dest.PlaceId, src => src.PlaceId)
                .Map(dest => dest.Location, src => src.LocalityDisplayText + ", " + src.LocalitySubtitleText);
            TypeAdapterConfig<Zone, ZoneDto>
                    .NewConfig()
                    .Map(dest => dest.UserIds, src => src.UserAssignment != null ? src.UserAssignment.UserIds : null);
            TypeAdapterConfig<Location, LocationDto>
                    .NewConfig()
                    .Map(dest => dest.UserIds, src => src.UserAssignment != null ? src.UserAssignment.UserIds : null);
            TypeAdapterConfig<City, CityDto>
                    .NewConfig()
                    .Map(dest => dest.UserIds, src => src.UserAssignment != null ? src.UserAssignment.UserIds : null);
        }
    }
}
