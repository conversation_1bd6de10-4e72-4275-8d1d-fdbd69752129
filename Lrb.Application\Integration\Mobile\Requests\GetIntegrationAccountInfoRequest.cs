﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Domain.Entities;

namespace Lrb.Application.Integration.Mobile
{
    public class GetIntegrationAccountInfoRequest : IRequest<Response<string>>
    {
        public Guid Id { get; set; }
        public GetIntegrationAccountInfoRequest(Guid id)
        {
            Id = id;
        }
    }
    public class GetIntegrationAccountInfoRequestHandler : IRequestHandler<GetIntegrationAccountInfoRequest, Response<string>>
    {
        private readonly IReadRepository<IntegrationAccountInfo> _integrationRepo;
        private readonly IBlobStorageService _blobStorageService;
        public GetIntegrationAccountInfoRequestHandler(IReadRepository<IntegrationAccountInfo> integrationRepo, IBlobStorageService blobStorageService)
        {
            _integrationRepo = integrationRepo;
            _blobStorageService = blobStorageService;
        }
        public async Task<Response<string>> Handle(GetIntegrationAccountInfoRequest request, CancellationToken cancellationToken)
        {
            var account = await _integrationRepo.GetByIdAsync(request.Id);
            string fileUrl = await _blobStorageService.GetPreSignedURL(_blobStorageService?.BucketName ?? "prd-leadratblack", account.FileUrl);
            return new(fileUrl, default);
        }
    }

}
