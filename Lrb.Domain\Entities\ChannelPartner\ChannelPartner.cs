﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Lrb.Domain.Entities
{
    public class ChannelPartner : AuditableEntity, IAggregateRoot
    {
        public string? FirmName { get; set; }
        [JsonIgnore]
        public IList<Lead>? Leads { get; set; }
        [JsonIgnore]
        public IList<Prospect>? Prospects { get; set; }
        public string? ContactNo { get; set; }
        public string? CountryCode { get; set; } 
        public string? Email { get; set; }
        public Address? Address { get; set; }
        public string? RERANumber { get; set; }
        public string? CompanyName { get; set; }
        public IList<Project>? Projects { get; set; }
    }
}
