﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Domain.Entities
{
    public class AttendanceLog : UserLevelAuditableEntity, IAggregateRoot
    {
        public DateTime ClockInTime { get; set; }
        public DateTime? ClockOutTime { get; set; }
        public double ClockInLatitude { get; set; }
        public double ClockInLongitude { get; set; }
        public string? ClockInLocation { get; set; }
        public double? ClockOutLatitude { get; set; }
        public double? ClockOutLongitude { get; set; }
        public string? ClockOutLocation { get; set; }
        public bool IsClosed { get; set; }  
        public bool IsSystemGenerated { get; set; }
        public string? ClockInImageUrl { get; set; }
        public string? ClockOutImageUrl { get; set; }
        public bool? IsPunchInLocation { get; set; }
    }
}