﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.Lead.Web;
using Lrb.Application.ZonewiseLocation.Web.Specs;
using Lrb.Domain.Enums;
using Newtonsoft.Json;
using System;

namespace Lrb.Application.Integration.Web.Requests
{
    public class CreateIVRIntegrationWithParamsRequest : IRequest<Response<string>>
    {
        public string? AccountName { get; set; }
        public LeadSource Source { get; set; }
        public IVRType CallType { get; set; }
        public List<IVRAssignmentDto>? IVRAssignmentDtos { get; set; }
        public string? ServiceProviderName { get; set; } // FreeText to add any name of the service provider which will get added to the callback url
        public Dictionary<string, string>? PayloadForPushEndPoint { get; set; }
        public bool? IsPayloadInForm { get; set; }
        public IVROutboundConfigurationDto? IVROutboundConfigurationDto { get; set; }
        public bool? ShouldSetPrimary { get; set; }
        public string? PayloadContentType { get; set; } // None/json/
        public string? PayloadMethodType { get; set; }
        public bool? CanIncludeApiKeyInPayloadHeader { get; set; }
    }
    public class CreateIVRIntegrationWithParamsRequestHandler : IRequestHandler<CreateIVRIntegrationWithParamsRequest, Response<string>>
    {
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccountInfoRepositoryAsync;
        private readonly IBlobStorageService _blobStorageService;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<TempProjects> _tempProjectsRepo;
        private readonly IRepositoryWithEvents<Location> _locationRepo;
        private readonly ISender _mediator;
        private readonly IRepositoryWithEvents<Domain.Entities.Project> _projectsRepo;

        public CreateIVRIntegrationWithParamsRequestHandler(
            IRepositoryWithEvents<IntegrationAccountInfo> integrationAccountInfoRepositoryAsync, 
            IBlobStorageService blobStorageService, 
            ICurrentUser currentUser, 
            IRepositoryWithEvents<TempProjects> tempProjectsRepo, 
            IRepositoryWithEvents<Location> locationRepo,
            ISender mediator,
            IRepositoryWithEvents<Domain.Entities.Project> projectsRepo)
        {
            _integrationAccountInfoRepositoryAsync = integrationAccountInfoRepositoryAsync;
            _blobStorageService = blobStorageService;
            _currentUser = currentUser;
            _tempProjectsRepo = tempProjectsRepo;
            _locationRepo = locationRepo;
            _mediator = mediator;
            _projectsRepo = projectsRepo;
        }

        public async Task<Response<string>> Handle(CreateIVRIntegrationWithParamsRequest request, CancellationToken cancellationToken)
        {
            request.Source = LeadSource.IVR;
            var tenant = _currentUser.GetTenant();
            Guid userId = _currentUser.GetUserId();
            IntegrationAccountInfo? integrationAccount = null;
            IDictionary<string, string> data = null;
            request.ServiceProviderName = request.ServiceProviderName?.Replace(" ","").ToLower();
            integrationAccount = CreateIntegrationEntity(request, userId, request.ServiceProviderName);
            var apiKey = ApiKeyHelper.GenerateApiKey(integrationAccount.Id);
            integrationAccount.ApiKey = apiKey;
            var payloadForExcel = request.PayloadForPushEndPoint?.ToDictionary(i => i.Value, j => j.Key);
            data = new Dictionary<string, string>(IntegrationTemplateBuilder.CreateIntegrationTemplate(tenant, integrationAccount, request.CallType, serviceProvider: request.ServiceProviderName, payloadForExcel: payloadForExcel, isPayloadInForm: request.IsPayloadInForm, ivrPayloadMapping: integrationAccount.IVRPayloadMapping, canSendApiKey: request.CanIncludeApiKeyInPayloadHeader));
            data[IntegrationTemplateKeys.PayloadKey] = JsonConvert.SerializeObject(payloadForExcel, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented });
            List<IVRAssignment> ivrAssignments = new();
            var projectsToAssign = await _projectsRepo.ListAsync(new ProjectsByIdSpecV2(request.IVRAssignmentDtos?.Select(i => i.ProjectId ?? Guid.Empty).ToList() ?? new()), cancellationToken);
            var locationsToAssign = await _locationRepo.ListAsync(new LocationByIdSpec(request.IVRAssignmentDtos?.Select(i => i.LocationId ?? Guid.Empty).ToList() ?? new()), cancellationToken);
            if (request.CallType == IVRType.Outbound && (request.IVROutboundConfigurationDto?.IsVirtualNumberRequired ?? false))
            {
                request.IVRAssignmentDtos?.ForEach(i =>
                {
                    ivrAssignments.Add(new()
                    {
                        VirtualNumber = i.VirtualNumber,
                        Assignment = new()
                        {
                            Project = projectsToAssign.FirstOrDefault(j => j.Id == i.ProjectId),
                            Location = locationsToAssign.FirstOrDefault(j => j.Id == i.LocationId)
                        },
                        UserIds = i.UserIds
                    });
                });
            }
            else if (request.CallType == IVRType.Inbound)
            {
                request.IVRAssignmentDtos?.ForEach(i =>
                {
                    ivrAssignments.Add(new()
                    {
                        VirtualNumber = i.VirtualNumber,
                        AgencyName = i.AgencyName,
                        Assignment = new()
                        {
                            Project = projectsToAssign.FirstOrDefault(j => j.Id == i.ProjectId),
                            Location = locationsToAssign.FirstOrDefault(j => j.Id == i.LocationId)
                        },
                    });
                });
            }
            integrationAccount.IVRAssignments = ivrAssignments;
            await _integrationAccountInfoRepositoryAsync.AddAsync(integrationAccount);
            string key = string.Empty;
            byte[] bytes = IntegrationExcelHelper.CreateExcelData(data).ToArray();
            string fileName = $"{tenant}-{integrationAccount.Id}-{DateTime.Now.ToString("yyyy_MM_dd-HH_mm_ss")}.xlsx";
            string folder = "Integration";
            key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", folder, fileName, bytes);
            string fileUrl = await _blobStorageService.GetPreSignedURL(_blobStorageService?.BucketName ?? "prd-leadratblack", key);
            integrationAccount.FileUrl = key;
            var existingIVRAccounts = await _integrationAccountInfoRepositoryAsync.ListAsync(new IntegrationAccountInfoBySourceSpec(LeadSource.IVR), cancellationToken);
            if ((request.ShouldSetPrimary ?? false) || (!existingIVRAccounts.Any()))
            {
                await _mediator.Send(new MakeIntegrationAccountPrimaryRequest(integrationAccount.Id), cancellationToken);
                if (integrationAccount.IVROutboundConfiguration != null)
                {
                    integrationAccount.IVROutboundConfiguration.IsPrimary = true;
                }
            }
            await _integrationAccountInfoRepositoryAsync.UpdateAsync(integrationAccount);
            
            return new(fileUrl, default);
        }
        private IntegrationAccountInfo CreateIntegrationEntity(CreateIVRIntegrationWithParamsRequest command, Guid userId, string? serviceProvider)
        {
            return new IntegrationAccountInfo()
            {
                Id = Guid.NewGuid(),
                AccountName = command.AccountName,
                LeadSource = command.Source,
                LicenseId = Guid.NewGuid(),
                JsonTemplate = JsonConvert.SerializeObject(command.PayloadForPushEndPoint, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                CreatedBy = userId,
                IVRCallType = command.CallType,
                IVRPayloadMapping = new() 
                { 
                    PushEndPointMappings = command.PayloadForPushEndPoint, 
                    IsPayloadInForm = command.IsPayloadInForm, 
                    ContentType = command.PayloadContentType,
                    MethodType = command.PayloadMethodType,
                },
                IVROutboundConfiguration = command.IVROutboundConfigurationDto?.Adapt<IVROutboundConfiguration>(),
                IVRServiceProvider = serviceProvider?.Trim(),
            };
        }
    }
}
