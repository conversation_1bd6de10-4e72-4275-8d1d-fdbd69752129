﻿using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.Lead.Mobile.Dtos.v4;
using Lrb.Application.Lead.Mobile.v2;
using Lrb.Application.Property.Mobile.Dtos;
using Lrb.Application.Utils;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;
using System.ComponentModel;
using System.Diagnostics;

namespace Lrb.Application.Lead.Mobile
{
    public class GetAllLeadsRequest : PaginationFilter, IRequest<Response<V4GetAllLeadsWrapperDto>>
    {
        public List<LeadSource>? Source { get; set; } = new();
        public List<EnquiryType>? EnquiredFor { get; set; }
        public Guid? AssignTo { get; set; }
        public DateType? DateType { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? SearchByNameOrNumber { get; set; }
        public long? MinBudget { get; set; }
        public long? MaxBudget { get; set; }
        public List<LeadFilterTypeMobile>? FilterTypes { get; set; }
        public BaseLeadVisibility LeadVisibility { get; set; }
        public List<Guid>? AssignToIds { get; set; }
        public List<Budget>? Budget { get; set; }
        public List<string>? Projects { get; set; }
        public List<string>? Properties { get; set; }
        public List<double>? NoOfBHKs { get; set; }
        public List<BHKType>? BHKTypes { get; set; }
        public List<Guid>? PropertyType { get; set; }
        public List<Guid>? PropertySubType { get; set; }
        public List<Guid>? StatusIds { get; set; }
        public List<Dtos.BudgetFilter>? BudgetFilters { get; set; }
        public List<string>? Locations { get; set; }
        public List<MeetingOrVisitCompletionStatus>? MeetingOrVisitStatuses { get; set; }
        public DateTime? ToDateForMeetingOrVisit { get; set; }
        public DateTime? FromDateForMeetingOrVisit { get; set; }
        public List<Guid>? AppointmentDoneByUserIds { get; set; }
        public bool? IsWithTeam { get; set; }
        public List<string>? SubSources { get; set; }
        public List<Guid>? IntegrationAccountIds { get; set; }
        public List<string>? AgencyNames { get; set; }
        public List<Guid>? LeadIds { get; set; }
        public List<string>? SerialNumbers { get; set; }
        public string? ReferralName { get; set; }
        public string? ReferralContactNo { get; set; }
        public double? CarpetArea { get; set; }
        public double? MinCarpetArea { get; set; }
        public double? MaxCarpetArea { get; set; }
        public Guid CarpetAreaUnitId { get; set; }
        public float? ConversionFactor { get; set; }
        public List<string>? Designations { get; set; }
        public string? Designation { get; set; }
        public bool? IsPicked { get; set; }
        public List<Guid>? SecondaryUsers { get; set; }
        public bool? IsDualOwnershipEnabled { get; set; }
        public List<Guid>? BookedByIds { get; set; }
        public List<v2.Date>? Dates { get; set; }
        public string? DatesJsonFormattedString { get; set; }
        public List<string>? CustomFlags { get; set; }
        public DateTime? BookedDate { get; set; }
        public Guid? LeadBrokerageInfoId { get; set; }
        public List<Guid>? SecondaryIds { get; set; }
        public Guid? TeamHead { get; set; }
        public int? UpperAgreementLimit { get; set; }
        public int? LowerAgreementLimit { get; set; }
        public TokenType? PaymentMode { get; set; }
        public int? UpperDiscountLimit { get; set; }
        public int? LowerDiscountLimit { get; set; }
        public DiscountType? DiscountMode { get; set; }
        public bool? ShouldShowBookedDetails { get; set; }
        public bool? ShouldShowBrokerageInfo { get; set; }
        public string? Currency { get; set; }
        public string? BookedUnderName { get; set; }
        public double? TotalBrokerage { get; set; }
        public double? SoldPrice { get; set; }
        public double? BrokerageCharges { get; set; }
        public double? NetBrokerageAmount { get; set; }
        public double? GST { get; set; }
        public string? ReferralNumber { get; set; }
        public Guid? ReferredBy { get; set; }
        public double? Commission { get; set; }
        public string? CommissionUnit { get; set; }
        public double? EarnedBrokerage { get; set; }
        public BrokerageType? BrokerageType { get; set; }
        public string? GSTUnit { get; set; }
        public string? BrokerageUnit { get; set; }
        public string? DiscountUnit { get; set; }
        public double? UpperRemainingAmountLimit { get; set; }
        public double? LowerRemainingAmountLimit { get; set; }
        public PaymentType? PaymentType { get; set; }
        public double? CarParkingCharges { get; set; }
        public double? RemainingAmount { get; set; }
        public double? AdditionalCharges { get; set; }
        public bool? IsUntouched { get; set; }
        public string? Longitude { get; set; }
        public string? Latitude { get; set; }
        public double? RadiusInKm { get; set; }
        public List<Guid>? SubStatuses { get; set; }
        public List<Guid>? HistoryAssignedToIds { get; set; }
        public List<Guid>? AssignFromIds { get; set; }
        public List<Guid>? SecondaryFromIds { get; set; }
        public List<Guid>? DoneBy { get; set; }
        public bool? IsWithHistory { get; set; }
        public bool? CanAccessAllLeads { get; set; }
        public List<int>? Beds { get; set; }
        public List<int>? Baths { get; set; }
        public List<string>? Floors { get; set; }
        public List<OfferType>? OfferTypes { get; set; }
        public List<FurnishStatus>? Furnished { get; set; }
        public List<string>? Communities { get; set; }
        public List<string>? SubCommunities { get; set; }
        public List<string>? TowerNames { get; set; }
        public List<string>? Countries { get; set; }
        public List<string>? PostalCodes { get; set; }
        public List<string>? Cities { get; set; }
        public List<string>? States { get; set; }
        public double? BuiltUpArea { get; set; }
        public double? MinBuiltUpArea { get; set; }
        public double? MaxBuiltUpArea { get; set; }
        public Guid BuiltUpAreaUnitId { get; set; } = Guid.Empty;
        public double? SaleableArea { get; set; }
        public double? MinSaleableArea { get; set; }
        public double? MaxSaleableArea { get; set; }
        public Guid SaleableAreaUnitId { get; set; }= Guid.Empty;
        public string? ReferralEmail { get; set; }
        public bool? DataConverted { get; set; }
        public List<Guid>? QualifiedByIds { get; set; }
        public string? ConfidentialNotes { get; set; }
        public double? NetArea { get; set; }
        public double? MinNetArea { get; set; }
        public double? MaxNetArea { get; set; }
        public Guid NetAreaUnitId { get; set; } = Guid.Empty;
        public double? PropertyArea { get; set; }
        public double? MinPropertyArea { get; set; }
        public double? MaxPropertyArea { get; set; }
        public Guid PropertyAreaUnitId { get; set; } = Guid.Empty;
        public string? UnitName { get; set; }
        public List<string>? ClusterName { get; set; }
        public List<string>? Nationality { get; set; }
        public List<string>? UnitNames { get; set; }


        public List<Guid>? CreatedByIds { get; set; }
        public List<Guid>? LastModifiedByIds { get; set; }
        public List<Guid>? ArchivedByIds { get; set; }
        public List<Guid>? RestoredByIds { get; set; }
        public List<Guid>? ClosingManagers { get; set; }
        public List<Guid>? SourcingManagers { get; set; }
        public string? AdditionalPropertiesKey { get; set; }
        public string? AdditionalPropertiesValue { get; set; }
        public List<string>? ChannelPartnerNames { get; set; }
        public List<Profession>? Profession { get; set; }
        public string? UploadTypeName { get; set; }
        public List<string>? CampaignNames { get; set; }
        public List<Purpose>? Purposes { get; set; }
        public bool? ShowOnlyParentLeads { get; set; }
        public List<LeadType>? LeadType { get; set; }
        public int? ChildLeadsCount { get; set; }
        public long? FromMinBudget { get; set; }
        public long? ToMinBudget { get; set; }
        public long? FromMaxBudget { get; set; }
        public long? ToMaxBudget { get; set; }
        public List<Guid>? OriginalOwnerIds { get; set; }
        public PossesionType? PossesionType { get; set; }
        public DateTime? FromPossesionDate { get; set; }
        public DateTime? ToPossesionDate { get; set; }
        public List<string>? LandLine { get; set; }


        public List<string>? PropertyToSearch { get; set; }
        public List<string>? CountryCode { get; set; }
        public List<string>? AltCountryCode { get; set; }

        public List<Gender>? GenderTypes { get; set; }
        public List<MaritalStatusType>? MaritalStatuses { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public List<Guid>? UserIds { get; set; }
        public List<CallStatus>? CallStatuses { get; set; }
        public List<CallDirection>? CallDirections { get; set; }
        public OwnerSelectionType? OwnerSelection { get; set; }
        public string? TimeZoneId { get; set; }
        public TimeSpan? BaseUTcOffset { get; set; }
        public DateTime? AnniversaryDate { get; set; }


    }
    public class GetAllLeadsRequestHandler : IRequestHandler<GetAllLeadsRequest, Response<V4GetAllLeadsWrapperDto>>
    {
        private readonly ICurrentUser _currentUser;
        private readonly ILeadRepository _efLeadRepository;
        private readonly IDapperRepository _dapperRepository;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customMasterLeadStatus;

        public GetAllLeadsRequestHandler(
            ICurrentUser currentUser,
            ILeadRepository efLeadRepository,
            IDapperRepository dapperRepository,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<CustomMasterLeadStatus> customMasterLeadStatus)
        {
            _currentUser = currentUser;
            _efLeadRepository = efLeadRepository;
            _dapperRepository = dapperRepository;
            _leadRepositoryAsync = leadRepositoryAsync;
            _customMasterLeadStatus = customMasterLeadStatus;
        }
        public async Task<Response<V4GetAllLeadsWrapperDto>> Handle(GetAllLeadsRequest request, CancellationToken cancellationToken)
        {
            var stopwatch = Stopwatch.StartNew();
            var userId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var getAllLeadsWrapperDto = new V4GetAllLeadsWrapperDto();
            var subIds = new List<Guid>();
            (List<Guid>, List<Guid>) leadHistoryIds = new();
            (List<AppointmentType>, List<bool>) appointments = new();
            var isAdmin = await _dapperRepository.IsAdminV2Async(userId, tenantId ?? string.Empty);
            var customMasterStatus = new List<CustomMasterLeadStatus>();
            List<LeadIdWithLatLongDto> leadIdsWithLatLong = new();
            List<Guid> leadIdsWithinRange = new();
            try
            {
                var tasks = new Task[]
                {
                    Task.Run(async () => request.IsDualOwnershipEnabled = await _dapperRepository.V2GetDualOwnershipDetails(tenantId ?? string.Empty)),
                    Task.Run(async () => subIds = await GetSubordinateIdsAsync(request.Adapt<GetAllLeadsRequest>(), userId, tenantId ?? string.Empty,isAdmin)),
                    Task.Run(async () => customMasterStatus = await _customMasterLeadStatus.ListAsync()),
                    Task.Run(async () => appointments = await GetAppointmentTypes(request.Adapt<GetAllLeadsRequest>())),
                    Task.Run(async () =>
                    {
                        if (request.RadiusInKm != null)
                        {
                            leadIdsWithLatLong =  (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<LeadIdWithLatLongDto>("LeadratBlack", "get_leads_with_lat_long", new { user_ids = subIds, tenant_id = tenantId }, 300)).ToList();
                        }
                    })
                };
                await Task.WhenAll(tasks);

                if (!string.IsNullOrEmpty(request.Latitude) && !string.IsNullOrEmpty(request.Longitude))
                {
                    leadIdsWithinRange = leadIdsWithLatLong.Where(i =>
                                         (i != null && i.Latitude != null && i.Longitude != null) &&
                                         (request.Latitude != null && request.Longitude != null) &&
                    GeoLocationHelper.IsLocationWithinRange(double.TryParse(request.Latitude, out var requestLat) ? requestLat : 0.0,
                                                                                double.TryParse(request.Longitude, out var requestLong) ? requestLong : 0.0,
                                                                                 double.TryParse(i.Latitude, out var leadLat) ? leadLat : 0.0,
                                                                                 double.TryParse(i.Longitude, out var leadLong) ? leadLong : 0.0, request.RadiusInKm * 1000 ?? 0.0)).Select(i => i.Id).ToList();

                    request.LeadIds = leadIdsWithinRange;
                }
                var totalCountTasks = new Task[]
                 {
                    Task.Run(async () => getAllLeadsWrapperDto.TotalLeadsCount = await _efLeadRepository.GetAllCategoryLeadsCountForMobileAsync(request.Adapt<GetAllLeadsRequest>(), userId, subIds, LeadFilterTypeMobile.All, new List<Guid>(), leadHistoryIds.Item1, leadHistoryIds.Item2)),
                 };
                var filterTypes = request.FilterTypes?.Any() ?? false ? request.FilterTypes : Enum.GetValues(typeof(LeadFilterTypeMobile)).Cast<LeadFilterTypeMobile>();
                if (filterTypes?.Any() ?? false)
                {
                    await GetLeadCategoryDtosAsync(getAllLeadsWrapperDto, request, userId, subIds, isAdmin, customMasterStatus, leadHistoryIds, appointments, filterTypes);
                }
                stopwatch.Stop();
                Console.WriteLine($"Query execution and data retrieval took: {stopwatch.ElapsedMilliseconds} ms User: {userId.ToString() ?? string.Empty} Tenant: {tenantId ?? string.Empty}");
                await Task.WhenAll(totalCountTasks);
                return new Response<V4GetAllLeadsWrapperDto>(getAllLeadsWrapperDto);
            }
            catch (Exception ex)
            {
                await LogExceptionAsync(ex);
                throw;
            }
        }
        private async Task GetLeadCategoryDtosAsync(V4GetAllLeadsWrapperDto getAllLeadsWrapperDto, GetAllLeadsRequest request, Guid userId, List<Guid> subIds, bool isAdmin, List<CustomMasterLeadStatus> customMasterStatus, (List<Guid>, List<Guid>) leadHistoryIds, (List<AppointmentType>, List<bool>) appointments, IEnumerable<LeadFilterTypeMobile> leadFilterTypes)
        {
            await Task.WhenAll(leadFilterTypes.Select(async filterType =>
            {
                var leads = await _efLeadRepository.GetAllCategoryLeadsForMobileAsync(request.Adapt<GetAllLeadsRequest>(), userId, subIds, filterType, new List<Guid>(), leadHistoryIds.Item1, leadHistoryIds.Item2, customMasterStatus);
                var count = await _efLeadRepository.GetAllCategoryLeadsCountForMobileAsync(request.Adapt<GetAllLeadsRequest>(), userId, subIds, filterType, new List<Guid>(), leadHistoryIds.Item1, leadHistoryIds.Item2, customMasterStatus);
                if (leads?.Any() ?? false)
                {
                    leads.ToList().ForEach(lead => FilterAppointments(request, isAdmin, userId, lead, appointments.Item1 ?? new(), appointments.Item2 ?? new(), subIds));
                    var result = new V4LeadCategoryDto
                    {
                        LeadFilter = filterType,
                        Leads = leads.Adapt<List<V4GetAllLeadDto>>(),
                        TotalCount = count
                    };
                    getAllLeadsWrapperDto.Leads.TryAdd(filterType, result);
                }
            }));
        }
        private async Task LogExceptionAsync(Exception ex)
        {
            var error = new LrbError()
            {
                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                ErrorSource = ex?.Source,
                StackTrace = ex?.StackTrace,
                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                ErrorModule = "GetAllLeadsRequestHandler -> Handle()"
            };
            await _leadRepositoryAsync.AddErrorAsync(error);
        }
        private async Task<List<Guid>> GetSubordinateIdsAsync(GetAllLeadsRequest request, Guid userId, string tenantId, bool isAdmin)
        {
            var assignToIds = request?.AssignToIds ?? new List<Guid>();
            return assignToIds.Any()
                ? (request?.IsWithTeam ?? false)
                    ? (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesV2Async(assignToIds, tenantId ?? string.Empty)).ToList()
                    : assignToIds
                : (await _dapperRepository.GetSubordinateIdsV2Async(userId, tenantId ?? string.Empty, request.CanAccessAllLeads, isAdmin))?.ToList() ?? new List<Guid>();
        }
        public async Task<(List<AppointmentType>, List<bool>)> GetAppointmentTypes(GetAllLeadsRequest request)
        {
            List<AppointmentType> appTypes = new();
            List<bool> appDoneStatuses = new();
            if (request.MeetingOrVisitStatuses == null)
            {
                return (appTypes, appDoneStatuses);
            }
            request.MeetingOrVisitStatuses?.ForEach(appType =>
            {
                switch (appType)
                {
                    case MeetingOrVisitCompletionStatus.IsMeetingDone:
                        appTypes.Add(AppointmentType.Meeting);
                        appDoneStatuses.Add(true);
                        break;
                    case MeetingOrVisitCompletionStatus.IsMeetingNotDone:
                        appTypes.Add(AppointmentType.Meeting);
                        appDoneStatuses.Add(false);
                        break;
                    case MeetingOrVisitCompletionStatus.IsSiteVisitDone:
                        appTypes.Add(AppointmentType.SiteVisit);
                        appDoneStatuses.Add(true);
                        break;
                    case MeetingOrVisitCompletionStatus.IsSiteVisitNotDone:
                        appTypes.Add(AppointmentType.SiteVisit);
                        appDoneStatuses.Add(false);
                        break;
                }
            });
            appTypes = appTypes.Distinct().ToList();
            appDoneStatuses = appDoneStatuses.Distinct().ToList();
            return (appTypes, appDoneStatuses);
        }
        private void FilterAppointments(GetAllLeadsRequest request, bool isAdmin, Guid userId, Domain.Entities.Lead lead, List<AppointmentType> appTypes, List<bool> appDoneStatuses, List<Guid> subIds)
        {
            if (lead?.Appointments?.Any() ?? false)
            {
                if (request.AppointmentDoneByUserIds?.Any() ?? false)
                {
                    var uniqueAppointments = lead.Appointments.Where(i => i.UniqueKey != null && i.UniqueKey != Guid.Empty).DistinctBy(i => i.UniqueKey).ToList();
                    uniqueAppointments.AddRange(lead.Appointments.Where(i => i.UniqueKey == null || i.UniqueKey == Guid.Empty).ToList());
                    lead.Appointments = uniqueAppointments.Where(i => request.AppointmentDoneByUserIds.Contains(i.CreatedBy) && appTypes.Contains(i.Type) && appDoneStatuses.Contains(i.IsDone)).ToList();
                }
                else if (subIds != null && !(isAdmin))
                {
                    if ((lead?.Appointments?.Any() ?? false) && lead.Appointments.Any(i => subIds.Contains(i.UserId)))
                    {
                        var appointmentsWithoutUniqueKey = lead.Appointments?.Where(i => i.UniqueKey == null || i.UniqueKey == default).ToList() ?? new();

                        var appointmentsWithUniqueKey = lead.Appointments?.Where(i => i.UniqueKey != null && i.UniqueKey != default && subIds.Contains(i.UserId))?.DistinctBy(i => i.UniqueKey)?.OrderBy(i => i.LastModifiedOn)?.ToList() ?? new();

                        appointmentsWithoutUniqueKey.AddRange(appointmentsWithUniqueKey);
                        lead.Appointments = appointmentsWithoutUniqueKey;
                    }
                    else
                    {
                        lead.Appointments = null;
                    }
                }
                else
                {
                    var appointmentsWithoutUniqueKey = lead.Appointments?.Where(i => i.UniqueKey == null || i.UniqueKey == default)?.ToList() ?? new();
                    var appointmentsWithUniqueKey = lead.Appointments?.Where(i => i.UniqueKey != null && i.UniqueKey != default)?.DistinctBy(i => i.UniqueKey).ToList() ?? new();
                    appointmentsWithoutUniqueKey.AddRange(appointmentsWithUniqueKey);
                    lead.Appointments = appointmentsWithoutUniqueKey;
                }
            }
        }
    }
    public enum DateType
    {
        None = 0,
        ReceivedDate,
        ScheduledDate,
        ModifiedDate,
        DeletedDate,
        PickedDate,
        BookedDate,
        AssignedDate,
        PossessionDate,
        ReEnquiredDate
    }
    public enum LeadFilterTypeMobile
    {
        [Description("New")]
        New = 0,
        [Description("Pending")]
        Pending,
        [Description("ScheduledMeeting")]
        ScheduledMeeting,
        [Description("SiteVisitScheduled")]
        SiteVisitScheduled,
        [Description("NotInterested")]
        NotInterested,
        [Description("CallBack")]
        CallBack,
        [Description("UnassignLeads")]
        UnassignLeads,
        [Description("Booked")]
        Booked,
        [Description("Dropped")]
        Dropped,
        [Description("HotLeads")]
        HotLeads,
        [Description("Escalated")]
        Escalated,
        [Description("AboutToConvert")]
        AboutToConvert,
        [Description("ScheduleToday")]
        ScheduleToday,
        [Description("Overdue")]
        Overdue,
        [Description("All")]
        All,
        [Description("WarmLeads")]
        WarmLeads,
        [Description("ColdLead")]
        ColdLead,
        [Description("Highlighted")]
        Highlighted,
        [Description("ScheduledTomorrow")]
        ScheduledTomorrow,
        [Description("UpcomingSchedules")]
        UpcomingSchedules,
        [Description("Active")]
        Active,
        [Description("AllWithNID")]
        AllWithNID,
        [Description("BookingCancel")]
        BookingCancel,
        [Description("Untouched")]
        Untouched,
        [Description("ExpressionOfInterest")]
        ExpressionOfInterest,
        [Description("SiteVisitDone")]
        SiteVisitDone,
        [Description("MeetingDone")]
        MeetingDone,
        [Description("Invoiced")]
        Invoiced,
    }
    public enum BaseLeadVisibility
    {
        [Description("SelfWithReportee")]
        SelfWithReportee = 0,
        [Description("Self")]
        Self,
        [Description("Reportee")]
        Reportee,
        [Description("UnassignLead")]
        UnassignLead,
        [Description("DeletedLeads")]
        DeletedLeads,
        [Description("ReEnquiredLeads")]
        ReEnquired,
    }
}
