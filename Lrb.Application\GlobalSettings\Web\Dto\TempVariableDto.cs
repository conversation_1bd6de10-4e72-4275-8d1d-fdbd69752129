﻿using System.ComponentModel.DataAnnotations.Schema;

namespace Lrb.Application.GlobalSettings.Web
{
    public class TempVariableDto : IDto
    {
        public List<string>? LeadVariables { get; set; }
        public List<string>? LeadEnquiryVariables { get; set; }
        public List<string>? UserVariables { get; set; }
        public List<string>? IVRVariables { get; set; }
        public List<TempVarWithDisplayNameDto>? IVRVariablesWithDisplayName { get; set; }
        public List<string>? WAVariables { get; set; }
        public List<string>? WebhookConstantVariables { get; set; }
        public List<TempVarWithDisplayNameDto>? WebhookVariablesWithDisplayName { get; set; }
        public List<string>? InvoiceConstantVariables { get; set; }
        public List<TempVarWithDisplayNameDto>? invoiceVariablesWithDisplayName { get; set; }
    }
    public class TempVarWithDisplayNameDto : IDto
    {
        public string Value { get; set; }
        public string? DisplayName { get; set; }
    }
}
