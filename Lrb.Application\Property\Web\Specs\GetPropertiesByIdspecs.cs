﻿using DocumentFormat.OpenXml.Office2010.Excel;
using Lrb.Application.Property.Web.Dtos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Property.Web.Specs
{
    public class GetPropertiesByIdspecs : Specification<Lrb.Domain.Entities.Property>
    {
        public GetPropertiesByIdspecs(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted && ids.Contains(i.Id))
                .Include(i => i.ListingSources);
        }

    }
    public class PropertiesByIdsSpec : Specification<Domain.Entities.Property>
    {
        public PropertiesByIdsSpec(List<Guid> ids)
        {
            Query.Where(p => !p.IsDeleted && ids.Contains(p.Id))
           .Include(i => i.Address)
           .Include(i => i.MonetaryInfo)
           .Include(i => i.PropertyType)
           .Include(i => i.OwnerDetails)
           .Include(i => i.Dimension)
           .Include(i => i.TagInfo)
           .Include(i => i.Attributes)
           .Include(i => i.Amenities)
           .Include(i => i.Project)
           .Include(i => i.PropertyAssignments)
           .Include(i => i.ListingSourceAddresses)
           .ThenInclude(i => i.ListingSource)
           .Include(i => i.Galleries.Where(j => !j.IsDeleted))
           .Include(i => i.TenantContactInfo)
           .Include(i => i.PropertyOwnerDetails);
        }
    }
    public class GetPropertyByNameSpecs : Specification<Lrb.Domain.Entities.Property, PropertyTitleDto>
    {
        public GetPropertyByNameSpecs(string? propertyName)
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived && i.Title.ToLower().Trim() == propertyName.ToLower().Trim());
        }
    }

}
