﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Reports.Web.Lead.Dtos.UserVsSource
{
     public class UserSourceReportDto: IDto
    {
        public User? User { get; set; }
        public List<UserSourceDto>? Source { get; set; }
    }
    public class UserSourceDto
    {
        public string? DisplayName { get; set; }
        public int? Count { get; set; }


    }

  

    public class FormattedUserSourceReportDto : IDto
    {
        public string SlNo { get; set; } = default!;
        public string? Name { get; set; }

        public List<ViewFormatteDataReportByUserDto>? Data { get; set; }
    }
    public class ViewFormatteDataReportByUserDto : IDto
    {
        public string? Status { get; set; }
        public long Count { get; set; }
    }

    public class ViewFormattedUserSourceReportDto : IDto
    {
        public string? SubSource { get; set; }
        public long? Count { get; set; }
    }

    public class SourceReportByUserDto : IDto
    {
        public Guid UserId { get; set; }
        private string? _firstName;
        private string? _lastName;
        private string? _userName;

        public string? FirstName
        {
            get => _firstName;
            set
            {
                _firstName = value;
                UpdateUserName();
            }
        }

        public string? LastName
        {
            get => _lastName;
            set
            {
                _lastName = value;
                UpdateUserName();
            }
        }

        public string? UserName
        {
            get => _userName;
            set => _userName = value;
        }
        private void UpdateUserName()
        {
            _userName = $"{_firstName} {_lastName}".Trim();
        }
        public List<UserSourceDto>? Source { get; set; }
    }

    public class FormattedRevenueUserSourceReportDto : IDto
    {
        public string SlNo { get; set; } = default!;
        public string? Name { get; set; }

        public List<ViewFormatteRevenueDataReportByUserDto>? Data { get; set; }
    }
    public class ViewFormatteRevenueDataReportByUserDto : IDto
    {
        public string? Status { get; set; }
        public long Ammount { get; set; }
        public double? LeadCount { get; set; }
    }
}
