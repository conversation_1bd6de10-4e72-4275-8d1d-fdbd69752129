﻿using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.Persistence;
using Lrb.Application.DailyStatusUpdates.Dtos;
using Lrb.Application.DailyStatusUpdates.Mappings;
using Lrb.Application.Lead.Web.Mappings;
using Lrb.Application.Lead.Web.Requests;
using Lrb.Application.Lead.Web.Requests.CommonHandler;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.MasterData;
using Microsoft.Extensions.DependencyInjection;

namespace Lrb.Application.Lead.Web
{
    public class UpdateBulkLeadSourceRequest : IRequest<Response<int>>
    {
        public LeadSource LeadSource { get; set; }
        public List<Guid> LeadIds { get; set; } = default!;
        public string? SubSource { get; set; }
        public Guid? TrackerId { get; set; }
        public Guid? CurrentUserId { get; set; }
        public string? TenantId { get; set; }
        public List<SourceDto>? Sources { get; set; }
        public BulkType? BulkCategory { get; set; }
    }
    public class UpdateBulkLeadSourceRequestHandler : BulkStatus<PERSON>and<PERSON>, IRequestHandler<UpdateBulkLeadSourceRequest, Response<int>>
    {
        protected readonly IDapperRepository _dapperRepository;
        protected readonly IServiceProvider _serviceProvider;
        private readonly IRepositoryWithEvents<LeadHistoryHot> _newLeadHistoryRepo;
        public UpdateBulkLeadSourceRequestHandler(IServiceProvider serviceProvider, IRepositoryWithEvents<LeadHistoryHot> newLeadHistoryRepo) : base(serviceProvider, typeof(UpdateBulkLeadSourceRequestHandler).Name, "Handle")
        {
            _serviceProvider = serviceProvider;
            _dapperRepository = _serviceProvider.GetRequiredService<IDapperRepository>();
            _newLeadHistoryRepo = newLeadHistoryRepo;
        }
        public async Task<Response<int>> Handle(UpdateBulkLeadSourceRequest request, CancellationToken cancellationToken)
        {
            try
            {
                int totalUpdatedCount = 0;
                var leads = await _leadRepo.ListAsync(new LeadsByIdsSpec(request.LeadIds), cancellationToken);
                var currentUserId = request.CurrentUserId ?? _currentUserRepo.GetUserId();
                var allSources = request.Sources ?? (await _dapperRepository.GetAllIntegrationSubSourceAsync<SourceDto>(request.TenantId ?? _currentUserRepo.GetTenant() ?? string.Empty)).ToList();
                List<LeadHistory> newHistories = new();
                List<LeadHistory> oldHistories = new();
                if (leads?.Any() ?? false)
                {
                    var oldLeadsDto = leads.Adapt<List<ViewLeadDto>>();
                    var leadEnquires = new List<LeadEnquiry>();
                    var updateLeads = new List<Domain.Entities.Lead>();
                    foreach (var lead in leads)
                    {
                        if (lead != null && lead.Enquiries != null && lead.Enquiries.Any())
                        {
                            try
                            {
                                lead.ShouldUpdatePickedDate = await ShouldUpdatePickedDate(lead, request.Adapt<PickedLeadDto>(), currentUser: currentUserId);
                            }
                            catch (Exception ex)
                            {
                                throw;
                            }

                            LeadEnquiry existingEnquiry = lead.Enquiries.FirstOrDefault() ?? new();

                            await UpdateSourceAndSubSourceAsync(existingEnquiry, request.LeadSource, request.SubSource);

                            //await UpdateLeadSourceInfoAsync(existingEnquiry, cancellationToken,allSources:allSources);

                            //await _leadEnquiryRepo.UpdateAsync(existingEnquiry, cancellationToken);
                            leadEnquires.Add(existingEnquiry);
                            //await _leadRepo.UpdateAsync(lead, cancellationToken);
                            lead.LastModifiedOn = DateTime.UtcNow;
                            lead.LastModifiedBy = currentUserId;
                            totalUpdatedCount++;
                            updateLeads.Add(lead);

                            var result = await UpdateLeadHistoryV1Async(lead, cancellationToken: cancellationToken, currentUserId: currentUserId);
                            if (result.Item1 != null)
                            {
                                oldHistories.Add(result.Item1);
                            }
                            if (result.Item2 != null)
                            {
                                newHistories.Add(result.Item2);
                            }
                            if ((request.BulkCategory != null) && request.BulkCategory != BulkType.None)
                            {
                                lead.BulkCategory = request.BulkCategory;
                            }
                        }
                    }
                    if (leadEnquires?.Any() ?? false)
                    {
                        await _leadEnquiryRepo.UpdateRangeAsync(leadEnquires, cancellationToken);
                    }
                    if (updateLeads?.Any() ?? false)
                    {
                        await _leadRepo.UpdateRangeAsync(updateLeads, cancellationToken);
                    }
                    if (newHistories?.Any() ?? false)
                    {
                        await _leadHistoryRepo.AddRangeAsync(newHistories, cancellationToken);
                    }
                    if (oldHistories?.Any() ?? false)
                    {
                        await _leadHistoryRepo.UpdateRangeAsync(oldHistories, cancellationToken);
                    }
                    var leadIds = updateLeads?.Select(x => x.Id).ToList();
                    if (leadIds?.Any() ?? false)
                    {
                        var newUpdateLeads = await _leadRepo.ListAsync(new LeadByIdSpec(leadIds));
                        List<LeadHistoryHot> newLeadHotHistories = new();
                        var leadStatues = await _customLeadStatusRepo.ListAsync(cancellationToken);
                        var masterPropertyTypes = await _propertyTypeRepo.ListAsync(cancellationToken);
                        foreach (var lead in newUpdateLeads)
                        {
                            #region Update New Lead History Table
                            var newLeadDto = lead.Adapt<ViewLeadDto>();
                            var oldLeadDto = oldLeadsDto.FirstOrDefault(x => x.Id == lead.Id);
                            newLeadDto.LastModifiedBy = currentUserId;
                            await newLeadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken, currentUserId);
                            await oldLeadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken, currentUserId);
                            var latestModificationVersion = await _dapperRepository.GetLatestLeadHistoryVersionAsync(newLeadDto.Id);
                            var histories = await LeadHistoryHelperV2.V2UpdateLeadHistoryForVM(newLeadDto, oldLeadDto, latestModificationVersion ?? 1, leadStatues, masterPropertyTypes, _userService, currentUserId, cancellationToken);
                            newLeadHotHistories.AddRange(histories);
                            #endregion   
                        }

                        if (newLeadHotHistories?.Any() ?? false)
                        {
                            await _newLeadHistoryRepo.AddRangeAsync(newLeadHotHistories);
                        }
                    }
                    else
                    {
                        return new(totalUpdatedCount);
                    }
                }
                return new(totalUpdatedCount);
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{typeof(UpdateBulkLeadSourceRequestHandler).Name} - Handle()");
                throw;
            }
        }
        private async Task UpdateSourceAndSubSourceAsync(LeadEnquiry existingEnquiry, LeadSource leadSource, string? subSource)
        {
            if (existingEnquiry.LeadSource != leadSource)
            {
                existingEnquiry.LeadSource = leadSource;
                if (existingEnquiry.SubSource != subSource)
                {
                    existingEnquiry.SubSource = subSource;
                }
                else
                {
                    existingEnquiry.SubSource = null;
                }
            }
            else
            {
                existingEnquiry.LeadSource = leadSource;
                existingEnquiry.SubSource = subSource;
            }

        }
    }
}
