﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.DataManagement.Web.Dtos
{
    public class ProspectDapperDto
    {
        public Guid Id { get; set; }
        public string? Name { get; set; }
        public string? ContactNo { get; set; }
        public string? AlternateContactNo { get; set; }
        public string? Email { get; set; }
        public string? Notes { get; set; }
        public Guid AssignTo { get; set; }
        public Guid? AssignedFrom { get; set; }
        public string? AgencyName { get; set; }
        public string? CompanyName { get; set; }
        public Guid? StatusId { get; set; }
        public Profession Profession { get; set; }
        public bool IsQualified { get; set; }
        public Guid? ArchivedBy { get; set; }
        public DateTime? ArchivedOn { get; set; }
        public Guid? RestoredBy { get; set; }
        public DateTime? RestoredOn { get; set; }
        public bool IsConvertedToLead { get; set; }
        public bool IsArchived { get; set; }
        public DateTime? QualifiedDate { get; set; }
        public DateTime? ConvertedDate { get; set; }
        public DateTime? ScheduleDate { get; set; }
        public string? ExcelUpload { get; set; }
        public Guid? QualifiedBy { get; set; }
        public Guid? ConvertedBy { get; set; }
        public Guid? AccountId { get; set; }
        public string? CountryCode { get; set; }
        public string? AltCountryCode { get; set; }
        public Guid CreatedBy { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid LastModifiedBy { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public DateTime? DeletedOn { get; set; }
        public Guid? DeletedBy { get; set; }
        public bool IsDeleted { get; set; }
        public Guid UserId { get; set; }
        public string? ReferralName { get; set; }
        public string? ReferralContactNo { get; set; }
        public string? ReferralEmail { get; set; }
        public string? ChannelPartnerName { get; set; }
        public string? ChannelPartnerExecutiveName { get; set; }
        public string? ChannelPartnerContactNo { get; set; }
        public string? Designation { get; set; }
        public string? ExecutiveName { get; set; }
        public string? ExecutiveContactNo { get; set; }
        public string? Nationality { get; set; }
        public Guid? AddressId { get; set; }
        public Guid? ClosingManager { get; set; }
        public Guid? SourcingManager { get; set; }

        public string? UploadTypeName { get; set; }
        public DateTime? PossesionDate { get; set; }
        public string? LandLine { get; set; }


        public Gender? Gender { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public MaritalStatusType? MaritalStatus { get; set; }
        public DateTime? AnniversaryDate { get; set; }

    }
}
