namespace Lrb.Application.Source.Web
{
    public class CreateOrUpdateSourceDto : BaseSourceDto
    {
    }

    public class ViewSourceDto : BaseSourceDto
    {
        public DateTime? LastModifiedOn { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid LastModifiedBy { get; set; }
    }

    public class BaseSourceDto : IDto
    {
        public Guid Id { get; set; }
        public string? DisplayName { get; set; }
        public int Value { get; set; }
        public int OrderRank { get; set; }
        public string? ImageURL { get; set; }
        public string? ProgressColor { get; set; }
        public string? BackgroundColor { get; set; }
        public LeadSourceType LeadSourceType { get; set; }
        public bool IsDefault { get; set; }
        public bool IsEnabled { get; set; }
    }
    public class SourceDto
    {
        public Guid? Id { get; set; }
        public string? DisplayName { get; set; }
        public int? Value { get; set; }
    }
}