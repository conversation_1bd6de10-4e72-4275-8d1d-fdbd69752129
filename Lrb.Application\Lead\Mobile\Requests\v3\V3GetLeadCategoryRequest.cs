﻿using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;

namespace Lrb.Application.Lead.Mobile.v3
{
    public class V3GetLeadCategoryRequest : PaginationFilter, IRequest<Response<V3LeadCategoryDto>>
    {
        public LeadFilterTypeMobile? FilterType { get; set; }
        public BaseLeadVisibility LeadVisibility { get; set; }
        public bool? IsWithTeam { get; set; }
        public List<Guid>? AssignToIds { get; set; }
        public DateType? DateType { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? SearchByNameOrNumber { get; set; }
        //public UserType? UserType { get; set; }
        public List<Guid>? SecondaryUsers { get; set; }
        public bool? IsDualOwnershipEnabled { get; set; }

        public bool? IsPicked { get; set; }
        public List<Guid>? HistoryAssignedToIds { get; set; }
        public List<Guid>? AssignFromIds { get; set; }
        public List<Guid>? SecondaryFromIds { get; set; }
        public List<Guid>? DoneBy { get; set; }
        public bool? IsWithHistory { get; set; }
        public bool? CanAccessAllLeads { get; set; }
        public List<string>? Communities { get; set; }
        public List<string>? SubCommunities { get; set; }
        public List<string>? TowerNames { get; set; }
        public List<string>? Countries { get; set; }
        public List<string>? PostalCodes { get; set; }
        public List<string>? Cities { get; set; }
        public List<string>? States { get; set; }
        public bool? DataConverted { get; set; }
        public List<Guid>? QualifiedByIds { get; set; }
        public List<Guid>? CreatedByIds { get; set; }
        public List<Guid>? LastModifiedByIds { get; set; }
        public List<Guid>? ArchivedByIds { get; set; }
        public List<Guid>? RestoredByIds { get; set; }
        public List<Guid>? ClosingManagers { get; set; }
        public List<Guid>? SourcingManagers { get; set; }
        public string? AdditionalPropertiesKey { get; set; }
        public string? AdditionalPropertiesValue { get; set; }
        public List<string>? ChannelPartnerNames { get; set; }
        public List<Profession>? Profession { get; set; }
        public string? UploadTypeName { get; set; }
        public string? ConfidentialNotes { get; set; }
        public int? ChildLeadsCount { get; set; }
        public bool? IsUntouched { get; set; }
        public List<string>? Designations { get; set; }
        public string? ReferralName { get; set; }
        public string? ReferralContactNo { get; set; }
        public string? ReferralEmail { get; set; }
        public List<string>? ClusterName { get; set; }
        public List<string>? Nationality { get; set; }
        public List<Guid>? OriginalOwnerIds { get; set; }

        public PossesionType? PossesionType { get; set; }
        public DateTime? FromPossesionDate { get; set; }
        public DateTime? ToPossesionDate { get; set; }
        public List<string>? LandLine { get; set; }
        public bool? ShowOnlyParentLeads { get; set; }
        public List<LeadType>? LeadType { get; set; }
        public List<string>? CountryCode { get; set; }
        public List<string>? AltCountryCode { get; set; }
        public List<Gender>? GenderTypes { get; set; }
        public List<MaritalStatusType>? MaritalStatuses { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string? TimeZoneId { get; set; }
        public TimeSpan? BaseUTcOffset { get; set; }
        public DateTime? AnniversaryDate { get; set; }

    }

    public class V3GetLeadCategoryRequestHandler : IRequestHandler<V3GetLeadCategoryRequest, Response<V3LeadCategoryDto>>
    {
        private readonly ICurrentUser _currentUser;
        private readonly ILeadRepository _efLeadRepository;
        private readonly IDapperRepository _dapperRepository;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _statuses;

        public V3GetLeadCategoryRequestHandler(
        ICurrentUser currentUser,
        ILeadRepository efLeadRepository,
        IDapperRepository dapperRepository,
        ILeadRepositoryAsync leadRepositoryAsync,
        IRepositoryWithEvents<CustomMasterLeadStatus> statuses)
        {
            _currentUser = currentUser;
            _efLeadRepository = efLeadRepository;
            _dapperRepository = dapperRepository;
            _leadRepositoryAsync = leadRepositoryAsync;
            _statuses = statuses;
        }
        #region New Implementation using EF core Repo
        public async Task<Response<V3LeadCategoryDto>> Handle(V3GetLeadCategoryRequest request, CancellationToken cancellationToken)
        {
            var userId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            //request.UserType = request?.UserType ?? UserType.None;
            List<Guid> subIds = new();
            try
            {
                if (request?.AssignToIds?.Any() ?? false)
                {
                    if (request?.IsWithTeam ?? false)
                    {
                        subIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.AssignToIds, tenantId ?? string.Empty)).ToList();
                    }
                    else
                    {
                        subIds = request?.AssignToIds ?? new List<Guid>();
                    }
                }
                else
                {
                    subIds = (await _dapperRepository.GetSubordinateIdsAsync(userId, tenantId ?? string.Empty, request?.CanAccessAllLeads))?.ToList() ?? new();
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "V2GetLeadCategoryRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
            if (request?.IsDualOwnershipEnabled == null)
            {
                request.IsDualOwnershipEnabled = await _dapperRepository.GetDualOwnershipDetails(tenantId ?? string.Empty);
            }
            var statuses = await _statuses.ListAsync(cancellationToken);
            var leads = await _efLeadRepository.GetLeadsByCategoryForMobileAsync(request.Adapt<V3GetLeadCategoryRequest>(), userId, subIds, statuses);
            var totalCount = await _efLeadRepository.GetAllCategoryLeadsCountForMobileAsync(request.Adapt<V3GetLeadCategoryRequest>(), userId, subIds, statuses);
            V3LeadCategoryDto categoryDto = new()
            {
                Leads = leads.Adapt<List<V3GetAllLeadsDto>>(),
                TotalCount = totalCount,
                LeadFilter = request?.FilterType ?? LeadFilterTypeMobile.All
            };
            return new Response<V3LeadCategoryDto>(categoryDto);
        }
        #endregion
    }
}
