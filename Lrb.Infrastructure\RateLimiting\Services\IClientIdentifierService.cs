using Microsoft.AspNetCore.Http;

namespace Lrb.Infrastructure.RateLimiting.Services;

/// <summary>
/// Service for identifying clients for rate limiting purposes
/// </summary>
public interface IClientIdentifierService
{
    /// <summary>
    /// Get the client identifier from the HTTP context
    /// </summary>
    /// <param name="context">HTTP context</param>
    /// <returns>Client identifier string</returns>
    string GetClientIdentifier(HttpContext context);

    /// <summary>
    /// Check if a client is whitelisted
    /// </summary>
    /// <param name="context">HTTP context</param>
    /// <returns>True if client is whitelisted</returns>
    bool IsWhitelisted(HttpContext context);

    /// <summary>
    /// Check if a path should be excluded from rate limiting
    /// </summary>
    /// <param name="path">Request path</param>
    /// <returns>True if path should be excluded</returns>
    bool IsPathExcluded(string path);
}