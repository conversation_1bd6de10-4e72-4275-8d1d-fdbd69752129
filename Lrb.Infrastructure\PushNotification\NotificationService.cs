﻿using Amazon;
using Amazon.Pinpoint;
using Amazon.Pinpoint.Model;
using Ardalis.Specification;
using FirebaseAdmin;
using Google.Apis.Auth.OAuth2;
using Lrb.Application.Common.CustomEmail;
using Lrb.Application.Common.Email;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.Email.Web.Dtos;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web;
using Lrb.Application.Notifications.Dtos;
using Lrb.Application.PushNotification.Specs;
using Lrb.Application.PushNotification.Web.Dtos;
using Lrb.Application.Utils;
using Lrb.Application.WA.Web;
using Lrb.Domain.Constants;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Entities.RawContent;
using Lrb.Domain.Enums;
using Lrb.Infrastructure.BlobStorage;
using Lrb.Infrastructure.Identity;
using Lrb.Infrastructure.PushNotification.Firebase;
using Lrb.Infrastructure.WhatsApp;
using Mapster;
using Microsoft.Azure.Cosmos.Linq;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using RestSharp;
using System.Globalization;
using System.Net;
using System.Net.Http.Headers;
using System.Net.Sockets;
using System.Text;
using System.Text.RegularExpressions;

namespace Lrb.Infrastructure.PushNotification
{
    public class NotificationService : INotificationService
    {
        private const string TOPIC_ATTRIBUTE_KEY = "TOPICS";
        private const string APP_TOKEN_ATTRIBUTE_KEY = "APP_TOKEN";
        private const string USERID_ATTRIBUTE_KEY = "USERID";
        private const string USERNAME_ATTRIBUTE_KEY = "USERNAME";
        private const string EMAIL_ATTRIBUTE_KEY = "EMAIL";
        protected readonly string DeviceUserAlreadyRegisteredException = "Device user already exists";
        // Pinpoint OptOut Values
        private const string OPTOUT_ALL_ATTRIBUTE_VALUE = "ALL";
        private const string OPTOUT_NONE_ATTRIBUTE_VALUE = "NONE";
        //EndpointStatus
        private const string ENDPOINT_ACTIVE = "ACTIVE";
        private const string ENDPOINT_INACTIVE = "INACTIVE";
        private const string PLATFROM_ANDROID_VALUE = "Android";
        private const string PLATFROM_iOS_VALUE = "iOS";

        private const string CreatedDateColumnName = "CreatedDate";
        private const string ApplicationName = "ApplicationName";
        //private const string PinpointApplicationId = "dde7a98c75494c18916f099151749059";
        private const string AppToken = "Leadrat";
        protected readonly string NotificationMessageBodyORRawContentBothShouldNotBeNull = "MessageBody or RawContent should not be null";
        protected readonly string NotificationMessageBodyAndRawContentExist = "MessageBody and RawContent both should not be part of the payload";
        protected readonly string NotificationScheduleDateShouldNotBeNull = "Date is Required for Schedule Notifications";
        protected readonly string NotificationScheduleTimeShouldNotBeNull = "Time is Required for Schedule Notifications";
        protected readonly string NotificationScheduleTimeZoneShouldNotBeNull = "Time Zone is Required for Schedule Notifications";

        private readonly IPinpointService _pinpointService;
        private readonly Serilog.ILogger _logger;
        private readonly IRepositoryWithEvents<Notification> _repository;
        private readonly IRepositoryWithEvents<Device> _deviceRepository;
        private PinpointSetting _settings;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<PushNotificationRecords> _pushNotificationRecordsRepo;
        private readonly IGraphEmailService _graphEmailService;
        private readonly IRepositoryWithEvents<GlobalSettings> _globalSettingsRepo;
        private readonly ICustomEmailService _customEmailService;
        private readonly IReadRepository<MasterEmailInfo> _masterEmailInfoRepo;
        private readonly IRepositoryWithEvents<WAMessage> _waMessage;
        private readonly IRepositoryWithEvents<WAPayloadMapping> _waPayloadMappingRepo;
        private FirebaseSetting _firebaseSetting;
        private MobileFirebaseSetting _mobileFirebaseSetting;
        private readonly RestClient _restClient;
        private readonly IRepositoryWithEvents<WAApiInfo> _waApiInfo;
        private readonly IUserService _userService;

        public NotificationService(
            IRepositoryWithEvents<Notification> repository,
            IRepositoryWithEvents<Device> deviceRepository,
            IPinpointService pinpointService,
            Serilog.ILogger logger,
            IOptions<PinpointSetting> options,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<PushNotificationRecords> pushNotificationRecordsRepo,
            IGraphEmailService graphEmailService,
            IRepositoryWithEvents<GlobalSettings> globalSettingsRepo,
            ICustomEmailService customEmailService,
            IReadRepository<MasterEmailInfo> masterEmailInfoRepo,
            IRepositoryWithEvents<WAMessage> waMessage,
            IRepositoryWithEvents<WAPayloadMapping> waPayloadMappingRepo,
            IOptions<FirebaseSetting> firebaseSetting,
            IOptions<MobileFirebaseSetting> mobileFirebaseSetting,
            IOptions<RestClientOptions> restClientOptions,
            IRepositoryWithEvents<WAApiInfo> waApiInfo,
            IUserService userService
            )
        {
            _repository = repository;
            _deviceRepository = deviceRepository;
            _logger = logger;
            _pinpointService = pinpointService;
            _settings = options.Value;
            _leadRepositoryAsync = leadRepositoryAsync;
            _pushNotificationRecordsRepo = pushNotificationRecordsRepo;
            _graphEmailService = graphEmailService;
            _globalSettingsRepo = globalSettingsRepo;
            _customEmailService = customEmailService;
            _masterEmailInfoRepo = masterEmailInfoRepo;
            _waMessage = waMessage;
            _waPayloadMappingRepo = waPayloadMappingRepo;
            _firebaseSetting = firebaseSetting.Value;
            _mobileFirebaseSetting = mobileFirebaseSetting.Value;
            _restClient = new RestClient(restClientOptions.Value);
            _waApiInfo = waApiInfo;
            _userService = userService;
        }

        public async Task<Guid> CreateNotificationAsync(NotificationDTO notification)
        {
            bool isBodyAvailable = false;

            if (!string.IsNullOrEmpty(notification.MessageBody))
                isBodyAvailable = true;

            if (!string.IsNullOrEmpty(notification.RawContent))
                isBodyAvailable = true;
            if (!string.IsNullOrEmpty(notification.APNsRawContent))
                isBodyAvailable = true;
            if (!string.IsNullOrEmpty(notification.FCMRawContent))
                isBodyAvailable = true;

            if (!isBodyAvailable)
            {
                throw new InvalidOperationException(NotificationMessageBodyAndRawContentExist);
            }

            if (notification.IsScheduled && string.IsNullOrEmpty(Convert.ToString(notification.ScheduledDate)))
                throw new InvalidOperationException(NotificationScheduleDateShouldNotBeNull);

            if (notification.IsScheduled && string.IsNullOrEmpty(Convert.ToString(notification.ScheduledTime)))
                throw new InvalidOperationException(NotificationScheduleTimeShouldNotBeNull);

            if (notification.IsScheduled && string.IsNullOrEmpty(Convert.ToString(notification.ZoneName)))
                throw new InvalidOperationException(NotificationScheduleTimeZoneShouldNotBeNull);

            notification.Created = DateTime.UtcNow;

            //Create unavailable SG and Custom topics
            //await AutoCreateTopics(notification);

            // check any HTML tags and remove them
            var regex = new Regex("<[^>]+>", RegexOptions.IgnoreCase);
            notification.MessageBody = string.IsNullOrEmpty(notification.MessageBody) ? string.Empty : WebUtility.HtmlDecode((regex.Replace(notification.MessageBody, "")));
            notification.SentToPinpoint = false;
            await _repository.AddAsync(notification.Adapt<Notification>());
            return notification.Id;
        }

        public async Task<Guid> SendNotificationAsync(NotificationDTO notification)
        {
            {
                try
                {
                    _logger.Information($"NotificationService::SendNotificationAsync() called, NotificationDTO: {JsonConvert.SerializeObject(notification)}");
                    Console.WriteLine("CreateNotificationAsync====");
                    bool isBodyAvailable = false;

                    if (!string.IsNullOrEmpty(notification.MessageBody))
                        isBodyAvailable = true;

                    if (!string.IsNullOrEmpty(notification.RawContent))
                        isBodyAvailable = true;
                    if (!string.IsNullOrEmpty(notification.APNsRawContent))
                        isBodyAvailable = true;
                    if (!string.IsNullOrEmpty(notification.FCMRawContent))
                        isBodyAvailable = true;

                    if (!isBodyAvailable)
                    {
                        throw new InvalidOperationException(NotificationMessageBodyAndRawContentExist);
                    }

                    if (notification.IsScheduled && string.IsNullOrEmpty(Convert.ToString(notification.ScheduledDate)))
                        throw new InvalidOperationException(NotificationScheduleDateShouldNotBeNull);

                    if (notification.IsScheduled && string.IsNullOrEmpty(Convert.ToString(notification.ScheduledTime)))
                        throw new InvalidOperationException(NotificationScheduleTimeShouldNotBeNull);

                    notification.SendNotificationType = NotificationType.TargetSpecific;
                    Console.WriteLine("notification.SendNotificationType==={0}", notification.SendNotificationType);
                    _logger.Information("notification.SendNotificationType==={0}", notification.SendNotificationType);
                    var createSegmentResponse = await CreateSegment(notification);
                    Console.WriteLine("createSegmentResponse==={0}", JsonConvert.SerializeObject(createSegmentResponse));
                    _logger.Information("createSegmentResponse==={0}", JsonConvert.SerializeObject(createSegmentResponse));
                    if (string.IsNullOrEmpty(createSegmentResponse?.SegmentResponse?.Id))
                        return Guid.Empty;


                    _logger.Information($"NotificationService -> AWS Settings: {JsonConvert.SerializeObject(_settings)}");
                    //Todo: Need to Remove this Hard Code
                    if (_settings == null || string.IsNullOrWhiteSpace(_settings.PinpointApplicationId))
                    {
                        _settings = new()
                        {
                            AWSAccessToken = "********************",
                            AWSSecret = "cFkS7v131SaaMLAmaFKBLXAUfU/+TpGOmF/PTAs8",
                            PinpointApplicationId = "9bdc67aedb1d4d16bb4ba65323c04a17",
                            Region = "ap-south-1"
                        };
                    }
                    GetCampaignsRequest campaignsRequest = new GetCampaignsRequest()
                    {
                        ApplicationId = _settings.PinpointApplicationId,
                    };
                    notification.UniqueId = Guid.NewGuid();
                    var records = await GetNotificationRecords(createSegmentResponse ?? new(), notification);
                    CreateCampaignRequest campaignRequest = CreateCampaign(notification, createSegmentResponse?.SegmentResponse?.Id ?? string.Empty, records);
                    _logger.Information($"Pinpoint CampaignRequest: {JsonConvert.SerializeObject(campaignRequest)}");
                    CreateCampaignResponse? response = null;
                    try
                    {
                        response = await _pinpointService.SendNotificationAsync(campaignRequest);
                        notification.SentToPinpoint = true;
                        records.ForEach(i => i.CampaignId = response.CampaignResponse.Id);
                        var recordsAdditionResponse = await _pushNotificationRecordsRepo.AddRangeAsync(records);
                    }
                    catch (Exception e)
                    {
                        _logger.Information($"Pinpoint CampaignResponse: {JsonConvert.SerializeObject(response)}");
                        _logger.Information($"Pinpoint CampaignResponse Exception: {JsonConvert.SerializeObject(e)}");
                        notification.SentToPinpoint = false;
                    }
                    _logger.Information($"Pinpoint CampaignResponse: {JsonConvert.SerializeObject(response)}");
                    Console.WriteLine("campaignRequest==={0}", JsonConvert.SerializeObject(campaignRequest));
                    Console.Write("Response from Pinpoint Service SendNotification" + JsonConvert.SerializeObject(response));

                    if (response != null)
                    {
                        notification.CampaignRequestDetails = JsonConvert.SerializeObject(campaignRequest);
                        notification.PinpointCampaignDetails = JsonConvert.SerializeObject(response);
                    }

                    Console.WriteLine("Update Notification {0}", JsonConvert.SerializeObject(notification));
                    //notification.Id = Guid.NewGuid();
                    //var result = await _repository.AddAsync(notification.Adapt<Notification>());
                    //var getNotification = await _repository.GetByIdAsync(result.Id);
                    //Console.WriteLine("Get Updated Notification {0}", JsonConvert.SerializeObject(getNotification));
                    //return result.Id;

                    var notificationsAdded = await _repository.AddRangeAsync(await GetUpdatedNotificationsAsync(notification, records));
                    var uniqueNotification = notificationsAdded?.FirstOrDefault();
                    //var getNotification = await _repository.FirstOrDefaultAsync(new GetNotificationsByUniqueIdSpec(notificationUniqueId ?? default));
                    _logger.Information($"Notification Unique Id -> {uniqueNotification?.UniqueId}");
                    return uniqueNotification?.Id ?? default;
                }
                catch (Exception ex)
                {
                    _logger.Information($"NotificationService::SendNotificationAsync() Exception: {JsonConvert.SerializeObject(ex)}");
                    var error = new LrbError()
                    {
                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                        ErrorSource = ex?.Source,
                        StackTrace = ex?.StackTrace,
                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                        ErrorModule = "NotificationService -> SendNotificationAsync()"
                    };
                    await _leadRepositoryAsync.AddErrorAsync(error);
                    throw;
                }

            }
        }
        public async Task<bool> SendEmailNotification(EmailSenderDto? emailSenderDto)
        {
            _logger.Information($"NotificationService -> SendEmailNotification -> emailSenderDto : {JsonConvert.SerializeObject(emailSenderDto, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}");
            try
            {
                var globalSettings = (await _globalSettingsRepo.ListAsync()).FirstOrDefault();
                _logger.Information($"NotificationService -> SendEmailNotification -> GlobalSettings : {JsonConvert.SerializeObject(globalSettings, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}");
                if (globalSettings != null)
                {
                    var notificationSettings = GetNotificationSettings(globalSettings);
                    if ((notificationSettings?.ChannelSettings?.IsEmailNotificationEnabled ?? false) && emailSenderDto != null)
                    {
                        var masterEmailInfo = (await _masterEmailInfoRepo.ListAsync()).FirstOrDefault();
                        if (masterEmailInfo != null)
                        {
                            await _customEmailService.SendEmailAsync(emailSenderDto.SenderEmailAddress,
                                                            emailSenderDto.To ?? new(),
                                                            emailSenderDto.Cc,
                                                            emailSenderDto.Bcc,
                                                            emailSenderDto.Subject ?? string.Empty,
                                                            emailSenderDto.EmailBody ?? string.Empty,
                                                            masterEmailInfo.ServerName,
                                                            masterEmailInfo.Port,
                                                            masterEmailInfo.UserName,
                                                            masterEmailInfo.Password ?? string.Empty,
                                                            MailPriority.Normal);
                        }
                        //await _graphEmailService.SendEmail(emailSenderDto);
                        _logger.Information($"NotificationService -> SendEmailNotification -> emailSenderDto (Email Sent) : {JsonConvert.SerializeObject(emailSenderDto, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}");
                        return true;
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.Information($"NotificationSenderService -> Exception While Sending Email Notification -> {JsonConvert.SerializeObject(ex, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}");
                return false;
            }

        }
        public NotificationSettings? GetNotificationSettings(GlobalSettings globalSettings)
        {
            var notificationSettingsString = globalSettings.NotificationSettings;
            NotificationSettings? notificationSettings = null;
            if (!string.IsNullOrWhiteSpace(notificationSettingsString))
            {
                notificationSettings = JsonConvert.DeserializeObject<NotificationSettings>(notificationSettingsString);
            }
            return notificationSettings;
        }
        public async Task<List<Notification>> GetUpdatedNotificationsAsync(NotificationDTO notificationDto, List<PushNotificationRecords> records)
        {
            var activeUserIds = records.Select(i => i.UserId).ToList().Distinct();
            List<NotificationDTO> dtos = new();
            foreach (var userId in notificationDto.UserIds ?? new())
            {
                var dto = notificationDto.Adapt<NotificationDTO>();
                dto.Id = Guid.NewGuid();
                dto.UserId = userId;
                dto.IsAnyDeviceRegistered = activeUserIds.Contains(userId);
                dtos.Add(dto);
            }
            return dtos.Adapt<List<Notification>>();
        }
        private async Task<List<PushNotificationRecords>> GetNotificationRecords(CreateSegmentResponse segmentResponse, NotificationDTO notificationDto)
        {
            var groups = segmentResponse.SegmentResponse.SegmentGroups.Groups;
            var users = groups[0].Dimensions[0];
            var u2 = users.UserAttributes.Where(i => i.Key == USERID_ATTRIBUTE_KEY).Select(i => i.Value).ToList();
            var u3 = u2[0];
            var attType = u3.AttributeType;
            var userids = u3.Values;
            List<PushNotificationRecords> pushNotificationRecords = new();
            foreach (var id in userids)
            {
                if (Guid.TryParse(id, out var userId))
                {
                    var devices = await _deviceRepository.ListAsync(new DeviceByUserIdSpec(userId));
                    foreach (var device in devices)
                    {
                        PushNotificationRecords record = new();
                        record.IsDelivered = false;
                        record.UserId = userId;
                        record.IsDelivered = false;
                        record.DeviceId = device.Id;
                        record.NotificationUniqueId = notificationDto.UniqueId;
                        record.DeviceUDID = device.DeviceUDID;
                        pushNotificationRecords.Add(record);
                    }
                }
            }
            return pushNotificationRecords;
        }
        public async Task<bool> SendTestNotificationAsync(List<Guid> userIds, Application.PushNotification.SendPushNotificationRequestHandler.GcmMessage gcmMessage, List<Guid> deviceIds)
        {
            //Todo: Need to Remove this Hard Code
            if (_settings == null || string.IsNullOrWhiteSpace(_settings.PinpointApplicationId))
            {
                _settings = new()
                {
                    AWSAccessToken = "********************",
                    AWSSecret = "cFkS7v131SaaMLAmaFKBLXAUfU/+TpGOmF/PTAs8",
                    PinpointApplicationId = "9bdc67aedb1d4d16bb4ba65323c04a17",
                    Region = "ap-south-1"
                };
            }
            var devices = await _deviceRepository.ListAsync(new DeviceByUserIdSpec(userIds), CancellationToken.None);
            var deviceTokens = devices?.Select(i => i.NewNotificationToken)?.ToList();
            using var pinpointClient = new AmazonPinpointClient(_settings.AWSAccessToken, _settings.AWSSecret, RegionEndpoint.GetBySystemName(_settings.Region));
            var addresses = new Dictionary<string, AddressConfiguration>();
            MessageRequest messageRequest = new();
            if (deviceTokens?.Any() ?? false)
            {
                foreach (var token in deviceTokens)
                {
                    addresses.Add(token, new AddressConfiguration()
                    {
                        ChannelType = "GCM",
                        RawContent = JsonConvert.SerializeObject(gcmMessage)
                    });
                }
                messageRequest = new MessageRequest()
                {
                    Addresses = addresses,
                };
            }
            else if (deviceIds.Any())
            {
                Dictionary<string, EndpointSendConfiguration> endPoints = new();
                foreach (var id in deviceIds)
                {
                    endPoints.Add(id.ToString(), new()
                    {
                        RawContent = JsonConvert.SerializeObject(gcmMessage),
                    });
                }
                messageRequest = new MessageRequest()
                {
                    Endpoints = endPoints
                };
            }
            SendMessagesRequest sendMessagesRequest = new SendMessagesRequest()
            {
                ApplicationId = _settings.PinpointApplicationId,
                MessageRequest = messageRequest,
            };
            var sendMessagesResponse = pinpointClient.SendMessagesAsync(sendMessagesRequest).Result;
            if (sendMessagesResponse.HttpStatusCode == System.Net.HttpStatusCode.OK)
            {
                _logger.Information($"NotificationService::SendTestNotificationAsync()-> Notification Sent Successfully!");
                return true;
            }
            else
            {
                _logger.Information($"NotificationService::SendTestNotificationAsync() -> Failed to send test Notification -> HttpStatusCode : {sendMessagesResponse.HttpStatusCode}, SendMessageResponse: {JsonConvert.SerializeObject(sendMessagesResponse.MessageResponse)}");
                return false;
            }
        }

        public async Task<bool> SendTestNotificationAsync(List<string> deviceTokens, Application.PushNotification.SendPushNotificationRequestHandler.GcmMessage gcmMessage, List<Guid> deviceIds)
        {
            //Todo: Need to Remove this Hard Code
            if (_settings == null || string.IsNullOrWhiteSpace(_settings.PinpointApplicationId))
            {
                _settings = new()
                {
                    AWSAccessToken = "********************",
                    AWSSecret = "cFkS7v131SaaMLAmaFKBLXAUfU/+TpGOmF/PTAs8",
                    PinpointApplicationId = "9bdc67aedb1d4d16bb4ba65323c04a17",
                    Region = "ap-south-1",
                };
            }
            using var pinpointClient = new AmazonPinpointClient(_settings.AWSAccessToken, _settings.AWSSecret, RegionEndpoint.GetBySystemName(_settings.Region));
            var addresses = new Dictionary<string, AddressConfiguration>();
            MessageRequest messageRequest = new();
            if (deviceTokens.Any())
            {
                foreach (var token in deviceTokens)
                {
                    addresses.Add(token, new AddressConfiguration()
                    {
                        ChannelType = "GCM",
                        RawContent = JsonConvert.SerializeObject(gcmMessage)
                    });
                }
                messageRequest = new MessageRequest()
                {
                    Addresses = addresses,
                };
            }
            else if (deviceIds.Any())
            {
                Dictionary<string, EndpointSendConfiguration> endPoints = new();
                foreach (var id in deviceIds)
                {
                    endPoints.Add(id.ToString(), new()
                    {
                        RawContent = JsonConvert.SerializeObject(gcmMessage),
                    });
                }
                messageRequest = new MessageRequest()
                {
                    Endpoints = endPoints
                };
            }
            SendMessagesRequest sendMessagesRequest = new SendMessagesRequest()
            {
                ApplicationId = _settings.PinpointApplicationId,
                MessageRequest = messageRequest,
            };
            var sendMessagesResponse = pinpointClient.SendMessagesAsync(sendMessagesRequest).Result;
            if (sendMessagesResponse.HttpStatusCode == System.Net.HttpStatusCode.OK)
            {
                _logger.Information($"NotificationService::SendTestNotificationAsync()-> Notification Sent Successfully!");
                return true;
            }
            else
            {
                _logger.Information($"NotificationService::SendTestNotificationAsync() -> Failed to send test Notification -> HttpStatusCode : {sendMessagesResponse.HttpStatusCode}, SendMessageResponse: {JsonConvert.SerializeObject(sendMessagesResponse.MessageResponse)}");
                return false;
            }
        }

        public Task<Guid> UpdateAndSendNotificationAsync(NotificationDTO notification)
        {
            throw new NotImplementedException();
        }

        #region Registraton
        public async Task<RegisteredDeviceResponseDto> CreateUpdateRegistrationAsync(DeviceRegistrationInfo registrationInfo)
        {
            Console.Write("Registration Info:" + JsonConvert.SerializeObject(registrationInfo));

            try
            {
                List<Guid> deviceToDelete = new List<Guid>();
                var devicesWithEndpointIds = await GetDeviceByEnpointId(registrationInfo.EndpointId);
                var devicesWithTokens = await GetDevicesByToken(registrationInfo.Token);
                var devicesByUserId = await GetDevicesByUserId(registrationInfo.UserId);
                var devicesWithExisitingUDID = devicesByUserId.Where(i => i.DeviceUDID == registrationInfo.DeviceUDID).Select(j => j.Id).ToList();

                if (devicesWithEndpointIds != Guid.Empty)
                {
                    Console.Write("Device Found for Deletion with EndPointID :" + JsonConvert.SerializeObject(devicesWithEndpointIds));
                    deviceToDelete.Add(registrationInfo.EndpointId);
                }

                if (devicesWithTokens != null && devicesWithTokens.Count() > 0)
                {
                    Console.Write("Device Found for Deletion with Token :" + JsonConvert.SerializeObject(devicesWithTokens.Except(deviceToDelete)));
                    deviceToDelete.AddRange(devicesWithTokens.Except(deviceToDelete));
                }

                //Check for devices with same UDID
                if (devicesWithExisitingUDID != null && devicesWithExisitingUDID.Any())
                {
                    deviceToDelete.AddRange(devicesWithExisitingUDID.Except(deviceToDelete));
                }

                var devicesNotInToDelete = devicesByUserId.Where(d => !deviceToDelete.Contains(d.Id));

                if (devicesNotInToDelete.Count() > 9)
                {
                    deviceToDelete.AddRange(devicesNotInToDelete.OrderByDescending(d => d.LastModifiedOn).Skip(9).Select(d => d.Id));
                }

                Console.Write("Devices Found for Deletion:" + JsonConvert.SerializeObject(deviceToDelete));

                //Delete the devive if Either EndpointId exists or the TokenExists
                if (deviceToDelete.Any())
                {
                    foreach (var deviceid in deviceToDelete)
                    {
                        await DeleteRegistrationAsync(deviceid);
                    }
                }

                return await GetRegisteredDeviceResponseDtoAsync(registrationInfo, null, false);
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                return new RegisteredDeviceResponseDto() { IsSuccess = false, Error = e.Message };
            }
        }
        public async Task<RegisteredDeviceResponseDto> CreateUpdateRegistrationAsync_Old(DeviceRegistrationInfo registrationInfo)
        {
            Console.Write("Registration Info:" + JsonConvert.SerializeObject(registrationInfo));

            try
            {
                // Get device if exists
                var deviceRegistration = await _deviceRepository.GetByIdAsync(registrationInfo.EndpointId);
                bool isUpdateRegistration = deviceRegistration != null;
                if (!isUpdateRegistration)
                    Console.Write("New registration");

                return await GetRegisteredDeviceResponseDtoAsync(registrationInfo, deviceRegistration, isUpdateRegistration);
            }
            catch (Exception e)
            {
                var error = new LrbError()
                {
                    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                    ErrorSource = e?.Source,
                    StackTrace = e?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "NotificationService -> CreateUpdateRegistrationAsync_Old()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                Console.WriteLine(e.Message);
                return new RegisteredDeviceResponseDto() { IsSuccess = false, Error = e.Message };
            }
        }

        private async Task<RegisteredDeviceResponseDto> GetRegisteredDeviceResponseDtoAsync(DeviceRegistrationInfo registrationInfo, Device deviceRegistration, bool isUpdateRegistration)
        {
            Amazon.Pinpoint.ChannelType channelType = Amazon.Pinpoint.ChannelType.GCM; // registrationInfo.Platform == Platform.Android ? Amazon.Pinpoint.ChannelType.GCM : Amazon.Pinpoint.ChannelType.APNS;
            string platform = registrationInfo.Platform == Lrb.Domain.Enums.Platform.Android ? PLATFROM_ANDROID_VALUE : PLATFROM_iOS_VALUE;

            var updateEndpointRequest = new UpdateEndpointRequest()
            {
                ApplicationId = _settings.PinpointApplicationId,
                EndpointId = registrationInfo.EndpointId.ToString(),
                EndpointRequest = new Amazon.Pinpoint.Model.EndpointRequest()
                {
                    ChannelType = channelType,
                    Address = registrationInfo.Token,
                    Attributes = new Dictionary<string, List<string>>
                    {
                        { TOPIC_ATTRIBUTE_KEY, registrationInfo.Topics },
                        { APP_TOKEN_ATTRIBUTE_KEY, new List<string>() { registrationInfo.ApplicationTokenValue } },
                    },
                    Demographic = new Amazon.Pinpoint.Model.EndpointDemographic()
                    {
                        AppVersion = registrationInfo.AppVersion,
                        Make = registrationInfo.DeviceModel,
                        Platform = registrationInfo.Platform == Lrb.Domain.Enums.Platform.iOS ? "ios" : "android",
                        Model = registrationInfo.DeviceModel
                    },
                    User = new Amazon.Pinpoint.Model.EndpointUser()
                    {
                        UserId = registrationInfo.UserId.ToString(),
                        UserAttributes = new Dictionary<string, List<string>>()
                        {
                            { EMAIL_ATTRIBUTE_KEY, new List<string>()
                            {
                                { registrationInfo.Email }
                            } },
                            { USERID_ATTRIBUTE_KEY, new List<string>()
                            {
                                { registrationInfo.UserId.ToString() }
                            } },
                            { USERNAME_ATTRIBUTE_KEY, new List<string>()
                            {
                                { registrationInfo.UserName }
                            } }
                        }
                    },
                    EndpointStatus = registrationInfo.IsActive ? ENDPOINT_ACTIVE : ENDPOINT_INACTIVE,
                    OptOut = registrationInfo.IsActive ? OPTOUT_NONE_ATTRIBUTE_VALUE : OPTOUT_ALL_ATTRIBUTE_VALUE
                }
            };

            Console.Write("Pinpoint Endpoint update request  " + JsonConvert.SerializeObject(updateEndpointRequest));

            var endPointSuccess = await _pinpointService.UpdateEndpointAsync(updateEndpointRequest);

            Console.Write("End Point reults" + JsonConvert.SerializeObject(endPointSuccess));

            if (endPointSuccess == null) return new RegisteredDeviceResponseDto() { IsSuccess = false, Error = "RegistrationFailed" };

            var device = registrationInfo.Adapt<Device>(); // Check condition in Mapper

            Console.WriteLine("Device Entity after Mapping " + JsonConvert.SerializeObject(device));

            device.PinpointApplicationId = _settings.PinpointApplicationId;

            try
            {
                if (isUpdateRegistration)
                {
                    Console.WriteLine("Entered isUpdateRegistration True");
                    device.NewNotificationToken = registrationInfo.Token;
                    device.OldNotificationToken = deviceRegistration.NewNotificationToken;
                    device.LastLoginDate = DateTime.UtcNow.ToString();
                    await _deviceRepository.UpdateAsync(device);
                    Console.Write("Device Updated " + JsonConvert.SerializeObject(device));
                }
                else
                {
                    Console.WriteLine("Entered isUpdateRegistration False");
                    device.OldNotificationToken = registrationInfo.Token;
                    device.NewNotificationToken = registrationInfo.Token;
                    device.LastLoginDate = DateTime.UtcNow.ToString();

                    // Unique Index for Token (CHECK in NPGSQL)
                    //var keys = Builders<Entities.Device>.IndexKeys;
                    //var indexModel = new CreateIndexModel<Entities.Device>(keys.Ascending(x => x.NewNotificationToken), new CreateIndexOptions() { Unique = true });


                    Console.Write("Device Before inserted " + JsonConvert.SerializeObject(device));
                    await _deviceRepository.AddAsync(device);
                    Console.Write("Device Created " + JsonConvert.SerializeObject(device));
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "NotificationService -> SendNotificationAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                Console.Write("exception occured during GetRegisteredDeviceDto " + JsonConvert.SerializeObject(ex.InnerException + ex.StackTrace));
            }

            return new RegisteredDeviceResponseDto() { IsSuccess = true };
        }

        public async Task<bool> DeleteRegistrationAsync(Guid id)
        {
            var device = await _deviceRepository.GetByIdAsync(id);

            Console.Write("Device Found:" + JsonConvert.SerializeObject(device));
            if (device != null)
            {
                Console.Write("Pinpoint Delete called with below payload");
                var payload = new Amazon.Pinpoint.Model.DeleteEndpointRequest()
                {
                    ApplicationId = device.PinpointApplicationId,
                    EndpointId = id.ToString()
                };
                Console.Write(JsonConvert.SerializeObject(payload));


                var response = await _pinpointService.DeleteEndpointAsync(new Amazon.Pinpoint.Model.DeleteEndpointRequest
                {
                    ApplicationId = device.PinpointApplicationId,
                    EndpointId = id.ToString()
                });

                Console.Write("DeleteEndpoint Response:" + JsonConvert.SerializeObject(response));


                if (response == null) return false;

                Console.Write("Delete device called");
                await _deviceRepository.DeleteAsync(device);
                return true;
            }

            return false;
        }

        private string GetPlatfromAttributeValue(Lrb.Domain.Enums.Platform platform)
        {
            switch (platform)
            {
                case Lrb.Domain.Enums.Platform.Android:
                    return PLATFROM_ANDROID_VALUE;
                case Lrb.Domain.Enums.Platform.iOS:
                    return PLATFROM_iOS_VALUE;
                default:
                    return string.Empty;
            }
        }

        private async Task<Guid> GetDeviceByEnpointId(Guid endPointId)
        {
            var deviceRegistration = await _deviceRepository.GetByIdAsync(endPointId);
            if (deviceRegistration != null)
                return endPointId;
            return Guid.Empty;


        }
        private async Task<IEnumerable<Guid>> GetDevicesByToken(string token)
        {
            var deviceRegistration = await _deviceRepository.ListAsync(new DeviceByTokenSpec(token), CancellationToken.None);

            if (deviceRegistration != null)
                Console.Write("Device Find result in GetDevicesByToken:" + JsonConvert.SerializeObject(deviceRegistration));

            if (deviceRegistration != null && deviceRegistration.Count() > 0)
                return deviceRegistration.Select(d => d.Id).ToList().AsEnumerable();
            return null;
        }
        private async Task<IEnumerable<Device>> GetDevicesByUserId(Guid userId)
        {
            var devices = await _deviceRepository.ListAsync(new DeviceByUserIdSpec(userId), CancellationToken.None);

            if (devices != null)
                Console.Write("Device Find result in GetDevicesByUserId: " + JsonConvert.SerializeObject(devices));
            return devices;
        }
        #endregion





        private async Task<CreateSegmentResponse> CreateSegment(NotificationDTO notification)
        {
            if (notification.SendNotificationType.Equals(NotificationType.Broadcast))
            {
                CreateSegmentRequest createSegmentRequest = CreateBroadcastSegmentRequest(notification);
                Console.WriteLine("CreateSegmentRequest : {0}", JsonConvert.SerializeObject(createSegmentRequest));
                return await _pinpointService.CreateSegmentAsync(createSegmentRequest);
            }
            else if (notification.SendNotificationType.Equals(NotificationType.TargetSpecific) && (notification.Topics == null || notification.Topics?.Count < 90))
            {
                try
                {
                    CreateSegmentRequest createSegmentRequest = CreateCustomSegmentRequest(notification);
                    Console.WriteLine("CreateSegmentRequest : {0}", JsonConvert.SerializeObject(createSegmentRequest));
                    var result = await _pinpointService.CreateSegmentAsync(createSegmentRequest);
                    return result;
                }
                catch (Exception ex)
                {
                    var error = new LrbError()
                    {
                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                        ErrorSource = ex?.Source,
                        StackTrace = ex?.StackTrace,
                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                        ErrorModule = "NotificationService -> CreateSegment()"
                    };
                    await _leadRepositoryAsync.AddErrorAsync(error);
                }
            }

            //CreateSegmentRequest createSegmentRequest = notification.SendNotificationType == NotificationType.Broadcast ?
            //    CreateBroadcastSegmentRequest(notification) :

            //Console.WriteLine("createSegmentRequest==={0}", JsonConvert.SerializeObject(createSegmentRequest));
            //var createSegmentResponse = await _pinpointService.CreateSegmentAsync(createSegmentRequest);
            //Console.WriteLine("createSegmentResponse==={0}", JsonConvert.SerializeObject(createSegmentResponse));
            return null;
        }

        private CreateSegmentRequest CreateCustomSegmentRequest(NotificationDTO notification)
        {
            Console.WriteLine(string.Format("CreateCustomSegmentRequest/notifyObj==={0}", JsonConvert.SerializeObject(notification)));


            //Topics less than 90
            return new CreateSegmentRequest()
            {
                ApplicationId = _settings.PinpointApplicationId,
                WriteSegmentRequest = new WriteSegmentRequest()
                {
                    Name = Guid.NewGuid().ToString(),
                    SegmentGroups = CreateSegmentGroupList(notification)
                }
            };

            //else
            //{
            //      If UserIds > 100 : Upload Json and Update Import Segment
            //      Create Segment without users
            //    CreateSegmentGroupListExcludingUsers(notification);
            //}
        }

        //private async Task<string> CreateImportSegmentAndUpdate(Notification notification)
        //{
        //    //If Topics > 90
        //    //Upload Users to S3, Create Import Job and Update Segment
        //    if (notification.Topics.Count > 90)
        //    {
        //        //Create Json
        //        string ndJsonValue = CreateNDJson(notification);
        //        byte[] fileBytes = GetFileBytesFromJsonString(ndJsonValue);
        //        Console.WriteLine("-----Byte Array Length------");
        //        Console.WriteLine(fileBytes.Length);
        //        //Upload to s3
        //        string s3Url = await UploadNDJsonToS3(fileBytes, notification.Id.ToString());
        //        Console.WriteLine("S3 Url : " + s3Url);
        //        return s3Url;
        //        ////Create Import Job
        //        //var response = await CreateImportUserIdSegmentJob(notification, s3Url);

        //        //if (!string.IsNullOrEmpty(response.ImportJobResponse.Definition.SegmentId))
        //        //{
        //        //    //Create Another Segment, and use Import Segment as Base
        //        //    return new CreateSegmentRequest()
        //        //    {
        //        //        ApplicationId = notification.Application.Application.PinpointApplication.Id,
        //        //        WriteSegmentRequest = new WriteSegmentRequest()
        //        //        {
        //        //            Name = Guid.NewGuid().ToString(),
        //        //            SegmentGroups = CreateSegmentGroupListExcludingUsers(notification, response.ImportJobResponse.Definition.SegmentId)
        //        //        }
        //        //    };
        //        //}
        //    }

        //    return string.Empty;
        //}

        private SegmentGroupList CreateSegmentGroupList(NotificationDTO notification)
        {
            Console.WriteLine("CreateSegmentGroupList");

            var segmentGroupList = new SegmentGroupList()
            {
                Groups = new List<SegmentGroup>(),
                Include = Include.ANY
            };

            //CHANNEL AND PLATFORM
            var segmentGroup = new SegmentGroup()
            {
                Type = Amazon.Pinpoint.Type.ALL,
                Dimensions = new List<SegmentDimensions>()
            };

            var channel = new SetDimension
            {
                DimensionType = DimensionType.INCLUSIVE,
                Values = new List<string>()
            };

            if (notification.IsIOSNotification)
            {
                channel.Values.Add("APNS");
                channel.Values.Add("APNS_SANDBOX");
            }

            if (notification.IsAndroidNotification)
            {
                channel.Values.Add("GCM");
                //channel.Values.Add("FCM");
            }

            var platform = new SetDimension
            {
                DimensionType = DimensionType.INCLUSIVE,
                Values = GetPlatformValues(notification)
            };

            //APP VERSIONS
            //var appVersionSegmentGroup = new SegmentGroup()
            //{
            //    Type = Amazon.Pinpoint.Type.ALL,
            //    Dimensions = new List<SegmentDimensions>()
            //};

            //var appVersions = GetAppVersionValues(notification);

            //if (appVersions != null && appVersions.Count > 0)
            //{
            //    appVersions = appVersions.Where(s => !string.IsNullOrWhiteSpace(s)).Distinct().ToList();
            //    var appVersionDimension = new SegmentDimensions()
            //    {
            //        Demographic = new SegmentDemographics()
            //        {
            //            Channel = channel,
            //            Platform = platform,
            //            AppVersion = new SetDimension()
            //            {
            //                DimensionType = DimensionType.INCLUSIVE,
            //                Values = appVersions
            //            }
            //        }
            //    };
            //    appVersionSegmentGroup.Dimensions.Add(appVersionDimension);
            //    segmentGroupList.Groups.Add(appVersionSegmentGroup);
            //}

            //USER ATTRIBUTES
            var userAttributeSegmentGroup = new SegmentGroup()
            {
                Type = Amazon.Pinpoint.Type.ALL,
                Dimensions = new List<SegmentDimensions>()
            };

            var userAttributes = PrepareSegmentUserAttributes(notification);
            if (userAttributes != null && userAttributes.Count > 0)
            {
                var userAttributeDimension = new SegmentDimensions()
                {
                    Demographic = new SegmentDemographics()
                    {
                        Channel = channel,
                        Platform = platform
                    },
                    UserAttributes = userAttributes
                };
                userAttributeSegmentGroup.Dimensions.Add(userAttributeDimension);
                segmentGroupList.Groups.Add(userAttributeSegmentGroup);
            }

            //CUSTOM ATTRIBUTES
            var customAttributeSegmentGroup = new SegmentGroup()
            {
                Type = Amazon.Pinpoint.Type.ALL,
                Dimensions = new List<SegmentDimensions>()
            };

            var customAttributes = PrepareSegmentAttributes(notification);
            if (customAttributes != null && customAttributes.Count > 0)
            {
                var customAttribDimension = new SegmentDimensions()
                {
                    Demographic = new SegmentDemographics()
                    {
                        Channel = channel,
                        Platform = platform
                    },
                    Attributes = customAttributes
                };
                customAttributeSegmentGroup.Dimensions.Add(customAttribDimension);
                segmentGroupList.Groups.Add(customAttributeSegmentGroup);
            }

            Console.WriteLine(string.Format("segmentGroupList==={0}", JsonConvert.SerializeObject(segmentGroupList)));
            return segmentGroupList;
        }
        public async Task<bool> SendWebNotificationAsync(NotificationDTO notification, string token)
        {
            try
            {
                if (string.IsNullOrEmpty(notification?.MessageBody))
                {
                    return false;
                }

                if (string.IsNullOrEmpty(token))
                {
                    return false;
                }

                // Validate token before sending notification
                if (!IsValidFCMToken(token))
                {
                    return false;
                }

                // Initialize Firebase Admin SDK
                var app = InitializeFirebaseApp();
                var messaging = FirebaseAdmin.Messaging.FirebaseMessaging.GetMessaging(app);

                // Create FCM message
                var message = new FirebaseAdmin.Messaging.Message()
                {
                    Token = token,
                    Notification = new FirebaseAdmin.Messaging.Notification()
                    {
                        Title = notification.Title,
                        Body = notification.MessageBody
                    }
                };

                // Send message using Firebase Admin SDK
                var response = await messaging.SendAsync(message);
                return !string.IsNullOrEmpty(response);
            }
            catch (FirebaseAdmin.Messaging.FirebaseMessagingException ex)
            {
                // Handle specific Firebase messaging errors
                if (ex.MessagingErrorCode == FirebaseAdmin.Messaging.MessagingErrorCode.Unregistered)
                {
                    // Token is invalid/unregistered - this is expected and should not be logged as error
                    return false;
                }
                else
                {
                    _logger.Error(ex, $"Firebase messaging error: {ex.MessagingErrorCode}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Exception in SendWebNotificationAsync: {ex.Message}");
                return false;
            }
        }

        private bool IsValidFCMToken(string token)
        {
            if (string.IsNullOrEmpty(token))
                return false;

            // Basic FCM token validation
            // FCM tokens are typically 152+ characters long and contain specific patterns
            if (token.Length < 140)
                return false;

            // FCM tokens contain colons and specific character patterns
            if (!token.Contains(":"))
                return false;

            return true;
        }

        private FirebaseApp InitializeFirebaseApp()
        {
            // Check if web app already exists
            var existingApp = FirebaseApp.GetInstance("web-app");
            if (existingApp != null)
            {
                return existingApp;
            }

            // App doesn't exist, create it
            var firebaseFile = JsonConvert.SerializeObject(_firebaseSetting);
            using var memoryStream = new MemoryStream(Encoding.UTF8.GetBytes(firebaseFile));

            var credential = GoogleCredential.FromStream(memoryStream);
            var options = new AppOptions()
            {
                Credential = credential,
                ProjectId = _firebaseSetting.project_id
            };

            return FirebaseApp.Create(options, "web-app");
        }

        private FirebaseApp InitializeMobileFirebaseApp()
        {
            // Check if mobile app already exists
            var existingApp = FirebaseApp.GetInstance("mobile-app");
            if (existingApp != null)
            {
                return existingApp;
            }

            // App doesn't exist, create it
            var firebaseFile = JsonConvert.SerializeObject(_mobileFirebaseSetting);
            using var memoryStream = new MemoryStream(Encoding.UTF8.GetBytes(firebaseFile));

            var credential = GoogleCredential.FromStream(memoryStream);
            var options = new AppOptions()
            {
                Credential = credential,
                ProjectId = _mobileFirebaseSetting.project_id
            };

            return FirebaseApp.Create(options, "mobile-app");
        }

        public async Task<bool> SendCallNotificationAsync(NotificationDTO notification, string token)
        {
            try
            {
                if (string.IsNullOrEmpty(notification?.MessageBody))
                {
                    return false;
                }

                if (string.IsNullOrEmpty(token))
                {
                    return false;
                }

                // Validate token before sending notification
                if (!IsValidFCMToken(token))
                {
                    return false;
                }

                // Initialize Mobile Firebase Admin SDK
                var app = InitializeMobileFirebaseApp();
                var messaging = FirebaseAdmin.Messaging.FirebaseMessaging.GetMessaging(app);

                // Create FCM message for mobile/call notification
                var message = new FirebaseAdmin.Messaging.Message()
                {
                    Token = token,
                    Notification = new FirebaseAdmin.Messaging.Notification()
                    {
                        Title = notification.Title,
                        Body = notification.MessageBody
                    },
                    Data = new Dictionary<string, string>
                    {
                        ["title"] = notification.Title ?? "",
                        ["message"] = notification.MessageBody ?? "",
                        ["imageIconUrl"] = "https://leadrat-resources-blob.s3.ap-south-1.amazonaws.com/images/ic_launcher_round.png",
                        ["imageSmallIconUrl"] = "https://leadrat-resources-blob.s3.ap-south-1.amazonaws.com/images/ic_launcher_round.png",
                        ["deeplinkUrl"] = notification.FCMDeepLinkUrl ?? "",
                        ["NotificationUniqueId"] = notification.Id.ToString()
                    }
                };

                // Send message using Firebase Admin SDK
                var response = await messaging.SendAsync(message);
                return !string.IsNullOrEmpty(response);
            }
            catch (FirebaseAdmin.Messaging.FirebaseMessagingException ex)
            {
                // Handle specific Firebase messaging errors
                if (ex.MessagingErrorCode == FirebaseAdmin.Messaging.MessagingErrorCode.Unregistered)
                {
                    // Token is invalid/unregistered - this is expected and should not be logged as error
                    return false;
                }
                else
                {
                    _logger.Error(ex, $"Firebase messaging error in SendCallNotificationAsync: {ex.MessagingErrorCode}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Exception in SendCallNotificationAsync: {ex.Message}");
                return false;
            }
        }


        //public async Task<bool> SendWebNotificationAsync(NotificationDTO notification, string token)
        //{
        //    try
        //    {
        //        _logger.Information($"NotificationService::SendWebNotificationAsync() called, NotificationDTO: {JsonConvert.SerializeObject(notification)}");
        //        Console.WriteLine("CreateNotificationAsync====");

        //        bool isBodyAvailable = false;

        //        if (!string.IsNullOrEmpty(notification?.MessageBody))
        //            isBodyAvailable = true;

        //        if (!isBodyAvailable)
        //        {
        //            throw new InvalidOperationException(NotificationMessageBodyAndRawContentExist);
        //        }
        //        var baseUrl = $"https://fcm.googleapis.com/v1/projects/{_firebaseSetting.project_id}/messages:send";
        //        var acessToken = await GeAccessTokenAsync();
        //        _logger.Information($"NotificationService::SendWebNotificationAsync():: Access Token: {acessToken}");
        //        RestClient client = new RestClient(baseUrl);
        //        var request = new RestRequest();
        //        request.AddHeader("Content-Type", "application/json");
        //        request.AddHeader("Authorization", $"Bearer {acessToken}");
        //        var payload = CreateWebNotificationJsonBody(notification ?? new(), token);
        //        request.AddBody(payload);
        //        RestResponse response = new();
        //        try
        //        {
        //            _logger.Information($"NotificationService::SendWebNotificationAsync() calling, NotificationDTO: {JsonConvert.SerializeObject(request)}");
        //            response = await client.ExecuteAsync(request);
        //            _logger.Information($"NotificationService::SendWebNotificationAsync() called, NotificationDTO: {JsonConvert.SerializeObject(response)}");
        //        }
        //        catch (Exception ex)
        //        {
        //            _logger.Information($"NotificationService::SendWebNotificationAsync() Exception while calling, NotificationDTO: {JsonConvert.SerializeObject(response)}");
        //        }

        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.Information($"NotificationService::SendWebNotificationAsync() Exception while calling, NotificationDTO: {JsonConvert.SerializeObject(notification)}");
        //    }
        //    return true;
        //}

        private string CreateWebNotificationJsonBody(NotificationDTO notification, string token)
        {
            var messageBody = new WebNotificationMessageBody()
            {
                token = token,
            };
            messageBody.notification = new()
            {
                body = notification.MessageBody,
                title = notification.Title
            };
            var notificationRequest = new WebNotificationRequestDto()
            {
                message = messageBody,
            };
            var payload = JsonConvert.SerializeObject(notificationRequest);
            return payload ?? string.Empty;
        }

        private async Task<string> GeAccessTokenAsync()
        {
            var firebaseFile = JsonConvert.SerializeObject(_firebaseSetting);
            using var memoryStream = new MemoryStream(Encoding.UTF8.GetBytes(firebaseFile));
            GoogleCredential googleCredential = GoogleCredential.FromStream(memoryStream)
            .CreateScoped(new[] { "https://www.googleapis.com/auth/cloud-platform" });
            var token = await googleCredential.UnderlyingCredential.GetAccessTokenForRequestAsync();
            return token;
        }

        #region Send Call  Notification
        //public async Task<bool> SendCallNotificationAsync(NotificationDTO notification, string token)
        //{
        //    try
        //    {
        //        bool isBodyAvailable = false;

        //        if (!string.IsNullOrEmpty(notification?.MessageBody))
        //            isBodyAvailable = true;

        //        if (!isBodyAvailable)
        //        {
        //            throw new InvalidOperationException(NotificationMessageBodyAndRawContentExist);
        //        }
        //        var baseUrl = $"https://fcm.googleapis.com/v1/projects/{_mobileFirebaseSetting.project_id}/messages:send";
        //        var acessToken = await GetMobileAccessTokenAsync();

        //        RestClient client = new RestClient(baseUrl);
        //        var request = new RestRequest();
        //        request.AddHeader("Content-Type", "application/json");
        //        request.AddHeader("Authorization", $"Bearer {acessToken}");

        //        var payload = CreateMobileNotificationJsonBody(notification ?? new(), token);
        //        request.AddBody(payload);
        //        RestResponse response = new();

        //        try
        //        {
        //            _logger.Information($"NotificationService::SendCallNotificationAsync() calling, NotificationDTO: {JsonConvert.SerializeObject(request)}");
        //            response = await client.PostAsync(request);
        //            _logger.Information($"NotificationService::SendCallNotificationAsync() called, NotificationDTO: {JsonConvert.SerializeObject(response)}");
        //        }
        //        catch (Exception ex)
        //        {
        //            _logger.Information($"NotificationService::SendCallNotificationAsync() Exception while calling, NotificationDTO: {JsonConvert.SerializeObject(response)}");
        //        }
        //    }
        //    catch(Exception ex)
        //    {
        //        _logger.Information($"NotificationService::SendCallNotificationAsync() Exception while calling, NotificationDTO: {JsonConvert.SerializeObject(notification)}");
        //    }
        //    return true;
        //}

        #region Access Token
        private async Task<string> GetMobileAccessTokenAsync()
        {
            var firebaseFile = JsonConvert.SerializeObject(_mobileFirebaseSetting);
            using var memoryStream = new MemoryStream(Encoding.UTF8.GetBytes(firebaseFile));
            GoogleCredential googleCredential = GoogleCredential.FromStream(memoryStream)
            .CreateScoped(new[] { "https://www.googleapis.com/auth/cloud-platform" });
            var token = await googleCredential.UnderlyingCredential.GetAccessTokenForRequestAsync();
            return token;
        }
        #endregion

        #region Payload
        private string CreateMobileNotificationJsonBody(NotificationDTO notification, string token)
        {
            var messageBody = new NotificationBodyPayload()
            {
                token = token,
            };
            messageBody.notification = new()
            {
                body = notification.MessageBody,
                title = notification.Title,
            };
            messageBody.data = new()
            {
                title = notification.Title,
                message = notification.MessageBody,
                imageIconUrl = "https://leadrat-resources-blob.s3.ap-south-1.amazonaws.com/images/ic_launcher_round.png",
                imageSmallIconUrl = "https://leadrat-resources-blob.s3.ap-south-1.amazonaws.com/images/ic_launcher_round.png",
                imageUrl = null,
                image = null,
                deeplinkUrl = notification.FCMDeepLinkUrl,
                NotificationUniqueId = notification.Id
            };

            var notificationRequest = new NotificationPayloadDto()
            {
                message = messageBody,
            };
            var payload = JsonConvert.SerializeObject(notificationRequest);
            return payload ?? string.Empty;
        }
        #endregion

        #endregion


        //private SegmentGroupList CreateSegmentGroupListExcludingUsers(Notification notification, string importSegmentId)
        //{
        //    Console.WriteLine("CreateSegmentGroupList");

        //    var segmentGroupList = new SegmentGroupList()
        //    {
        //        Groups = new List<SegmentGroup>(),
        //        Include = Include.ANY
        //    };

        //    //CHANNEL AND PLATFORM
        //    var segmentGroup = new SegmentGroup()
        //    {
        //        Type = Amazon.Pinpoint.Type.ALL,
        //        Dimensions = new List<SegmentDimensions>()
        //    };

        //    var channel = new SetDimension
        //    {
        //        DimensionType = DimensionType.INCLUSIVE,
        //        Values = new List<string>()
        //    };

        //    if (notification.IsIOSNotification)
        //    {
        //        channel.Values.Add("APNS");
        //        channel.Values.Add("APNS_SANDBOX");
        //    }

        //    if (notification.IsAndroidNotification)
        //    {
        //        channel.Values.Add("GCM");
        //    }

        //    var platform = new SetDimension
        //    {
        //        DimensionType = DimensionType.INCLUSIVE,
        //        Values = GetPlatformValues(notification)
        //    };

        //    //APP VERSIONS
        //    var appVersionSegmentGroup = new SegmentGroup()
        //    {
        //        Type = Amazon.Pinpoint.Type.ALL,
        //        Dimensions = new List<SegmentDimensions>()
        //    };

        //    var appVersions = GetAppVersionValues(notification);

        //    if (appVersions != null && appVersions.Count > 0)
        //    {
        //        appVersions = appVersions.Where(s => !string.IsNullOrWhiteSpace(s)).Distinct().ToList();
        //        var appVersionDimension = new SegmentDimensions()
        //        {
        //            Demographic = new SegmentDemographics()
        //            {
        //                Channel = channel,
        //                Platform = platform,
        //                AppVersion = new SetDimension()
        //                {
        //                    DimensionType = DimensionType.INCLUSIVE,
        //                    Values = appVersions
        //                }
        //            }
        //        };
        //        appVersionSegmentGroup.Dimensions.Add(appVersionDimension);
        //        segmentGroupList.Groups.Add(appVersionSegmentGroup);
        //    }

        //    //CUSTOM ATTRIBUTES
        //    var customAttributeSegmentGroup = new SegmentGroup()
        //    {
        //        Type = Amazon.Pinpoint.Type.ALL,
        //        Dimensions = new List<SegmentDimensions>()
        //    };

        //    var customAttributes = PrepareSegmentAttributes(notification);
        //    if (customAttributes != null && customAttributes.Count > 0)
        //    {
        //        var customAttribDimension = new SegmentDimensions()
        //        {
        //            Demographic = new SegmentDemographics()
        //            {
        //                Channel = channel,
        //                Platform = platform
        //            },
        //            Attributes = customAttributes
        //        };
        //        customAttributeSegmentGroup.Dimensions.Add(customAttribDimension);
        //        segmentGroupList.Groups.Add(customAttributeSegmentGroup);
        //    }

        //    //Create Base Segment : Users

        //    var userSegmentGrp = new SegmentGroup()
        //    {
        //        Type = Amazon.Pinpoint.Type.ALL,
        //        Dimensions = new List<SegmentDimensions>(),
        //        SourceSegments = new List<SegmentReference>(),
        //        SourceType = SourceType.ANY
        //    };

        //    //Add Source Segment
        //    userSegmentGrp.SourceSegments.Add(new SegmentReference()
        //    {
        //        Id = importSegmentId
        //    });

        //    userSegmentGrp.Dimensions.Add(new SegmentDimensions()
        //    {
        //        Demographic = new SegmentDemographics()
        //        {
        //            Channel = channel,
        //            Platform = platform
        //        }
        //    });

        //    segmentGroupList.Groups.Add(userSegmentGrp);

        //    Console.WriteLine(string.Format("segmentGroupList==={0}", JsonConvert.SerializeObject(segmentGroupList)));
        //    return segmentGroupList;
        //}
        private UpdateSegmentRequest UpdateCustomSegmentRequest(NotificationDTO notification, string segmentId)
        {
            return new UpdateSegmentRequest()
            {
                SegmentId = segmentId,
                ApplicationId = _settings.PinpointApplicationId,
                WriteSegmentRequest = new WriteSegmentRequest()
                {
                    SegmentGroups = CreateSegmentGroupList(notification),
                }
            };
        }
        private CreateSegmentRequest CreateBroadcastSegmentRequest(NotificationDTO notification)
        {
            var channel = new SetDimension
            {
                DimensionType = DimensionType.INCLUSIVE,
                Values = new List<string>()
            };

            if (notification.IsIOSNotification)
            {
                channel.Values.Add("APNS");
                channel.Values.Add("APNS_SANDBOX");
            }

            if (notification.IsAndroidNotification)
            {
                channel.Values.Add("GCM");
            }
            return new CreateSegmentRequest()
            {
                ApplicationId = _settings.PinpointApplicationId,
                WriteSegmentRequest = new WriteSegmentRequest()
                {
                    Name = Guid.NewGuid().ToString(),
                    Dimensions = new SegmentDimensions()
                    {
                        Attributes = PrepareSegmentAttributesForBroadcast(notification),
                        Demographic = new SegmentDemographics()
                        {
                            Channel = channel,
                            Platform = new SetDimension()
                            {
                                DimensionType = DimensionType.INCLUSIVE,
                                Values = GetPlatformValues(notification)
                            }
                        }
                    }
                }
            };
        }

        private Dictionary<string, AttributeDimension> PrepareSegmentUserAttributes(NotificationDTO notification)
        {
            Dictionary<string, AttributeDimension> attributes = new Dictionary<string, AttributeDimension>();

            //var topics = notification.Topics.Where(i => i.TopicType == TopicType.User && !string.IsNullOrWhiteSpace(i.TopicValue)).GroupBy(j => j.TopicType);
            //foreach (var item in topics)
            //{
            //    string attributeKey = GetTopicKeyFromCategory(item.Key);
            //    if (attributes.ContainsKey(attributeKey))
            //    {
            //        attributes[attributeKey].Values.AddRange(item.Select(i => i.TopicValue).ToList());
            //    }
            //    else
            //    {
            //        attributes.Add(attributeKey, new AttributeDimension()
            //        {
            //            AttributeType = AttributeType.INCLUSIVE,
            //            Values = item.Select(i => i.TopicValue).ToList()
            //        });
            //    }
            //}
            string attributeKey = GetTopicKeyFromCategory(TopicType.User);
            if (attributes.ContainsKey(attributeKey))
            {
                //attributes[attributeKey].Values.Add(notification.UserId.ToString());
                if (notification.UserIds?.Any() ?? false)
                {
                    var userIds = notification.UserIds?.Select(i => i.ToString()).ToList();
                    attributes[attributeKey].Values.AddRange(userIds ?? new());
                }
                else
                {
                    attributes[attributeKey].Values.Add(notification.UserId.ToString());
                }
            }
            else
            {
                if (notification.UserIds?.Any() ?? false)
                {
                    var userIds = notification.UserIds?.Select(i => i.ToString()).ToList();
                    attributes.Add(attributeKey, new AttributeDimension()
                    {
                        AttributeType = AttributeType.INCLUSIVE,
                        //Values = new List<string>() { notification.UserId.ToString() }
                        Values = userIds
                    });
                }
                else
                {
                    attributes.Add(attributeKey, new AttributeDimension()
                    {
                        AttributeType = AttributeType.INCLUSIVE,
                        Values = new List<string>() { notification.UserId.ToString() }
                    });
                }
            }
            if (notification.Topics?.Any() ?? false)
            {
                string customTopicAttributeKey = GetTopicKeyFromCategory(TopicType.CustomTopic);
                if (attributes.ContainsKey(customTopicAttributeKey))
                {
                    attributes[customTopicAttributeKey].Values.AddRange(notification.Topics);
                }
                else
                {
                    attributes.Add(customTopicAttributeKey, new AttributeDimension()
                    {
                        AttributeType = AttributeType.INCLUSIVE,
                        Values = notification.Topics
                    });
                }
            }
            return attributes;
        }

        private Dictionary<string, AttributeDimension> PrepareSegmentAttributes(NotificationDTO notification)
        {
            Dictionary<string, AttributeDimension> attributes = new Dictionary<string, AttributeDimension>();

            if (notification.Topics?.Any() ?? false)
            {
                string customTopicAttributeKey = GetTopicKeyFromCategory(TopicType.CustomTopic);
                if (attributes.ContainsKey(customTopicAttributeKey))
                {
                    attributes[customTopicAttributeKey].Values.AddRange(notification.Topics);
                }
                else
                {
                    attributes.Add(customTopicAttributeKey, new AttributeDimension()
                    {
                        AttributeType = AttributeType.INCLUSIVE,
                        Values = notification.Topics
                    });
                }
            }

            return attributes;
        }
        private Dictionary<string, AttributeDimension> PrepareSegmentAttributesForBroadcast(NotificationDTO notification)
        {
            Dictionary<string, AttributeDimension> attributes = new Dictionary<string, AttributeDimension>
                {
                    {
                        TopicKeys.APP_TOKEN_ATTRIBUTE_KEY,
                        new AttributeDimension()
                        {
                            AttributeType = AttributeType.INCLUSIVE,
                            Values = new List<string>() { AppToken }
                        }
                    }
                };
            return attributes;
        }
        private string GetTopicKeyFromCategory(TopicType category)
        {
            switch (category)
            {
                case TopicType.Default:
                    return TopicKeys.TOPIC_ATTRIBUTE_KEY;
                case TopicType.Version:
                    return TopicKeys.VERSION_ATTRIBUTE_KEY;
                case TopicType.CustomTopic:
                    return TopicKeys.TOPIC_ATTRIBUTE_KEY;
                case TopicType.User:
                    return TopicKeys.USERID_ATTRIBUTE_KEY;
                default:
                    return TopicKeys.TOPIC_ATTRIBUTE_KEY;
            }
        }
        private List<string> GetPlatformValues(NotificationDTO notification)
        {
            List<string> platformValues = new List<string>();
            if (notification.IsAndroidNotification)
            {
                platformValues.Add(TopicValues.PLATFROM_ANDROID);
                platformValues.Add(TopicValues.PLATFROM_iOS);
            }
            if (notification.IsIOSNotification)
                platformValues.Add(TopicValues.PLATFROM_iOS);
            return platformValues;
        }
        //private List<string> GetAppVersionValues(Notification notification)
        //{
        //    Dictionary<string, AttributeDimension> attributes = new Dictionary<string, AttributeDimension>();

        //    var topics = notification.Topics.Where(i => i.TopicType == TopicType.Version);
        //    if (topics.Any())
        //    {
        //        return topics.Select(i => i.TopicValue).ToList();
        //    }
        //    else
        //    {
        //        return null;
        //    }
        //}
        private Schedule CreateCampaignSchedule(NotificationDTO notification)
        {
            return notification.IsScheduled ? new Schedule()
            {
                Frequency = CampaignConstants.FREQUNCY_ONCE,
                StartTime = notification.ScheduledTime.ToString("u").Replace(" ", "T")//.ToString("yyyy-MM-ddTHH:mm:sszzz")//notification.GetAmzUtcDateStringFromString()//DateTime.UtcNow.AddDays(1).ToString("yyyy-MM-ddTHH:mm:sszzz") //
            } : new Schedule()
            {
                StartTime = CampaignConstants.STARTTIME_IMMEDIATE
            };
        }
        private CreateCampaignRequest CreateCampaign(NotificationDTO notification, string segmentId, List<PushNotificationRecords> records)
        {
            //IsRichMediaNotification
            bool isRichMediaNotification = IsRichMediaNotification(notification);

            Console.WriteLine("Is Rich Media Notification " + isRichMediaNotification.ToString());

            notification.IsMutableContent = isRichMediaNotification;

            //TODO: Setup Mechanism to show images in notification
            //Get Presigned Url for Notification Image
            //string imageUrl = string.Empty;
            //if (!string.IsNullOrEmpty(notification?.NotificationImage.Value))
            //{
            //    imageUrl = GetPresignedUrl(notification.NotificationImage.Value);
            //}

            //Check if rich media notification or not
            //Create Payload Based On it
            Message gCMMessage = GetPinPointGCMMessage(notification, records);
            //Message aPSMessage = GetPinPointAPSMessage(notification);

            var campaignRequest = new CreateCampaignRequest()
            {
                ApplicationId = _settings.PinpointApplicationId,
                WriteCampaignRequest = new WriteCampaignRequest()
                {
                    Name = string.Format("{0}-{1}", notification.Title ?? string.Empty, DateTime.Now.ToShortTimeString()),
                    SegmentId = segmentId,
                    MessageConfiguration = new Amazon.Pinpoint.Model.MessageConfiguration()
                    {
                        //Default Body

                        #region Specific Platform
                        GCMMessage = gCMMessage,
                        //APNSMessage = aPSMessage
                        #endregion
                    },
                    Schedule = CreateCampaignSchedule(notification)
                }
            };
            Console.WriteLine("Campaign Request LOG == " + JsonConvert.SerializeObject(campaignRequest));
            return campaignRequest;
        }
        private UpdateCampaignRequest UpdateCampaign(NotificationDTO editedNotification, string campaignId, string segmentId)
        {
            //IsRichMediaNotification
            bool isRichMediaNotification = IsRichMediaNotification(editedNotification);
            editedNotification.IsMutableContent = isRichMediaNotification;

            //Get Presigned Url for Notification Image
            //string imageUrl = string.Empty;
            //if (!string.IsNullOrEmpty(editedNotification?.NotificationImage.Value))
            //{
            //    imageUrl = GetPresignedUrl(editedNotification.NotificationImage.Value);
            //}

            //Check if rich media notification or not
            //Create Payload Based On it
            Message gCMMessage = GetPinPointGCMMessage(editedNotification);
            //Message aPSMessage = GetPinPointAPSMessage(editedNotification);

            //if (string.IsNullOrEmpty(editedNotification.RawContent))
            //{
            //    aPSMessage = isRichMediaNotification ? GetPinPointAPSMessage(editedNotification, imageUrl) : GetPinPointAPSMessage(editedNotification);
            //    gCMMessage = isRichMediaNotification ? GetPinPointGCMMessage(editedNotification, imageUrl) : GetPinPointGCMMessage(editedNotification);
            //}
            //else
            //{
            //    gCMMessage = editedNotification.IsAndroidNotification ? GetGCMRawMessage(editedNotification.RawContent) : null;
            //    aPSMessage = editedNotification.IsIOSNotification ? GetAPNsRawMessage(editedNotification) : null;
            //}

            var updateCampaignRequest = new UpdateCampaignRequest()
            {
                ApplicationId = _settings.PinpointApplicationId,
                CampaignId = campaignId,
                WriteCampaignRequest = new WriteCampaignRequest()
                {
                    Name = string.Format("{0}-{1}", editedNotification.Title ?? string.Empty, DateTime.Now.ToShortTimeString()),
                    SegmentId = segmentId,
                    MessageConfiguration = new MessageConfiguration()
                    {
                        GCMMessage = gCMMessage,
                        //APNSMessage = aPSMessage
                    },
                    Schedule = CreateCampaignSchedule(editedNotification)
                }
            };
            return updateCampaignRequest;
        }
        private static Message GetPinPointGCMMessage(NotificationDTO notification, string imageUrl)
        {
            if (notification.IsAndroidNotification)
            {
                try
                {
                    var msg = new Message()
                    {
                        Body = notification.MessageBody ?? string.Empty,
                        RawContent = notification?.RawContent ?? string.Empty,
                        Title = notification.Title ?? string.Empty,
                        ImageIconUrl = imageUrl,
                        ImageUrl = imageUrl,
                        ImageSmallIconUrl = imageUrl,
                        MediaUrl = notification?.NotificationSound,
                        SilentPush = false,
                        Action = MessageActionExtention.GetStringValue(notification.Action),
                        Url = notification.Action == MessageAction.DEEP_LINK ? notification.FCMDeepLinkUrl : string.Empty
                    };

                    return new Message()
                    {
                        JsonBody = JsonConvert.SerializeObject(msg) ?? string.Empty
                    };
                }
                catch (Exception ex)
                {
                    Console.WriteLine("PinPointGCMMessage()=except: ");
                    Console.WriteLine(ex);
                }
            }
            return null;
        }

        public async Task<bool> SendWATemplateNotification(string recipientPhoneNumber, WATemplate template, List<string> bodyValues, string? headerValue = null, bool isSavedMessage = false, Guid? leadId = null, string? leadName = null, string? campaignName = null, ViewLeadDto? leadDto = null, Application.Identity.Users.UserDetailsDto? assignUser = null, bool isLeadNotification = false, Guid userId = default)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(recipientPhoneNumber))
                {
                    return false;
                }
                if (string.IsNullOrWhiteSpace(template?.WAApiInfo?.URL))
                {
                    return false;
                }
                string bodyValuesArray = string.Join(",", bodyValues.Select(value => $"\"{value ?? ""}\""));
                var payload = template.WAApiInfo?.JsonPayload;
                if (string.IsNullOrWhiteSpace(payload))
                {
                    return false;
                }

                // Replace payload parameters
                payload = payload.Replace("#LeadFullContactNo#", recipientPhoneNumber)
                  .Replace("#ShortPhoneNumber#", recipientPhoneNumber.Length > 10 ? recipientPhoneNumber[^10..] : recipientPhoneNumber)
                  .Replace("#OnlyPhoneNumber#", Regex.Replace(recipientPhoneNumber, @"\D", ""))
                  .Replace("#LeadContactNo#", recipientPhoneNumber)
                  .Replace("#TemplateName#", template.Name)
                  .Replace("\"#BodyValues#\"", bodyValuesArray)
                  .Replace("#FileName#", "Sample")
                  .Replace("#HeaderValues#", headerValue)
                  .Replace("#MediaUrl#", template.MediaURL)
                  .Replace("#UUID#", Guid.NewGuid().ToString())
                  .Replace("#leadName#", leadName)
                  .Replace("#CampaignName", campaignName)
                  .Replace("#LeadEmail#", leadDto?.Email)
                  .Replace("#LeadNotes#", leadDto?.Notes)
                  .Replace("#LeadSource#", leadDto?.Enquiry?.LeadSource.ToString())
                  .Replace("#LeadSourceEnum#", ((int?)leadDto?.Enquiry?.LeadSource)?.ToString())
                  .Replace("#AssignedTo#", $"{assignUser?.FirstName ?? ""} {assignUser?.LastName ?? ""}".Trim())
                  .Replace("#AssignedToPhoneNumber#", assignUser?.PhoneNumber)
                  .Replace("#Project#", leadDto?.Projects?.FirstOrDefault()?.Name)
                  .Replace("#LeadStatus#", leadDto?.Status?.DisplayName)
                  .Replace("#LeadSubStatus#", leadDto?.Status?.ChildType?.DisplayName)
                  .Replace("#userPhoneNumber#", assignUser?.PhoneNumber)
                  .Replace("#Schedule Date#", leadDto?.ScheduledDate?.ToString("yyyy-MM-ddTHH:mm:ss", CultureInfo.InvariantCulture))
                  .Replace("#JsonBody#",template.JsonBody);

                if (!IsValidJson(payload)) { return false; }
                var url = template?.WAApiInfo?.URL;
                var method = template?.WAApiInfo?.MethodType?.ToUpper() == "GET" ? Method.Get : Method.Post;

                if (method == Method.Get && url.Contains("#"))
                {
                    url = url.Replace("#LeadFullContactNo#", Regex.Replace(recipientPhoneNumber ?? "", "[^0-9]", ""))
                        .Replace("#ShortPhoneNumber#", Regex.Replace(recipientPhoneNumber?.Length > 10 ? recipientPhoneNumber[^10..] : recipientPhoneNumber ?? "", "[^0-9]", ""))
                        .Replace("#OnlyPhoneNumber#", Regex.Replace(recipientPhoneNumber ?? "", "[^0-9]", ""))
                        .Replace("#LeadContactNo#", Regex.Replace(recipientPhoneNumber ?? "", "[^0-9]", ""))
                        .Replace("#TemplateName#", Uri.EscapeDataString(template?.Name ?? ""))
                        .Replace("#leadName#", Uri.EscapeDataString(leadName ?? ""))
                        .Replace("#AssignedTo#", Uri.EscapeDataString($"{assignUser?.FirstName ?? ""} {assignUser?.LastName ?? ""}".Trim()))
                        .Replace("#AssignedToPhoneNumber#", Regex.Replace(assignUser?.PhoneNumber ?? "", "[^0-9]", ""));
                }

                var request = new RestRequest(url, method);
                if (template?.WAApiInfo?.Headers?.Any() ?? false)
                {
                    foreach (var item in template.WAApiInfo.Headers)
                    {
                        request.AddHeader(item.Key, item.Value);
                    }
                }
                if (method == Method.Post)
                {
                    request.AddJsonBody(payload ?? string.Empty);
                }
                var response = await _restClient.ExecuteAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var waPayload = (await _waPayloadMappingRepo.ListAsync()).FirstOrDefault();
                    var mappedKey = waPayload?.ResponseMapping?.FirstOrDefault(x => x.Key == "#MessageId#").Value;
                    if (mappedKey != null)
                    {
                        var resDict = JTokenHelper.GetWithArrayKeysAndValues(JObject.Parse(response.Content ?? string.Empty));
                        var message = template.Adapt<WAMessage>();
                        message.TemplateName = template.Name;
                        message.Message = !string.IsNullOrEmpty(template.MediaType) &&
                                         template.MediaType.ToLowerInvariant().Contains("text")
                            ? string.Join("\n", new[] { template.Header, template.Message, template.Footer }
                                .Where(part => !string.IsNullOrEmpty(part)))
                            : template.Message;

                        message.CustomerNo = recipientPhoneNumber;
                        message.MessageId = resDict.TryGetValue(mappedKey, out var result) ? result : null;
                        message.WAEvent = WAEvent.Sent;

                        if (isLeadNotification)
                        {
                            message.CustomerId = leadId;
                            message.UserId = null;
                        }
                        else
                        {
                            message.CustomerId = null;
                            message.UserId = userId;
                        }
                        await _waMessage.AddAsync(message);
                    }
                    return response.IsSuccessStatusCode;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"SendWATemplateNotification->Error: {ex}");
                return false;
            }
        }
        private bool IsValidJson(string str)
        {
            try
            {
                JObject.Parse(str);
                return true;
            }
            catch
            {
                return false;
            }
        }

        //Simple Notification Payload

        private static Message GetPinPointGCMMessage(NotificationDTO notification, List<PushNotificationRecords> records = default)
        {
            if (notification.IsAndroidNotification)
            {
                try
                {
                    //var msg = new Message()
                    //{
                    //    Body = notification.MessageBody ?? string.Empty,
                    //    RawContent = notification?.RawContent ?? string.Empty,
                    //    Title = notification.Title ?? string.Empty,
                    //    SilentPush = false,
                    //    Action = MessageActionExtention.GetStringValue(notification.Action),
                    //    Url = notification.Action == MessageAction.DEEP_LINK ? notification.FCMDeepLinkUrl : string.Empty
                    //};
                    //return new Message()
                    //{
                    //    JsonBody = JsonConvert.SerializeObject(msg) ?? string.Empty
                    //};
                    return new Message()
                    {
                        //Body = notification.MessageBody ?? string.Empty,
                        //Title = notification.Title ?? string.Empty,
                        //SilentPush = false,
                        Action = (string.IsNullOrEmpty(notification.FCMDeepLinkUrl) && string.IsNullOrEmpty(notification.OpenInBrowserUrl)) ? "OPEN_APP" : MessageActionExtention.GetStringValue(notification.Action),
                        Url = notification.Action == MessageAction.DEEP_LINK ? notification.FCMDeepLinkUrl : notification.Action == MessageAction.URL ? notification.OpenInBrowserUrl : string.Empty,
                        RawContent = JsonConvert.SerializeObject(new Payload.Root
                        {
                            data = new Payload.Data()
                            {
                                message = notification.MessageBody ?? string.Empty,
                                title = notification.Title,
                                imageIconUrl = "https://leadrat-resources-blob.s3.ap-south-1.amazonaws.com/images/ic_launcher_round.png",
                                imageSmallIconUrl = "https://leadrat-resources-blob.s3.ap-south-1.amazonaws.com/images/ic_launcher_round.png",
                                imageUrl = notification.NotificationImage,
                                image = notification.NotificationImage,
                                deeplinkUrl = notification.FCMDeepLinkUrl,
                                NotificationUniqueId = notification.UniqueId
                            },
                            mutable_content = 1,
                            priority = "high",
                            notification = new Payload.Notification()
                            {
                                title = notification.Title,
                                body = notification.MessageBody,
                                image = notification.NotificationImage,
                                icon = "ic_logo_small", // Fetching from android resource
                                link = notification.FCMDeepLinkUrl,
                                sound = "default",
                                NotificationUniqueId = notification.UniqueId
                            },
                            NotificationUniqueId = notification.UniqueId
                        }),
                    };

                }
                catch (Exception ex)
                {
                    Console.WriteLine("PinPointGCMMessage()=except: ");
                    Console.WriteLine(ex);
                }
            }
            return null;
        }

        public async Task SendLeadUpdateToEngageto<T>(T entity, CancellationToken cancellationToken)
        {
            try
            {
                if (entity is Lead lead)
                {
                    var viewLeadDto = lead.Adapt<ViewLeadDto>();

                    UserDetailsDto? assignUser = null;
                    if (lead.AssignTo != Guid.Empty)
                    {
                        assignUser = (await _userService
                            .GetListOfUsersByIdsAsync(new List<string> { lead.AssignTo.ToString() }, cancellationToken)).FirstOrDefault();
                    }

                    var WaApiInfo = await _waApiInfo.FirstOrDefaultAsync(new GetWAApiInfoByWaApiActionSpec(WAApiAction.SendLeadToEngageto), cancellationToken);
                    if (WaApiInfo != null)
                    {
                        var payload = WaApiInfo.JsonPayload;
                        if (string.IsNullOrWhiteSpace(payload))
                        {
                            return;
                        }
                        payload = payload.Replace("#LeadFullContactNo#", viewLeadDto.ContactNo)
                                 .Replace("#ShortPhoneNumber#", viewLeadDto.ContactNo.Length > 10 ? viewLeadDto.ContactNo[^10..] : viewLeadDto.ContactNo)
                                 .Replace("#OnlyPhoneNumber#", Regex.Replace(viewLeadDto.ContactNo, @"\D", ""))
                                 .Replace("#LeadContactNo#", viewLeadDto.ContactNo)
                                 .Replace("#FileName#", "Sample")
                                 .Replace("#UUID#", Guid.NewGuid().ToString())
                                 .Replace("#leadName#", viewLeadDto.Name)
                                 .Replace("#LeadEmail#", viewLeadDto?.Email)
                                 .Replace("#LeadNotes#", viewLeadDto?.Notes)
                                 .Replace("#LeadSource#", viewLeadDto?.Enquiry?.LeadSource.ToString())
                                 .Replace("#LeadSourceEnum#", ((int?)viewLeadDto?.Enquiry?.LeadSource)?.ToString())
                                 .Replace("#AssignedTo#", $"{assignUser?.FirstName ?? ""} {assignUser?.LastName ?? ""}".Trim())
                                 .Replace("#AssignedToPhoneNumber#", assignUser?.PhoneNumber)
                                 .Replace("#Project#", viewLeadDto?.Projects?.FirstOrDefault()?.Name)
                                 .Replace("#LeadStatus#", viewLeadDto?.Status?.DisplayName)
                                 .Replace("#LeadSubStatus#", viewLeadDto?.Status?.ChildType?.DisplayName)
                                 .Replace("#userPhoneNumber#", assignUser?.PhoneNumber)
                                 .Replace("#Schedule Date#", viewLeadDto?.ScheduledDate?.ToString("yyyy-MM-ddTHH:mm:ss", CultureInfo.InvariantCulture));

                        if (!IsValidJson(payload)) { return; }
                        var request = new RestRequest(WaApiInfo?.URL, Method.Post);
                        if (WaApiInfo?.Headers?.Any() ?? false)
                        {
                            foreach (var item in WaApiInfo.Headers)
                            {
                                request.AddHeader(item.Key, item.Value);
                            }
                        }
                        request.AddJsonBody(payload ?? string.Empty);
                        var response = await _restClient.ExecuteAsync(request);
                    }
                }
            }
            catch (Exception ex)
            {
                return;
            }
        }

        private static Message GetGCMRawMessage(string rawContent)
        {
            return new Message()
            {
                RawContent = JsonConvert.SerializeObject(new GCMRawTemplate()
                {
                    Data = new Data()
                    {
                        Message = rawContent
                    }
                }
                , new JsonSerializerSettings()
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                }),
                Action = Amazon.Pinpoint.Action.OPEN_APP,
                SilentPush = false
            };
        }

        private static Message GetAPNsRawMessage(NotificationDTO notification)
        {
            return new Message()
            {
                RawContent = JsonConvert.SerializeObject(new APNSRawTemplate()
                {
                    Aps = new APS()
                    {
                        Alert = notification.MessageBody
                    },
                    data = JsonConvert.SerializeObject(notification.RawContent)
                }
                , new JsonSerializerSettings()
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                }),
                Action = Amazon.Pinpoint.Action.OPEN_APP,
                SilentPush = false
            };
        }



        private Message GetAPNsRawMessageV3(NotificationDTO notification)
        {
            return new Message()
            {
                RawContent = JsonConvert.SerializeObject(new APNSRawTemplateV3()
                {
                    Title = notification.Title,
                    Body = notification.MessageBody,
                    Data = notification.APNsRawContent,
                    ContentAvailable = notification.IsIOSNotification ? "1" : "0",
                    Action = Amazon.Pinpoint.Action.OPEN_APP,
                    SilentPush = false
                })
            };
        }

        private Message GetGCMRawMessageV3(NotificationDTO notification)
        {
            return new Message()
            {
                RawContent = JsonConvert.SerializeObject(new GCMRawTempalteV3()
                {
                    Title = notification.Title,
                    Body = notification.MessageBody,
                    Data = notification.FCMRawContent
                })
            };
        }

        private static Message GetAPNsRawMessageV2(string apnsRawContent)
        {
            return new Message()
            {
                RawContent = apnsRawContent
            };
        }

        private static Message GetGCMRawMessageV2(string fcmContent)
        {
            return new Message()
            {
                RawContent = fcmContent,
            };
        }

        private static Message GetPinPointAPSMessage(NotificationDTO notification, string imageUrl)
        {
            var message = new Message();
            if (notification.IsIOSNotification)
            {
                try
                {
                    message.Title = notification?.Title ?? string.Empty;
                    message.Body = notification?.MessageBody ?? string.Empty;
                    message.SilentPush = false;
                    message.Action = MessageActionExtention.GetStringValue(notification.Action);
                    message.Url = notification.Action == MessageAction.DEEP_LINK ? notification.APNsDeepLinkUrl : string.Empty;
                    //message.MediaUrl = JsonConvert.SerializeObject(new MediaUri()
                    //{
                    //    Image = imageUrl,
                    //    Sound = string.IsNullOrEmpty(notification?.NotificationSound.Value) ? "default" : notification?.NotificationSound.Value
                    //});
                    message.MediaUrl = imageUrl;
                    return message;
                }
                catch (Exception ex)
                {
                    Console.WriteLine("exception===");
                    Console.WriteLine(ex);
                }
            }
            return null;
        }

        //Simple Notification Payload
        //private static Message GetPinPointAPSMessage(NotificationDTO notification)
        //{
        //    var message = new Message();
        //    if (notification.IsIOSNotification)
        //    {
        //        try
        //        {
        //            message.Title = notification?.Title ?? string.Empty;
        //            message.Body = notification?.MessageBody ?? string.Empty;
        //            message.SilentPush = false;
        //            message.Action = MessageActionExtention.GetStringValue(notification.Action);
        //            message.Url = notification.Action == MessageAction.DEEP_LINK ? notification.APNsDeepLinkUrl : string.Empty;

        //            return message;
        //        }
        //        catch (Exception ex)
        //        {
        //            Console.WriteLine("exception===");
        //            Console.WriteLine(ex);
        //        }
        //    }
        //    return null;
        //}

        private bool IsRichMediaNotification(NotificationDTO notification)
        {
            return string.IsNullOrEmpty(notification.NotificationImage) && string.IsNullOrEmpty(notification.NotificationSound);
        }

        //private string GetPresignedUrl(string object_key)
        //{
        //    return _awsS3Service.GetPresignedUrl(object_key);
        //}

        private async Task<bool> UpdatePinpointEndpoint(Device device)
        {
            if (device.PinpointApplicationId != null)
            {
                try
                {
                    Amazon.Pinpoint.ChannelType channelType = device.Platform == Lrb.Domain.Enums.Platform.Android ? Amazon.Pinpoint.ChannelType.GCM : Amazon.Pinpoint.ChannelType.APNS;

                    var pinpointPayload = new Amazon.Pinpoint.Model.UpdateEndpointRequest()
                    {
                        ApplicationId = device.PinpointApplicationId,
                        EndpointId = device.Id.ToString(),
                        EndpointRequest = new Amazon.Pinpoint.Model.EndpointRequest()
                        {
                            ChannelType = channelType,
                            Address = device.NewNotificationToken,
                            Attributes = new Dictionary<string, List<string>>
                            {
                                { TopicKeys.TOPIC_ATTRIBUTE_KEY, JsonConvert.DeserializeObject<List<string>>(device.Topics) },
                                { TopicKeys.APP_TOKEN_ATTRIBUTE_KEY, new List<string>() { device.ApplicationTokenValue } },
                            },
                            Demographic = new Amazon.Pinpoint.Model.EndpointDemographic()
                            {
                                AppVersion = device.AppVersion,
                                Make = device.DeviceModel,
                                Platform = device.Platform == Lrb.Domain.Enums.Platform.iOS ? "ios" : "android",
                                Model = device.DeviceModel
                            },
                            User = new Amazon.Pinpoint.Model.EndpointUser()
                            {
                                UserId = device.UserId.ToString(),
                                UserAttributes = new Dictionary<string, List<string>>()
                            {
                                { TopicKeys.EMAIL_ATTRIBUTE_KEY, new List<string>()
                                {
                                    { device.Email }
                                } },
                                { TopicKeys.USERID_ATTRIBUTE_KEY, new List<string>()
                                {
                                    { device.UserId.ToString() }
                                } },
                                { TopicKeys.USERNAME_ATTRIBUTE_KEY, new List<string>()
                                {
                                    { device.UserName }
                                } }
                            }
                            },
                            EndpointStatus = device.IsActive ? DeviceEndpoint.ENDPOINT_ACTIVE : DeviceEndpoint.ENDPOINT_INACTIVE,
                            OptOut = device.IsActive ? PipnpointOptOut.OPTOUT_NONE_ATTRIBUTE_VALUE : PipnpointOptOut.OPTOUT_ALL_ATTRIBUTE_VALUE

                        }
                    };
                    Console.WriteLine("Update PinPoint Endpoint Payload:" + JsonConvert.SerializeObject(pinpointPayload));

                    var endPointSuccess = await _pinpointService.UpdateEndpointAsync(pinpointPayload);
                    Console.WriteLine("Update PinPoint Endpoint Response:" + JsonConvert.SerializeObject(endPointSuccess));
                    if (endPointSuccess.HttpStatusCode == HttpStatusCode.OK)
                        return true;
                    else
                        return true;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error occure while updating security group in pipoint endpoint, Exception: {ex.ToString()}");
                    throw ex;
                }
            }
            return false;
        }
        private int GetPlatformAttributeCount(NotificationDTO notification)
        {
            int count = 0;

            count += GetPlatformValues(notification).Count;

            return count;
        }

        private int GetChannelAttributeCount(NotificationDTO notification)
        {
            int count = 0;

            //Check Channel Count
            if (notification.IsAndroidNotification)
            {
                //GCM
                count++;
            }

            if (notification.IsIOSNotification)
            {
                //APNS and APNS_SNADBOX
                count += 2;
            }

            return count;
        }

        //private int GetDemographicsAttributeCount(Notification notification)
        //{
        //    int count = 0;

        //    //Check Channel Count
        //    count += GetChannelAttributeCount(notification);

        //    //Check Platform Count
        //    count += GetPlatformAttributeCount(notification);

        //    //App version
        //    count += (notification.Topics?.Count(i => i?.TopicType == TopicType.Version && i?.TopicValue?.Length > 0) ?? 0);

        //    return count;
        //}

        //private int GetCustomAttributeDimensionCount(Notification notification)
        //{
        //    int count = 0;

        //    //Check Channel Count
        //    count += GetChannelAttributeCount(notification);

        //    //Check Platform Count
        //    count += GetPlatformAttributeCount(notification);

        //    //Check for Custom topics
        //    count += (notification.Topics?.Count(i => i?.TopicType == TopicType.CustomTopic && i?.TopicValue?.Length > 0) ?? 0);

        //    return count;
        //}

        //private int GetUserAttributeDimensionCount(Notification notification)
        //{
        //    int count = 0;

        //    //Check Channel Count
        //    count += GetChannelAttributeCount(notification);

        //    //Check Platform Count
        //    count += GetPlatformAttributeCount(notification);

        //    //Check for UserEmails (UserID)
        //    count += (notification.Topics?.Count(i => i?.TopicType == TopicType.User && i?.TopicValue?.Length > 0) ?? 0);

        //    return count;
        //}

        //private void NotificationBatch(Notification notification)
        //{
        //    int demographicsAttribCount = GetDemographicsAttributeCount(notification);
        //    int userAttribCount = GetUserAttributeDimensionCount(notification);
        //    int customsAttribCount = GetCustomAttributeDimensionCount(notification);

        //    int totalCount = demographicsAttribCount + userAttribCount + customsAttribCount;

        //    if (totalCount > 99)
        //    {
        //        //AppVersion
        //        if (demographicsAttribCount < 100)
        //        {
        //            /// Do Normal
        //        }
        //        else
        //        {
        //            while (demographicsAttribCount > 0)
        //            {
        //                //CreateSegment + CreateCampaign(99)
        //                demographicsAttribCount -= 99;
        //            }
        //        }

        //        //User
        //        if (demographicsAttribCount < 100)
        //        {
        //            /// Do Normal
        //        }
        //        else
        //        {
        //            while (demographicsAttribCount > 0)
        //            {

        //            }
        //        }

        //        //Custom + DL

        //    }
        //    else
        //    {
        //        //Use existing method
        //    }
        //}

        ////Upload Segment

        //private async Task<string> UploadNDJsonToS3(byte[] fileBytes, string notificationId)
        //{
        //    return await _awsS3Service.UploadSegmentAsJson(fileBytes, GetBucketNameFromConfig(), notificationId);
        //}

        //Only for User
        //private async Task<CreateImportJobResponse> CreateImportUserIdSegmentJob(Notification notification, string s3Url)
        //{
        //    var request = new CreateImportJobRequest()
        //    {
        //        ApplicationId = PinpointApplicationId,
        //        ImportJobRequest = new ImportJobRequest()
        //        {
        //            DefineSegment = true,
        //            Format = Format.JSON,
        //            RegisterEndpoints = true,
        //            RoleArn = GetPinpointRoleArn(),
        //            S3Url = s3Url
        //        }
        //    };

        //    Console.WriteLine("Import Request : {0}", JsonConvert.SerializeObject(request));

        //    var result = await _pinpointService.CreateImportJobAsync(request);

        //    if (result.HttpStatusCode.Equals(HttpStatusCode.OK))
        //    {
        //        Console.WriteLine("Import Job Create");
        //        Console.WriteLine("Import Job Response : " + JsonConvert.SerializeObject(result.ImportJobResponse));
        //    }
        //    else
        //    {
        //        Console.WriteLine(JsonConvert.SerializeObject(result));
        //    }

        //    return result;
        //}

        //private string GetBucketNameFromConfig()
        //{
        //    var bucket_key_variable = _configuration.GetSection(ConfigSettings.S3_BUCKET_KEY).Value;
        //    return Environment.GetEnvironmentVariable(bucket_key_variable);
        //}

        //private string GetPinpointRoleArn()
        //{
        //    var roleArn_key_variable = _configuration.GetSection(ConfigSettings.PINPOINT_ROLE_ARN).Value;
        //    return Environment.GetEnvironmentVariable(roleArn_key_variable);
        //}

        //private string CreateNDJson(Notification notification)
        //{
        //    var users = notification.Topics?.Where(i => i.TopicType.Equals(TopicType.User) && !string.IsNullOrEmpty(i.TopicValue)).Distinct();

        //    if (users.Any())
        //    {
        //        Console.WriteLine("User Count : ", users.Count().ToString());

        //        StringBuilder sb = new StringBuilder();

        //        foreach (var user in users)
        //        {
        //            sb.AppendLine(JsonConvert.SerializeObject(new PinpointSegment.UploadSegmentUser()
        //            {
        //                User = new PinpointSegment.User()
        //                {
        //                    UserId = user.TopicValue
        //                }
        //            }));
        //        }

        //        return sb.ToString();
        //    }
        //    else
        //    {
        //        return string.Empty;
        //    }
        //}

        private byte[] GetFileBytesFromJsonString(string jsonValue)
        {
            return Encoding.UTF8.GetBytes(jsonValue);
        }

        //Creating only SG and Custom Topics
        //private async Task AutoCreateTopics(Notification notification)
        //{
        //    var topics = notification.Topics.Where(i => (i.TopicType.Equals(TopicType.CustomTopic) || i.TopicType.Equals(TopicType.SecurityGroup)) && !string.IsNullOrEmpty(i.TopicValue)).ToList();

        //    if (topics.Count > 0)
        //    {

        //        var availableTopicsInDb = await _topicRepository.FindAsync(i => (i.AssociatedApplication.Application.Id.Equals(notification.Application.Application.Id) && i.TopicType.Equals(TopicType.CustomTopic)) ||
        //                 (i.AssociatedApplication.Application.Id.Equals(notification.Application.Application.Id) && i.TopicType.Equals(TopicType.SecurityGroup))) ?? new List<Entities.Topic>();

        //        var availableTopics = topics.Where(i => availableTopicsInDb.Any(j => j.TopicValue.Equals(i.TopicValue)));

        //        var topicsToCreate = topics.Except(availableTopics);

        //        //Bulk Insert
        //        //Convert To Associated Topic to Topics

        //        if (topicsToCreate.ToList().Count > 0)
        //        {
        //            List<Topic> insertList = new List<Topic>();

        //            foreach (var topic in topicsToCreate)
        //            {
        //                insertList.Add(new Topic()
        //                {
        //                    AssociatedApplication = notification.Application,
        //                    CreatedDate = DateTime.Now,
        //                    ModifiedDate = DateTime.Now,
        //                    TopicStatus = true,
        //                    TopicType = topic.TopicType,
        //                    TopicName = string.IsNullOrEmpty(topic.TopicName) ? string.Format("{0} - {1}", notification.Application.Application.Name, topic.TopicValue) : topic.TopicName,
        //                    TopicValue = topic.TopicValue
        //                });
        //            }

        //            await _topicRepository.InsertManyAsync(insertList);
        //        }
        //    }
        //}
    }
    public class DeviceByTokenSpec : Specification<Device>
    {
        public DeviceByTokenSpec(string token)
        {
            Query.Where(i => !i.IsDeleted && i.NewNotificationToken == token);
        }
    }
    public class DeviceByUserIdSpec : Specification<Device>
    {
        public DeviceByUserIdSpec(Guid userId)
        {
            Query.Where(i => !i.IsDeleted && i.UserId == userId && i.Platform != Platform.WebApp);
        }
        public DeviceByUserIdSpec(List<Guid> userIds)
        {
            Query.Where(i => !i.IsDeleted && userIds.Contains(i.UserId) && i.Platform != Platform.WebApp);
        }
    }
}
namespace Payload
{
    public class Data
    {
        public string? title { get; set; }
        public string? message { get; set; }
        public string? imageIconUrl { get; set; }
        public string? imageSmallIconUrl { get; set; }
        public string? imageUrl { get; set; }
        public string? image { get; set; }
        public string? deeplinkUrl { get; set; }
        public Guid? NotificationUniqueId { get; set; }
    }

    public class Notification
    {
        public string? title { get; set; }
        public string? body { get; set; }
        public string? image { get; set; }
        public string? icon { get; set; }
        public string? link { get; set; }
        public string? sound { get; set; }
        public Guid? NotificationUniqueId { get; set; }
    }

    public class Root
    {
        public Notification? notification { get; set; }
        public Data? data { get; set; }

        [JsonProperty("mutable-content")]
        public int mutable_content { get; set; }

        public string? priority { get; set; }
        public Guid? NotificationUniqueId { get; set; }
    }
}

