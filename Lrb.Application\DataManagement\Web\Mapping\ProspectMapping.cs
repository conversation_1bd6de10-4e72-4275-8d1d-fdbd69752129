﻿using Lrb.Application.DataCallLogs.Web.Dtos;
using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.Lead.Web;
using Lrb.Domain.Entities.MasterData;
using Microsoft.Extensions.DependencyInjection;
using System;

namespace Lrb.Application.DataManagement.Web
{
    public class ProspectMapping
    {
        private static IRepository<MasterPropertyType> _masterPropertyTypeRepo = null;
        public static IRepository<MasterAreaUnit> _masterAreaUnitRepo = null;
        public static async void Configure(IServiceProvider provider)
        {
            _masterPropertyTypeRepo = (IRepository<MasterPropertyType>)provider.GetRequiredService(typeof(IRepository<>).MakeGenericType(typeof(MasterPropertyType)));
            _masterAreaUnitRepo = (IRepository<MasterAreaUnit>)provider.GetRequiredService(typeof(IRepository<>).MakeGenericType(typeof(MasterAreaUnit)));
            List<MasterPropertyType>? propertytypes = null;
            if (_masterPropertyTypeRepo != null)
            {
                propertytypes = _masterPropertyTypeRepo.ListAsync().Result;
            }
            List<MasterAreaUnit>? areaUnits = null;
            if (_masterAreaUnitRepo != null)
            {
                areaUnits = _masterAreaUnitRepo.ListAsync().Result;
            }
            List<CreateProspectEnquiryDto> prospectEnquiry = new();
            TypeAdapterConfig<Lrb.Domain.Entities.Prospect, ViewProspectDto>
                .NewConfig()
                .Map(dest => dest.Enquiry, src => src.Enquiries != null && src.Enquiries.Any() ? src.Enquiries.FirstOrDefault(i => i.IsPrimary) : null)
                .Map(dest => dest.AddressDto, src => src.Address)
                .Map(dest => dest.CallRecordingUrls, src => (src.CallRecordingUrls != null && src.CallRecordingUrls.Any()) ? src.CallRecordingUrls.GroupBy(i => i.Key.Year).ToDictionary(i => i.Key, i => i.GroupBy(i => i.Key.Month).ToDictionary(i => i.Key, i => i.ToList().ToDictionary(i => i.Key, i => i.Value))) : null)
                ;

            TypeAdapterConfig<ProspectEnquiry, ViewProspectEnquiryDto>
                .NewConfig()
                
                .Map(dest => dest.PropertyType, src => src.PropertyType != null ? new PropertyTypeDto
                {
                    Id = src.PropertyType.BaseId != null ? src.PropertyType.BaseId.Value : default,
                    BaseId = null,
                    DisplayName = src.PropertyType.BaseId != null ? propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).DisplayName : default,
                    Type = src.PropertyType.BaseId != null ? propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).Type : default,
                    Level = 0,
                    ChildType = new()
                    {
                        Id = src.PropertyType.Id,
                        BaseId = src.PropertyType.BaseId != null ? src.PropertyType.BaseId : default,
                        DisplayName = src.PropertyType.DisplayName,
                        Level = src.PropertyType.Level,
                        Type = src.PropertyType.Type,
                        ChildType = null
                    }
                } : null)
                .Map(dest => dest.ProspectSource, src => src.Source)
                .Map(dest => dest.CarpetAreaUnit, src => src.CarpetAreaUnitId != default && areaUnits != null && areaUnits.FirstOrDefault(i => i.Id == src.CarpetAreaUnitId) != null ? areaUnits.FirstOrDefault(i => i.Id == src.CarpetAreaUnitId).Unit : default)
                .Map(dest => dest.BuiltUpAreaUnit, src => src.BuiltUpAreaUnitId != default && areaUnits != null && areaUnits.FirstOrDefault(i => i.Id == src.BuiltUpAreaUnitId) != null ? areaUnits.FirstOrDefault(i => i.Id == src.BuiltUpAreaUnitId).Unit : default)
                .Map(dest => dest.SaleableAreaUnit, src => src.SaleableAreaUnitId != default && areaUnits != null && areaUnits.FirstOrDefault(i => i.Id == src.SaleableAreaUnitId) != null ? areaUnits.FirstOrDefault(i => i.Id == src.SaleableAreaUnitId).Unit : default)
                .Map(dest => dest.PropertyAreaUnit, src => src.PropertyAreaUnitId != default && areaUnits != null && areaUnits.FirstOrDefault(i => i.Id == src.PropertyAreaUnitId) != null ? areaUnits.FirstOrDefault(i => i.Id == src.PropertyAreaUnitId).Unit : default)
                .Map(dest => dest.NetAreaUnit, src => src.NetAreaUnitId != default && areaUnits != null && areaUnits.FirstOrDefault(i => i.Id == src.NetAreaUnitId) != null ? areaUnits.FirstOrDefault(i => i.Id == src.NetAreaUnitId).Unit : default)
                .Map(dest => dest.PropertyTypes, src => src.PropertyTypes != null ? src.PropertyTypes.Select(pt => new PropertyTypeDto
                {
                    Id = pt.BaseId.HasValue ? pt.BaseId.Value : default,
                    BaseId = null,
                    DisplayName = pt.BaseId.HasValue ? (propertytypes.FirstOrDefault(i => i.Id == pt.BaseId.Value) != null ? propertytypes.FirstOrDefault(i => i.Id == pt.BaseId.Value).DisplayName : default) : default,
                    Type = pt.BaseId.HasValue ? (propertytypes.FirstOrDefault(i => i.Id == pt.BaseId.Value) != null ? propertytypes.FirstOrDefault(i => i.Id == pt.BaseId.Value).Type : default) : default,
                    Level = 0,
                    ChildType = new PropertyTypeDto
                    {
                        Id = pt.Id,
                        BaseId = pt.BaseId,
                        DisplayName = pt.DisplayName,
                        Level = pt.Level,
                        Type = pt.Type,
                        ChildType = null
                    }
                }).ToList() : null); 


            TypeAdapterConfig<ProspectEnquiry, Lrb.Domain.Entities.LeadEnquiry>
                 .NewConfig()
                 .Map(dest => dest.EnquiredFor, src => src.EnquiryType)
                 .Map(dest => dest.BHKTypes, src => src.BHKTypes)
                 .Map(dest => dest.BHKs, src => src.BHKs)
                 .Map(dest => dest.EnquiryTypes, src => src.EnquiryTypes)
                 .Map(dest => dest.SubSource, src => src.SubSource)
                 .Map(dest => dest.LowerBudget, src => src.LowerBudget)
                 .Map(dest => dest.UpperBudget, src => src.UpperBudget)
                 .Map(dest => dest.CarpetArea, src => src.CarpetArea)
                 .Map(dest => dest.CarpetAreaInSqMtr, src => src.CarpetAreaInSqMtr)
                 .Map(dest => dest.NoOfBHKs, src => src.NoOfBhks)
                 .Map(dest => dest.BuiltUpAreaUnitId, src => src.BuiltUpAreaUnitId)
                 .Map(dest => dest.BuiltUpArea, src => src.BuiltUpArea)
                 .Map(dest => dest.BuiltUpAreaInSqMtr, src => src.BuiltUpAreaInSqMtr)
                 .Map(dest => dest.SaleableArea, src => src.SaleableArea)
                 .Map(dest => dest.SaleableAreaUnitId, src => src.SaleableAreaUnitId)
                 .Map(dest => dest.SaleableAreaInSqMtr, src => src.SaleableAreaInSqMtr)
                  .Map(dest => dest.NetArea, src => src.NetArea)
                 .Map(dest => dest.NetAreaUnitId, src => src.NetAreaUnitId)
                 .Map(dest => dest.NetAreaInSqMtr, src => src.NetAreaInSqMtr)
                  .Map(dest => dest.PropertyArea, src => src.PropertyArea)
                 .Map(dest => dest.PropertyAreaUnitId, src => src.PropertyAreaUnitId)
                 .Map(dest => dest.PropertyAreaInSqMtr, src => src.PropertyAreaInSqMtr)
                  .Map(dest => dest.UnitName, src => src.UnitName)
                 .Map(dest => dest.ClusterName, src => src.ClusterName)



                 .Map(dest => dest.PropertyTypes, src => src.PropertyTypes);
            TypeAdapterConfig<Prospect, Lrb.Domain.Entities.Lead>
                .NewConfig()
                .Map(dest => dest.Properties, src => src.Properties)
                .Map(dest => dest.Projects, src => src.Projects)
                .Map(dest => dest.ChannelPartners, src => src.ChannelPartners);

            TypeAdapterConfig<Prospect, Lrb.Domain.Entities.Lead>
                .NewConfig()
                .Ignore(i => i.Enquiries);
            //.Ignore(i => i.Status);

            TypeAdapterConfig<IVRCommonCallLog, ProspectCallLogDto>
                .NewConfig()
                .Map(dest => dest.CallStartTime, src => src.StartStamp)
                .Map(dest => dest.CallEndTime, src => src.EndStamp)
                .Map(dest => dest.CallDuration, src => src.Duration)
                .Map(dest => dest.CallDirection, src => (((!string.IsNullOrWhiteSpace(src.Direction)) && src.Direction.Contains("inbound")) ? CallDirection.Incoming :
                                                        ((!string.IsNullOrWhiteSpace(src.Direction)) && src.Direction.Contains("outbound")) ? CallDirection.Outgoing :
                                                        CallDirection.None))
                .Map(dest => dest.UpdatedCallStatus, src => src.CallStatus)
                .Map(dest => dest.LastModifiedOn, src => src.LastModifiedOn)
                .Map(dest => dest.UpdatedCallDirection, src => src.Direction)
                .Map(dest => dest.CallRecordingUrl, src => !string.IsNullOrWhiteSpace(src.CallRecordingURL) ? src.CallRecordingURL : string.Empty)
                .Ignore(dest => dest.CallStatus);
        }
    }
}
