﻿using Lrb.Application.Common.Persistence.New_Implementation;

namespace Lrb.Application.Project.Mobile
{

    public class GetAllProjectParameter : PaginationFilter
    {
        public ProjectVisiblityType ProjectVisiblity { get; set; }
        public ProjectStatus? ProjectStatus { get; set; }
        public ProjectCurrentStatus? CurrentStatus { get; set; }
        public List<Guid>? ProjectType { get; set; }
        public Facing? Facing { get; set; }
        public List<string>? ProjectName { get; set; }
        public List<string>? BuilderName { get; set; }
        public List<Guid>? ProjectSubType { get; set; }
        public long? MinPrice { get; set; }
        public long? MaxPrice { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? Search { get; set; }
        public DateTime? FromPossesionDate { get; set; }
        public DateTime? ToPossesionDate { get; set; }
        public PossesionType? Possesion { get; set; }
        public List<string>? Locations { get; set; }
        public double? CarpetArea { get; set; }
        public Guid? CarpetAreaUnitId { get; set; }
        public List<Guid>? AmenitesIds { get; set; }
        public List<Budget>? Budgets { get; set; }
        public string? Currency { get;set; }
        public List<Facing>? Facings { get; set; }
        public int? MinLeadCount { get; set; }
        public int? MaxLeadCount { get; set; } 
        public int? MaxProspectCount { get; set; } 
        public int? MinProspectCount { get; set; } 
        public double? MinCarpetArea { get; set; }
        public double? MaxCarpetArea { get; set; }
        public long? FromMinPrice { get; set; }
        public long? ToMinPrice { get; set; }
        public long? FromMaxPrice { get; set; }
        public long? ToMaxPrice { get; set; }
    }

    public class GetAllProjectRequest : GetAllProjectParameter, IRequest<PagedResponse<ViewProjectDto, string>>
    {
    }
    public class GetAllProjectRequestHandler : PaginationFilter, IRequestHandler<GetAllProjectRequest, PagedResponse<ViewProjectDto, string>>
    {
        public readonly IRepositoryWithEvents<Domain.Entities.Project> _projectRepo;
        private readonly IProjectRepository _efProjectRepository;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        public GetAllProjectRequestHandler(IRepositoryWithEvents<Domain.Entities.Project> leadRepo,
            IProjectRepository efProjectRepository,
            IDapperRepository dapperRepository,
            ICurrentUser currentUser)
        {
            _projectRepo = leadRepo;
            _efProjectRepository = efProjectRepository;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        public async Task<PagedResponse<ViewProjectDto, string>> Handle(GetAllProjectRequest request, CancellationToken cancellationToken)
        {
            List<Guid> projectIds = new();
            if (request.MinLeadCount != null || request.MaxLeadCount != null || request.MinProspectCount != null || request.MaxProspectCount != null)
            {
                var tenantId = _currentUser.GetTenant();
                var project = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<ProjectIdsDto>("LeadratBlack", "Lead&Prospects_ProjectAssociatedCountFilter", new
                {
                    tenantid = tenantId,
                    minprospectcount = request.MinProspectCount,
                    maxprospectcount = request.MaxProspectCount,
                    minleadcount = request.MinLeadCount,
                    maxleadcount = request.MaxLeadCount
                })).FirstOrDefault()?.ProjectIds ?? new List<Guid>();
                projectIds = project.ToList();
            }
            var projects = await _efProjectRepository.GetAllProjectsForMobileNewV2Async(request, projectIds);
            var count = await _efProjectRepository.GetAllProjectsCountForMobileNewAsync(request, projectIds);
            var projectDto = projects.Adapt<List<ViewProjectDto>>();
            return new PagedResponse<ViewProjectDto, string>(projectDto, count);
         }
    }

    public enum ProjectVisiblityType
    {
        All,
        Residential,
        Commercial,
        Agriculture,
        Deleted
    }

    public enum ProjectType
    {
        All,
        Residential,
        Commercial,
        Agriculture,
    }
    public class ProjectIdsDto
    {
        public IList<Guid>? ProjectIds { get; set; }
    }
}
