﻿using Lrb.Application.Common.Persistence;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Mobile.Mappings;
using Lrb.Application.Lead.Mobile.Specs.v1;
using Lrb.Application.Lead.Web.Mappings;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Newtonsoft.Json;

namespace Lrb.Application.Lead.Mobile
{
    public class AssignLeadsOfUserRequest : IRequest<Response<bool>>
    {
        public Guid UserId { get; set; }
        public List<Guid> UserIdsToAssign { get; set; }
    }
    public class AssignLeadsOfUserRequestHandler : IRequestHandler<AssignLeadsOfUserRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepository;
        private readonly IRepositoryWithEvents<LeadHistory> _leadHistoryRepository;
        private readonly IUserService _userService;
        private readonly INotificationSenderService _notificationSenderService;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalsettingRepo;
        public AssignLeadsOfUserRequestHandler(
            IUserService userService,
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepository,
            IRepositoryWithEvents<LeadHistory> leadHistoryRepository,
            INotificationSenderService notificationSenderService,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalsettingRepo)
        {
            _leadRepository = leadRepository;
            _leadHistoryRepository = leadHistoryRepository;
            _userService = userService;
            _notificationSenderService = notificationSenderService;
            _leadRepositoryAsync = leadRepositoryAsync;
            _globalsettingRepo = globalsettingRepo;
        }

        public async Task<Response<bool>> Handle(AssignLeadsOfUserRequest request, CancellationToken cancellationToken)
        {
            Domain.Entities.GlobalSettings? globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
            var leads = await _leadRepository.ListAsync(new AllLeadsByUserIdSpec(request.UserId));
            if (leads?.Any() ?? false)
            {
                var leadsCount = leads.Count;
                var counter = 0;
                var users = await _userService.GetListOfUsersByIdsAsync(request.UserIdsToAssign.Select(i => i.ToString()).ToList(), cancellationToken);
                if (users?.Any() ?? false)
                {
                    //var usersWithLeadCount = users.Select(i => new { Id = i.Id, LeadCount = _leadRepo.CountAsync(new LeadsCountByUserIdSpec(i.Id), cancellationToken).Result });
                    while (counter < leadsCount)
                    {
                        foreach (var user in users)
                        {
                            leads[counter].AssignedFrom = leads[counter].AssignTo;
                            leads[counter].AssignTo = user.Id;
                            if (leads[counter].AssignTo != Guid.Empty || leads[counter].OriginalOwner == null)
                            {
                                leads[counter].OriginalOwner = leads[counter].AssignTo;
                            }
                            if (leads[counter].AssignTo != leads[counter].AssignedFrom)
                            {
                                leads[counter].PickedDate = null;
                                leads[counter].IsPicked = false;
                                leads[counter].ShouldUpdatePickedDate = false;
                            }
                            await _leadRepository.UpdateAsync(leads[counter]);
                            var fullLead = (await _leadRepository.ListAsync(new LeadByIdSpec(leads[counter].Id), cancellationToken))?.FirstOrDefault();
                            var leadDto = fullLead?.Adapt<ViewLeadDto>();
                            await leadDto?.SetUsersInViewLeadDtoAsync(_userService, cancellationToken);
                            var leadHsitory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                            var existingLeadHistory = await _leadHistoryRepository.FirstOrDefaultAsync(new LeadHistorySpec(fullLead.Id, fullLead.AssignedFrom ?? Guid.Empty));
                            if (existingLeadHistory != null)
                            {
                                await _leadHistoryRepository.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHsitory));
                            }
                            else
                            {
                                await _leadHistoryRepository.AddAsync(leadHsitory);
                            }
                            counter++;
                            try
                            {
                                List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadAssignment, leads[counter], leadDto.AssignTo, leadDto.AssignedUser.Name, topics: new List<string> { leads[counter].CreatedBy.ToString(), leads[counter].LastModifiedBy.ToString() }, globalSettings: globalSettings);
                                await leads[counter].SendLeadStatusChangeNotificationsAsync(leadDto, _notificationSenderService, globalSettings);
                                if(leads[counter].AssignTo == Guid.Empty)
                                {
                                    await _notificationSenderService.DeleteScheduledNotificationsAsync(leads[counter]);
                                }
                            }
                            catch(Exception e)
                            {
                                var error = new LrbError()
                                {
                                    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                                    ErrorSource = e?.Source,
                                    StackTrace = e?.StackTrace,
                                    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                    ErrorModule = "AssignLeadsOfUserRequestHandler -. Handle()"
                                };
                                await _leadRepositoryAsync.AddErrorAsync(error);
                            }
                        }
                    }
                }
            }
            return new Response<bool>(true);
        }
    }
}
