﻿using System.ComponentModel.DataAnnotations.Schema;

namespace Lrb.Domain.Entities
{
    public class TempVariable : AuditableEntity, IAggregateRoot
    {
        [Column(TypeName = "jsonb")]
        public List<string>? LeadVariables { get; set; }
        [Column(TypeName = "jsonb")]
        public List<string>? LeadEnquiryVariables { get; set; }
        [Column(TypeName = "jsonb")]
        public List<string>? UserVariables { get; set; }
        [Column(TypeName = "jsonb")]
        public List<string>? IVRVariables { get; set; }
        [Column(TypeName = "jsonb")]
        public List<string>? WAVariables { get; set; }
        [Column(TypeName = "jsonb")]
        public Dictionary<string, List<string>>? WebHookVariables { get; set; }
        [Column(TypeName = "jsonb")]
        public List<string>? WebhookConstantVariables { get; set; }
        [Column(TypeName = "jsonb")]
        public List<string>? InvoiceConstantVariables { get; set; }
    }
}