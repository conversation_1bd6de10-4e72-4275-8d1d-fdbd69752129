﻿using Lrb.Application.Automation.Requests;
using Lrb.Application.Lead.Web;
using Lrb.Application.Project.Web;
using Lrb.Application.Project.Web.Dtos;
using Lrb.Application.Project.Web.Dtos.Microsite;
using Lrb.Application.Project.Web.Requests;
using Lrb.Application.Project.Web.Requests.Bulk_Upload;
using Lrb.Application.Project.Web.Requests.Microsite;
using Lrb.Application.Property.Web;
using Lrb.Application.Property.Web.Dtos;
using Lrb.Application.Property.Web.Requests;
using Lrb.Application.UserDetails.Web.Dtos;
using Lrb.Application.UserDetails.Web.Request;
using Lrb.Domain.Entities;
using Newtonsoft.Json;
using UpdateBrochureRequest = Lrb.Application.Project.Web.Requests.UpdateBrochureRequest;

namespace Lrb.WebApi.Host.Controllers
{
    [Authorize]
    public class ProjectController : VersionedApiController
    {
        private readonly Serilog.ILogger _logger;
        public ProjectController(Serilog.ILogger logger)
        {
            _logger = logger;
        }
        [HttpGet]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Search, LrbResource.Projects)]
        [OpenApiOperation("Search projects using available filters.", "")]
        public async Task<PagedResponse<ViewProjectDto, string>> SearchAsync([FromQuery] GetAllProjectRequest request)
        {
            return await Mediator.Send(request);
        }
        [AllowAnonymous]
        [HttpGet("anonymous")]
        [TenantIdHeader]
        [OpenApiOperation("Get all Projects.", "")]
        public Task<PagedResponse<PullViewProjectDto, string>> GetAllProjectsAsync([FromQuery] GetAllProjectAnonymousRequest request)
        {
            if (HttpContext.Request.Headers.TryGetValue(Lrb.Shared.Multitenancy.MultitenancyConstants.TenantIdName, out var tenantIdValues))
            {
                string? tenantId = tenantIdValues.FirstOrDefault();
                _logger.Information("GetAllProjectAnonymousRequest info: PageNumber={PageNumber}, PageSize={PageSize}, TenantId={TenantId}", request.PageNumber, request.PageSize, tenantId);

            }
            return Mediator.Send(request);
        }
        [HttpGet("count")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get Project Top Level Count")]
        public async Task<Response<ProjectTopLevelCountDto>> GetProjectTopLevelCount([FromQuery] GetProjectTopLevelCountRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get Project details.", "")]
        public Task<Response<ViewProjectDto>> GetAsync(Guid id)
        {
            return Mediator.Send(new GetProjectByIdRequest(id));
        }

        [HttpPost]
        [TenantIdHeader]
        [OpenApiOperation("Create a Project.", "")]
        public async Task<Response<Guid>> CreateAsync(CreateProjectRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("Block")]
        [TenantIdHeader]
        [OpenApiOperation("Create a Block.", "")]
        public async Task<Response<Guid>> CreateAsync(CreateBlockRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("UnitType")]
        [TenantIdHeader]
        [OpenApiOperation("Create a UnitType.", "")]
        public async Task<Response<Guid>> CreateAsync(CreateUnitTypeRequest request)
        {
            return await Mediator.Send(request);
        }
        //[HttpPost("Floor")]
        //[TenantIdHeader]
        //[OpenApiOperation("Create a Floor.", "")]
        //public async Task<Response<Guid>> CreateAsync(CreateFloorRequest request)
        //{
        //    return await Mediator.Send(request);
        //}
        [HttpPut("UpdateBasicDetails")]
        [TenantIdHeader]
        [OpenApiOperation("Update basic details of a Project.", "")]
        public async Task<ActionResult<Response<Guid>>> UpdateAsync(UpdateProjectBasicDetailsRequest request, Guid id)
        {
            return id != request.Id ?
                BadRequest() :
                Ok(await Mediator.Send(request));
        }
        [HttpPut("UpdateBlockDetails")]
        [TenantIdHeader]
        [OpenApiOperation("Update Block details of a Project", "")]
        public async Task<Response<Guid>> UpdateAsync(UpdateProjectBlockDetailsRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPut("UpdateUnitTypes")]
        [TenantIdHeader]
        [OpenApiOperation("Update UnitTypes of a Project", "")]
        public async Task<Response<Guid>> UpdateAsync(UpdateProjectUnitTypesRequest request)
        {
            return await Mediator.Send(request);
        }
        //[HttpPut("UpdateFloorDetails")]
        //[TenantIdHeader]
        //[OpenApiOperation("Update Floor details of a Block", "")]
        //public async Task<Response<Guid>> UpdateAsync(UpdateProjectFloorRequest request)
        //{
        //    return await Mediator.Send(request);
        //}
        [HttpDelete("{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Delete, LrbResource.Projects)]
        [OpenApiOperation("Delete a Project.", "")]
        public Task<Response<bool>> DeleteAsync(Guid id)
        {
            return Mediator.Send(new DeleteProjectDetailRequest(id));
        }

        [HttpDelete("block/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Delete, LrbResource.Projects)]
        [OpenApiOperation("Delete a Block.", "")]
        public Task<Response<bool>> DeleteBlockAsync(Guid id)
        {
            return Mediator.Send(new DeleteBlockRequest(id));
        }

        //[HttpDelete("floor/{id:guid}")]
        //[TenantIdHeader]
        //[MustHavePermission(LrbAction.Delete, LrbResource.Properties)]
        //[OpenApiOperation("Delete a Floor.", "")]
        //public Task<Response<bool>> DeleteFloorAsync(Guid id)
        //{
        //    return Mediator.Send(new DeleteFloorRequest(id));
        //}

        [HttpDelete("UnitType{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Delete, LrbResource.Projects)]
        [OpenApiOperation("Delete a UnitType.", "")]
        public Task<Response<bool>> DeleteUnitTypeAsync(Guid id)
        {
            return Mediator.Send(new DeleteUnitTypeRequest(id));
        }

        [HttpPut("toggle/status")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Projects)]
        [OpenApiOperation("Toggle Project Current Status.", "")]
        public Task<Response<bool>> ToggleProjectCurrentStatus(Guid id)
        {
            return Mediator.Send(new ToggleProjectStatusRequest(id));
        }

        [HttpDelete("archive")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Projects)]
        [OpenApiOperation("Archive Project.", "")]
        public Task<Response<bool>> ArchiveProject(Guid id)
        {
            return Mediator.Send(new ArchivedProjectRequest(id));
        }

        [HttpPut("update/brochure")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Projects)]
        [OpenApiOperation("Toggle Project Current Status.", "")]
        public Task<Response<bool>> UpdateProjectBrouchers(UpdateBrochureRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpPost("amenities")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Projects)]
        [OpenApiOperation("Create Project Amenities", "")]
        public Task<Response<Guid>> CreateProjectAmenities(CreateProjectAmenitiesRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpPut("gallery")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Projects)]
        [OpenApiOperation("Update Project Gallery", "")]
        public Task<Response<Guid>> UpdateProjectGallery(UpdateProjectGalleryRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpPut("amenity")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Projects)]
        [OpenApiOperation("Update Project Amenities")]
        public Task<Response<bool>> UpdateProjectAmenity(UpdateProjectAmenitiesRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpGet("assignments")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get All Project For Integration Assignment")]
        public async Task<PagedResponse<ProjectAssignmentDto, string>> GetAll([FromQuery] GetAllProjectForIntegrationAssignmentRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPut("toggle/unitTypes")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Projects)]
        [OpenApiOperation("Toggle UnitTypes Status.", "")]
        public Task<Response<bool>> ToggleUnitTypesStatus(Guid id)
        {
            return Mediator.Send(new ToggleUnitTypeRequest(id));
        }

        [HttpGet("CustomProjectType")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get Custom ProjectTypes.", "")]
        public Task<PagedResponse<CustomProjectTypeDto, string>> GetcustomProjectTypes()
        {
            return Mediator.Send(new GetCustomMasterProjectTypeRequest());
        }

        [HttpGet("UnitInfos")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get all unit infos using available filters.", "")]
        public async Task<PagedResponse<ViewUnitTypeDto, string>> SearchAsync([FromQuery] GetAllProjectUnitInfoRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("blocks")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get all blocks using available filters.", "")]
        public async Task<PagedResponse<ViewBlockDto, string>> SearchAsync([FromQuery] GetProjectBlocksRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("UnitInfo/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get all unit infos using available filters.", "")]
        public async Task<Response<ViewUnitTypeDto>> SearchAsync(Guid id)
        {
            return await Mediator.Send(new GetUnitInfoByIdRequest(id));
        }

        [HttpGet("builder/Infos")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get all Builder Details", "")]
        public async Task<Response<List<ProjectBuilderInfosDto>>> GetBuilderAsync()
        {
            return await Mediator.Send(new GetProjectBuilderInfoRequest());
        }
        #region Microsite Apis

        [AllowAnonymous]
        [HttpGet("microsite")]
        [TenantIdHeader]
        [OpenApiOperation("Get Project Microsite")]
        public async Task<Response<ViewProjectMicrosite>> GetMicrosite(string serialNo)
        {
            return await Mediator.Send(new GetProjectMicrositeRequest(serialNo));
        }

        [AllowAnonymous]
        [HttpGet("microsite/anonymous")]
        [TenantIdHeader]
        [OpenApiOperation("Get Anonymous Project Microsite")]
        public async Task<Response<PullViewProjectMicrosite>> GetMicrositeasync(string serialNo)
        {
            return await Mediator.Send(new GetProjectMicrositeAnonymousRequest(serialNo));
        }

        [AllowAnonymous]
        [HttpGet("microsite/unit")]
        [TenantIdHeader]
        [OpenApiOperation("Get Unit for project microsite")]
        public async Task<Response<List<MicrositeUnit>>> GetUnit(string serialNo)
        {
            return await Mediator.Send(new GetUnitTypeForMicrositeRequest(serialNo));
        }
        [AllowAnonymous]
        [HttpGet("microsite/units-blocks")]
        [TenantIdHeader]
        [OpenApiOperation("Get Project Unit And Block For Microsite", "")]
        public async Task<Response<ViewMicrositeUnitAndBlock>> GetMicrositeUnitAndBlock(string serialNo)
        {
            return await Mediator.Send(new GetProjectUnitForMicrositeRequest(serialNo));
        }

        [AllowAnonymous]
        [HttpGet("microsite/amenities")]
        [TenantIdHeader]
        [OpenApiOperation("Get Project Amenities For Microsite", "")]
        public async Task<Response<ViewProjectAmenitiesForMicrosite>> GetMicrositeAmenities(string serialNo)
        {
            return await Mediator.Send(new GetProjectAmenitiesForMicrositeRequest(serialNo));
        }

        #endregion


        [HttpGet("unitInfo")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get Project Unit Info", "")]
        public async Task<Response<List<ProjectUnitInfo>>> GetUnitinfoAsync(Guid id)
        {
            return await Mediator.Send(new GetProjectUnitInfoByProjectIdRequest(id));
        }

        [AllowAnonymous]
        [HttpGet("IdWithName")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get Project Unit Info", "")]
        public async Task<Response<List<ProjectInfoDto>>> GetProjectInfoAsync()
        {
            var tenantId = this.HttpContext.Request.Headers["tenant"];
            var request = new GetAllProjectNameAndRequest
            {
                TenantId = tenantId
            };
            return await Mediator.Send(request);
        }

        [HttpDelete("bulk")]
        [TenantIdHeader]
        [OpenApiOperation("Bulk Delete project", "")]
        public async Task<Response<bool>> BulkDeleteAsync([FromBody] BulkDeleteProjectRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpDelete("block/bulk")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Delete, LrbResource.Projects)]
        [OpenApiOperation("Bulk delete block infos")]
        public async Task<Response<bool>> BulkDeleteAsync([FromBody] BulkDeleteBlockInfoRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("location")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get all project addresses")]
        public async Task<Response<List<string>>> GetAllAddresses()
        {
            return await Mediator.Send(new GetAllProjectAddressesRequest());
        }

        [HttpPut("bulk/restore")]
        [TenantIdHeader]
        [OpenApiOperation("Bulk restore project")]
        public async Task<Response<bool>> RestoreProjectAsync(RestoreProjectRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("amenities")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get Project amenities")]
        public async Task<Response<List<Guid>>> GetAmenitiesAsync(Guid id)
        {
            return await Mediator.Send(new GetProjectAmenitiesRequest(id));
        }

        [HttpGet("name")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get Project Id. by name", "")]
        public async Task<Response<ProjectNameDto>> GetAsync(string projectName)
        {
            return await Mediator.Send(new GetProjectNameRequest(projectName));
        }
        [HttpGet("Currency")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get All Currency .", "")]
        public async Task<Response<List<string>>> GetAllCuurencyy()
        {
            return await Mediator.Send(new GetAllProjectCurrencyRequest());
        }

        [HttpPut("ContactRecordsCount")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.BulkShare, LrbResource.Projects)]
        [OpenApiOperation("update ContactTypesCount", "")]
        public async Task<Response<bool>> UpdateContact([FromBody] UpdateContactRecordsCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("gallery-dropdown")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get Gallery Image Names ", "")]
        public async Task<PagedResponse<string, string>> GetProjectGalleryDropdownDataAsync([FromQuery] GetProjectGalleryDropdownDataRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("unit/excel")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.BulkUpload, LrbResource.Projects)]
        [OpenApiOperation("upload excel", "")]
        public async Task<Response<FileColumnUnitDto>> UploadExcelasync1(IFormFile file)
        {
            return await Mediator.Send(new GetUnitExcelUploadFileRequest(file));
        }
        [HttpPost("unit/bulk")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.BulkUpload, LrbResource.Projects)]
        [OpenApiOperation("create new Unit from Excel", "")]
        public async Task<Response<BulkUnitUploadTracker>> CreateBulkUnitAsync([FromBody] RunAWSBatchForBulkUnitUploadRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("unit/tracker")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get All Tracker", "")]
        public async Task<PagedResponse<BulkUnitUploadTracker, string>> GetAllTracker([FromQuery] GetAllBulkUnitTrackerRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("matchingLeads")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get Matching Leads.", "")]
        public async Task<PagedResponse<LeadWithDegreeMatched, string>> GetMatchingLeadsAsync([FromQuery] GetMatchingLeadsByUnitIdRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("matchingLeadsByProject")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get Matching Leads.", "")]
        public async Task<PagedResponse<ViewLeadDto, string>> GetMatchingLeadsByProjectAsync([FromQuery] GetMatchingLeadsByProjectIdRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("GetBasicDetails")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get Project Basic Details.", "")]
        public async Task<Response<ViewProjectDto>> GetProjectBasicDetailsById(Guid id)
        {
            return await Mediator.Send(new GetProjectBasicDetailsByIdRequest(id));
        }

        [HttpGet("GetProjectGallery")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get Project Gallery.", "")]
        public async Task<Response<ViewProjectGelleryDto>> GetProjectGalleryById(Guid id)
        {
            return await Mediator.Send(new GetProjectGalleryByIdRequest(id));
        }

        [HttpPut("UnitType/ContactRecordsCount")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Projects)]
        [OpenApiOperation("Update UnitType ContactTypesCount", "")]
        public async Task<Response<bool>> UpdateUnitTypeContact([FromBody] UpdateUnitTypeContactRecordsCountRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpDelete("Delete")]
        [TenantIdHeader]
        [OpenApiOperation("Bulk project delete")]
        public async Task<Response<bool>> PermanantProjectDeleteRequest([FromBody] PermanantProjectDeleteRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("export/batch")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Export, LrbResource.Projects)]
        [OpenApiOperation("Export projects by excel.", "")]
        public Task<Response<Guid>> ExportProjectsAsync(RunAWSBatchForExportProjectsRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpGet("export/trackers")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get all export projects trackers", "")]
        public async Task<PagedResponse<ExportProjectTrackerDto, string>> GetAllProjectTrackers([FromQuery] GetProjectExportTracker request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("excel")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.BulkUpload, LrbResource.Projects)]
        [OpenApiOperation("Upload excel File")]
        public async Task<ActionResult<Response<Application.Property.Web.FileColumnDto>>> UploadExcelFileAsync(IFormFile file)
        {
            try
            {
                return await Mediator.Send(new Application.Project.Web.Requests.Bulk_Upload.GetExcelColumnsUsingEPPlusRequest(file));
            }
            catch (Exception e)
            {
                throw;
            }

        }
        [HttpPost("batch")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.BulkUpload, LrbResource.Projects)]
        [OpenApiOperation("Create new projects by excel.", "")]
        public Task<Response<BulkProjectUploadTracker>> CreateBulkAsync(RunAWSBatchForBulkProjectUploadRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpGet("bulk/trackers")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.BulkUpload, LrbResource.Properties)]
        [OpenApiOperation("Get all bulk project upload trackers", "")]
        public async Task<PagedResponse<BulkProjectUploadTracker, string>> GetAllTrackers([FromQuery] GetProjectBulkUploadTrackersRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("getMultipleProjectsByIds")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get multiple projects by their IDs.", "")]
        public Task<Response<List<ViewProjectDto>>> GetMultipleAsync([FromBody] GetMultipleProjectsByIdsRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpGet("assignedUsers")]
        [TenantIdHeader]
        [OpenApiOperation("Get project Assignmennts.", "")]
        public Task<Response<ProjectUserAssignmentDto>> GetAssignmentsAsync([FromQuery] GetProjectAssignmentRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpGet("all/projects")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get all projects.", "")]
        public async Task<Response<List<UserProjectInfoDto>>> GetAllProjectsAsync()
        {
            return await Mediator.Send(new GetAllProjectInfoRequest());
        }
        [HttpPost("cloneProject")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.CloneProject, LrbResource.Projects)]
        [OpenApiOperation("Create a Clone project.", "")]
        public async Task<ActionResult<Guid>> CreateAsync(CreateCloneProjectRequest request)
        {
            var result = await Mediator.Send(request);
            return Ok(result);
        }
    }
}
