﻿using Lrb.Application.Automation.Dtos;
using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.DataManagement.Web.Request.Bulk_Upload;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Property.Web.Dtos;
using Lrb.Application.Property.Web.Requests;
using Lrb.Application.UserDetails.Web;
using Lrb.Application.UserDetails.Web.Dtos;
using Lrb.Application.UserDetails.Web.Request;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.User;
using Lrb.Domain.Enums;
using MediatR;
using Microsoft.Extensions.Primitives;
using Newtonsoft.Json;

namespace Lrb.WebApi.Host.Controllers
{
    [Authorize]
    public class UserController : VersionedApiController
    {
        [HttpPost]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Users)]
        [OpenApiOperation("Create a new User.", "")]
        public Task<Response<Guid>> CreateAsync(CreateUserDetailsRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpPost("bulk")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Users)]
        [OpenApiOperation("Create a new bulk Users.", "")]
        public Task<Response<bool>> CreateAsync(IFormFile file)
        {
            return Mediator.Send(new CreateBulkUsersRequest(file, GetOriginFromRequest()));
        }

        //[HttpGet("getAllUsers")]
        //[TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Users)]
        //[OpenApiOperation("Get all user details.", "")]
        //public Task<PagedResponse<UserDetailsDto, UserDetailsCountDto>> SearchAsync([FromQuery] GetAllUserDetailsRequest request)
        //{
        //    return Mediator.Send(request);
        //}
        [HttpGet("getAllUsers")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Users)]
        [OpenApiOperation("Get all user details.", "")]
        public Task<PagedResponse<UserDetailsDto, UserDetailsCountDto>> SearchAsync([FromQuery] GetAllUserInfoViewRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpGet("user-view")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Users)]
        [OpenApiOperation("Get all user details.", "")]
        public Task<PagedResponse<UserDetailsDto, UserDetailsCountDto>> SearchViewAsync([FromQuery] GetAllUserInfoViewRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpGet("lead-count")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Users)]
        [OpenApiOperation("Get all user's leads count.", "")]
        public Task<PagedResponse<UserLeadCountDto, string>> GetLeadCountAsync([FromQuery] GetLeadCountForAllUsersRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpGet("lead-count/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Users)]
        [OpenApiOperation("Get all leads count.", "")]
        public Task<Response<int>> GetLeadCountAsync(Guid id)
        {
            GetLeadsCountByUserRequest request = new(id);
            return Mediator.Send(request);
        }

        [HttpGet("user-view/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Users)]
        [OpenApiOperation("Get user by id.", "")]
        public Task<Response<UserDetailsDto>> GetViewAsync(Guid id)
        {
            GetFullUserViewByIdRequest request = new(id);
            return Mediator.Send(request);
        }

        [HttpGet("getUsersByRoleId")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Users)]
        [OpenApiOperation("Get users by role id.", "")]
        public Task<PagedResponse<ViewUserDto, string>> GetUsersByRoleIdAsync([FromQuery] GetUsersByRoleIdRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpGet]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.ViewForFilter, LrbResource.Users)]
        [OpenApiOperation("Get all users.", "")]
        public Task<PagedResponse<ViewUserDto, string>> SearchAsync([FromQuery] GetAllUsersRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpGet("EngageToUsers")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.ViewForFilter, LrbResource.Users)]
        [OpenApiOperation("Get all users.", "")]
        public Task<PagedResponse<EngageToUserDto, string>> GetAllEngageToUsers([FromQuery] GetAllEngageToUserInfoRequest request)
        {
            return Mediator.Send(request);
        }


        [HttpPut("{id:guid}")]
        [MustHavePermission(LrbAction.Update, LrbResource.Users)]
        [TenantIdHeader]
        [OpenApiOperation("Update a user.", "")]
        public async Task<ActionResult<Guid>> UpdateAsync(UpdateUserDetailsRequest request, Guid id)
        {
            return id != request.UserId
                ? BadRequest()
                : Ok(await Mediator.Send(request));
        }
        [HttpDelete("{userId:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Delete, LrbResource.Users)]
        [OpenApiOperation("Delete a user.", "")]
        public Task<Response<UserDataCountDto>> DeleteAsync(Guid userId)
        {
            return Mediator.Send(new DeleteUserRequest(userId));
        }
        [HttpPost("department")]
        [TenantIdHeader]
        [OpenApiOperation("Create a new department.", "")]
        public Task<Response<Guid>> CreateAsync(CreateDepartmantRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpGet("getAllDepartments")]
        [TenantIdHeader]
        [OpenApiOperation("Get all department details.", "")]
        public Task<PagedResponse<DepartmantDto, string>> SearchAsync([FromQuery] GetAllDepartmantRequset request)
        {
            return Mediator.Send(request);
        }

        [HttpDelete("department/{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Delete a department.", "")]
        public Task<Response<bool>> DeleteDepartmentAsync(Guid id)
        {
            return Mediator.Send(new DeleteDepartmentRequest(id));
        }

        [HttpPost("designation")]
        [TenantIdHeader]
        [OpenApiOperation("Create a new designation.", "")]
        public Task<Response<Guid>> CreateAsync(CreateDesignationRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpGet("getAllDesignations")]
        [TenantIdHeader]
        [OpenApiOperation("Get all designation details.", "")]
        public Task<PagedResponse<DesignationDto, string>> SearchAsync([FromQuery] GetAllDesignationRequset request)
        {
            return Mediator.Send(request);
        }

        [HttpDelete("designation/{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Delete a designation.", "")]
        public Task<Response<bool>> DeleteDesignationAsync(Guid id)
        {
            return Mediator.Send(new DeleteDesignationRequest(id));
        }

        //[AllowAnonymous]
        //[HttpGet("{id:guid}")]
        //[TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Users)]
        //[OpenApiOperation("Get user details.", "")]
        //public async Task<Response<UserDetailsDto>> GetAsync(Guid id)
        //{
        //    return await Mediator.Send(new GetUserDetailsByIdRequest(id));
        //}

        [AllowAnonymous]
        [HttpGet("{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Users)]
        [OpenApiOperation("Get user details.", "")]
        public async Task<Response<UserDetailsDto>> GetAsync(Guid id)
        {
            return await Mediator.Send(new GetFullUserViewByIdRequest(id));
        }

        [HttpDelete("document/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Users)]
        [OpenApiOperation("Delete a document.", "")]
        public Task<Response<bool>> DeleteDocumentAsync(Guid id)
        {
            return Mediator.Send(new DeleteDocumentRequest(id));
        }

        [HttpPut("document/{userId:guid}")]
        [MustHavePermission(LrbAction.Update, LrbResource.Users)]
        [TenantIdHeader]
        [OpenApiOperation("Update a document.", "")]
        public async Task<ActionResult<Response<Guid>>> UpdateAsync(UpdateDocumentRequest request, Guid userId)
        {
            return userId != request.UserId
                ? BadRequest()
                : Ok(await Mediator.Send(request));
        }

        [HttpGet("BasicDetails/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get user details.", "")]
        public async Task<Response<UserBasicDetailsDto>> GetUserAsync(Guid id)
        {
            _ = this.HttpContext.Request.Headers.TryGetValue("tenant", out StringValues tenantId);
            return await Mediator.Send(new GetBasicUserDetailesRequest(id,tenantId));
        }
        [HttpPut("image")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.UserProfile)]
        [OpenApiOperation("Update user image.", "")]
        public Task<Response<Guid>> UpdateAsync([FromBody] string? imgUrl)
        {
            return Mediator.Send(new UpdateUserImgRequest(imgUrl));
        }
        [HttpPost("profileCompletion")]
        [TenantIdHeader]
        [OpenApiOperation("Get user profile completion.", "")]
        public async Task<Response<int>> GetAsync(GetProfileCompletionRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("reportees")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.ViewForFilter, LrbResource.Users)]
        [OpenApiOperation("Get All Reportees.", "")]
        public async Task<PagedResponse<ReportUserDto, string>> GetReportees([FromQuery] GetReporteesRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPut("toggle-automation")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Users)]
        [OpenApiOperation("Toggle a user's automation status.", "")]
        public async Task<Response<bool>> ToggleAutomationAsync([FromQuery] ToggleAutomationRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("adminsandreportees")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.ViewForFilter, LrbResource.Users)]
        [OpenApiOperation("Get All admins and reportees.", "")]
        public async Task<PagedResponse<ReportUserDto, string>> GetAdminsAndReporteesAsync()
        {
            return await Mediator.Send(new GetReporteesAndAdminsRequest());
        }

        [HttpGet("admin-mfa")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Users)]
        [OpenApiOperation("Get All admins and mfa enabled users.", "")]
        public async Task<Response<MFAEnabledUserDto>> GetAdminsAndMFAEnabledUsersAsync()
        {
            return await Mediator.Send(new GetAdminsAndMFAEnabledUsersRequest());
        }

        [HttpGet("allUsersToAssign")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.AssignToAny, LrbResource.Users)]
        [OpenApiOperation("Get All users To Assign.", "")]
        public async Task<PagedResponse<ReportUserDto, string>> GetAllUsersAsync([FromQuery] GetAllUsersToAssignRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("assignments/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Users)]
        [OpenApiOperation("Get All assignments by id.", "")]
        public async Task<Response<UserAssignmentsDto>> GetAssignmentsAsync(Guid id)
        {
            return await Mediator.Send(new GetAllTypeOfAssignmentsRequest() { Id = id });
        }

        [HttpDelete("assignments")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Users)]
        [OpenApiOperation("Delete All assignments by id.", "")]
        public async Task<Response<bool>> DeleteAssignmentsAsync(DeleteAssignmentsOfUesrRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("user-assignments/{id:guid}")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Users)]
        [OpenApiOperation("Get all user assignments by id.", "")]
        public async Task<Response<AggregatedUserAssignmentsUserBasedDto>> GetUserAssignmentsAsync(Guid id)
        {
            return await Mediator.Send(new GetUserAssignmentsByUserIdRequest(id));
        }

        [HttpDelete("user-assignments")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Users)]
        [OpenApiOperation("Delete All assignments by id.", "")]
        public async Task<Response<bool>> DeleteAssignmentsAsync(DeleteUserAssignmentsByUserIdRequest request)
        {
            return await Mediator.Send(request);
        }

        [AllowAnonymous]
        [HttpPost("bulkForAdmin")]
        [TenantIdHeader]
        [OpenApiOperation("Create a new bulk Users for Admin.", "")]
        public Task<Response<bool>> CreateBulkUserAsync(IFormFile file)
        {
            return Mediator.Send(new CreateBulkUsersRequest(file, GetOriginFromRequest()));
        }
        private string GetOriginFromRequest() => $"{Request.Scheme}://{Request.Host.Value}{Request.PathBase.Value}";

        [HttpGet("onlyReportees")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.ViewForFilter, LrbResource.Users)]
        [OpenApiOperation("Get only reportees.", "")]
        public async Task<PagedResponse<ReportUserDto, string>> GetAllUsersAsync()
        {
            return await Mediator.Send(new GetOnlyReporteesRequest());
        }
        [HttpGet("onlyReportees/withInactiveUsers")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.ViewForFilter, LrbResource.Users)]
        [OpenApiOperation("Get only reportees including inactive users.", "")]
        public async Task<PagedResponse<ReportUserDto, string>> GetAllReporteesWithInactiveUsersAsync()
        {
            return await Mediator.Send(new GetAllReporteesWithInactiveUsersRequest());
        }
        [HttpPut("bulk")]
        [MustHavePermission(LrbAction.BulkUpdate, LrbResource.Users)]
        [TenantIdHeader]
        [OpenApiOperation("Bulk Update users.", "")]
        public async Task<ActionResult<Guid>> BulkpdateAsync(BulkUpdateAllUserDeltaisrequest request)
        {
            return Ok(await Mediator.Send(request));
        }
        [HttpPut("bulk/userdetails")]
        [MustHavePermission(LrbAction.Update, LrbResource.Users)]
        [TenantIdHeader]
        [OpenApiOperation("Bulk Update users details.", "")]
        public async Task<ActionResult<Guid>> BulkpdateUserDetailsAsync(BulkUpdateUserDetailsRequest request)
        {
            return Ok(await Mediator.Send(request));
        }

        [HttpGet("settings/call")]
        [TenantIdHeader]
        [OpenApiOperation("Get filtered settings.", "")]
        public async Task<Response<GetFilteredSettingsDto>> GetAsync()
        {
            return await Mediator.Send(new GetFilteredSettingsRequest());
        }

        [HttpGet("settings/call-through")]
        [TenantIdHeader]
        [OpenApiOperation("Get call through type by user id.", "")]
        public async Task<Response<UserChannelSettingsDto>> GetAsync([FromQuery] GetCallThroughTypeRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPut("settings/bulk")]
        [TenantIdHeader]
        [OpenApiOperation("Update users settings.", "")]
        public async Task<Response<bool>> UpdateAsync(BulkSettingsUpdateRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPut("bulk/roles")]
        [TenantIdHeader]
        [OpenApiOperation("Bulk user Roles Update.", "")]
        public async Task<ActionResult<Response<Guid>>> UpdateBulkUserRolesAsync(BulkUpdateUserRolesRequest request)
        {
            return Ok(await Mediator.Send(request));
        }
   
        [HttpPost("export/batch")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Export, LrbResource.Users)]
        [OpenApiOperation("export users by excel.", "")]
        public Task<Response<Guid>> ExportUserAsync(RunAWSBatchForUserReportRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpGet("export/trackers")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Users)]
        [OpenApiOperation("Get all export users trackers", "")]
        public async Task<PagedResponse<ExportUserTrackerDto, string>> GetAllUserTrackers([FromQuery] GetUserReportTrackerRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("without/admin")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Users)]
        [OpenApiOperation("Get all users without admin role .", "")]
        public Task<PagedResponse<ViewUserDto,int>> SearchUsersAsync([FromQuery] GetAllUsersWithoutAdminRoleRequest request)
        {
            return Mediator.Send(request);
        }
       
        [HttpGet("data/{userid:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Users)]
        [OpenApiOperation("Get data of the assigned user.", "")]
        public Task<Response<UserDataCountDto>> GetDataAsync(Guid userid)
        {
            return Mediator.Send(new GetDataCountsOfUserRequest(userid));
        }
        [HttpPost("UserDelete")]
        [TenantIdHeader]
        [OpenApiOperation("User Delete Tracker", "")]
        public async Task<Response<Guid>> GetReportAsync(RunAWSBatchsForUserDeleteRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("delete/user/trackers")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Users)]
        [OpenApiOperation("Get all export users trackers", "")]
        public async Task<PagedResponse<UserDeletedTrackerDto, string>> GetAllDeletUserTrackers([FromQuery] GetDeleteUsersTrackerRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("generalManagers")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.ViewForFilter, LrbResource.Users)]
        [OpenApiOperation("Get All GeneralManagers.", "")]
        public async Task<PagedResponse<GeneralManagerUserDto, string>> GetGeneralManagers()
        {
            return await Mediator.Send(new GetGenaralManagersRequest());
        }
        [HttpGet("getallusers/designation")]
        [TenantIdHeader]
        [OpenApiOperation("Get All Users Based on these designations.", "")]
        public async Task<Response<Dictionary<string, List<UserViewDto>>>> GetAllUsers()
        {
            return await Mediator.Send(new GetAllUsersRequestinDashBoard());

        }

        [HttpPost("excel")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Users)]
        [OpenApiOperation("create new Users from Excel", "")]
        public async Task<Response<FileColumnUserDto>> UploadExcelasync(IFormFile file)
        {
            try
            {
                return await Mediator.Send(new GetExcelUploadUserRequest(file, GetOriginFromRequest()));
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        [HttpPost("bulk/user")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.BulkUpload, LrbResource.Prospects)]
        [OpenApiOperation("create new User from Excel", "")]
        public async Task<Response<BulkUserUploadTrackerDto>> CreateBulkUserAsync(string S3BucketKey,string SheetName)
        {
            return await Mediator.Send(new RunAWSBatchForBulkUserUploadRequest(S3BucketKey, SheetName, GetOriginFromRequest()));

        }
        [HttpGet("import/trackers")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Users)]
        [OpenApiOperation("Get trackers", "")]
        public async Task<PagedResponse<BulkUserUploadTrackerDto, string>> GetTrackers([FromQuery] GetImportUsersTrackerRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPut("settings/Whatsapp")]
        [TenantIdHeader]
        [OpenApiOperation("Update users settings.", "")]
        public async Task<Response<bool>> UpdateWhastappSettingsAsync(WhatsappSettingsUpdateRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("settings/whatsapp")]
        [TenantIdHeader]
        [OpenApiOperation("Get whatsapp filtered settings.", "")]
        public async Task<Response<GetWhatsappFilteredSettingsDto>> GetWhatsappAsync()
        {
            return await Mediator.Send(new GetWhatsappFilteredSettingsRequest());
        }
        [HttpGet("getAllUsersWithRoles")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Users)]
        [OpenApiOperation("Get all user details with roles.", "")]
        public Task<Response<List<UserDetailsWithRoleDto>>> GetUsersAsync()
        {
            return Mediator.Send(new GetAllUsersWithRolesRequest());
        }
        [HttpPost("geofence-settings")]
        [HttpPut("geofence-settings")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.SetGeoFence, LrbResource.Users)]
        [OpenApiOperation("Configure geo-fencing settings for a user", "")]
        public async Task<Response<bool>> CreateGeofenceSettings([FromBody] GeofenceSettingsRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("geofence-details/{userId:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.SetGeoFence, LrbResource.Users)]
        [OpenApiOperation("Get user details for clock-in including geo-fencing settings", "")]
        public async Task<Response<UserDetailsClockInDto>> GetUserDetailsForClockIn(Guid userId)
        {
            var request = new GetUserDetailsClockInRequest { UserId = userId };
            return await Mediator.Send(request);
        }

        [HttpPut("geofence-active-status")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.SetGeoFence, LrbResource.Users)]
        [OpenApiOperation("Update geo-fencing active status for a user", "")]
        public async Task<Response<bool>> UpdateGeofenceActiveStatus([FromBody] UpdateGeofenceActiveStatusRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPut]
        [TenantIdHeader]
        [OpenApiOperation("Create or  update search results.", "")]
        public Task<Response<bool>> UpdateSearchResultsAsync(UpdateUserSearchResultsRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpGet("searchresults")]
        [TenantIdHeader]
        [OpenApiOperation("Get all User searchresults.", "")]
        public Task<Response<List<PropertyListsDto>>> GetSearchResultsAsync([FromQuery]GetUserSearchResultsRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpPut("toggle-automation/tenant")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Users)]
        [OpenApiOperation("Toggle a user's automation status as per tenant.", "")]
        public async Task<Response<bool>> ToggleAutomationAsync([FromQuery] ToggleAutomatioPerTenantRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("AllAdminUsers")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Users)]
        [OpenApiOperation("Get all admin user details.", "")]
        public Task<PagedResponse<ReportUserDto, int>> GetAllAdminUsersAsync()
        {
            return Mediator.Send(new GetAllAdminUsersRequest());
        }
    }
}
