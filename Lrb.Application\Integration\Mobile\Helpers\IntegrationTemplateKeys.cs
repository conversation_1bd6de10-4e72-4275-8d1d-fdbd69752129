﻿namespace Lrb.Application.Integration.Mobile
{
    public class IntegrationTemplateKeys
    {
        public const string HeaderKey = "Details Required";
        public const string CRMNameKey = "CRM Name";
        public const string PushIntegrationTypeKey = "Push Integration Type";
        public const string APIKey = "API-Key";
        public const string Tenant = "tenant";
        public const string WorkingEndpointOpenUrlKey = "Working Endpoint URL";
        public const string PayloadKey = "Parameters to be passed with values";
        public const string SuccessReponseMessageSuccessKey = "Success Response Message on lead sharing";
        public const string FailReponseMessageSuccessKey = "Faliure Response Message on lead sharing";
        public const string AuthenticationTypeKey = "Authentication Type";
        public const string IntegrationMannualKey = "Integration Mannual";
        public const string Method = "Method";
    }
    public static class IntegrationTemplateValues
    {
        public const string HeaderValue = "Sample";
        public const string CRMNameValue = "LeadRat";
        public const string PushIntegrationTypeValue = "POST";
        public const string DevWorkingEndpointOpenUrlValue = "https://connect.leadratd.com/api/v{0}/integration/{1}";
        public const string QAWorkingEndpointOpenUrlValue = "https://connect.leadrat.info/api/v{0}/integration/{1}";
        public const string PrdWorkingEndpointOpenUrlValue = "https://connect.leadrat.com/api/v{0}/integration/{1}";
        public const string SuccessReponseMessageSuccessValue = "Success: 200 OK";
        public const string FailReponseMessageSuccessValue = "Faliure: 400 Bad Request";
        public const string AuthenticationTypeValue = "No Authentication";
        public const string IntegrationMannualValue = "CRM Integration doc for reference";
    }
    public static class WorkingEndPoints
    {
        public const string DevWorkingEndpointOpenUrlValue = "https://connect.leadratd.com/api/v1";
        public const string QAWorkingEndpointOpenUrlValue = "https://connect.leadrat.info/api/v1";
        public const string PrdWorkingEndpointOpenUrlValue = "https://connect.leadrat.com/api/v1";
    }
}
