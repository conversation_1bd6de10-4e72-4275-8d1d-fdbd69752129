﻿namespace Lrb.Application.DataManagement.Web.Export.Dtos
{
    public class ExportBaseProspectDto : IDto
    {
        public Guid Id { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public DateTime ReceivedOn { get; set; }
        public DateTime? ScheduledDate { get; set; }
        public List<NotesDetails>? NotesDetails { get; set; }
        public Guid AssignTo { get; set; }

    }

    public class ExportProspectDto : ExportBaseProspectDto
    {
        public string? Name { get; set; }
        public string? ContactNo { get; set; }
        public string? AlternateContactNo { get; set; }
        public string? DataSource { get; set; }
        public string? SubSource { get; set; }
        public string? Email { get; set; }
        public string? Status { get; set; }
        public string? Reason { get; set; }
        //public EnquiryType EnquiredFor { get; set; }
        public string? EnquiredTypes { get; set; }
        public string? PropertyType { get; set; }
        public string? PropertySubType { get; set; }
        //public BHKType BHKType { get; set; }
        public string? BHKTypes { get; set; }
        //public double NoOfBHK { get; set; }
        public string? BHKs { get; set; }
        public long? LowerBudget { get; set; }
        public long? UpperBudget { get; set; }
        public double? CarpetArea { get; set; }
        public string? CarpetAreaUnit { get; set; }
        public double Area { get; set; }
        public string? AreaUnit { get; set; }
        public DateTime? PossessionDate { get; set; }
        public string? SubLocality { get; set; }
        public string? Locality { get; set; }
        public string? District { get; set; }
        public string? City { get; set; }
        public string? State { get; set; }
        public string? Country { get; set; }
        public string? PostalCode { get; set; }
        public string? AssignedUserName { get; set; }
        public string? AssignedUserEmail { get; set; }
        public string? AssignedUserPhoneNumber { get; set; }
        public string? Projects { get; set; }
        public string? Properties { get; set; }
        public double? NetArea { get; set; }
        public string? NetAreaUnit { get; set; }
        public double? PropertyArea { get; set; }
        public string? PropertyAreaUnit { get; set; }
        public double? BuiltUpArea { get; set; }
        public string? BuiltUpAreaUnit { get; set; }
        public double? SaleableArea { get; set; }
        public string? SaleableAreaUnit { get; set; }
        public string? UnitName { get; set; }
        public string? ClusterName { get; set; }
        public string? Nationality { get; set; }
        public string? Purpose { get; set; }
        public string? Community { get; set; }
        public string? SubCommunity { get; set; }

        public string? CustomerLocality { get; set; }
        public string? CustomerCity { get; set; }
        public string? CustomerState { get; set; }
        public string? CustomerCountry { get; set; }
        public string? CustomerPostalCode { get; set; }
        public string? CustomerCommunity { get; set; }
        public string? CustomerSubCommunity { get; set; }
        public string? CustomerTowerName { get; set; }

        public string? TowerName { get; set; }
        public string? PossesionType { get; set; }
        public string? Beds { get; set; }
        public string? LandLine { get; set; }
        public string? Gender { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string? MaritalStatus { get; set; }
        public string? ChannelPartnerName { get; set; }
        public string? CampaignNames { get; set; }
        public string? ChannelPartnerMobile { get; set; }
        public string? ChannelPartnerEmail { get; set; }
        public string? AgencyName { get; set; }
        public DateTime? AnniversaryDate { get; set; }




    }
    public class NotesDetails
    {
        public Guid? ProspectId { get; set; }
        public string? Notes { get; set; }
        public DateTime? ModifiedOn { get; set; }
        public string? LastModifiedByUser { get; set; }
    }

}
