﻿using Lrb.Application.Reports.Web;
using Lrb.Application.Utils;
using Lrb.Shared.Extensions;

namespace Lrb.Application.Reports.Web
{
    public class GetDataStatusReportByAgenciesCountRequest : IRequest<int>
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public DataManagement.Web.Request.ProspectDateType? DateType { get; set; }
        public string? SearchText { get; set; }
        public List<Guid>? SourceIds { get; set; }
        public List<string>? SubSources { get; set; }
        public List<string>? AgencyNames { get; set; }
    }
}
public class GetDataStatusReportByAgenciesCountsRequestHandler : IRequestHandler<GetDataStatusReportByAgenciesCountRequest, int>
{
    private readonly IDapperRepository _dapperRepository;
    private readonly ICurrentUser _currentUser;
     
    public GetDataStatusReportByAgenciesCountsRequestHandler(IDapperRepository dapperRepository, ICurrentUser currentUser)
    {
        _dapperRepository = dapperRepository;
        _currentUser = currentUser;
    }
    public async Task<int> Handle(GetDataStatusReportByAgenciesCountRequest request, CancellationToken cancellationToken)
    {
        var tenantId = _currentUser.GetTenant();

        request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
        request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
        var count = (await _dapperRepository.QueryStoredProcedureCountFromReadReplicaAsync("LeadratBlack", "Data_GetDataCountReportByAgency", new
        {
            fromdate = request.FromDate,
            todate = request.ToDate,
            datetype = request.DateType,
            tenantid = tenantId,
            searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
            agencies = request?.AgencyNames?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
            subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
            sources = request?.SourceIds,
        }));
        return count;
    }
}
 