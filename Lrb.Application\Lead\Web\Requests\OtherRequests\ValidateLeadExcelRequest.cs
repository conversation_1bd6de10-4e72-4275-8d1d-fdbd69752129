﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.Utils;
using Lrb.Domain.Constants;
using Lrb.Domain.Entities.MasterData;
using System.Data;
using System.Text.RegularExpressions;

namespace Lrb.Application.Lead.Web.Requests
{
    public class ValidateLeadExcelRequest : IRequest<Response<string>>
    {
        public string? S3BucketKey { get; set; }
        public Dictionary<DataColumns, string>? MappedColumnsData { get; set; }
        public string? InvalidTagName { get; set; } = "Tag";
        public string SheetName { get; set; }

    }
    public class ValidateLeadExcelRequestHandler : IRequestHandler<ValidateLeadExcelRequest, Response<string>>
    {
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Lead> _leadRepository;
        private readonly IRepositoryWithEvents<ValidatedExcel> _excelRepo;
        private readonly IBlobStorageService _blobStorageService;
        private readonly ICurrentUser _currentUser;
        private readonly IUserService _userService;
        //private readonly IRepositoryWithEvents<MasterLeadStatus> _leadStatusRepo;
        public ValidateLeadExcelRequestHandler(IBlobStorageService blobStorageService,
            IRepositoryWithEvents<Lrb.Domain.Entities.Lead> leadRepository,
            ICurrentUser currentUser,
           IRepositoryWithEvents<ValidatedExcel> excelRepo,
           //IRepositoryWithEvents<MasterLeadStatus> leadStatusRepo,
           IUserService userService)
        {

            _blobStorageService = blobStorageService;
            _leadRepository = leadRepository;
            _currentUser = currentUser;
            _excelRepo = excelRepo;
            //_leadStatusRepo = leadStatusRepo;
            _userService = userService;

        }
        public async Task<Response<string>> Handle(ValidateLeadExcelRequest request, CancellationToken cancellationToken)
        {
            var existingLeads = await _leadRepository.ListAsync(new DuplicateLeadCheckSpec(), cancellationToken);
            var users = new List<Lrb.Application.Identity.Users.UserDetailsDto>(await _userService.GetListAsync(cancellationToken));
            //var leadStatuses = new List<MasterLeadStatus>(await _leadStatusRepo.ListAsync(cancellationToken));
            var existingContactNos = existingLeads.Select(i => i.ContactNo).ToList();
            DataTable dataTable = new();
            Stream fileStream = await _blobStorageService.GetObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", request.S3BucketKey);
            if (request.S3BucketKey.Split('.').LastOrDefault() == "csv")
            {
                using MemoryStream memoryStream = new();
                fileStream.CopyTo(memoryStream);
                dataTable = CSVHelper.CSVToDataTable(memoryStream);
            }
            else
            {
                //dataTable = ExcelHelper.ExcelToDataTable(fileStream);
                dataTable = EPPlusExcelHelper.ConvertExcelToDataTable(fileStream);
            }
            dataTable.Columns.Add("Tag", typeof(string));
            dataTable.Columns.Add("Created On", typeof(string));
            dataTable.Columns.Add("Existing Status", typeof(string));
            dataTable.Columns.Add("Existing SubStatus", typeof(string));
            dataTable.Columns.Add("Existing LeadSource", typeof(string));
            dataTable.Columns.Add("Existing Subsource", typeof(string));
            dataTable.Columns.Add("Assign To", typeof(string));
            int totalRows = dataTable.Rows.Count;
            for (int i = totalRows - 1; i >= 0; i--)
            {
                var row = dataTable.Rows[i];
                if (row.ItemArray.All(i => string.IsNullOrEmpty(i.ToString())))
                {
                    row.Delete();
                }
                else
                {
                    var contactNo = row[request.MappedColumnsData[DataColumns.ContactNo]].ToString();
                    var leadName = row[request.MappedColumnsData[DataColumns.Name]].ToString();
                    bool Isvalid = Regex.IsMatch(contactNo ?? string.Empty, RegexPatterns.IndianPhoneNumberPattern);
                    if (!Isvalid)
                    {
                        row["Tag"] = "Invalid Contact No";
                    }
                    else if (string.IsNullOrEmpty(leadName))
                    {
                        row["Tag"] = "Invalid Lead Name";
                    }
                    else if (!string.IsNullOrEmpty(leadName) && existingContactNos.Any(i => !string.IsNullOrWhiteSpace(contactNo) && contactNo.Length >= 10 && i.Contains(contactNo[^10..])))
                    {
                        row["Tag"] = "Duplicate Lead";
                        var lead = existingLeads.FirstOrDefault(i => i.ContactNo == contactNo);
                        if (lead != null)
                        {
                            var user = users.FirstOrDefault(i => i.Id == lead.AssignTo);
                            row["Assign To"] = user != null ? user.FirstName + " " + user.LastName : string.Empty;
                            //if (lead?.Status?.BaseId != null || lead?.Status?.BaseId != default)
                            //{
                            //    //row["Existing Status"] = leadStatuses?.FirstOrDefault(i => i.Id == lead?.Status?.BaseId)?.Status ?? string.Empty;
                            //    row["Existing SubStatus"] = lead?.Status?.DisplayName ?? string.Empty;
                            //}
                            //else
                            //{
                            //    row["Existing Status"] = lead?.Status?.DisplayName ?? string.Empty;
                            //}
                            row["Existing LeadSource"] = lead?.Enquiries?.FirstOrDefault()?.LeadSource.ToString() ?? string.Empty;
                            row["Existing Subsource"] = lead?.Enquiries?.FirstOrDefault()?.SubSource;
                        }
                    }
                    else
                    {
                        row["Tag"] = "Valid";
                    }
                }
            }
            if (dataTable.Rows.Count <= 0)
            {
                throw new Exception("Excel sheet is empty. Please fill some data in the excel sheet template.");
            }
            var tenantId = _currentUser.GetTenant();
            var stream = EPPlusExcelHelper.GenarateExcelbyDataTable(dataTable);
            var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", $"ValidatedExcel/{tenantId ?? "Default"}", $"Report-{DateTime.Now}.xlsx", stream);
            var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
            ValidatedExcel ExcelDetails = new()
            {
                ExcelS3BucketKey = request.S3BucketKey,
                ValidatedExcelS3BucketKey = key,
                SheetName = request.SheetName
            };
            try
            {
                await _excelRepo.AddAsync(ExcelDetails);
            }
            catch (Exception ex) { }
            return new(presignedUrl, null);
        }
    }

}
