{"AWSSettings": {"Region": "ap-south-1", "AWSAccessToken": "********************", "AWSSecret": "XcDOxHtp//5J0eHMc/TL1vKL61P4imzQ4cJs/cpi", "AWSS3BucketName": "dleadrat-black", "PinpointApplicationId": "dde7a98c75494c18916f099151749059"}, "ExcelUploadBatchJobSettings": {"BatchJobDefination": "aws_batch_poc_excel-job-definition-dev", "BatchJobQueue": "aws_batch_poc_excel_job_queue_config-dev"}, "AwsSecretManagerSettings": {"Region": "ap-south-1", "AWSAccessToken": "********************", "AWSSecret": "cFkS7v131SaaMLAmaFKBLXAUfU/+TpGOmF/PTAs8", "SecretsManagerName": "BaseUrls"}, "DynamoDbAWSSettings": {"Region": "ap-south-1", "AWSAccessToken": "********************", "AWSSecret": "cFkS7v131SaaMLAmaFKBLXAUfU/+TpGOmF/PTAs8"}}