﻿using Newtonsoft.Json;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Net;
using System.Net.Sockets;
using System.Reflection;

namespace Lrb.Shared.Utils
{
    public static class QueryGenerator
    {
        public static string GenerateInsertQuery(string schemaName, string tableName, List<string> columnNames)
        {
            // Generate the columns list as a comma-separated string (e.g., "Column1, Column2, Column3")
            string columns = string.Join(", ", columnNames.Select(i => $"\"{i}\""));

            // Generate the values list as a parameterized string (e.g., "@Column1, @Column2, @Column3")
            string values = string.Join(", ", columnNames.Select(c => "@" + c));

            // Return the complete INSERT INTO query
            return $"INSERT INTO \"{schemaName}\".\"{tableName}\" ({columns}) VALUES ({values});";
        }
        public static string GenerateInsertQuery<T>(string schemaName, string tableName, List<string> columnNames, IEnumerable<T> data)
        {
            // Generate the columns list as a comma-separated string (e.g., "Column1, Column2, Column3")
            string columns = string.Join(", ", columnNames.Select(i => $"\"{i}\""));

            // Generate the VALUES clause dynamically for each record in the data
            try
            {
                var valueClauses = data.Select(record =>
                {
                    var values = columnNames.Select(col =>
                    {
                        var propValue = typeof(T).GetProperty(col)?.GetValue(record);

                        // Handle null values and format strings with single quotes
                        if (propValue == null) return "NULL";
                        return propValue is string or DateTime
                            ? $"'{propValue.ToString()?.Replace("'", "''")}'" // Escape single quotes
                            : propValue.ToString();
                    });

                    return $"({string.Join(", ", values)})";
                });
                // Combine all values into the VALUES clause
                string valuesClause = string.Join(", ", valueClauses);

                // Return the complete INSERT INTO query
                return $"INSERT INTO \"{schemaName}\".\"{tableName}\" ({columns}) VALUES {valuesClause};";

            }
            catch (Exception ex)
            {

            }
            return string.Empty;
        }
        public static string AddressUpdateQuery(string schemaName, string tableName, Guid locationId,Guid id)
        {
            return $"UPDATE \"{schemaName}\".\"{tableName}\" SET \"LocationId\" ='{locationId}' WHERE \"Id\"='{id}';";

        }
        public static string AddressUpdateQueryForMultiple<T>(string schemaName, string tableName, IEnumerable<T> addresses)
        {
            var addressUpdateCases = addresses.Select(address =>
            {
                var idProperty = typeof(T).GetProperty("Id");
                var locationIdProperty = typeof(T).GetProperty("LocationId");

                var id = idProperty.GetValue(address);
                var locationId = locationIdProperty.GetValue(address);

                return $"WHEN \"Id\" = '{id}' THEN '{locationId}'::uuid";
            });

            var addressIds = string.Join(", ", addresses.Select(address =>
            {
                var idProperty = typeof(T).GetProperty("Id");
                var id = idProperty.GetValue(address);

                return $"'{id}'";
            }));

            var query = $"UPDATE \"{ schemaName}\".\"{tableName}\"SET \"LocationId\" = CASE {string.Join(" ", addressUpdateCases)} END WHERE \"Id\" IN ({addressIds});";

            return query;
        }

        public static string GenerateLeadsUpdateQuery<T>(string schemaName, string tableName, IEnumerable<T> entities)
        {
            // Build the CASE statement for updating the column
            var updateCases = entities.Select(entity =>
            {
                var idProperty = typeof(T).GetProperty("Id");
                var updateProperty = typeof(T).GetProperty("ChildLeadsCount");

                var id = idProperty?.GetValue(entity);
                var updateValue = updateProperty?.GetValue(entity);

                return $"WHEN \"Id\" = '{id}' THEN {updateValue}";
            });

            // Collect all IDs for the WHERE clause
            var ids = string.Join(", ", entities.Select(entity =>
            {
                var idProperty = typeof(T).GetProperty("Id");
                return $"'{idProperty?.GetValue(entity)}'";
            }));

            // Generate the final query
            var query = $" UPDATE \"{ schemaName}\".\"{tableName}\" SET \"ChildLeadsCount\" = CASE { string.Join(" ", updateCases)} END WHERE \"Id\" IN ({ids});";

            return query;
        }
        public static string GenerateDeleteByLeadIdsQuery(string schemaName, string tableName, List<Guid> leadIds)
        {
            if (leadIds == null || !leadIds.Any())
                return string.Empty; // Return empty query if no IDs are provided

            // Format the LeadIds for the SQL IN clause
            var idsArray = "ARRAY[" + string.Join(", ", leadIds.Select(id => $"'{id}'::UUID")) + "]";

            // Get the current timestamp for the "DeletedOn" column
            var currentTimestamp = "CURRENT_TIMESTAMP";

            // Generate the final query
            var query = $"UPDATE \"{ schemaName}\".\"{tableName}\" SET \"IsDeleted\" = TRUE, \"DeletedOn\" = {currentTimestamp}  WHERE \"LeadId\" = ANY({idsArray}) AND \"IsDeleted\" = false;";

            return query;
        }
        public static string GenerateResotreByIdsQuery(string tenantId, string schemaName, string tableName, List<Guid> ids)
        {
            if (ids == null || !ids.Any())
                return string.Empty; // Return empty query if no IDs are provided

            // Format the LeadIds for the SQL IN clause
            var idsArray = "ARRAY[" + string.Join(", ", ids.Select(id => $"'{id}'::UUID")) + "]";

            // Generate the final query
            var query = $"UPDATE \"{schemaName}\".\"{tableName}\" SET \"IsArchived\" = false WHERE \"Id\" = ANY({idsArray}) and \"TenantId\" = '{tenantId}'";

            return query;
        }



        public static string GenerateInsertLeadHistoryQuery<T>(string tenantId, string schemaName, string tableName, List<string> columnNames, IEnumerable<T> data)
        {
            // Generate the columns list as a comma-separated string (e.g., "Column1, Column2, Column3")
            columnNames.Add("TenantId");
            string columns = string.Join(", ", columnNames.Select(i => $"\"{i}\""));
            // Generate the VALUES clause dynamically for each record in the data
            var valueClauses = data.Select(record =>
            {
                var values = columnNames.Select(col =>
                {
                    if (col == "TenantId")
                    {
                        return $"'{tenantId}'";
                    }
                    var propInfo = typeof(T).GetProperty(col);
                    if (propInfo == null) return "NULL"; // Skip if the property is not found

                    var propValue = propInfo.GetValue(record);

                    // Handle null values
                    if (propValue == null) return "NULL";

                    if (propValue is DateTime dateTimeValue)
                    {
                        // Format DateTime in the desired format with UTC offset
                        return $"'{dateTimeValue.ToString("yyyy-MM-dd HH:mm:ss.ffffffzzz")}'";
                    }
                    if (propValue is Guid guidValue)
                    {
                        return $"'{guidValue}'";
                    }
                    if (propValue is string)
                    {
                        return $"'{propValue.ToString()?.Replace("'", "''")}'"; // Escape strings with quotes
                    }
                    if (propValue.GetType().IsPrimitive || propValue is decimal)
                    {
                        return propValue.ToString(); // Handle primitive types directly
                    }

                    // For all other types, serialize to JSON
                    var serializedValue = JsonConvert.SerializeObject(propValue);

                    //if (serializedValue.Equals("{\"1\":\" \"}") ||
                    //    serializedValue.Equals("{\"1\":\"0\"}") ||
                    //    serializedValue.Equals("{\"1\":\"None\"}") ||
                    //    serializedValue.Equals("{\"1\":\"null\"}") ||
                    //    serializedValue.Equals("{\"1\":null}") ||
                    //    serializedValue.Equals("{\"1\":0}") ||
                    //    serializedValue.Equals("{\"1\":}") ||
                    //    serializedValue.Equals("{\"1\":\"\"}") ||
                    //    serializedValue.Equals("{\"1\":0.0}") ||
                    //    serializedValue.Equals("{\"1\":false}"))
                    //{
                    //    return "NULL";
                    //}
                    return $"'{serializedValue.Replace("'", "''")}'"; // Escape single quotes in JSON
                });

                return $"({string.Join(", ", values)})";
            });

            // Combine all values into the VALUES clause
            string valuesClause = string.Join(", ", valueClauses);

            // Return the complete INSERT INTO query
            return $"INSERT INTO \"{schemaName}\".\"{tableName}\" ({columns}) VALUES {valuesClause};";
        }
        public static string GenerateUpdateLeadHistoryQuery<T>(string tenantId, string schemaName, string tableName, List<string> columnNames, IEnumerable<T> data)
        {
            var updateClauses = data.Select(record =>
            {
                var setClauses = columnNames.Select(col =>
                {
                    if (col == "TenantId") // Skip TenantId in SET clause
                    {
                        return string.Empty;
                    }
                    var propInfo = typeof(T).GetProperty(col);
                    if (propInfo == null) return string.Empty; // Skip if the property is not found

                    var propValue = propInfo.GetValue(record);

                    // Handle null values
                    if (propValue == null) return $"\"{col}\" = NULL";

                    if (propValue is DateTime dateTimeValue)
                    {
                        // Format DateTime in the desired format with UTC offset
                        return $"\"{col}\" = '{dateTimeValue.ToString("yyyy-MM-dd HH:mm:ss.ffffffzzz")}'";
                    }
                    if (propValue is Guid guidValue)
                    {
                        return $"\"{col}\" = '{guidValue}'";
                    }
                    if (propValue is string)
                    {
                        return $"\"{col}\" = '{propValue.ToString()?.Replace("'", "''")}'"; // Escape strings with quotes
                    }
                    if (propValue.GetType().IsPrimitive || propValue is decimal)
                    {
                        return $"\"{col}\" = {propValue}"; // Handle primitive types directly
                    }

                    // For all other types, serialize to JSON
                    var serializedValue = JsonConvert.SerializeObject(propValue);
                    return $"\"{col}\" = '{serializedValue.Replace("'", "''")}'"; // Escape single quotes in JSON
                })
                .Where(clause => !string.IsNullOrEmpty(clause)); // Remove empty clauses

                // Ensure 'Id' property exists and is not null
                var idPropInfo = typeof(T).GetProperty("Id");
                if (idPropInfo == null)
                {
                    throw new InvalidOperationException("The 'Id' property is required for updates.");
                }
                var idValue = idPropInfo.GetValue(record);
                if (idValue == null)
                {
                    throw new InvalidOperationException("The 'Id' value is required for updates.");
                }

                var whereClause = $"\"TenantId\" = '{tenantId}' AND \"Id\" = '{idValue}'";

                return $"UPDATE \"{schemaName}\".\"{tableName}\" SET {string.Join(", ", setClauses)} WHERE {whereClause};";
            });

            // Combine all update statements
            string updateStatements = string.Join(" ", updateClauses);

            // Log the update statements for debugging
            Console.WriteLine(updateStatements);

            // Return the complete UPDATE query
            return updateStatements;
        }


        public static string GenerateInsertQuery<T>(string tenantId, string schemaName, string tableName, List<string> columnNames, IEnumerable<T> data)
        {
            // Generate the columns list as a comma-separated string (e.g., "Column1, Column2, Column3")
            if (tenantId != null)
            {
                columnNames.Add("TenantId");
            }
            string columns = string.Join(", ", columnNames.Select(i => $"\"{i}\""));

            // Generate the VALUES clause dynamically for each record in the data
            var valueClauses = data.Select(record =>
            {
                var values = columnNames.Select(col =>
                {
                    if (col == "TenantId")
                    {
                        return $"'{tenantId}'";
                    }
                    var propInfo = typeof(T).GetProperty(col);
                    if (propInfo == null) return "NULL"; // Skip if the property is not found

                    var propValue = propInfo.GetValue(record);

                    // Handle null values
                    if (propValue == null) return "NULL";
                    if (col == "DateOfBirth" && propValue is DateTime dateValue)
                    {
                        return $"'{dateValue.ToString("yyyy-MM-dd HH:mm:ss")}'";
                    }
                    if (col == "AnniversaryDate" && propValue is DateTime anniversaryDateValue)
                    {
                        return $"'{anniversaryDateValue.ToString("yyyy-MM-dd HH:mm:ss")}'";
                    }
                    if (propValue is DateTime dateTimeValue)
                    {
                        // Format DateTime in the desired format with UTC offset
                        return $"'{dateTimeValue.ToString("yyyy-MM-dd HH:mm:ss.ffffffzzz")}'";
                    }
                    if (propValue is Guid guidValue)
                    {
                        return $"'{guidValue}'";
                    }
                    if (propValue is string)
                    {
                        return $"'{propValue.ToString()?.Replace("'", "''")}'"; // Escape strings with quotes
                    }
                    if (propValue.GetType().IsPrimitive || propValue is decimal)
                    {
                        return propValue.ToString(); // Handle primitive types directly
                    }
                    // For all other types, serialize to JSON
                    var serializedValue = JsonConvert.SerializeObject(propValue);
                    if(serializedValue.StartsWith("[") && serializedValue.EndsWith("]"))
                    {
                        return $"'{serializedValue.Replace("[", "{").Replace("]", "}").Replace("'", "''")}'"; // Escape single quotes in JSON
                    }
                    return $"'{serializedValue.Replace("'", "''")}'"; // Escape single quotes in JSON
                });

                return $"({string.Join(", ", values)})";
            });

            // Combine all values into the VALUES clause
            string valuesClause = string.Join(", ", valueClauses);
            // Return the complete INSERT INTO query

            return $"INSERT INTO \"{schemaName}\".\"{tableName}\" ({columns}) VALUES {valuesClause};";

        }
        public static string GenerateInsertQueryV1<T>(string tenantId, string schemaName, string tableName, List<string> columnNames, IEnumerable<T> data)
        {
            // Generate the columns list as a comma-separated string (e.g., "Column1, Column2, Column3")
            if (tenantId != null)
            {
                columnNames.Add("TenantId");
            }
            string columns = string.Join(", ", columnNames.Select(i => $"\"{i}\""));

            // Generate the VALUES clause dynamically for each record in the data
            var valueClauses = data.Select(record =>
            {
                var values = columnNames.Select(col =>
                {
                    if (col == "TenantId")
                    {
                        return $"'{tenantId}'";
                    }
                    var propInfo = typeof(T).GetProperty(col);
                    if (propInfo == null) return "NULL"; // Skip if the property is not found

                    var propValue = propInfo.GetValue(record);

                    // Handle null values
                    if (propValue == null) return "NULL";
                    if (col == "DateOfBirth" && propValue is DateTime dateValue)
                    {
                        return $"'{dateValue.ToString("yyyy-MM-dd HH:mm:ss")}'";
                    }
                    if (propValue is DateTime dateTimeValue)
                    {
                        // Format DateTime in the desired format with UTC offset
                        return $"'{dateTimeValue.ToString("yyyy-MM-dd HH:mm:ss.ffffffzzz")}'";
                    }
                    if (propValue is Guid guidValue)
                    {
                        return $"'{guidValue}'";
                    }
                    if (propValue is string)
                    {
                        return $"'{propValue.ToString()?.Replace("'", "''")}'"; // Escape strings with quotes
                    }
                    if (propValue.GetType().IsPrimitive || propValue is decimal)
                    {
                        return propValue.ToString(); // Handle primitive types directly
                    }
                    // For all other types, serialize to JSON
                    var serializedValue = JsonConvert.SerializeObject(propValue);
                    if (serializedValue.StartsWith("[") && serializedValue.EndsWith("]"))
                    {
                        return $"'{serializedValue.Replace("[", "{").Replace("]", "}").Replace("'", "''")}'"; // Escape single quotes in JSON
                    }
                    return $"'{serializedValue.Replace("'", "''")}'"; // Escape single quotes in JSON
                });

                return $"({string.Join(", ", values)})";
            });

            // Combine all values into the VALUES clause
            string valuesClause = string.Join(", ", valueClauses);

            // Return the complete INSERT INTO query

            return $"INSERT INTO \"{schemaName}\".\"{tableName}\" ({columns}) VALUES {valuesClause} ON CONFLICT ({string.Join(", ", columnNames.Select(c => $"\"{c}\""))}) DO NOTHING;";

        }

        public static string GenerateGetQuery(string schemaName, string tableName, Dictionary<string, string>? condition, string? arithmeticOperators, string? logicalOperator)
        {
            var setCondition = string.Empty;
            if ((condition?.Any() ?? false) && !string.IsNullOrEmpty(arithmeticOperators) && !string.IsNullOrEmpty(logicalOperator))
            {
                int count = 0;
                int totalConditions = condition.Count;
                foreach (var item in condition)
                {
                    count++;
                    setCondition += $"\"{item.Key}\" {arithmeticOperators} '{item.Value}' ";
                    if (count < totalConditions)
                    {
                        setCondition += $" {logicalOperator} ";
                    }
                }
                return $"SELECT * FROM \"{schemaName}\".\"{tableName}\" WHERE {setCondition};";
            }
            else
            {
                return $"SELECT * FROM \"{schemaName}\".\"{tableName}\";";
            }
        }
        public static string GenerateCountQuery(string schemaName, string tableName, Dictionary<string, string>? condition, string? arithmeticOperators, string? logicalOperator)
        {
            var setCondition = string.Empty;
            if ((condition?.Any() ?? false) && !string.IsNullOrEmpty(arithmeticOperators) && !string.IsNullOrEmpty(logicalOperator))
            {
                int count = 0;
                int totalConditions = condition.Count;
                foreach (var item in condition)
                {
                    count++;
                    setCondition += $"\"{item.Key}\" {arithmeticOperators} '{item.Value}' ";
                    if (count < totalConditions)
                    {
                        setCondition += $" {logicalOperator} ";
                    }
                }
                return $"SELECT Count(*) FROM \"{schemaName}\".\"{tableName}\" WHERE {setCondition};";
            }
            else
            {
                return $"SELECT Count(*) FROM \"{schemaName}\".\"{tableName}\";";
            }
        }
        public static string GenerateUpdateQuery(string schemaName, string tableName, List<string> columnNames, Dictionary<string, string>? condition, string? arithmeticOperators, string? logicalOperator)
        {
            string setClause = string.Join(", ", columnNames.Select(c => $"\"{c}\"" + " = @" + c));
            var setCondition = string.Empty;
            if ((condition?.Any() ?? false) && !string.IsNullOrEmpty(arithmeticOperators) && !string.IsNullOrEmpty(logicalOperator))
            {
                int count = 0;
                int totalConditions = condition.Count;
                foreach (var item in condition)
                {
                    count++;
                    setCondition += $"\"{item.Key}\" {arithmeticOperators} '{item.Value}' ";
                    if (count < totalConditions)
                    {
                        setCondition += $" {logicalOperator} ";
                    }
                }
                return $"UPDATE \"{schemaName}\".\"{tableName}\" SET {setClause} WHERE {setCondition};";
            }
            else
            {
                return $"UPDATE \"{schemaName}\".\"{tableName}\" SET {setClause}";
            }
        }
        public static string V2GenerateUpdateQuery<T>(string tenantId, string schemaName, string tableName, List<string> columnNames, IEnumerable<T> data)
        {
            // Print out the data object to see its values
            Console.WriteLine($"Data objects: {JsonConvert.SerializeObject(data)}");

            var updateQueries = data.Select(record =>
            {
                var setClauses = columnNames.Select(col =>
                {
                    if (col == "TenantId")
                    {
                        return $"\"{col}\" = '{tenantId}'";
                    }

                    var propInfo = typeof(T).GetProperty(col);
                    if (propInfo == null)
                    {
                        Console.WriteLine($"Property {col} not found in {typeof(T).Name}");
                        return $"\"{col}\" = NULL"; // Skip if the property is not found
                    }

                    var propValue = propInfo.GetValue(record);

                    if (propValue == null) return $"\"{col}\" = NULL";

                    if (propValue is DateTime dateTimeValue)
                    {
                        return $"\"{col}\" = '{dateTimeValue.ToString("yyyy-MM-dd HH:mm:ss.ffffffzzz")}'";
                    }
                    if (propValue is Guid guidValue)
                    {
                        return $"\"{col}\" = '{guidValue}'";
                    }
                    if (propValue is string)
                    {
                        return $"\"{col}\" = '{propValue.ToString()?.Replace("'", "''")}'"; // Escape strings with quotes
                    }
                    if (propValue.GetType().IsPrimitive || propValue is decimal)
                    {
                        return $"\"{col}\" = {propValue}"; // Handle primitive types directly
                    }

                    var serializedValue = JsonConvert.SerializeObject(propValue);
                    if (serializedValue.StartsWith("[") && serializedValue.EndsWith("]"))
                    {
                        return $"\"{col}\" = '{serializedValue.Replace("[", "{").Replace("]", "}").Replace("'", "''")}'"; // Escape single quotes in JSON
                    }
                    return $"\"{col}\" = '{serializedValue.Replace("'", "''")}'"; // Escape single quotes in JSON
                });

                string setClause = string.Join(", ", setClauses);

                // Assuming you have a primary key column named "Id" for the WHERE clause
                string idColumn = "Id";
                var idProperty = typeof(T).GetProperty(idColumn);
                if (idProperty == null)
                {
                    Console.WriteLine($"Property {idColumn} not found in {typeof(T).Name}");
                }
                var idValue = idProperty.GetValue(record);
                if (idValue == null)
                {
                    Console.WriteLine($"Id value cannot be null for updating a record in {schemaName}.{tableName}");
                }

                // Return the complete UPDATE query
                return $"UPDATE \"{schemaName}\".\"{tableName}\" SET {setClause} WHERE \"{idColumn}\" = '{idValue}';";
            });

            // Combine all queries into a single batch separated by semicolons
            string batchQuery = string.Join(" ", updateQueries);
            return batchQuery;
        }

        public static string GenerateDynamicUpdateQueryForManyToMany(string schemaName, string tableName, List<string> columnNames, string keyColumnName, string valueColumnName, IEnumerable<object> data)
        {
            var upsertQueries = data.Select(record =>
            {
                // Retrieve the key and value from the object
                var keyValue = record.GetType().GetProperty(keyColumnName)?.GetValue(record)?.ToString().Replace("'", "''");
                var value = record.GetType().GetProperty(valueColumnName)?.GetValue(record)?.ToString().Replace("'", "''");

                // Ensure both keyValue and value are not null
                if (keyValue == null || value == null)
                {
                    Console.WriteLine($"Both {keyColumnName} and {valueColumnName} must have valid values.");
                }
                // Generate the upsert query
                return $@"
                    DO $$
                    BEGIN
                        -- Check if there are duplicate rows for the same key
                        IF EXISTS (
                            SELECT 1 FROM ""{schemaName}"".""{tableName}""
                            WHERE ""{keyColumnName}"" = '{keyValue}'
                            ) THEN
                            -- Delete all duplicate rows except one
                            DELETE FROM ""{schemaName}"".""{tableName}""
                            WHERE ""{keyColumnName}"" = '{keyValue}'
                            AND ctid NOT IN (
                                SELECT ctid FROM ""{schemaName}"".""{tableName}""
                                WHERE ""{keyColumnName}"" = '{keyValue}'
                                LIMIT 1
                            );

                            -- Update the remaining row
                            UPDATE ""{schemaName}"".""{tableName}""
                            SET ""{valueColumnName}"" = '{value}'
                            WHERE ""{keyColumnName}"" = '{keyValue}';
                        ELSE
                            -- Insert a new row if no rows exist
                            INSERT INTO ""{schemaName}"".""{tableName}"" (""{keyColumnName}"", ""{valueColumnName}"")
                            VALUES ('{keyValue}', '{value}');
                        END IF;
                    END $$;";
            });

            return string.Join(" ", upsertQueries);
        }
        public static List<string> GetMappedPropertiesV2<T>()
        {
            return typeof(T).GetProperties()
                .Where(prop => !prop.GetCustomAttributes<NotMappedAttribute>().Any() // Ignore NotMapped properties
                    && (prop.PropertyType.IsPrimitive // Primitive types (int, double, etc.)
                        || prop.PropertyType.IsValueType // Structs (DateTime, Guid, etc.)
                        || prop.PropertyType == typeof(string))) // Strings
                .Select(prop => prop.Name)
                .ToList();
        }
        public static List<string> GetMappedProperties<T>()
        {
            return typeof(T).GetProperties()
                .Where(prop => !prop.GetCustomAttributes<NotMappedAttribute>().Any())
                .Select(prop => prop.Name)
                .ToList();
        }
    }
}
