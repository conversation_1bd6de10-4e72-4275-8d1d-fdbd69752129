﻿namespace Lrb.Application.Attendance.Web.Specs
{
    public class UserLatestOpenAttendanceLogByTodaysDateSpec : Specification<AttendanceLog>
    {
        public UserLatestOpenAttendanceLogByTodaysDateSpec(Guid userId, DateTime fromDate, DateTime toDate)
        {
            //Query.Where(i => !i.IsDeleted
            //    && !i.IsClosed
            //    && i.UserId == userId
            //    && i.ClockInTime.Date == DateTime.UtcNow.Date);

            Query.Where(i => !i.IsDeleted && i.IsPunchInLocation != true
            && !i.IsClosed
            && i.UserId == userId
            && (i.ClockInTime >= fromDate && i.ClockInTime <= toDate));
        }
    }
}
