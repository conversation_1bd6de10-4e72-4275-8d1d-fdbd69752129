﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Common.GooglePlaces;
using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web;
using Lrb.Application.Utils;
using Lrb.Domain.Constants;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;
using PhoneNumbers;
using System.Data;
using System.Text.RegularExpressions;

namespace Lrb.Application.Lead.Mobile
{
    public class CreateBulkLeadRequest : IRequest<Response<bool>>
    {
        public string? S3BucketKey { get; set; }
        public Dictionary<DataColumns, string>? MappedColumnsData { get; set; }
    }
    public class CreateBulkLeadRequestHandler : IRequestHandler<CreateBulkLeadRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        private readonly IReadRepository<MasterAreaUnit> _masterAreaUnitRepo;
        private readonly IRepositoryWithEvents<LeadEnquiry> _leadEnquiryRepo;
        private readonly IRepositoryWithEvents<Address> _addressRepo;
        private readonly IRepositoryWithEvents<MasterPropertyType> _propertyTypeRepo;
        //private readonly IRepositoryWithEvents<MasterLeadStatus> _leadStatusRepo;
        private readonly IRepositoryWithEvents<PropertyDimension> _dimensionRepo;
        private readonly IRepositoryWithEvents<LeadTag> _leadTagRepo;
        //private readonly IReadRepository<MasterLeadStatus> _masterLeadStatusRepo;
        private readonly IBlobStorageService _blobStorageService;
        private readonly IRepositoryWithEvents<LeadHistory> _leadHistoryRepo;
        private readonly IGooglePlacesService _googlePlacesService;
        private readonly IUserService _userService;
        private readonly INotificationSenderService _notificationSenderService;
        private readonly INpgsqlRepository _npgsqlRepo;
        private readonly ICurrentUser _currentUser;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customLeadStatusRepo;

        public CreateBulkLeadRequestHandler(
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepo,
            //IReadRepository<MasterLeadStatus> masterLeadStatusRepo,
            IReadRepository<MasterAreaUnit> masterAreaUnitRepo,
            IRepositoryWithEvents<LeadTag> leadTagRepo,
            IRepositoryWithEvents<LeadEnquiry> leadEnquiryRepo,
            IRepositoryWithEvents<Address> addressRepo,
            IRepositoryWithEvents<MasterPropertyType> propertyTypeRepo,
            //IRepositoryWithEvents<MasterLeadStatus> leadStatusRepo,
            IRepositoryWithEvents<PropertyDimension> dimensionRepo,
            IBlobStorageService blobStorageService,
            IRepositoryWithEvents<LeadHistory> leadHistoryRepo,
            IGooglePlacesService googlePlacesService,
            IUserService userService,
            INotificationSenderService notificationSenderService,
            INpgsqlRepository npgsqlRepo,
            IRepositoryWithEvents<CustomMasterLeadStatus> customLeadStatusRepo,
            ICurrentUser currentUser,
            ILeadRepositoryAsync leadRepositoryAsync
            )
        {
            _leadRepo = leadRepo;
            _masterAreaUnitRepo = masterAreaUnitRepo;
            _leadEnquiryRepo = leadEnquiryRepo;
            _addressRepo = addressRepo;
            _propertyTypeRepo = propertyTypeRepo;
            //_leadStatusRepo = leadStatusRepo;
            _dimensionRepo = dimensionRepo;
            _leadTagRepo = leadTagRepo;
            //_masterLeadStatusRepo = masterLeadStatusRepo;
            _blobStorageService = blobStorageService;
            _leadHistoryRepo = leadHistoryRepo;
            _googlePlacesService = googlePlacesService;
            _userService = userService;
            _notificationSenderService = notificationSenderService;
            _npgsqlRepo = npgsqlRepo;
            _currentUser = currentUser;
            _leadRepositoryAsync = leadRepositoryAsync;
            _customLeadStatusRepo = customLeadStatusRepo;
        }
        public async Task<Response<bool>> Handle(CreateBulkLeadRequest request, CancellationToken cancellationToken)
        {
            DataTable dataTable = new();
            Stream fileStream = await _blobStorageService.GetObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", request.S3BucketKey);
            if (request.S3BucketKey.Split('.').LastOrDefault() == "csv")
            {
                using MemoryStream memoryStream = new MemoryStream();
                fileStream.CopyTo(memoryStream);
                dataTable = CSVHelper.CSVToDataTable(memoryStream);
            }
            else
            {
                dataTable = ExcelHelper.ExcelToDataTable(fileStream);
            }
            int totalRows = dataTable.Rows.Count;
            for (int i = totalRows - 1; i >= 0; i--)
            {
                var row = dataTable.Rows[i];
                if (row.ItemArray.All(i => string.IsNullOrEmpty(i.ToString())))
                {
                    row.Delete();
                }
            }
            if (dataTable.Rows.Count <= 0)
            {
                return new(false, "Excel sheet is empty. Please fill some data in the excel sheet template.");
            }
            List<CreateLeadRequest> invalidContactNumberLeads = new();
            List<CreateLeadRequest> leadRequests = dataTable.GetCreateLeadRequests(request.MappedColumnsData);
            foreach (CreateLeadRequest leadRequest in leadRequests)
            {
                if (string.IsNullOrEmpty(leadRequest.Name.Trim()))
                {
                    return new(false, "One or more required columned cells do not contain data, please fix your data sheet and try again.");
                }
                else
                {
                    leadRequest.Name = leadRequest.Name.Trim();
                }
                if (string.IsNullOrEmpty(leadRequest.ContactNo.Trim()))
                {
                    return new(false, "One or more required columned cells do not contain data, please fix your data sheet and try again.");
                }
                else if (leadRequest.ContactNo.Trim().Length < 1 || !Regex.IsMatch(leadRequest.ContactNo.Trim(), RegexPatterns.MobNoPattern))
                {
                    return new(false, "One or more required columned cells do not contain valid data, please fix your data sheet and try again.");
                }
                else
                {
                    leadRequest.ContactNo = leadRequest.ContactNo.Trim();
                }
                if (!string.IsNullOrEmpty(leadRequest.Email.Trim()))
                {
                    if (Regex.IsMatch(leadRequest.Email.Trim(), RegexPatterns.EmailPattern))
                    {
                        leadRequest.Email = leadRequest.Email.Trim();
                    }
                    else
                    {
                        return new(false, "One or more columned cells do not contain valid data, please fix your data sheet and try again.");
                    }
                }
                if(leadRequest.ContactNo.Length == 10)
                {
                    leadRequest.ContactNo = $"+91{leadRequest.ContactNo}";
                }
                var phoneUtil = PhoneNumberUtil.GetInstance();
                PhoneNumber number = phoneUtil.Parse(leadRequest.ContactNo, "");
                if (!phoneUtil.IsValidNumber(number))
                {
                    leadRequests.RemoveAll(i => i.ContactNo == leadRequest.ContactNo);
                    invalidContactNumberLeads.Add(leadRequest);
                }

            }
            List<string> contactsInRequests = leadRequests.Select(i => i.ContactNo).ToList();
            IEnumerable<Domain.Entities.Lead> existingLeads = new List<Domain.Entities.Lead>();
            existingLeads = await _leadRepo.ListAsync(new LeadByContactNoSpec(contactsInRequests), cancellationToken);
            List<string> existingContacts = existingLeads?.Select(i => i.ContactNo)?.ToList();
            foreach (var leadRequest in leadRequests)
            {
                if (!existingContacts.Contains(leadRequest.ContactNo))
                {
                  
                    List<MasterPropertyType>? propertyTypeses = null;
                    if (leadRequest.Enquiry?.PropertyTypeId != Guid.Empty && leadRequest.Enquiry?.PropertyTypeId != null)
                    {
                        propertyTypeses = await _propertyTypeRepo.ListAsync(new Lrb.Application.LeadCallLog.Mobile.GetMasterPropertyTypeSpec(leadRequest?.Enquiry?.PropertyTypeIds));

                    }
                    Domain.Entities.Lead lead = leadRequest.Adapt<Domain.Entities.Lead>();
                    //Address? address = null;
                    List<Address> addresses = new List<Address>();

                    //if (!string.IsNullOrWhiteSpace(leadRequest.Enquiry.Address?.PlaceId))
                    //{
                    //    address = (await _googlePlacesService.GetPlaceDetailsByPlaceIdAsync(leadRequest.Enquiry.Address.PlaceId))?.Adapt<Address>() ?? null;
                    //    address = await _addressRepo.AddAsync(address);
                    //}
                    //else
                    //{
                    //    address = await _addressRepo.AddAsync(leadRequest.Enquiry.Address?.Adapt<Address>() ?? new());
                    //}
                    if(leadRequest?.Enquiry?.Addresses?.Any() ?? false)
                    {
                        foreach (var enquiryAddress in leadRequest.Enquiry.Addresses)
                        {
                            if (!string.IsNullOrWhiteSpace(enquiryAddress.PlaceId))
                            {
                                var address = (await _googlePlacesService.GetPlaceDetailsByPlaceIdAsync(enquiryAddress.PlaceId))?.Adapt<Address>();
                                if (address != null)
                                {
                                    addresses.Add(await _addressRepo.AddAsync(address));
                                }
                            }
                            else
                            {
                                var providedAddress = enquiryAddress.Adapt<Address>() ?? new Address();
                                addresses.Add(await _addressRepo.AddAsync(providedAddress));
                            }
                        }

                    }
                    //var newStatus = (await _leadStatusRepo.ListAsync(cancellationToken)).Where(i => i.Status == "new");
                    var customnewStatus = (await _customLeadStatusRepo.ListAsync(cancellationToken)).Where(i => i.IsDefault);
                    string name = lead.Name.Trim();
                    lead.LeadNumber = name[0].ToString().ToUpper() + new Random().Next(1000, 9999).ToString() + DateTime.UtcNow.ToString("FFFFFFF");
                    //lead.Status = newStatus?.FirstOrDefault();
                    lead.CustomLeadStatus = customnewStatus?.FirstOrDefault();
                    lead.TagInfo = leadRequest.LeadTags?.Adapt<LeadTag>() ?? new();
                    var leadEnquiry = leadRequest.Enquiry?.Adapt<LeadEnquiry>() ?? new();
                    leadEnquiry.IsPrimary = true;
                    //leadEnquiry.Address = address;
                    leadEnquiry.Addresses = addresses;
                    leadEnquiry.PropertyType = propertyTypeses?.FirstOrDefault();
                    leadEnquiry.PropertyTypes = propertyTypeses;
                    lead.Enquiries = new List<LeadEnquiry>();
                    lead.Enquiries.Add(leadEnquiry);
                    lead = await _leadRepo.AddAsync(lead);

                    var leadDto = lead.Adapt<ViewLeadDto>();
                    await leadDto?.SetUsersInViewLeadDtoAsync(_userService, cancellationToken);
                    var leadHsitory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                    try
                    {
                        await _leadHistoryRepo.AddAsync(leadHsitory);
                    }
                    catch(Exception ex) 
                    {
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = " CreateBulkLeadRequestHandler -> Handle() -> AddAsync()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                    existingContacts.Add(leadRequest.ContactNo);
                    if (lead.AssignTo != default && lead.AssignTo != Guid.Empty)
                    {
                        try
                        {
                            List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadAssignment, lead, lead.AssignTo, leadDto.AssignedUser?.Name, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() });
                        }
                        catch (Exception ex) 
                        {
                            var error = new LrbError()
                            {
                                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                ErrorSource = ex?.Source,
                                StackTrace = ex?.StackTrace,
                                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                ErrorModule = "CreateBulkLeadRequestHandler ->Handle() -> ScheduleNotificationsAsync()"
                            };
                            await _leadRepositoryAsync.AddErrorAsync(error);
                        }
                    }
                }
            }
            //Sending notification to admin
            #region PushNotification
            try
            {
                int numberOfNewLeads = leadRequests.Count() - existingLeads?.Count() ?? 0;
                int numberOfUnAssignedLeads = leadRequests.Count(i => i.AssignTo == Guid.Empty || i.AssignTo == null);
                var lead = existingLeads?.FirstOrDefault(i => i.AssignTo == Guid.Empty || i.AssignTo == null) ?? null;
                if (lead != null && leadRequests != null && leadRequests.Any())
                {
                    List<Guid> adminIds = await _npgsqlRepo.GetAdminIdsAsync(_currentUser?.GetTenant());
                    var leadDto = lead.Adapt<ViewLeadDto>();
                    if (adminIds != null && adminIds.Any())
                    {
                        foreach (var adminId in adminIds)
                        {
                            var adminDetails = await _userService.GetAsync(adminId.ToString(), cancellationToken);
                            if (adminDetails != null)
                            {
                                List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.UnAssignedLeadUpdate, lead, adminId, adminDetails.FirstName + " " + adminDetails.LastName, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex.Message,
                    ErrorSource = ex.Source,
                    StackTrace = ex.StackTrace,
                    ErrorModule = "CreateBulkLeadRequestHandler ->Handle() -> Push Notification"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            #endregion

            GetInvalidItemsModel InvalidData = new();
            if (existingLeads.Any(i => i != null))
            {
                List<DuplicateItem> duplicateItems = new();
                existingLeads.ToList().ForEach(i => duplicateItems.Add(new DuplicateItem(i.Name, i.ContactNo, DuplicateItemType.Lead)));
                InvalidData.DuplicateItems.DuplicateItems.AddRange(duplicateItems);
                InvalidData.DuplicateItems.LeadCount = duplicateItems.Count;
                InvalidData.DuplicateItems.RequestedItemCount = leadRequests.Count;
            }
            if (invalidContactNumberLeads?.Any() ?? false)
            {
                List<InValidPhoneNumbersItem> invalidPhoneNumberItems = new();
                invalidContactNumberLeads.ToList().ForEach(i => invalidPhoneNumberItems.Add(new InValidPhoneNumbersItem(i.Name, i.ContactNo)));
                InvalidData.InvalidPhoneNumbersData.InvalidPhoneNumberItems.AddRange(invalidPhoneNumberItems);
            }
            if (InvalidData.DuplicateItems.DuplicateItems.Any() || InvalidData.InvalidPhoneNumbersData.InvalidPhoneNumberItems.Any())
            {
                return new Response<bool>(true, JsonConvert.SerializeObject(InvalidData));
            }
            return new Response<bool>(true);
        }
    }
}
