﻿using Lrb.Shared.Multitenancy;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;


namespace Lrb.Infrastructure.Middleware
{
    public class DuplicateRequestMiddleware
    {
        private readonly IConnectionMultiplexer _connection;
        private readonly RequestDelegate _next;
        private readonly IDatabase _redis;
        private readonly ILogger<DuplicateRequestMiddleware> _logger;
        private readonly TimeSpan _ttl = TimeSpan.FromSeconds(5);
        public DuplicateRequestMiddleware(
            RequestDelegate next,
            IConnectionMultiplexer connection,
            ILogger<DuplicateRequestMiddleware> logger)
        {
            _next = next ?? throw new ArgumentNullException(nameof(next));
            _connection = connection ?? throw new ArgumentNullException(nameof(connection));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            try
            {
                _redis = _connection.GetDatabase();
            }
            catch (Exception ex)
            {
                //throw;
            }
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Only apply check for POST /api/v1/integration
            if (context.Request.Method.Equals("POST", StringComparison.OrdinalIgnoreCase) &&
                context.Request.Path.StartsWithSegments("/api/v1/integration", out var remainingPath))
            {
                try
                {
                    var source = remainingPath.Value?.Trim('/');

                    // Allowed sources
                    var allowedSources = new List<string> { "Facebook", "LinkedIn", "GoogleAds", "MagicBricks", "NinetyNineAcres", "Housing", "Website", "Gmail/messages", "SquareYards", "QuikrHomes", "JustLead", "WhatsApp", "YouTube", "Instagram", "OLX", "EstateDekho", "googlesheet/webhook", "RealEstateIndia", "CommonFloor", "RoofandFloor", "MicrosoftAds", "PropertyWala", "project/microsite", "MyGate", "Flipkart", "PropertyFinder", "Bayut", "Dubizzle", "Any" };

                    if (string.IsNullOrWhiteSpace(source) ||
                        !allowedSources.Any(s => source.StartsWith(s, StringComparison.OrdinalIgnoreCase)))
                    {
                        await _next(context);
                        return;
                    }

                    if (!_connection.IsConnected)
                    {
                        await _next(context);
                        return;
                    }

                    var requestKey = await GenerateRequestKeyAsync(context, source ?? string.Empty);
                    if (string.IsNullOrEmpty(requestKey.Item2))
                    {
                        await _next(context);
                        return;
                    }
                    bool acquired = await _redis.StringSetAsync(
                        requestKey.Item1,
                        "locked",
                        _ttl,
                        When.NotExists // Only succeed if the key doesn't already exist
                    );

                    if (!acquired)
                    {
                        context.Response.StatusCode = StatusCodes.Status429TooManyRequests;
                        context.Response.Headers["X-Duplicate-Request"] = "true";
                        await context.Response.WriteAsync("Duplicate request detected. Please try again later.");
                        return;
                    }

                    try
                    {
                        var sequentialKey = $"integration:queue:{source}";
                        var now = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
                        var incrementMs = 2000;

                        var script = @"
                                 local last = redis.call('GET', KEYS[1])
                                 local now = tonumber(ARGV[1])
                                 local increment = tonumber(ARGV[2])
                                 local scheduled = now
                                 if last then
                                 last = tonumber(last)
                                 if last > now then
                                 scheduled = last
                                 end
                                 end
                                 scheduled = scheduled + increment
                                 redis.call('SET', KEYS[1], scheduled, 'PX', ARGV[3])
                                 return scheduled";

                        var ttlMs = 15000;
                        var scheduledTime = (long)(await _redis.ScriptEvaluateAsync(
                            script,
                            new RedisKey[] { sequentialKey },
                            new RedisValue[] { now, incrementMs, ttlMs }
                        ));

                        var delayMs = scheduledTime - incrementMs - now; // time we must wait
                        if (delayMs > 0)
                        {
                            await Task.Delay((int)delayMs, context.RequestAborted);
                        }

                        context.Response.Headers["X-Duplicate-Request"] = "false";
                        await _next(context);
                    }
                    finally
                    {
                        await _redis.KeyDeleteAsync(requestKey.Item1);
                    }
                }
                catch (RedisException ex)
                {
                    await _next(context);
                }
                catch (Exception ex)
                {
                    throw;
                }
            }
            else
            {
                await _next(context);
                return;
            }

        }

        private async Task<(string, string)> GenerateRequestKeyAsync(HttpContext context, string? source)
        {
            try
            {
                context.Request.EnableBuffering();

                string mobileNumber = string.Empty;
                string tenantId = string.Empty;
                if (context.Request.ContentLength > 0 && context.Request.Body.CanSeek)
                {
                    context.Request.Headers.TryGetValue(MultitenancyConstants.TenantIdName, out var tenantIds);
                    if (tenantIds.Count > 0)
                    {
                        tenantId = tenantIds[0];
                    }
                    using var reader = new StreamReader(context.Request.Body, Encoding.UTF8, leaveOpen: true);
                    var body = await reader.ReadToEndAsync();
                    context.Request.Body.Position = 0;

                    try
                    {
                        // 1. Try JSON
                        using var doc = JsonDocument.Parse(body);

                        if (doc.RootElement.ValueKind != JsonValueKind.Undefined &&
                            doc.RootElement.ValueKind != JsonValueKind.Null)
                        {
                            var foundMobile = FindMobile(doc.RootElement);
                            if (!string.IsNullOrWhiteSpace(foundMobile))
                            {
                                mobileNumber = foundMobile;
                            }
                        }
                    }
                    catch
                    {
                        await _redis.StringSetAsync(DateTime.UtcNow.ToString(), "catchblock", TimeSpan.FromSeconds(40));
                        try
                        {
                            var xmlDoc = new System.Xml.XmlDocument();
                            xmlDoc.LoadXml(body);
                            var node = xmlDoc.SelectSingleNode("//*[translate(local-name(),'MOBILE','mobile')='mobile']");
                            if (node != null)
                                mobileNumber = node.InnerText;
                        }
                        catch
                        {
                            var match = System.Text.RegularExpressions.Regex.Match(
                                body,
                                @"mobile\s*[:=]\s*[""']?([\+\d][\d\s\-]*)[""']?",
                                RegexOptions.IgnoreCase
                            );
                            if (match.Success)
                                mobileNumber = match.Groups[1].Value;
                        }
                    }
                }

                string rawKey = $"{context.Request.Method}:{context.Request.Path}:{source}:{tenantId}:{mobileNumber}";

                using var sha256 = SHA256.Create();
                var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(rawKey));

                return ($"dupReq:{Convert.ToBase64String(hashBytes)}", mobileNumber);
            }
            catch (Exception ex)
            {
                return ($"dupReq:fallback:{context.Request.Method}:{context.Request.Path}:{source}", string.Empty);
            }
        }



        private static string? FindMobile(JsonElement element)
        {
            if (element.ValueKind == JsonValueKind.Object)
            {
                foreach (var property in element.EnumerateObject())
                {
                    if (property.Name.Equals("mobile", StringComparison.OrdinalIgnoreCase))
                        return property.Value.GetString();

                    var result = FindMobile(property.Value);
                    if (!string.IsNullOrEmpty(result))
                        return result;
                }
            }
            else if (element.ValueKind == JsonValueKind.Array)
            {
                foreach (var item in element.EnumerateArray())
                {
                    var result = FindMobile(item);
                    if (!string.IsNullOrEmpty(result))
                        return result;
                }
            }
            return null;
        }

    }


}
