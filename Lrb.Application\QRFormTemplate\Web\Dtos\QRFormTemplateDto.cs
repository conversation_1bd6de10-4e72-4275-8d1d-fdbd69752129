﻿using Lrb.Application.Agency.Web;
using Lrb.Application.Campaigns.Mobile.Dto;
using Lrb.Application.ChannelPartner.Mobile.Dtos;
using Lrb.Application.OrgProfile.Web;
using Lrb.Application.OrgProfile.Web.Dtos;

namespace Lrb.Application.QRFormTemplate.Web.Dtos
{
    public class ConfigurableFormDto : IDto
    {
        public Guid FormId { get; set; }
        public bool IsSelected { get; set; }
    }
    public class QRFormTemplateDto : IDto
    {
        public Guid Id { get; set; }
        public string? Name { get; set; }
        public TemplateStatus? Status { get; set; }
        public string? ShortUrl { get; set; }
    }
    public class CreateQRFromTemplate : BaseQRFormTemplateDto
    {
        public List<ConfigurableFormDto>? FormDtos { get; set; }
    }
    public class UpdateQRFromTemplate : BaseQRFormTemplateDto
    {
        public List<ConfigurableFormDto>? FormDtos { get; set; }
    }

    public class ViewQRFormTemplateDto : BaseQRFormTemplateDto
    {
        public DateTime? LastModifiedOn { get; set; }
        public DateTime? CreatedOn { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid LastModifiedBy { get; set; } 
        public List<CustomMasterQRformDto>? Content { get; set; }
        public List<SocialMediaDto>? SocialMedias { get; set; } = new();
        public bool? IsArchived { get; set; }
        public string? ShortUrl { get; set; }
    }
    public class BaseQRFormTemplateDto
    {
        public Guid Id { get; set; }
        public string? Name { get; set; }
        public QRFormTemplateHeaderDto? Header { get; set; }
        public QRFormTemplateFooterDto? Footer { get; set; }
        public TemplateStatus Status { get; set; }
        public string? CompanyName { get; set; }
        public string? PhoneNumber { get; set; }
        public string? Email { get; set; }
        public string? Address { get; set; }
        public string? AgencyName { get; set; }
        public AgencyDto? Agency { get; set; }
        public int NumberOfClones { get; set; }
        public bool IsClone { get; set;}
        public LeadSource? LeadSource { get; set; }
        public string? SubSource { get; set; }
        public string? CampaignName { get; set; }
        public CampaignDto? Campaign { get; set; }
        public string? ChannelPartnerName { get; set; }
        public ChannelPartnerDto? ChannelPartner { get; set; }
        public List<Guid>? FlagIds { get; set; }
    }
}
