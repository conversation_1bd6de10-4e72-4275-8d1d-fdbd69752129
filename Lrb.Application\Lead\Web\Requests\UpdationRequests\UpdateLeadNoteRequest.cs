﻿using Lrb.Application.DailyStatusUpdates.Dtos;
using Lrb.Application.DailyStatusUpdates.Mappings;
using Lrb.Application.Lead.Web.Mappings;
using Lrb.Application.Lead.Web.Requests;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.MasterData;

namespace Lrb.Application.Lead.Web
{
    public class UpdateLeadNoteRequest : IRequest<Response<bool>>
    {
        public Guid Id { get; set; }
        public string? Notes { get; set; }
        public string? ConfidentialNotes { get; set; }
    }
    public class UpdateLeadNoteRequestHandler : LeadCommonRequestHandler, IRequestHandler<UpdateLeadNoteRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<LeadHistoryHot> _newLeadHistoryRepo;
        public UpdateLeadNoteRequestHandler(IServiceProvider serviceProvider, IRepositoryWithEvents<LeadHistoryHot> newLeadHistoryRepo) : base(serviceProvider, typeof(UpdateLeadNoteRequestHandler).Name, "Handle")
        {
            _newLeadHistoryRepo = newLeadHistoryRepo;
        }

        public async Task<Response<bool>> Handle(UpdateLeadNoteRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var existingLead = await _leadRepo.GetByIdAsync(request.Id, cancellationToken) ?? throw new NotFoundException("No Lead found by this Id");
                var oldLeadDto = existingLead.Adapt<ViewLeadDto>();
                try
                {
                   if(existingLead.IsPicked != true)
                    {
                        existingLead.ShouldUpdatePickedDate = await ShouldUpdatePickedDate(existingLead, request.Adapt<PickedLeadDto>());
                        existingLead.IsPicked = existingLead.ShouldUpdatePickedDate;
                    }
                }
                catch (Exception ex)
                {
                    throw;
                }
                existingLead.Notes = request.Notes ?? existingLead.Notes;
                existingLead.ConfidentialNotes = request.ConfidentialNotes ?? existingLead.ConfidentialNotes;

                await _leadRepo.UpdateAsync(existingLead, cancellationToken);
                var currentUser = _currentUser.GetUserId();
                await UpdateLeadHistoryAsync(existingLead, cancellationToken: cancellationToken,isLeadUpdateRequest:true,currentUserId:currentUser);

                #region Update New Lead History Table
                var newLeadDto = existingLead.Adapt<ViewLeadDto>();
                newLeadDto.LastModifiedBy = currentUser;
                await newLeadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken, currentUser);
                await oldLeadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken, currentUser);
                var leadStatues = await _customLeadStatusRepo.ListAsync(cancellationToken);
                var masterPropertyTypes = await _propertyTypeRepo.ListAsync(cancellationToken);
                var latestModificationVersion = await _dapperRepository.GetLatestLeadHistoryVersionAsync(newLeadDto.Id);
                var histories = await LeadHistoryHelperV2.V2UpdateLeadHistoryForVM(newLeadDto, oldLeadDto, latestModificationVersion ?? 1, leadStatues, masterPropertyTypes, _userService, currentUser, cancellationToken);
                await _newLeadHistoryRepo.AddRangeAsync(histories);
                #endregion

                await SendOnlyLeadInfoUpdateNotificationAsync(existingLead, cancellationToken);

                return new(true);
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{typeof(UpdateLeadNoteRequestHandler).Name} - Handle()");
                throw;
            }
        }
    }
}
