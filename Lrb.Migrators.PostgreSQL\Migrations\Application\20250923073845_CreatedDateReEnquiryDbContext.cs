﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class CreatedDateReEnquiryDbContext : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "CreatedBy",
                schema: "LeadratBlack",
                table: "LeadReEnquiry",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedOn",
                schema: "LeadratBlack",
                table: "LeadReEnquiry",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<Guid>(
                name: "Deleted<PERSON><PERSON>",
                schema: "LeadratBlack",
                table: "LeadReEnquiry",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletedOn",
                schema: "LeadratBlack",
                table: "LeadReEnquiry",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "LastModifiedBy",
                schema: "LeadratBlack",
                table: "LeadReEnquiry",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddColumn<DateTime>(
                name: "LastModifiedOn",
                schema: "LeadratBlack",
                table: "LeadReEnquiry",
                type: "timestamp with time zone",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CreatedBy",
                schema: "LeadratBlack",
                table: "LeadReEnquiry");

            migrationBuilder.DropColumn(
                name: "CreatedOn",
                schema: "LeadratBlack",
                table: "LeadReEnquiry");

            migrationBuilder.DropColumn(
                name: "DeletedBy",
                schema: "LeadratBlack",
                table: "LeadReEnquiry");

            migrationBuilder.DropColumn(
                name: "DeletedOn",
                schema: "LeadratBlack",
                table: "LeadReEnquiry");

            migrationBuilder.DropColumn(
                name: "LastModifiedBy",
                schema: "LeadratBlack",
                table: "LeadReEnquiry");

            migrationBuilder.DropColumn(
                name: "LastModifiedOn",
                schema: "LeadratBlack",
                table: "LeadReEnquiry");
        }
    }
}
