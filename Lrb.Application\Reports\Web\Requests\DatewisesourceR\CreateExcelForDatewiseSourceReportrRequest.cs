﻿using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Reports.Web.Dtos.DateWiseSource;
using Lrb.Application.Utils;
using Lrb.Shared.Extensions;

namespace Lrb.Application.Reports.Web.Requests.DatewiseSourceCount
{
    public class CreateExcelForDatewiseSourceReportrRequest : IRequest<Response<string>>
    {
        public DateType? DateType { get; set; }
        public bool? IsWithTeam { get; set; }
        public List<LeadSource>? LeadSources { get; set; }
        public ReportPermission? ReportPermission { get; set; }
        public List<string>? Projects { get; set; }
        public List<string>? SubSources { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<Guid>? UserIds { get; set; }
        public List<string>? Localites { get; set; }
        public List<string>? States { get; set; }
        public List<string>? Cities { get; set; }
        public DateTime? ToDateForLeadReceived { get; set; }
        public DateTime? FromDateForLeadReceived { get; set; }
        public LeadGeneratingFrom? GeneratingFrom { get; set; }
        public string? SearchText { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; } = int.MaxValue;
        public string? TimeZoneId { get; set; }
        public TimeSpan BaseUTcOffset { get; set; }
    }
    public class CreateExcelForDatewiseSourceReportrRequestHandler : IRequestHandler<CreateExcelForDatewiseSourceReportrRequest, Response<string>>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly IBlobStorageService _blobStorageService;
        private readonly ICurrentUser _currentUser;

        public CreateExcelForDatewiseSourceReportrRequestHandler(IDapperRepository dapperRepository, IBlobStorageService blobStorageService, ICurrentUser currentUser)
        {
            _dapperRepository = dapperRepository;
            _blobStorageService = blobStorageService;
            _currentUser = currentUser;
        }

        public async Task<Response<string>> Handle(CreateExcelForDatewiseSourceReportrRequest request, CancellationToken cancellationToken)
        {
            var tenantId = _currentUser.GetTenant();
            var userId = _currentUser.GetUserId();
            List<Guid> teamUserIds = new();
            List<Guid> permittedUserIds = new();
            var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
            if (isAdmin)
            {
                permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
            }
            else if (request.ReportPermission != null)
            {
                switch (request.ReportPermission)
                {
                    case ReportPermission.All:
                        permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        break;
                    case ReportPermission.Reportees:
                        permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                        break;
                }
            }
            if (request?.UserIds?.Any() ?? false)
            {
                if (request?.IsWithTeam ?? false)
                {
                    teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                }
                else
                {
                    teamUserIds = request?.UserIds ?? new List<Guid>();
                }
            }
            else
            {
                if (!isAdmin)
                {
                    teamUserIds = permittedUserIds;
                }
            }
            if (teamUserIds.Any())
            {
                teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
            }
            else
            {
                teamUserIds = permittedUserIds;
            }
            request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
            request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertFromDateToUtc() : null;
            request.FromDateForLeadReceived = request.FromDateForLeadReceived.HasValue ? request.FromDateForLeadReceived.Value.ConvertFromDateToUtc() : null;
            request.ToDateForLeadReceived = request.ToDateForLeadReceived.HasValue ? request.ToDateForLeadReceived.Value.ConvertToDateToUtc() : null;
            IEnumerable<LeadReceivedBySourceDto> leadReportByYear = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<LeadReceivedBySourceDto>("LeadratBlack", "GetOut", new

            {
                lead_source = request?.LeadSources?.ConvertAll(i => (int)i),
                user_id = teamUserIds,
                date_type = request?.DateType,
                from_date = request?.FromDate,
                to_date = request?.ToDate,
                from_date_for_lead_recived = request?.FromDateForLeadReceived,
                to_date_for_lead_recived = request.ToDateForLeadReceived,
                tenant_id = tenantId,
                lead_generating_from = (request?.GeneratingFrom ?? 0),
                projects = request?.Projects,
                subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "")),
                searchtext = request?.SearchText,
                pagesize = request?.PageSize,
                pagenumber = request?.PageNumber,
                localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                states = request?.States?.ConvertAll<string>(i => i.LrbNormalize())

            }));
            Dictionary<string, int> topSources = GroupLeadsBySource(leadReportByYear);


            var groupedLeadsByDay = GroupLeadsByDay(leadReportByYear, topSources);

            int offset = (request.PageSize * (request.PageNumber - 1));

            var pagedGroupedLeadsByDate = groupedLeadsByDay
    .OrderByDescending(dictionary => dictionary["CreatedOn"])
    .Skip(offset)
    .Take(request.PageSize)
    .ToList();

            var convertedItems = pagedGroupedLeadsByDate.Select(dayGroup =>
            {
                var convertedGroup = new Dictionary<string, int>();

                foreach (var kvp in dayGroup)
                {
                    if (kvp.Key == "CreatedOn")
                        continue;

                    convertedGroup.Add(kvp.Key, (int)kvp.Value);
                }

                var resultDictionary = new Dictionary<string, object>();
                resultDictionary.Add("CreatedOn", (string)dayGroup["CreatedOn"]);

                foreach (var kvp in convertedGroup)
                {
                    resultDictionary.Add(kvp.Key, kvp.Value);
                }

                return resultDictionary;
            });

            var fileBytes = CreateExcelDictionaryFromList(convertedItems.ToList()).ToArray();
            var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", $"Reports/{tenantId ?? "Default"}", $"Report-{DateTime.Now}.xlsx", fileBytes);
            var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
            return new(presignedUrl, null);
        }
        static List<Dictionary<string, object>> GroupLeadsByDay(IEnumerable<LeadReceivedBySourceDto> leadReportByYear, Dictionary<string, int> topSources)
        {
            var groupedLeadsByDay = leadReportByYear
                .GroupBy(lead => lead.CreatedOn.Date.ToString("yyyy/MM/dd"))
                .Select(dayGroup =>
                {
                    var dayDictionary = new Dictionary<string, object>();
                    dayDictionary.Add("CreatedOn", dayGroup.Key);
                    foreach (var source in topSources)
                    {
                        dayDictionary.Add(source.Key, dayGroup.Sum(lead => lead.Source == (LeadSource)Enum.Parse(typeof(LeadSource), source.Key) ? lead.LeadCount : 0));
                    }
                    return dayDictionary;
                })
                .ToList();
            return groupedLeadsByDay;
        }
        static Dictionary<string, int> GroupLeadsBySource(IEnumerable<LeadReceivedBySourceDto> leadReport)
        {
            return leadReport
                .Select(lead => new { Source = lead.Source.ToString(), LeadCount = lead.LeadCount })
                .GroupBy(item => item.Source)
                .ToDictionary(group => group.Key, group => group.Sum(item => item.LeadCount))
                .OrderByDescending(item => item.Value)
                .Take(31)
                .ToDictionary(item => item.Key, item => item.Value);
        }



        public static MemoryStream CreateExcelDictionaryFromList(List<Dictionary<string, object>> items)
        {
            MemoryStream stream = new MemoryStream();
            using (SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Create(stream, SpreadsheetDocumentType.Workbook))
            {
                WorkbookPart workbookpart = spreadsheetDocument.AddWorkbookPart();
                workbookpart.Workbook = new Workbook();
                WorksheetPart worksheetPart = workbookpart.AddNewPart<WorksheetPart>();
                worksheetPart.Worksheet = new Worksheet(new SheetData());
                Sheets sheets = spreadsheetDocument.WorkbookPart.Workbook.AppendChild(new Sheets());
                Sheet sheet = new Sheet()
                {
                    Id = spreadsheetDocument.WorkbookPart.GetIdOfPart(worksheetPart),
                    SheetId = 1,
                    Name = "Report"
                };
                sheets.Append(sheet);
                Worksheet worksheet = worksheetPart.Worksheet;
                SheetData sheetData = worksheet.GetFirstChild<SheetData>();


                Row headerRow = new Row();
                foreach (var item in items.First())
                {
                    Cell cell = new Cell(new CellValue(item.Key))
                    {
                        DataType = CellValues.String
                    };
                    headerRow.Append(cell);
                }
                sheetData.Append(headerRow);


                foreach (var item in items)
                {
                    Row dataRow = new Row();
                    foreach (var kvp in item)
                    {
                        Cell cell = new Cell
                        {
                            DataType = CellValues.String,
                            CellValue = new CellValue(kvp.Value.ToString())
                        };
                        dataRow.Append(cell);
                    }
                    sheetData.Append(dataRow);
                }
                worksheetPart.Worksheet.Save();
                workbookpart.Workbook.Save();
                spreadsheetDocument.Close();
            }
            return stream;
        }

    }
}
