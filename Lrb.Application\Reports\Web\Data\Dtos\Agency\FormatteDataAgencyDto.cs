﻿namespace Lrb.Application.Reports.Web
{
    public class FormattedDataAgencyDto : IDto 
    {
        public string SlNo { get; set; } = default!;
        public string? Name { get; set; }
        public long ConvertedData { get; set; }
        public List<ViewFormattedDataReportByAgencyDto>? Data { get; set; }
    }
    public class ViewFormattedDataReportByAgencyDto : IDto 
    {
        public string? Status { get; set; }
        public long Count { get; set; }
    }
}
    