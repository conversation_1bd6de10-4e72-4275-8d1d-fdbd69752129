﻿using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Entities.Templates.QRForm;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace Lrb.Domain.Entities
{
    public class QRFormTemplate : AuditableEntity, IAggregateRoot
    {
        public string? Name { get; set; }
        public QRFormTemplateDetail? Header { get; set; }
        public QRFormTemplateDetail? Footer { get; set; }

        [Column(TypeName = "jsonb")]
        public List<string>? Contents { get; set; }
        public bool IsHeaderAdded { get; set; }
        public bool IsFooterAdded { get; set; }
        public TemplateStatus Status { get; set; }
        public IList<CustomMasterQRform>? QRForms { get; set; }
        public string? CompanyName { get; set; }
        public string? PhoneNumber { get; set; }
        public string? Email { get; set; }
        public string? Address { get; set; }
        public List<SocialMedia>? SocialMedias { get; set; } = new();
        public UserAssignment? UserAssignment { get; set; }
        public QRAssignment? Assignment { get; set; }
        public string? AgencyName { get;set; }
        public Agency? Agency { get; set; }
        public int NumberOfClones { get; set; }
        public bool IsArchived { get; set; }
        public string? CampaignName { get; set; }
        public Campaign? Campaign { get; set; }
        public LeadSource? LeadSource { get; set; }
        public string? SubSource { get; set; }
        public string? ChannelPartnerName { get; set; }
        public ChannelPartner? ChannelPartner { get; set; }
        public string? LongUrl { get; set; }
        public string? ShortUrl { get; set; }
        public string? Code { get; set; }
    }
}
