﻿using Lrb.Application.CustomFields.Web;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.DataManagement.Web.Dtos
{
    public class CustomProspectStatusDto
    {
        public Guid Id { get; set; }
        public Guid BaseId { get; set; }
        public int Leverl { get; set; }
        public string? Status { get; set; }
        public string? DisplayName { get; set; }
        public string? ActionName { get; set; }
        public int OrderRank { get; set; }
        public string? Color { get; set; }
        public bool IsDefault { get; set; }
        public bool? IsScheduled { get; set; }

        public List<CustomFieldDto>? CustomFields { get; set; }
    }
}
