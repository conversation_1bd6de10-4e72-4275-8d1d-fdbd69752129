﻿using Lrb.Application.Project.Web;
using Lrb.Application.Property.Web;

namespace Lrb.Application.Lead.Web.Dtos
{
    public class ViewBookedLeadDto : UpdateBookedLeadDto
    {
        //public ViewLeadDto? Lead { get; set; }
        public IList<BasicPropertyInfoDto>? Properties { get; set; }
        public IList<BasicProjectDto>? Projects { get; set; }
    }
    public class UpdateBookedLeadDto : BaseBookedLeadDto
    {
        public Guid? TeamHead { get; set; }
        public double? AgreementValue { get; set; }
        public IList<DocumentsDto>? Documents { get; set; }
        public double? CarParkingCharges { get; set; }
        public double? AdditionalCharges { get; set; }
        public double? TokenAmount { get; set; }
        public TokenType PaymentMode { get; set; }
        public double? Discount { get; set; }
        public string? DiscountUnit { get; set; }
        public DiscountType DiscountMode { get; set; }
        public double? RemainingAmount { get; set; }
        public Guid? LeadBrokerageInfoId { get; set; }
        public PaymentType PaymentType { get; set; }
        public LeadBrokerageInfoDto? BrokerageInfo { get; set; }
        public string? Currency { get; set; }
        public UnitTypeDto? UnitType { get; set; }
        public bool? IsBookingCompleted { get; set; }
        public List<string>? InvoiceUrl { get; set; }

    }
    public class BaseBookedLeadDto
    {
        public Guid LeadId { get; set; }
        public DateTime? BookedDate { get; set; }
        public Guid? BookedBy { get; set; }
        public string? BookedByUser { get; set; }
        public string? BookedUnderName { get; set; }
        public Guid? SecondaryOwner { get; set; }
        public Guid? UserId { get; set; }
        public string? SoldPrice { get; set; }
        public string? Notes { get; set; }
        public List<string>? ProjectsList { get; set; }
        public List<string>? PropertiesList { get; set; }
        public List<Guid>? ProjectIds { get; set; }
        public List<Guid>? PropertyIds { get; set; }
        public Guid LastModifiedBy { get; set; }
        public DateTime LastModifiedOn { get; set; }
    }
    public class DocumentsDto
    {
        public Guid? Id { get; set; }   
        public string? DocumentName { get; set; }
        public string? FilePath { get; set; }
        public DocumentType Type { get; set; }
        public BookedDocumentType BookedDocumentType { get; set; }
        public DateTime? UploadedOn { get; set; }
        public Guid? CreatedBy { get; set; }
        public DateTime? CreatedOn { get; set; }
        public Guid? LastModifiedBy { get; set; }
        public DateTime LastModifiedOn { get; set; }
        public string? LastModifiedByUser { get; set; }
    }
    public class LeadBrokerageInfoDto
    {
        public double? SoldPrice { get; set; }
        public double? AgreementValue { get; set; }
        public double? BrokerageCharges { get; set; }
        public double? NetBrokerageAmount { get; set; }
        public double? GST { get; set; }
        public double? TotalBrokerage { get; set; }
        public string? ReferralNumber { get; set; }
        public string? ReferralName { get; set; }
        public Guid? ReferredBy { get; set; }
        public double? Commission { get; set; }
        public string CommissionUnit { get; set; }
        public BrokerageType BrokerageType { get; set; }    
        public double? EarnedBrokerage { get; set; }
        public string? GSTUnit { get; set; }
        public string? BrokerageUnit { get; set; }
        public Guid LastModifiedBy { get; set; }
        public DateTime LastModifiedOn { get; set; }
        public string? LastModifiedByUser { get; set; }
    }
    public class UnitTypeDto
    {
        public Guid? Id { get; set; }
        public string? Name { get; set; }
        public double? CarpetArea { get; set; }
        public Guid? CarpetAreaUnitId { get; set; }
        public double? SuperBuildUpArea { get; set; }
        public Guid? SuperBuildUpAreaUnit { get; set; }
        public string? LastModifiedByUser { get; set; }
        public Guid? LastModifiedBy { get; set; }

    }
}
