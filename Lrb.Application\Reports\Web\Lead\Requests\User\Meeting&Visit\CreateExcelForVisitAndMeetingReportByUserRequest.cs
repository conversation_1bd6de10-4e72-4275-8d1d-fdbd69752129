﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Utils;
using Newtonsoft.Json;

namespace Lrb.Application.Reports.Web
{
    public class CreateExcelForVisitAndMeetingReportByUserRequest : IRequest<Response<string>>
    {
        public string? SearchText { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public DateType? DateType { get; set; }
        public DateTime? ToDateForMeetingOrVisit { get; set; }
        public DateTime? FromDateForMeetingOrVisit { get; set; }
        public bool IsWithTeam { get; set; }
        public List<Guid>? UserIds { get; set; }
        public List<string>? Projects { get; set; }
        public List<LeadSource>? Sources { get; set; }
        public List<string>? AgencyNames { get; set; }
        public List<string>? SubSources { get; set; }
        public UserStatus? UserStatus { get; set; }
        public ReportPermission? ExportPermission { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = int.MaxValue;
        public List<string>? Localites { get; set; }
        public List<string>? States { get; set; }
        public List<string>? Cities { get; set; }
        public string? TimeZoneId { get; set; }
        public TimeSpan BaseUTcOffset { get; set; }
    }
    public class CreateExcelForVisitAndMeetingReportByUserRequestHandler : IRequestHandler<CreateExcelForVisitAndMeetingReportByUserRequest, Response<string>>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly IBlobStorageService _blobStorageService;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<ExportReportsTracker> _exportReportsTrackerRepo;
        public const string TYPE = "meetingandvisitreport";
        public CreateExcelForVisitAndMeetingReportByUserRequestHandler(IDapperRepository dapperRepository,
            IBlobStorageService blobStorageService,
            ICurrentUser currentUser,
            IRepositoryWithEvents<ExportReportsTracker> exportReportsTrackerRepo)
        {
            _dapperRepository = dapperRepository;
            _blobStorageService = blobStorageService;
            _currentUser = currentUser;
            _exportReportsTrackerRepo = exportReportsTrackerRepo;
        }

        public async Task<Response<string>> Handle(CreateExcelForVisitAndMeetingReportByUserRequest request, CancellationToken cancellationToken)
        {
            ExportReportsTracker tracker = new();
            var tenantId = _currentUser.GetTenant();
            var userId = _currentUser.GetUserId();
            List<Guid> teamUserIds = new();
            List<Guid> permittedUserIds = new();
            var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
            if (isAdmin)
            {
                permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
            }
            else if (request.ExportPermission != null)
            {
                switch (request.ExportPermission)
                {
                    case ReportPermission.All:
                        permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        break;
                    case ReportPermission.Reportees:
                        permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                        break;
                }
            }
            if (request?.UserIds?.Any() ?? false)
            {
                if (request?.IsWithTeam ?? false)
                {
                    teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                }
                else
                {
                    teamUserIds = request?.UserIds ?? new List<Guid>();
                }
            }
            else
            {
                if (!isAdmin)
                {
                    //request.UserIds = new List<Guid>() { userId };
                    //teamUserIds = (await _dapperRepository.GetSubordinateIdsAsync(request.UserIds, tenantId ?? string.Empty)).ToList();

                    teamUserIds = permittedUserIds;
                }
            }
            if (teamUserIds.Any())
            {
                teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
            }
            else
            {
                teamUserIds = permittedUserIds;
            }
            request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
            request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
            request.FromDateForMeetingOrVisit = request.FromDateForMeetingOrVisit.HasValue ? request.FromDateForMeetingOrVisit.Value.ConvertFromDateToUtc() : null;
            request.ToDateForMeetingOrVisit = request.ToDateForMeetingOrVisit.HasValue ? request.ToDateForMeetingOrVisit.Value.ConvertToDateToUtc() : null;
            var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<LeadAppointmentByUserDto>("LeadratBlack", "GetMeetingAndSitevisitReportByUser", new
            {
                fromdate = request.FromDate,
                todate = request.ToDate,
                fromdateformeetingorvisit = request.FromDateForMeetingOrVisit,
                todateformeetingorvisit = request.ToDateForMeetingOrVisit,
                datetype = request.DateType,
                userids = teamUserIds,
                projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                sources = request?.Sources?.ConvertAll(i => (int)i),
                tenantid = tenantId,
                searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                agencynames = request?.AgencyNames?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                userstatus = (request?.UserStatus ?? 0),
                pagesize = request.PageSize,
                pagenumber = request.PageNumber
            }))?.ToList();
            res?.ForEach(i => i.TotalCount = (i.SiteVisitScheduledCount + i.MeetingScheduledCount + i.SiteVisitDoneCount + i.SiteVisitNotDoneCount + i.MeetingDoneCount + i.MeetingNotDoneCount));
            var fileBytes = ExcelHelper.CreateExcelFromList(res, new List<string>(), new List<string>() { "Id" }, request.TimeZoneId, request.BaseUTcOffset).ToArray();
            var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", $"Reports/{tenantId ?? "Default"}", $"Report-{DateTime.Now}.xlsx", fileBytes);
            var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
            tracker.Count = res.Count();
            tracker.Type = TYPE;
            tracker.Request = JsonConvert.SerializeObject(request);
            tracker.S3BucketKey = presignedUrl;
            await _exportReportsTrackerRepo.AddAsync(tracker, cancellationToken);
            return new(presignedUrl, null);
        }
    }
}
