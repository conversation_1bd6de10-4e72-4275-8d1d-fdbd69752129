﻿using Lrb.Application.Reports.Web;
using Lrb.Application.Reports.Web.Lead.Dtos.UserVsSource;
using Lrb.Application.Reports.Web.Lead.Dtos.UserVsSubSource;
using Lrb.Application.Reports.Web.Requests.DatewiseSourceCount;
using Lrb.Application.Reports.Web.Requests.DatewisesourceR;
using Lrb.Application.Reports.Web.Requests.ProjectvsSubStatus;
using Lrb.Application.Reports.Web.Requests.SubSource;
using Lrb.Application.Reports.Web.UserVsSource.Requests;
namespace Lrb.WebApi.Host.Controllers.V2
{
    [Authorize]
    [Route("api/v2/[controller]")]
    [ApiVersionNeutral]
    public class ReportController : VersionedApiController
    {
        [HttpPost("user/status")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.ViewAllUsers, LrbResource.Reports)]
        [OpenApiOperation("Get lead status report based on users", "")]
        public async Task<PagedResponse<LeadsReportByUserDto, string>> GetReportAsync([FromBody] GetLeadStatusReportByUsersRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("user/new/status-count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get lead status report based on users", "")]
        public async Task<int> GetReportAsync([FromBody] V2GetLeadStatusReportByUsersCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("user/new/status")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get lead status report count based on users", "")]
        public async Task<PagedResponse<LeadsReportByUserDto, string>> GetReportAsync([FromBody] V2GetLeadStatusReportByUsersRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("user/meetingandvisit")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get meeting and site visit report based on users", "")]
        public async Task<PagedResponse<LeadAppointmentByUserDto, string>> GetReportAsync([FromBody] GetVisitAndMeetingReportByUserRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("project/status")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get lead status report based on projects", "")]
        public async Task<PagedResponse<ProjectReportDto, string>> GetReportAsync([FromBody] GetLeadStatusReportByProjectsRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("project/status/new-count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get lead status report based on projects", "")]
        public async Task<int> GetReportAsync([FromBody] V2GetLeadStatusReportByProjectsCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("project/status/new")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get lead status report count based on projects", "")]
        public async Task<PagedResponse<ProjectReportDto, string>> GetReportAsync([FromBody] V2GetLeadStatusReportByProjectsRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("source/status")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get lead status report based on source", "")]
        public async Task<PagedResponse<LeadsSourceReportDto, string>> GetReportAsync([FromBody] GetLeadStatusReportBySourceRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("source/status/new")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get lead status report based on source", "")]
        public async Task<PagedResponse<LeadsSourceReportDto, string>> GetReportAsync([FromBody] V2GetLeadStatusReportBySourceRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("source/status/new-count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get lead status report count based on source", "")]
        public async Task<int> GetReportAsync([FromBody] V2GetLeadStatusReportBySourceCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("subsource/status")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get lead status report based on sub source", "")]
        public async Task<PagedResponse<LeadsSubSourceReportDto, string>> GetReportAsync([FromBody] GetLeadStatusReportBySubSourceRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("subsource/status/new")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get lead status report based on sub source", "")]
        public async Task<PagedResponse<LeadsSubSourceReportDto, string>> GetReportAsync([FromBody] V2GetLeadStatusReportBySubSourceRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("subsource/status/new-count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get lead status report count based on sub source", "")]
        public async Task<int> GetReportAsync([FromBody] V2GetLeadStatusReportBySubSourceCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("agency/status/new")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get lead status report based on agency name", "")]
        public async Task<PagedResponse<LeadsAgencyReportDto, string>> GetReportAsync([FromBody] V2GetLeadStatusReportByAgencyRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("agency/status/new-count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get lead status report count based on agency name", "")]
        public async Task<int> GetReportAsync([FromBody] V2GetLeadStatusReportByAgencyCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("activity")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("View Activity Report of all Users", "")]
        public async Task<PagedResponse<UserActivityReportDto, string>> GetReportAsync([FromBody] GetActivityReportRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("substatus")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("View Sub-Status Report of all Users", "")]
        public async Task<PagedResponse<ModifiedSubStatusReportDto, string>> GetReportAsync([FromBody] GetSubStatusReportRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("substatus/new")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("View Sub-Status Report of all Users", "")]
        public async Task<PagedResponse<Dictionary<string, object>, string>> GetReportAsync([FromBody] V2GetSubStatusReportRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("substatus/new-count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("View Sub-Status Report count of all Users", "")]
        public async Task<int> GetReportAsync([FromBody] V2GetSubStatusReportCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("substatus/bysubsource")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("View Sub-Status Report by Sub-Source", "")]
        public async Task<PagedResponse<Dictionary<string, object>, string>> GetReportAsync([FromBody] GetSubStatusReportBySubSourceRequest request)
        {
            return await Mediator.Send(request);
        }


        [HttpPost("substatus/bysubsource/new")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("View Sub-Status Report by Sub-Source", "")]
        public async Task<PagedResponse<Dictionary<string, object>, string>> GetReportAsync([FromBody] V2GetSubStatusReportBySubSourceRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("substatus/bysubsource/new-count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("View Sub-Status Report count by Sub-Source", "")]
        public async Task<int> GetReportAsync([FromBody] V2GetSubStatusReportBySubSourceCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("project/bysubstatus")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("View Project Report by Sub-Status ", "")]
        public async Task<PagedResponse<Dictionary<string, object>, string>> GetReportAsync([FromBody] GetProjectReportBySubStatusRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("project/bysubstatus/new")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("View Project Report by Sub-Status", "")]
        public async Task<PagedResponse<Dictionary<string, object>, string>> GetReportAsync([FromBody] V2GetProjectReportBySubStatusRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("project/bysubstatus/new-count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("View Project Report count by Sub-Status", "")]
        public async Task<int> GetReportAsync([FromBody] V2GetProjectReportBySubStatusCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("activity-count")]
        [TenantIdHeader]
        [OpenApiOperation("View activity report count of all users", "")]
        public async Task<int> GetReportAsync([FromBody] GetActivityReportCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("activity/level9")]
        [TenantIdHeader]
        [OpenApiOperation("View Activity Report of all Users", "")]
        public async Task<PagedResponse<UserActivityReportLevel8Dto, string>> GetReportAsync([FromBody] GetActivityReportLevel8Request request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("activity/level10")]
        [TenantIdHeader]
        [OpenApiOperation("View Activity Report of all Users", "")]
        public async Task<PagedResponse<UserActivityReportLevel9Dto, string>> GetReportAsync([FromBody] GetActivityReportLevel9Request request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("activity/level11")]
        [TenantIdHeader]
        [OpenApiOperation("View Activity Report of all Users", "")]
        public async Task<PagedResponse<UserActivityReportLevel10Dto, string>> GetReportAsync([FromBody] GetActivityReportLevel10Request request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("activity/level12")]
        [TenantIdHeader]
        [OpenApiOperation("View Activity Report of all Users", "")]
        public async Task<PagedResponse<UserActivityReportLevel11Dto, string>> GetReportAsync([FromBody] GetActivityReportLevel11Request request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("project/bysubstatus/updated")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("View Project Report by Sub-Status ", "")]
        public async Task<PagedResponse<ModifiedProjectvsSubStatusReportDto, string>> GetReportAsync([FromBody] GetProjectReportvsSubStatusnewRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("substatus/bysubsource/updated")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("View Sub-Status vs Subsource Report of all Users", "")]
        public async Task<PagedResponse<ModifiedSubStatusvsSubSourceReportDto, string>> GetReportAsync([FromBody] GetSubStatusvsSubSourceReportRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("subsource/status/custom-count")]
        [TenantIdHeader]
        [OpenApiOperation("Get lead status report count based on sub source", "")]
        public async Task<int> GetReportAsync([FromBody] SubSourceReportByStatusCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("user/meetingandvisit/level1")]
        [TenantIdHeader]
        [OpenApiOperation("View Meeting and Visit Report of all Users", "")]
        public async Task<PagedResponse<LeadAppointmentByUserV3Dto, string>> GetReportAsync([FromBody] V3GetMeetingAndSitevisitReportByUserRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("user/meetingandvisit/level2")]
        [TenantIdHeader]
        [OpenApiOperation("View Meeting and Visit Report of all Users", "")]
        public async Task<PagedResponse<LeadAppointmentByUserV4Dto, string>> GetReportAsync([FromBody] V4GetMeetingAndSitevisitReportByUserRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("user/meetingandvisit/count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get meeting and site visit report count based on users", "")]
        public async Task<int> GetReportAsync([FromBody] V3GetVisitAndMeetingReportByUserCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("datewisesource")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get Leads Source Count Based on Created Date .", "")]
        public async Task<PagedResponse<Dictionary<string, object>, string>> GeLeadsByYear([FromBody] GetLeadDateBySourceCountRequest request)
        {
            return await (Mediator.Send(request));
        }
        [HttpPost("datewisesource/count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("View Datewise Source Report Count", "")]
        public async Task<int> GetReportAsync([FromBody] GetDatewiseSourceReportCountRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("uservssource")]
        [TenantIdHeader]
        [OpenApiOperation("view uservssource report ", "")]
        public async Task<PagedResponse<SourceReportByUserDto, string>> GetReportAsync([FromBody] GetUserVsSourceReportRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("uservssource/count")]
        [TenantIdHeader]
        [OpenApiOperation("Get uservssource report count ", "")]
        public async Task<int> GetReportAsync([FromBody] GetUserVsSourceReportCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("uservssubsource")]
        [TenantIdHeader]
        [OpenApiOperation("view uservssubsource report", "")]
        public async Task<PagedResponse<UservsSubSourceReportDto, string>> GetReportAsync([FromBody] GetUserVsSubSourceReportRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("uservssubsource/count")]
        [TenantIdHeader]
        [OpenApiOperation("Get  uservssubsource report count", "")]
        public async Task<int> GetReportAsync([FromBody] GetUserVsSubSourceReportCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("user/status/custom-count")]
        [TenantIdHeader]
        [OpenApiOperation("Get lead status report based on users", "")]
        public async Task<int> GetReportAsync([FromBody] GetUserCountReportRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("user/status/custom")]
        [TenantIdHeader]
        [OpenApiOperation("Get lead status report count based on users", "")]
        public async Task<PagedResponse<ViewUserReportDto, string>> GetReportAsync([FromBody] LeadStatusReportByUsersRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("project/status/custom-count")]
        [TenantIdHeader]
        [OpenApiOperation("Get lead status report based on projects", "")]
        public async Task<int> GetReportAsync([FromBody] ProjectReportByStatusCountRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("project/status/custom")]
        [TenantIdHeader]
        [OpenApiOperation("Get lead status report count based on projects", "")]
        public async Task<PagedResponse<ViewProjectReportDto, string>> GetReportAsync([FromBody] ProjectReportByStatusRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("source/status/custom")]
        [TenantIdHeader]
        [OpenApiOperation("Get lead status report based on source", "")]
        public async Task<PagedResponse<ViewSourceReportDto, string>> GetReportAsync([FromBody] SourceReportByStatusRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("source/status/custom-count")]
        [TenantIdHeader]
        [OpenApiOperation("Get lead status report count based on source", "")]
        public async Task<int> GetReportAsync([FromBody] SourceReportByStatusCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("subsource/status/custom")]
        [TenantIdHeader]
        [OpenApiOperation("Get lead status report based on sub source", "")]
        public async Task<PagedResponse<ViewSubSourceReportDto, string>> GetReportAsync([FromBody] SubSourceReportByStatusRequest request)
        {
            return await Mediator.Send(request);
        }
    }
}

