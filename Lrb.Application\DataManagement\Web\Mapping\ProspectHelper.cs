﻿using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Common.TimeZone;
using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.Lead.Web.Mappings;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.Source.Web;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;
using PhoneNumbers;
using System.Data;
using System.Globalization;
using System.Reflection.Metadata;
using System.Text;
using System.Text.RegularExpressions;
using Lrb.Application.Utils;
using ThirdParty.Json.LitJson;
using Lrb.Application.Lead.Web;

namespace Lrb.Application.DataManagement.Web.Mapping
{
    public static class ProspectHelper
    {
        public static List<Lrb.Domain.Entities.Prospect> ConvertToProspect(this DataTable dataTable,
            Dictionary<ProspectDataColumn, string> dataColumn,
            List<string> unMappedColumn,
            List<MasterPropertyType> propertyTypes,
            List<Domain.Entities.Property> properties,
            List<CustomProspectStatus> prospectStatus,
            List<MasterProspectSource> prospectSources,
            List<Lrb.Domain.Entities.Project> projectsList,
            List<Domain.Entities.Agency> agencies,
            Domain.Entities.GlobalSettings globalSettings,
            List<Domain.Entities.ChannelPartner> channelPartners,
            Domain.Entities.BulkProspectUploadTracker tracker,
            List<Domain.Entities.Campaign> campaigns)

        {
            List<Prospect> prospects = new();
            foreach (DataRow dataRow in dataTable.Rows)
            {
                #region GetData
                string? enquiryFor = !dataColumn.ContainsKey(ProspectDataColumn.EnquiryFor) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.EnquiryFor]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.EnquiryFor]].ToString();
                string? bhkType = !dataColumn.ContainsKey(ProspectDataColumn.BHKType) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.BHKType]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.BHKType]].ToString();
                string? noOfBhks = !dataColumn.ContainsKey(ProspectDataColumn.NoOfBHKs) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.NoOfBHKs]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.NoOfBHKs]].ToString();
                string? baseProperty = !dataColumn.ContainsKey(ProspectDataColumn.BasePropertyType) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.BasePropertyType]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.BasePropertyType]].ToString();
                string? subProperty = !dataColumn.ContainsKey(ProspectDataColumn.SubPropertyType) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.SubPropertyType]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.SubPropertyType]].ToString();
                string? lowerBudget = !dataColumn.ContainsKey(ProspectDataColumn.LowerBudget) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.LowerBudget]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.LowerBudget]].ToString();
                string? upperBudget = !dataColumn.ContainsKey(ProspectDataColumn.UpperBudget) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.UpperBudget]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.UpperBudget]].ToString();
                string? dataSource = !dataColumn.ContainsKey(ProspectDataColumn.Source) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.Source]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.Source]].ToString();
                string currecncy = !dataColumn.ContainsKey(ProspectDataColumn.Currency) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.Currency]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.Currency]].ToString();
                var propertyInfo = BulkUploadHelper.GetPropertyType(baseProperty, subProperty, propertyTypes, bhkType, noOfBhks);
                var enquiryInfo = BulkUploadHelper.GetEnquiryForInfo(enquiryFor ?? string.Empty);
                var lowerBudgetInfo = BudgetHelper.ConvertBuget(lowerBudget ?? string.Empty);
                var upperBudgetInfo = BudgetHelper.ConvertBuget(upperBudget ?? string.Empty);
                //var validSource = GetValidDataSource(dataSource ?? string.Empty, prospectSources);
                var status = prospectStatus.Where(i => !i.IsDeleted && i.IsDefault).FirstOrDefault();
                var source = prospectSources.Where(i => i.IsDefault).FirstOrDefault();
                var agencyName = !dataColumn.ContainsKey(ProspectDataColumn.AgencyName) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.AgencyName]) ? null : dataRow[dataColumn[ProspectDataColumn.AgencyName]].ToString();
                var agencyToAdd = agencies.FirstOrDefault(i => i.Name.Trim().ToLower().Equals(agencyName?.Trim().ToLower(), StringComparison.InvariantCultureIgnoreCase));
                var currencycode = dataColumn.GetCurrencySymbol1(dataRow, currecncy, globalSettings?.Countries?.FirstOrDefault()?.DefaultCurrency);
                var cpName = !dataColumn.ContainsKey(ProspectDataColumn.ChannelPartnerName) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.ChannelPartnerName]) ? null : dataRow[dataColumn[ProspectDataColumn.ChannelPartnerName]].ToString();
                var channelPartenr = channelPartners.FirstOrDefault(i => i.FirmName.Trim().ToLower().Equals(cpName?.Trim().ToLower(), StringComparison.InvariantCultureIgnoreCase));
                string countryCode = !dataColumn.ContainsKey(ProspectDataColumn.CountryCode) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.CountryCode]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.CountryCode]].ToString();
                string altCountryCode = !dataColumn.ContainsKey(ProspectDataColumn.AlternativeNoCountryCode) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.AlternativeNoCountryCode]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.AlternativeNoCountryCode]].ToString();
                var campaignName = !dataColumn.ContainsKey(ProspectDataColumn.CampaignName) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.CampaignName]) ? null : dataRow[dataColumn[ProspectDataColumn.CampaignName]].ToString();
                var campaignToAdd = campaigns.FirstOrDefault(i => i.Name.Trim().ToLower().Equals(campaignName?.Trim().ToLower(), StringComparison.InvariantCultureIgnoreCase));
                var prospect = new Prospect()
                {
                    Name = !dataColumn.ContainsKey(ProspectDataColumn.Name) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.Name]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.Name]].ToString(),
                    ContactNo = !dataColumn.ContainsKey(ProspectDataColumn.ContactNo) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.ContactNo]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.ContactNo]].ToString(),
                    AlternateContactNo = !dataColumn.ContainsKey(ProspectDataColumn.AltContactNo) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.AltContactNo]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.AltContactNo]].ToString(),
                    Email = !dataColumn.ContainsKey(ProspectDataColumn.Email) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.Email]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.Email]].ToString(),
                    Notes = !dataColumn.ContainsKey(ProspectDataColumn.Notes) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.Notes]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.Notes]].ToString(),
                    Enquiries = new List<ProspectEnquiry>()
                    {
                        new()
                        {
                            IsPrimary = true,
                            //EnquiryType = propertyInfo.IsValidInfo ? enquiryInfo.EnquiredFor : default,
                            EnquiryTypes=propertyInfo.IsValidInfo?enquiryInfo.EnquiryTypes : default,
                            BHKTypes = propertyInfo.IsValidInfo ? propertyInfo.BHKTypes : default,
                            //NoOfBhks = propertyInfo.IsValidInfo ? propertyInfo.NoOfBHK : default,
                            BHKs = propertyInfo.IsValidInfo?propertyInfo.BHKs : default,
                            PropertyType = propertyInfo.IsValidInfo ? propertyInfo.PropertyType : default,
                            LowerBudget = lowerBudgetInfo.IsValidInfo ? lowerBudgetInfo.Budget : null,
                            UpperBudget = upperBudgetInfo.IsValidInfo ? upperBudgetInfo.Budget : null,
                            Currency = currencycode ?? "INR",
                            Addresses = new()
                            {
                                new()
                                {

                                City = !dataColumn.ContainsKey(ProspectDataColumn.City) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.City]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.City]].ToString(),
                                State = !dataColumn.ContainsKey(ProspectDataColumn.State) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.State]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.State]].ToString(),
                                Locality = !dataColumn.ContainsKey(ProspectDataColumn.Location) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.Location]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.Location]].ToString(),
                                }
                            },
                            //Source = validSource ?? source,
                            SubSource = !dataColumn.ContainsKey(ProspectDataColumn.SubSource) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.SubSource]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.SubSource]].ToString(),
                        }
                    },
                    Properties = !dataColumn.ContainsKey(ProspectDataColumn.Property) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.Property]) || string.IsNullOrWhiteSpace(dataRow[dataColumn[ProspectDataColumn.Property]].ToString()) ? null : new List<Domain.Entities.Property>() { properties.FirstOrDefault(i => i.Title?.ToLower() == (!dataColumn.ContainsKey(ProspectDataColumn.Property) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.Property]) ? default : dataRow[dataColumn[ProspectDataColumn.Property]].ToString()?.ToLower())) },
                    Projects = !dataColumn.ContainsKey(ProspectDataColumn.Project) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.Project]) || string.IsNullOrWhiteSpace(dataRow[dataColumn[ProspectDataColumn.Project]].ToString()) ? null : projectsList.Any(i => i.Name?.ToLower() == dataRow[dataColumn[ProspectDataColumn.Project]].ToString()?.ToLower()) ? new List<Domain.Entities.Project>() { projectsList.First(i => i.Name?.ToLower() == dataRow[dataColumn[ProspectDataColumn.Project]].ToString()?.ToLower()) } : null,
                    Status = status ?? null,
                    AgencyName = !dataColumn.ContainsKey(ProspectDataColumn.AgencyName) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.AgencyName]) ? null : dataRow[dataColumn[ProspectDataColumn.AgencyName]].ToString(),
                    Agencies = agencyToAdd != null
                            ? new List<Domain.Entities.Agency>() { agencyToAdd }
                             : !string.IsNullOrEmpty(agencyName)
                             ? new List<Domain.Entities.Agency>()
                                  {
                                        new Domain.Entities.Agency()
                                        {
                                            Name = agencyName,
                                            CreatedBy = tracker.CreatedBy,
                                            LastModifiedBy = tracker.LastModifiedBy
                                        }
                                  } : null,
                    CompanyName = !dataColumn.ContainsKey(ProspectDataColumn.CompanyName) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.CompanyName]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.CompanyName]].ToString(),
                    ChannelPartners = channelPartenr != null
                            ? new List<Domain.Entities.ChannelPartner>() { channelPartenr }
                             : !string.IsNullOrEmpty(cpName)
                             ? new List<Domain.Entities.ChannelPartner>()
                                  {
                                        new Domain.Entities.ChannelPartner()
                                        {
                                            FirmName = cpName,
                                            CreatedBy = tracker.CreatedBy,
                                            LastModifiedBy = tracker.LastModifiedBy
                                        }
                                  } : null,
                    Campaigns = campaignToAdd != null
                            ? new List<Domain.Entities.Campaign>() { campaignToAdd }
                             : !string.IsNullOrEmpty(campaignName)
                             ? new List<Domain.Entities.Campaign>()
                                  {
                                        new Domain.Entities.Campaign()
                                        {
                                            Name = campaignName,
                                            CreatedBy = tracker.CreatedBy,
                                            LastModifiedBy = tracker.LastModifiedBy
                                        }
                                  } : null,
                    CountryCode = countryCode ?? globalSettings?.Countries?.FirstOrDefault()?.DefaultCallingCode ?? "+91",
                    AltCountryCode = altCountryCode ?? globalSettings?.Countries?.FirstOrDefault()?.DefaultCallingCode ?? "+91",

                };
                if (!propertyInfo.IsValidInfo)
                {
                    prospect.Notes += !string.IsNullOrEmpty(propertyInfo.BasePropertyType) ? " \n" + "BaseProperty" + " - " + propertyInfo.BasePropertyType : string.Empty;
                    prospect.Notes += !string.IsNullOrEmpty(propertyInfo.SubPropertyType) ? ", \n" + "SubProperty" + " - " + propertyInfo.SubPropertyType : string.Empty;
                    prospect.Notes += !string.IsNullOrEmpty(propertyInfo.InvalidNoOfBHK) ? ", \n" + "NoOfBHK" + " - " + propertyInfo.InvalidNoOfBHK : string.Empty;
                    prospect.Notes += !string.IsNullOrEmpty(propertyInfo.InvalidBHKType) ? ", \n" + "BHKType" + " - " + propertyInfo.InvalidBHKType : string.Empty;
                }
                if (!upperBudgetInfo.IsValidInfo)
                {
                    prospect.Notes += !string.IsNullOrEmpty(upperBudgetInfo.Invalidbudget) ? ", \n" + "UpperBudget" + " - " + upperBudgetInfo.Invalidbudget : string.Empty;
                }
                if (!lowerBudgetInfo.IsValidInfo)
                {
                    prospect.Notes += !string.IsNullOrEmpty(lowerBudgetInfo.Invalidbudget) ? ", \n" + "LowerBudget" + " - " + lowerBudgetInfo.Invalidbudget : string.Empty;
                }
                #endregion
                prospects.Add(prospect);
            }
            return prospects;
        }

        public static bool TryGetCurrencySymbol(string currencySymbol, out string isoCurrencyCode)
        {
            isoCurrencyCode = CultureInfo
        .GetCultures(CultureTypes.AllCultures)
        .Where(c => !c.IsNeutralCulture)
        .Select(culture =>
        {
            try
            {
                return new RegionInfo(culture.Name);
            }
            catch
            {
                return null;
            }
        })
        .Where(ri => ri != null && ri.CurrencySymbol.Equals(currencySymbol, StringComparison.OrdinalIgnoreCase))
        .Select(ri => ri.ISOCurrencySymbol)
        .FirstOrDefault();

            return isoCurrencyCode != null;
        }
        public static List<string> GetValidCurrencySymbols()
        {
            var symbols = new List<string>();
            var countries = ISO3166.Country.List;

            var currencyProvider = new Nager.Country.CountryProvider();

            foreach (var country in countries)
            {
                var matchingCountry = currencyProvider.GetCountries()
                    .FirstOrDefault(c => c.CommonName.Equals(country.Name, StringComparison.OrdinalIgnoreCase));

                if (matchingCountry != null)
                {
                    foreach (var currency in matchingCountry.Currencies)
                    {
                        var symbol = currency.IsoCode;
                        symbols.Add(symbol);
                    }
                }
            }

            return symbols;

        }
        public static string GetCurrencySymbol1(this Dictionary<ProspectDataColumn, string> mappedColumnsData, DataRow row, string? currency, string? defaultcurrency)
        {
            if (string.IsNullOrWhiteSpace(currency))
            {
                return defaultcurrency;
            }
            currency = currency.Replace(" ", "").ToUpper();
            var currencies = ISO3166.Country.List
                .SelectMany(country => new Nager.Country.CountryProvider().GetCountries()
                    .Where(c => c.CommonName.Equals(country.Name, StringComparison.OrdinalIgnoreCase))
                    .SelectMany(c => c.Currencies)
                    .Select(currency => currency.IsoCode))
                .ToList();
            bool isCurrencyCode = !currencies.Contains(currency);
            if (isCurrencyCode)
            {
                string symbol;
                if (TryGetCurrencySymbol(currency, out symbol))
                {
                    return symbol;
                }
                else
                {
                    return defaultcurrency;
                }
            }
            if (GetValidCurrencySymbols().Contains(currency))
            {
                return currency;
            }

            else
            {
                return defaultcurrency;
            }
        }

        public static async Task<MasterProspectSource>? GetValidDataSource(string source, List<MasterProspectSource> sources, IRepositoryWithEvents<Lrb.Domain.Entities.Source> _sourceRepo)
        {
            var validSource = source.Replace(" ", "").ToLower().Trim();
            MasterProspectSource masterSource = null;
            if (string.IsNullOrEmpty(validSource))
            {
                return sources.Where(i => i.IsDefault).FirstOrDefault();
            }
            if (validSource == "99acres" || validSource == "99" || validSource == "ninetynineacre")
            {
                validSource = "ninetynineacres";
            }
            masterSource = sources.FirstOrDefault(i => string.Equals(i.DisplayName.Replace(" ", "").ToLower().Trim(), validSource, StringComparison.OrdinalIgnoreCase));

            if (masterSource == null)
            {
                return null;
            }

            return await IsSourceEnabled(masterSource.Value, _sourceRepo) ? masterSource : null;
        }
        private async static Task<bool> IsSourceEnabled(int value, IRepositoryWithEvents<Lrb.Domain.Entities.Source> _sourceRepo, CancellationToken cancellationToken = default)
        {
            try
            {
                var source = await _sourceRepo.FirstOrDefaultAsync(new GetSourceByValueSpecs(value), cancellationToken);

                if (source == null)
                {
                    return false;
                }
                if (source.IsEnabled == false) return false;
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        public static List<string> GetAgencyNamesFromDataTable(
DataTable table,
Dictionary<ProspectDataColumn, string> mappedColumnsData)
        {
            var agencyNames = new HashSet<string>();

            foreach (DataRow row in table.Rows)
            {

                if (mappedColumnsData.TryGetValue(ProspectDataColumn.AgencyName, out var agencyColumn) &&
                    !string.IsNullOrEmpty(agencyColumn))
                {
                    string agencyName = row[agencyColumn].ToString();
                    if (!string.IsNullOrEmpty(agencyName))
                    {
                        agencyNames.Add(agencyName);
                    }
                }
            }

            return agencyNames.Distinct().ToList();
        }

        public static List<string> GetCpNamesFromDataTable(
  DataTable table,
  Dictionary<ProspectDataColumn, string> mappedColumnsData)
        {
            var cpNames = new HashSet<string>();

            foreach (DataRow row in table.Rows)
            {

                if (mappedColumnsData.TryGetValue(ProspectDataColumn.ChannelPartnerName, out var Cpname) &&
                    !string.IsNullOrEmpty(Cpname))
                {
                    string agencyName = row[Cpname].ToString();
                    if (!string.IsNullOrEmpty(agencyName))
                    {
                        cpNames.Add(agencyName);
                    }
                }
            }

            return cpNames.Distinct().ToList();
        }


        public static void SetProspect(this Domain.Entities.Prospect prospect, Dictionary<ProspectDataColumn, string>? mappedColumndata, Guid userId)
        {
            try
            {
                if (prospect != null)
                {
                    /* if (!string.IsNullOrWhiteSpace(prospect.ContactNo))
                     {
                         var multiNumbers = prospect.ContactNo.Contains(',') ? prospect.ContactNo.Split(',') : prospect.ContactNo.Contains('\\') ? prospect.ContactNo.Split('\\') : prospect.ContactNo.Split('/');
                         if (multiNumbers.Length > 1 && !mappedColumndata.ContainsKey(ProspectDataColumn.AltContactNo))
                         {
                             prospect.AlternateContactNo = multiNumbers[1];
                         }
                         prospect.ContactNo = multiNumbers[0];
                         if (prospect.ContactNo.ToLower().Contains('e'))
                         {
                             if (double.TryParse(prospect.ContactNo.Replace("+91", ""), out double cNumber))
                             {
                                 prospect.ContactNo = (cNumber).ToString().Split('.')[0];
                             }
                         }
                         prospect.ContactNo = Regex.Replace(prospect.ContactNo, @"[^0-9]+", "");
                         if (prospect.ContactNo.Length > 10) { prospect.ContactNo = prospect.ContactNo[^10..]; }; prospect.ContactNo = $"+91{prospect.ContactNo.Trim()}";
                     }*/
                    prospect.CreatedBy = userId;
                    prospect.LastModifiedBy = userId;
                    prospect.AssignTo = userId;
                    prospect.AssignedFrom = userId;
                }
            }
            catch (Exception ex) { }
        }

        public static List<InvalidProspect> GetInvalidProspect(List<Domain.Entities.Prospect> prospects)
        {
            List<InvalidProspect> invalidDatas = new List<InvalidProspect>();
            Parallel.ForEach(prospects, (prospect) =>
            {
                if (string.IsNullOrEmpty(prospect.Name))
                {
                    var invalidName = prospect.Adapt<InvalidProspect>();
                    invalidName.Errors = "Invalid Name";
                    invalidDatas.Add(invalidName);
                    prospects.Remove(prospect);
                }
                if (string.IsNullOrEmpty(prospect.ContactNo))
                {
                    var invalidContact = prospect.Adapt<InvalidProspect>();
                    invalidContact.Errors = "Invalid Contact No";
                    invalidDatas.Add(invalidContact);
                    prospects.Remove(prospect);
                }
                if (string.IsNullOrEmpty(prospect.Email))
                {
                    var invalidEmail = prospect.Adapt<InvalidProspect>();
                    invalidEmail.Errors = "Invalid Email";
                    invalidDatas.Add(invalidEmail);
                    prospects.Remove(prospect);
                }
                if (string.IsNullOrEmpty(prospect.AlternateContactNo))
                {
                    var invalidAltContact = prospect.Adapt<InvalidProspect>();
                    invalidAltContact.Errors = "Invalid Alternate conatct";
                    invalidDatas.Add(invalidAltContact);
                    prospects.Remove(prospect);
                }
                if (string.IsNullOrEmpty(prospect.Notes))
                {
                    var invalidNotes = prospect.Adapt<InvalidProspect>();
                    invalidNotes.Errors = "Invalid Notes";
                    invalidDatas.Add(invalidNotes);
                    prospects.Remove(prospect);
                }
            });
            return invalidDatas;
        }

        public static List<string> GetUnmappedProspectColumnNames(this DataTable table, Dictionary<ProspectDataColumn, string> mappedColumnsData)
        {
            List<string> columns = new();
            foreach (DataColumn column in table.Columns)
            {
                if (!mappedColumnsData.ContainsValue(column.ColumnName) && !column.ColumnName.Contains("S. No"))
                {
                    if (!columns.Contains(column.ColumnName))
                    {
                        columns.Add(column.ColumnName);
                    }
                }
            }
            return columns;
        }

        public static void AssignProspect(this List<Domain.Entities.Prospect> prospects, List<Guid> userIds, Guid currentUserId)
        {
            #region Assigning User
            try
            {
                if (userIds != null && userIds.Count > 0)
                {
                    var filteredUserIds = userIds.Where(i => i != null).ToList();

                    if (filteredUserIds.Count > 0 && prospects.Any())
                    {
                        int userCount = filteredUserIds.Count;
                        int index = 0;
                        foreach (var prospect in prospects)
                        {
                            if (index < userCount)
                            {
                                prospect.AssignTo = filteredUserIds[index];
                                prospect.AssignedFrom = currentUserId;
                            }
                            else
                            {
                                index = 0;
                                prospect.AssignTo = filteredUserIds[index];
                                prospect.AssignedFrom = currentUserId;
                            }
                            index++;
                        }
                    }
                }
            }
            catch (Exception e)
            {

            }
            #endregion
        }

        public static MemoryStream CreateExcelData(List<InvalidProspect> inValidData)
        {
            using MemoryStream stream = new MemoryStream();
            SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Create(stream, SpreadsheetDocumentType.Workbook);
            WorkbookPart workbookpart = spreadsheetDocument.AddWorkbookPart();

            workbookpart.Workbook = new DocumentFormat.OpenXml.Spreadsheet.Workbook();
            WorksheetPart worksheetPart = workbookpart.AddNewPart<WorksheetPart>();
            worksheetPart.Worksheet = new Worksheet(new SheetData());
            Sheets sheets = spreadsheetDocument.WorkbookPart.Workbook.AppendChild<Sheets>(new Sheets());
            Sheet sheet = new Sheet()
            {
                Id = spreadsheetDocument.WorkbookPart.GetIdOfPart(worksheetPart),
                SheetId = 1,
                Name = "mySheet"
            };
            sheets.Append(sheet);
            Worksheet worksheet = worksheetPart.Worksheet;

            SheetData sheetData = worksheet.GetFirstChild<SheetData>();
            List<string> headers = new();
            var obj = inValidData.FirstOrDefault();
            Type objType = obj.GetType();
            System.Reflection.PropertyInfo[] properties = objType.GetProperties();
            foreach (System.Reflection.PropertyInfo property in properties)
            {
                headers.Add(property.Name);
            }
            Row row1 = new Row();
            foreach (var item in headers)
            {
                Cell cell1 = new Cell()
                {
                    CellValue = new CellValue(item),
                    DataType = CellValues.String,

                };
                row1.Append(cell1);
            }

            sheetData.Append(row1);
            foreach (var inValid in inValidData)
            {
                Row row = new Row();
                CreateCell(row, inValid.Created.ToString(), CellValues.Date);
                CreateCell(row, inValid.Name);
                CreateCell(row, inValid.ContactNo);
                CreateCell(row, inValid.AlternateContactNo);
                CreateCell(row, inValid.Email);
                //CreateCell(row, inValid.State);
                CreateCell(row, inValid.Notes);
                //CreateCell(row, inValid.EnquiryFor);
                //CreateCell(row, inValid?.City);
                //CreateCell(row, inValid.State);
                //CreateCell(row, inValid.Location);
                //CreateCell(row, inValid.BHKType);
                //CreateCell(row, inValid.NoOfBHKs);
                CreateCell(row, inValid.Source);
                CreateCell(row, inValid.SubSource);
                //CreateCell(row, inValid.Property);
                CreateCell(row, inValid.AssignTo);
                CreateCell(row, inValid.Errors);
                //CreateCell(row, inValid.RepeatedCount.ToString(), CellValues.Number);
                sheetData.Append(row);

            }
            worksheetPart.Worksheet.Save();
            workbookpart.Workbook.Save();
            spreadsheetDocument.Close();
            return stream;
        }
        public static MemoryStream V2CreateExcelData(List<V2InvalidProspect> inValidData)
        {
            using MemoryStream stream = new MemoryStream();
            SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Create(stream, SpreadsheetDocumentType.Workbook);
            WorkbookPart workbookpart = spreadsheetDocument.AddWorkbookPart();

            workbookpart.Workbook = new DocumentFormat.OpenXml.Spreadsheet.Workbook();
            WorksheetPart worksheetPart = workbookpart.AddNewPart<WorksheetPart>();
            worksheetPart.Worksheet = new Worksheet(new SheetData());
            Sheets sheets = spreadsheetDocument.WorkbookPart.Workbook.AppendChild<Sheets>(new Sheets());
            Sheet sheet = new Sheet()
            {
                Id = spreadsheetDocument.WorkbookPart.GetIdOfPart(worksheetPart),
                SheetId = 1,
                Name = "mySheet"
            };
            sheets.Append(sheet);
            Worksheet worksheet = worksheetPart.Worksheet;

            SheetData sheetData = worksheet.GetFirstChild<SheetData>();
            List<string> headers = new();
            var obj = inValidData.FirstOrDefault();
            Type objType = obj.GetType();
            System.Reflection.PropertyInfo[] properties = objType.GetProperties();
            foreach (System.Reflection.PropertyInfo property in properties)
            {
                headers.Add(property.Name);
            }
            Row row1 = new Row();
            foreach (var item in headers)
            {
                Cell cell1 = new Cell()
                {
                    CellValue = new CellValue(item),
                    DataType = CellValues.String,

                };
                row1.Append(cell1);
            }

            sheetData.Append(row1);
            foreach (var inValid in inValidData)
            {
                Row row = new Row();
                CreateCell(row, inValid.Created.ToString(), CellValues.Date);
                CreateCell(row, inValid.Name);
                CreateCell(row, inValid.ContactNo);
                CreateCell(row, inValid.AlternateContactNo);
                CreateCell(row, inValid.Email);
                //CreateCell(row, inValid.State);
                CreateCell(row, inValid.Notes);
                //CreateCell(row, inValid.EnquiryFor);
                //CreateCell(row, inValid?.City);
                //CreateCell(row, inValid.State);
                //CreateCell(row, inValid.Location);
                //CreateCell(row, inValid.BHKType);
                //CreateCell(row, inValid.NoOfBHKs);
                CreateCell(row, inValid.Source);
                CreateCell(row, inValid.SubSource);
                //CreateCell(row, inValid.Property);
                CreateCell(row, inValid.Errors);
                //CreateCell(row, inValid.RepeatedCount.ToString(), CellValues.Number);
                sheetData.Append(row);

            }
            worksheetPart.Worksheet.Save();
            workbookpart.Workbook.Save();
            spreadsheetDocument.Close();
            return stream;
        }

        public static void CreateCell(Row row, string value, CellValues type = CellValues.String)
        {
            Cell cell = new Cell()
            {
                CellValue = new CellValue(value),
                DataType = type,
            };
            row.Append(cell);
        }


        public static string InsertSpaceBeforeCapital(string input)
        {
            StringBuilder result = new StringBuilder();

            for (int i = 0; i < input.Length; i++)
            {
                if (i > 0 && char.IsUpper(input[i]))
                {
                    result.Append(' ');
                }

                result.Append(input[i]);
            }

            return result.ToString();
        }

        public static (List<Lrb.Domain.Entities.Prospect>, List<V2InvalidProspect>) ConvertToProspectV2(this DataTable dataTable,
            Dictionary<ProspectDataColumn, string> dataColumn,
            List<string> unMappedColumn,
            MasterItems masterItems,
            Domain.Entities.BulkProspectUploadTracker tracker,
            Guid currentUserId,
            IRepositoryWithEvents<Lrb.Domain.Entities.Source> _sourceRepo, string? jsonData = null)

        {
            List<Prospect> prospects = new();
            List<V2InvalidProspect> invalidProspects = new();
            PhoneNumberUtil phoneUtil = PhoneNumberUtil.GetInstance();
            Guid? defaultUnitId = Guid.TryParse(masterItems?.GlobalSettings?.DefaultValues?.FirstOrDefault().Value, out Guid parsedGuid) ? parsedGuid : (Guid?)null;
            foreach (DataRow dataRow in dataTable.Rows)
            {
                #region GetData
                string? enquiryFor = !dataColumn.ContainsKey(ProspectDataColumn.EnquiryFor) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.EnquiryFor]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.EnquiryFor]].ToString();
                string? bhkType = !dataColumn.ContainsKey(ProspectDataColumn.BHKType) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.BHKType]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.BHKType]].ToString();
                string? noOfBhks = !dataColumn.ContainsKey(ProspectDataColumn.NoOfBHKs) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.NoOfBHKs]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.NoOfBHKs]].ToString();
                string? baseProperty = !dataColumn.ContainsKey(ProspectDataColumn.BasePropertyType) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.BasePropertyType]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.BasePropertyType]].ToString();
                string? subProperty = !dataColumn.ContainsKey(ProspectDataColumn.SubPropertyType) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.SubPropertyType]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.SubPropertyType]].ToString();
                string? lowerBudget = !dataColumn.ContainsKey(ProspectDataColumn.LowerBudget) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.LowerBudget]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.LowerBudget]].ToString();
                string? upperBudget = !dataColumn.ContainsKey(ProspectDataColumn.UpperBudget) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.UpperBudget]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.UpperBudget]].ToString();
                string? dataSource = !dataColumn.ContainsKey(ProspectDataColumn.Source) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.Source]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.Source]].ToString();
                string currecncy = !dataColumn.ContainsKey(ProspectDataColumn.Currency) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.Currency]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.Currency]].ToString();
                var enquiryInfo = BulkUploadHelper.GetEnquiryForInfo(enquiryFor ?? string.Empty);
                var lowerBudgetInfo = BudgetHelper.ConvertBuget(lowerBudget ?? string.Empty);
                var upperBudgetInfo = BudgetHelper.ConvertBuget(upperBudget ?? string.Empty);
                var validSource = GetValidDataSource(dataSource ?? string.Empty, masterItems.ProspectSources, _sourceRepo);
                var status = masterItems.ProspectStatuses?.Where(i => !i.IsDeleted && i.IsDefault).FirstOrDefault();
                var subsource = !dataColumn.ContainsKey(ProspectDataColumn.SubSource) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.SubSource]) ? null : dataRow[dataColumn[ProspectDataColumn.SubSource]].ToString();
                var projectName = !dataColumn.ContainsKey(ProspectDataColumn.Project) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.Project]) ? null : dataRow[dataColumn[ProspectDataColumn.Project]].ToString();
                var project = masterItems.Projects?.FirstOrDefault(i => i.Name.Trim().ToLower().Equals(projectName?.Trim().ToLower(), StringComparison.InvariantCultureIgnoreCase));
                var propertyName = !dataColumn.ContainsKey(ProspectDataColumn.Property) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.Property]) ? null : dataRow[dataColumn[ProspectDataColumn.Property]].ToString();
                var property = masterItems.Properties?.FirstOrDefault(i => i.Title.Trim().ToLower().Equals(propertyName?.Trim().ToLower(), StringComparison.InvariantCultureIgnoreCase));
                var agencyName = !dataColumn.ContainsKey(ProspectDataColumn.AgencyName) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.AgencyName]) ? null : dataRow[dataColumn[ProspectDataColumn.AgencyName]].ToString();
                var agencyToAdd = masterItems.Agencies?.FirstOrDefault(i => i.Name.Replace(" ", "").Trim().Equals(agencyName?.Replace(" ", "").Trim(), StringComparison.InvariantCultureIgnoreCase));
                var currencycode = dataColumn.GetCurrencySymbol1(dataRow, currecncy, masterItems.GlobalSettings?.Countries?.FirstOrDefault()?.DefaultCurrency);
                var cpName = !dataColumn.ContainsKey(ProspectDataColumn.ChannelPartnerName) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.ChannelPartnerName]) ? null : dataRow[dataColumn[ProspectDataColumn.ChannelPartnerName]].ToString();
                var channelPartenr = masterItems.ChannelPartners?.FirstOrDefault(i => i.FirmName.Trim().ToLower().Equals(cpName?.Trim().ToLower(), StringComparison.InvariantCultureIgnoreCase));
                string countryCode = !dataColumn.ContainsKey(ProspectDataColumn.CountryCode) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.CountryCode]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.CountryCode]].ToString();
                string altCountryCode = !dataColumn.ContainsKey(ProspectDataColumn.AlternativeNoCountryCode) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.AlternativeNoCountryCode]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.AlternativeNoCountryCode]].ToString();
                var beds = !dataColumn.ContainsKey(ProspectDataColumn.Beds) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.Beds]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.Beds]].ToString();
                var baths = !dataColumn.ContainsKey(ProspectDataColumn.Baths) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.Baths]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.Baths]].ToString();
                var furnishStatus = !dataColumn.ContainsKey(ProspectDataColumn.FurnishStatus) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.FurnishStatus]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.FurnishStatus]].ToString();
                string floor = !dataColumn.ContainsKey(ProspectDataColumn.PreferredFloor) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.PreferredFloor]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.PreferredFloor]].ToString();
                string bHKTypes = !dataColumn.ContainsKey(ProspectDataColumn.BHKType) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.BHKType]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.BHKType]].ToString();
                var offeringType = !dataColumn.ContainsKey(ProspectDataColumn.OfferingType) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.OfferingType]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.OfferingType]].ToString();
                 //var offeringTypeInfo = BulkUploadHelper.GetOfferingTypesInfo(offeringType ?? string.Empty);
                var isOfferTypeValid = BulkUploadHelper.TryParseGetEnum<OfferType>(offeringType, out var parsedOfferType);
                var propertyInfo = BulkUploadHelper.GetPropertyType(baseProperty, subProperty, masterItems.PropetyTypes, bhkType, noOfBhks, beds, baths, furnishStatus, floor);
                string? carpetArea = !dataColumn.ContainsKey(ProspectDataColumn.CarpetArea) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.CarpetArea]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.CarpetArea]].ToString();
                string? carpetAreaUnit = !dataColumn.ContainsKey(ProspectDataColumn.CarpetAreaUnit) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.CarpetAreaUnit]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.CarpetAreaUnit]].ToString();
                var carpetarea = GetUnitDetails(carpetArea ?? string.Empty, masterItems.AreaUnits, carpetAreaUnit ?? string.Empty,defaultUnitId);
                string? propertyArea = !dataColumn.ContainsKey(ProspectDataColumn.PropertyArea) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.PropertyArea]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.PropertyArea]].ToString();
                string? propertyAreaUnit = !dataColumn.ContainsKey(ProspectDataColumn.PropertyAreaUnit) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.PropertyAreaUnit]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.PropertyAreaUnit]].ToString();
                var propertyArea1 = GetUnitDetails(propertyArea ?? string.Empty, masterItems.AreaUnits, propertyAreaUnit ?? string.Empty, defaultUnitId);
                string? netArea = !dataColumn.ContainsKey(ProspectDataColumn.NetArea) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.NetArea]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.NetArea]].ToString();
                string? netAreaUnit = !dataColumn.ContainsKey(ProspectDataColumn.NetAreaUnit) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.NetAreaUnit]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.NetAreaUnit]].ToString();
                var netArea1 = GetUnitDetails(netArea ?? string.Empty, masterItems.AreaUnits, netAreaUnit ?? string.Empty, defaultUnitId);
                string? builtUpArea = !dataColumn.ContainsKey(ProspectDataColumn.BuiltUpArea) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.BuiltUpArea]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.BuiltUpArea]].ToString();
                string? builtUpAreaUnit = !dataColumn.ContainsKey(ProspectDataColumn.BuiltUpAreaUnit) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.BuiltUpAreaUnit]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.BuiltUpAreaUnit]].ToString();
                var builtUpArea1 = GetUnitDetails(builtUpArea ?? string.Empty, masterItems.AreaUnits, builtUpAreaUnit ?? string.Empty, defaultUnitId);
                string? SaleableArea = !dataColumn.ContainsKey(ProspectDataColumn.SaleableArea) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.SaleableArea]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.SaleableArea]].ToString();
                string? SaleableAreaUnit = !dataColumn.ContainsKey(ProspectDataColumn.SaleableAreaUnit) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.SaleableAreaUnit]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.SaleableAreaUnit]].ToString();
                var SaleableArea1 = GetUnitDetails(SaleableArea ?? string.Empty, masterItems.AreaUnits, SaleableAreaUnit ?? string.Empty);
                var purpose = !dataColumn.ContainsKey(ProspectDataColumn.Purpose) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.Purpose]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.Purpose]].ToString();
                var purposees = BulkUploadHelper.GetPurposeInfo(purpose ?? string.Empty);

                var campaignName = !dataColumn.ContainsKey(ProspectDataColumn.CampaignName) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.CampaignName]) ? null : dataRow[dataColumn[ProspectDataColumn.CampaignName]].ToString();
                var campaign = masterItems.Campaigns?.FirstOrDefault(i => i.Name.Trim().ToLower().Equals(campaignName?.Trim().ToLower(), StringComparison.InvariantCultureIgnoreCase));
                var sourcinguser = dataColumn.GetStringValue(ProspectDataColumn.SourcingManager, dataRow);
                var sourcinguserName = masterItems.Users?.FirstOrDefault(u => u.UserName.ToLower() == sourcinguser.ToLower().Trim().Replace(" ", "") && !u.IsDeleted);
                var ClosingUser = dataColumn.GetStringValue(ProspectDataColumn.ClosingManager, dataRow);
                var ClosingUserName = masterItems.Users?.FirstOrDefault(u => u.UserName.ToLower() == ClosingUser.ToLower().Trim().Replace(" ", "") && !u.IsDeleted);
                var profession = !dataColumn.ContainsKey(ProspectDataColumn.Profession) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.Profession]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.Profession]].ToString();
                var validProfession = BulkUploadHelper.GetProfessionInfo(profession ?? string.Empty);
                var possesionType = !dataColumn.ContainsKey(ProspectDataColumn.PossesionType) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.PossesionType]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.PossesionType]].ToString();
                var possessionDate = dataColumn.GetPossessionDate(ProspectDataColumn.PossessionDate, dataRow, jsonData);
                bool isValidPossessionDate = possessionDate.HasValue;
                DateTime? dateTime = possessionDate;
                if (possesionType != "None" && possesionType != "CustomDate")
                {
                    DateTime currentUtcTime = DateTime.UtcNow;
                    possessionDate = currentUtcTime;
                    isValidPossessionDate = true;

                    switch (possesionType)
                    {
                        case "UnderConstruction":
                            dateTime = null;
                            possesionType = PossesionType.UnderConstruction.ToString(); ;

                            break;
                        case "Next 3 Months":
                            dateTime = currentUtcTime.AddMonths(3);
                            possesionType = PossesionType.ThreeMonth.ToString(); ;

                            break;

                        case "3–6 Months":
                            dateTime = currentUtcTime.AddMonths(6);
                            possesionType = PossesionType.SixMonth.ToString(); ;

                            break;

                        case "6–12 Months":
                            dateTime = currentUtcTime.AddYears(1);
                            possesionType = PossesionType.Year.ToString(); ;

                            break;

                        case "1-2 year":
                            dateTime = currentUtcTime.AddYears(2);
                            possesionType = PossesionType.TwoYears.ToString(); ;

                            break;

                        case "Immediate Possession (within 30 days)":
                            dateTime = currentUtcTime.AddMonths(1);
                            possesionType = PossesionType.Immediate.ToString(); ;
                            break;
                    }
                }
                if (dateTime.HasValue && (possesionType == null || possesionType == "None" || string.IsNullOrWhiteSpace(possesionType)))
                {
                    possesionType = PossesionType.CustomDate.ToString();
                }
                var landline = !dataColumn.ContainsKey(ProspectDataColumn.LandLine) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.LandLine]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.LandLine]].ToString();

                var gender = !dataColumn.ContainsKey(ProspectDataColumn.Gender) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.Gender]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.Gender]].ToString();
                var validGenderType = BulkUploadHelper.GetGenderInfo(gender ?? string.Empty);
                var maritalStatus = !dataColumn.ContainsKey(ProspectDataColumn.MaritalStatus) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.MaritalStatus]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.MaritalStatus]].ToString();
                var validMaritalStatusType = BulkUploadHelper.GetMaritalStatusInfo(maritalStatus ?? string.Empty);
                var dateOdBirth = !dataColumn.ContainsKey(ProspectDataColumn.DateOfBirth) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.DateOfBirth]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.DateOfBirth]].ToString();
                var validDateOfBirth = dataColumn.GetDate(ProspectDataColumn.DateOfBirth, dataRow, jsonData);
                var anniversaryDate = !dataColumn.ContainsKey(ProspectDataColumn.AnniversaryDate) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.AnniversaryDate]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.AnniversaryDate]].ToString();
                var validanniversaryDate = dataColumn.GetDate(ProspectDataColumn.AnniversaryDate, dataRow, jsonData);

                var prospect = new Prospect()
                {
                    Id = Guid.NewGuid(),
                    Name = !dataColumn.ContainsKey(ProspectDataColumn.Name) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.Name]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.Name]].ToString(),
                    ContactNo = !dataColumn.ContainsKey(ProspectDataColumn.ContactNo) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.ContactNo]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.ContactNo]].ToString(),
                    AlternateContactNo = !dataColumn.ContainsKey(ProspectDataColumn.AltContactNo) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.AltContactNo]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.AltContactNo]].ToString(),
                    Email = !dataColumn.ContainsKey(ProspectDataColumn.Email) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.Email]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.Email]].ToString(),
                    Notes = !dataColumn.ContainsKey(ProspectDataColumn.Notes) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.Notes]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.Notes]].ToString(),
                    Enquiries = new List<ProspectEnquiry>()
                    {
                        new()
                        {
                            Id= Guid.NewGuid(),
                            IsPrimary = true,
                            //EnquiryType = propertyInfo.IsValidInfo ? enquiryInfo.EnquiredFor : default,
                            EnquiryTypes=enquiryInfo.IsValidInfo?enquiryInfo.EnquiryTypes : default,
                            BHKTypes = propertyInfo.IsValidInfo ? propertyInfo.BHKTypes : default,
                            //NoOfBhks = propertyInfo.IsValidInfo ? propertyInfo.NoOfBHK : default,
                            BHKs = propertyInfo.IsValidInfo?propertyInfo.BHKs : default,
                            PropertyType = propertyInfo.IsValidInfo ? propertyInfo.PropertyType : default,
                            LowerBudget = lowerBudgetInfo.IsValidInfo ? lowerBudgetInfo.Budget : null,
                            UpperBudget = upperBudgetInfo.IsValidInfo ? upperBudgetInfo.Budget : null,
                            Currency = currencycode ?? "INR",
                            PropertyTypes = propertyInfo.IsValidInfo ? propertyInfo.PropertyTypes : default,
                            Addresses =
                            ((dataColumn.ContainsKey(ProspectDataColumn.City) && !string.IsNullOrEmpty(dataColumn[ProspectDataColumn.City]) && !string.IsNullOrEmpty(dataRow[dataColumn[ProspectDataColumn.City]].ToString()))
                            ||(dataColumn.ContainsKey(ProspectDataColumn.State) && !string.IsNullOrEmpty(dataColumn[ProspectDataColumn.State]) && !string.IsNullOrEmpty(dataRow[dataColumn[ProspectDataColumn.State]].ToString()))
                            ||(dataColumn.ContainsKey(ProspectDataColumn.Location) && !string.IsNullOrEmpty(dataColumn[ProspectDataColumn.Location]) && !string.IsNullOrEmpty(dataRow[dataColumn[ProspectDataColumn.Location]].ToString()))
                            ||(dataColumn.ContainsKey(ProspectDataColumn.Community) && !string.IsNullOrEmpty(dataColumn[ProspectDataColumn.Community]) && !string.IsNullOrEmpty(dataRow[dataColumn[ProspectDataColumn.Community]].ToString()))
                            ||(dataColumn.ContainsKey(ProspectDataColumn.SubCommunity) && !string.IsNullOrEmpty(dataColumn[ProspectDataColumn.SubCommunity]) && !string.IsNullOrEmpty(dataRow[dataColumn[ProspectDataColumn.SubCommunity]].ToString()))
                            ||(dataColumn.ContainsKey(ProspectDataColumn.TowerName) && !string.IsNullOrEmpty(dataColumn[ProspectDataColumn.TowerName]) && !string.IsNullOrEmpty(dataRow[dataColumn[ProspectDataColumn.TowerName]].ToString()))
                            ||(dataColumn.ContainsKey(ProspectDataColumn.Country) && !string.IsNullOrEmpty(dataColumn[ProspectDataColumn.Country]) && !string.IsNullOrEmpty(dataRow[dataColumn[ProspectDataColumn.Country]].ToString()))
                            ||(dataColumn.ContainsKey(ProspectDataColumn.PostalCode) && !string.IsNullOrEmpty(dataColumn[ProspectDataColumn.PostalCode]) && !string.IsNullOrEmpty(dataRow[dataColumn[ProspectDataColumn.PostalCode]].ToString()))) ? 
                            new()
                            {
                                new()
                                {
                                Id = Guid.NewGuid(),
                                City = !dataColumn.ContainsKey(ProspectDataColumn.City) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.City]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.City]].ToString(),
                                State = !dataColumn.ContainsKey(ProspectDataColumn.State) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.State]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.State]].ToString(),
                                Locality = !dataColumn.ContainsKey(ProspectDataColumn.Location) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.Location]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.Location]].ToString(),
                                Community = !dataColumn.ContainsKey(ProspectDataColumn.Community) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.Community]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.Community]].ToString(),
                                SubCommunity = !dataColumn.ContainsKey(ProspectDataColumn.SubCommunity) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.SubCommunity]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.SubCommunity]].ToString(),
                                TowerName = !dataColumn.ContainsKey(ProspectDataColumn.TowerName) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.TowerName]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.TowerName]].ToString(),
                                Country = !dataColumn.ContainsKey(ProspectDataColumn.Country) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.Country]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.Country]].ToString(),
                                PostalCode=!dataColumn.ContainsKey(ProspectDataColumn.PostalCode) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.PostalCode]) ? string.Empty :dataRow[dataColumn[ProspectDataColumn.PostalCode]].ToString(), }
                            } : null,
                            Beds=propertyInfo.IsValidInfo ? propertyInfo.Beds : default,
                            Baths=propertyInfo.IsValidInfo ? propertyInfo.Baths : default,
                            Furnished=propertyInfo.IsValidInfo ? propertyInfo.Furnished : null,
                            Floors =propertyInfo.IsValidInfo ? propertyInfo.Floors :default,
                            OfferType = isOfferTypeValid ? parsedOfferType : OfferType.None,
                            CarpetArea =  carpetarea.Item1,
                            CarpetAreaUnitId = carpetarea.Item2,
                            PropertyArea =  propertyArea1.Item1,
                            PropertyAreaUnitId = propertyArea1.Item2,
                            NetArea =  netArea1.Item1,
                            NetAreaUnitId = netArea1.Item2,
                            BuiltUpArea =  builtUpArea1.Item1,BuiltUpAreaUnitId = builtUpArea1.Item2,SaleableArea =  SaleableArea1.Item1,SaleableAreaUnitId = SaleableArea1.Item2,
                            UnitName = !dataColumn.ContainsKey(ProspectDataColumn.UnitName) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.UnitName]) ? null : dataRow[dataColumn[ProspectDataColumn.UnitName]].ToString(),
                            ClusterName = !dataColumn.ContainsKey(ProspectDataColumn.ClusterName) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.ClusterName]) ? null : dataRow[dataColumn[ProspectDataColumn.ClusterName]].ToString(),
                            Purpose = purposees.IsValidInfo ? purposees.Purpose :Purpose.None,
                            PossesionType = Enum.TryParse(possesionType, true, out PossesionType parsedType) && (parsedType != PossesionType.CustomDate || isValidPossessionDate) ? parsedType : default,
                        }
                    },
                    Properties = property != null ? new List<Domain.Entities.Property>() { property } : null,
                    Projects = project != null ? new List<Domain.Entities.Project>() { project } : null,
                    Status = status ?? null,
                    AgencyName = !dataColumn.ContainsKey(ProspectDataColumn.AgencyName) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.AgencyName]) ? null : dataRow[dataColumn[ProspectDataColumn.AgencyName]].ToString(),
                    Agencies = agencyToAdd != null ? new List<Domain.Entities.Agency>() { agencyToAdd } : null,
                    CompanyName = !dataColumn.ContainsKey(ProspectDataColumn.CompanyName) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.CompanyName]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.CompanyName]].ToString(),
                    ChannelPartners = channelPartenr != null ? new List<Domain.Entities.ChannelPartner>() { channelPartenr } : null,
                    CreatedOn = DateTime.UtcNow,
                    LastModifiedOn = DateTime.UtcNow,
                    CreatedBy = currentUserId,
                    LastModifiedBy = currentUserId,
                    CountryCode = countryCode ?? masterItems.GlobalSettings?.Countries?.FirstOrDefault()?.DefaultCallingCode ?? "+91",
                    AltCountryCode = altCountryCode ?? masterItems.GlobalSettings?.Countries?.FirstOrDefault()?.DefaultCallingCode ?? "+91",
                    ReferralName = !dataColumn.ContainsKey(ProspectDataColumn.ReferralName) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.ReferralName]) ? null : dataRow[dataColumn[ProspectDataColumn.ReferralName]].ToString(),
                    ReferralContactNo = !dataColumn.ContainsKey(ProspectDataColumn.ReferralContactNo) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.ReferralContactNo]) ? null : dataRow[dataColumn[ProspectDataColumn.ReferralContactNo]].ToString(),
                    ReferralEmail = !dataColumn.ContainsKey(ProspectDataColumn.ReferralEmail) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.ReferralEmail]) ? null : dataRow[dataColumn[ProspectDataColumn.ReferralEmail]].ToString(),
                    Designation = !dataColumn.ContainsKey(ProspectDataColumn.Designation) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.Designation]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.Designation]].ToString(),
                    ExecutiveName = !dataColumn.ContainsKey(ProspectDataColumn.ChannelPartnerExecutiveName) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.ChannelPartnerExecutiveName]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.ChannelPartnerExecutiveName]].ToString(),
                    ChannelPartnerName = !dataColumn.ContainsKey(ProspectDataColumn.ChannelPartnerName) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.ChannelPartnerName]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.ChannelPartnerName]].ToString(),
                    ExecutiveContactNo = !dataColumn.ContainsKey(ProspectDataColumn.ChannelPartnerContactNo) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.ChannelPartnerContactNo]) ? null : dataRow[dataColumn[ProspectDataColumn.ChannelPartnerContactNo]].ToString().Trim(),
                    Nationality = !dataColumn.ContainsKey(ProspectDataColumn.Nationality) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.Nationality]) ? null : dataRow[dataColumn[ProspectDataColumn.Nationality]].ToString(),
                    Campaigns = campaign != null ? new List<Domain.Entities.Campaign>() { campaign } : null,
                    Address = ((dataColumn.ContainsKey(ProspectDataColumn.CustomerCity) && !string.IsNullOrEmpty(dataColumn[ProspectDataColumn.CustomerCity]) && !string.IsNullOrEmpty(dataRow[dataColumn[ProspectDataColumn.CustomerCity]].ToString()))
                    || (dataColumn.ContainsKey(ProspectDataColumn.CustomerState) && !string.IsNullOrEmpty(dataColumn[ProspectDataColumn.CustomerState]) && !string.IsNullOrEmpty(dataRow[dataColumn[ProspectDataColumn.CustomerState]].ToString()))
                    || (dataColumn.ContainsKey(ProspectDataColumn.CustomerLocation) && !string.IsNullOrEmpty(dataColumn[ProspectDataColumn.CustomerLocation]) && !string.IsNullOrEmpty(dataRow[dataColumn[ProspectDataColumn.CustomerLocation]].ToString()))
                    || (dataColumn.ContainsKey(ProspectDataColumn.CustomerCommunity) && !string.IsNullOrEmpty(dataColumn[ProspectDataColumn.CustomerCommunity]) && !string.IsNullOrEmpty(dataRow[dataColumn[ProspectDataColumn.CustomerCommunity]].ToString()))
                    || (dataColumn.ContainsKey(ProspectDataColumn.CustomerSubCommunity) && !string.IsNullOrEmpty(dataColumn[ProspectDataColumn.CustomerSubCommunity]) && !string.IsNullOrEmpty(dataRow[dataColumn[ProspectDataColumn.CustomerSubCommunity]].ToString()))
                    || (dataColumn.ContainsKey(ProspectDataColumn.CustomerTowerName) && !string.IsNullOrEmpty(dataColumn[ProspectDataColumn.CustomerTowerName]) && !string.IsNullOrEmpty(dataRow[dataColumn[ProspectDataColumn.CustomerTowerName]].ToString()))
                    || (dataColumn.ContainsKey(ProspectDataColumn.CustomerCountry) && !string.IsNullOrEmpty(dataColumn[ProspectDataColumn.CustomerCountry]) && !string.IsNullOrEmpty(dataRow[dataColumn[ProspectDataColumn.CustomerCountry]].ToString()))
                    || (dataColumn.ContainsKey(ProspectDataColumn.CustomerPostalCode) && !string.IsNullOrEmpty(dataColumn[ProspectDataColumn.CustomerPostalCode]) && !string.IsNullOrEmpty(dataRow[dataColumn[ProspectDataColumn.CustomerPostalCode]].ToString()))) ?
                    new Address
                    {
                        Id = Guid.NewGuid(),
                        City = !dataColumn.ContainsKey(ProspectDataColumn.CustomerCity) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.CustomerCity]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.CustomerCity]].ToString(),
                        State = !dataColumn.ContainsKey(ProspectDataColumn.CustomerState) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.CustomerState]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.CustomerState]].ToString(),
                        SubLocality = !dataColumn.ContainsKey(ProspectDataColumn.CustomerLocation) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.CustomerLocation]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.CustomerLocation]].ToString(),
                        Community = !dataColumn.ContainsKey(ProspectDataColumn.CustomerCommunity) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.CustomerCommunity]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.CustomerCommunity]].ToString(),
                        SubCommunity = !dataColumn.ContainsKey(ProspectDataColumn.CustomerSubCommunity) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.CustomerSubCommunity]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.CustomerSubCommunity]].ToString(),
                        TowerName = !dataColumn.ContainsKey(ProspectDataColumn.CustomerTowerName) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.CustomerTowerName]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.CustomerTowerName]].ToString(),
                        Country = !dataColumn.ContainsKey(ProspectDataColumn.CustomerCountry) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.CustomerCountry]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.CustomerCountry]].ToString(),
                        PostalCode = !dataColumn.ContainsKey(ProspectDataColumn.CustomerPostalCode) || string.IsNullOrEmpty(dataColumn[ProspectDataColumn.CustomerPostalCode]) ? string.Empty : dataRow[dataColumn[ProspectDataColumn.CustomerPostalCode]].ToString(),
                    } : null,
                    SourcingManager = sourcinguserName?.Id ?? Guid.Empty,
                    ClosingManager = ClosingUserName?.Id ?? Guid.Empty,
                    Profession = validProfession.IsValidInfo ? validProfession.Profession : Profession.None,
                    PossesionDate = isValidPossessionDate ? dateTime?.ToUniversalTime() : null,
                    LandLine = (!string.IsNullOrWhiteSpace(landline) && Regex.IsMatch(Regex.Replace(landline, @"[^0-9\-]", ""), @"^[0-9\-]{6,16}$")) ? Regex.Replace(landline, @"[^0-9\-]", "") : string.Empty,

                    UploadTypeName = tracker?.S3BucketKey,
                    Gender = validGenderType.IsValidInfo ? validGenderType.Gender : Gender.NotMentioned,
                    MaritalStatus = validMaritalStatusType.IsValidInfo ? validMaritalStatusType.MaritalStatusType : MaritalStatusType.NotMentioned,
                    DateOfBirth = validDateOfBirth ?? null,
                    AnniversaryDate = validMaritalStatusType.IsValidInfo && validMaritalStatusType.MaritalStatusType == MaritalStatusType.Married ? validanniversaryDate : null,
                    
                };
                V2InvalidProspect invalidProspect = null;
                bool isInvalidContact = false;
                if (!string.IsNullOrEmpty(prospect.ContactNo))
                {
                    var contactNo = phoneUtil.V2ConcatenatePhoneNumber(prospect.CountryCode, prospect.ContactNo, masterItems.GlobalSettings ?? new());
                    if (string.IsNullOrEmpty(contactNo))
                    {
                        invalidProspect = prospect.Adapt<V2InvalidProspect>();
                        invalidProspect.Errors = "Invalid ContactNo";
                        invalidProspect.Source = prospect.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.Source?.DisplayName?.ToString() ?? prospect.Enquiries?.FirstOrDefault()?.Source?.DisplayName?.ToString();
                        invalidProspect.SubSource = prospect.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.SubSource ?? prospect.Enquiries?.FirstOrDefault()?.Source?.DisplayName?.ToString();
                        invalidProspect.Status = prospect.Status?.DisplayName ?? string.Empty;
                        invalidProspect.Created = prospect.CreatedOn.Date;
                        invalidProspects.Add(invalidProspect);
                        isInvalidContact = true;
                    }
                    prospect.ContactNo = contactNo;
                }
                else
                {
                    invalidProspect = prospect.Adapt<V2InvalidProspect>();
                    invalidProspect.Errors = "Invalid ContactNo";
                    invalidProspect.Source = prospect.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.Source?.DisplayName?.ToString() ?? prospect.Enquiries?.FirstOrDefault()?.Source?.DisplayName?.ToString();
                    invalidProspect.SubSource = prospect.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.SubSource ?? prospect.Enquiries?.FirstOrDefault()?.Source?.DisplayName?.ToString();
                    invalidProspect.Status = prospect.Status?.DisplayName ?? string.Empty;
                    invalidProspect.Created = prospect.CreatedOn.Date;
                    invalidProspects.Add(invalidProspect);
                    isInvalidContact = true;
                }
                prospect.AlternateContactNo = !string.IsNullOrEmpty(prospect.AlternateContactNo) ? phoneUtil.V2ConcatenatePhoneNumber(prospect.AltCountryCode, prospect.AlternateContactNo, masterItems.GlobalSettings ?? new()) : null;
                prospect.ReferralContactNo = !string.IsNullOrEmpty(prospect.AlternateContactNo) ? phoneUtil.V2ConcatenatePhoneNumber(null, prospect.ReferralContactNo, masterItems.GlobalSettings ?? new()) : null;
                prospect.ChannelPartnerContactNo = !string.IsNullOrEmpty(prospect.ChannelPartnerContactNo) ? phoneUtil.V2ConcatenatePhoneNumber(null, prospect.ChannelPartnerContactNo, masterItems.GlobalSettings ?? new()) : null;
                if (string.IsNullOrEmpty(prospect.Name))
                {
                    prospect.Name = "Unknown";
                }
                if (validSource.Result == null && !isInvalidContact)
                {
                    invalidProspect = prospect.Adapt<V2InvalidProspect>();
                    invalidProspect.Errors = "Invalid Source";
                    invalidProspect.Source = dataSource;
                    invalidProspect.SubSource = subsource;
                    invalidProspect.Status = prospect.Status?.DisplayName ?? string.Empty;
                    invalidProspect.Created = prospect.CreatedOn.Date;
                    invalidProspects.Add(invalidProspect);
                }
                else if (validSource?.Result != null)
                {
                    prospect.Enquiries.FirstOrDefault().Source = validSource.Result;
                    prospect.Enquiries.FirstOrDefault().SubSource = subsource;
                }
                if (!propertyInfo.IsValidInfo)
                {
                    prospect.Notes += !string.IsNullOrEmpty(propertyInfo.BasePropertyType) ? " \n" + "BaseProperty" + " - " + propertyInfo.BasePropertyType : string.Empty;
                    prospect.Notes += !string.IsNullOrEmpty(propertyInfo.SubPropertyType) ? ", \n" + "SubProperty" + " - " + propertyInfo.SubPropertyType : string.Empty;
                    prospect.Notes += !string.IsNullOrEmpty(propertyInfo.InvalidNoOfBHK) ? ", \n" + "NoOfBHK" + " - " + propertyInfo.InvalidNoOfBHK : string.Empty;
                    prospect.Notes += !string.IsNullOrEmpty(propertyInfo.InvalidBHKType) ? ", \n" + "BHKType" + " - " + propertyInfo.InvalidBHKType : string.Empty;
                }
                if (!upperBudgetInfo.IsValidInfo)
                {
                    prospect.Notes += !string.IsNullOrEmpty(upperBudgetInfo.Invalidbudget) ? ", \n" + "UpperBudget" + " - " + upperBudgetInfo.Invalidbudget : string.Empty;
                }
                if (!lowerBudgetInfo.IsValidInfo)
                {
                    prospect.Notes += !string.IsNullOrEmpty(lowerBudgetInfo.Invalidbudget) ? ", \n" + "LowerBudget" + " - " + lowerBudgetInfo.Invalidbudget : string.Empty;
                }
                #endregion
                var existingContacts = prospects.SelectMany(i => new[] { i.ContactNo, i.AlternateContactNo })
                     .Where(i => !string.IsNullOrWhiteSpace(i)).ToList();
                if (invalidProspect == null && (!existingContacts.Contains(prospect.ContactNo) && !existingContacts.Contains(prospect.AlternateContactNo)))
                {
                    prospects.Add(prospect);
                }
                else if ((existingContacts.Contains(prospect.ContactNo) || existingContacts.Contains(prospect.AlternateContactNo)))
                {
                    invalidProspect = prospect.Adapt<V2InvalidProspect>();
                    invalidProspect.Errors = "Duplicate ContactNo";
                    invalidProspect.Source = prospect.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.Source?.DisplayName?.ToString() ?? prospect.Enquiries?.FirstOrDefault()?.Source?.DisplayName?.ToString();
                    invalidProspect.SubSource = prospect.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.SubSource ?? prospect.Enquiries?.FirstOrDefault()?.Source?.DisplayName?.ToString();
                    invalidProspect.Status = prospect.Status?.DisplayName ?? string.Empty;
                    invalidProspect.Created = prospect.CreatedOn.Date;
                    invalidProspects.Add(invalidProspect);
                }
            }
            return (prospects, invalidProspects);

        }
        public static double GetArea(string number)
        {
            try
            {
                Regex regex = new Regex(@"^\d+(\.\d+)?$");
                Match match = regex.Match(number);
                double res = 0;

                if (match.Success)
                {
                    res = double.Parse(match.Value);
                }
                return res;
            }
            catch (Exception e)
            {
                throw;
            }

        }
        public static (double, Guid) GetUnitDetails(string unitAreaSize, List<MasterAreaUnit> areaUnits, string unit, Guid? areaUnitId = null)
        {
            var unitArea = GetArea(unitAreaSize);
            if (areaUnits.Count > 0)
            {
                var masterUnits = areaUnits?.ConvertAll(i => i?.Unit?.Replace(" ", "")?.Replace(".", "")?.ToLower()?.Trim() ?? string.Empty)?.ToList();
                var unitOfArea = Regex.Replace(unitAreaSize, "[a-zA-Z]", string.Empty).Trim();
                if (!string.IsNullOrWhiteSpace(unitOfArea) && !string.IsNullOrWhiteSpace(unit) && (masterUnits?.Any(i => i.Contains(unit?.Replace(" ", "")?.Replace(".", "")?.ToLower()?.Trim() ?? string.Empty)) ?? false))
                {
                    var normalizedUnit = unit?.Replace(" ", "")?.Replace(".", "")?.ToLower()?.Trim() ?? string.Empty;
                    var unitId = areaUnits?.FirstOrDefault(i => (i?.Unit?.Replace(" ", "")?.Replace(".", "")?.ToLower()?.Trim() ?? string.Empty).Contains(normalizedUnit))?.Id ?? Guid.Empty;
                    return (unitArea, unitId);
                }
                else if (areaUnitId != null)
                {
                    return (unitArea, areaUnitId ?? Guid.Empty);
                }
                else if (string.IsNullOrWhiteSpace(unit))
                {
                    var unitId = areaUnits?.FirstOrDefault(i => i.Unit == "Sq. Feet")?.Id ?? Guid.Empty;
                    return (unitArea, unitId);
                }
            }
            return (unitArea, Guid.Empty);
        }
        
        private static string GetStringValue(this Dictionary<ProspectDataColumn, string> mappedColumnsData, ProspectDataColumn column, DataRow row)
        {
            var data = !mappedColumnsData.ContainsKey(column)
                    || string.IsNullOrEmpty(mappedColumnsData[column])
                    ? string.Empty
                    : row[mappedColumnsData[column]]?.ToString();
            return data?.Trim() ?? string.Empty;
        }
        public static DateTime? GetPossessionDate(this Dictionary<ProspectDataColumn, string> mappedColumnsData, ProspectDataColumn column, DataRow row, string jsonData = null)
        {
            try
            {
                // Get the date string from the mapped columns or row
                var dateString = GetStringValue(mappedColumnsData, column, row);

                // If the date string is empty or null, handle default cases
                if (string.IsNullOrEmpty(dateString))
                {
                    return null;
                }

                // Try to parse the date string
                DateTime? date = null;
                CommonTimeZoneDto? commonTimeZoneDto = null;

                // Deserialize JSON data if provided
                if (!string.IsNullOrWhiteSpace(jsonData))
                {
                    commonTimeZoneDto = JsonConvert.DeserializeObject<CommonTimeZoneDto>(jsonData);
                }

                // Handle Excel date format (OLE Automation date)
                if (double.TryParse(dateString, out var excelDateValue))
                {
                    date = DateTime.FromOADate(excelDateValue);
                }
                // Handle regular date strings
                else
                {
                    // Try parsing with the default format
                    if (!DateTime.TryParse(dateString, out var parsedDate))
                    {
                        // Fallback to a specific format if the default parsing fails
                        date = DateTime.ParseExact(dateString, LeadMigrateHelper.formats, CultureInfo.InvariantCulture);
                    }
                    else
                    {
                        string formattedDate = parsedDate.ToString(CultureInfo.CurrentCulture);
                        date = DateTime.Parse(formattedDate, CultureInfo.CurrentCulture);
                    }
                }

                // Convert the date to the specified time zone if JSON data is provided
                if (commonTimeZoneDto != null)
                {
                    date = date?.ConvertDateTime(commonTimeZoneDto.TimeZoneId ?? string.Empty, commonTimeZoneDto.BaseUTcOffset);
                }

                return date;
            }
            catch (Exception ex)
            {
                // Return UTC time as a fallback in case of any errors
                return null;
            }
        }
        public static DateTime? GetDate(this Dictionary<ProspectDataColumn, string> mappedColumnsData, ProspectDataColumn column, DataRow row, string jsonData = null)
        {
            try
            {
                var dateString = GetStringValue(mappedColumnsData, column, row);
                if (string.IsNullOrEmpty(dateString))
                {
                    return null;
                }
                DateTime? date = null;
                if (double.TryParse(dateString, out var excelDateValue))
                {
                    date = DateTime.FromOADate(excelDateValue).Date;
                }
                else
                {
                    if (!DateTime.TryParseExact(dateString, LeadMigrateHelper.formats, CultureInfo.InvariantCulture, DateTimeStyles.None, out var parsedDate))
                    {
                        date = DateTime.ParseExact(dateString, LeadMigrateHelper.formats, CultureInfo.InvariantCulture).Date;
                    }
                    else
                    {
                        string formattedDate = parsedDate.ToString(CultureInfo.CurrentCulture);
                        date = DateTime.Parse(formattedDate, CultureInfo.CurrentCulture).Date;
                    }
                }
                if (date.HasValue && date.Value > DateTime.UtcNow.Date)
                {
                    return null;
                }

                return date;
            }
            catch
            {
                return null;
            }
        }
    }
}
