{"CacheSettings": {"UseDistributedCache": false, "PreferRedis": false, "RedisURL": "localhost:10420"}, "RedisStackSettings": {"ConnectionString": "redis-10420.crce182.ap-south-1-1.ec2.redns.redis-cloud.com:10420,User=default,Password=zslBjP0i4YtANuvHctE5trkxOT9YwbzI", "InstanceName": "database-ME9RZDSG", "DefaultDatabase": 0, "SSL": true, "AbortOnConnectFail": false, "ConnectTimeout": 100000, "ConnectRetry": 3, "KeepAlive": 60, "SyncTimeout": 100000}}