﻿using Finbuckle.MultiTenant;
using Lrb.Application.AutoDialer.Web.Mappings;
using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.IVR;
using Lrb.Application.Common.LeadRotation;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.Common.ServiceBus;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.LeadRotation.Web.Dtos;
using Lrb.Application.LeadRotation.Web.Specs;
using Lrb.Application.Team.Web;
using Lrb.Application.UserDetails.Web;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using Lrb.Infrastructure.Auth;
using Lrb.Infrastructure.Identity;
using Mapster;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Threading;

namespace Lrb.Infrastructure.LeadRotation
{
    public class LeadRotationService : ILeadRotationService
    {

        private readonly IJobService _hangfireService;
       // private readonly IRepositoryWithEvents<Lrb.Domain.Entities.LeadRotationConfiguration> _leadRotationConfigRepo;
        private readonly IRepositoryWithEvents<LeadsAssignRotationInfo> _leadRotationRepository;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Lead> _leadRepository;
        private readonly IRepositoryWithEvents<UserDetails> _userDetailsRepository;
        private readonly ICurrentUser _currentUser;
        private readonly IUserService _userService;
        private readonly ITenantInfo _tenantInfo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly Serilog.ILogger _logger;
        private readonly INotificationSenderService _notificationSenderService;
        private readonly IRepositoryWithEvents<Domain.Entities.LeadHistory> _leadHistoryRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingsRepo;
        private readonly INpgsqlRepository _npgsqlRepo;
        private readonly IRepositoryWithEvents<LeadRotationTracker> _leadRotationTrackerRepo;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _leadStatusRepo;
        private readonly IServiceBus _serviceBus;
        private readonly IRepositoryWithEvents<Team> _teamRepo;
        private readonly IRepositoryWithEvents<TeamConfiguration> _teamConfigurationRepo;
        private readonly IDapperRepository _dapperRepository;
        private readonly IRepositoryWithEvents<Domain.Entities.AutoDialerAudit> _autoDialerRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.IntegrationAccountInfo> _integrationAccRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.IVRCommonCallLog> _ivrCommonCallRepo;
        private readonly IIVRService _iVRService;
        public LeadRotationService(
           // IRepositoryWithEvents<LeadsAssignRotationInfo> leadRotationRepository,
            IRepositoryWithEvents<Lead> leadRepository,
            IRepositoryWithEvents<UserDetails> userDetailsRepository,
            ICurrentUser currentUser,
            IUserService userService,
            ILeadRepositoryAsync leadRepositoryAsync,
            Serilog.ILogger logger,
            ITenantInfo tenantInfo,
            IJobService hangfireService,
            IRepositoryWithEvents<Lrb.Domain.Entities.LeadRotationConfiguration> leadRotationConfigRepo,
            INotificationSenderService notificationSenderService,
            IRepositoryWithEvents<Domain.Entities.LeadHistory> leadHistoryRepo,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingsRepo,
            INpgsqlRepository npgsqlRepo,
            IRepositoryWithEvents<LeadRotationTracker> leadRotationTrackerRepo,
            IRepositoryWithEvents<CustomMasterLeadStatus> leadStatusRepo,
            IServiceBus serviceBus,
            IRepositoryWithEvents<Team> teamRepo,
            IRepositoryWithEvents<TeamConfiguration> teamConfigurationRepo,
            IDapperRepository dapperRepository,
            IRepositoryWithEvents<AutoDialerAudit> autoDialerRepo,
            IIVRService iVRService, IRepositoryWithEvents<Domain.Entities.IntegrationAccountInfo> integrationAccRepo,
            IRepositoryWithEvents<Domain.Entities.IVRCommonCallLog> ivrCommonCallRepo)
        {
           // _leadRotationRepository = leadRotationRepository;
            _leadRepository = leadRepository;
            _userDetailsRepository = userDetailsRepository;
            _currentUser = currentUser;
            _userService = userService;
            _leadRepositoryAsync = leadRepositoryAsync;
            _logger = logger;
            _hangfireService = hangfireService;
            _tenantInfo = tenantInfo;
          //  _leadRotationConfigRepo = leadRotationConfigRepo;
            _notificationSenderService = notificationSenderService;
            _leadHistoryRepo = leadHistoryRepo;
            _globalSettingsRepo = globalSettingsRepo;
            _npgsqlRepo = npgsqlRepo;
            _leadRotationTrackerRepo = leadRotationTrackerRepo;
            _leadStatusRepo = leadStatusRepo;
            _serviceBus = serviceBus;
            _teamRepo = teamRepo;
            _teamConfigurationRepo = teamConfigurationRepo;
            _dapperRepository = dapperRepository;
            _iVRService = iVRService;
            _integrationAccRepo = integrationAccRepo;
            _ivrCommonCallRepo = ivrCommonCallRepo;
            _autoDialerRepo = autoDialerRepo;
        }

        #region Lead Rotation
        public async Task<(bool, LeadsAssignRotationInfoDto)> RotateAssignLeadsAsync(LeadsAssignRotationInfoDto dto, Guid leadId)
        {
            try
            {
                Guid leadRotationId = Guid.Empty;
                var fetchLead = (await _leadRepository.FirstOrDefaultAsync(new GetLeadForLeadRotationByIdSpecs(leadId)));
                var rotationTracker = (await _leadRotationTrackerRepo.FirstOrDefaultAsync(new GetLeadRotationTrackerSpecs(fetchLead.Id)));
                if (fetchLead != null && !fetchLead.IsPicked)
                {
                    if (rotationTracker != null)
                    {
                        var statuses = await _leadStatusRepo.ListAsync(CancellationToken.None);
                        var dropedStatus = statuses.FirstOrDefault(i => i.Status is "dropped");
                        if (fetchLead?.CustomLeadStatus?.Id != dropedStatus?.Id && fetchLead?.CustomLeadStatus?.Id != dropedStatus?.BaseId)
                        {
                            var assignTo = fetchLead.AssignTo;
                            if (fetchLead.LastModifiedBy == Guid.Empty)
                            {
                                if (fetchLead.AssignTo == Guid.Empty && fetchLead?.AssignDate == default)
                                {
                                    dto.IsRotationCompleted = true;
                                    return (false, dto);
                                }
                                _logger.Information($"LeadRotationService() -> RotateAssignLeadsAsync() -> LeadDetails : {JsonConvert.SerializeObject(fetchLead, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}");
                                TeamConfiguration? leadROtationConfiguration = null;
                                if (dto.AccountId != null)
                                {
                                    leadROtationConfiguration = (await _teamConfigurationRepo.FirstOrDefaultAsync(new GetTeamConfigurationByAccountSpecs(dto.AccountId)));
                                    if (leadROtationConfiguration == null)
                                    {
                                        leadROtationConfiguration = (await _teamConfigurationRepo.FirstOrDefaultAsync(new GetTeamConfigurationBySourceSpecs(dto.Source ?? Domain.Enums.LeadSource.Any)));
                                    }
                                }
                                else
                                {
                                    leadROtationConfiguration = (await _teamConfigurationRepo.FirstOrDefaultAsync(new GetTeamConfigurationBySourceSpecs(dto.Source ?? Domain.Enums.LeadSource.Any)));
                                }

                             //   _logger.Information($"LeadRotationService() -> RotateAssignLeadsAsync() -> leadRotationDetails : {JsonConvert.SerializeObject(leadRotation, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}");

                                List<string>? groupUserIds = new();
                                if (leadROtationConfiguration == null) 
                                { 
                                      return (false, dto);  
                                }
                                var leadRotation = leadROtationConfiguration.Team;
                                if (leadRotation == null)
                                {
                                    return (false, dto);
                                }
                                if (leadRotation?.UserIds?.Any() ?? false)
                                {
                                    groupUserIds = (leadRotation.UserIds.Select(i => i.ToString())).ToList();
                                }
                                else
                                {
                                    dto.IsRotationCompleted = true;
                                    return (false, dto);
                                }
                                var userIds = dto.UserDetails ?? await _userService.GetListOfUsersByIdsAsync(groupUserIds, default);

                                var activeUsers = userIds.Where(i => i.IsActive).Select(i => i.Id).ToList();

                                var usersDetails = await _userDetailsRepository.ListAsync(new GetUserDetailsByIdsSpecs(activeUsers));

                                if (!usersDetails?.Any() ?? false)
                                {
                                    dto.IsRotationCompleted = true;
                                    return (false, dto);
                                }

                                _logger.Information($"LeadRotationService() -> RotateAssignLeadsAsync() -> usersDetails : {JsonConvert.SerializeObject(usersDetails, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}");

                                var assignedUserIds = usersDetails?.Where(i => i.IsAutomationEnabled).Select(i => i.UserId).ToList();
                                assignedUserIds = assignedUserIds?
                                    .OrderBy(id => leadRotation.UserIds.IndexOf(id))
                                    .ToList();
                                if (leadRotation == null || assignedUserIds == null || assignedUserIds.Count <= 0)
                                {
                                    return (false, dto);
                                }
                                //leadROtationConfiguration = leadRotation?.Configurations?.FirstOrDefault(i => i.IsForRetention == false);
                                if (leadROtationConfiguration == null)
                                {
                                    dto.IsRotationCompleted = true;
                                    return (false, dto);
                                }

                                rotationTracker.NoOfRotation = (assignedUserIds.Count * (leadROtationConfiguration?.NoOfRotation ?? 0));

                                rotationTracker.Status = Domain.Enums.UploadStatus.InProgress;
                                rotationTracker.ClassType = "Lead Rotation";

                                int? rotationTime = null;

                                if (leadROtationConfiguration?.RotationTime != null)
                                {
                                    rotationTime = (int)leadROtationConfiguration.RotationTime.Value.TotalMinutes;
                                }

                                if (rotationTracker.RotationCount < rotationTracker.NoOfRotation && (assignedUserIds?.Count > 1))
                                {
                                    var currentIndex = assignedUserIds.LastIndexOf(fetchLead?.AssignTo ?? Guid.Empty);
                                    leadROtationConfiguration.LastAssignedUser = assignedUserIds[(currentIndex + 1) % assignedUserIds.Count()];
                                    leadROtationConfiguration.NextUserToBeAssigned = assignedUserIds[(currentIndex + 2) % assignedUserIds.Count()];
                                    leadRotationId = leadROtationConfiguration.LastAssignedUser ?? assignedUserIds[0];
                                    if (leadRotationId == fetchLead.AssignTo)
                                    {
                                        leadRotationId = assignedUserIds[(currentIndex + 2) % assignedUserIds.Count()];
                                    }
                                    fetchLead.AssignedFrom = assignTo;
                                    dto.PreviousAssignedUser = fetchLead.AssignTo;
                                    fetchLead.AssignTo = leadRotationId;
                                    dto.AssignedUser = leadRotationId;
                                    _logger.Information($"LeadRotationService() -> RotateAssignLeadsAsync() -> leadAssignTo : {JsonConvert.SerializeObject(leadRotationId, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}");
                                    await _leadRepository.UpdateAsync(fetchLead);

                                    #region Lead Rotation Tracker

                                    rotationTracker.RotationCount++;
                                    if (rotationTracker.AssignedUsers != null)
                                    {
                                        rotationTracker.AssignedUsers.Add(DateTime.UtcNow, fetchLead.AssignTo);
                                    }
                                    else
                                    {
                                        rotationTracker.AssignedUsers = new Dictionary<DateTime, Guid>() { { DateTime.UtcNow, fetchLead.AssignTo } };
                                    }
                                    rotationTracker.Message += $"InProgress {fetchLead.Name}";
                                    await _leadRotationTrackerRepo.UpdateAsync(rotationTracker);

                                    #endregion

                                    #region Lead History
                                    try
                                    {
                                        await UpdateRotationHistoryAsync(fetchLead, _userService, leadRotationId, assignedUserIds);
                                    }
                                    catch
                                    {
                                        await UpdateRotationHistoryAsync(fetchLead, _userService, leadRotationId, assignedUserIds);
                                    }

                                    #endregion

                                    #region Push Notification

                                    try
                                    {
                                        Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), default);
                                        NotificationSettings? notificationSettings = JsonConvert.DeserializeObject<NotificationSettings>(globalSettings?.NotificationSettings ?? string.Empty);
                                        List<string> notificationResponses = new();
                                        string? tenantId = dto?.TenantInfoDto?.Id;

                                        List<Guid> adminIds = dto?.AdminIds ?? await _npgsqlRepo.GetAdminIdsAsync(tenantId ?? string.Empty);
                                        if (fetchLead.AssignTo == default || fetchLead.AssignTo == Guid.Empty)
                                        {
                                            _logger.Information($"ListingSitesIntegrationRequest -> tenantId : {tenantId} , adminIds : " + JsonConvert.SerializeObject(adminIds));
                                            if (adminIds.Any())
                                            {

                                                List<string> notificationSchduleResponse = new();
                                                notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadRotation, fetchLead, null, null, topics: new List<string> { fetchLead.CreatedBy.ToString(), fetchLead.LastModifiedBy.ToString() }, null, null, adminIds, rotationTime: rotationTime);
                                                notificationResponses.AddRange(notificationSchduleResponse);
                                            }
                                        }
                                        else if (fetchLead.AssignTo != Guid.Empty)
                                        {
                                            var user = dto?.UserDetails?.FirstOrDefault(i => i.Id == fetchLead.AssignTo) ?? await _userService.GetAsync(fetchLead.AssignTo.ToString(), default);
                                            if (user != null)
                                            {
                                                List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadRotation, fetchLead, fetchLead.AssignTo, user.FirstName + " " + user.LastName, topics: new List<string> { fetchLead.CreatedBy.ToString(), fetchLead.LastModifiedBy.ToString() }, rotationTime: rotationTime);
                                                notificationResponses.AddRange(notificationSchduleResponse);
                                            }
                                            List<Guid> userWithManagerIds = new();
                                            List<UserBasicInfoDto>? userDetails = null;
                                            if (notificationSettings?.IsManagerEnabled ?? false)
                                            {
                                                userDetails = (await _dapperRepository.GetUserDetailsAsync(tenantId ?? string.Empty, new List<Guid> { fetchLead.AssignTo })).ToList();
                                                List<Guid> managerIds = userDetails?
                                                    .FirstOrDefault(i => i.Id == fetchLead.AssignTo)
                                                    ?.ReportsTo?.Id is Guid managerId
                                                    ? new List<Guid> { managerId }
                                                    : await _npgsqlRepo.GetReportingManagerUserIdsAsync(new List<Guid> { fetchLead.AssignTo }); userWithManagerIds.AddRange(managerIds);
                                            }
                                            if (notificationSettings?.IsAdminEnabled ?? false)
                                            {
                                                userWithManagerIds.AddRange(adminIds);
                                            }
                                            if (notificationSettings?.IsGeneralManagerEnabled ?? false)
                                            {
                                                userDetails ??= (await _dapperRepository.GetUserDetailsAsync(tenantId ?? string.Empty, new List<Guid> { fetchLead.AssignTo })).ToList();
                                                List<Guid> generalManagerIds = userDetails?
                                                    .FirstOrDefault(i => i.Id == fetchLead.AssignTo)
                                                    ?.ReportsTo?.Id is Guid generalManagerId
                                                    ? new List<Guid> { generalManagerId }
                                                    : (await _dapperRepository.GetUserDetailsAsync(tenantId ?? string.Empty, new List<Guid> { fetchLead.AssignTo })).ToList()?.Select(i => i.Id)?.ToList();
                                                userWithManagerIds.AddRange(generalManagerIds);
                                            }
                                            if (user != null && userWithManagerIds.Any())
                                            {
                                                userWithManagerIds = userWithManagerIds.Distinct().ToList();
                                                userWithManagerIds.Remove(fetchLead.AssignTo);
                                                List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, fetchLead, null, null, topics: new List<string> { fetchLead.CreatedBy.ToString(), fetchLead.LastModifiedBy.ToString() }, userIds: userWithManagerIds);
                                                notificationResponses.AddRange(notificationSchduleResponse);
                                            }
                                        }
                                        _logger.Information($"ListingSitesIntegrationRequest -> NotificationSchedulingResponses JobIds : " + JsonConvert.SerializeObject(notificationResponses));
                                    }
                                    catch (Exception ex)
                                    {
                                        var error = new LrbError()
                                        {
                                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                            ErrorSource = ex?.Source,
                                            StackTrace = ex?.StackTrace,
                                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                            ErrorModule = "CreateLeadRequestHandler -> Handle() -> ScheduleNotificationsAsync()"
                                        };
                                        await _leadRepositoryAsync.AddErrorAsync(error);
                                    }

                                    #endregion

                                    // leadRotation.LeadId = fullLead.Id;
                                    leadROtationConfiguration.AssignedUserId = leadRotationId;
                                    //leadRotation.Configuration = leadROtationConfiguration;
                                    await _teamConfigurationRepo.UpdateAsync(leadROtationConfiguration);
                                    dto.IsRotationCompleted = false;
                                    return (true, dto);
                                }
                                else
                                {
                                    fetchLead.AssignedFrom = assignTo;
                                    dto.PreviousAssignedUser = fetchLead.AssignTo;
                                    fetchLead.AssignTo = leadRotation?.Manager ?? Guid.Empty;
                                    dto.AssignedUser = Guid.Empty;

                                    _logger.Information($"LeadRotationService() -> RotateAssignLeadsAsync() -> leadAssignTo : {JsonConvert.SerializeObject(leadRotationId, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}");
                                    await _leadRepository.UpdateAsync(fetchLead);

                                    #region Lead Rotation Tracker

                                    rotationTracker.Status = Domain.Enums.UploadStatus.Completed;
                                    rotationTracker.RotationCount = 0;
                                    rotationTracker.Message = "Completed";
                                    if (rotationTracker.AssignedUsers != null)
                                    {
                                        rotationTracker.AssignedUsers.Add(DateTime.UtcNow, fetchLead.AssignTo);
                                    }
                                    else
                                    {
                                        rotationTracker.AssignedUsers = new Dictionary<DateTime, Guid>() { { DateTime.UtcNow, fetchLead.AssignTo } };
                                    }
                                    await _leadRotationTrackerRepo.UpdateAsync(rotationTracker);

                                    #endregion

                                    #region Lead History

                                    try
                                    {
                                        await UpdateRotationHistoryAsync(fetchLead, _userService, leadRotationId, assignedUserIds);
                                    }
                                    catch
                                    {
                                        await UpdateRotationHistoryAsync(fetchLead, _userService, leadRotationId, assignedUserIds);
                                    }
                                    #endregion

                                    #region Push Notification

                                    try
                                    {
                                        Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), default);
                                        NotificationSettings? notificationSettings = JsonConvert.DeserializeObject<NotificationSettings>(globalSettings?.NotificationSettings ?? string.Empty);
                                        List<string> notificationResponses = new();
                                        string? tenantId = dto?.TenantInfoDto?.Id;

                                        List<Guid> adminIds = dto?.AdminIds ?? await _npgsqlRepo.GetAdminIdsAsync(tenantId ?? string.Empty);
                                        if (fetchLead.AssignTo == default || fetchLead.AssignTo == Guid.Empty)
                                        {
                                            _logger.Information($"ListingSitesIntegrationRequest -> tenantId : {tenantId} , adminIds : " + JsonConvert.SerializeObject(adminIds));
                                            if (adminIds.Any())
                                            {

                                                List<string> notificationSchduleResponse = new();
                                                notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadRotation, fetchLead, null, null, topics: new List<string> { fetchLead.CreatedBy.ToString(), fetchLead.LastModifiedBy.ToString() }, null, null, adminIds, rotationTime: rotationTime);
                                                notificationResponses.AddRange(notificationSchduleResponse);
                                            }
                                        }
                                        else if (fetchLead.AssignTo != Guid.Empty)
                                        {
                                            var user = dto?.UserDetails?.FirstOrDefault(i => i.Id == fetchLead.AssignTo) ?? await _userService.GetAsync(fetchLead.AssignTo.ToString(), default);
                                            if (user != null)
                                            {
                                                List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadRotation, fetchLead, fetchLead.AssignTo, user.FirstName + " " + user.LastName, topics: new List<string> { fetchLead.CreatedBy.ToString(), fetchLead.LastModifiedBy.ToString() }, rotationTime: rotationTime);
                                                notificationResponses.AddRange(notificationSchduleResponse);
                                            }
                                            List<Guid> userWithManagerIds = new();
                                            if (notificationSettings?.IsManagerEnabled ?? false)
                                            {
                                                List<Guid> managerIds = await _npgsqlRepo.GetReportingManagerUserIdsAsync(new List<Guid> { fetchLead.AssignTo });
                                                userWithManagerIds.AddRange(managerIds);
                                            }
                                            if (notificationSettings?.IsAdminEnabled ?? false)
                                            {
                                                userWithManagerIds.AddRange(adminIds);
                                            }
                                            if (user != null && userWithManagerIds.Any())
                                            {
                                                userWithManagerIds = userWithManagerIds.Distinct().ToList();
                                                userWithManagerIds.Remove(fetchLead.AssignTo);
                                                List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, fetchLead, null, null, topics: new List<string> { fetchLead.CreatedBy.ToString(), fetchLead.LastModifiedBy.ToString() }, userIds: userWithManagerIds);
                                                notificationResponses.AddRange(notificationSchduleResponse);
                                            }
                                        }
                                        _logger.Information($"ListingSitesIntegrationRequest -> NotificationSchedulingResponses JobIds : " + JsonConvert.SerializeObject(notificationResponses));
                                    }
                                    catch (Exception ex)
                                    {
                                        var error = new LrbError()
                                        {
                                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                            ErrorSource = ex?.Source,
                                            StackTrace = ex?.StackTrace,
                                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                            ErrorModule = "CreateLeadRequestHandler -> Handle() -> ScheduleNotificationsAsync()"
                                        };
                                        await _leadRepositoryAsync.AddErrorAsync(error);
                                    }

                                    #endregion

                                    // leadRotation.LeadId = fullLead.Id;
                                    leadROtationConfiguration.AssignedUserId = leadRotationId;
                                    leadRotation.Configuration = leadROtationConfiguration;

                                    await _teamConfigurationRepo.UpdateAsync(leadROtationConfiguration);
                                    dto.IsRotationCompleted = true;
                                    return (false, dto);
                                }
                            }
                            else
                            {
                                rotationTracker.Status = Domain.Enums.UploadStatus.NotEligible;
                                rotationTracker.Message = $"Lead is not pushed from integration: {fetchLead.LastModifiedBy}";
                                await _leadRotationTrackerRepo.UpdateAsync(rotationTracker);
                            }
                        }
                        else
                        {
                            rotationTracker.Status = Domain.Enums.UploadStatus.NotEligible;
                            rotationTracker.Message = $"Dropped Lead";
                            await _leadRotationTrackerRepo.UpdateAsync(rotationTracker);
                        }
                    }
                }
                else
                {
                    if (rotationTracker != null)
                    {
                        rotationTracker.Message = $"Lead is Picked";
                        rotationTracker.Status = Domain.Enums.UploadStatus.NotEligible;
                        await _leadRotationTrackerRepo.UpdateAsync(rotationTracker);
                    }
                }
                return (false, dto);
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "LeadRotationService -> RotateAssignLeadsAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
        }

        #endregion

        #region Lead Retention
        public async Task<bool> ScheduleTeamRetentionRotation(List<Domain.Entities.Lead> leads)
        {
            try
            {
                foreach (var lead in leads)
                {
                    var team = await _teamRepo.FirstOrDefaultAsync(new GetTeamByLeadStatusIdSpecs(lead?.CustomLeadStatus?.Id ?? Guid.Empty));
                    var teamConfig = team?.Configurations?.FirstOrDefault(i => i.IsForRetention == true);
                    var currentWeek = DateTime.UtcNow.DayOfWeek;
                    if (team != null && (team?.IsRotationEnabled ?? false) && team?.UserIds?.Count > 1 && (teamConfig?.DayOfWeeks?.Contains(currentWeek) ?? false))
                    {
                        var currentUserId = _currentUser.GetUserId();
                        var leadRotationInfoDto = new LeadsAssignRotationInfoDto()
                        {
                            LeadId = lead?.Id ?? Guid.Empty,
                            CurrentUserId = currentUserId,
                            TenantInfoDto = new()
                            {
                                Id = _tenantInfo.Id,
                                Name = _tenantInfo.Name,
                                ConnectionString = _tenantInfo.ConnectionString,
                                Identifier = _tenantInfo.Identifier,
                            }
                        };

                        //var timeInterval = new TimeSpan(5, 30, 0);

                        TimeSpan? shiftStart = null;
                        TimeSpan? shiftEnd = null;
                        int cronExpression = default;
                        DateTimeOffset? dateTimeOffset = null;

                        TimeSpan? currentTime = DateTime.UtcNow.TimeOfDay;

                        if (teamConfig?.RotationTime != null)
                        {
                            cronExpression = (int)teamConfig.RotationTime.Value.TotalMinutes;
                        }
                        if (teamConfig?.StartTime != null)
                        {
                            shiftStart = teamConfig?.StartTime;
                            leadRotationInfoDto.ShiftStartTime = shiftStart;
                        }
                        if (teamConfig?.EndTime != null)
                        {
                            shiftEnd = teamConfig?.EndTime;
                            leadRotationInfoDto.ShiftEndTime = shiftEnd;
                        }
                        InputPayload payload = new(leadRotationInfoDto?.TenantInfoDto?.Id ?? string.Empty, leadRotationInfoDto?.CurrentUserId ?? Guid.Empty, "team", lead?.Id ?? Guid.Empty, cronExpression, leadRotationInfoDto ?? new());
                        var messageBody = JsonConvert.SerializeObject(payload, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented });
                        if (currentTime != null && shiftStart != null && shiftEnd != null && cronExpression != default)
                        {
                            if (shiftStart <= shiftEnd)
                            {
                                if (currentTime >= shiftStart && currentTime <= shiftEnd)
                                {
                                    var scheduleLeadRotation = DateTime.UtcNow.AddMinutes(cronExpression);
                                    dateTimeOffset = new DateTimeOffset(scheduleLeadRotation, TimeSpan.Zero);

                                    #region Lead Rotation Tracker
                                    var rotationTracker = (await _leadRotationTrackerRepo.FirstOrDefaultAsync(new GetLeadRotationTrackerForRetentionSpecs(lead?.Id ?? Guid.Empty)));
                                    if (rotationTracker != null)
                                    {
                                        rotationTracker.AssignedUsers = new Dictionary<DateTime, Guid>() { { DateTime.UtcNow, lead?.AssignTo ?? Guid.Empty } };
                                        rotationTracker.Status = Domain.Enums.UploadStatus.Initiated;
                                        await _leadRotationTrackerRepo.UpdateAsync(rotationTracker);
                                    }
                                    else
                                    {
                                        var leadRotationTracker = new LeadRotationTracker()
                                        {
                                            LeadId = lead?.Id,
                                            JobId = string.Empty,
                                            ClassType = "Retention Team",
                                            AssignedUsers = new Dictionary<DateTime, Guid>() { { DateTime.UtcNow, lead?.AssignTo ?? Guid.Empty } },
                                            Status = Domain.Enums.UploadStatus.Initiated,
                                        };
                                        await _leadRotationTrackerRepo.AddAsync(leadRotationTracker);
                                    }
                                    #endregion
                                    await _serviceBus.RunLeadRetentionTriggerJobAsync(payload);

                                }
                                else
                                {
                                    var rotationTracker = (await _leadRotationTrackerRepo.FirstOrDefaultAsync(new GetLeadRotationTrackerForRetentionSpecs(lead?.Id ?? Guid.Empty)));
                                    if (rotationTracker != null)
                                    {
                                        rotationTracker.AssignedUsers = new Dictionary<DateTime, Guid>() { { DateTime.UtcNow, lead?.AssignTo ?? Guid.Empty } };
                                        rotationTracker.Status = Domain.Enums.UploadStatus.Initiated;
                                        await _leadRotationTrackerRepo.UpdateAsync(rotationTracker);
                                    }
                                    else
                                    {
                                        var leadRotationTracker = new LeadRotationTracker()
                                        {
                                            LeadId = lead?.Id,
                                            JobId = string.Empty,
                                            ClassType = "Retention Team",
                                            AssignedUsers = new Dictionary<DateTime, Guid>() { { DateTime.UtcNow, lead?.AssignTo ?? Guid.Empty } },
                                            Status = Domain.Enums.UploadStatus.Initiated,
                                        };
                                        await _leadRotationTrackerRepo.AddAsync(leadRotationTracker);
                                    }
                                    await _serviceBus.RunLeadRetentionTriggerJobAsync(payload);

                                }
                            }
                            else
                            {

                                if (currentTime >= shiftStart || currentTime <= shiftEnd)
                                {
                                    var scheduleLeadRotation = DateTime.UtcNow.AddMinutes(cronExpression);
                                    dateTimeOffset = new DateTimeOffset(scheduleLeadRotation, TimeSpan.Zero);

                                    #region Lead Rotation Tracker
                                    var leadRotationTracker = new LeadRotationTracker()
                                    {
                                        LeadId = lead?.Id,
                                        JobId = string.Empty,
                                        ClassType = "Retention Team",
                                        AssignedUsers = new Dictionary<DateTime, Guid>() { { DateTime.UtcNow, lead?.AssignTo ?? Guid.Empty } },
                                        Status = Domain.Enums.UploadStatus.Initiated,
                                    };
                                    await _leadRotationTrackerRepo.AddAsync(leadRotationTracker);
                                    #endregion

                                    await _serviceBus.RunLeadRetentionTriggerJobAsync(payload);
                                }
                                else
                                {
                                    var rotationTracker = (await _leadRotationTrackerRepo.FirstOrDefaultAsync(new GetLeadRotationTrackerForRetentionSpecs(lead?.Id ?? Guid.Empty)));
                                    if (rotationTracker != null)
                                    {
                                        rotationTracker.AssignedUsers = new Dictionary<DateTime, Guid>() { { DateTime.UtcNow, lead?.AssignTo ?? Guid.Empty } };
                                        rotationTracker.Status = Domain.Enums.UploadStatus.Initiated;
                                        await _leadRotationTrackerRepo.UpdateAsync(rotationTracker);
                                    }
                                    else
                                    {
                                        var leadRotationTracker = new LeadRotationTracker()
                                        {
                                            LeadId = lead?.Id,
                                            JobId = string.Empty,
                                            ClassType = "Retention Team",
                                            AssignedUsers = new Dictionary<DateTime, Guid>() { { DateTime.UtcNow, lead?.AssignTo ?? Guid.Empty } },
                                            Status = Domain.Enums.UploadStatus.Initiated,
                                        };
                                        await _leadRotationTrackerRepo.AddAsync(leadRotationTracker);
                                    }
                                    await _serviceBus.RunLeadRetentionTriggerJobAsync(payload);

                                }
                            }
                        }
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "LeadRotationService -> ScheduleTeamLeadRotation()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            return false;
        }


        public async Task<(bool, LeadsAssignRotationInfoDto)> RotateTeamRetentionLeadsAsync(LeadsAssignRotationInfoDto dto, Guid leadId)
        {
            try
            {
                Guid leadRotationId = Guid.Empty;
                var fetchLead = (await _leadRepository.ListAsync(new GetLeadForLeadRotationByIdSpecs(leadId))).FirstOrDefault();
                if (fetchLead != null)
                {
                    var rotationTracker = (await _leadRotationTrackerRepo.ListAsync(new GetLeadRotationTrackerForRetentionSpecs(fetchLead.Id))).FirstOrDefault();
                    if (rotationTracker != null)
                    {
                        if (!fetchLead.IsPicked)
                        {
                            var team = await _teamRepo.FirstOrDefaultAsync(new GetTeamByLeadStatusIdSpecs(fetchLead?.CustomLeadStatus?.Id ?? Guid.Empty));
                            if (team != null)
                            {
                                var assignTo = fetchLead?.AssignTo ?? Guid.Empty;

                                if (fetchLead?.AssignTo == Guid.Empty || team?.UserIds?.Count <= 1)
                                {
                                    dto.IsRotationCompleted = true;
                                    return (false, dto);
                                }
                                _logger.Information($"LeadRotationService() -> RotateTeamRetentionLeadsAsync() -> LeadDetails : {JsonConvert.SerializeObject(fetchLead, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}");


                                List<string>? groupUserIds = new();

                                if (team?.UserIds?.Any() ?? false)
                                {
                                    groupUserIds = (team.UserIds.Select(i => i.ToString())).ToList();
                                }
                                else
                                {
                                    dto.IsRotationCompleted = true;
                                    return (false, dto);
                                }
                                groupUserIds.Add(fetchLead?.AssignedFrom?.ToString() ?? Guid.Empty.ToString());
                                var userIds = await _userService.GetListOfUsersByIdsAsync(groupUserIds, default);

                                var activeUser = userIds.Where(i => i.IsActive).Select(i => i.Id).ToList();

                                var inActiveUsers = userIds.Where(i => !i.IsActive).Select(i => i.Id).ToList();

                                var usersDetails = await _userDetailsRepository.ListAsync(new GetUserDetailsByIdsSpecs(activeUser));

                                if (!usersDetails?.Any() ?? false)
                                {
                                    dto.IsRotationCompleted = true;
                                    return (false, dto);
                                }

                                _logger.Information($"LeadRotationService() -> RotateTeamRetentionLeadsAsync() -> usersDetails : {JsonConvert.SerializeObject(usersDetails, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}");


                                var unAssignmentIds = usersDetails?.Where(i => !i.IsAutomationEnabled).Select(i => i.UserId).ToList();
                                if (inActiveUsers?.Any() ?? false)
                                {
                                    unAssignmentIds?.AddRange(inActiveUsers);
                                }

                                var teamConfiguration = team?.Configurations?.FirstOrDefault(i => i.IsForRetention == true);

                                if (fetchLead?.SecondaryUserId != Guid.Empty && fetchLead?.SecondaryUserId != null)
                                {
                                    unAssignmentIds?.Add(fetchLead?.SecondaryUserId ?? Guid.Empty);
                                }

                                var assignedUserIds = team?.UserIds?.Except(unAssignmentIds).ToList();
                                assignedUserIds = usersDetails?.Where(i => i.IsAutomationEnabled).OrderBy(i => i.UserId).Select(i => i.UserId).ToList();

                                if (assignedUserIds == null || assignedUserIds.Count <= 0)
                                {
                                    dto.IsRotationCompleted = true;
                                    return (false, dto);
                                }

                                #region Update Rotation Tracker
                                //if (team?.Configuration?.NoOfRotation > 1)
                                //{
                                //    rotationTracker.NoOfRotation = (assignedUserIds.Count() * (team?.Configuration?.NoOfRotation ?? default)) - 1;
                                //}
                                //else
                                //{
                                //    rotationTracker.NoOfRotation = assignedUserIds.Count() * (team?.Configuration?.NoOfRotation ?? default);
                                //}
                                rotationTracker.NoOfRotation = ((assignedUserIds.Count()) * (teamConfiguration?.NoOfRotation ?? 0)) - 1;

                                rotationTracker.Status = Domain.Enums.UploadStatus.InProgress;
                                await _leadRotationTrackerRepo.UpdateAsync(rotationTracker);
                                #endregion

                                int? rotationTime = null;

                                if (teamConfiguration != null)
                                {
                                    if (rotationTracker.RotationCount < rotationTracker.NoOfRotation)
                                    {
                                        if (assignedUserIds.Count > 1)
                                        {
                                            var currentIndex = assignedUserIds.LastIndexOf(fetchLead?.AssignTo ?? Guid.Empty);
                                            teamConfiguration.LastAssignedUser = assignedUserIds[(currentIndex + 1) % assignedUserIds.Count()];
                                            teamConfiguration.NextUserToBeAssigned = assignedUserIds[(currentIndex + 2) % assignedUserIds.Count()];
                                            leadRotationId = teamConfiguration.LastAssignedUser ?? assignedUserIds[0];
                                            if (leadRotationId == fetchLead.AssignTo)
                                            {
                                                leadRotationId = assignedUserIds[(currentIndex + 2) % assignedUserIds.Count()];
                                            }
                                        }
                                        fetchLead.AssignedFrom = assignTo;
                                        fetchLead.AssignTo = leadRotationId;
                                        fetchLead.LastModifiedBy = leadRotationId;

                                        _logger.Information($"LeadRotationService() -> RotateTeamRetentionLeadsAsync() -> leadAssignTo : {JsonConvert.SerializeObject(leadRotationId, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}");
                                        await _leadRepository.UpdateAsync(fetchLead);

                                        #region Lead Rotation Tracker
                                        if (teamConfiguration.IsReshuffleEnabled == true)
                                        {
                                            rotationTracker.ReShuffleCount++;
                                        }
                                        rotationTracker.RotationCount++;
                                        rotationTracker.Message += $"In progress {fetchLead.Name}";
                                        if (rotationTracker.AssignedUsers != null)
                                        {
                                            rotationTracker.AssignedUsers.Add(DateTime.UtcNow, fetchLead.AssignTo);
                                        }
                                        else
                                        {
                                            rotationTracker.AssignedUsers = new Dictionary<DateTime, Guid>() { { DateTime.UtcNow, fetchLead.AssignTo } };
                                        }
                                        await _leadRotationTrackerRepo.UpdateAsync(rotationTracker);

                                        #endregion

                                        #region Lead History

                                        var userId = dto.CurrentUserId;
                                        try
                                        {
                                            if (teamConfiguration.IsReshuffleEnabled == true && team.LeadAssignmentType == LeadAssignmentType.WithoutHistory)
                                            {
                                                await UpdateReassignedLeadHistoryAsync(fetchLead, LeadAssignmentType.WithoutHistory, userIds, assignedUserIds, CancellationToken.None, dto.CurrentUserId, previousAssignedUser: fetchLead.AssignedFrom);
                                            }
                                            else
                                            {
                                                await UpdateRotationHistoryAsync(fetchLead, _userService, leadRotationId, assignedUserIds);
                                            }
                                        }
                                        catch
                                        {
                                            await UpdateRotationHistoryAsync(fetchLead, _userService, leadRotationId, assignedUserIds);
                                        }

                                        #endregion

                                        #region Push Notification

                                        try
                                        {
                                            Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), default);
                                            NotificationSettings? notificationSettings = JsonConvert.DeserializeObject<NotificationSettings>(globalSettings?.NotificationSettings ?? string.Empty);
                                            List<string> notificationResponses = new();
                                            string? tenantId = dto?.TenantInfoDto?.Id;

                                            List<Guid> adminIds = await _npgsqlRepo.GetAdminIdsAsync(tenantId ?? string.Empty);
                                            if (fetchLead.AssignTo == default || fetchLead.AssignTo == Guid.Empty)
                                            {
                                                _logger.Information($"ListingSitesIntegrationRequest -> tenantId : {tenantId} , adminIds : " + JsonConvert.SerializeObject(adminIds));
                                                if (adminIds.Any())
                                                {

                                                    List<string> notificationSchduleResponse = new();
                                                    notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadAssignment, fetchLead, null, null, topics: new List<string> { fetchLead.CreatedBy.ToString(), fetchLead.LastModifiedBy.ToString() }, null, null, adminIds, rotationTime: rotationTime);
                                                    notificationResponses.AddRange(notificationSchduleResponse);
                                                }
                                            }
                                            else if (fetchLead.AssignTo != Guid.Empty)
                                            {
                                                var user = await _userService.GetAsync(fetchLead.AssignTo.ToString(), default);
                                                if (user != null)
                                                {
                                                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadAssignment, fetchLead, fetchLead.AssignTo, user.FirstName + " " + user.LastName, topics: new List<string> { fetchLead.CreatedBy.ToString(), fetchLead.LastModifiedBy.ToString() }, rotationTime: rotationTime);
                                                    notificationResponses.AddRange(notificationSchduleResponse);
                                                }
                                                List<Guid> userWithManagerIds = new();
                                                if (notificationSettings?.IsManagerEnabled ?? false)
                                                {
                                                    List<Guid> managerIds = await _npgsqlRepo.GetReportingManagerUserIdsAsync(new List<Guid> { fetchLead.AssignTo });
                                                    userWithManagerIds.AddRange(managerIds);
                                                }
                                                if (notificationSettings?.IsAdminEnabled ?? false)
                                                {
                                                    userWithManagerIds.AddRange(adminIds);
                                                }
                                                if (user != null && userWithManagerIds.Any())
                                                {
                                                    userWithManagerIds = userWithManagerIds.Distinct().ToList();
                                                    userWithManagerIds.Remove(fetchLead.AssignTo);
                                                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, fetchLead, null, null, topics: new List<string> { fetchLead.CreatedBy.ToString(), fetchLead.LastModifiedBy.ToString() }, userIds: userWithManagerIds);
                                                    notificationResponses.AddRange(notificationSchduleResponse);
                                                }
                                            }
                                            _logger.Information($"ListingSitesIntegrationRequest -> NotificationSchedulingResponses JobIds : " + JsonConvert.SerializeObject(notificationResponses));
                                        }
                                        catch (Exception ex)
                                        {
                                            var error = new LrbError()
                                            {
                                                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                                ErrorSource = ex?.Source,
                                                StackTrace = ex?.StackTrace,
                                                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                                ErrorModule = "CreateLeadRequestHandler -> Handle() -> ScheduleNotificationsAsync()"
                                            };
                                            await _leadRepositoryAsync.AddErrorAsync(error);
                                        }

                                        #endregion

                                        #region Update Team Configuration
                                        teamConfiguration.AssignedUserId = leadRotationId;
                                        await _teamConfigurationRepo.UpdateAsync(teamConfiguration);

                                        #endregion
                                        dto.IsRotationCompleted = false;
                                        return (true, dto);
                                    }
                                    else
                                    {
                                        fetchLead.AssignedFrom = assignTo;
                                        fetchLead.AssignTo = team?.Manager ?? Guid.Empty;

                                        _logger.Information($"LeadRotationService() -> RotateTeamRetentionLeadsAsync() -> leadAssignTo : {JsonConvert.SerializeObject(leadRotationId, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}");
                                        await _leadRepository.UpdateAsync(fetchLead);

                                        #region Lead Rotation Tracker

                                        rotationTracker.Status = Domain.Enums.UploadStatus.Completed;
                                        rotationTracker.Message += $"Completed {fetchLead.Name}";

                                        rotationTracker.RotationCount = 0;
                                        if (rotationTracker.AssignedUsers != null)
                                        {
                                            rotationTracker.AssignedUsers.Add(DateTime.UtcNow, fetchLead.AssignTo);
                                        }
                                        else
                                        {
                                            rotationTracker.AssignedUsers = new Dictionary<DateTime, Guid>() { { DateTime.UtcNow, fetchLead.AssignTo } };
                                        }
                                        await _leadRotationTrackerRepo.UpdateAsync(rotationTracker);

                                        #endregion

                                        #region Lead History

                                        var userId = dto.CurrentUserId;
                                        try
                                        {
                                            await UpdateRotationHistoryAsync(fetchLead, _userService, leadRotationId, assignedUserIds);
                                        }
                                        catch
                                        {
                                            await UpdateRotationHistoryAsync(fetchLead, _userService, leadRotationId, assignedUserIds);
                                        }

                                        #endregion

                                        #region Push Notification

                                        try
                                        {
                                            Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), default);
                                            NotificationSettings? notificationSettings = JsonConvert.DeserializeObject<NotificationSettings>(globalSettings?.NotificationSettings ?? string.Empty);
                                            List<string> notificationResponses = new();
                                            string? tenantId = dto?.TenantInfoDto?.Id;

                                            List<Guid> adminIds = await _npgsqlRepo.GetAdminIdsAsync(tenantId ?? string.Empty);
                                            if (fetchLead.AssignTo == default || fetchLead.AssignTo == Guid.Empty)
                                            {
                                                _logger.Information($"ListingSitesIntegrationRequest -> tenantId : {tenantId} , adminIds : " + JsonConvert.SerializeObject(adminIds));
                                                if (adminIds.Any())
                                                {

                                                    List<string> notificationSchduleResponse = new();
                                                    notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadAssignment, fetchLead, null, null, topics: new List<string> { fetchLead.CreatedBy.ToString(), fetchLead.LastModifiedBy.ToString() }, null, null, adminIds, rotationTime: rotationTime);
                                                    notificationResponses.AddRange(notificationSchduleResponse);
                                                }
                                            }
                                            else if (fetchLead.AssignTo != Guid.Empty)
                                            {
                                                var user = await _userService.GetAsync(fetchLead.AssignTo.ToString(), default);
                                                if (user != null)
                                                {
                                                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadAssignment, fetchLead, fetchLead.AssignTo, user.FirstName + " " + user.LastName, topics: new List<string> { fetchLead.CreatedBy.ToString(), fetchLead.LastModifiedBy.ToString() }, rotationTime: rotationTime);
                                                    notificationResponses.AddRange(notificationSchduleResponse);
                                                }
                                                List<Guid> userWithManagerIds = new();
                                                if (notificationSettings?.IsManagerEnabled ?? false)
                                                {
                                                    List<Guid> managerIds = await _npgsqlRepo.GetReportingManagerUserIdsAsync(new List<Guid> { fetchLead.AssignTo });
                                                    userWithManagerIds.AddRange(managerIds);
                                                }
                                                if (notificationSettings?.IsAdminEnabled ?? false)
                                                {
                                                    userWithManagerIds.AddRange(adminIds);
                                                }
                                                if (user != null && userWithManagerIds.Any())
                                                {
                                                    userWithManagerIds = userWithManagerIds.Distinct().ToList();
                                                    userWithManagerIds.Remove(fetchLead.AssignTo);
                                                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, fetchLead, null, null, topics: new List<string> { fetchLead.CreatedBy.ToString(), fetchLead.LastModifiedBy.ToString() }, userIds: userWithManagerIds);
                                                    notificationResponses.AddRange(notificationSchduleResponse);
                                                }
                                            }
                                            _logger.Information($"ListingSitesIntegrationRequest -> NotificationSchedulingResponses JobIds : " + JsonConvert.SerializeObject(notificationResponses));
                                        }
                                        catch (Exception ex)
                                        {
                                            var error = new LrbError()
                                            {
                                                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                                ErrorSource = ex?.Source,
                                                StackTrace = ex?.StackTrace,
                                                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                                ErrorModule = "CreateLeadRequestHandler -> Handle() -> ScheduleNotificationsAsync()"
                                            };
                                            await _leadRepositoryAsync.AddErrorAsync(error);
                                        }

                                        #endregion

                                        #region Update Team Configuration
                                        teamConfiguration.AssignedUserId = leadRotationId;
                                        await _teamConfigurationRepo.UpdateAsync(teamConfiguration);

                                        #endregion
                                        dto.IsRotationCompleted = true;
                                        return (false, dto);
                                    }

                                }

                            }
                        }
                        else
                        {
                            rotationTracker.Status = Domain.Enums.UploadStatus.NotEligible;
                            rotationTracker.Message = $"Lead is picked";
                            await _leadRotationTrackerRepo.UpdateAsync(rotationTracker);
                        }

                    }
                }
                dto.IsRotationCompleted = true;
                return (false, dto);
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {

                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "LeadRotationService -> RotateAssignLeadsAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
        }
        #endregion
        public async Task<string> ScheduleTeamLeadRotation(Guid leadId, Guid? accountId = null)
        {
            string jobId = string.Empty;
            try
            {
                var fetchLead = (await _leadRepository.FirstOrDefaultAsync(new GetLeadForLeadRotationByIdSpecs(leadId)));
                if (fetchLead == null)
                {
                    return jobId;
                }

                if (!fetchLead.IsPicked)
                {
                    if (fetchLead.AssignTo != Guid.Empty || fetchLead.AssignDate != null)
                    {
                        var currentUserId = _currentUser.GetUserId();
                        if (currentUserId == Guid.Empty)
                        {
                            currentUserId = fetchLead?.LastModifiedBy ?? Guid.Empty;
                        }
                        var leadRotationInfoDto = new LeadsAssignRotationInfoDto()
                        {
                            LeadId = fetchLead?.Id ?? Guid.Empty,
                            CurrentUserId = currentUserId,
                            AssignedUser = fetchLead.AssignTo,
                            PreviousAssignedUser = fetchLead.AssignTo
                        };
                        leadRotationInfoDto.TenantInfoDto = new()
                        {
                            Id = _tenantInfo.Id,
                            Name = _tenantInfo.Name,
                            ConnectionString = _tenantInfo.ConnectionString,
                            Identifier = _tenantInfo.Identifier,
                        };
                        Domain.Entities.Team? teamGroup = null;
                        TeamConfiguration? leadRotationConfig = null;
                        if (accountId != null)
                        {
                            leadRotationInfoDto.AccountId = accountId;
                            leadRotationConfig = (await _teamConfigurationRepo.FirstOrDefaultAsync(new GetTeamConfigurationByAccountSpecs(accountId)));
                            if (leadRotationConfig == null)
                            {
                                leadRotationInfoDto.Source = fetchLead?.Enquiries?.FirstOrDefault()?.LeadSource ?? Domain.Enums.LeadSource.Any;
                                leadRotationConfig = (await _teamConfigurationRepo.FirstOrDefaultAsync(new GetTeamConfigurationBySourceSpecs(fetchLead?.Enquiries?.FirstOrDefault()?.LeadSource ?? Domain.Enums.LeadSource.Any)));
                            }
                        }
                        else
                        {
                            leadRotationInfoDto.Source = fetchLead?.Enquiries?.FirstOrDefault()?.LeadSource ?? Domain.Enums.LeadSource.Any;
                            leadRotationConfig = (await _teamConfigurationRepo.FirstOrDefaultAsync(new GetTeamConfigurationBySourceSpecs(fetchLead?.Enquiries?.FirstOrDefault()?.LeadSource ?? Domain.Enums.LeadSource.Any)));

                        }
                        if (leadRotationConfig == null)
                        {
                            return jobId;
                        }
                        teamGroup = leadRotationConfig.Team;
                        if (teamGroup == null)
                        {
                            return jobId;
                        }
                        if (teamGroup?.UserIds?.Any() ?? false)
                        {
                            var groupUserIds = (teamGroup.UserIds.Select(i => i.ToString())).ToList();
                            var userDetails = (await _userService.GetListOfUsersByIdsAsync(groupUserIds, default)).OrderBy(u => groupUserIds.IndexOf(u.Id.ToString())).ToList();
                            List<Guid>? adminIds = await _npgsqlRepo.GetAdminIdsAsync(_tenantInfo.Id ?? string.Empty);
                            leadRotationInfoDto.UserDetails = userDetails;
                            leadRotationInfoDto.AdminIds = adminIds;
                        }

                        //leadRotationConfig = teamGroup?.Configurations?.FirstOrDefault(i => i.IsForRetention == false);


                        //var timeInterval = new TimeSpan(5, 30, 0);

                        TimeSpan? currentTime = DateTime.UtcNow.TimeOfDay;
                        TimeSpan? shiftStart = null;
                        TimeSpan? shiftEnd = null;
                        DateTimeOffset? dateTimeOffset = null;
                        int cronExpression = default;

                        if (fetchLead.AssignDate.HasValue)
                        {
                            currentTime = fetchLead.AssignDate.Value.TimeOfDay;
                        }

                        if (leadRotationConfig?.RotationTime != null)
                        {
                            cronExpression = (int)leadRotationConfig.RotationTime.Value.TotalMinutes;
                        }
                        if (leadRotationConfig?.StartTime != null)
                        {
                            shiftStart = leadRotationConfig?.StartTime;
                            leadRotationInfoDto.ShiftStartTime = shiftStart;
                        }
                        if (leadRotationConfig?.EndTime != null)
                        {
                            shiftEnd = leadRotationConfig?.EndTime;
                            leadRotationInfoDto.ShiftEndTime = shiftEnd;
                        }

                        InputPayload payload = new(leadRotationInfoDto?.TenantInfoDto?.Id ?? string.Empty, leadRotationInfoDto?.CurrentUserId ?? Guid.Empty, "leadrotation", fetchLead.Id, cronExpression, leadRotationInfoDto ?? new(), leadRotationConfig?.BufferTime);
                        var messageBody = JsonConvert.SerializeObject(payload, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented });
                        if (currentTime != null && shiftStart != null && shiftEnd != null && cronExpression != default)
                        {
                            if (shiftStart <= shiftEnd)
                            {
                                if (currentTime >= shiftStart && currentTime <= shiftEnd)
                                {
                                    var scheduleLeadRotation = fetchLead.CreatedOn.AddMinutes(cronExpression);
                                    dateTimeOffset = new DateTimeOffset(scheduleLeadRotation, TimeSpan.Zero);

                                    #region Lead Rotation Tracker
                                    var leadRotationTracker = new LeadRotationTracker()
                                    {
                                        LeadId = fetchLead.Id,
                                        JobId = jobId,
                                        Status = Domain.Enums.UploadStatus.Initiated,
                                        ClassType = "Lead Rotation"
                                    };
                                    await _leadRotationTrackerRepo.AddAsync(leadRotationTracker);
                                    #endregion

                                    await _serviceBus.RunLeadRotationTriggerJobAsync(payload);
                                }
                                else
                                {
                                    #region Lead Rotation Tracker

                                    var leadRotationTracker = new LeadRotationTracker()
                                    {
                                        LeadId = fetchLead.Id,
                                        JobId = jobId,
                                        Status = Domain.Enums.UploadStatus.Initiated,
                                        ClassType = "Lead Rotation",
                                        Message = "Scheduled For Rotation At ShiftTime"

                                    };
                                    await _leadRotationTrackerRepo.AddAsync(leadRotationTracker);
                                    #endregion

                                    await _serviceBus.RunLeadRotationTriggerJobAsync(payload);
                                }

                            }
                            else
                            {
                                if (currentTime >= shiftStart || currentTime <= shiftEnd)
                                {
                                    var scheduleLeadRotation = fetchLead.CreatedOn.AddMinutes(cronExpression);
                                    dateTimeOffset = new DateTimeOffset(scheduleLeadRotation, TimeSpan.Zero);

                                    #region Lead Rotation Tracker
                                    var leadRotationTracker = new LeadRotationTracker()
                                    {
                                        LeadId = fetchLead.Id,
                                        JobId = jobId,
                                        Status = Domain.Enums.UploadStatus.Initiated,
                                        ClassType = "Lead Rotation"

                                    };
                                    await _leadRotationTrackerRepo.AddAsync(leadRotationTracker);
                                    #endregion

                                    await _serviceBus.RunLeadRotationTriggerJobAsync(payload);
                                }
                                else
                                {
                                    #region Lead Rotation Tracker

                                    var leadRotationTracker = new LeadRotationTracker()
                                    {
                                        LeadId = fetchLead.Id,
                                        JobId = jobId,
                                        Status = Domain.Enums.UploadStatus.Initiated,
                                        ClassType = "Lead Rotation",
                                        Message = "Scheduled For Rotation At ShiftTime"

                                    };
                                    await _leadRotationTrackerRepo.AddAsync(leadRotationTracker);
                                    #endregion

                                    await _serviceBus.RunLeadRotationTriggerJobAsync(payload);
                                }
                            }

                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CreateGroupRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            return jobId;
        }
        public async Task UpdateRotationHistoryAsync(Domain.Entities.Lead fetchLead, IUserService userService, Guid leadRotationId, List<Guid> assignedUserIds)
        {
            try
            {
                var fullLead = (await _leadRepository.FirstOrDefaultAsync(new LeadByIdSpec(fetchLead.Id), default));
                var viewLeadDto = fullLead?.Adapt<ViewLeadDto>();
                viewLeadDto.LastModifiedOn = DateTime.UtcNow;
                viewLeadDto.LastModifiedBy = Guid.Empty;
                await viewLeadDto.SetUsersInViewLeadDtoAsync(_userService, default);
                var leadHistory = LeadHistoryHelper.LeadHistoryMapper(viewLeadDto);

                var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(fetchLead.Id, leadRotationId));
                if (existingLeadHistory != null)
                {
                    await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory), default);
                }
                else
                {
                    existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(fetchLead.Id, fetchLead.AssignedFrom ?? Guid.Empty));
                    if (existingLeadHistory != null)
                    {
                        await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory), default);
                    }
                    else
                    {
                        try
                        {
                            int index = 0;
                            while (index < assignedUserIds.Count() && existingLeadHistory == null)
                            {
                                existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(fetchLead.Id, assignedUserIds[index]));
                                index++;
                            }
                            if (existingLeadHistory != null)
                            {
                                await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory), default);
                            }
                            else
                            {
                                await _leadHistoryRepo.AddAsync(leadHistory, default);
                            }

                        }
                        catch (Exception ex)
                        {
                            await _leadHistoryRepo.AddAsync(leadHistory, default);
                        }
                    }
                }
            }
            catch
            {
                var fullLead = (await _leadRepository.FirstOrDefaultAsync(new LeadByIdSpec(fetchLead.Id), default));
                var viewLeadDto = fullLead?.Adapt<ViewLeadDto>();
                viewLeadDto.LastModifiedOn = DateTime.UtcNow;
                await viewLeadDto.SetUsersInViewLeadDtoAsync(_userService, default);
                var leadHistory = LeadHistoryHelper.LeadHistoryMapper(viewLeadDto);

                var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(fetchLead.Id, leadRotationId));
                if (existingLeadHistory != null)
                {
                    await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory), default);
                }
                else
                {
                    existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(fetchLead.Id, fetchLead.AssignedFrom ?? Guid.Empty));
                    if (existingLeadHistory != null)
                    {
                        await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory), default);
                    }
                    else
                    {
                        try
                        {
                            int index = 0;
                            while (index < assignedUserIds.Count() && existingLeadHistory == null)
                            {
                                existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(fetchLead.Id, assignedUserIds[index]));
                                index++;
                            }
                            if (existingLeadHistory != null)
                            {
                                await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory), default);
                            }
                            else
                            {
                                await _leadHistoryRepo.AddAsync(leadHistory, default);
                            }

                        }
                        catch (Exception ex)
                        {
                            await _leadHistoryRepo.AddAsync(leadHistory, default);
                        }
                    }
                }
            }
        }
        protected async Task UpdateReassignedLeadHistoryAsync(Domain.Entities.Lead lead, LeadAssignmentType assignmentType, List<Application.Identity.Users.UserDetailsDto> users, List<Guid> assignedUserIds, CancellationToken cancellationToken = default, Guid? currentUserId = null, Guid? previousAssignedUser = null)
        {
            var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignTo)) ?? await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, previousAssignedUser ?? currentUserId ?? Guid.Empty));
            try
            {
                if (currentUserId != null && currentUserId != default && users != null && !(users.Any(i => i.Id == currentUserId)))
                {
                    var currentUser = await _userService.GetAsync(currentUserId?.ToString() ?? string.Empty, cancellationToken);
                    if (currentUser != null && users.Any(i => i.Id != currentUser.Id))
                    {
                        users.Add(currentUser);
                    }
                }
            }
            catch (Exception ex) { }
            var leadDto = lead.Adapt<ViewLeadDto>();
            if (leadDto != null)
            {
                leadDto.AssignmentType = assignmentType;
            }
            leadDto = await leadDto.SetUsersInViewLeadDtoAsync( _userService, cancellationToken);
            var newLeadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
            if (existingLeadHistory != null)
            {
                try
                {

                    await AddNewHistoryForLeadReassignmentAsync(existingLeadHistory, newLeadHistory, users, leadDto, false, cancellationToken, currentUserId: currentUserId);
                }
                catch (Exception ex)
                {
                }
            }
            else
            {
                existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignedFrom ?? Guid.Empty));
                if (existingLeadHistory != null)
                {
                    try
                    {
                        await AddNewHistoryForLeadReassignmentAsync(existingLeadHistory, newLeadHistory, users, leadDto, false, cancellationToken, currentUserId: currentUserId);

                    }
                    catch (Exception ex)
                    {

                    }
                }
                else if (existingLeadHistory == null)
                {
                    existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new GetParentLeadHistorySpec(lead.Id));
                    if (existingLeadHistory != null)
                    {
                        existingLeadHistory.UserId = currentUserId ?? _currentUser.GetUserId();

                        await AddNewHistoryForLeadReassignmentAsync(existingLeadHistory, newLeadHistory, users, leadDto, false, cancellationToken, currentUserId: currentUserId);

                    }
                    else
                    {

                        try
                        {
                            await UpdateRotationHistoryAsync(lead, _userService, lead.AssignTo, assignedUserIds);
                        }
                        catch (Exception ex)
                        {
                        }
                    }
                }
            }
        }
        protected async Task AddNewHistoryForLeadReassignmentAsync(LeadHistory existingLeadHistory, LeadHistory newHistory, List<Application.Identity.Users.UserDetailsDto> users, ViewLeadDto fullLeadDto, bool withNewStatus, CancellationToken cancellationToken = default, Guid? currentUserId = null)
        {
            try
            {
                var currentUser = users?.FirstOrDefault(i => i.Id == (currentUserId ?? _currentUser.GetUserId()));
                if (currentUser == null)
                {
                    try
                    {
                        currentUser = (await _userService.GetListOfUsersByIdsAsync(new() { (currentUserId ?? _currentUser.GetUserId()).ToString() }, cancellationToken)).FirstOrDefault();
                    }
                    catch (Exception ex)
                    {

                    }
                }
                var assignedFromUser = users?.FirstOrDefault(i => i.Id == (fullLeadDto?.AssignedFrom ?? Guid.Empty));
                var assignToUser = users?.FirstOrDefault(i => i.Id == (fullLeadDto?.AssignTo ?? Guid.Empty));
                existingLeadHistory = await UpdateHistoryForLeadReassignmentAsync(existingLeadHistory, assignedFromUser, assignToUser, currentUser, withNewStatus, fullLeadDto);
                if (existingLeadHistory != null)
                {
                    await _leadHistoryRepo.UpdateAsync(existingLeadHistory, cancellationToken);
                }
                var leadHistory = existingLeadHistory.MapV1LeadHistory(currentUser, assignedFromUser, assignToUser, withNewStatus);
                leadHistory = LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, newHistory, leadHistory, withNewStatus);
                if (leadHistory != null && leadHistory?.Notes != null &&
                            leadHistory.CurrentVersion >= 1 &&
                            (!string.IsNullOrEmpty(leadHistory.Notes.FirstOrDefault().Value)) && (!string.Equals(leadHistory?.LastModifiedBy?.First().Value?.Trim(), "Integration", StringComparison.OrdinalIgnoreCase)))
                {

                    leadHistory.Notes[leadHistory.Notes.FirstOrDefault().Key] = "";

                }
                await _leadHistoryRepo.AddAsync(leadHistory, cancellationToken);

            }
            catch (Exception ex)
            {

            }
        }
        private async Task<LeadHistory?> UpdateHistoryForLeadReassignmentAsync(LeadHistory leadHistory, Application.Identity.Users.UserDetailsDto? assignedFromUser, Application.Identity.Users.UserDetailsDto? assignToUser, Application.Identity.Users.UserDetailsDto? currentUser, bool? isWithNewStatus = false, ViewLeadDto? leadDto = null)
        {
            try
            {
                if (leadHistory != null)
                {
                    var version = leadHistory.CurrentVersion + 1;
                    leadHistory.LastModifiedBy?.Add(version, $"{currentUser?.FirstName} {currentUser?.LastName}" ?? string.Empty);
                    if (leadHistory?.AssignedTo?.LastOrDefault().Value != assignToUser?.Id)
                    {
                        leadHistory.AssignedTo?.Add(version, assignToUser?.Id ?? default);
                    }
                    if (!(string.IsNullOrEmpty(leadHistory?.AssignedToUser?.LastOrDefault().Value)) && leadHistory?.AssignedToUser?.LastOrDefault().Value != $"{assignToUser?.FirstName} {assignToUser?.LastName}")
                    {
                        leadHistory.AssignedToUser?.Add(version, $"{assignToUser?.FirstName} {assignToUser?.LastName}" ?? string.Empty);
                    }
                    if (!(string.IsNullOrEmpty(leadHistory?.AssignedFromUser?.LastOrDefault().Value)) && leadHistory?.AssignedFromUser?.LastOrDefault().Value != $"{assignedFromUser?.FirstName} {assignedFromUser?.LastName}")
                    {
                        leadHistory.AssignedFromUser?.Add(version, $"{assignedFromUser?.FirstName} {assignedFromUser?.LastName}" ?? string.Empty);

                    }
                    if (isWithNewStatus == true && leadDto != null && leadDto.Status != null && (!string.IsNullOrEmpty(leadDto.Status.DisplayName)) && (!(string.IsNullOrEmpty(leadHistory?.BaseLeadStatus?.LastOrDefault().Value)) && leadHistory?.BaseLeadStatus?.LastOrDefault().Value != leadDto.Status?.DisplayName))
                    {
                        leadHistory.BaseLeadStatus?.Add(version, leadDto.Status?.DisplayName ?? string.Empty);
                    }
                    leadHistory.ModifiedDate?.Add(version, DateTime.UtcNow);
                    leadHistory.CurrentVersion = version;
                }
                return leadHistory;
            }
            catch (Exception ex)
            {
                return new();
            }

        }
        public async Task InitiateAutoDailetCalllingAsync(Guid userId)
        {
            await AutoDailerHelper.InitiateAutoCall(userId, _autoDialerRepo, _integrationAccRepo, _ivrCommonCallRepo, _iVRService);
        }

    }

    public record InputPayload(string TenantId, Guid CurrentUserId, string Type, Guid leadId, int interval, object entity, TimeSpan? bufferTime = null);
}
