﻿using Amazon;
using Amazon.Pinpoint;
using Amazon.Pinpoint.Model;
using Lrb.Application.Common.PushNotification;
using Lrb.Infrastructure.BlobStorage;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace Lrb.Infrastructure.PushNotification
{
    public class PinpointService : IPinpointService
    {
        private AmazonPinpointClient _amazonPinpointClient;
        private readonly PinpointSetting _settings;
        private readonly Serilog.ILogger _logger;

        public PinpointService(Serilog.ILogger logger, IOptions<PinpointSetting>? options = null)
        {
            _logger = logger;
            _settings = options?.Value ?? null;
            _logger.Information("awssettings:" + JsonConvert.SerializeObject(_settings));
            if (_settings == null || _settings.GetType().GetProperties().Any(p => p.GetValue(_settings) == null))
            {
                _settings = ConfigurationHelper.GetAwsSettingsConfig();
            }
            _amazonPinpointClient = new AmazonPinpointClient(_settings.AWSAccessToken, _settings.AWSSecret, RegionEndpoint.GetBySystemName(_settings.Region));
        }

        public async Task<UpdateEndpointResponse> UpdateEndpointAsync(UpdateEndpointRequest request)
        {
            var result = await _amazonPinpointClient.UpdateEndpointAsync(request);
            return result;
        }

        public async Task<UpdateEndpointsBatchResponse> UpdateEndpointsBatchAsync(UpdateEndpointsBatchRequest request)
        {
            var result = await _amazonPinpointClient.UpdateEndpointsBatchAsync(request);
            return result;
        }

        public async Task<DeleteEndpointResponse> DeleteEndpointAsync(DeleteEndpointRequest request)
        {
            var result = await _amazonPinpointClient.DeleteEndpointAsync(request);
            return result;
        }

        public async Task<CreateCampaignResponse> SendNotificationAsync(CreateCampaignRequest campaignRequest)
        {
            return await _amazonPinpointClient.CreateCampaignAsync(campaignRequest);
        }
        public async Task<UpdateCampaignResponse> SendNotificationAsync(UpdateCampaignRequest campaignRequest)
        {
            return await _amazonPinpointClient.UpdateCampaignAsync(campaignRequest);
        }

        public async Task<CreateSegmentResponse> CreateSegmentAsync(CreateSegmentRequest segmentRequest)
        {
            return await _amazonPinpointClient.CreateSegmentAsync(segmentRequest);
        }
    }

    public static class ConfigurationHelper
    {
        public static PinpointSetting GetAwsSettingsConfig()
        {
            string? env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
            return env switch
            {
                "dev" => new PinpointSetting
                {
                    Region = "ap-south-1",
                    AWSAccessToken = "********************",
                    AWSSecret = "cFkS7v131SaaMLAmaFKBLXAUfU/+TpGOmF/PTAs8",
                    PinpointApplicationId = "393a340a6a25439e84b3463c5273ddc3"
                },
                "qa" => new PinpointSetting
                {
                    Region = "ap-south-1",
                    AWSAccessToken = "********************",
                    AWSSecret = "cFkS7v131SaaMLAmaFKBLXAUfU/+TpGOmF/PTAs8",
                    PinpointApplicationId = "393a340a6a25439e84b3463c5273ddc3"
                },
                "prd" => new PinpointSetting
                {
                    Region = "ap-south-1",
                    AWSAccessToken = "********************",
                    AWSSecret = "cFkS7v131SaaMLAmaFKBLXAUfU/+TpGOmF/PTAs8",
                    PinpointApplicationId = "9bdc67aedb1d4d16bb4ba65323c04a17"
                },
                _ => new PinpointSetting
                {
                    Region = "ap-south-1",
                    AWSAccessToken = "********************",
                    AWSSecret = "cFkS7v131SaaMLAmaFKBLXAUfU/+TpGOmF/PTAs8",
                    PinpointApplicationId = "9bdc67aedb1d4d16bb4ba65323c04a17"
                },
            };
        }

    }

}
