﻿using Lrb.Application.CustomStatus.Web;
using Lrb.Domain.Entities.MasterData;
using Lrb.Application.Team.Web.Dtos;

namespace Lrb.Application.Team.Web.Requests
{
    public class CreateRetentionTeamRequest : CreateRetentionTeamsDto, IRequest<Response<Guid>>
    {

    }

    public class CreateRetentionTeamRequestHandler : IRequestHandler<CreateRetentionTeamRequest, Response<Guid>>
    {

        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Team> _teamRepo;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _custLeadStatusRepo;
        private readonly IRepositoryWithEvents<TeamConfiguration> _teamConfigRepo;
        public CreateRetentionTeamRequestHandler(
            IRepositoryWithEvents<CustomMasterLeadStatus> custLeadStatusRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.Team> teamRepo,
            IRepositoryWithEvents<TeamConfiguration> teamConfigRepo)
        {
            _teamRepo = teamRepo;
            _custLeadStatusRepo = custLeadStatusRepo;
            _teamConfigRepo = teamConfigRepo;
        }
        public async Task<Response<Guid>> Handle(CreateRetentionTeamRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var team = await _teamRepo.FirstOrDefaultAsync(new TeamByIdSpec(request.Id ?? Guid.Empty));
                if (team == null)
                {
                    throw new InvalidOperationException("Team not found by this Id");
                }
                List<CustomMasterLeadStatus> customMasterLeadStatuses = new();
                List<CustomMasterLeadStatus> masterLeadStatuses = new();
                if (request.StatusesIds?.Any() ?? false)
                {
                    var allStatusIds = request.StatusesIds;
                    if (request.Configuration?.StatusesIds?.Any() ?? false)
                    {
                        allStatusIds.AddRange(request.Configuration.StatusesIds);
                    }
                    customMasterLeadStatuses = await _custLeadStatusRepo.ListAsync(new GetAllStatusByIdSpec(allStatusIds ?? new List<Guid>()), cancellationToken);
                    if (customMasterLeadStatuses?.Any() ?? false)
                    {
                        // customMasterLeadStatuses.AddRange(statuses);
                        masterLeadStatuses = customMasterLeadStatuses.Where(i => request.StatusesIds.Contains(i.Id)).ToList();
                    }
                }
                //if (request.SubStatusesIds?.Any() ?? false)
                //{
                //    var subStatuses = await _custLeadStatusRepo.ListAsync(new GetAllStatusByIdSpec(request.SubStatusesIds ?? new List<Guid>()), cancellationToken);
                //    if (subStatuses?.Any() ?? false)
                //    {
                //        customMasterLeadStatuses.AddRange(subStatuses);
                //    }
                //}
                team.Statuses = masterLeadStatuses;
                team.IsRotationEnabled = request.IsRotationEnabled;
                await _teamRepo.UpdateAsync(team);
                if (request.Configuration != null)
                {
                    var teamConfig = request.Configuration.Adapt<TeamConfiguration>();
                    teamConfig.TeamId = team.Id;
                    team.LeadAssignmentType = request.LeadAssignmentType;
                    if (request.Configuration?.StatusesIds?.Any() ?? false)
                    {
                        var postRetentionStatuses = customMasterLeadStatuses?.Where(i => request.Configuration.StatusesIds.Contains(i.Id))?.ToList();
                        teamConfig.Statuses = postRetentionStatuses;
                    }
                    teamConfig.IsForRetention = true;
                    teamConfig = await _teamConfigRepo.AddAsync(teamConfig);

                    return new(teamConfig.Id);
                }
                return new(Guid.Empty);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
    }
}
