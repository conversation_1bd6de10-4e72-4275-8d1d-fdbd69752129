﻿using Lrb.Application.LeadRotation.Web.Dtos;
using Lrb.Application.Project.Web.Dtos;
using Lrb.Application.Property.Web.Requests;
using Lrb.Application.Team.Web.Dtos;
using Lrb.Domain.Entities.Automation;

namespace Lrb.Application.Automation.Dtos
{
    public class UpdateUserAssignmentDto : BaseUserAssignmentDto
    {
        public Guid ModuleId { get; set; } 
        public BaseTeamLeadRotationInfoDto? TeamLeadRotationInfo { get; set; }
    }
    public class ViewUserAssignmentDto : BaseUserAssignmentDto
    {
        public Guid Id { get; set; }
        public AssignmentModule? Module { get; set; }
        public Guid CreatedBy { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid LastModifiedBy { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public DateTime? DeletedOn { get; set; }
        public Guid? DeletedBy { get; set; }

    }
    public class BaseUserAssignmentDto : IDto
    {
        public Guid EntityId { get; set; }
        public List<Guid>? UserIds { get; set; } = default!;
        public List<Guid>? SecondaryUserIds { get; set; }
        public List<Guid>? DuplicateUserIds { get; set; }
        public bool? IsDuplicateAssignmentEnabled { get; set; }
        public bool? IsDualAssignmentEnabled { get; set; }
        public bool ShouldCreateMultipleDuplicates { get; set; }
        public List<UserAssignmentConfigDto>? UserAssignmentConfigurations { get; set; }
        public bool? ShouldConfigureLeadRotation { get; set; }
        public AssignmentCategoryType? CategoryType { get; set; }
        public UserAssignmentType? UserAssignmentType { get; set; }
        public ViewTeamLeadRotationInfoDto? Team { get; set; }
    }
    public class MultiEntityUserAssignmentDto : IDto
    {
        public List<Guid> EntityIds { get; set; } = default!;
        public Guid ModuleId { get; set; }
        public List<Guid>? UserIds { get; set; } = default!;
        public List<Guid>? SecondaryUserIds { get; set; } = default!;
        public List<Guid>? DuplicateUserIds { get; set; } = default!;
        public bool IsDuplicateAssignmentEnabled { get; set; }
        public bool IsDualAssignmentEnabled { get; set; }
        public bool ShouldCreateMultipleDuplicates { get; set; }
        public List<UserAssignmentConfigDto>? UserAssignmentConfigurations { get; set; }
        public AssignmentCategoryType? CategoryType { get; set; }
        public UserAssignmentType? UserAssignmentType { get; set; }
        public bool? ShouldConfigureLeadRotation { get; set; }
        public BaseTeamLeadRotationInfoDto? TeamLeadRotationInfo { get; set; }
    }
    public class AggregatedUserAssignmentsUserBasedDto : IDto
    {
        public Guid UserId { get; set; }
        public string UserName { get; set; } = default!;
        public string FirstName { get; set; } = default!;
        public string LastName { get; set; } = default!;
        public Dictionary<string, List<AssignedEntityDto>> Entities { get; set; }
        public AggregatedUserAssignmentsUserBasedDto()
        {
            Entities = new();
        }
    }
    public class AssignedEntityDto : BaseAssignedEntityDto, IDto
    {
        public Dictionary<string, List<AssignedEntityDto>> SubEntities { get; set; }
        public AssignedEntityDto()
        {
            SubEntities = new();
        }
    }
    public class BaseAssignedEntityDto : IDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = default!;
        public LeadSource? Source { get; set; }
    }
    public class AssignedFacebookIntegrationAccDto : BaseAssignedEntityDto
    {
        public Guid FacebookAccountId { get; set; }
    }
    public class Projectname
    {
        public string Name { get; set; }
    }
}
