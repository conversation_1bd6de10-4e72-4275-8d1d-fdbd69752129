﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.Identity.Users;
using Lrb.Application.Utils;
using Lrb.Domain.Constants;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;
using System.Collections.Concurrent;
using System.Data;
using System.Text.RegularExpressions;

namespace Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation
{
    public class CreateBulkLeadRequestUsingEPPlus : IRequest<Response<BulkLeadUploadTracker>>
    {
        public string? S3BucketKey { get; set; }
        public Dictionary<DataColumns, string>? MappedColumnsData { get; set; }
        public List<string>? UserIds { get; set; }
    }
    public class CreateBulkLeadRequestUsingEPPlusHandler : IRequestHandler<CreateBulkLeadRequestUsingEPPlus, Response<BulkLeadUploadTracker>>
    {
        private readonly IBlobStorageService _blobStorageService;
        private readonly INotificationSenderService _notificationSenderService;
        private readonly IJobService _hangfireService;
        private readonly IRepositoryWithEvents<Domain.Entities.Property> _propertyRepo;
        private readonly IRepositoryWithEvents<MasterPropertyType> _propertyTypeRepo;
        //private readonly IRepositoryWithEvents<MasterLeadStatus> _leadStatusRepo;
        private readonly IRepositoryWithEvents<Address> _addressRepo;
        private readonly IReadRepository<MasterAreaUnit> _masterAreaUnitRepo;
        private readonly ILeadRepository _leadRepository;
        private readonly ILeadHistoryRepository _leadHistoryRepository;
        private readonly IUserService _userService;
        private readonly ICurrentUser _currentUser;
        private readonly INpgsqlRepository _npgsqlRepo;
        private readonly ITenantIndependentRepository _tenantIndependentRepo;
        private readonly IBulkLeadUploadTrackerRepository _bulkLeadUploadTrackerRepository;
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        private readonly IRepositoryWithEvents<LeadHistory> _leadHistoryRepo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;

        public CreateBulkLeadRequestUsingEPPlusHandler(
            IBlobStorageService blobStorageService,
            IJobService hangfireService,
            INotificationSenderService notificationSenderService,
            IRepositoryWithEvents<Domain.Entities.Property> propertyRepo,
            IRepositoryWithEvents<MasterPropertyType> propertyTypeRepo,
            //IRepositoryWithEvents<MasterLeadStatus> leadStatusRepo,
            IRepositoryWithEvents<Address> addressRepo,
            IReadRepository<MasterAreaUnit> masterAreaUnitRepo,
            ILeadRepository leadRepository,
            ILeadHistoryRepository leadHistoryRepository,
            INpgsqlRepository npgsqlRepo,
            IUserService userService,
            ICurrentUser currentUser,
            ITenantIndependentRepository tenantIndependentRepo,
            IBulkLeadUploadTrackerRepository bulkLeadUploadTrackerRepository,
            IRepositoryWithEvents<Domain.Entities.Lead> LeadRepo,
            IRepositoryWithEvents<Domain.Entities.LeadHistory> leadHistoryRepo,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<Lrb.Domain.Entities.Project> projectRepo)
        {
            _blobStorageService = blobStorageService;
            _hangfireService = hangfireService;
            _notificationSenderService = notificationSenderService;
            _propertyRepo = propertyRepo;
            _propertyTypeRepo = propertyTypeRepo;
            //_leadStatusRepo = leadStatusRepo;
            _addressRepo = addressRepo;
            _masterAreaUnitRepo = masterAreaUnitRepo;
            _leadRepository = leadRepository;
            _leadHistoryRepository = leadHistoryRepository;
            _npgsqlRepo = npgsqlRepo;
            _userService = userService;
            _currentUser = currentUser;
            _tenantIndependentRepo = tenantIndependentRepo;
            _bulkLeadUploadTrackerRepository = bulkLeadUploadTrackerRepository;
            _leadRepo = LeadRepo;
            _leadHistoryRepo = leadHistoryRepo;
            _leadRepositoryAsync = leadRepositoryAsync;
            _projectRepo = projectRepo;
        }
        public async Task<Response<BulkLeadUploadTracker>> Handle(CreateBulkLeadRequestUsingEPPlus request, CancellationToken cancellationToken)
        {
            //Initialize a Tracker 
            BulkLeadUploadTracker leadUploadTracker = request.Adapt<BulkLeadUploadTracker>();
            leadUploadTracker.Status = UploadStatus.Started;
            await _bulkLeadUploadTrackerRepository.AddAsync(leadUploadTracker);

            #region Fetch all required MasterData and Other data
            var projects = new List<Lrb.Domain.Entities.Project>(await _projectRepo.ListAsync(cancellationToken));
            var properties = new List<Domain.Entities.Property>(await _propertyRepo.ListAsync(cancellationToken));
            var propetyTypes = new List<MasterPropertyType>(await _propertyTypeRepo.ListAsync(cancellationToken));
            var areaUnits = new List<MasterAreaUnit>(await _masterAreaUnitRepo.ListAsync(cancellationToken));
            //var leadStatuses = new List<MasterLeadStatus>(await _leadStatusRepo.ListAsync(cancellationToken));
            var users = new List<UserDetailsDto>(await _userService.GetListAsync(cancellationToken));
            var existingLeads = await _leadRepo.ListAsync();
            #endregion

            #region Convert file to Datatable
            Stream fileStream = await _blobStorageService.GetObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", request.S3BucketKey);
            DataTable dataTable = new();
            if (request.S3BucketKey.Split('.').LastOrDefault() == "csv")
            {
                using MemoryStream memoryStream = new();
                fileStream.CopyTo(memoryStream);
                dataTable = CSVHelper.CSVToDataTable(memoryStream);
            }
            else
            {
                //dataTable = ExcelHelper.ExcelToDataTable(fileStream);
                dataTable = EPPlusExcelHelper.ConvertExcelToDataTable(fileStream);
            }
            int totalRows = dataTable.Rows.Count;
            for (int i = totalRows - 1; i >= 0; i--)
            {
                var row = dataTable.Rows[i];
                if (row.ItemArray.All(i => string.IsNullOrEmpty(i.ToString())))
                {
                    row.Delete();
                }
            }
            if (dataTable.Rows.Count <= 0)
            {
                throw new Exception("Excel sheet is empty. Please fill some data in the excel sheet template.");
            }
            #endregion

            #region checking For new Properties or projects
            List<Domain.Entities.Property> newProperties = new List<Domain.Entities.Property>();
            List<Lrb.Domain.Entities.Project> newProjects = new List<Lrb.Domain.Entities.Project>();

            if ((request?.MappedColumnsData?.ContainsKey(DataColumns.Property) ?? false) && (request?.MappedColumnsData[DataColumns.Property] != null)
                && (request?.MappedColumnsData?.ContainsKey(DataColumns.Project) ?? false) && (request?.MappedColumnsData[DataColumns.Project] != null))
            {
                var existingPropertynames = properties?.Where(i => i != null && !string.IsNullOrEmpty(i.Title)).Select(i => i.Title.ToLower()).ToList();
                var existingprojctnames = projects?.Where(i => i != null && !string.IsNullOrEmpty(i.Name)).Select(i => i.Name.ToLower()).ToList();
                dataTable.AsEnumerable().ToList().ForEach(row =>
                {
                    var propertyName = row[request.MappedColumnsData[DataColumns.Property]]?.ToString();
                    if (!string.IsNullOrWhiteSpace(propertyName) && !(existingPropertynames?.Contains(propertyName.ToLower()) ?? false) && !newProperties.Select(i => i.Title).Contains(propertyName))
                    {
                        newProperties.Add(new()
                        {
                            Title = propertyName,
                        });
                    }
                    var projectName = row[request.MappedColumnsData[DataColumns.Project]]?.ToString();
                    if (!string.IsNullOrWhiteSpace(projectName) && !(existingprojctnames?.Contains(projectName.ToLower()) ?? false) && !newProjects.Select(i => i.Name).Contains(projectName))
                    {
                        newProjects.Add(new()
                        {
                            Name = projectName,
                        });
                    }
                });
            }
            else if ((request?.MappedColumnsData?.ContainsKey(DataColumns.Property) ?? false) && (request?.MappedColumnsData[DataColumns.Property] != null))
            {
                var existingPropertynames = properties?.Where(i => i != null && !string.IsNullOrEmpty(i.Title)).Select(i => i.Title.ToLower()).ToList();
                dataTable.AsEnumerable().ToList().ForEach(row =>
                {
                    var propertyName = row[request.MappedColumnsData[DataColumns.Property]]?.ToString();
                    if (!string.IsNullOrWhiteSpace(propertyName) && !(existingPropertynames?.Contains(propertyName.ToLower()) ?? false) && !newProperties.Select(i => i.Title).Contains(propertyName))
                    {
                        newProperties.Add(new()
                        {
                            Title = propertyName,
                        });
                    }
                });
            }
            else if ((request?.MappedColumnsData?.ContainsKey(DataColumns.Project) ?? false) && (request?.MappedColumnsData[DataColumns.Project] != null))
            {
                var existingprojctnames = projects?.Where(i => i != null && !string.IsNullOrEmpty(i.Name)).Select(i => i.Name.ToLower()).ToList();
                dataTable.AsEnumerable().ToList().ForEach(row =>
                {
                    var projectName = row[request.MappedColumnsData[DataColumns.Project]]?.ToString();
                    if (!string.IsNullOrWhiteSpace(projectName) && !(existingprojctnames?.Contains(projectName.ToLower()) ?? false) && !newProjects.Select(i => i.Name).Contains(projectName))
                    {
                        newProjects.Add(new()
                        {
                            Name = projectName,
                        });
                    }
                });
            }
            if (newProperties.Any())
            {
                await _propertyRepo.AddRangeAsync(newProperties);
                properties.AddRange(newProperties);
            }
            if (newProjects.Any())
            {
                await _projectRepo.AddRangeAsync(newProjects);
                projects.AddRange(newProjects);
            }
            #endregion

            var unMappedColumns = dataTable.GetUnmappedColumnNames(request?.MappedColumnsData);
            var leads = dataTable.ConvertToLeads(request?.MappedColumnsData, unMappedColumns, projects, properties, propetyTypes, areaUnits, new List<MasterLeadStatus>(), users);
            leads.ForEach(lead => lead.SetLead(request.MappedColumnsData, _currentUser.GetUserId()));
            leads = leads.DistinctBy(i => i.ContactNo).ToList();
            var existingContactNos = existingLeads.Select(i => i.ContactNo).ToList();
            List<InvalidData> invalids = new List<InvalidData>();
            Parallel.ForEach(leads, lead =>
            {
                if (existingContactNos.Any(i => i.Contains(lead.ContactNo)))
                {
                    var invalidLead = lead.Adapt<InvalidData>();
                    invalidLead.Errors = "Duplicate Lead";
                    invalids.Add(invalidLead);
                }
                else if (!Regex.IsMatch(lead.ContactNo, RegexPatterns.IndianPhoneNumberPattern) || string.IsNullOrEmpty(lead.Name.Trim()))
                {
                    var invalidLead = lead.Adapt<InvalidData>();
                    invalidLead.Errors = "Invalid Data";
                    invalids.Add(invalidLead);
                }
            });
            leads.RemoveAll(i => invalids.Select(i => i.ContactNo).Contains(i.ContactNo));

            //update Tracker
            leadUploadTracker.Status = UploadStatus.InProgress;
            leadUploadTracker.TotalCount = totalRows;
            leadUploadTracker.DistinctLeadCount = leads.Count();
            if (invalids.Any())
            {
                leadUploadTracker.DuplicateCount = invalids.Where(i => i.Errors == "Duplicate Lead").Count();
                leadUploadTracker.InvalidCount = invalids.Where(i => i.Errors == "Invalid Data").Count();
                byte[] bytes = CreateLeadHelper.CreateExcelData(invalids).ToArray();
                string fileName = $"InvalidLeads-{DateTime.Now:yyyy_MM_dd-HH_mm_ss}.xlsx";
                string folder = "Leads";
                var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", folder, fileName, bytes);
                leadUploadTracker.InvalidDataS3BucketKey = key;
            }
            await _bulkLeadUploadTrackerRepository.UpdateAsync(leadUploadTracker);

            if (leads.Count > 0)
            {
                int leadsPerchunk = leads.Count > 5000 ? leads.Count / 5000 : leads.Count;
                var chunks = leads.Chunk(leadsPerchunk).Select(i => new ConcurrentBag<Domain.Entities.Lead>(i));
                List<Task> tasks = new List<Task>();
                var currentUserId = _currentUser.GetUserId();
                var tenantId = _currentUser.GetTenant();
                var tenantInfo = await _tenantIndependentRepo.GetTenantInfoAsync(tenantId);

                var chunkIndex = 1;
                foreach (var chunk in chunks.ToList())
                {
                    var backgroundDto = new BulkUploadbackgroundDto()
                    {
                        CurrentUserId = currentUserId,
                        TrackerId = leadUploadTracker.Id,
                        TenantInfoDto = tenantInfo,
                        CancellationToken = CancellationToken.None,
                        Leads = new(chunk),
                        UserIds = new(request?.UserIds ?? new()),
                        Users = users.ToList(),
                    };
                    if (chunkIndex == chunks.Count())
                    {
                        backgroundDto.IsLastChunk = true;
                    }
                    await ExecuteDBOperationsAsync(backgroundDto);
                    chunkIndex++;
                }

                

            }

            leadUploadTracker = await _bulkLeadUploadTrackerRepository.GetByIdAsync(leadUploadTracker.Id);
            return new(leadUploadTracker);
        }
        public async Task ExecuteDBOperationsAsync(BulkUploadbackgroundDto dto)
        {
            try
            {
                if (dto?.UserIds?.Any() ?? false)
                {
                    dto.Leads.AssignLead(dto.UserIds.Select(i => Guid.Parse(i)).ToList(), dto.CurrentUserId);
                }
                var leads = await _leadRepo.AddRangeAsync(dto.Leads);
                var leadDtos = leads.Adapt<List<ViewLeadDto>>();
                dto.LeadDtos = leadDtos;
                List<LeadHistory> leadHistories = new();
                leadDtos.ForEach(leadDto =>
                {
                    leadDto.SetUsersInViewLeadDto(dto.Users);
                    leadHistories.Add(LeadHistoryHelper.LeadHistoryMapper(leadDto));
                });
                leadHistories = (await _leadHistoryRepo.AddRangeAsync(leadHistories)).ToList();
                var tracker = await _bulkLeadUploadTrackerRepository.GetByIdAsync(dto.TrackerId);
                if (tracker != null)
                {
                    tracker.TotalUploadedCount += dto.Leads.Count;
                    if (dto.IsLastChunk)
                    {
                        tracker.Status = UploadStatus.Completed;
                    }
                    await _bulkLeadUploadTrackerRepository.UpdateAsync(tracker);
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CreateBulkLeadRequestUsingEPPlusHandler -> ExecuteDBOperationsAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            try
            {
                await SendNotificationsasync(dto);
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CreateBulkLeadRequestUsingEPPlusHandler -> ExecuteDBOperationsAsync() -> SendNotificationsasync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
        }
        private async Task SendNotificationsasync(BulkUploadbackgroundDto dto)
        {
            if (dto?.UserIds?.Any() ?? false)
            {
                var userIdsWithNoOfLeadsAssigned = dto.Leads.Where(i => i.AssignTo != Guid.Empty).GroupBy(i => i.AssignTo).ToDictionary(i => i.Key, j => j.Count());
                if (userIdsWithNoOfLeadsAssigned.Count() > 0)
                {
                    foreach (var item in userIdsWithNoOfLeadsAssigned)
                    {
                        try
                        {
                            var userDetails = dto.Users.FirstOrDefault(i => i.Id == item.Key);
                            if (userDetails.Id != dto.CurrentUserId)
                            {
                                var lead = dto.Leads.FirstOrDefault(i => i.AssignTo == item.Key);
                                if (item.Value == 1)
                                {
                                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadAssignment, lead, userDetails.Id, userDetails.FirstName + " " + userDetails.LastName, null, item.Value);
                                }
                                else
                                {
                                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.MultipleLeadAssignment, lead, userDetails.Id, userDetails.FirstName + " " + userDetails.LastName, null, item.Value);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            var error = new LrbError()
                            {
                                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                ErrorSource = ex?.Source,
                                StackTrace = ex?.StackTrace,
                                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                ErrorModule = "CreateBulkLeadRequestUsingEPPlusHandler -> SendNotificationsasync()"
                            };
                            await _leadRepositoryAsync.AddErrorAsync(error);
                        }
                    }
                }
            }
            else
            {
                List<Guid> adminIds = await _npgsqlRepo.GetAdminIdsAsync(dto.TenantInfoDto.Id);
                var leadForNotification = dto.Leads?.FirstOrDefault();
                if (adminIds != null && adminIds.Any())
                {
                    foreach (var adminId in adminIds)
                    {
                        var adminDetails = await _userService.GetAsync(adminId.ToString(), dto.CancellationToken);
                        if (adminDetails != null)
                        {
                            List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.UnAssignedLeadUpdate, leadForNotification, adminId, adminDetails.FirstName + " " + adminDetails.LastName, topics: new List<string> { leadForNotification.CreatedBy.ToString(), leadForNotification.LastModifiedBy.ToString() }, dto.Leads.Count());
                        }
                    }
                }
            }
        }
    }
}
