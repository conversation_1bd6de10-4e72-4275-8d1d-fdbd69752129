﻿using Lrb.Domain.Enums;
using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Infrastructure.Identity
{
    public class ApplicationUserLogin : IdentityUserLogin<string>
    {
        public Platform? Platform { get; set; }
        public string? DeviceModel { get; set; }
        public string? DeviceUDID { get; set; }
        public string? DeviceName { get; set; }
        public DateTime CreatedOn { get; set; } = DateTime.UtcNow;
    }
}
