﻿using Lrb.Application.Project.Web;
using Lrb.Application.Project.Web.Dtos;
using Lrb.Application.Project.Web.Requests;


namespace Lrb.WebApi.Host.Controllers.V2
{

    [Authorize]
    [Route("api/v2/[controller]")]
    [ApiVersionNeutral]
    public class ProjectController : VersionedApiController
    {
        private readonly Serilog.ILogger _logger;
        public ProjectController(Serilog.ILogger logger)
        {
            _logger = logger;
        }
        [HttpPost]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Search, LrbResource.Projects)]
        [OpenApiOperation("Search projects using available filters.", "")]
        public async Task<PagedResponse<ViewProjectDto, string>> SearchAsync([FromBody] GetAllProjectRequest request)
        {
            return await Mediator.Send(request);
        }
        [AllowAnonymous]
        [HttpPost("anonymous")]
        [TenantIdHeader]
        [OpenApiOperation("Get all Projects.", "")]
        public Task<PagedResponse<PullViewProjectDto, string>> GetAllProjectsAsync([FromBody] GetAllProjectAnonymousRequest request)
        {
            if (HttpContext.Request.Headers.TryGetValue(Lrb.Shared.Multitenancy.MultitenancyConstants.TenantIdName, out var tenantIdValues))
            {
                string? tenantId = tenantIdValues.FirstOrDefault();
                _logger.Information("GetAllProjectAnonymousRequest info: PageNumber={PageNumber}, PageSize={PageSize}, TenantId={TenantId}", request.PageNumber, request.PageSize, tenantId);

            }
            return Mediator.Send(request);
        }
        [HttpPost("count")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get Project Top Level Count")]
        public async Task<Response<ProjectTopLevelCountDto>> GetProjectTopLevelCount([FromBody] GetProjectTopLevelCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("UnitInfos")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get all unit infos using available filters.", "")]
        public async Task<PagedResponse<ViewUnitTypeDto, string>> SearchAsync([FromBody] GetAllProjectUnitInfoRequest request)
        {
            return await Mediator.Send(request);
        }
    }
}
