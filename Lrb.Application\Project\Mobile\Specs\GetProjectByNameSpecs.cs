﻿
namespace Lrb.Application.Project.Mobile.Specs
{
    public class GetProjectByNameSpecs : Specification<Lrb.Domain.Entities.Project>
    {
        public GetProjectByNameSpecs(string? projectName)
        {
            Query.Where(i => !i.IsDeleted && i.Name.ToLower().Trim() == projectName.ToLower().Trim());
        }
    }
    public class GetProjectByNameSpecsV1 : Specification<Lrb.Domain.Entities.Project, ProjectTitleDto>
    {
        public GetProjectByNameSpecsV1(string? projectName)
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived && i.Name.ToLower().Trim() == projectName.ToLower().Trim());
        }
    }
}
