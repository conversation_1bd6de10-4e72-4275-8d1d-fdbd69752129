﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Utils;

namespace Lrb.Application.Reports.Web
{
    public class CreateExcelForVisitAndMeetingReportBySourceRequest : IRequest<Response<string>>
    {
        public string? SearchText { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public DateTime? ScheduledFrom { get; set; }
        public DateTime? ScheduledTo { get; set; }
        public bool IsWithTeam { get; set; }
        public List<Guid>? UserIds { get; set; }
        public List<string>? Projects { get; set; }
        public List<LeadSource>? Sources { get; set; }
        public List<string>? AgencyNames { get; set; }
        public List<string>? SubSources { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = int.MaxValue;
        public string? TimeZoneId { get; set; }
        public TimeSpan BaseUTcOffset { get; set; }
    }
    public class CreateExcelForVisitAndMeetingReportBySourceRequestHandler : IRequestHandler<CreateExcelForVisitAndMeetingReportBySourceRequest, Response<string>>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly IBlobStorageService _blobStorageService;
        private readonly ICurrentUser _currentUser;

        public CreateExcelForVisitAndMeetingReportBySourceRequestHandler(IDapperRepository dapperRepository, IBlobStorageService blobStorageService, ICurrentUser currentUser)
        {
            _dapperRepository = dapperRepository;
            _blobStorageService = blobStorageService;
            _currentUser = currentUser;
        }

        public async Task<Response<string>> Handle(CreateExcelForVisitAndMeetingReportBySourceRequest request, CancellationToken cancellationToken)
        {
            var tenantId = _currentUser.GetTenant();
            var userId = _currentUser.GetUserId();
            List<Guid> teamUserIds = new();
            if (request?.UserIds?.Any() ?? false)
            {
                if (request?.IsWithTeam ?? false)
                {
                    teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                }
                else
                {
                    teamUserIds = request?.UserIds ?? new List<Guid>();
                }
            }
            else
            {
                request.UserIds = new List<Guid>() { userId };
                teamUserIds = (await _dapperRepository.GetSubordinateIdsAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
            }
            request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
            request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
            request.ScheduledFrom = request.ScheduledFrom != null ? request.ScheduledFrom.Value.ConvertFromDateToUtc() : null;
            request.ScheduledTo = request.ScheduledTo != null ? request.ScheduledTo.Value.ConvertToDateToUtc() : null;
            var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<LeadAppointmentBySourceDto>("LeadratBlack", "GetMeetingAndSitevisitReportBySource", new
            {
                fromdate = request.FromDate,
                todate = request.ToDate,
                scheduledfrom = request.ScheduledFrom,
                scheduledto = request.ScheduledTo,
                userids = teamUserIds,
                projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                sources = request?.Sources?.ConvertAll(i => (int)i),
                tenantid = tenantId,
                searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                agencynames = request?.AgencyNames?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                pagesize = request.PageSize,
                pagenumber = request.PageNumber
            }))?.ToList();
            res?.ForEach(i => i.TotalCount = (i.SiteVisitScheduledCount + i.MeetingScheduledCount + i.SiteVisitDoneCount + i.SiteVisitNotDoneCount + i.MeetingDoneCount + i.MeetingNotDoneCount + i.BookedCount + i.DroppedCount + i.NotInterestedCount));
            var fileBytes = ExcelHelper.CreateExcelFromList(res, new List<string>(), new List<string>() { "Id" },request.TimeZoneId, request.BaseUTcOffset).ToArray();
            var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", $"Reports/{tenantId ?? "Default"}", $"Report-{DateTime.Now}.xlsx", fileBytes);
            var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
            return new(presignedUrl, null);
        }
    }
}
