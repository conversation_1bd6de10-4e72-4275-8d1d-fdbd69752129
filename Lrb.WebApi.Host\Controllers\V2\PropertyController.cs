﻿using Lrb.Application.Property.Web;
using Lrb.Application.Property.Web.Dtos;
using Lrb.Application.Property.Web.Requests;
using Lrb.Application.Property.Web.V2.Dtos;
using Lrb.Application.Property.Web.V2.Requests;
using Mapster;

namespace Lrb.WebApi.Host.Controllers.V2
{
    [Authorize]
    [Route("api/v2/[controller]")]
    [ApiVersionNeutral]
    public class PropertyController : VersionedApiController
    {
        [HttpGet]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get all Listing details.", "")]
        public async Task<PagedResponse<ViewPropertyDtoV2, string>> SearchAsync([FromQuery] GetAllListingV2Request request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Properties)]
        [OpenApiOperation("Create a new property lsiting.", "")]
        public Task<Response<UpdatePropertyDtoV2>> CreateAsync([FromBody] CreatePropertyDtoV2 dto)
        {
            CreatePropertyListingV2Request request = dto.Adapt<CreatePropertyListingV2Request>();
            return Mediator.Send(request);
        }

        [HttpPut("{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Properties)]
        [OpenApiOperation("Update a property listing.", "")]
        public async Task<ActionResult<Guid>> UpdateAsync(UpdatePropertyDtoV2 dto, Guid id)
        {
            return id != dto.Id
                ? BadRequest()
                : Ok(await Mediator.Send(dto.Adapt<UpdatePropertyListingV2Request>()));
        }

        [HttpDelete("{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Delete, LrbResource.Properties)]
        [OpenApiOperation("Delete a property listing.", "")]
        public Task<Response<Guid>> DeleteAsync(Guid id)
        {
            return Mediator.Send(new DeletePropertyListingByIdV2Request(id));
        }

        [HttpPost("publish")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.PublishProperty, LrbResource.Properties)]
        [OpenApiOperation("send properties for listing.", "")]
        public async Task<Response<bool>> ListPropertyAsync(PublishPropertyListingRequestV2 request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("location")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Properties)]
        [OpenApiOperation("Search location.", "")]
        public async Task<Response<List<ListingSourceAddressDtoV2>>> SearchAsync([FromQuery] GetPFLocationRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get property details by Id.", "")]
        public async Task<Response<ViewPropertyDtoV2>> GetAsync(Guid id)
        {
            return await Mediator.Send(new GetPropertyListingByIdRequestV2(id));
        }
        [HttpPost("archived")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get Archived Properties.", "")]
        public async Task<PagedResponse<ViewPropertyDto, PropertyCountDto>> GetArchivedProperties([FromBody] GetArchivedPropertiesRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("lead-count")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get lead count for proeprties by ids.", "")]
        public async Task<Response<IEnumerable<PropertyLeadCountDto>>> GetAsync([FromBody] GetLeadsCountByPropertyIdsRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("new/all")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get all property details.", "")]
        public Task<PagedResponse<ViewPropertyDto, string>> SearchAsync([FromBody] V2GetAllPropertyRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpPost("count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get all property details.", "")]
        public Task<Response<PropertyCountDto>> SearchAsync([FromBody] V2GetAllPropertyCountRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpPost("all/listing")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get all property details.", "")]
        public async Task<PagedResponse<ViewListingManagementDto, string>> SearchAsync([FromBody] GetAllPropertyForListingManagementRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("listing/top-count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get all property top level count.", "")]
        public async Task<Response<GetPropertyCountForListingManagementDto>> CountAsync([FromBody] GetPropertyBaseLevelCountForListingManagementRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("listing/base-count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get all property base level count.", "")]
        public async Task<Response<GetPropertySecondLevelFilterCountForListingManagementDto>> CountAsync([FromBody] GetPropertySecondLevelCountForListingRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("Listing/details")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get all Listing details.", "")]
        public async Task<PagedResponse<ViewPropertyDtoV2, string>> getSearchAsync([FromBody] GetAllListingV2Request request)
        {
            return await Mediator.Send(request);
        }
    }
}
