using System.Net;
using System.Security.Claims;
using Lrb.Infrastructure.RateLimiting.Configuration;
using Lrb.Shared.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;

namespace Lrb.Infrastructure.RateLimiting.Services;

/// <summary>
/// Service for identifying clients for rate limiting purposes
/// </summary>
public class ClientIdentifierService : IClientIdentifierService
{
    private readonly RateLimitingSettings _settings;

    public ClientIdentifierService(IOptions<RateLimitingSettings> settings)
    {
        _settings = settings.Value;
    }

    public string GetClientIdentifier(HttpContext context)
    {
        var identifiers = new List<string>();

        // Custom header identifier (highest priority)
        if (!string.IsNullOrEmpty(_settings.CustomIdentifierHeader) &&
            context.Request.Headers.TryGetValue(_settings.CustomIdentifierHeader, out var customValue))
        {
            identifiers.Add($"custom:{customValue}");
        }

        // User ID (if authenticated and enabled)
        if (_settings.TrackByUserId && context.User.Identity?.IsAuthenticated == true)
        {
            var userId = context.User.GetUserId();
            if (!string.IsNullOrEmpty(userId))
            {
                identifiers.Add($"user:{userId}");
            }
        }

        // Tenant ID (if enabled)
        if (_settings.TrackByTenantId)
        {
            var tenantId = context.User.GetTenant();
            if (!string.IsNullOrEmpty(tenantId))
            {
                identifiers.Add($"tenant:{tenantId}");
            }
        }

        // IP Address or Network
        var ipAddress = GetClientIpAddress(context);
        if (ipAddress != null)
        {
            if (_settings.TrackByNetwork)
            {
                var networkId = GetNetworkIdentifier(ipAddress, _settings.NetworkMask);
                identifiers.Add($"network:{networkId}");
            }
            else if (_settings.TrackByIpAddress)
            {
                identifiers.Add($"ip:{ipAddress}");
            }
        }

        // Fallback to a combination or default
        return identifiers.Any() ? string.Join("|", identifiers) : "unknown";
    }

    public bool IsWhitelisted(HttpContext context)
    {
        // Check IP whitelist
        var ipAddress = GetClientIpAddress(context);
        if ((ipAddress != null) && (_settings.WhitelistedIps?.Any() ?? false) && (_settings.WhitelistedIps.Contains(ipAddress.ToString())))
        {
            return true;
        }

        // Check User Agent whitelist
        var userAgent = context.Request.Headers.UserAgent.ToString();
        if ((!string.IsNullOrEmpty(userAgent)) && (_settings.WhitelistedUserAgents?.Any() ?? false) &&
            (_settings.WhitelistedUserAgents.Any(ua => userAgent.Contains(ua, StringComparison.OrdinalIgnoreCase))))
        {
            return true;
        }

        return false;
    }

    public bool IsPathExcluded(string path)
    {
        if (string.IsNullOrEmpty(path))
            return false;
        if (!(_settings.ExcludedPaths?.Any() ?? false))
            return false;

        return _settings.ExcludedPaths.Any(excludedPath =>
        {
            if (excludedPath.EndsWith("*"))
            {
                var prefix = excludedPath[..^1];
                return path.StartsWith(prefix, StringComparison.OrdinalIgnoreCase);
            }
            return string.Equals(path, excludedPath, StringComparison.OrdinalIgnoreCase);
        });
    }

    private IPAddress? GetClientIpAddress(HttpContext context)
    {
        // Check for forwarded headers first (for load balancers/proxies)
        var forwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(forwardedFor))
        {
            var ips = forwardedFor.Split(',', StringSplitOptions.RemoveEmptyEntries);
            if (ips.Length > 0 && IPAddress.TryParse(ips[0].Trim(), out var forwardedIp))
            {
                return forwardedIp;
            }
        }

        // Check X-Real-IP header
        var realIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(realIp) && IPAddress.TryParse(realIp, out var realIpAddress))
        {
            return realIpAddress;
        }

        // Use connection remote IP
        return context.Connection.RemoteIpAddress;
    }

    private string GetNetworkIdentifier(IPAddress ipAddress, int networkMask)
    {
        try
        {
            if (ipAddress.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
            {
                // IPv4
                var bytes = ipAddress.GetAddressBytes();
                var maskBytes = GetSubnetMaskBytes(networkMask);

                for (int i = 0; i < bytes.Length; i++)
                {
                    bytes[i] = (byte)(bytes[i] & maskBytes[i]);
                }

                var networkAddress = new IPAddress(bytes);
                return $"{networkAddress}/{networkMask}";
            }
            else if (ipAddress.AddressFamily == System.Net.Sockets.AddressFamily.InterNetworkV6)
            {
                // IPv6 - simplified implementation
                var bytes = ipAddress.GetAddressBytes();
                var prefixBytes = networkMask / 8;
                var remainingBits = networkMask % 8;

                for (int i = prefixBytes; i < bytes.Length; i++)
                {
                    if (i == prefixBytes && remainingBits > 0)
                    {
                        var mask = (byte)(0xFF << (8 - remainingBits));
                        bytes[i] = (byte)(bytes[i] & mask);
                    }
                    else
                    {
                        bytes[i] = 0;
                    }
                }

                var networkAddress = new IPAddress(bytes);
                return $"{networkAddress}/{networkMask}";
            }
        }
        catch
        {
            // Fallback to IP address if network calculation fails
        }

        return ipAddress.ToString();
    }

    private byte[] GetSubnetMaskBytes(int networkMask)
    {
        var mask = 0xFFFFFFFF << (32 - networkMask);
        return new byte[]
        {
            (byte)((mask >> 24) & 0xFF),
            (byte)((mask >> 16) & 0xFF),
            (byte)((mask >> 8) & 0xFF),
            (byte)(mask & 0xFF)
        };
    }
}