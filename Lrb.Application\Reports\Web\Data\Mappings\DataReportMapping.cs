﻿using Lrb.Application.Reports.Web.Data.Dtos.User;
using Lrb.Application.Reports.Web.Dtos.Activity;
using Lrb.Application.Reports.Web.UserVsSource;
using Lrb.Application.Reports.Web.Data.Dtos;
using Lrb.Application.Reports.Web.Dtos.FiltersName;
using Lrb.Application.Reports.Web.Data.Dtos.Campaign;
using Lrb.Application.Reports.Web.Data.Dtos.ChannelPartner;

namespace Lrb.Application.Reports.Web
{
    public static class DataReportMapping
    {
        public static void Configure(IServiceProvider serviceProvider)
        {

            TypeAdapterConfig<DataUserDto, BaseDataReportByUserDto>
                .NewConfig()
                .Map(dest => dest.UserName, src => src.FirstName + "" + src.LastName);
            TypeAdapterConfig<BaseDataReportByUserDto, FormattedDataReportByUserDto>
                .NewConfig()
                .Map(dest => dest.Name, src => src.UserName)
                .Map(dest => dest.ConvertedData, src => src.ConvertedDataCount)
                .Map(dest => dest.Data, src => src.Data != null ? src.Data.Adapt<List<ViewFormatteDataReportByUserDto>>() : null);
            TypeAdapterConfig<ViewDataReportByUserDto, ViewFormatteDataReportByUserDto>
                .NewConfig()
                .Map(dest => dest.Status, src => src.StatusDisplayName)
                .Map(dest => dest.Count, src => src.DataCount);

            TypeAdapterConfig<ViewDataSubSourceDto, FormattedDataSubSourceDto>
                .NewConfig()
                .Map(dest => dest.Name, src => src.Name)
                .Map(dest => dest.ConvertedData, src => src.ConvertedDataCount)
                .Map(dest => dest.Data, src => src.Data != null ? src.Data.Adapt<List<ViewFormattedDataReportBySubSourceDto>>() : null);
            TypeAdapterConfig<ViewDataReportBySubSourceDto, ViewFormattedDataReportBySubSourceDto>
                .NewConfig()
                .Map(dest => dest.Status, src => src.StatusDisplayName)
                .Map(dest => dest.Count, src => src.DataCount);

            TypeAdapterConfig<ViewDataSourceDto, FormattedDataSourceDto>
                .NewConfig()
                .Map(dest => dest.Name, src => src.Name)
                .Map(dest => dest.ConvertedData, src => src.ConvertedDataCount)
                .Map(dest => dest.Data, src => src.Data != null ? src.Data.Adapt<List<ViewFormattedDataReportBySourceDto>>() : null);
            TypeAdapterConfig<ViewDataReportBySourceDto, ViewFormattedDataReportBySourceDto>
                .NewConfig()
                .Map(dest => dest.Status, src => src.StatusDisplayName)
                .Map(dest => dest.Count, src => src.DataCount);

            TypeAdapterConfig<ViewDataProjectDto, FormattedDataProjectDto>
                .NewConfig()
                .Map(dest => dest.Name, src => src.Name)
                .Map(dest => dest.ConvertedData, src => src.ConvertedDataCount)
                .Map(dest => dest.Data, src => src.Data != null ? src.Data.Adapt<List<ViewFormattedDataReportByProjectDto>>() : null);
            TypeAdapterConfig<ViewDataCampaignDto, FormattedDataCampaignDto>
                .NewConfig()
                .Map(dest => dest.Name, src => src.Name)
                .Map(dest => dest.ConvertedData, src => src.ConvertedDataCount)
                .Map(dest => dest.Data, src => src.Data != null ? src.Data.Adapt<List<ViewFormattedDataReportByCampaignDto>>() : null);
            TypeAdapterConfig<ViewDataChannelPartnerDto, FormattedDataChannelPartnerDto>
                .NewConfig()
                .Map(dest => dest.Name, src => src.Name)
                .Map(dest => dest.ConvertedData, src => src.ConvertedDataCount)
                .Map(dest => dest.Data, src => src.Data != null ? src.Data.Adapt<List<ViewFormattedDataReportByChannelPartnerDto>>() : null);
            TypeAdapterConfig<ViewDataReportByProjectDto, ViewFormattedDataReportByProjectDto>
                .NewConfig()
                .Map(dest => dest.Status, src => src.StatusDisplayName)
                .Map(dest => dest.Count, src => src.DataCount);

            TypeAdapterConfig<ViewDataAgencyDto, FormattedDataAgencyDto>
                .NewConfig()
                .Map(dest => dest.Name, src => src.Name)
                .Map(dest => dest.ConvertedData, src => src.ConvertedDataCount)
                .Map(dest => dest.Data, src => src.Data != null ? src.Data.Adapt<List<ViewFormattedDataReportByAgencyDto>>() : null);

            TypeAdapterConfig<ViewDataReportByAgencyDto, ViewFormattedDataReportByAgencyDto>
                .NewConfig()
                .Map(dest => dest.Status, src => src.StatusDisplayName)
                .Map(dest => dest.Count, src => src.DataCount);

            TypeAdapterConfig<ViewDataReportByCampaignDto, ViewFormattedDataReportByCampaignDto>
                .NewConfig()
                .Map(dest => dest.Status, src => src.StatusDisplayName)
                .Map(dest => dest.Count, src => src.DataCount);
            TypeAdapterConfig<ViewDataReportByChannelPartnerDto, ViewFormattedDataReportByChannelPartnerDto>
                .NewConfig()
                .Map(dest => dest.Status, src => src.StatusDisplayName)
                .Map(dest => dest.Count, src => src.DataCount);
            TypeAdapterConfig<DataActivityReportDto, DataActivityFormattedDto>
                .NewConfig()
                .Map(dest => dest.Calls, src => src.CallsInitiatedCount)
                .Map(dest => dest.CallsUnique, src => src.CallsInitiatedDataCount)
                .Map(dest => dest.WhatsApp, src => src.WhatsAppInitiatedCount)
                .Map(dest => dest.WhatsAppUnique, src => src.WhatsAppInitiatedDataCount)
                .Map(dest => dest.StatusEdits, src => src.StatusEditsCount)
                .Map(dest => dest.StatusEditsUnique, src => src.StatusEditsDataCount)
                .Map(dest => dest.FormEdits, src => src.FormEditsCount)
                .Map(dest => dest.FormEditsUnique, src => src.FormEditsDataCount)
                .Map(dest => dest.NotesAdded, src => src.NotesAddedCount)
                .Map(dest => dest.NotesUnique, src => src.NotesAddedDataCount)
                .Map(dest => dest.NotInterestedUnique, src => src.NotInterestedDataCount)
                .Map(dest => dest.NotReachableUnique, src => src.NotReachableDataCount);
            TypeAdapterConfig<UserDataCommunicationReportDto, DataCommunicationReportDto>
                .NewConfig()
                .Map(dest => dest.CallsInitiatedAllCount, src => src.CallsInitiatedCount)
                .Map(dest => dest.CallsInitiatedDataUniqueCount, src => src.CallsInitiatedDataCount)
                .Map(dest => dest.WhatsAppInitiatedAllCount, src => src.WhatsAppInitiatedCount)
                .Map(dest => dest.WhatsAppInitiatedDataUniqueCount, src => src.WhatsAppInitiatedDataCount);

            TypeAdapterConfig<UserDataActivityReportDto, UserActivityDataReportDto>
                .NewConfig()
                .Map(dest => dest.StatusEditsAllCount, src => src.StatusEditsCount)
                .Map(dest => dest.StatusEditsDataUniqueCount, src => src.StatusEditsDataCount)
                .Map(dest => dest.FormEditsAllCount, src => src.FormEditsCount)
                .Map(dest => dest.FormEditsDataUniqueCount, src => src.FormEditsDataCount)
                .Map(dest => dest.NotesAddedAllCount, src => src.NotesAddedCount)
                .Map(dest => dest.NotesAddedDataUniqueCount, src => src.NotesAddedDataCount)
                .Map(dest => dest.NotInterestedDataCount, src => src.NotInterestedDataCount)
                .Map(dest => dest.NotReachableDataCount, src => src.NotReachableDataCount);



            TypeAdapterConfig<CallLogReportDto, CallLogReportFormattedDto>
                .NewConfig()
                .Map(dest => dest.UserName, src => src.FirstName + " " + src.LastName)
                .Map(dest => dest.Incoming, src => new IncomingDto()
                {
                    Answered = src.IncomingAnswered,
                    Missed = src.IncomingMissed,
                    Total = src.TotalIncomingCalls
                })
                .Map(dest => dest.Outgoing, src => new OutgoingDto()
                {
                    Answered = src.OutgoingAnswered,
                    NotConnected = src.OutgoingNotConnected,
                    Total = src.TotalOutgoingCalls
                })
                .Map(dest => dest.AverageTalkTime, src => TimeSpan.FromSeconds(src.AverageTalkTime).ToString(@"hh\:mm\:ss"))
                .Map(dest => dest.MaxTalkTime, src => TimeSpan.FromSeconds(src.MaxTalkTime).ToString(@"hh\:mm\:ss"))
                .Map(dest => dest.MinTalkTime, src => TimeSpan.FromSeconds(src.MinTalkTime).ToString(@"hh\:mm\:ss"))
                .Map(dest => dest.TotalTalkTime, src => TimeSpan.FromSeconds(src.TotalTalkTime).ToString(@"hh\:mm\:ss"));

        }
    }
}

