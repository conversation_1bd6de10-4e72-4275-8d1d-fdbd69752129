﻿using Lrb.Application.Automation.Dtos;
using Lrb.Application.ZonewiseLocation.Web.Dtos;
using Lrb.Application.ZonewiseLocation.Web.Requests;
using Lrb.Domain.Entities.AmenitiesAttributes;
using Lrb.Shared.Extensions;

namespace Lrb.Application.ZonewiseLocation.Web.Specs
{
    public class LocationByIdSpec : Specification<Location>
    {
        public LocationByIdSpec(Guid id)
        {
            Query
               .Include(i => i.Zone)
                .ThenInclude(i => i.UserAssignment)
               .Include(i => i.City)
                .ThenInclude(i => i.UserAssignment)
               .Include(i => i.State)
                .ThenInclude(i => i.UserAssignment)
               .Include(i => i.Country)
               .Include(i => i.UserAssignment)
                .Where(i => !i.IsDeleted && i.Id == id)
               .OrderByDescending(i => i.LastModifiedOn);
        }
        public LocationByIdSpec(List<Guid> ids)
        {
            Query
               .Include(i => i.Zone)
                .ThenInclude(i => i.UserAssignment)
               .Include(i => i.City)
                .ThenInclude(i => i.UserAssignment)
               .Include(i => i.State)
                .ThenInclude(i => i.UserAssignment)
               .Include(i => i.Country)
               .Include(i => i.UserAssignment)
                .Where(i => !i.IsDeleted && ids.Contains(i.Id))
               .OrderByDescending(i => i.LastModifiedOn);
        }
    }
    public class LocationByLocalitySpec : Specification<Location>
    {
        public LocationByLocalitySpec(string? locality)
        {
            Query
               .Include(i => i.Zone)
                .ThenInclude(i => i.UserAssignment)
               .Include(i => i.City)
                .ThenInclude(i => i.UserAssignment)
               .Include(i => i.State)
                .ThenInclude(i => i.UserAssignment)
               .Include(i => i.Country)
               .Include(i => i.UserAssignment)
               .OrderByDescending(i => i.LastModifiedOn);

            var nornalizedName = locality?.LrbNormalize();
            Query.Where(i => !i.IsDeleted && i.NormalizedName != null && locality != null && i.Locality.ToLower() == locality.ToLower() && i.Locality != string.Empty);
        }

        public LocationByLocalitySpec(string? locality, string? city = null, string? state = null)
        {
            Query
               .Include(i => i.Zone)
                .ThenInclude(i => i.UserAssignment)
               .Include(i => i.City)
                .ThenInclude(i => i.UserAssignment)
               .Include(i => i.State)
                .ThenInclude(i => i.UserAssignment)
               .Include(i => i.Country)
               .Include(i => i.UserAssignment)
               .OrderByDescending(i => i.LastModifiedOn);

            var nornalizedName = locality?.LrbNormalize();
            Query.Where(i => !i.IsDeleted && i.NormalizedName != null && locality != null && i.Locality.ToLower() == locality.ToLower() && i.Locality != string.Empty);

            if (!string.IsNullOrWhiteSpace(city))
            {
                var normalizedCityName = city.LrbNormalize();
                Query.Where(i => i.City != null && i.City.NormalizedName == normalizedCityName);
            }
            if (!string.IsNullOrWhiteSpace(state))
            {
                var normalizedStateName = state.LrbNormalize();
                Query.Where(i => i.State != null && i.State.NormalizedName == normalizedStateName);
            }
        }

        public LocationByLocalitySpec(AddLocationRequest request)
        {
            Query
               .Include(i => i.Zone)
                .ThenInclude(i => i.UserAssignment)
               .Include(i => i.City)
                .ThenInclude(i => i.UserAssignment)
               .Include(i => i.State)
                .ThenInclude(i => i.UserAssignment)
               .Include(i => i.Country)
               .Include(i => i.UserAssignment)
               .OrderByDescending(i => i.LastModifiedOn);

            Query.Where(i => !i.IsDeleted);

            if (!string.IsNullOrWhiteSpace(request.PlaceId))
            {
                Query.Where(i => i.PlaceId == request.PlaceId);
            }
            else
            {
                var nornalizedName = request.Locality.LrbNormalize();
                Query.Where(i => i.NormalizedName != null && nornalizedName != null && i.Locality.ToLower() == request.Locality.ToLower() && i.Locality != string.Empty);

                if (!string.IsNullOrWhiteSpace(request.City) || (request.CityId != null && request.CityId != default))
                {
                    var normalizedCityName = request.City?.LrbNormalize();
                    Query.Where(i => (normalizedCityName != null && i.NormalizedName != null && i.NormalizedName.Contains(normalizedCityName)) || (i.City != null && (i.City.Id == request.CityId || i.City.NormalizedName == normalizedCityName)));
                }
                if (!string.IsNullOrWhiteSpace(request.State) || (request.StateId != null && request.StateId != default))
                {
                    var normalizedStateName = request.State?.LrbNormalize();
                    Query.Where(i => (normalizedStateName != null && i.NormalizedName != null && i.NormalizedName.Contains(normalizedStateName)) || (i.State != null && (i.State.Id == request.StateId || i.State.NormalizedName == normalizedStateName)));
                }
                if (!string.IsNullOrWhiteSpace(request.Country) || (request.CountryId != null && request.CountryId != default))
                {
                    var normalizedCountryName = request.Country?.LrbNormalize();
                    Query.Where(i => (normalizedCountryName != null && i.NormalizedName != null && i.NormalizedName.Contains(normalizedCountryName)) || (i.Country != null && (i.Country.Id == request.CountryId || i.Country.NormalizedName == normalizedCountryName)));
                }
            }
        }
        public LocationByLocalitySpec(List<Address> addresses)
        {
            Query
               .Include(i => i.Zone)
                .ThenInclude(i => i.UserAssignment)
               .Include(i => i.City)
                .ThenInclude(i => i.UserAssignment)
               .Include(i => i.State)
                .ThenInclude(i => i.UserAssignment)
               .Include(i => i.Country)
               .Include(i => i.UserAssignment)
               .OrderByDescending(i => i.LastModifiedOn);

            Query.Where(i => !i.IsDeleted);

            var placeIds = addresses
            .Where(l => !string.IsNullOrWhiteSpace(l.PlaceId))
            .Select(l => l.PlaceId)
            .Distinct()
            .ToList();

            if (placeIds.Any())
            {
                Query.Where(i => placeIds.Contains(i.PlaceId));
            }
            else
            {
                var normalizedLocalities = addresses
                .Where(l => !string.IsNullOrWhiteSpace(l.Locality))
                .Select(l => l.Locality.LrbNormalize())
                .Distinct()
                .ToList();
                if (normalizedLocalities.Any())
                {
                    Query.Where(i => i.NormalizedName != null && normalizedLocalities.Contains(i.NormalizedName));
                }

                var normalizedCityNames = addresses
                    .Where(l => !string.IsNullOrWhiteSpace(l.City))
                    .Select(l => l.City.LrbNormalize())
                    .Distinct()
                    .ToList();
                if (normalizedCityNames.Any())
                {
                    Query.Where(i =>
                        (i.City != null && normalizedCityNames.Contains(i.City.NormalizedName)));
                }

                var normalizedStateNames = addresses
                    .Where(l => !string.IsNullOrWhiteSpace(l.State))
                    .Select(l => l.State.LrbNormalize())
                    .Distinct()
                    .ToList();
                if (normalizedStateNames.Any())
                {
                    Query.Where(i =>
                        (i.State != null && normalizedStateNames.Contains(i.State.NormalizedName)));
                }

                var normalizedCountryNames = addresses
                    .Where(l => !string.IsNullOrWhiteSpace(l.Country))
                    .Select(l => l.Country.LrbNormalize())
                    .Distinct()
                    .ToList();
                if (normalizedCountryNames.Any())
                {
                    Query.Where(i =>
                        (i.Country != null && normalizedCountryNames.Contains(i.Country.NormalizedName)));
                }
            }
        }
    }

    public class LocationSpec : EntitiesByPaginationFilterSpec<Location, LocationDto>
    {
        public LocationSpec(GetAllLocationsRequest filter) : base(filter)
        {
            Query
                .Include(i => i.Zone)
                .Include(i => i.State)
                .Include(i => i.City)
                .Where(i => !i.IsDeleted)
                .OrderByDescending(i => i.LastModifiedOn);

            if (filter.Ids?.Any() ?? false)
            {
                Query.Where(i => filter.Ids.Contains(i.Id));
            }
            if (filter.CityIds?.Any() ?? false)
            {
                Query.Where(i => i.City != null && filter.CityIds.Contains(i.City.Id));
            }
            if (filter.StateIds?.Any() ?? false)
            {
                Query.Where(i => i.State != null && filter.StateIds.Contains(i.State.Id));
            }
            if (filter.ZoneIds?.Any() ?? false)
            {
                Query.Where(i => i.Zone != null && filter.ZoneIds.Contains(i.Zone.Id));
            }
            if (!string.IsNullOrWhiteSpace(filter.SearchText))
            {
                string normalizedText = filter.SearchText.LrbNormalize();
                Query.Where(i => !string.IsNullOrEmpty(i.NormalizedName) && i.NormalizedName.Contains(normalizedText));
            }
        }
    }

    public class LocationLeadSpec : EntitiesByPaginationFilterSpec<Location, LocationLeadDto>
    {
        public LocationLeadSpec(GetLocationsForLeadRequest filter) : base(filter)
        {
            Query
                .Include(i => i.Zone)
                .Include(i => i.City)
                .Include(i => i.State)
                .Include(i => i.Country)
                .Include(i => i.UserAssignment)
                .Where(i => !i.IsDeleted)
                .OrderByDescending(i => i.LastModifiedOn);

            if (!string.IsNullOrWhiteSpace(filter.SearchText))
            {
                string normalizedText = filter.SearchText.LrbNormalize();
                Query.Where(i => !string.IsNullOrEmpty(i.NormalizedName) && i.NormalizedName.Contains(normalizedText));
            }
        }
    }
    public class LocationBySearchSpec : Specification<Location>
    {
        public LocationBySearchSpec(GetLocationsByGlobalSettingsRequest filter)
        {
            Query
                .Include(i => i.Zone)
                .Include(i => i.City)
                .Include(i => i.State)
                .Include(i => i.Country)
                .Include(i => i.UserAssignment)
                .Where(i => !i.IsDeleted)
                .OrderByDescending(i => i.LastModifiedOn);

            if (!string.IsNullOrWhiteSpace(filter.SearchText))
            {
                string normalizedText = filter.SearchText.LrbNormalize();
                Query.Where(i => !string.IsNullOrEmpty(i.NormalizedName) && i.NormalizedName.Contains(normalizedText));
            }
        }
    }
    public class LocationCountSpec : Specification<Location>
    {
        public LocationCountSpec(GetAllLocationsRequest filter)
        {
            Query
                .Include(i => i.Zone)
                .Include(i => i.City)
                .Include(i => i.State)
                .Include(i => i.Country)
                .Include(i => i.UserAssignment)
                .Where(i => !i.IsDeleted)
                .OrderByDescending(i => i.LastModifiedOn);

            if (filter.Ids?.Any() ?? false)
            {
                Query.Where(i => filter.Ids.Contains(i.Id));
            }
            if (filter.CityIds?.Any() ?? false)
            {
                Query.Where(i => i.City != null && filter.CityIds.Contains(i.City.Id));
            }
            if (filter.StateIds?.Any() ?? false)
            {
                Query.Where(i => i.State != null && filter.StateIds.Contains(i.State.Id));
            }
            if (filter.ZoneIds?.Any() ?? false)
            {
                Query.Where(i => i.Zone != null && filter.ZoneIds.Contains(i.Zone.Id));
            }
            if (!string.IsNullOrWhiteSpace(filter.SearchText))
            {
                string normalizedText = filter.SearchText.LrbNormalize();
                Query.Where(i => !string.IsNullOrEmpty(i.NormalizedName) && i.NormalizedName.Contains(normalizedText));
            }
        }
        public LocationCountSpec(GetLocationsForLeadRequest filter)
        {
            Query
                .Include(i => i.Zone)
                .Include(i => i.City)
                .Include(i => i.State)
                .Include(i => i.Country)
                .Include(i => i.UserAssignment)
                .Where(i => !i.IsDeleted)
                .OrderByDescending(i => i.LastModifiedOn);

            if (!string.IsNullOrWhiteSpace(filter.SearchText))
            {
                string normalizedText = filter.SearchText.LrbNormalize();
                Query.Where(i => !string.IsNullOrEmpty(i.NormalizedName) && i.NormalizedName.Contains(normalizedText));
            }
        }
        public LocationCountSpec(GetLocationsByGlobalSettingsRequest filter)
        {
            Query
                .Include(i => i.Zone)
                .Include(i => i.City)
                .Include(i => i.State)
                .Include(i => i.Country)
                .Include(i => i.UserAssignment)
                .Where(i => !i.IsDeleted)
                .OrderByDescending(i => i.LastModifiedOn);
            if (!string.IsNullOrWhiteSpace(filter.SearchText))
            {
                string normalizedText = filter.SearchText.LrbNormalize();
                Query.Where(i => !string.IsNullOrEmpty(i.NormalizedName) && i.NormalizedName.Contains(normalizedText));
            }
        }
        public class LocationByCityIdsSpec : Specification<Location>
        {
            public LocationByCityIdsSpec(List<Guid> cityIds)
            {
                Query
                    .Include(i => i.City)
                    .Where(i => !i.IsDeleted)
                    .Where(i => i.City != null && cityIds.Contains(i.City.Id));
            }
        }
        public class AssignedLocationByIdSpec : Specification<Location, BaseAssignedEntityDto>
        {
            public AssignedLocationByIdSpec(Guid id)
            {
                Query.Where(i => !i.IsDeleted && i.Id == id);
            }
            public AssignedLocationByIdSpec(List<Guid> ids)
            {
                Query.Where(i => !i.IsDeleted && ids.Contains(i.Id));
            }
        }
        public class GetAmenityByIdsSpec : Specification<CustomMasterAmenity>
        {
            public GetAmenityByIdsSpec(Guid id)
            {
                Query.Where(i => !i.IsDeleted && (i.Id == id || i.MasterAmenityId == id));
            }
        }

        public class GetAttributeByIdsSpec : Specification<CustomMasterAttribute>
        {
            public GetAttributeByIdsSpec(Guid id)
            {
                Query.Where(i => !i.IsDeleted && (i.Id == id || i.MasterAttributeId == id));
            }
        }

    }
}
