﻿{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "DatabaseSettings": {
    "DBProvider": "postgresql",
    "ConnectionString": "Host=lrb-dev-new.postgres.database.azure.com;Port=5432;Database=leadratBlackApp;Username=dbmasteruser;Password=************`H9hTJK5ojM)C=$C6w,0;Pooling=true;MinPoolSize=3;MaxPoolSize=5000;"
  },
  "WorkingEndPoint": {
    "LrbBaseUri": "https://connect.leadratd.com",
    "JustLeadBaseUri": "https://www.lms.justlead.in",
    "CommonFloorBaseuri": "https://www.commonfloor.com"
  },
  "CosmosSettings": {
    "EndpointUri": "https://lrb-prd.documents.azure.com:443/",
    "PrimaryKey": "****************************************************************************************"
  }
}