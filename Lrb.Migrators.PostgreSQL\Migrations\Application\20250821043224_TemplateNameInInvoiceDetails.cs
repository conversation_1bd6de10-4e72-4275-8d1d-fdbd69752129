﻿using System.Collections.Generic;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class TemplateNameInInvoiceDetails : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<List<string>>(
                name: "InvoiceUrl",
                schema: "LeadratBlack",
                table: "LeadBookedDetails",
                type: "text[]",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TemplateName",
                schema: "LeadratBlack",
                table: "InvoiceDetails",
                type: "text",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "TemplateName",
                schema: "LeadratBlack",
                table: "InvoiceDetails");

            migrationBuilder.DropColumn(
                name: "InvoiceUrl",
                schema: "LeadratBlack",
                table: "LeadBookedDetails");
        }
    }
}
