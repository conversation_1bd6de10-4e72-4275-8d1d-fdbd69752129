﻿using Lrb.Application.Property.Web.Dtos;
using Lrb.Application.Property.Web.Specs;
using Lrb.Domain.Entities.AmenitiesAttributes;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;

namespace Lrb.Application.Property.Web
{
    public class GetArchivedPropertiesRequest : PaginationFilter,IRequest<PagedResponse<ViewPropertyDto, PropertyCountDto>>
    {
        public string? PropertySearch { get; set; }
    }
    public class GetArchivedPropertiesRequestHandler : IRequestHandler<GetArchivedPropertiesRequest, PagedResponse<ViewPropertyDto, PropertyCountDto>>
    {
        private readonly IReadRepository<Domain.Entities.Property> _propertyRepo;
        private readonly IReadRepository<MasterPropertyType> _masterPropertyTypeRepo;
        private readonly IReadRepository<CustomMasterAmenity> _masterPropertyAmenityRepo;
        private readonly IReadRepository<CustomMasterAttribute> _masterPropertyAttributeRepo;
        private readonly IReadRepository<MasterAreaUnit> _masterAreaUnitRepo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        public GetArchivedPropertiesRequestHandler(IReadRepository<Domain.Entities.Property> propertyRepo,
            IReadRepository<MasterPropertyType> masterPropertyTypeRepo,
            IReadRepository<CustomMasterAmenity> masterPropertyAmenityRepo,
            IReadRepository<CustomMasterAttribute> masterPropertyAttributeRepo,
            IReadRepository<MasterAreaUnit> masterAreaUnitRepo,
            ILeadRepositoryAsync leadRepositoryAsync
)
        {
            _propertyRepo = propertyRepo;
            _masterPropertyTypeRepo = masterPropertyTypeRepo;
            _masterPropertyAmenityRepo = masterPropertyAmenityRepo;
            _masterPropertyAttributeRepo = masterPropertyAttributeRepo;
            _masterAreaUnitRepo = masterAreaUnitRepo;
            _leadRepositoryAsync = leadRepositoryAsync;
        }

        public async Task<PagedResponse<ViewPropertyDto, PropertyCountDto>> Handle(GetArchivedPropertiesRequest request, CancellationToken cancellationToken)
        {
            CustomMasterAttribute? masterPropertyAttribute = null;
            CustomMasterAmenity? masterPropertyAmenity = null;
            MasterPropertyType? masterPropertyType = null;
            if (!string.IsNullOrWhiteSpace(request.PropertySearch))
            {
                masterPropertyAttribute = (await _masterPropertyAttributeRepo.ListAsync(new CustomMasterPropertyAttributeSpec(request?.PropertySearch?.Trim().ToLower() ?? string.Empty), cancellationToken)).FirstOrDefault();
                masterPropertyAmenity = (await _masterPropertyAmenityRepo.ListAsync(new GetCustomAmenitySpec(request?.PropertySearch?.Trim().ToLower() ?? string.Empty), cancellationToken)).FirstOrDefault();
                masterPropertyType = (await _masterPropertyTypeRepo.ListAsync(new GetMasterPropertyTypeSpec(request?.PropertySearch?.Trim().ToLower() ?? string.Empty), cancellationToken)).FirstOrDefault();
            }
            var masterPropertyAttributes = (await _masterPropertyAttributeRepo.ListAsync(new GetAllCustomMasterPropertyAttributeSpec(), cancellationToken));
            var archivedProperties = (await _propertyRepo.ListAsync(new GetArchivedPropertiesSpec(request, masterPropertyAttribute?.Id ?? null, masterPropertyAmenity?.Id ?? null, masterPropertyType?.Id ?? null), cancellationToken));
            var totalCount = (await _propertyRepo.CountAsync(new GetArchivedPropertiesCountSpec(request, masterPropertyAttribute?.Id ?? null, masterPropertyAmenity?.Id ?? null, masterPropertyType?.Id ?? null), cancellationToken));
            List<ViewPropertyDto> propertyDtos = new();
            List<ViewPropertyDto> resultPropertyDtos = new();
            try
            {
                propertyDtos = archivedProperties.Adapt<List<ViewPropertyDto>>();
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "GetArchivedPropertiesRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            foreach (var propertyDto in propertyDtos)
            {
                if (propertyDto != null && (propertyDto.Attributes?.Any() ?? false))
                {
                    //var property = properties.Where(i => i.Id == propertyDto.Id).FirstOrDefault();
                    //if (property != null && (property.Amenities?.Any() ?? false))
                    //{
                    //    propertyDto.Amenities = property.Amenities.Select(i => i.MasterPropertyAmenityId).ToList();
                    //}
                    List<PropertyAttributeDto> attributes = new();
                    foreach (var attribute in propertyDto.Attributes)
                    {
                        var masterAttribute = masterPropertyAttributes.Where(i => i.Id == attribute.MasterPropertyAttributeId).FirstOrDefault();
                        if (masterAttribute != null)
                        {
                            attribute.AttributeName = masterAttribute.AttributeName;
                            attributes.Add(attribute);
                        }
                        else
                        {
                            attributes.Add(attribute);
                        }
                    }
                    propertyDto.Attributes = attributes;
                    if (propertyDto != null && propertyDto.Dimension != null && propertyDto.Dimension.AreaUnitId != Guid.Empty)
                    {
                        var dimension = (await _masterAreaUnitRepo.ListAsync(new GetMasterAreaUnitByAreaIdSpec(propertyDto?.Dimension?.AreaUnitId ?? Guid.Empty), cancellationToken)).FirstOrDefault();
                        propertyDto.Dimension.Unit = dimension?.Unit ?? null;
                    }
                    if(propertyDtos != null)
                    {
                        resultPropertyDtos.Add(propertyDto);
                    }
                }
                else if (propertyDto != null)
                {
                    if (propertyDto != null && propertyDto.Dimension != null && propertyDto.Dimension.AreaUnitId != Guid.Empty)
                    {
                        var dimension = (await _masterAreaUnitRepo.ListAsync(new GetMasterAreaUnitByAreaIdSpec(propertyDto?.Dimension?.AreaUnitId ?? Guid.Empty), cancellationToken)).FirstOrDefault();
                        propertyDto.Dimension.Unit = dimension?.Unit ?? null;
                    }
                    if (propertyDtos != null)
                    {
                        resultPropertyDtos.Add(propertyDto);
                    }
                }
            }
            return new PagedResponse<ViewPropertyDto, PropertyCountDto>(resultPropertyDtos, totalCount);
        }
    }
}
