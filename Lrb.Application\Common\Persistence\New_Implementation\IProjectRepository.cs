﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Lrb.Application.Project.Web.Requests;
namespace Lrb.Application.Common.Persistence.New_Implementation
{
    public interface IProjectRepository : IEFRepository<Lrb.Domain.Entities.Project>
    {
        //public Task<bool> AddAsync(TempProjects project);

        public Task<bool> AddAsync(Lrb.Domain.Entities.Project project);
        Task<IEnumerable<Lrb.Domain.Entities.Project>> GetAllProjectsForWebNewAsync(GetAllProjectRequest filter, List<Guid> projectIds);
        Task<int> GetAllProjectsCountForWebNewAsync(Lrb.Application.Project.Web.Requests.GetAllProjectRequest request, List<Guid> projectIds);
        Task<IEnumerable<Lrb.Domain.Entities.Project>> GetAllProjectsForMobileNewAsync(Lrb.Application.Project.Mobile.GetAllProjectRequest request, List<Guid> projectIds);
        Task<int> GetAllProjectsCountForMobileNewAsync(Lrb.Application.Project.Mobile.GetAllProjectRequest request, List<Guid> projectIds);
        Task<IEnumerable<Lrb.Domain.Entities.Project>> GetAllProjectsExportForWebNewAsync(RunAWSBatchForExportProjectsRequest request, List<Guid> projectIds);
        Task<IEnumerable<Lrb.Domain.Entities.Project>> GetAnonymousProjectsForWebAsync(GetAllProjectAnonymousRequest request);
        Task<int> GetAnonymousProjectsCountForWebAsync(GetAllProjectAnonymousRequest request);
        Task<IEnumerable<Lrb.Domain.Entities.Project>> GetAllProjectsForMobileNewV2Async(Lrb.Application.Project.Mobile.GetAllProjectRequest request, List<Guid> projectIds);

    }
}
