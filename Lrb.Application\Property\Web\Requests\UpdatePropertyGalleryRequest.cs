﻿using Lrb.Application.Property.Web.Dtos;
using Lrb.Domain.Entities.MasterData;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Property.Web.Requests
{
    public class UpdatePropertyGalleryRequest : IRequest<Response<Guid>>
    {
        public Guid PropertyId { get; set; }
        public Dictionary<string, List<PropertyVideoGallaryDto>>? ImageUrls { get; set; }
    }
    public class CreatePropertyGalleryRequestHandler : IRequestHandler<UpdatePropertyGalleryRequest, Response<Guid>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.Property> _propertyRepo;
        private readonly IRepositoryWithEvents<PropertyGallery> _propertyGalleryRepo;

        public CreatePropertyGalleryRequestHandler(
            IRepositoryWithEvents<Domain.Entities.Property> propertyRepo,
            IRepositoryWithEvents<PropertyGallery> propertyGallaryRepo
            )
        {
            _propertyRepo = propertyRepo;
            _propertyGalleryRepo = propertyGallaryRepo;
        }
        public async Task<Response<Guid>> Handle(UpdatePropertyGalleryRequest request, CancellationToken cancellationToken)
        {
            var property = await _propertyRepo.GetByIdAsync(request.PropertyId, cancellationToken);
            if (property == null) { throw new NotFoundException("No property found by the id."); }

            if(request?.ImageUrls?.Any()?? false)
            {
                var propertyGalleries = await _propertyGalleryRepo.ListAsync(new GalleryByPropertyIdSpec(property.Id), cancellationToken);
                foreach (var propertyGallery in propertyGalleries)
                {
                    await _propertyGalleryRepo.SoftDeleteAsync(propertyGallery, cancellationToken);
                }
                foreach (var keyValuePair in request.ImageUrls)
                {
                    foreach (var imagePath in keyValuePair.Value)
                    {
                        if (!string.IsNullOrWhiteSpace(imagePath.ImageFilePath) && !string.IsNullOrWhiteSpace(keyValuePair.Key))
                        {
                            PropertyGallery propertyGallery = new()
                            {
                                ImageKey = keyValuePair.Key,
                                ImageFilePath = imagePath.ImageFilePath,
                                PropertyId = property.Id,
                                IsCoverImage = imagePath.IsCoverImage,
                                Name = imagePath.Name,
                                GalleryType = imagePath?.GalleryType ?? PropertyGalleryType.None,
                                OrderRank = imagePath?.OrderRank,
                                Height = imagePath?.Height,
                                Width = imagePath?.Width,
                            };
                            await _propertyGalleryRepo.AddAsync(propertyGallery, cancellationToken);
                        }
                    }
                }
            }
            else
            {
                var propertyGalleries = await _propertyGalleryRepo.ListAsync(new GalleryByPropertyIdSpec(property.Id), cancellationToken);
                foreach(var  propertyGallery in propertyGalleries)
                {
                    await _propertyGalleryRepo.SoftDeleteAsync(propertyGallery, cancellationToken);
                }
            }
            return new(property.Id);

        }
    }
}
