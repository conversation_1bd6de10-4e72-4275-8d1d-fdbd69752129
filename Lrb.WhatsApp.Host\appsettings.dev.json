﻿{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "Serilog": {
    "Using": [
      "AWS.Logger.SeriLog"
    ],
    "LogGroup": "/aws/lightsail/container/lrb-dev-whatsappapi",
    "Region": "ap-south-1",
    "Enrich": [
      "FromLogContext",
      "WithMachineName",
      "WithProcessId",
      "WithThreadId",
      "WithHangfireContext"
    ],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Hangfire": "Warning",
        "Microsoft": "Error",
        "Microsoft.Hosting.Lifetime": "Information",
        "System": "Information"
      }
    },
    "Properties": {
      "Application": "Lrb.WhatsApp.Host"
    },
    "WriteTo": [
      {
        "Args": {
          "path": "Logs/logs.json",
          "formatter": "Serilog.Formatting.Json.JsonFormatter, Serilog",
          "rollingInterval": "Day",
          "restrictedToMinimumLevel": "Information",
          "retainedFileCountLimit": 5
        },
        "Name": "File"
      },
      {
        "Name": "Seq",
        "Args": {
          "serverUrl": "http://localhost:5341"
        }
      },
      {
        "Name": "Hangfire"
      },
      {
        "Name": "Elasticsearch",
        "Args": {
          "nodeUris": "http://localhost:9200;",
          "indexFormat": "Lrb.WhatsApp.Host-logs-{0:yyyy.MM}",
          "numberOfShards": 2,
          "numberOfReplicas": 1,
          "restrictedToMinimumLevel": "Information"
        }
      }
    ]
  },
  "DatabaseSettings": {
    "DefaultConnection": "Host=lrb-dev.postgres.database.azure.com;Port=5432;Database=leadratBlackApp;Username=dbmasteruser;Password=************`H9hTJK5ojM)C=$C6w,0;Pooling=true;MinPoolSize=3;MaxPoolSize=1000;"
  },

  "AppUrls": {
    "WebApi": "https://connect.leadratd.com/"
  },
  "LeadratApis": {
    "WhatsAppApi": "https://connect.leadratd.com/api/v1/integration/whatsapp",
    "WaPushNotification": "https://lrb-mobile-api-dev.azurewebsites.net/api/v1/utility/wa-notification"
  },

  "CosmosSettings": {
    "EndpointUri": "https://lrb-prd-whatsapp.documents.azure.com:443/",
    "PrimaryKey": "****************************************************************************************",
    "ContainerName": "lrb-prd"
  },

  "CorsSettings": {
    "Angular": "http://localhost:4200",
    "Blazor": "https://localhost:5002;https://www.mydomain.my",
    "React": "http://localhost:3000"
  }

}