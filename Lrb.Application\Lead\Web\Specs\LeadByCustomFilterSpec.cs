﻿using Lrb.Application.Utils;
using Microsoft.EntityFrameworkCore;

namespace Lrb.Application.Lead.Web
{
    public class LeadByCustomFilterSpec : EntitiesByPaginationFilterSpec<Domain.Entities.Lead>
    {

        public LeadByCustomFilterSpec(GetAllLeadsRequest filter, ICurrentUser currentUser, List<Guid> propertiesLeadIds, List<Guid> projectLeadIds) : base(filter)
        {
            var subIds = currentUser.GetSubordinateIds() ?? new();
            var reporteeIds = subIds.Where(i => i != currentUser.GetUserId() && i != Guid.Empty);
            var selfWithReporteeIds = subIds.Where(i => i != Guid.Empty);
            //by default we are using  OrderByDescending here, It should get it done by filters later.
            Query.Where(i => !i.IsDeleted).Where(i => !i.IsArchived)
                .Include(i => i.TagInfo)
                //.Include(i => i.Status)
                .Include(i => i.Enquiries)
                .Include(i => i.Enquiries)
                    //.ThenInclude(i => i.Address)
                    .ThenInclude(i => i.Addresses)
                        .ThenInclude(i => i.Location)
                            .ThenInclude(i => i.Zone)
                .Include(i => i.Enquiries)
                    //.ThenInclude(i => i.Address)
                    .ThenInclude(i => i.Addresses)
                        .ThenInclude(i => i.Location)
                            .ThenInclude(i => i.City)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyType)
                .Include(i => i.Appointments)
                .ThenInclude(i => i.Location)
                .Include(i => i.Projects)
                .Include(i => i.Projects)
                .Include(i => i.Properties)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyTypes)
                .OrderByDescending(i => i.LastModifiedOn);

            switch (filter.LeadVisibility)
            {
                case BaseLeadVisibility.SelfWithReportee:
                    Query.Where(i => selfWithReporteeIds.Contains(i.AssignTo));
                    break;
                case BaseLeadVisibility.Self:
                    Query.Where(i => i.AssignTo == currentUser.GetUserId());
                    break;
                case BaseLeadVisibility.Reportee:
                    Query.Where(i => reporteeIds.Contains(i.AssignTo));
                    break;
                case BaseLeadVisibility.UnassignLead:
                    Query.Where(i => i.AssignTo == Guid.Empty);
                    break;
                case BaseLeadVisibility.ReEnquired:
                    Query.Where(i => i.IsReEnquired == true);
                    break;
                default:
                    break;
            }
            if (filter.DateType != null && filter.FromDate != null && filter.FromDate.Value != default && filter.ToDate != null && filter.ToDate.Value != default)
            {
                switch (filter.DateType)
                {
                    case DateType.ReceivedDate:
                        Query.Where(i => i.CreatedOn.Date >= filter.FromDate.Value.ToUniversalTime().Date && i.CreatedOn.Date <= filter.ToDate.Value.ToUniversalTime().Date);
                        break;
                    case DateType.ScheduledDate:
                        Query.Where(i => i.ScheduledDate != null && i.ScheduledDate.Value.Date >= filter.FromDate.Value.ToUniversalTime().Date && i.ScheduledDate.Value.Date <= filter.ToDate.Value.ToUniversalTime().Date);
                        break;
                    case DateType.ModifiedDate:
                        Query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value.Date >= filter.FromDate.Value.ToUniversalTime().Date && i.LastModifiedOn.Value.Date <= filter.ToDate.Value.ToUniversalTime().Date);
                        break;
                    case DateType.All:
                        Query.Where(i => (i.CreatedOn.Date >= filter.FromDate.Value.ToUniversalTime().Date && i.CreatedOn.Date <= filter.ToDate.Value.ToUniversalTime().Date) ||
                                          (i.ScheduledDate != null && i.ScheduledDate.Value.Date >= filter.FromDate.Value.ToUniversalTime().Date && i.ScheduledDate.Value.Date <= filter.ToDate.Value.ToUniversalTime().Date) ||
                                          i.LastModifiedOn != null && i.LastModifiedOn.Value.Date >= filter.FromDate.Value.ToUniversalTime().Date && i.LastModifiedOn.Value.Date <= filter.ToDate.Value.ToUniversalTime().Date);
                        break;
                    default:
                        break;
                }
            }
            //var schuduledStatusIds = new List<Guid>() { Guid.Parse("99a7f794-9046-4a9d-b7e2-e0a2196b98dd"), Guid.Parse("5ae346bc-c695-4af4-8c3b-c8648587fbd6"), Guid.Parse("59647294-09d6-44a2-a346-9de5ba829e04") };
            //switch (filter.FilterType)
            //{
            //    case LeadFilterTypeWeb.New:
            //        Query.Where(i => i.Status != null && i.Status.Status == "new");
            //        break;
            //    #region ScheduleDate based Categories
            //    case LeadFilterTypeWeb.Today:
            //        Query.Where(i => (i.ScheduledDate ?? DateTime.MinValue) >= DateTime.UtcNow.Date.AddMinutes(-330) && (i.ScheduledDate ?? DateTime.MinValue) < DateTime.UtcNow.Date.AddDays(1).AddMinutes(-330))
            //        .Where(i => i.Status == null || schuduledStatusIds.Contains(i.Status.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.Status.Id));
            //        break;
            //    case LeadFilterTypeWeb.Upcoming:
            //        Query.Where(i => (i.ScheduledDate ?? DateTime.MinValue) >= DateTime.UtcNow.Date.AddDays(1).AddMinutes(-330))
            //        .Where(i => i.Status == null || schuduledStatusIds.Contains(i.Status.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.Status.Id));
            //        break;
            //    case LeadFilterTypeWeb.Overdue:
            //        Query.Where(i => (i.ScheduledDate ?? DateTime.MinValue) < DateTime.UtcNow.Date.AddMinutes(-330))
            //        .Where(i => i.Status != null && (schuduledStatusIds.Contains(i.Status.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.Status.Id)));
            //        break;
            //    #endregion
            //    case LeadFilterTypeWeb.NotInterested:
            //        Query.Where(i => i.Status != null && i.Status.BaseId == Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
            //        break;
            //    case LeadFilterTypeWeb.Dropped:
            //        Query.Where(i => i.Status != null && i.Status.BaseId == Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"));
            //        break;
            //    case LeadFilterTypeWeb.Escalated:
            //        Query.Where(i => i.TagInfo != null && i.TagInfo.IsEscalated)
            //        .Where(i => i.Status == null || i.Status.BaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
            //        .Where(i => i.Status == null || i.Status.Status != "booked")
            //        .Where(i => !new List<string>() { "different_requirements", "different_location", "unmatched_budget" }.Contains(i.Status.Status));
            //        break;
            //    case LeadFilterTypeWeb.Pending:
            //        Query.Where(i => i.Status != null && i.Status.Status == "pending");
            //        break;
            //    case LeadFilterTypeWeb.Booked:
            //        Query.Where(i => i.Status != null && i.Status.Status == "booked");
            //        break;
            //    case LeadFilterTypeWeb.All:
            //        var Statuses = new List<string>() { "not_interested", "dropped" };
            //        var StatusesIds = new List<Guid?>() { Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"), Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99") };
            //        Query.Where(i => i.Status != null && !Statuses.Contains(i.Status.Status) && !StatusesIds.Contains(i.Status.BaseId));
            //        break;
            //    default:
            //        break;
            //}
            if (!string.IsNullOrWhiteSpace(filter.LeadSearch))
            {
                Query.Where(i => i.ContactNo.Contains(filter.LeadSearch) || i.Name.ToLower().Contains(filter.LeadSearch.ToLower()));
            }
            if (filter.Source != null && filter.Source.Any())
            {
                Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => filter.Source.Contains(i.LeadSource)));
            }
            if (filter.EnquiredFor != null && filter.EnquiredFor.Any())
            {
                //Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => filter.EnquiredFor.Contains(i.EnquiredFor)));
                Query.Where(i => i.Enquiries != null && i.Enquiries.Any(e => EF.Functions.JsonContains(e.EnquiryTypes, filter.EnquiredFor)));
            }
            if (filter.AssignTo != null && filter.AssignTo.Any())
            {
                Query.Where(i => filter.AssignTo.Contains(i.AssignTo));
            }
            if (filter.NoOfBHKs != null && filter.NoOfBHKs.Any())
            {
                //Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => filter.NoOfBHKs.Contains(i.NoOfBHK)));
                Query.Where(i => i.Enquiries != null && i.Enquiries.Any(e => EF.Functions.JsonContains(e.BHKs, filter.NoOfBHKs)));
            }
            if (filter.BHKTypes != null && filter.BHKTypes.Any())
            {
                //Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => filter.BHKTypes.Contains(i.BHKType)));
                Query.Where(i => i.Enquiries != null && i.Enquiries.Any(e => EF.Functions.JsonContains(e.BHKTypes, filter.BHKTypes)));
            }
            if (filter.PropertyType != null && filter.PropertyType.Any())
            {
                 Query.Where(i => i.Enquiries != null  && i.Enquiries.Any(e => e.PropertyTypes != null && e.PropertyTypes.Any(i => filter.PropertyType.Contains(i.BaseId ?? Guid.Empty))));

            }
            if (filter.PropertySubType != null && filter.PropertySubType.Any())
            {
                Query.Where(i => i.Enquiries != null && i.Enquiries.Any(e => e.PropertyTypes != null&& e.PropertyTypes.Any(i => filter.PropertySubType.Contains(i.Id))));

            }
            //if (filter.StatusIds != null && filter.StatusIds.Any())
            //{
            //    Query.Where(i => i.Status != null && filter.StatusIds.Contains(i.Status.Id));
            //}
            if (filter.Properties != null && filter.Properties.Any() && propertiesLeadIds.Any())
            {
                Query.Where(i => propertiesLeadIds.Contains(i.Id));
            }
            if (filter.Projects != null && filter.Projects.Any() && projectLeadIds.Any())
            {
                Query.Where(i => projectLeadIds.Contains(i.Id));
            }
            if (filter.Budget != null && filter.Budget.Any())
            {
                foreach (var budget in filter.Budget)
                {
                    switch (budget)
                    {
                        case Budget.UpToTenLakhs:
                            Query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget <= 1000000));
                            break;
                        case Budget.TenToTwentyLakhs:
                            Query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 1000000 && j.UpperBudget <= 2000000));
                            break;
                        case Budget.TwentyToThirtyLakhs:
                            Query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 2000000 && j.UpperBudget <= 3000000));
                            break;
                        case Budget.ThirtyToFourtyLakhs:
                            Query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 3000000 && j.UpperBudget <= 4000000));
                            break;
                        case Budget.FourtyToFiftyLakhs:
                            Query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 4000000 && j.UpperBudget <= 5000000));
                            break;
                        case Budget.FiftyToOneCrore:
                            Query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 5000000 && j.UpperBudget <= 10000000));
                            break;
                        case Budget.MoreThanOneCrore:
                            Query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 10000000));
                            break;
                    }
                }
            }
            if (filter.LeadTags != null && filter.LeadTags.Any())
            {
                foreach (var tag in filter.LeadTags)
                {
                    switch (tag)
                    {
                        case LeadTagEnum.IsHot:
                            Query.Where(i => i.TagInfo != null && i.TagInfo.IsHotLead);
                            break;
                        case LeadTagEnum.IsAboutToConvert:
                            Query.Where(i => i.TagInfo != null && i.TagInfo.IsAboutToConvert);
                            break;
                        case LeadTagEnum.IsEscalated:
                            Query.Where(i => i.TagInfo != null && i.TagInfo.IsEscalated);
                            break;
                        case LeadTagEnum.IsIntegrationLead:
                            Query.Where(i => i.TagInfo != null && i.TagInfo.IsIntegrationLead);
                            break;
                        case LeadTagEnum.IsHighlighted:
                            Query.Where(i => i.TagInfo != null && i.TagInfo.IsHighlighted);
                            break;
                        case LeadTagEnum.IsWarmLead:
                            Query.Where(i => i.TagInfo != null && i.TagInfo.IsWarmLead);
                            break;
                        case LeadTagEnum.IsColdLead:
                            Query.Where(i => i.TagInfo != null && i.TagInfo.IsColdLead);
                            break;
                    }
                }
            }

        }
        public LeadByCustomFilterSpec(GetAllLeadsRequest filter, IEnumerable<Guid> subIds, Guid userId, List<Guid> propertiesLeadIds, List<Guid> projectLeadIds, List<Guid> leadHistoryIds) : base(filter)
        {
            //var subIds = currentUser.GetSubordinateIds() ?? new();
            var reporteeIds = subIds.Where(i => i != userId && i != Guid.Empty);
            var selfWithReporteeIds = subIds.Where(i => i != Guid.Empty);
            //by default we are using  OrderByDescending here, It should get it done by filters later.
            Query.Where(i => !i.IsDeleted).Where(i => !i.IsArchived)
                .Include(i => i.TagInfo)
                //.Include(i => i.Status)
                .Include(i => i.Enquiries)
                .Include(i => i.Enquiries)
                    //.ThenInclude(i => i.Address)
                    .ThenInclude(i => i.Addresses)
                        .ThenInclude(i => i.Location)
                            .ThenInclude(i => i.Zone)
                .Include(i => i.Enquiries)
                    //.ThenInclude(i => i.Address)
                    .ThenInclude(i => i.Addresses)
                        .ThenInclude(i => i.Location)
                            .ThenInclude(i => i.City)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyType)
                .Include(i => i.Appointments)
                .ThenInclude(i => i.Location)
                .Include(i => i.Projects)
                .Include(i => i.Properties)
                .Include(i => i.Communications)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyTypes)
                .OrderByDescending(i => i.LastModifiedOn);

            if (leadHistoryIds != null && leadHistoryIds.Any())
            {
                Query.Where(i => leadHistoryIds.Contains(i.Id));
            }

            switch (filter.LeadVisibility)
            {
                case BaseLeadVisibility.SelfWithReportee:
                    Query.Where(i => selfWithReporteeIds.Contains(i.AssignTo));
                    break;
                case BaseLeadVisibility.Self:
                    Query.Where(i => i.AssignTo == userId);
                    break;
                case BaseLeadVisibility.Reportee:
                    Query.Where(i => reporteeIds.Contains(i.AssignTo));
                    break;
                case BaseLeadVisibility.UnassignLead:
                    Query.Where(i => i.AssignTo == Guid.Empty);
                    break;
                case BaseLeadVisibility.ReEnquired:
                    Query.Where(i => i.IsReEnquired == true);
                    break;
                default:
                    break;
            }
            if (filter.DateType != null && filter.FromDate != null && filter.FromDate.Value != default && filter.ToDate != null && filter.ToDate.Value != default)
            {
                filter.FromDate = filter.FromDate.Value.ConvertFromDateToUtc();
                filter.ToDate = filter.ToDate.Value.ConvertToDateToUtc();
                switch (filter.DateType)
                {
                    case DateType.ReceivedDate:
                        Query.Where(i => i.CreatedOn >= filter.FromDate.Value && i.CreatedOn <= filter.ToDate.Value);
                        break;
                    case DateType.ScheduledDate:
                        Query.Where(i => i.ScheduledDate != null && i.ScheduledDate.Value >= filter.FromDate.Value && i.ScheduledDate.Value <= filter.ToDate.Value);
                        break;
                    case DateType.ModifiedDate:
                        Query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value && i.LastModifiedOn.Value <= filter.ToDate.Value);
                        break;
                    case DateType.All:
                        Query.Where(i => (i.CreatedOn >= filter.FromDate.Value && i.CreatedOn <= filter.ToDate.Value) ||
                                          (i.ScheduledDate != null && i.ScheduledDate.Value >= filter.FromDate.Value && i.ScheduledDate.Value <= filter.ToDate.Value) ||
                                          i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value && i.LastModifiedOn.Value <= filter.ToDate.Value);
                        break;
                    default:
                        break;
                }
            }

            //var schuduledStatusIds = new List<Guid>() { Guid.Parse("99a7f794-9046-4a9d-b7e2-e0a2196b98dd"), Guid.Parse("5ae346bc-c695-4af4-8c3b-c8648587fbd6"), Guid.Parse("59647294-09d6-44a2-a346-9de5ba829e04") };
            //switch (filter.FilterType)
            //{
            //    case LeadFilterTypeWeb.New:
            //        Query.Where(i => i.Status != null && i.Status.Status == "new");
            //        break;
            //    #region ScheduleDate based Categories
            //    case LeadFilterTypeWeb.Today:
            //        Query.Where(i => (i.ScheduledDate ?? DateTime.MinValue) >= DateTime.UtcNow.Date.AddMinutes(-330) && (i.ScheduledDate ?? DateTime.MinValue) < DateTime.UtcNow.Date.AddDays(1).AddMinutes(-330))
            //        .Where(i => i.Status == null || schuduledStatusIds.Contains(i.Status.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.Status.Id));
            //        break;
            //    case LeadFilterTypeWeb.Upcoming:
            //        Query.Where(i => (i.ScheduledDate ?? DateTime.MinValue) >= DateTime.UtcNow.Date.AddDays(1).AddMinutes(-330))
            //        .Where(i => i.Status == null || schuduledStatusIds.Contains(i.Status.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.Status.Id));
            //        break;
            //    case LeadFilterTypeWeb.Overdue:
            //        Query.Where(i => (i.ScheduledDate ?? DateTime.MinValue) < DateTime.UtcNow.Date.AddMinutes(-330))
            //        .Where(i => i.Status != null && (schuduledStatusIds.Contains(i.Status.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.Status.Id)));
            //        break;
            //    #endregion
            //    case LeadFilterTypeWeb.NotInterested:
            //        Query.Where(i => i.Status != null && i.Status.BaseId == Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
            //        break;
            //    case LeadFilterTypeWeb.Dropped:
            //        Query.Where(i => i.Status != null && i.Status.BaseId == Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"));
            //        break;
            //    case LeadFilterTypeWeb.Escalated:
            //        Query.Where(i => i.TagInfo != null && i.TagInfo.IsEscalated)
            //        .Where(i => i.Status == null || i.Status.BaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
            //        .Where(i => i.Status == null || i.Status.Status != "booked")
            //        .Where(i => !new List<string>() { "different_requirements", "different_location", "unmatched_budget" }.Contains(i.Status.Status));
            //        break;
            //    case LeadFilterTypeWeb.Pending:
            //        Query.Where(i => i.Status != null && i.Status.Status == "pending");
            //        break;
            //    case LeadFilterTypeWeb.Booked:
            //        Query.Where(i => i.Status != null && i.Status.Status == "booked");
            //        break;
            //    case LeadFilterTypeWeb.All:
            //        var Statuses = new List<string>() { "not_interested", "dropped" };
            //        var StatusesIds = new List<Guid?>() { Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"), Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99") };
            //        Query.Where(i => i.Status != null && !Statuses.Contains(i.Status.Status) && !StatusesIds.Contains(i.Status.BaseId));
            //        break;
            //    default:
            //        break;
            //}
            if (!string.IsNullOrWhiteSpace(filter.LeadSearch))
            {
                Query.Where(i => i.ContactNo.Contains(filter.LeadSearch) || i.Name.ToLower().Contains(filter.LeadSearch.ToLower()));
            }
            if (filter.Source != null && filter.Source.Any())
            {
                Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => filter.Source.Contains(i.LeadSource)));
            }
            if (filter.EnquiredFor != null && filter.EnquiredFor.Any())
            {
                //Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => filter.EnquiredFor.Contains(i.EnquiredFor)));
                Query.Where(i => i.Enquiries != null && i.Enquiries.Any(e => EF.Functions.JsonContains(e.EnquiryTypes, filter.EnquiredFor)));
            }
            if (filter.AssignTo != null && filter.AssignTo.Any())
            {
                Query.Where(i => filter.AssignTo.Contains(i.AssignTo));
            }
            if (filter.NoOfBHKs != null && filter.NoOfBHKs.Any())
            {
                //Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => filter.NoOfBHKs.Contains(i.NoOfBHKs)));
                Query.Where(i => i.Enquiries != null && i.Enquiries.Any(e => EF.Functions.JsonContains(e.BHKs, filter.NoOfBHKs)));
            }
            if (filter.BHKTypes != null && filter.BHKTypes.Any())
            {
                //Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => filter.BHKTypes.Contains(i.BHKType)));
                Query.Where(i => i.Enquiries != null && i.Enquiries.Any(e => EF.Functions.JsonContains(e.BHKTypes, filter.BHKTypes)));
            }
            if (filter.PropertyType != null && filter.PropertyType.Any())
            {
                Query.Where(i => i.Enquiries.Any(e =>e.PropertyTypes!=null&& e.PropertyTypes.Any(i => filter.PropertyType.Contains(i.BaseId ?? Guid.Empty))));

            }
            if (filter.PropertySubType != null && filter.PropertySubType.Any())
            {
                Query.Where(i => i.Enquiries.Any(e => e.PropertyTypes != null && e.PropertyTypes.Any(i => filter.PropertySubType.Contains(i.Id))));
            }
            //if (filter.StatusIds != null && filter.StatusIds.Any())
            //{
            //    Query.Where(i => i.Status != null && filter.StatusIds.Contains(i.Status.Id));
            //}
            if (filter.Properties != null && filter.Properties.Any() && propertiesLeadIds.Any())
            {
                Query.Where(i => propertiesLeadIds.Contains(i.Id));
            }
            if (filter.Projects != null && filter.Projects.Any() && projectLeadIds.Any())
            {
                Query.Where(i => projectLeadIds.Contains(i.Id));
            }
            if (filter.Budget != null && filter.Budget.Any())
            {
                foreach (var budget in filter.Budget)
                {
                    switch (budget)
                    {
                        case Budget.UpToTenLakhs:
                            Query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget <= 1000000));
                            break;
                        case Budget.TenToTwentyLakhs:
                            Query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 1000000 && j.UpperBudget <= 2000000));
                            break;
                        case Budget.TwentyToThirtyLakhs:
                            Query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 2000000 && j.UpperBudget <= 3000000));
                            break;
                        case Budget.ThirtyToFourtyLakhs:
                            Query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 3000000 && j.UpperBudget <= 4000000));
                            break;
                        case Budget.FourtyToFiftyLakhs:
                            Query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 4000000 && j.UpperBudget <= 5000000));
                            break;
                        case Budget.FiftyToOneCrore:
                            Query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 5000000 && j.UpperBudget <= 10000000));
                            break;
                        case Budget.MoreThanOneCrore:
                            Query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 10000000));
                            break;
                    }
                }
            }
            if (filter.LeadTags != null && filter.LeadTags.Any())
            {
                foreach (var tag in filter.LeadTags)
                {
                    switch (tag)
                    {
                        case LeadTagEnum.IsHot:
                            Query.Where(i => i.TagInfo != null && i.TagInfo.IsHotLead);
                            break;
                        case LeadTagEnum.IsAboutToConvert:
                            Query.Where(i => i.TagInfo != null && i.TagInfo.IsAboutToConvert);
                            break;
                        case LeadTagEnum.IsEscalated:
                            Query.Where(i => i.TagInfo != null && i.TagInfo.IsEscalated);
                            break;
                        case LeadTagEnum.IsIntegrationLead:
                            Query.Where(i => i.TagInfo != null && i.TagInfo.IsIntegrationLead);
                            break;
                        case LeadTagEnum.IsHighlighted:
                            Query.Where(i => i.TagInfo != null && i.TagInfo.IsHighlighted);
                            break;
                        case LeadTagEnum.IsWarmLead:
                            Query.Where(i => i.TagInfo != null && i.TagInfo.IsWarmLead);
                            break;
                        case LeadTagEnum.IsColdLead:
                            Query.Where(i => i.TagInfo != null && i.TagInfo.IsColdLead);
                            break;
                    }
                }
            }
            if (filter.Locations != null && filter.Locations.Any())
            {
                filter.Locations = filter.Locations.ConvertAll(i => i.Replace(",", "").ToLower().Trim().Replace(" ", "")).ToList();
                /*Query.Where(i => i.Enquiries.Any(i => filter.Locations.Contains((i.Address.SubLocality + "" +
                   i.Address.Locality + "" +
                   i.Address.District + "" +
                   i.Address.City + "" +
                   i.Address.State + "" +
                   i.Address.Country + "" +
                   i.Address.PostalCode + "").ToLower().Trim().Replace(" ", ""))));*/
                Query.Where(i => i.Enquiries.Any(i => i.Addresses.Where(j => filter.Locations.Contains((j.SubLocality + "" +
               j.Locality + "" +
               j.District + "" +
               j.City + "" +
               j.State + "" +
               j.Country + "" +
               j.PostalCode + "").ToLower().Trim())).Any()));
            }
            if (filter.MinBudget != null && filter.MaxBudget != null)
            {
                Query.Where(i => i.Enquiries.Any(i => i.UpperBudget >= filter.MinBudget && i.UpperBudget <= filter.MaxBudget));
            }
        }
    }


    public class LeadCountByCustomFilterSpec : Specification<Domain.Entities.Lead>
    {
        public LeadCountByCustomFilterSpec(GetAllLeadsRequest filter, ICurrentUser currentUser, List<Guid> propertiesLeadIds, List<Guid> projectLeadIds)
        {
            var subIds = currentUser.GetSubordinateIds() ?? new();
            var reporteeIds = subIds.Where(i => i != currentUser.GetUserId() && i != Guid.Empty);
            var selfWithReporteeIds = subIds.Where(i => i != Guid.Empty);
            //by default we are using  OrderByDescending here, It should get it done by filters later.
            Query.Where(i => !i.IsDeleted)
                .Where(i => !i.IsArchived)
                .Include(i => i.TagInfo)
                //.Include(i => i.Status)
                .Include(i => i.Enquiries)
                .Include(i => i.Enquiries)
                    //.ThenInclude(i => i.Address)
                    .ThenInclude(i => i.Addresses)
                        .ThenInclude(i => i.Location)
                            .ThenInclude(i => i.Zone)
                .Include(i => i.Enquiries)
                    //.ThenInclude(i => i.Address)
                    .ThenInclude(i => i.Addresses)
                        .ThenInclude(i => i.Location)
                            .ThenInclude(i => i.City)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyType)
                .Include(i => i.Appointments)
                .ThenInclude(i => i.Location)
                .Include(i => i.Projects)
                .Include(i => i.Properties)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyTypes)
                .OrderByDescending(i => i.LastModifiedOn);

            switch (filter.LeadVisibility)
            {
                case BaseLeadVisibility.SelfWithReportee:
                    Query.Where(i => selfWithReporteeIds.Contains(i.AssignTo));
                    break;
                case BaseLeadVisibility.Self:
                    Query.Where(i => i.AssignTo == currentUser.GetUserId());
                    break;
                case BaseLeadVisibility.Reportee:
                    Query.Where(i => reporteeIds.Contains(i.AssignTo));
                    break;
                case BaseLeadVisibility.UnassignLead:
                    Query.Where(i => i.AssignTo == Guid.Empty);
                    break;
                case BaseLeadVisibility.ReEnquired:
                    Query.Where(i => i.IsReEnquired == true);
                    break;
                default:
                    break;
            }
            if (filter.DateType != null && filter.FromDate != null && filter.FromDate.Value != default && filter.ToDate != null && filter.ToDate.Value != default)
            {
                filter.FromDate = filter.FromDate.Value.ConvertFromDateToUtc();
                filter.ToDate = filter.ToDate.Value.ConvertToDateToUtc();
                switch (filter.DateType)
                {
                    case DateType.ReceivedDate:
                        Query.Where(i => i.CreatedOn >= filter.FromDate.Value && i.CreatedOn.Date <= filter.ToDate.Value);
                        break;
                    case DateType.ScheduledDate:
                        Query.Where(i => i.ScheduledDate != null && i.ScheduledDate.Value >= filter.FromDate.Value && i.ScheduledDate.Value <= filter.ToDate.Value);
                        break;
                    case DateType.ModifiedDate:
                        Query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value && i.LastModifiedOn.Value <= filter.ToDate.Value);
                        break;
                    case DateType.All:
                        Query.Where(i => (i.CreatedOn >= filter.FromDate.Value && i.CreatedOn <= filter.ToDate.Value) ||
                                          (i.ScheduledDate != null && i.ScheduledDate.Value >= filter.FromDate.Value && i.ScheduledDate.Value <= filter.ToDate.Value) ||
                                          i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value && i.LastModifiedOn.Value <= filter.ToDate.Value);
                        break;
                    default:
                        break;
                }
            }

            //var schuduledStatusIds = new List<Guid>() { Guid.Parse("99a7f794-9046-4a9d-b7e2-e0a2196b98dd"), Guid.Parse("5ae346bc-c695-4af4-8c3b-c8648587fbd6"), Guid.Parse("59647294-09d6-44a2-a346-9de5ba829e04") };
            //switch (filter.FilterType)
            //{
            //    case LeadFilterTypeWeb.New:
            //        Query.Where(i => i.Status != null && i.Status.Status == "new");
            //        break;
            //    #region ScheduleDate based Categories
            //    case LeadFilterTypeWeb.Today:
            //        Query.Where(i => (i.ScheduledDate ?? DateTime.MinValue) >= DateTime.UtcNow.Date.AddMinutes(-330) && (i.ScheduledDate ?? DateTime.MinValue) < DateTime.UtcNow.Date.AddDays(1).AddMinutes(-330))
            //        .Where(i => i.Status == null || schuduledStatusIds.Contains(i.Status.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.Status.Id));
            //        break;
            //    case LeadFilterTypeWeb.Upcoming:
            //        Query.Where(i => (i.ScheduledDate ?? DateTime.MinValue) >= DateTime.UtcNow.Date.AddDays(1).AddMinutes(-330))
            //        .Where(i => i.Status == null || schuduledStatusIds.Contains(i.Status.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.Status.Id));
            //        break;
            //    case LeadFilterTypeWeb.Overdue:
            //        Query.Where(i => (i.ScheduledDate ?? DateTime.MinValue) < DateTime.UtcNow.Date.AddMinutes(-330))
            //        .Where(i => i.Status != null && (schuduledStatusIds.Contains(i.Status.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.Status.Id)));
            //        break;
            //    #endregion
            //    case LeadFilterTypeWeb.NotInterested:
            //        Query.Where(i => i.Status != null && i.Status.BaseId == Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
            //        break;
            //    case LeadFilterTypeWeb.Dropped:
            //        Query.Where(i => i.Status != null && i.Status.BaseId == Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"));
            //        break;
            //    case LeadFilterTypeWeb.Escalated:
            //        Query.Where(i => i.TagInfo != null && i.TagInfo.IsEscalated)
            //        .Where(i => i.Status == null || i.Status.BaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
            //        .Where(i => i.Status == null || i.Status.Status != "booked")
            //        .Where(i => !new List<string>() { "different_requirements", "different_location", "unmatched_budget" }.Contains(i.Status.Status));
            //        break;
            //    case LeadFilterTypeWeb.Pending:
            //        Query.Where(i => i.Status != null && i.Status.Status == "pending");
            //        break;
            //    case LeadFilterTypeWeb.Booked:
            //        Query.Where(i => i.Status != null && i.Status.Status == "booked");
            //        break;
            //    case LeadFilterTypeWeb.All:
            //        var Statuses = new List<string>() { "not_interested", "dropped" };
            //        var StatusesIds = new List<Guid?>() { Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"), Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99") };
            //        Query.Where(i => i.Status != null && !Statuses.Contains(i.Status.Status) && !StatusesIds.Contains(i.Status.BaseId));
            //        break;
            //    default:
            //        break;
            //}
            if (!string.IsNullOrWhiteSpace(filter.LeadSearch))
            {
                Query.Where(i => i.ContactNo.Contains(filter.LeadSearch) || i.Name.ToLower().Contains(filter.LeadSearch.ToLower()));
            }
            if (filter.Source != null && filter.Source.Any())
            {
                Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => filter.Source.Contains(i.LeadSource)));
            }
            if (filter.EnquiredFor != null && filter.EnquiredFor.Any())
            {
                //Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => filter.EnquiredFor.Contains(i.EnquiredFor)));
                Query.Where(i => i.Enquiries != null && i.Enquiries.Any(e => EF.Functions.JsonContains(e.EnquiryTypes, filter.EnquiredFor)));
            }
            if (filter.AssignTo != null && filter.AssignTo.Any())
            {
                Query.Where(i => filter.AssignTo.Contains(i.AssignTo));
            }
            if (filter.NoOfBHKs != null && filter.NoOfBHKs.Any())
            {
                //Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => filter.NoOfBHKs.Contains(i.NoOfBHKs)));
                Query.Where(i => i.Enquiries != null && i.Enquiries.Any(e => EF.Functions.JsonContains(e.BHKs, filter.NoOfBHKs)));

            }
            if (filter.BHKTypes != null && filter.BHKTypes.Any())
            {
                //Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => filter.BHKTypes.Contains(i.BHKType)));
                Query.Where(i => i.Enquiries != null && i.Enquiries.Any(e => EF.Functions.JsonContains(e.BHKTypes, filter.BHKTypes)));
            }
            if (filter.PropertyType != null && filter.PropertyType.Any())
            {
                Query.Where(i => i.Enquiries.Any(e => e.PropertyTypes != null && e.PropertyTypes.Any(i => filter.PropertyType.Contains(i.BaseId ?? Guid.Empty))));
            }
            if (filter.PropertySubType != null && filter.PropertySubType.Any())
            {
                Query.Where(i => i.Enquiries.Any(e => e.PropertyTypes != null && e.PropertyTypes.Any(i => filter.PropertySubType.Contains(i.Id))));
            }
            //if (filter.StatusIds != null && filter.StatusIds.Any())
            //{
            //    Query.Where(i => i.Status != null && filter.StatusIds.Contains(i.Status.Id));
            //}
            if (filter.Properties != null && filter.Properties.Any() && propertiesLeadIds.Any())
            {
                Query.Where(i => propertiesLeadIds.Contains(i.Id));
            }
            if (filter.Projects != null && filter.Projects.Any() && projectLeadIds.Any())
            {
                Query.Where(i => projectLeadIds.Contains(i.Id));
            }
            if (filter.Budget != null && filter.Budget.Any())
            {
                foreach (var budget in filter.Budget)
                {
                    switch (budget)
                    {
                        case Budget.UpToTenLakhs:
                            Query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget <= 1000000));
                            break;
                        case Budget.TenToTwentyLakhs:
                            Query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 1000000 && j.UpperBudget <= 2000000));
                            break;
                        case Budget.TwentyToThirtyLakhs:
                            Query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 2000000 && j.UpperBudget <= 3000000));
                            break;
                        case Budget.ThirtyToFourtyLakhs:
                            Query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 3000000 && j.UpperBudget <= 4000000));
                            break;
                        case Budget.FourtyToFiftyLakhs:
                            Query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 4000000 && j.UpperBudget <= 5000000));
                            break;
                        case Budget.FiftyToOneCrore:
                            Query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 5000000 && j.UpperBudget <= 10000000));
                            break;
                        case Budget.MoreThanOneCrore:
                            Query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 10000000));
                            break;
                    }
                }
            }
            if (filter.LeadTags != null && filter.LeadTags.Any())
            {
                foreach (var tag in filter.LeadTags)
                {
                    switch (tag)
                    {
                        case LeadTagEnum.IsHot:
                            Query.Where(i => i.TagInfo != null && i.TagInfo.IsHotLead);
                            break;
                        case LeadTagEnum.IsAboutToConvert:
                            Query.Where(i => i.TagInfo != null && i.TagInfo.IsAboutToConvert);
                            break;
                        case LeadTagEnum.IsEscalated:
                            Query.Where(i => i.TagInfo != null && i.TagInfo.IsEscalated);
                            break;
                        case LeadTagEnum.IsIntegrationLead:
                            Query.Where(i => i.TagInfo != null && i.TagInfo.IsIntegrationLead);
                            break;
                        case LeadTagEnum.IsHighlighted:
                            Query.Where(i => i.TagInfo != null && i.TagInfo.IsHighlighted);
                            break;
                        case LeadTagEnum.IsWarmLead:
                            Query.Where(i => i.TagInfo != null && i.TagInfo.IsWarmLead);
                            break;
                        case LeadTagEnum.IsColdLead:
                            Query.Where(i => i.TagInfo != null && i.TagInfo.IsColdLead);
                            break;
                    }
                }
            }

        }
        public LeadCountByCustomFilterSpec(GetAllLeadsRequest filter, IEnumerable<Guid> subIds, Guid userId, List<Guid> propertiesLeadIds, List<Guid> projectLeadIds, List<Guid>? leadHistoryIds = null)
        {
            //var subIds = currentUser.GetSubordinateIds() ?? new();
            var reporteeIds = subIds.Where(i => i != userId && i != Guid.Empty);
            var selfWithReporteeIds = subIds.Where(i => i != Guid.Empty);
            //by default we are using  OrderByDescending here, It should get it done by filters later.
            Query.Where(i => !i.IsDeleted)
                .Where(i => !i.IsArchived)
                .Include(i => i.TagInfo)
                //.Include(i => i.Status)
                .Include(i => i.Enquiries)
                .Include(i => i.Enquiries)
                    //.ThenInclude(i => i.Address)
                    .ThenInclude(i => i.Addresses)
                        .ThenInclude(i => i.Location)
                            .ThenInclude(i => i.Zone)
                .Include(i => i.Enquiries)
                    //.ThenInclude(i => i.Address)
                    .ThenInclude(i => i.Addresses)
                        .ThenInclude(i => i.Location)
                            .ThenInclude(i => i.City)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyType)
                .Include(i => i.Appointments)
                .ThenInclude(i => i.Location)
                .Include(i => i.Projects)
                .Include(i => i.Properties)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyTypes)
                .OrderByDescending(i => i.LastModifiedOn);

            if (leadHistoryIds != null && leadHistoryIds.Any())
            {
                Query.Where(i => leadHistoryIds.Contains(i.Id));
            }

            switch (filter.LeadVisibility)
            {
                case BaseLeadVisibility.SelfWithReportee:
                    Query.Where(i => selfWithReporteeIds.Contains(i.AssignTo));
                    break;
                case BaseLeadVisibility.Self:
                    Query.Where(i => i.AssignTo == userId);
                    break;
                case BaseLeadVisibility.Reportee:
                    Query.Where(i => reporteeIds.Contains(i.AssignTo));
                    break;
                case BaseLeadVisibility.UnassignLead:
                    Query.Where(i => i.AssignTo == Guid.Empty);
                    break;
                case BaseLeadVisibility.ReEnquired:
                    Query.Where(i => i.IsReEnquired == true);
                    break;
                default:
                    break;
            }
            if (filter.DateType != null && filter.FromDate != null && filter.FromDate.Value != default && filter.ToDate != null && filter.ToDate.Value != default)
            {
                filter.FromDate = filter.FromDate.Value.ConvertFromDateToUtc();
                filter.ToDate = filter.ToDate.Value.ConvertToDateToUtc();
                switch (filter.DateType)
                {
                    case DateType.ReceivedDate:
                        Query.Where(i => i.CreatedOn >= filter.FromDate.Value && i.CreatedOn <= filter.ToDate.Value);
                        break;
                    case DateType.ScheduledDate:
                        Query.Where(i => i.ScheduledDate != null && i.ScheduledDate.Value >= filter.FromDate.Value && i.ScheduledDate.Value <= filter.ToDate.Value);
                        break;
                    case DateType.ModifiedDate:
                        Query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value && i.LastModifiedOn.Value <= filter.ToDate.Value);
                        break;
                    case DateType.All:
                        Query.Where(i => (i.CreatedOn >= filter.FromDate.Value && i.CreatedOn <= filter.ToDate.Value) ||
                                          (i.ScheduledDate != null && i.ScheduledDate.Value >= filter.FromDate.Value && i.ScheduledDate.Value <= filter.ToDate.Value) ||
                                          i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value && i.LastModifiedOn.Value <= filter.ToDate.Value);
                        break;
                    default:
                        break;
                }
            }

            //var schuduledStatusIds = new List<Guid>() { Guid.Parse("99a7f794-9046-4a9d-b7e2-e0a2196b98dd"), Guid.Parse("5ae346bc-c695-4af4-8c3b-c8648587fbd6"), Guid.Parse("59647294-09d6-44a2-a346-9de5ba829e04") };
            //switch (filter.FilterType)
            //{
            //    case LeadFilterTypeWeb.New:
            //        Query.Where(i => i.Status != null && i.Status.Status == "new");
            //        break;
            //    #region ScheduleDate based Categories
            //    case LeadFilterTypeWeb.Today:
            //        Query.Where(i => (i.ScheduledDate ?? DateTime.MinValue) >= DateTime.UtcNow.Date.AddMinutes(-330) && (i.ScheduledDate ?? DateTime.MinValue) < DateTime.UtcNow.Date.AddDays(1).AddMinutes(-330))
            //        .Where(i => i.Status == null || schuduledStatusIds.Contains(i.Status.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.Status.Id));
            //        break;
            //    case LeadFilterTypeWeb.Upcoming:
            //        Query.Where(i => (i.ScheduledDate ?? DateTime.MinValue) >= DateTime.UtcNow.Date.AddDays(1).AddMinutes(-330))
            //        .Where(i => i.Status == null || schuduledStatusIds.Contains(i.Status.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.Status.Id));
            //        break;
            //    case LeadFilterTypeWeb.Overdue:
            //        Query.Where(i => (i.ScheduledDate ?? DateTime.MinValue) < DateTime.UtcNow.Date.AddMinutes(-330))
            //        .Where(i => i.Status != null && (schuduledStatusIds.Contains(i.Status.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.Status.Id)));
            //        break;
            //    #endregion
            //    case LeadFilterTypeWeb.NotInterested:
            //        Query.Where(i => i.Status != null && i.Status.BaseId == Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
            //        break;
            //    case LeadFilterTypeWeb.Dropped:
            //        Query.Where(i => i.Status != null && i.Status.BaseId == Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"));
            //        break;
            //    case LeadFilterTypeWeb.Escalated:
            //        Query.Where(i => i.TagInfo != null && i.TagInfo.IsEscalated)
            //        .Where(i => i.Status == null || i.Status.BaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
            //        .Where(i => i.Status == null || i.Status.Status != "booked")
            //        .Where(i => !new List<string>() { "different_requirements", "different_location", "unmatched_budget" }.Contains(i.Status.Status));
            //        break;
            //    case LeadFilterTypeWeb.Pending:
            //        Query.Where(i => i.Status != null && i.Status.Status == "pending");
            //        break;
            //    case LeadFilterTypeWeb.Booked:
            //        Query.Where(i => i.Status != null && i.Status.Status == "booked");
            //        break;
            //    case LeadFilterTypeWeb.All:
            //        var Statuses = new List<string>() { "not_interested", "dropped" };
            //        var StatusesIds = new List<Guid?>() { Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"), Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99") };
            //        Query.Where(i => i.Status != null && !Statuses.Contains(i.Status.Status) && !StatusesIds.Contains(i.Status.BaseId));
            //        break;
            //    default:
            //        break;
            //}
            if (!string.IsNullOrWhiteSpace(filter.LeadSearch))
            {
                Query.Where(i => i.ContactNo.Contains(filter.LeadSearch) || i.Name.ToLower().Contains(filter.LeadSearch.ToLower()));
            }
            if (filter.Source != null && filter.Source.Any())
            {
                Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => filter.Source.Contains(i.LeadSource)));
            }
            if (filter.EnquiredFor != null && filter.EnquiredFor.Any())
            {
                //Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => filter.EnquiredFor.Contains(i.EnquiredFor)));
                Query.Where(i => i.Enquiries != null && i.Enquiries.Any(e => EF.Functions.JsonContains(e.EnquiryTypes, filter.EnquiredFor)));

            }
            if (filter.AssignTo != null && filter.AssignTo.Any())
            {
                Query.Where(i => filter.AssignTo.Contains(i.AssignTo));
            }
            if (filter.NoOfBHKs != null && filter.NoOfBHKs.Any())
            {
                //Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => filter.NoOfBHKs.Contains(i.NoOfBHKs)));
                Query.Where(i => i.Enquiries != null && i.Enquiries.Any(e => EF.Functions.JsonContains(e.BHKs, filter.NoOfBHKs)));
            }
            if (filter.BHKTypes != null && filter.BHKTypes.Any())
            {
                //Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => filter.BHKTypes.Contains(i.BHKType)));
                Query.Where(i => i.Enquiries != null && i.Enquiries.Any(e => EF.Functions.JsonContains(e.BHKTypes, filter.BHKTypes)));
            }
            if (filter.PropertyType != null && filter.PropertyType.Any())
            {
                Query.Where(i => i.Enquiries.Any(e => e.PropertyTypes != null && e.PropertyTypes.Any(i => filter.PropertyType.Contains(i.BaseId ?? Guid.Empty))));
            }
            if (filter.PropertySubType != null && filter.PropertySubType.Any())
            {
                Query.Where(i => i.Enquiries.Any(e => e.PropertyTypes != null && e.PropertyTypes.Any(i => filter.PropertySubType.Contains(i.Id))));
            }
            //if (filter.StatusIds != null && filter.StatusIds.Any())
            //{
            //    Query.Where(i => i.Status != null && filter.StatusIds.Contains(i.Status.Id));
            //}
            if (filter.Properties != null && filter.Properties.Any() && propertiesLeadIds.Any())
            {
                Query.Where(i => propertiesLeadIds.Contains(i.Id));
            }
            if (filter.Projects != null && filter.Projects.Any() && projectLeadIds.Any())
            {
                Query.Where(i => projectLeadIds.Contains(i.Id));
            }
            if (filter.Budget != null && filter.Budget.Any())
            {
                foreach (var budget in filter.Budget)
                {
                    switch (budget)
                    {
                        case Budget.UpToTenLakhs:
                            Query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget <= 1000000));
                            break;
                        case Budget.TenToTwentyLakhs:
                            Query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 1000000 && j.UpperBudget <= 2000000));
                            break;
                        case Budget.TwentyToThirtyLakhs:
                            Query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 2000000 && j.UpperBudget <= 3000000));
                            break;
                        case Budget.ThirtyToFourtyLakhs:
                            Query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 3000000 && j.UpperBudget <= 4000000));
                            break;
                        case Budget.FourtyToFiftyLakhs:
                            Query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 4000000 && j.UpperBudget <= 5000000));
                            break;
                        case Budget.FiftyToOneCrore:
                            Query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 5000000 && j.UpperBudget <= 10000000));
                            break;
                        case Budget.MoreThanOneCrore:
                            Query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 10000000));
                            break;
                    }
                }
            }
            if (filter.LeadTags != null && filter.LeadTags.Any())
            {
                foreach (var tag in filter.LeadTags)
                {
                    switch (tag)
                    {
                        case LeadTagEnum.IsHot:
                            Query.Where(i => i.TagInfo != null && i.TagInfo.IsHotLead);
                            break;
                        case LeadTagEnum.IsAboutToConvert:
                            Query.Where(i => i.TagInfo != null && i.TagInfo.IsAboutToConvert);
                            break;
                        case LeadTagEnum.IsEscalated:
                            Query.Where(i => i.TagInfo != null && i.TagInfo.IsEscalated);
                            break;
                        case LeadTagEnum.IsIntegrationLead:
                            Query.Where(i => i.TagInfo != null && i.TagInfo.IsIntegrationLead);
                            break;
                        case LeadTagEnum.IsHighlighted:
                            Query.Where(i => i.TagInfo != null && i.TagInfo.IsHighlighted);
                            break;
                        case LeadTagEnum.IsWarmLead:
                            Query.Where(i => i.TagInfo != null && i.TagInfo.IsWarmLead);
                            break;
                        case LeadTagEnum.IsColdLead:
                            Query.Where(i => i.TagInfo != null && i.TagInfo.IsColdLead);
                            break;
                    }
                }
            }
            if (filter.Locations != null && filter.Locations.Any())
            {
                filter.Locations = filter.Locations.ConvertAll(i => i.Replace(",", "").ToLower().Trim().Replace(" ", "")).ToList();
                /*Query.Where(i => i.Enquiries.Any(i => filter.Locations.Contains((i.Address.SubLocality + "" +
                   i.Address.Locality + "" +
                   i.Address.District + "" +
                   i.Address.City + "" +
                   i.Address.State + "" +
                   i.Address.Country + "" +
                   i.Address.PostalCode + "").ToLower().Trim().Replace(" ", ""))));*/
                Query.Where(i => i.Enquiries.Any(i => i.Addresses.Where(j => filter.Locations.Contains((j.SubLocality + "" +
                    j.Locality + "" +
                    j.District + "" +
                    j.City + "" +
                    j.State + "" +
                    j.Country + "" +
                    j.PostalCode + "").ToLower().Trim())).Any()));
            }
            if (filter.MinBudget != null && filter.MaxBudget != null)
            {
                Query.Where(i => i.Enquiries.Any(i => i.UpperBudget >= filter.MinBudget && i.UpperBudget <= filter.MaxBudget));
            }
        }
    }
}
