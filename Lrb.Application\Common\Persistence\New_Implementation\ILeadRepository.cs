﻿using Lrb.Application.Lead.Mobile;
using Lrb.Application.Lead.Mobile.v3;
using Lrb.Application.Lead.Web.Requests.GetRequests;
using Lrb.Domain.Entities.MasterData;

namespace Lrb.Application.Common.Persistence.New_Implementation
{
    public interface ILeadRepository : IEFRepository<Domain.Entities.Lead>
    {
        #region v1 methods for mobile
        Task<IEnumerable<Domain.Entities.Lead>> GetAllCategoryLeadsForMobileAsync(GetAllLeadsRequest request, Guid userId, IList<Guid> subIds, LeadFilterTypeMobile category, List<Guid>? leadHistoryIds, List<Guid>? scheduledMeetingLeadHistoryIds, List<Guid>? scheduledVisitLeadHistoryIds, List<CustomMasterLeadStatus> custumStatuses, bool? isAdmin = null);
        Task<IEnumerable<Domain.Entities.Lead>> GetAllCategoryLeadsForMobileV2Async(GetAllLeadsRequest request, Guid userId, IList<Guid> subIds, LeadFilterTypeMobile category, List<Guid>? leadHistoryIds, List<Guid>? scheduledMeetingLeadHistoryIds, List<Guid>? scheduledVisitLeadHistoryIds, List<CustomMasterLeadStatus> custumStatuses, bool? isAdmin = null);
        Task<int> GetAllCategoryLeadsCountForMobileAsync(GetAllLeadsRequest request, Guid userId, IList<Guid> subIds, LeadFilterTypeMobile category, List<Guid>? leadHistoryIds, List<Guid>? scheduledMeetingLeadHistoryIds, List<Guid>? scheduledVisitLeadHistoryIds, List<CustomMasterLeadStatus> custumStatuses = null, bool? isAdmin = null);
        Task<IEnumerable<Domain.Entities.Lead>> GetLeadsByCategoryForMobileAsync(GetLeadCategoryRequest request, Guid userId, IList<Guid> subIds, List<Guid>? leadHistoryIds, List<Guid>? ScheduledMeetingLeadHistoryIds, List<Guid>? ScheduledVisitLeadHistoryIds, List<CustomMasterLeadStatus> customMasterLeadStatus = null);
        Task<int> GetLeadsCountByCategoryForMobileAsync(GetLeadCategoryRequest request, Guid userId, IList<Guid> subIds, List<Guid>? leadHistoryIds, List<Guid>? ScheduledMeetingLeadHistoryIds, List<Guid>? ScheduledVisitLeadHistoryIds, List<CustomMasterLeadStatus> customMasterLeadStatus = null);
        Task<IEnumerable<Domain.Entities.Lead>> SearchLeadForMobileAsync(SearchLeadRequest request, Guid userId, IList<Guid> subIds, List<CustomMasterLeadStatus>? customMasterLeadStatus = null);
        Task<int> SearchLeadCountForMobileAsync(SearchLeadRequest request, Guid userId, IList<Guid> subIds, List<CustomMasterLeadStatus>? customMasterLeadStatus = null);
        #endregion
        #region v2 methods for mobile
        Task<IEnumerable<Domain.Entities.Lead>> GetAllCategoryLeadsForMobileAsync(Lead.Mobile.v2.V2GetAllLeadsRequest request, Guid userId, IList<Guid> subIds, LeadFilterTypeMobile category, List<Guid>? leadHistoryIds, List<Guid>? ScheduledMeetingLeadHistoryIds, List<Guid>? ScheduledVisitLeadHistoryIds, List<CustomMasterLeadStatus> custumStatuses = null, bool? isAdmin = null);
        Task<int> GetAllCategoryLeadsCountForMobileAsync(Lead.Mobile.v2.V2GetAllLeadsRequest request, Guid userId, IList<Guid> subIds, LeadFilterTypeMobile category, List<Guid>? leadHistoryIds, List<Guid>? ScheduledMeetingLeadHistoryIds, List<Guid>? ScheduledVisitLeadHistoryIds, List<CustomMasterLeadStatus> customMasterLeadStatus = null, bool? isAdmin = null);
        Task<IEnumerable<Domain.Entities.Lead>> GetLeadsByCategoryForMobileAsync(Lead.Mobile.v2.V2GetLeadCategoryRequest request, Guid userId, IList<Guid> subIds, List<Guid>? leadHistoryIds, List<Guid>? ScheduledMeetingLeadHistoryIds, List<Guid>? ScheduledVisitLeadHistoryIds, List<CustomMasterLeadStatus> customMasterLeadStatus = null, bool? isAdmin = null);
        Task<int> GetLeadsCountByCategoryForMobileAsync(Lead.Mobile.v2.V2GetLeadCategoryRequest request, Guid userId, IList<Guid> subIds, List<Guid>? leadHistoryIds, List<Guid>? ScheduledMeetingLeadHistoryIds, List<Guid>? ScheduledVisitLeadHistoryIds, List<CustomMasterLeadStatus> customMasterLeadStatus = null, bool? isAdmin = null);
        Task<IEnumerable<Domain.Entities.Lead>> SearchLeadForMobileAsync(Lead.Mobile.v2.V2SearchLeadRequest request, Guid userId, KeyValuePair<bool, List<Guid>> adminWithSubIds, List<CustomMasterLeadStatus>? customMasterLeadStatus = null);
        Task<int> SearchLeadCountForMobileAsync(Lead.Mobile.v2.V2SearchLeadRequest request, Guid userId, KeyValuePair<bool, List<Guid>> adminWithSubIds, List<CustomMasterLeadStatus>? customMasterLeadStatus = null);
        (List<AppointmentType>, List<bool>) GetAppointmentTypes(Application.Lead.Mobile.v2.V2GetAllLeadsRequest request);
        (List<AppointmentType>, List<bool>) GetAppointmentTypes(Application.Lead.Mobile.v2.V2GetLeadCategoryRequest request);
        #endregion
        #region v1 methods for web
        Task<List<Domain.Entities.Lead>> ListAsync();
        Task<IEnumerable<Domain.Entities.Lead>> GetAllLeadsForWebAsync(Lead.Web.GetAllLeadsRequest request, IEnumerable<Guid> subIds, Guid userId, List<Guid> leadHistoryIds = null, bool? isAdmin = null);
        Task<int> GetLeadsCountForWebAsync(Lead.Web.GetAllLeadsRequest request, IEnumerable<Guid> subIds, Guid userId, List<Guid> leadHistoryIds = null, bool? isAdmin = null);
        Task<List<Domain.Entities.Lead>> AddRangeAsync(List<Domain.Entities.Lead> leads);
        Task<IEnumerable<Domain.Entities.Lead>> GetAllLeadsByNewFiltersForWebAsync(Lead.Web.GetAllLeadsByNewFiltersRequest request, IEnumerable<Guid> subIds, Guid userId, List<Guid> leadHistoryIds = null, List<CustomMasterLeadStatus>? customMasterLeadStatus = null, string? tenantId = null, bool? isAdmin = null);
        Task<int> GetLeadsCountByNewFiltersForWebAsync(Lead.Web.GetAllLeadsParametersNewFilters request, IEnumerable<Guid> subIds, Guid userId, List<Guid> leadHistoryIds = null, List<CustomMasterLeadStatus>? customMasterLeadStatus = null, bool? isAdmin = null);
        Task<List<Application.Lead.Web.StatusFilterDto>> GetLeadsStatusCountByNewFiltersForWebAsync(Lead.Web.GetAllLeadsParametersNewFilters request, IEnumerable<Guid> subIds, Guid userId, List<Guid> leadHistoryIds = null, List<CustomMasterLeadStatus>? customMasterLeadStatus = null, bool? isAdmin = null);
        public (List<AppointmentType>, List<bool>) GetAppointmentTypes(Application.Lead.Web.GetAllLeadsParametersNewFilters request);
        #endregion
        #region export leads
        Task<IEnumerable<Domain.Entities.Lead>> GetAllLeadsForWebAsync(Lead.Web.Export.ExportLeadsRequest request, IEnumerable<Guid> subIds, Guid userId, List<Guid> leadHistoryIds);
        Task<IEnumerable<Domain.Entities.Lead>> GetAllLeadsExportByNewFiltersForWebAsync(Application.Lead.Web.GetAllLeadsByNewFiltersRequest request, IEnumerable<Guid> subIds, Guid userId, List<Guid> leadHistoryIds = null, List<CustomMasterLeadStatus>? customMasterLeadStatus = null, string? tenantId = null, bool? isAdmin = null);
        #endregion
        #region v3 methods mobile
        Task<IEnumerable<Domain.Entities.Lead>> GetAllCategoryLeadsForMobileAsync(V3GetAllLeadsRequest request, Guid userId, IList<Guid> subIds, List<CustomMasterLeadStatus> statuses = null);
        Task<int> GetAllCategoryLeadsCountForMobileAsync(V3GetAllLeadsRequest request, Guid userId, IList<Guid> subIds, List<CustomMasterLeadStatus> statuses = null);
        Task<int> GetAllCategoryLeadsCountForMobileAsync(V3GetLeadCategoryRequest request, Guid userId, IList<Guid> subIds, List<CustomMasterLeadStatus> statuses = null);
        Task<IEnumerable<Domain.Entities.Lead>> GetLeadsByCategoryForMobileAsync(V3GetLeadCategoryRequest request, Guid userId, IList<Guid> subIds, List<CustomMasterLeadStatus> statuses = null);
        #endregion

        #region CustomFiltersWeb
        Task<int> GetLeadsCountByCustomFiltersForWebAsync(CustomFilter filter, Lead.Web.GetAllLeadsParametersNewFilters request, IEnumerable<Guid> subIds, Guid userId, bool isAdmin, List<Guid> leadHistoryIds = null, List<CustomMasterLeadStatus>? customMasterLeadStatus = null);
        Task<List<Lead.Web.CustomFiltersDto>> GetLeadsCountByCustomFiltersForWebAsync(List<CustomFilter>? filters, Lead.Web.GetAllLeadsParametersNewFilters request, IEnumerable<Guid> subIds, Guid userId, bool isAdmin, List<Guid> leadHistoryIds = null, bool shouldGetOnlyBaseFilters = false, List<CustomMasterLeadStatus>? customMasterLeadStatus = null);
        Task<IEnumerable<Domain.Entities.Lead>> GetAllLeadsByCustomFiltersForWebAsync(CustomFilter filter, Lead.Web.GetAllLeadsParametersNewFilters request, IEnumerable<Guid> subIds, Guid userId, bool isAdmin, List<Guid> leadHistoryIds = null, List<CustomMasterLeadStatus>? customMasterLeadStatus = null);
        Task<IEnumerable<Domain.Entities.Lead>> GetAllLeadsExportByCustomFiltersForWebAsync(CustomFilter filter, Lead.Web.GetAllLeadsParametersNewFilters request, IEnumerable<Guid> subIds, Guid userId, bool isAdmin, List<Guid> leadHistoryIds = null, List<CustomMasterLeadStatus>? customMasterLeadStatus = null);
        #endregion

        #region CustomFiltersMobile
        Task<int> GetLeadsCountByCustomFiltersForMobileAsync(CustomFilter? filter, GetAllLeadsParametersNewFilters request, IEnumerable<Guid> subIds, Guid userId, bool isAdmin, List<Guid> leadHistoryIds = null, List<CustomMasterLeadStatus>? customMasterLeadStatus = null);
        Task<IEnumerable<Domain.Entities.Lead>> GetAllLeadsByCustomFiltersForMobileAsync(CustomFilter? filter, GetAllLeadsParametersNewFilters request, IEnumerable<Guid> subIds, Guid userId, bool isAdmin, List<Guid> leadHistoryIds = null);
        Task<IEnumerable<Domain.Entities.Lead>> GetAllLeadsByCustomFiltersForMobileV2Async(CustomFilter? filter, GetAllLeadsParametersNewFilters request, IEnumerable<Guid> subIds, Guid userId, bool isAdmin, List<Guid> leadHistoryIds = null);
        #endregion
        #region Anonymous
        Task<IEnumerable<Domain.Entities.Lead>> GetAnonymousLeadsForWebAsync(Lead.Web.GetAllLeadsAnonymousRequest request, List<Guid> leadHistoryIds = null, List<CustomMasterLeadStatus>? customMasterLeadStatus = null);
        Task<int> GetAnonymousLeadsCountForWebAsync(Lead.Web.GetAllLeadsAnonymousRequest request, List<Guid> leadHistoryIds = null, List<CustomMasterLeadStatus>? customMasterLeadStatus = null);
        #endregion

        Task<IEnumerable<Domain.Entities.Lead>> GetLeadByNumberAsync(List<string> contactNos);
        Task<IEnumerable<Domain.Entities.Lead>> GetParentLeadByContactNoAsync(List<string> contactNos);
        Task<IEnumerable<Domain.Entities.Lead>> GetParentLeadByContactNosAsync(List<string> contactNos);
        Task<IEnumerable<Domain.Entities.Lead>> GeLastDuplicateLeadsbyContactNosAsync(List<string> contactNos);
        Task<IQueryable<Domain.Entities.Lead>> BuildQueryForLeadsCount(Application.Lead.Web.GetAllLeadsParametersNewFilters request, IEnumerable<Guid> subIds, Guid userId, List<Guid> leadHistoryIds, List<CustomMasterLeadStatus>? customMasterLeadStatuses = null, string? tenantId = null, bool? isAdmin = null);
        Task<int> GetAllLeadsCountForWebAsync(Application.Lead.Web.GetAllLeadsParametersNewFilters request, IQueryable<Domain.Entities.Lead> query, IEnumerable<Guid> subIds, Guid userId, List<CustomMasterLeadStatus>? customMasterLeadStatuses = null, bool? isAdmin = null);

    }
}