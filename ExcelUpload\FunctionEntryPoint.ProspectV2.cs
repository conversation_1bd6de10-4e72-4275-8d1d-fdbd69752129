﻿using Dapper;
using Lrb.Application.Agency;
using Lrb.Application.Campaigns.Spec;
using Lrb.Application.ChannelPartner;
using Lrb.Application.DataManagement.Web;
using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.DataManagement.Web.Dtos.CustomData;
using Lrb.Application.DataManagement.Web.Mapping;
using Lrb.Application.DataManagement.Web.Specs;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.Project;
using Lrb.Application.Property;
using Lrb.Application.UserDetails.Web;
using Lrb.Application.UserDetails.Web.Specs;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using Lrb.Shared.Utils;
using Mapster;
using Newtonsoft.Json;
using Npgsql;
using System.Collections.Concurrent;
using System.Data;

namespace ExcelUpload
{
    public partial class FunctionEntryPoint : IFunctionEntryPoint
    {
        public async Task<DataTable> GetDataTableAsync(BulkProspectUploadTracker prospectUploadTracker)
        {
            #region Convert To DataTable
            Stream fileStream = await _blobStorageService.GetObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", prospectUploadTracker.S3BucketKey);
            DataTable dataTable = new();
            if (prospectUploadTracker.S3BucketKey.Split('.').LastOrDefault() == "csv")
            {
                using MemoryStream memoryStream = new();
                fileStream.CopyTo(memoryStream);
                dataTable = CSVHelper.CSVToDataTable(memoryStream);
            }
            else
            {
                dataTable = EPPlusExcelHelper.ConvertExcelToDataTable(fileStream, prospectUploadTracker.SheetName);
            }

            List<V2InvalidProspect> invalids = new();
            int totalRows = dataTable.Rows.Count;
            for (int i = totalRows - 1; i >= 0; i--)
            {
                var row = dataTable.Rows[i];
                var data1 = row[prospectUploadTracker.MappedColumnData[ProspectDataColumn.Name]].ToString();
                var data2 = row[prospectUploadTracker.MappedColumnData[ProspectDataColumn.ContactNo]].ToString();
                if (row.ItemArray.All(i => string.IsNullOrEmpty(i.ToString())))
                {
                    row.Delete();
                }
                else if (string.IsNullOrEmpty(row[prospectUploadTracker.MappedColumnData[ProspectDataColumn.Name]].ToString()) && string.IsNullOrEmpty(row[prospectUploadTracker.MappedColumnData[ProspectDataColumn.ContactNo]].ToString()))
                {
                    var notes = string.Join(",", row.ItemArray.Where(i => !string.IsNullOrEmpty(i.ToString())));
                    var invalidData = new V2InvalidProspect
                    {
                        Errors = "contact number and name are empty",
                        Notes = notes
                    };
                    if (!invalids.Any(i => i.Notes == invalidData.Notes))
                    {
                        invalids.Add(invalidData);
                    }
                    row.Delete();
                }
            }
            if (dataTable.Rows.Count <= 0)
            {
                throw new Exception("Excel sheet is empty. Please fill some data in the excel sheet template.");
            }
            totalRows = dataTable.Rows.Count;
            Console.WriteLine($"handler() -> Total Rows in the Excel: {dataTable.Rows.Count}");
            #endregion

            return dataTable;
        }

        public async Task<Lrb.Application.DataManagement.Web.Dtos.MasterItems> GetMasterItemsAsync(DataTable dataTable, BulkProspectUploadTracker prospectUploadTracker, string currentUserId, string tenantId)
        {
            #region featching data from excel
            List<string> properties = new List<string>();
            List<string> projects = new List<string>();
            List<string> agencies = new List<string>();
            List<string> channelPartners = new List<string>();
            List<string> campaigns = new List<string>();
            List<string>? assignedToUsers = new();
            if (((prospectUploadTracker.MappedColumnData?.ContainsKey(ProspectDataColumn.Property) ?? false))
                || ((prospectUploadTracker.MappedColumnData?.ContainsKey(ProspectDataColumn.Project) ?? false))
                || ((prospectUploadTracker.MappedColumnData?.ContainsKey(ProspectDataColumn.AgencyName) ?? false))
                || ((prospectUploadTracker.MappedColumnData?.ContainsKey(ProspectDataColumn.ChannelPartnerName) ?? false))
                || ((prospectUploadTracker.MappedColumnData?.ContainsKey(ProspectDataColumn.CampaignName) ?? false))
                || ((prospectUploadTracker.MappedColumnData?.ContainsKey(ProspectDataColumn.SourcingManager) ?? false))
                || ((prospectUploadTracker.MappedColumnData?.ContainsKey(ProspectDataColumn.ClosingManager) ?? false)))
            {
                var isPropertyPresent = (prospectUploadTracker.MappedColumnData?.ContainsKey(ProspectDataColumn.Property) ?? false);
                var isProjectPresent = (prospectUploadTracker.MappedColumnData?.ContainsKey(ProspectDataColumn.Project) ?? false);
                var isAgencyNamePresent = (prospectUploadTracker.MappedColumnData?.ContainsKey(ProspectDataColumn.AgencyName) ?? false);
                var isChannelPresent = (prospectUploadTracker.MappedColumnData?.ContainsKey(ProspectDataColumn.ChannelPartnerName) ?? false);
                var isCampaignPresent = (prospectUploadTracker.MappedColumnData?.ContainsKey(ProspectDataColumn.CampaignName) ?? false);
                var isSourcingManagerPresent = (prospectUploadTracker.MappedColumnData?.ContainsKey(ProspectDataColumn.SourcingManager) ?? false);
                var isClosingManagerPresent = (prospectUploadTracker.MappedColumnData?.ContainsKey(ProspectDataColumn.ClosingManager) ?? false);

                dataTable.AsEnumerable().ToList().ForEach(row =>
                {
                    if (isPropertyPresent)
                    {
                        var propertyName = row[prospectUploadTracker.MappedColumnData[ProspectDataColumn.Property]]?.ToString();
                        if (!string.IsNullOrWhiteSpace(propertyName))
                        {
                            properties.Add(propertyName.Trim());
                        }
                    }
                    if (isProjectPresent)
                    {
                        var projectName = row[prospectUploadTracker.MappedColumnData[ProspectDataColumn.Project]]?.ToString();
                        if (!string.IsNullOrWhiteSpace(projectName))
                        {
                            projects.Add(projectName.Trim());
                        }
                    }
                    if (isAgencyNamePresent)
                    {
                        var agencyName = row[prospectUploadTracker.MappedColumnData[ProspectDataColumn.AgencyName]]?.ToString();
                        if (!string.IsNullOrWhiteSpace(agencyName))
                        {
                            agencies.Add(agencyName.Trim());
                        }
                    }
                    if (isChannelPresent)
                    {
                        var cpName = row[prospectUploadTracker.MappedColumnData[ProspectDataColumn.ChannelPartnerName]]?.ToString();
                        if (!string.IsNullOrWhiteSpace(cpName))
                        {
                            channelPartners.Add(cpName.Trim());
                        }
                    }
                    if (isCampaignPresent)
                    {
                        var campaignName = row[prospectUploadTracker.MappedColumnData[ProspectDataColumn.CampaignName]]?.ToString();
                        if (!string.IsNullOrWhiteSpace(campaignName))
                        {
                            campaigns.Add(campaignName.Trim());
                        }
                    }
                    if (isSourcingManagerPresent)
                    {
                        var sourcingUser = row[prospectUploadTracker.MappedColumnData[ProspectDataColumn.SourcingManager]]?.ToString();
                        if (!string.IsNullOrWhiteSpace(sourcingUser))
                        {
                            assignedToUsers.Add(sourcingUser.Trim().ToLower());
                        }
                    }
                    if (isClosingManagerPresent)
                    {
                        var closingUser = row[prospectUploadTracker.MappedColumnData[ProspectDataColumn.ClosingManager]]?.ToString();
                        if (!string.IsNullOrWhiteSpace(closingUser))
                        {
                            assignedToUsers.Add(closingUser.Trim().ToLower());
                        }
                    }
                });
            }
            #endregion

            var masterItems = new Lrb.Application.DataManagement.Web.Dtos.MasterItems();
            var cancellationToken = CancellationToken.None;
            var connection = (_dapperRepository.GetConnectionStringAsync()) ?? throw new InvalidOperationException("Connection string can't be null");
            masterItems.TenantDisplayPrefix = await _dapperRepository.GetDisplayIndexPrefixByTenantIdAsync(tenantId);
            #region Projects
            var allProjects = await _newProjectRepo.ListAsync(new GetAllProjectByNamesSpec(projects.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""))), cancellationToken);
            allProjects = allProjects.GroupBy(p => p.Name.ToLower().Trim()).Select(g => g.First()).ToList();
            var remainingProjects = projects.Select(i => i).ToHashSet()
                .Where(project => !allProjects.ConvertAll(i => i.Name.ToLower().Trim().Replace(" ", "")).Contains(project.ToLower().Trim().Replace(" ", "")))
                .ToList();
            if (remainingProjects?.Any() ?? false)
            {
                var maxserial = await _dapperRepository.GetProjectMaxSerialNumberAsync(masterItems.TenantDisplayPrefix ?? string.Empty);
                var newprojects = remainingProjects
                    .Select(i =>
                    {
                        var serialNumber = BulkUploadHelper.GenerateDisplayIndexPrefixAsync(masterItems.TenantDisplayPrefix ?? string.Empty, maxserial);
                        maxserial = serialNumber.Item2;
                        return new Project
                        {
                            Id = Guid.NewGuid(),
                            Name = i,
                            CreatedOn = DateTime.UtcNow,
                            LastModifiedOn = DateTime.UtcNow,
                            SerialNo = serialNumber.Item1,
                        };
                    }).ToList();
                var newProjects = await CreateProejctsInDapperAsync(newprojects, connection, tenantId);
                if (newProjects.Item1 != null)
                {
                    allProjects.AddRange(newProjects.Item1);
                }
            }
            var deletedProjects = allProjects.Where(i => i.IsArchived).ToList();
            allProjects.RemoveAll(p => deletedProjects.Any(d => d.Id == p.Id));
            if (deletedProjects?.Any() ?? false)
            {
                var restoredProjects = await RestoreProejctsInDapperAsync(deletedProjects, connection, tenantId);
                if (restoredProjects.Item1 != null)
                {
                    allProjects.AddRange(restoredProjects.Item1);
                }
            }
            masterItems.Projects = allProjects;
            #endregion

            #region Properties
            var allProperties = await _propertyRepo.ListAsync(new GetAllPropertyByTitlesSpec(properties.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""))), cancellationToken);
            allProperties = allProperties.GroupBy(p => p.Title.ToLower().Trim()).Select(g => g.First()).ToList();
            var remainingProperties = properties.Select(i => i).ToHashSet()
                .Where(property => !string.IsNullOrEmpty(property) && !allProperties.ConvertAll(i => i.Title.ToLower().Trim().Replace(" ", "")).Contains(property.ToLower().Trim().Replace(" ", "")))
                .ToList();
            if (remainingProperties?.Any() ?? false)
            {
                var maxserial = await _dapperRepository.GetPropertiesMaxSerialNumberAsync(masterItems.TenantDisplayPrefix ?? string.Empty);
                var newproperties = remainingProperties
                    .Select(i =>
                    {
                        var serialNumber = BulkUploadHelper.GenerateDisplayIndexPrefixAsync(masterItems.TenantDisplayPrefix ?? string.Empty, maxserial);
                        maxserial = serialNumber.Item2;
                        return new Property
                        {
                            Id = Guid.NewGuid(),
                            Title = i,
                            CreatedOn = DateTime.UtcNow,
                            LastModifiedOn = DateTime.UtcNow,
                            SerialNo = serialNumber.Item1,
                        };
                    }).ToList();
                var newProperties = await CreateProrpertyInDapperAsync(newproperties, connection, tenantId);
                if (newProperties.Item1 != null)
                {
                    allProperties.AddRange(newProperties.Item1);
                }
            }
            var deletedProperties = allProperties.Where(i => i.IsArchived).ToList();
            allProperties.RemoveAll(p => deletedProperties.Any(d => d.Id == p.Id));
            if (deletedProperties?.Any() ?? false)
            {
                var restoredProperties = await RestorePropertiesInDapperAsync(deletedProperties, connection, tenantId);
                if (restoredProperties.Item1 != null)
                {
                    allProperties.AddRange(restoredProperties.Item1);
                }
            }
            masterItems.Properties = allProperties;
            #endregion

            #region Agencies
            var allAgencies = await _agencyRepo.ListAsync(new GetAllAgencyByNameSpec(agencies.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""))), cancellationToken);
            var remainingAgencies = agencies.Select(i => i).ToHashSet()
                .Where(agency => !string.IsNullOrEmpty(agency) && !allAgencies.ConvertAll(i => i.Name.ToLower().Trim().Replace(" ", "")).Contains(agency.ToLower().Trim().Replace(" ", "")))
                .ToList();
            if (remainingAgencies?.Any() ?? false)
            {
                //var newAgencies = await _agencyRepo.AddRangeAsync(remainingAgencies.Select(i => new Agency() { Name = i }));
                var newagencies = remainingAgencies.Select(i => new Agency { Id = Guid.NewGuid(), Name = i, CreatedOn = DateTime.UtcNow, LastModifiedOn = DateTime.UtcNow }).ToList();
                var newAgencies = await CreateAgencyInDapperAsync(newagencies, connection, tenantId);
                if (newAgencies.Item1 != null)
                {
                    allAgencies.AddRange(newAgencies.Item1);
                }
            }
            masterItems.Agencies = allAgencies;
            #endregion

            #region ChannelPartner
            var allChannelPartner = await _cpRepository.ListAsync(new GetAllChannelPartnerByNameSpec(channelPartners.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""))), cancellationToken);
            var remainingChannelPartner = channelPartners.Select(i => i).ToHashSet()
                .Where(channelPartner => !string.IsNullOrEmpty(channelPartner) && !allChannelPartner.ConvertAll(i => i.FirmName.ToLower().Trim().Replace(" ", "")).Contains(channelPartner.ToLower().Trim().Replace(" ", "")))
                .ToList();
            if (remainingChannelPartner?.Any() ?? false)
            {
                //var newChannelPartner = await _cpRepository.AddRangeAsync(remainingChannelPartner.Select(i => new ChannelPartner() { FirmName = i }));
                var newchannelPartner = remainingChannelPartner.Select(i => new ChannelPartner { Id = Guid.NewGuid(), FirmName = i, CreatedOn = DateTime.UtcNow, LastModifiedOn = DateTime.UtcNow }).ToList();
                var newChannelPartner = await CreateChannalPartnerInDapperAsync(newchannelPartner, connection, tenantId);
                if (newChannelPartner.Item1 != null)
                {
                    allChannelPartner.AddRange(newChannelPartner.Item1);
                }
            }
            masterItems.ChannelPartners = allChannelPartner;
            #endregion

            #region CommonData
            masterItems.PropetyTypes = await _propertyTypeRepo.ListAsync(cancellationToken);
            masterItems.ProspectStatuses = await _prospectStatusRepo.ListAsync(CancellationToken.None);
            masterItems.ProspectSources = await _prospectSourceRepo.ListAsync(CancellationToken.None);
            masterItems.GlobalSettings = await _globalSettingsRepository.FirstOrDefaultAsync(new Lrb.Application.GlobalSettings.Web.GetGlobalSettingsSpec(), cancellationToken);
            masterItems.AreaUnits = await _masterAreaUnitRepo.ListAsync(cancellationToken);
            #endregion

            #region Users
            masterItems.Users = masterItems.Users ?? new List<UserView>();
            if (prospectUploadTracker.UserIds?.Any() ?? false)
            {
                prospectUploadTracker.UserIds ??= new();
                var userIds = prospectUploadTracker.UserIds.Concat(new List<string> { prospectUploadTracker.CreatedBy.ToString(),
                    prospectUploadTracker.LastModifiedBy.ToString(),currentUserId }).ToList().ConvertAll(i => Guid.Parse(i));
                masterItems.HistoryUsers = await _userViewRepo.ListAsync(new UserViewByIdSpec(userIds));
                masterItems.Users = masterItems.HistoryUsers.Where(i => prospectUploadTracker.UserIds.Contains(i.Id.ToString())).ToList();
            }
            else
            {
                var userIds = (new List<string> { prospectUploadTracker.CreatedBy.ToString(),
                    prospectUploadTracker.LastModifiedBy.ToString(),currentUserId }).ConvertAll(i => Guid.Parse(i));
                masterItems.HistoryUsers = await _userViewRepo.ListAsync(new UserViewByIdSpec(userIds));
            }
            if (assignedToUsers?.Any() ?? false)
            {
                var usersnameById = await _userViewRepo.ListAsync(new GetUsersByUsernamesSpec(assignedToUsers.Distinct().ToList() ?? new()), cancellationToken);
                masterItems.Users.AddRange(usersnameById);
            }
            #endregion
            #region Campaign
            var allCampaigns = await _campaignRepo.ListAsync(new GetCampaignByNameSpec(campaigns.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""))), cancellationToken);
            var remainingCampaigns = campaigns.Select(i => i).ToHashSet()
                .Where(campaign => !string.IsNullOrEmpty(campaign) && !allCampaigns.ConvertAll(i => i.Name.ToLower().Trim().Replace(" ", "")).Contains(campaign.ToLower().Trim().Replace(" ", "")))
                .ToList();
            if (remainingCampaigns?.Any() ?? false)
            {
                var newCampaign = remainingCampaigns.Select(i => new Campaign { Id = Guid.NewGuid(), Name = i, CreatedOn = DateTime.UtcNow, LastModifiedOn = DateTime.UtcNow }).ToList();
                var updatedCampaign = await CreateCampaignInDapperAsync(newCampaign, connection, tenantId);
                if (updatedCampaign.Item1 != null)
                {
                    allCampaigns.AddRange(updatedCampaign.Item1);
                }
            }
            masterItems.Campaigns = allCampaigns;
            #endregion

            return masterItems;
        }
        public async Task ProspectHandlerV2(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            BulkProspectUploadTracker? bulkProspectUpload = (await _bulkProspectUploadRepo.ListAsync(new GetBulkProspectByTrackerId(input.TrackerId))).FirstOrDefault();
            try
            {
                if (bulkProspectUpload != null)
                {
                    try
                    {
                        bulkProspectUpload.MappedColumnData = bulkProspectUpload.MappedColumnData?.ToDictionary(i => i.Key, j => j.Value?.Trim() ?? string.Empty);
                        bulkProspectUpload.Status = Lrb.Domain.Enums.UploadStatus.Started;
                        bulkProspectUpload.LastModifiedBy = input.CurrentUserId;
                        bulkProspectUpload.CreatedBy = input.CurrentUserId;
                        var propetyTypes = new List<MasterPropertyType>(await _propertyTypeRepo.ListAsync(cancellationToken));
                        await _bulkProspectUploadRepo.UpdateAsync(bulkProspectUpload);
                        Console.WriteLine($"handler() -> ProspectHandler Updated Status: {bulkProspectUpload.Status} \n {JsonConvert.SerializeObject(bulkProspectUpload)}");

                        // retrieving the data from excel  
                        DataTable dataTable = await GetDataTableAsync(bulkProspectUpload);

                        //retrieving the master items  
                        var masterItems = await GetMasterItemsAsync(dataTable, bulkProspectUpload, input.CurrentUserId.ToString(), input.TenantId);

                        // Un Mapped Columns
                        var unMappedColumn = dataTable.GetUnmappedProspectColumnNames(bulkProspectUpload.MappedColumnData ?? new());
                        var results = dataTable.ConvertToProspectV2(bulkProspectUpload.MappedColumnData ?? new(), unMappedColumn, masterItems, bulkProspectUpload, input.CurrentUserId, _sourceRepo, input.JsonData ?? string.Empty);
                        List<Prospect> prospects = results.Item1;
                        var invalids = results.Item2;

                        prospects = prospects.DistinctBy(i => i.ContactNo).ToList();
                        List<DuplicateDto> existingProspects = (await _dapperRepository.CheckDuplicatePropsectsAsync(prospects.Select(i => i.ContactNo).ToList() ?? new(), prospects.Where(i => !string.IsNullOrEmpty(i.AlternateContactNo))?.Select(i => i.AlternateContactNo ?? string.Empty)?.ToList() ?? new(), input.TenantId)).ToList();
                        var existingContactNos = existingProspects.Where(i => !string.IsNullOrEmpty(i.ContactNo)).Select(i => i.ContactNo).ToList().Concat(existingProspects.Where(i => !string.IsNullOrEmpty(i.AlternateContactNo)).Select(i => i.AlternateContactNo).ToList());
                        var duplicateProspects = prospects.Where(i => existingContactNos.Contains(i.ContactNo) || existingContactNos.Contains(i.AlternateContactNo)).ToList();
                        prospects.RemoveAll(i => existingContactNos.Contains(i.ContactNo) || existingContactNos.Contains(i.AlternateContactNo));
                        if (duplicateProspects?.Any() ?? false)
                        {
                            var duplicateProspect = duplicateProspects
                           .Select(prospect =>
                           {
                               var invalidProspect = prospect.Adapt<V2InvalidProspect>();
                               invalidProspect.Errors = "Duplicate Prospect";
                               invalidProspect.Source = prospect.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.Source?.DisplayName?.ToString() ?? prospect.Enquiries?.FirstOrDefault()?.Source?.DisplayName?.ToString();
                               invalidProspect.SubSource = prospect.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.SubSource ?? prospect.Enquiries?.FirstOrDefault()?.Source?.DisplayName?.ToString();
                               invalidProspect.Status = prospect.Status?.DisplayName ?? string.Empty;
                               invalidProspect.Created = prospect.CreatedOn.Date;
                               return invalidProspect;
                           })
                           .ToList();
                            invalids.AddRange(duplicateProspect);
                        }
                        //update Tracker
                        bulkProspectUpload.Status = UploadStatus.InProgress;
                        bulkProspectUpload.TotalCount = dataTable.Rows.Count;
                        bulkProspectUpload.DistinctProspectCount = prospects.Count() + results.Item2.Count();
                        bulkProspectUpload.LastModifiedBy = input.CurrentUserId;
                        bulkProspectUpload.CreatedBy = input.CurrentUserId;

                        if (invalids.Any())
                        {
                            bulkProspectUpload.DuplicateCount = duplicateProspects?.Count() ?? 0;
                            bulkProspectUpload.InvalidCount = invalids.Where(i => i.Errors == "Invalid ContactNo" || i.Errors == "Invalid Name" || i.Errors == "contact number and name are empty" || i.Errors == "Duplicate ContactNo" || i.Errors == "Invalid Source").Count();
                            byte[] bytes = ProspectHelper.V2CreateExcelData(invalids).ToArray();
                            string fileName = $"InvalidProspect-{DateTime.Now:yyyy_MM_dd-HH_mm_ss}.xlsx";
                            string folder = "Prospects";
                            var key = await _blobStorageService?.UploadObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", folder, fileName, bytes) ?? string.Empty;
                            var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                            bulkProspectUpload.InvalidDataS3BucketKey = key;
                        }
                        if (prospects.Any())
                        {
                            bulkProspectUpload.ProspectCount = prospects?.Count() ?? 0;
                        }
                        await _bulkProspectUploadRepo.UpdateAsync(bulkProspectUpload);
                        Console.WriteLine($"handler() -> BulkProspectUploadTracker Updated Status: {bulkProspectUpload.Status} \n {JsonConvert.SerializeObject(bulkProspectUpload)}");
                        V2BulkUploadBackgroundDto backgroundDto = new();
                        if (prospects?.Any() ?? false)
                        {
                            int prospectsPerchunk = prospects.Count > 5000 ? 5000 : prospects.Count;
                            var chunks = prospects.Chunk(prospectsPerchunk).Select(i => new ConcurrentBag<Prospect>(i));
                            List<Task> tasks = new();
                            var currentUserId = _currentUser.GetUserId();
                            var tenantInfo = await _tenantIndependentRepo.GetTenantInfoAsync(input.TenantId);

                            if (currentUserId == Guid.Empty)
                            {
                                currentUserId = input.CurrentUserId;
                            }
                            Console.WriteLine($"handler() -> CurrentUserId: {currentUserId}");
                            var chunkIndex = 1;
                            foreach (var chunk in chunks.ToList())
                            {
                                backgroundDto = new V2BulkUploadBackgroundDto()
                                {
                                    CurrentUserId = currentUserId,
                                    TrackerId = bulkProspectUpload.Id,
                                    TenantInfoDto = tenantInfo,
                                    CancellationToken = CancellationToken.None,
                                    Prospects = new(chunk),
                                    UserIds = new(bulkProspectUpload.UserIds ?? new()),
                                    UserViews = masterItems.Users,
                                    HistoryUsers = masterItems.HistoryUsers,
                                };
                                await V2ExecuteDBOperationsAsync(backgroundDto, masterItems);
                                chunkIndex++;
                            }
                            bulkProspectUpload.TotalUploadedCount = prospects.Count();
                            bulkProspectUpload.LastModifiedOn = DateTime.UtcNow;
                            bulkProspectUpload.Status = UploadStatus.Completed;
                            await _bulkProspectUploadRepo.UpdateAsync(bulkProspectUpload);
                        }
                        else
                        {
                            bulkProspectUpload.TotalUploadedCount = 0;
                            bulkProspectUpload.LastModifiedOn = DateTime.UtcNow;
                            bulkProspectUpload.Status = UploadStatus.Completed;
                            await _bulkProspectUploadRepo.UpdateAsync(bulkProspectUpload);
                        }
                    }
                    catch (Exception ex)
                    {
                        bulkProspectUpload = await _bulkProspectUploadRepo.GetByIdAsync(bulkProspectUpload.Id);
                        bulkProspectUpload.Status = UploadStatus.Failed;
                        bulkProspectUpload.Message = ex.Message;
                        bulkProspectUpload.LastModifiedBy = input.CurrentUserId;
                        bulkProspectUpload.CreatedBy = input.CurrentUserId;
                        await _bulkProspectUploadRepo.UpdateAsync(bulkProspectUpload);
                    }
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CreateBulkProspectleadUploadTrackerUsingEPPlus -> ProspectHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                Console.WriteLine($"handler() -> Exception:  {JsonConvert.SerializeObject(ex, settings: new() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                throw;
            }
        }
        public async Task V2ExecuteDBOperationsAsync(V2BulkUploadBackgroundDto dto, Lrb.Application.DataManagement.Web.Dtos.MasterItems masterItems)
        {
            var tracker = (await _bulkProspectUploadRepo.ListAsync(new GetBulkProspectByTrackerId(dto.TrackerId))).FirstOrDefault();
            if (!(dto.Prospects?.Any() ?? false))
            {
                return;
            }
            try
            {
                if (dto.UserIds?.Any() ?? false)
                {
                    var users = await _userDetailsRepo.ListAsync(new GetUsersSpec(dto.UserIds.Select(i => Guid.Parse(i)).ToList() ?? new()));
                    dto?.Prospects?.AssignProspect(users.Where(i => i.IsAutomationEnabled).Select(i => i.UserId).ToList(), dto.CurrentUserId);
                }
                var connection = (_dapperRepository.GetConnectionStringAsync()) ?? throw new InvalidOperationException("Connection string can't be null");

                //var prospectdeatils = await _prospectRepo.AddRangeAsync(dto.Prospects);
                var prospectDetails = await CreateProspectInDapperAsync(dto.Prospects, connection, dto.TenantInfoDto?.Id ?? string.Empty, dto.CurrentUserId);
                var addresses = ExtractAddressFromProspects(prospectDetails);
                if (addresses?.Any() ?? false)
                {
                    await V3AddBulkLocations(addresses, connection, dto.TenantInfoDto?.Id ?? string.Empty);
                }
                var prospectVM = prospectDetails.Adapt<List<Lrb.Application.DataManagement.Web.Dtos.ViewProspectDto>>();
                Lrb.Application.Identity.Users.UserDetailsDto? user = await _userService.GetAsync(dto.CurrentUserId.ToString(), CancellationToken.None);
                List<ProspectHistory> allHistories = new();
                foreach (var prospectDto in prospectVM)
                {
                    prospectDto.V2SetUserViewForProspect(dto.HistoryUsers?.Adapt<List<Lrb.Application.Identity.Users.UserDetailsDto>>() ?? new(), currentUserId: dto.CurrentUserId);
                    var histories = ProspectHistoryHelper.V2CreateProspectHistoryForVM(prospectDto, null, user, 1, masterItems.ProspectStatuses ?? new(), masterItems.PropetyTypes ?? new(), masterItems.ProspectSources ?? new(), _userService, dto.CancellationToken);
                    allHistories.AddRange(histories);
                }
                //await _prospectHistoryRepo.AddRangeAsync(allHistories);
                await CreateProspectHistoryInDapperAsync(allHistories, connection, dto.TenantInfoDto?.Id ?? string.Empty);
                if (tracker != null)
                {
                    tracker.TotalUploadedCount += dto.Prospects.Count;
                    tracker.LastModifiedBy = dto.CurrentUserId;
                    tracker.CreatedBy = dto.CurrentUserId;
                    await _bulkProspectUploadRepo.UpdateAsync(tracker);
                }
            }
            catch (Exception ex)
            {

            }
            if (tracker != null)
            {
                tracker.TotalUploadedCount += dto.Prospects.Count;
                if (dto.IsLastChunk)
                {
                    tracker.Status = UploadStatus.Completed;
                }
                tracker.LastModifiedBy = dto.CurrentUserId;
                tracker.CreatedBy = dto.CurrentUserId;
                await _bulkProspectUploadRepo.UpdateAsync(tracker);
            }
        }
        public async Task<List<Prospect>> CreateProspectInDapperAsync(List<Prospect> prospects, string connectionString, string tenantId, Guid currentUserId)
        {

            if (!string.IsNullOrEmpty(connectionString))
            {

                var conn = new NpgsqlConnection(connectionString);
                try
                {
                    conn.Open();

                    var address = prospects
                       .Where(i => i.Address != null)
                       .Select(i => i.Address)
                       .Adapt<List<AddressDapperDto>>();
                    if (address.Any())
                    {
                        var addressProperties = GetMappedProperties<AddressDapperDto>();
                        var addressInsertQuery = QueryGenerator.GenerateInsertQuery(null, DataBaseDetails.LRBSchema, "Addresses", addressProperties, address);
                        await conn.ExecuteAsync(addressInsertQuery, commandTimeout: 1000);
                    }
                    //Prospect
                    var addProspects = prospects.Select(i =>
                    {
                        var prospectDto = i.Adapt<ProspectDapperDto>();
                        prospectDto.StatusId = i.Status?.Id ?? Guid.Empty;
                        prospectDto.AddressId = i.Address?.Id ?? null;
                        return prospectDto;
                    }).ToList();

                    var properties = GetMappedProperties<ProspectDapperDto>();
                    var insertQuery = QueryGenerator.GenerateInsertQuery(tenantId, DataBaseDetails.LRBSchema, "Prospects", properties, addProspects);
                    await conn.ExecuteAsync(insertQuery, commandTimeout: 1000);

                    //enquiries
                    var enquiries = prospects
                       .Where(prospect => prospect.Enquiries != null && prospect.Enquiries.Any())
                       .SelectMany(prospect =>
                            prospect.Enquiries.Select(enquiry =>
                            {
                                var prospectEnquiryDto = enquiry.Adapt<ProspectEnquiryDapperDto>();
                                prospectEnquiryDto.ProspectId = prospect.Id;
                                prospectEnquiryDto.PropertyTypeId = enquiry.PropertyType?.Id ?? null;
                                prospectEnquiryDto.SourceId = enquiry.Source?.Id ?? Guid.Empty;
                                return prospectEnquiryDto;
                            })
                     ).ToList();
                    if (enquiries.Any())
                    {
                        var enquiryProperties = GetMappedProperties<ProspectEnquiryDapperDto>();
                        var enquiryInsertQuery = QueryGenerator.GenerateInsertQuery(null, DataBaseDetails.LRBSchema, "ProspectEnquiries", enquiryProperties, enquiries);
                        await conn.ExecuteAsync(enquiryInsertQuery, commandTimeout: 1000);
                    }
                    var EnquiryPropTypes = prospects.SelectMany(p => p.Enquiries)
                        .Where(en => en.PropertyTypes != null && en.PropertyTypes.Any())
                        .SelectMany(en => en.PropertyTypes.Select(p => new ProspectEnquiryPropertyTypesDTO
                        {
                            ProspectEnquiriesId = en.Id,
                            PropertyTypesId = p.Id,

                        }));
                    if (EnquiryPropTypes.Any())
                    {
                        List<string> columnNames = new List<string> { "PropertyTypesId", "ProspectEnquiriesId" };
                        var projectInsertQuery = QueryGenerator.GenerateInsertQuery(null, DataBaseDetails.LRBSchema, "MasterPropertyTypeProspectEnquiry", columnNames, EnquiryPropTypes);
                        await conn.ExecuteAsync(projectInsertQuery, commandTimeout: 1000);
                    }
                    //addresses
                    var addresses = prospects.Select(i => i.Enquiries.FirstOrDefault())
                        .Where(i => i.Addresses != null && i.Addresses.Any()).SelectMany(address => address.Addresses).Adapt<List<AddressDapperDto>>();

                    if (addresses.Any())
                    {
                        var addressProperties = GetMappedProperties<AddressDapperDto>();
                        var addressInsertQuery = QueryGenerator.GenerateInsertQuery(null, DataBaseDetails.LRBSchema, "Addresses", addressProperties, addresses);
                        await conn.ExecuteAsync(addressInsertQuery, commandTimeout: 1000);

                    }

                    // enquiry address
                    var enquiryAddresses = prospects.Select(i => i.Enquiries.FirstOrDefault())
                    .Where(i => i.Addresses != null && i.Addresses.Any())
                    .SelectMany(en =>
                        en.Addresses.Select(address => new AddressProspectEnquiryDto
                        {
                            ProspectEnquiriesId = en.Id,
                            AddressesId = address.Id
                        })
                    ).ToList();
                    if (enquiryAddresses.Any())
                    {
                        var enquiryAddressProperties = GetMappedProperties<AddressProspectEnquiryDto>();
                        var enquiryAddressInsertQuery = QueryGenerator.GenerateInsertQuery(null, DataBaseDetails.LRBSchema, "AddressProspectEnquiry", enquiryAddressProperties, enquiryAddresses);
                        await conn.ExecuteAsync(enquiryAddressInsertQuery, commandTimeout: 1000);
                    }

                    //projects
                    var prospectProjects = prospects
                    .Where(prospect => prospect.Projects != null && prospect.Projects.Any())
                    .SelectMany(prospect => prospect.Projects.Select(project => new ProjectProspectDto
                    {
                        ProspectsId = prospect.Id,
                        ProjectsId = project.Id
                    })).ToList();
                    if (prospectProjects.Any())
                    {
                        List<string> columnNames = new List<string> { "ProspectsId", "ProjectsId" };
                        var projectInsertQuery = QueryGenerator.GenerateInsertQuery(null, DataBaseDetails.LRBSchema, "ProjectProspect", columnNames, prospectProjects);
                        await conn.ExecuteAsync(projectInsertQuery, commandTimeout: 1000);
                    }

                    //properties
                    var prospectsProperties = prospects
                    .Where(prospect => prospect.Properties != null && prospect.Properties.Any())
                    .SelectMany(prospect => prospect.Properties.Select(i => new PropertyProspectDto
                    {
                        ProspectsId = prospect.Id,
                        PropertiesId = i.Id
                    })).ToList();
                    if (prospectsProperties.Any())
                    {
                        List<string> columnNames = new List<string> { "ProspectsId", "PropertiesId" };
                        var propertyInsertQuery = QueryGenerator.GenerateInsertQuery(null, DataBaseDetails.LRBSchema, "PropertyProspect", columnNames, prospectsProperties);
                        await conn.ExecuteAsync(propertyInsertQuery, commandTimeout: 1000);
                    }

                    //agencies
                    var prospectAgencies = prospects
                    .Where(prospect => prospect.Agencies != null && prospect.Agencies.Any())
                    .SelectMany(prospect => prospect.Agencies.Select(i => new AgencyProspectDto
                    {
                        ProspectsId = prospect.Id,
                        AgenciesId = i.Id
                    })).ToList();
                    if (prospectAgencies.Any())
                    {
                        List<string> columnNames = new List<string> { "AgenciesId", "ProspectsId" };
                        var agencyInsertQuery = QueryGenerator.GenerateInsertQuery(null, DataBaseDetails.LRBSchema, "AgencyProspect", columnNames, prospectAgencies);
                        await conn.ExecuteAsync(agencyInsertQuery, commandTimeout: 1000);
                    }

                    //channel partners
                    var prospectChannelpartners = prospects
                    .Where(prospect => prospect.ChannelPartners != null && prospect.ChannelPartners.Any())
                    .SelectMany(prospect => prospect.ChannelPartners.Select(i => new ChannelPartnerProspectDto
                    {
                        ProspectsId = prospect.Id,
                        ChannelPartnersId = i.Id
                    })).ToList();
                    if (prospectChannelpartners.Any())
                    {
                        List<string> columnNames = new List<string> { "ChannelPartnersId", "ProspectsId" };
                        var channelpartnersInsertQuery = QueryGenerator.GenerateInsertQuery(null, DataBaseDetails.LRBSchema, "ChannelPartnerProspect", columnNames, prospectChannelpartners);
                        await conn.ExecuteAsync(channelpartnersInsertQuery, commandTimeout: 1000);
                    }
                    //campaigns
                    var prospectCampaigns = prospects
                    .Where(prospect => prospect.Campaigns != null && prospect.Campaigns.Any())
                    .SelectMany(prospect => prospect.Campaigns.Select(i => new CampaignProspectDto
                    {
                        ProspectsId = prospect.Id,
                        CampaignsId = i.Id
                    })).ToList();
                    if (prospectCampaigns.Any())
                    {
                        List<string> columnNames = new List<string> { "CampaignsId", "ProspectsId" };
                        var campaignInsertQuery = QueryGenerator.GenerateInsertQuery(null, DataBaseDetails.LRBSchema, "CampaignProspect", columnNames, prospectCampaigns);
                        await conn.ExecuteAsync(campaignInsertQuery, commandTimeout: 1000);
                    }


                    conn.Close();
                    return prospects.ToList();
                }
                catch (Exception ex)
                {
                    conn.Close();
                    throw;
                }
            }
            else
            {
                return new();
            }
        }
        public async Task<(List<ProspectHistory>?, bool)> CreateProspectHistoryInDapperAsync(List<ProspectHistory> histories, string connectionString, string tenantId)
        {
            if (!string.IsNullOrEmpty(connectionString))
            {
                var conn = new NpgsqlConnection(connectionString);
                try
                {
                    conn.Open();
                    var addHistories = histories.Adapt<List<ProspectHistoryDapperDto>>();
                    var columns = GetMappedProperties<ProspectHistoryDapperDto>();
                    var insertQuery = QueryGenerator.GenerateInsertQuery(tenantId, DataBaseDetails.LRBSchema, "ProspectHistories", columns, addHistories);
                    await conn.ExecuteAsync(insertQuery, commandTimeout: 1000);

                    conn.Close();
                    return (histories.ToList(), true);
                }
                catch (Exception ex)
                {
                    conn.Close();
                    throw;
                }
            }
            else
            {
                return (null, false);
            }
        }
    }
}
