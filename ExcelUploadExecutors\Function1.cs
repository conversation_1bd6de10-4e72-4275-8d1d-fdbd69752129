using ExcelUploadExecutors.Dtos;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Identity.Users;
using Lrb.Application.Reports.Web.Lead.Mappings;
using Lrb.Domain.Entities;
using Mapster;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.DurableTask;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using ExcelUpload;
namespace LrbExcelUpload
{
    public static class Function1
    {
        // static Semaphore _semaphoreOne = new Semaphore(1, 1);
        private static readonly SemaphoreSlim _semaphoreOne = new(1, 1);
        public static string Dev = "dev";
        public static string Qa = "qa";
        public static string Prd = "prd";

        [FunctionName("PrdExcelUpload")]
        public static async Task<IActionResult> PrdExcelUpload(
            [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = null)] HttpRequest req,
            ILogger log)
        {
            //log.LogInformation("HTTP PrdExcelUpload trigger function processed a request.");
            return await ProcessAsync(req, log, "PrdExcelUpload", Prd);
        }

        [FunctionName("QaExcelUpload")]
        public static async Task<IActionResult> QaExcelUpload(
            [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = null)] HttpRequest req,
            ILogger log)
        {
            //log.LogInformation("HTTP QaExcelUpload trigger function processed a request.");
            return await ProcessAsync(req, log, "QaExcelUpload", Qa);
        }

        [FunctionName("DevExcelUpload")]
        public static async Task<IActionResult> DevExcelUpload(
            [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = null)] HttpRequest req,
            ILogger log)
        {
            //log.LogInformation($"HTTP DevExcelUpload trigger function processed a request.");
            return await ProcessAsync(req, log, "DevExcelUpload", Dev);
        }

        private static async Task<IActionResult> ProcessAsync(HttpRequest req, ILogger log, string functionName, string environmentName)
        {
            try
            {
                string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                log.LogInformation($"HTTP {functionName} trigger Request Body." + requestBody ?? string.Empty);
                if (!string.IsNullOrWhiteSpace(requestBody))
                {
                    var requestStrings = JsonConvert.DeserializeObject<string[]>(requestBody);
                    if (requestStrings?.Any() ?? false)
                    {
                        var payload = JsonConvert.DeserializeObject<ExcelUpload.InputPayload>(requestStrings[0]);
                        var startup = new Startup(environmentName);
                        IServiceProvider provider = startup.ConfigureServices(payload.TenantId, environmentName);
                        ExcelUpload.IFunctionEntryPoint _functionEntryPoint = provider.GetRequiredService<ExcelUpload.IFunctionEntryPoint>();
                        var result = await _functionEntryPoint.ProcessBulkOperationsAsync(payload);
                        //log.LogInformation($"Completed {result}");
                        return new OkObjectResult(result ? "This HTTP triggered function executed successfully" : "ProcessBulkOperationsAsync returned false");
                    }
                }
                else
                {
                    log.LogInformation($"HTTP {functionName} trigger function Body is null.");
                }
                return new OkObjectResult($"HTTP {functionName} triggered function executed successfully");
            }
            catch (Exception ex)
            {
                //log.LogInformation($"failed {functionName}");
                log.LogInformation($"Exception details while calling {functionName}" + ex.Message);
                return new OkObjectResult(ex);
            }
        }
        [FunctionName("RunReportConfiguration")]
        public static async Task ScheduleJobs(
            [OrchestrationTrigger] IDurableOrchestrationContext context,
            ILogger log)
        {
            log.LogInformation("RunReportConfiguration function is Triggered.");
            var reportSchedule = context.GetInput<ReportsConfigurationDto>();
            var nextRun = GetRepetationOfScheduledTime(reportSchedule);
            log.LogInformation($"instanceId: {context.InstanceId}");
            if (context.IsReplaying)
            {
                nextRun = context.CurrentUtcDateTime;
            }
            if (!context.IsReplaying)
            {
                log.LogInformation($"upcoming triger date and time : {nextRun}");
            }
            await context.CreateTimer(nextRun, CancellationToken.None);
            log.LogInformation("Timer triggered. " + JsonConvert.SerializeObject(reportSchedule));
            var isTenantActive = await context.CallActivityAsync<bool>("SendEmailorWhatsApp", reportSchedule);    //calling the 

            if (!isTenantActive)
            {
                return;
            }
            context.ContinueAsNew(reportSchedule);
            log.LogInformation("RunReportConfiguration function is Ended.");
        }
        public static DateTime GetRepetationOfScheduledTime(ReportsConfigurationDto reportConfig)
        {
            DateTime nextRun = DateTime.UtcNow;
            DateTime currentTime = DateTime.UtcNow;
            TimeSpan timeOfDaySpan = reportConfig.ScheduleTime?.ToTimeSpan() ?? TimeSpan.Zero;
            switch (reportConfig.ScheduleType)
            {
                case Lrb.Domain.Enums.ScheduleType.Daily:
                    nextRun = DateTime.UtcNow.Date.Add(timeOfDaySpan);
                    if (nextRun <= currentTime)
                    {
                        nextRun = nextRun.AddDays(1);
                    }
                    break;
                case Lrb.Domain.Enums.ScheduleType.Weekly:
                    var selectedDays = reportConfig.SelectedDays ?? new List<int>();
                    int currentDay = (int)currentTime.DayOfWeek;
                    var nextRunDates = selectedDays
                        .Select(day =>
                        {
                            int daysToAdd = day >= currentDay ? day - currentDay : 7 - (currentDay - day);
                            DateTime nextRun = DateTime.UtcNow.Date.AddDays(daysToAdd).Add(timeOfDaySpan);
                            if (nextRun <= currentTime)
                            {
                                nextRun = nextRun.AddDays(7);
                            }
                            return nextRun;
                        })
                        .OrderBy(date => date)
                        .ToList();
                    nextRun = nextRunDates.FirstOrDefault();
                    break;
                case Lrb.Domain.Enums.ScheduleType.Monthly:
                    int targetDay = reportConfig.Duration ?? 1;
                    int year = DateTime.UtcNow.Year;
                    int month = DateTime.UtcNow.Month;

                    // Get the last valid day of the current month
                    int lastDayOfMonth = DateTime.DaysInMonth(year, month);
                    int validDay = Math.Min(targetDay, lastDayOfMonth);

                    nextRun = new DateTime(year, month, validDay).Add(timeOfDaySpan);
                    if (nextRun <= currentTime)
                    {
                        nextRun = nextRun.AddMonths(1);
                        int lastDayOfNextMonth = DateTime.DaysInMonth(nextRun.Year, nextRun.Month);
                        if (nextRun.Day > lastDayOfNextMonth)
                        {
                            nextRun = new DateTime(nextRun.Year, nextRun.Month, lastDayOfNextMonth, nextRun.Hour, nextRun.Minute, nextRun.Second);
                        }
                    }
                    break;
                case Lrb.Domain.Enums.ScheduleType.Days:
                    nextRun = DateTime.UtcNow.Date.AddDays(reportConfig.Duration ?? 1).Add(timeOfDaySpan);
                    break;
            }
            return nextRun;
        }
        [FunctionName("SendEmailorWhatsApp")]
        public static async Task<bool> ScheduleReport(
            [ActivityTrigger] ReportsConfigurationDto reportSchedule,
            ILogger log)
        {
            try
            {
                log.LogInformation("SendEmailorWhatsApp function is Triggered. " + reportSchedule.TenantId + " " + reportSchedule.Env);
                // Initialize services
                var startup = new Startup(reportSchedule.Env);
                IServiceProvider provider = startup.ConfigureServices(
                    reportSchedule.TenantId,
                    reportSchedule.Env);

                // Fetch users and tracker logic
                IUserService userService = provider.GetRequiredService<IUserService>();
                var userIds = reportSchedule.UserIds.Select(id => id.ToString()).ToList();
                var users = await userService.GetListOfActiveUsersByIdsAsync(userIds, CancellationToken.None);
                if (!(users?.Any() ?? false))
                {
                    log.LogWarning("No active users found. Skipping report scheduling.");
                    return false;
                }
                var userEmails = users.Select(i => i.Email).ToList();
                var contacts = users.Select(i => i.PhoneNumber).ToList();
                IRepositoryWithEvents<ExportReportsTracker> exportTrackerRepo = provider.GetRequiredService<IRepositoryWithEvents<ExportReportsTracker>>();
                IRepositoryWithEvents<UserDetails> userDetailsRepo = provider.GetRequiredService<IRepositoryWithEvents<UserDetails>>();
                var user = await userDetailsRepo.FirstOrDefaultAsync(new Lrb.Application.UserDetails.Web.GetUserDetailsByIdSpec(reportSchedule.CurrentUserId ?? Guid.Empty));
                var timeZoneInfo = user?.TimeZoneInfo;
                // Create and save tracker
                ExportReportsTracker tracker = new()
                {
                    Request = AutomationReportsHelper.GetReportRequestString(reportSchedule.ReportName, reportSchedule.Frequency ?? 1, reportSchedule.TenantId + " " + new Random().Next(100, 999999).ToString(), timeZoneInfo),
                    ToRecipients = userEmails,
                    Type = reportSchedule.ReportName,
                    CreatedBy = reportSchedule.CurrentUserId ?? Guid.Empty,
                    LastModifiedBy = reportSchedule.CurrentUserId ?? Guid.Empty,
                    ServiceType = reportSchedule.ServiceType,
                    IsReportAutomatedExport = true,
                    FileName = reportSchedule.TenantId + " " + new Random().Next(100, 999999).ToString(),
                    Id = Guid.NewGuid(),
                };
                await exportTrackerRepo.AddAsync(tracker, CancellationToken.None);
                InputPayload inputPayload = new(tracker.Id, reportSchedule.TenantId, reportSchedule.CurrentUserId ?? Guid.Empty, tracker.Type);
                Lrb.Application.GlobalSettings.Web.Dto.ReportsConfigurationDto reportConfiguration = reportSchedule.Adapt<Lrb.Application.GlobalSettings.Web.Dto.ReportsConfigurationDto>();
                reportConfiguration.PhoneNumbers = contacts;
                reportConfiguration.WATemplateId = reportConfiguration.WATemplateId ?? null;
                //log.LogInformation("ReportsConfigurationDto " + JsonConvert.SerializeObject(reportConfiguration) + " " + JsonConvert.SerializeObject(inputPayload) + " " + JsonConvert.SerializeObject(tracker));
                ExcelUpload.IFunctionEntryPoint _functionEntryPoint = provider.GetRequiredService<ExcelUpload.IFunctionEntryPoint>();
                var result = await _functionEntryPoint.ProcessBulkOperationsAsync(inputPayload, reportConfiguration);
                log.LogInformation("Reports are sent.");
                return true;
            }
            catch (Exception e)
            {
                log.LogError($"Error in SendEmailorWhatsApp: {e.Message} + {e?.InnerException} ");
                return false;

            }
        }

        [FunctionName("AutomationReport")]
        public static async Task<string> RequestCreateOrUpdate(
            [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = null)] HttpRequest req,
            [DurableClient] IDurableOrchestrationClient starter,
            ILogger log)
        {
            try
            {
                string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                var reportConfig = JsonConvert.DeserializeObject<ReportsConfigurationDto>(requestBody);
                string instanceId = "";

                // Only try to terminate if JobId is not null or empty
                if (!string.IsNullOrEmpty(reportConfig.JobId))
                {
                    var status = await starter.GetStatusAsync(reportConfig.JobId);
                    if (status != null &&
                        (status.RuntimeStatus == OrchestrationRuntimeStatus.Completed ||
                        status.RuntimeStatus == OrchestrationRuntimeStatus.Failed ||
                        status.RuntimeStatus == OrchestrationRuntimeStatus.Terminated))
                    {
                        log.LogInformation($"Job with Id {reportConfig.JobId} was already terminated or doesn't exist");
                    }
                    else if (status != null)
                    {
                        try
                        {
                            log.LogInformation($"Terminating existing job with Id: {reportConfig.JobId}");
                            await starter.TerminateAsync(reportConfig.JobId, "Updating the job");
                        }
                        catch (Exception e)
                        {
                            log.LogInformation($"Error in TerminateAsync: {e.Message} + {e?.InnerException}");
                            // Continue even if termination fails
                        }
                    }
                }

                // Always create a new job
                instanceId = await starter.StartNewAsync("RunReportConfiguration", reportConfig);
                log.LogInformation($"Orchestration instance created with Id: {instanceId}");
                reportConfig.JobId = instanceId;
                return instanceId;
            }
            catch (Exception ex)
            {
                log.LogError($"Exception in AutomationReport: {ex.Message} + {ex?.InnerException}");
                return string.Empty;
            }
        }
        [FunctionName("Delete")]
        public static async Task<IActionResult> DeleteAutomationReport(
               [HttpTrigger(AuthorizationLevel.Function, "delete", Route = "DeleteAutomationReport")] HttpRequest req,
               [DurableClient] IDurableOrchestrationClient starter,
               ILogger log)
        {
            try
            {
                string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                List<string> instanceIds = JsonConvert.DeserializeObject<List<string>>(requestBody);
                if (instanceIds == null || instanceIds.Count == 0)
                {
                    return new BadRequestObjectResult("No instance IDs provided.");
                }
                var response = new
                {
                    TerminatedInstances = new List<object>(),
                    AlreadyCompletedInstances = new List<object>()
                };

                var statuses = await starter.GetStatusAsync(instanceIds);
                if (statuses != null && statuses.Any(status => status != null))
                {
                    foreach (var instanceState in statuses)
                    {
                        if (instanceState.RuntimeStatus == OrchestrationRuntimeStatus.Completed
                            || instanceState.RuntimeStatus == OrchestrationRuntimeStatus.Failed
                            || instanceState.RuntimeStatus == OrchestrationRuntimeStatus.Terminated)
                        {
                            //alreadyCompletedInstances.Add(instanceState.InstanceId);
                            response.AlreadyCompletedInstances.Add(new
                            {
                                InstanceId = instanceState.InstanceId,
                                Status = instanceState.RuntimeStatus.ToString()
                            });
                        }
                        else
                        {
                            await starter.TerminateAsync(instanceState.InstanceId, "User requested to terminate the running job.");
                            await starter.PurgeInstanceHistoryAsync(instanceState.InstanceId);
                            // Re-check the status after attempting termination
                            var status = await starter.GetStatusAsync(instanceState.InstanceId);
                            if (status != null && status.RuntimeStatus != OrchestrationRuntimeStatus.Terminated)
                            {
                                log.LogInformation($"Orchestration {instanceState.InstanceId} is still in Pending state after termination. Restarting.");
                                // Start a new instance with the same input
                                string newInstanceId = await starter.RestartAsync(instanceState.InstanceId, false);
                            }
                            response.TerminatedInstances.Add(new
                            {
                                InstanceId = instanceState.InstanceId,
                                Status = instanceState.RuntimeStatus.ToString()
                            });
                        }
                    }
                    log.LogInformation($"Summary: Terminated {response.TerminatedInstances.Count} instances, {response.AlreadyCompletedInstances.Count} were already completed.");
                    return new OkObjectResult(response);
                }
                else
                {
                    log.LogInformation("No running orchestration instances found.");
                    return new OkObjectResult("No running orchestration instances found.");
                }
            }
            catch (Exception ex)
            {
                log.LogError($"Error while terminating orchestration instances : {ex.Message} + {ex?.InnerException} ");
                return new BadRequestObjectResult($"Error terminating orchestration instance with ID ': {ex.Message}");
            }
        }
        [FunctionName("TerminateTenantJobs")]
        public static async Task<IActionResult> TerminateAllRunningJobsByTenantId(
        [HttpTrigger(AuthorizationLevel.Function, "delete", Route = "TerminateTenantAllRunningJobs")] HttpRequest req,
        [DurableClient] IDurableOrchestrationClient starter,
        ILogger log)
        {
            try
            {
                string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                string tenantId = JsonConvert.DeserializeObject<string>(requestBody);
                if (string.IsNullOrEmpty(tenantId))
                {
                    return new BadRequestObjectResult("TenantId is required.");
                }

                // Fetch all orchestration statuses
                var result = await starter.ListInstancesAsync(new OrchestrationStatusQueryCondition
                {
                    RuntimeStatus = new[]
                    {
                        OrchestrationRuntimeStatus.Pending,
                        OrchestrationRuntimeStatus.Running
                    }
                }, CancellationToken.None);

                if (result.DurableOrchestrationState.Count() == 0)
                {
                    return new OkObjectResult("No running orchestration instances found.");
                }

                var terminatedInstances = new List<object>();
                foreach (var instance in result.DurableOrchestrationState)
                {
                    try
                    {
                        // Get the input of the orchestration to check its tenantId
                        var input = instance.Input.ToObject<ReportsConfigurationDto>();

                        // Only terminate if it belongs to the specified tenant
                        if (input != null && input.TenantId == tenantId)
                        {
                            await starter.TerminateAsync(instance.InstanceId, "User requested to terminate the running job.");
                            terminatedInstances.Add(new
                            {
                                InstanceId = instance.InstanceId,
                                Status = instance.RuntimeStatus.ToString()
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        log.LogError($"Could not process instance {instance.InstanceId}: {ex.Message}");
                        continue; // Skip this instance if there's an error
                    }
                }
                log.LogInformation($"Terminated {terminatedInstances.Count} running orchestration instances for tenant {tenantId}.");
                return new OkObjectResult(new { TerminatedInstances = terminatedInstances });
            }
            catch (Exception ex)
            {
                log.LogError($"Error while terminating running orchestration instances : {ex.Message} + {ex?.InnerException}");
                return new BadRequestObjectResult($"Error terminating running orchestration instances: {ex.Message}");
            }
        }
        [FunctionName("TerminateAllJobs")]
        public static async Task<IActionResult> TerminateAllRunningJobs(
        [HttpTrigger(AuthorizationLevel.Function, "delete", Route = "TerminateAllRunningJobs")] HttpRequest req,
        [DurableClient] IDurableOrchestrationClient starter,
        ILogger log)
        {
            try
            {
                // Fetch all orchestration statuses
                var result = await starter.ListInstancesAsync(new OrchestrationStatusQueryCondition
                {
                    RuntimeStatus = new[]
                    {
                        OrchestrationRuntimeStatus.Pending,
                        OrchestrationRuntimeStatus.Running
                    }
                }, CancellationToken.None);

                if (!result.DurableOrchestrationState.Any())
                {
                    return new OkObjectResult("No running orchestration instances found.");
                }

                var terminatedInstances = new List<object>();
                foreach (var instance in result.DurableOrchestrationState)
                {
                    try
                    {
                        await starter.TerminateAsync(instance.InstanceId, "User requested to terminate all running jobs.");
                        terminatedInstances.Add(new
                        {
                            InstanceId = instance.InstanceId,
                            Status = instance.RuntimeStatus.ToString()
                        });
                    }
                    catch (Exception ex)
                    {
                        log.LogError($"Could not terminate instance {instance.InstanceId}: {ex.Message}");
                        continue; // Skip this instance if there's an error
                    }
                }
                log.LogInformation($"Terminated {terminatedInstances.Count} running orchestration instances.");
                return new OkObjectResult(new { TerminatedInstances = terminatedInstances });
            }
            catch (Exception ex)
            {
                log.LogError($"Error while terminating running orchestration instances: {ex.Message} + {ex?.InnerException}");
                return new BadRequestObjectResult($"Error terminating running orchestration instances: {ex.Message}");
            }
        }
        [FunctionName("GetAllRunningJobs")]
        public static async Task<IActionResult> GetAllRunningJobs(
            [HttpTrigger(AuthorizationLevel.Function, "get", Route = "GetAllRunningJobs")] HttpRequest req,
            [DurableClient] IDurableOrchestrationClient starter,
            ILogger log)
        {
            try
            {
                // Fetch all orchestration statuses
                var result = await starter.ListInstancesAsync(new OrchestrationStatusQueryCondition
                {
                    RuntimeStatus = new[]
                    {
                        OrchestrationRuntimeStatus.Pending,
                        OrchestrationRuntimeStatus.Running
                    }
                }, CancellationToken.None);

                if (!result.DurableOrchestrationState.Any())
                {
                    return new OkObjectResult("No running orchestration instances found.");
                }

                var runningJobs = result.DurableOrchestrationState.Select(instance => new
                {
                    InstanceId = instance.InstanceId,
                    CreatedTime = instance.CreatedTime,
                    LastUpdatedTime = instance.LastUpdatedTime,
                    RuntimeStatus = instance.RuntimeStatus.ToString()
                });

                return new OkObjectResult(runningJobs);
            }
            catch (Exception ex)
            {
                log.LogError($"Error retrieving running orchestration instances: {ex.Message} + {ex?.InnerException}");
                return new BadRequestObjectResult($"Error retrieving running orchestration instances: {ex.Message}");
            }
        }
        [FunctionName("RedirectFunction")]
        public static async Task<IActionResult> Run([HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "{tenant}/{code}/{user}")] HttpRequest req, string tenant, string code, string user, ILogger log)
        {
            try
            {
                var startup = new Startup("qa");
                IServiceProvider provider = startup.ConfigureServices(tenant, "qa");
                ExcelUpload.IFunctionEntryPoint _functionEntryPoint = provider.GetRequiredService<ExcelUpload.IFunctionEntryPoint>();

                var result = await _functionEntryPoint.GetRedirectUrlFromShortUrl(tenant, code);
                var updatedResult = result?.Replace("/username/", $"/{user}/");
                if (!string.IsNullOrWhiteSpace(updatedResult) && updatedResult.Contains("property"))
                {
                    var check = await _functionEntryPoint.CheckListingPropertyByTenant(tenant);
                    updatedResult = check ? updatedResult.Replace("property", "listing-preview") : updatedResult.Replace("property", "property-preview");
                }
                var redirectUrl = string.IsNullOrWhiteSpace(updatedResult)
                    ? "https://sleep.leadrat.info/leads/manage-leads"
                    : updatedResult;

                return new RedirectResult(redirectUrl);
            }
            catch (Exception ex)
            {
                log.LogError(ex, "Error during redirect function execution");
                return new BadRequestResult();
            }
        }
    }
}
