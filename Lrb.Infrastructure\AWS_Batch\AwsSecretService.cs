﻿using Amazon;
using Amazon.Runtime;
using Amazon.SecretsManager;
using Amazon.SecretsManager.Model;
using Lrb.Application.Common.AWS_Batch;
using Lrb.Infrastructure.BlobStorage;
using Microsoft.Extensions.Options;

namespace Lrb.Infrastructure.AWS_Batch
{
    public class AwsSecretService : IAwsSecretService
    {
        private readonly AwsSecretManagerSettings _awsSettings;

        public AwsSecretService(IOptions<AwsSecretManagerSettings> awsSettings)
        {
            _awsSettings = awsSettings.Value;
        }

        public async Task<string> GetSecretAsync(string? secretName, CancellationToken cancellationToken = default)
        {
            var credentials = new BasicAWSCredentials(_awsSettings.AWSAccessToken, _awsSettings.AWSSecret);
            var region = RegionEndpoint.GetBySystemName(_awsSettings.Region);

            using var client = new AmazonSecretsManagerClient(credentials, region);

            var request = new GetSecretValueRequest
            {
                SecretId = secretName ?? _awsSettings.SecretsManagerName,
                VersionStage = "AWSCURRENT"
            };

            try
            {
                var response = await client.GetSecretValueAsync(request, cancellationToken);
                return string.IsNullOrWhiteSpace(response.SecretString)
                    ? string.Empty
                    : response.SecretString;
            }
            catch (ResourceNotFoundException)
            {
                return "Error: Secret not found";
            }
            catch (Exception ex)
            {
                return $"Error: {ex.Message}";
            }
        }
    }
}
