﻿using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Requests.GetRequests;
using Lrb.Application.Lead.Web.Requests.UpdationRequests;
using Mapster;
using Lrb.Application.Lead.Web.Requests;
using Newtonsoft.Json;

namespace Lrb.WebApi.Host.Controllers.V2
{
    [Authorize]
    [Route("api/v2/[controller]")]
    [ApiVersionNeutral]
    public class LeadController : VersionedApiController
    {
        private readonly Serilog.ILogger _logger;

        public LeadController( Serilog.ILogger logger)
        {
            _logger = logger;
        }

        [AllowAnonymous]
        [ApiKey]
        [HttpGet("anonymous")]
        [TenantIdHeader]
        [OpenApiOperation("Get all leads using available filters.", "")]
        public async Task<PagedResponse<PullViewLeadDto, string>> SearchAsync([FromQuery] GetAllLeadsAnonymousRequest request)
        {
            var response = await Mediator.Send(request);
            if (HttpContext.Request.Headers.TryGetValue(Lrb.Shared.Multitenancy.MultitenancyConstants.TenantIdName, out var tenantIdValues))
            {
                string? tenantId = tenantIdValues.FirstOrDefault();
                _logger.Information("GetAllLeadsAnonymousRequest info: PageNumber={PageNumber}, PageSize={PageSize}, TenantId={TenantId}", request.PageNumber, request.PageSize, tenantId);

            }
            return response;
        }
        [AllowAnonymous]
        [HttpPost("anonymous")]
        [TenantIdHeader]
        [OpenApiOperation("Get all leads using available filters.", "")]
        public async Task<PagedResponse<PullViewLeadDto, string>> SearchAsyncV1([FromBody] GetAllLeadsAnonymousRequest request)
        {
            var response = await Mediator.Send(request);
            if (HttpContext.Request.Headers.TryGetValue(Lrb.Shared.Multitenancy.MultitenancyConstants.TenantIdName, out var tenantIdValues))
            {
                string? tenantId = tenantIdValues.FirstOrDefault();
                _logger.Information("GetAllLeadsAnonymousRequest info: PageNumber={PageNumber}, PageSize={PageSize}, TenantId={TenantId}", request.PageNumber, request.PageSize, tenantId);

            }
            return response;
        }

        [HttpPost("new")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Search leads using available filters New Requirements.", "")]
        public async Task<PagedResponse<ViewLeadDto, LeadCountsByNewFilterDto>> SearchAsync([FromBody] GetAllLeadsByNewFiltersRequest request)
        {
            var response = await Mediator.Send(request);
            //_logger.Information($"LeadController -> GetAllLeads, Leads:" + JsonConvert.SerializeObject(response));
            return response;
        }
        [HttpPost("counts")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Search leads using available filters.", "")]
        public async Task<Response<LeadCountDto>> GetCountAsync([FromBody] GetAllLeadCountsRequest request)
        {
            var response = await Mediator.Send(request);
            //_logger.Information($"LeadController -> GetAllLeads, Leads:" + JsonConvert.SerializeObject(response));
            return response;
        }
        [HttpPost("counts/basefilter")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get Leads Count By BaseLeadFilter types.", "")]
        public async Task<Response<LeadCountsByBaseFiltersDto>> GetBaseCountAsync([FromBody] GetAllLeadCountsByBaseFilterRequest request)
        {
            var response = await Mediator.Send(request);
            return response;
        }
        [HttpPost("all")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Search leads using available filters.", "")]
        public async Task<PagedResponse<ViewLeadDto, string>> GetAllAsync([FromBody] GetAllLeadsOnlyRequest request)
        {

            var response = await Mediator.Send(request);
            //_logger.Information($"LeadController -> GetAllLeads, Leads:" + JsonConvert.SerializeObject(response));
            return response;
        }

        [HttpPost("getAll")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Search leads using available filters.", "")]
        public async Task<PagedResponse<ViewLeadDto, string>> GetAllAsync([FromBody] GetAllLeadRequestDapper request)
        {
            var response = await Mediator.Send(request);
            _logger.Information($"LeadController -> GetAllLeads, Leads:" + JsonConvert.SerializeObject(response));
            return response;
        }
        [HttpPost("counts/active")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get Active Lead Counts.", "")]
        public async Task<Response<ActiveLeadCountsDto>> GetCountsAsync([FromBody] GetActiveLeadCountsRequest request)
        {
            var response = await Mediator.Send(request);
            return new(response);
        }
        [HttpPost("counts/notinterested")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get Not Interested Lead Counts.", "")]
        public async Task<Response<NotInterestedLeadsCountDto>> GetCountsAsync([FromBody] GetNotInterestedLeadCountsRequest request)
        {
            var response = await Mediator.Send(request);
            return new(response);
        }
        [HttpPost("counts/Statuses")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get Lead Counts by Statuses .", "")]
        public async Task<Response<PagedResponse<LeadStatusFilterDto, string>>> GetCountsAsync([FromBody] LeadsCountByStatusRequest request)
        {
            var response = await Mediator.Send(request);
            return new(response);
        }
        [HttpPost("new/all")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get All Leads Only by New Filters.", "")]
        public async Task<PagedResponse<ViewLeadDto, string>> SearchAsync([FromBody] GetAllLeadsOnlyByNewFiltersRequest request)
        {
            var response = await Mediator.Send(request);
            return response;
        }
        [HttpPost("new/counts/basefilter")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get Lead Counts By BaseLeadFilter types By New Filters.", "")]
        public async Task<Response<LeadCountsByNewBaseFiltersDto>> GetCountsAsync([FromBody] GetAllLeadCountsByBaseFiltersNewRequest request)
        {
            var response = await Mediator.Send(request);
            return response;
        }
        [HttpPost("custom-filters-count")]
        [TenantIdHeader]
        [OpenApiOperation("Get leads count by custom filters.", "")]
        public async Task<Response<List<CustomFiltersDto>>> GetAsync([FromBody] GetAllLeadsCountByCustomFiltersRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("custom-filters-count-level1")]
        [TenantIdHeader]
        [OpenApiOperation("Get leads count by custom filters.", "")]
        public async Task<Response<List<CustomFiltersDto>>> GetLevel1DataAsync([FromBody] GetAllLeadsCountByCustomFiltersLevel1Request request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("custom-filters-count-level2")]
        [TenantIdHeader]
        [OpenApiOperation("Get leads count by custom filters.", "")]
        public async Task<Response<List<CustomFiltersDto>>> GetLevel2DataAsync([FromBody] GetAllLeadsCountByCustomFiltersLevel2Request request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("custom-filters")]
        [TenantIdHeader]
        [OpenApiOperation("Get leads by custom filters.", "")]
        public async Task<PagedResponse<ViewLeadDto, string>> GetAsync([FromBody] GetAllLeadsByCustomFiltersRequest request)
        {
            return await Mediator.Send(request);
        }


        [HttpGet("history/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get LeadHistory of a lead.", "")]
        public async Task<Response<List<LeadHistoryDto>>> GetHistoriesAsync(Guid id)
        {
            var res = await Mediator.Send(new GetLeadHistoriesByIdRequestV2(id));
            return res;
        }

        [HttpPut("status/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.UpdateLeadStatus, LrbResource.Leads)]
        [OpenApiOperation("Update status of a lead.", "")]
        public async Task<ActionResult<bool>> UpdateStatusAsync(UpdateLeadStatusRequest request, Guid id)
        {
            return id != request.Id
                ? BadRequest()
                :

                Ok(await Mediator.Send(request.Adapt<UpdateLeadStatusRequest>()));
        }
    }
}
