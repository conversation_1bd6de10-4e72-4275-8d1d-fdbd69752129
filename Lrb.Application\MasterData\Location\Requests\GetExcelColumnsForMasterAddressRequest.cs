﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.MasterData.Location.Dto;
using Lrb.Application.Utils;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using Serilog;


namespace Lrb.Application.MasterData.Location.Requests
{
    public class GetExcelColumnsForMasterAddressRequest : IRequest<Response<FileColumnDto>>
    {
        public IFormFile? File { get; set; }
        public GetExcelColumnsForMasterAddressRequest(IFormFile file)
        {
            File = file;
        }
    }

    public class GetExcelColumnsForMasterAddressRequestHandler : IRequestHandler<GetExcelColumnsForMasterAddressRequest, Response<FileColumnDto>>
    {
        private readonly IBlobStorageService _blobStorageService;
        private readonly ILogger _logger;
        public GetExcelColumnsForMasterAddressRequestHandler(IBlobStorageService blobStorageService, ILogger logger)
        {
            _blobStorageService = blobStorageService;
            _logger = logger;
        }
        public async Task<Response<FileColumnDto>> Handle(GetExcelColumnsForMasterAddressRequest request, CancellationToken cancellationToken)
        {
            _logger.Information("GetExcelColumnsForMasterAddressRequest -> Handle: " + JsonConvert.SerializeObject(request));
            var file = request.File;
            if (file == null)
            {
                throw new ArgumentNullException(nameof(file));
            }
            var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", "MasterData", file);
            List<string> columns = new List<string>();
            Dictionary<string, List<string>> multiSheetColumns = new();
            if (key.Split('.').LastOrDefault() == "csv")
            {
                columns = await CSVHelper.GetCSVColumns(file);
                multiSheetColumns.Add("Default", columns);
            }
            else
            {
                columns = EPPlusExcelHelper.GetFileColumns(file);
                multiSheetColumns = EPPlusExcelHelper.GetFileColumnsOfMultiSheets(file);
            }
            FileColumnDto excelColumnsViewModel = new()
            {
                S3BucketKey = key,
                ColumnNames = columns,
                MultiSheetColumnNames = multiSheetColumns,
            };
            return new Response<FileColumnDto>(excelColumnsViewModel);
        }
    }
}
