{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Serilog": {"Using": ["AWS.Logger.SeriLog"], "LogGroup": "/aws/lightsail/container/lrb-prd-whatsappapi", "Region": "ap-south-1", "Enrich": ["FromLogContext", "WithMachineName", "WithProcessId", "WithThreadId", "WithHangfireContext"], "MinimumLevel": {"Default": "Information", "Override": {"Hangfire": "Warning", "Microsoft": "Error", "Microsoft.Hosting.Lifetime": "Information", "System": "Information"}}, "Properties": {"Application": "Lrb.WhatsApp.Host"}, "WriteTo": [{"Args": {"path": "Logs/logs.json", "formatter": "Serilog.Formatting.Json.Json<PERSON>, Ser<PERSON>g", "rollingInterval": "Day", "restrictedToMinimumLevel": "Information", "retainedFileCountLimit": 5}, "Name": "File"}, {"Name": "Seq", "Args": {"serverUrl": "http://localhost:5341"}}, {"Name": "Hangfire"}, {"Name": "Elasticsearch", "Args": {"nodeUris": "http://localhost:9200;", "indexFormat": "Lrb.WhatsApp.Host-logs-{0:yyyy.MM}", "numberOfShards": 2, "numberOfReplicas": 1, "restrictedToMinimumLevel": "Information"}}]}, "DatabaseSettings": {"DefaultConnection": "Host=lrb-prd.postgres.database.azure.com;Port=5432;Database=leadratBlackApp;Username=dbmasteruser;Password=********************;Pooling=true;MinPoolSize=3;MaxPoolSize=5000;"}, "AppUrls": {"WebApi": "https://connect.leadrat.com/"}, "CosmosSettings": {"EndpointUri": "https://lrb-prd-whatsapp.documents.azure.com:443/", "PrimaryKey": "****************************************************************************************", "ContainerName": "lrb-prd"}, "LeadratApis": {"WhatsAppApi": "https://connect.leadrat.com/api/v1/integration/whatsapp", "WaPushNotification": "https://prd-mobile.leadrat.com/api/v1/utility/wa-notification"}, "CorsSettings": {"Angular": "http://localhost:4200", "Blazor": "https://lrb-whatsapp-api-prd.azurewebsites.net;https://localhost:44383", "React": "http://localhost:3000"}}