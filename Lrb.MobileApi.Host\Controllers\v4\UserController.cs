﻿using Lrb.Application.UserDetails.Mobile;
using Lrb.Application.UserDetails.Mobile.Request;
using static Lrb.Application.UserDetails.Mobile.Request.GetReporteesRequestHandler;

namespace Lrb.MobileApi.Host.Controllers.v4
{
    [Authorize]
    [Route("api/v4/[controller]")]
    [ApiVersionNeutral]
    public class UserController:BaseApiController
    {
        [HttpPost("getAllUsers")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Users)]
        [OpenApiOperation("Get all user details.", "")]
        public Task<PagedResponse<UserDetailsDto, string>> SearchAsync([FromBody] GetAllUserInfoViewRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpPost]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.ViewForFilter, LrbResource.Users)]
        [OpenApiOperation("Get all users.", "")]
        public Task<PagedResponse<ViewUserDto, string>> SearchAsync([FromBody] GetAllUsersRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpPost("allUsersToAssign")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.AssignToAny, LrbResource.Users)]
        [OpenApiOperation("Get All users To Assign.", "")]
        public Task<PagedResponse<ReportUserDto, string>> GetAllUsersAsync([FromBody] GetAllUsersToAssignRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpPost("getUsersByRoleId")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Users)]
        [OpenApiOperation("Get users by role id.", "")]
        public Task<PagedResponse<ViewUserDto, string>> GetUsersByRoleIdAsync([FromBody] GetUsersByRoleIdRequest request)
        {
            return Mediator.Send(request);
        }

    }
}
