﻿using Microsoft.Graph;

namespace Lrb.Application.Lead.Web
{
    public class LeadCountDto : IDto
    {
        public long? AllLeadsCount { get; set; }
        public long? UnassignLeadsCount { get; set; }
        public long? NewLeadsCount { get; set; }
        public long? TodayLeadsCount { get; set; }
        public long? OverdueLeadsCount { get; set; }
        public long? NotInterestedLeadsCount { get; set; }
        public long? DroppedLeadsCount { get; set; }
        public long? EscalatedLeadsCount { get; set; }
        public long? PendingLeadsCount { get; set; }
        public long? BookedLeadsCount { get; set; }
        public long? DeletedLeadsCount { get; set; }
        public long? EscalatedLeadsFlagCount { get; set; }
        public long? HotLeadsFlagCount { get; set; }
        public long? WarmLeadsFlagCount { get; set; }
        public long? ColdLeadsFlagCount { get; set; }
        public long? AboutToConvertLeadsFlagCount { get; set; }
        public long? HighlightedLeadsFlagCount { get; set; }
        public long? UpcomingLeadsCount { get; set; }
        public long? AllWithNIDLeadsCount { get; set; }
        public long? BookingCancelLeadCount { get; set; }
        public long? ExpressionOfIntrestLeadCount { get; set; }
        public long? InvoicedLeadsCount { get; set; }
        public long? ReEnquiredLeadsCount { get; set; }


    }
    public class LeadCountsByNewFilterDto : IDto
    {
        public long? ManageLeadsCount { get; set; }
        public long? AllLeadsCount { get; set; }
        public long? MyLeadsCount { get; set; }
        public long? TeamLeadsCount { get; set; }
        public long? UnassignLeadsCount { get; set; }
        public long? DeletedLeadsCount { get; set; }
        public long? DuplicateLeadsCount { get; set; }
        public long? ActiveLeadsCount { get; set; }
        public long? NotInterestedLeadsCount { get; set; }
        public long? DroppedLeadsCount { get; set; }
        public long? NewLeadsCount { get; set; }
        public long? PendingLeadsCount { get; set; }
        public long? ScheduledLeadsCount { get; set; }
        public long? OverdueLeadsCount { get; set; }
        public long? BookedLeadsCount { get; set; }
        public long? ScheduledTodayLeadsCount { get; set; }
        public long? ScheduledTomorrowLeadsCount { get; set; }
        public long? ScheduledNextTwoDaysLeadsCount { get; set; }
        public long? UpcomingScheduledLeadsCount { get; set; }
        public long? SiteVisitsCount { get; set; }
        public long? MeetingsCount { get; set; }
        public long? CallbackCount { get; set; }
        public long? DifferentRequirementsCount { get; set; }
        //public long? PlanPostponedCount { get; set; }
        public long? DifferentLocationCount { get; set; }
        public long? UnmatchedBudgetCount { get; set; }
        public long? NotLookingCount { get; set; }
        public long? WrongOrInvalidNoCount { get; set; }
        public long? PurchasedFromOthersCount { get; set; }
        //public long? EscalatedLeadsCount { get; set; }
        public long? EscalatedLeadsFlagCount { get; set; }
        public long? HotLeadsFlagCount { get; set; }
        public long? WarmLeadsFlagCount { get; set; }
        public long? ColdLeadsFlagCount { get; set; }
        public long? AboutToConvertLeadsFlagCount { get; set; }
        public long? HighlightedLeadsFlagCount { get; set; }
        public long? TouchedUnassignedLeadsCount { get; set; }
        public long? AllFirstLevelLeadsCount { get; set; }
        public long? OverdueMeetingCount { get; set; }
        public long? OverdueSiteVisitCount { get; set; }
        public long? OverdueCallbackCount { get; set; }
        public long? RingingNotReceivedCount { get; set; }
        public long? BookingCancelLeadCount { get; set; }
        public long? ExpressionOfInterestLeadCount { get; set; }
        public long? InvoicedLeadsCount { get; set; }
        public long? ReEnquiredLeadsCount { get; set; }

    }
    public class LeadCountsByBaseFiltersDto : IDto
    {
        public long? ManageLeadsCount { get; set; }
        public long? AllLeads { get; set; }
        public long? MyLeads { get; set; }
        public long? Teams { get; set; }
        public long? Unassigned { get; set; }
        public long? Deleted { get; set; }
        public long? Duplicate { get; set; }
        public long? ReEnquired { get; set; }

        
    }
    public class LeadCountsByNewBaseFiltersDto : IDto
    {
        //public long? ManageLeadsCount { get; set; }
        public long? AllLeadsCount { get; set; }
        public long? MyLeadsCount { get; set; }
        public long? TeamLeadsCount { get; set; }
        public long? UnassignLeadsCount { get; set; }
        public long? DeletedLeadsCount { get; set; }
        public long? DuplicateLeadsCount { get; set; }
    }
    public class ActiveLeadCountsDto : IDto
    {
        public long? ActiveLeadsCount { get; set; }
        //public long? NotInterestedLeadsCount { get; set; }
        //public long? DroppedLeadsCount { get; set; }
        public long? NewLeadsCount { get; set; }
        public long? PendingLeadsCount { get; set; }
        public long? ScheduledLeadsCount { get; set; }
        public long? OverdueLeadsCount { get; set; }
        public long? BookedLeadsCount { get; set; }
        public long? ScheduledTodayLeadsCount { get; set; }
        public long? ScheduledTomorrowLeadsCount { get; set; }
        public long? ScheduledNextTwoDaysLeadsCount { get; set; }
        public long? UpcomingScheduledLeadsCount { get; set; }
        public long? SiteVisitsCount { get; set; }
        public long? MeetingsCount { get; set; }
        public long? CallbackCount { get; set; }
        public long? AllLeadsCount { get; set; }
        public long? OverdueMeetingCount { get; set; }
        public long? OverdueSiteVisitCount { get; set; }
        public long? OverdueCallbackCount { get; set; }
        public long? BookingCancelLeadCount { get; set; }
        public long? ExpressionOfInterestLeadCount { get; set; } 
        public long? InvoicedLeadsCount { get; set; }
    }
    public class NotInterestedLeadsCountDto : IDto
    {
        public long? NotInterestedLeadsCount { get; set; }
        //public long? ActiveLeadsCount { get; set; }
        //public long? DroppedLeadsCount { get; set; }
        public long? DifferentRequirementsCount { get; set; }
        public long? DifferentLocationCount { get; set; }
        public long? UnmatchedBudgetCount { get; set; }
        public long? AllLeadsCount { get; set; }
        public long? BookedLeadsCount { get; set; }
        public long? BookingCancelLeadCount { get; set; }
    }
    public class DroppedLeadsCountDto : IDto
    {
        public long? DroppedLeadsCount { get; set; }
        //public long? ActiveLeadsCount { get; set; }
        //public long? NotInterestedLeadsCount { get; set; }
        public long? NotLookingCount { get; set; }
        public long? WrongOrInvalidNoCount { get; set; }
        public long? PurchasedFromOthersCount { get; set; }
        public long? AllLeadsCount { get; set; }
        public long? BookedLeadsCount { get; set; }
        public long? RingingNotReceivedCount { get; set; }
        public long? BookingCancelLeadCount { get; set; }
    }
    public class LeadCountsByFlagDto : IDto
    {
        public long? EscalatedLeadsFlagCount { get; set; }
        public long? HotLeadsFlagCount { get; set; }
        public long? WarmLeadsFlagCount { get; set; }
        public long? ColdLeadsFlagCount { get; set; }
        public long? AboutToConvertLeadsFlagCount { get; set; }
        public long? HighlightedLeadsFlagCount { get; set; }
    }

    public class FlagsCountDto : IDto
    {
        public string? Name { get; set; }
        public long Count { get; set; }
    }

    public class LeadStatusFilterDto
    {
        public StatusFilter StatusCount { get; set; } = new();
        public List<StatusFilter> SubStatusCount { get; set; }
    }
    public class StatusFilter
    {
        public Guid StatusId { get; set; }
        public int Count { get; set; }
    }
    public class StatusFilterDto
    {
        public Guid StatusId { get; set; }
        public int Count { get; set; }
        public Guid? BaseId { get; set; }
    }

}
