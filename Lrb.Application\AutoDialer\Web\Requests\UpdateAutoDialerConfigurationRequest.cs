﻿using Lrb.Application.AutoDialer.Web.Specs;
using Lrb.Application.Integration.Web.Specs;
using Lrb.Application.UserDetails.Web;

namespace Lrb.Application.AutoDialer.Web.Requests
{
    public class UpdateAutoDialerConfigurationRequest : IRequest<Response<bool>>
    {
        public Guid? IVROutboundConfigurationId { get; set; }
        public string? MaxCallInterval { get; set; }
        public bool? PrioritizeIntegrationLead { get; set; }
        public List<Guid>? UserIds { get; set; }
    }
    public class UpdateAutoDialerConfigurationRequestHandler : IRequestHandler<UpdateAutoDialerConfigurationRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<AutoDialerConfiguration> _autoDialerConfigRepo;
        private readonly IRepositoryWithEvents<IVROutboundConfiguration> _ivrOutbounfConfigRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetailsRepo;
        public UpdateAutoDialerConfigurationRequestHandler(IRepositoryWithEvents<AutoDialerConfiguration> autoDialerConfigRepo,
            IRepositoryWithEvents<Domain.Entities.UserDetails> userDetailsRepo,
            IRepositoryWithEvents<IVROutboundConfiguration> ivrOutbounfConfigRepo)
        {
            _autoDialerConfigRepo = autoDialerConfigRepo;
            _userDetailsRepo = userDetailsRepo;
            _ivrOutbounfConfigRepo = ivrOutbounfConfigRepo;
        }

        public async Task<Response<bool>> Handle(UpdateAutoDialerConfigurationRequest request, CancellationToken cancellationToken)
        {
            try
            {
                if (request.UserIds != null)
                {
                    var autoConfig = await _autoDialerConfigRepo.FirstOrDefaultAsync(new GetAutoDialerConfigSpec());
                    var ivrConfig = await _ivrOutbounfConfigRepo.FirstOrDefaultAsync(new GetIVROutboundConfigSpec());
                    if (ivrConfig == null)
                    {
                        return new("No Primary outbound Account was found");
                    }
                    var users = await _userDetailsRepo.ListAsync(new GetUserDetailsForAutoDailerSpec(request.UserIds));
                    users.ForEach(i =>
                    {
                        if (request.UserIds.Contains(i.UserId))
                        {
                            i.IsAutoDialerEnabled = true;
                        }
                        else
                        {
                            i.IsAutoDialerEnabled = false;
                        }
                    });
                    await _userDetailsRepo.UpdateRangeAsync(users);
                    if (autoConfig == null)
                    {
                        autoConfig = request.Adapt<AutoDialerConfiguration>();
                        autoConfig.IVROutboundConfiguration = ivrConfig;
                        await _autoDialerConfigRepo.AddAsync(autoConfig);
                    }
                    else
                    {
                        autoConfig = request.Adapt<AutoDialerConfiguration>();
                        autoConfig.IVROutboundConfiguration = ivrConfig;
                        await _autoDialerConfigRepo.UpdateAsync(autoConfig, cancellationToken);
                    }
                    return new(true);
                }
                return new(false);
            }
            catch (Exception ex)
            {
                return new(false, ex.Message);
            }
        }
    }
}
