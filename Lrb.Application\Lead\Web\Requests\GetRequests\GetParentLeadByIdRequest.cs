﻿using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.Project.Web;
using Lrb.Application.Property.Web;
using Lrb.Application.Team.Web;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Lead.Web.Requests.GetRequests
{
    public class GetParentLeadByIdRequest : IRequest<Response<ViewLeadDto>>
    {
        public Guid Id { get; set; }
        public GetParentLeadByIdRequest(Guid id) => Id = id;
    }
    public class GetParentLeadByIdRequestHandler : IRequestHandler<GetParentLeadByIdRequest, Response<ViewLeadDto>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        private readonly IUserService _userService;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;

        public GetParentLeadByIdRequestHandler(
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepo,
            IUserService userService,
            IDapperRepository dapperRepository,
            ICurrentUser currentUser)
        {
            _leadRepo = leadRepo;
            _userService = userService;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        public async Task<Response<ViewLeadDto>> Handle(GetParentLeadByIdRequest request, CancellationToken cancellationToken)
        {
            var lead = (await _leadRepo.ListAsync(new GetParentLeadByIdSpec(request.Id), cancellationToken))?.FirstOrDefault();
            if (lead == null)
            {
                return new();
            }
            string? tenantId = _currentUser.GetTenant();
            Guid userId = _currentUser.GetUserId();
            if (!(await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty)))
            {
                if (lead?.Appointments?.Any() ?? false)
                {
                    var appointmentsWithoutUniqueKey = lead.Appointments?.Where(appointment => appointment.UserId == userId)?.Where(i => i.UniqueKey == null && i.UniqueKey == default).ToList() ?? new();
                    var appointmentsWithUniqueKey = lead.Appointments?.Where(appointment => appointment.UserId == userId)?.Where(i => i.UniqueKey != null && i.UniqueKey != default)?.DistinctBy(i => i.UniqueKey).ToList() ?? new();

                    appointmentsWithoutUniqueKey.AddRange(appointmentsWithUniqueKey);
                    lead.Appointments = appointmentsWithoutUniqueKey;
                }
            }
            else
            {
                if (lead?.Appointments?.Any(i => i.UniqueKey == Guid.Empty || i.UniqueKey == null) ?? false)
                {
                    var appointmentsWithoutUniqueKey = lead.Appointments?.Where(i => i.UniqueKey == null && i.UniqueKey == default).ToList() ?? new();
                    var appointmentsWithUniqueKey = lead.Appointments?.Where(i => i.UniqueKey != null && i.UniqueKey != default)?.DistinctBy(i => i.UniqueKey).ToList() ?? new();

                    appointmentsWithoutUniqueKey.AddRange(appointmentsWithUniqueKey);
                    lead.Appointments = appointmentsWithoutUniqueKey;
                }
            }
            if (lead?.BookedDetails?.Any() ?? false)
            {
                lead.BookedDetails = lead.BookedDetails.OrderByDescending(i => i.LastModifiedOn).ToList();
                List<LeadBookedDetail> details = new();
                var book = lead.BookedDetails.FirstOrDefault();
                details.Add(book);
                lead.BookedDetails = details;

            }
            var leadDto = lead.Adapt<ViewLeadDto>();

            if (lead.BookedDetails?.FirstOrDefault()?.Properties?.Any() ?? false)
            {
                leadDto.BookedDetails.FirstOrDefault().Property = lead.BookedDetails.FirstOrDefault().Properties.FirstOrDefault().Adapt<BasicPropertyInfoDto>();
            }
            if (lead.BookedDetails?.FirstOrDefault()?.Projects?.Any() ?? false)
            {
                leadDto.BookedDetails.FirstOrDefault().Projects = lead.BookedDetails.FirstOrDefault().Projects.FirstOrDefault().Adapt<BasicProjectDto>();
            }
          //  leadDto.Appointments = leadDto.Appointments?.Where(i => i.Type != AppointmentType.None).ToList();
            var userIds = new List<string>() { (leadDto?.AssignTo ?? Guid.Empty).ToString(), (leadDto?.SecondaryUserId ?? Guid.Empty).ToString() };
            var users = await _userService.GetListOfUsersByIdsAsync(userIds, cancellationToken);
            leadDto.ContactRecords = lead.ContactRecords;
            var assignedUser = users?.FirstOrDefault(i => i.Id == (leadDto?.AssignTo ?? Guid.Empty));
            var secondaryUser = users?.FirstOrDefault(i => i.Id == (leadDto?.SecondaryUserId ?? Guid.Empty));
            if (assignedUser != null)
            {
                leadDto.AssignedUser = assignedUser.Adapt<UserDto>();
            }
            if (secondaryUser != null)
            {
                leadDto.SecondaryUser = secondaryUser.Adapt<UserDto>();
            }
            var res = (await _dapperRepository.GetLeadSourceUpdateStatusAsync(lead.Id, tenantId ?? string.Empty));
            leadDto.IsSourceUpdated = res > 1 ? true : false;

            return new Response<ViewLeadDto>(leadDto);
        }
    }
}

