﻿using Lrb.Application.CustomStatus.Mobile.Request;
using Lrb.Domain.Entities.MasterData;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.CustomStatus.Mobile.Specs
{
    public class GetAllStatusV2Spec : EntitiesByPaginationFilterSpec<CustomMasterLeadStatus>
    { 
        public GetAllStatusV2Spec(GetAllStatusV2Request filter, Guid userId) : base(filter)
        {
            Query.Where(i => !i.IsDeleted)
                .Include(i => i.CustomFields)
                .ThenInclude(i => i.Field)
                .OrderByDescending(i => i.LastModifiedOn);
            if (userId != Guid.Empty)
            {
                Query.Where(i => !i.Teams.Any() || i.Teams.Any(j => j.UserIds.Contains(userId) || j.Manager == userId));
            }
        }
    }
}
