﻿using System.Text.Json.Serialization;

namespace Lrb.Domain.Entities
{
    public class LeadBookedDetail : AuditableEntity, IAggregateRoot
    {
        public Guid LeadId { get; set; }
        [JsonIgnore]
        public Lead Lead { get; set; }
        public DateTime? BookedDate { get; set; }
        public Guid? BookedBy { get; set; }
        public string? BookedByUser { get; set; }
        public Guid? SecondaryOwner { get; set; }
        public string? BookedUnderName { get; set; }
        public Guid? UserId { get; set; }
        public string? SoldPrice { get; set; }
        public string? Notes { get; set; }
        public List<string>? ProjectsList { get; set; }
        public List<string>? PropertiesList { get; set; }
        public Guid? TeamHead { get; set; }
        public double? AgreementValue { get; set; }
        public IList<Document>? Documents { get; set; }
        public double? CarParkingCharges { get; set; }
        public double? AdditionalCharges { get; set; }
        public double? TokenAmount { get; set; }
        public TokenType PaymentMode { get; set; }
        public double? Discount { get; set; }
        public string? DiscountUnit { get; set; }
        public DiscountType DiscountMode { get; set; }
        public double? RemainingAmount { get; set; }
        public PaymentType PaymentType { get; set; }
        public bool IsBookingCompleted { get; set; }
        public Guid? LeadBrokerageInfoId { get; set; }
        [JsonIgnore]
        public LeadBrokerageInfo? BrokerageInfo { get; set; }
        [JsonIgnore]
        public IList<Property>? Properties { get; set; }
        public bool IsChoosenProperty { get; set; }
        [JsonIgnore]
        public IList<Project>? Projects { get; set; }
        public string? Currency { get; set; }
        [JsonIgnore]
        public UnitType? UnitType { get; set; }
        public List<string>? InvoiceUrl { get; set; }
    }
}
