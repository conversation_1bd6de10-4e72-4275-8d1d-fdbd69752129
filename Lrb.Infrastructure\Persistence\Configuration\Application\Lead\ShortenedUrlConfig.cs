﻿using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Lrb.Infrastructure.Persistence.Configuration.Application.Lead
{
    public class ShortenedUrlConfig : IEntityTypeConfiguration<Lrb.Domain.Entities.ShortenedUrl>
    {
        public void Configure(EntityTypeBuilder<Lrb.Domain.Entities.ShortenedUrl> builder)
        {
            builder.IsMultiTenant();
            builder.Property(s => s.Code)
               .HasMaxLength(7);
            builder.HasIndex(s => s.Code)
                   .IsUnique();
        }
    }
}
