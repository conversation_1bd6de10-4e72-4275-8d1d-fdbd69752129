﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Common.GooglePlaces;
using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web.Mappings;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.Team.Web;
using Lrb.Application.Utils;
using Lrb.Domain.Constants;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;
using PhoneNumbers;
using System.Data;
using System.Text.RegularExpressions;

namespace Lrb.Application.Lead.Web
{
    public class CreateBulkLeadRequest : IRequest<Response<BulkLeadDetail>>
    {
        public string? S3BucketKey { get; set; }
        public Dictionary<DataColumns, string>? MappedColumnsData { get; set; }
        public List<string>? UserIds { get; set; }
    }
    public class CreateBulkLeadRequestHandler : IRequestHandler<CreateBulkLeadRequest, Response<BulkLeadDetail>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        private readonly IReadRepository<MasterAreaUnit> _masterAreaUnitRepo;
        private readonly IRepositoryWithEvents<LeadEnquiry> _leadEnquiryRepo;
        private readonly IRepositoryWithEvents<Address> _addressRepo;
        private readonly IRepositoryWithEvents<MasterPropertyType> _propertyTypeRepo;
        //private readonly IRepositoryWithEvents<MasterLeadStatus> _leadStatusRepo;
        private readonly IRepositoryWithEvents<PropertyDimension> _dimensionRepo;
        private readonly IRepositoryWithEvents<LeadTag> _leadTagRepo;
        //private readonly IReadRepository<MasterLeadStatus> _masterLeadStatusRepo;
        private readonly IBlobStorageService _blobStorageService;
        private readonly IRepositoryWithEvents<LeadHistory> _leadHistoryRepo;
        private readonly IGooglePlacesService _googlePlacesService;
        private readonly IUserService _userService;
        private readonly INotificationSenderService _notificationSenderService;
        private readonly INpgsqlRepository _npgsqlRepo;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalsettingRepo;
        private readonly IJobService _hangfireService;
        private readonly ITenantIndependentRepository _repository;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectsRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Property> _propertyRepo;
        private readonly IPropertyRepository _efPropertyRepository;
        private readonly IProjectRepository _efProjectRepository;
        private readonly Serilog.ILogger _logger;
        private readonly IMasterPropertyRepository _efMasterDataPropertyRepository;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;

        public CreateBulkLeadRequestHandler(
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepo,
            //IReadRepository<MasterLeadStatus> masterLeadStatusRepo,
            IReadRepository<MasterAreaUnit> masterAreaUnitRepo,
            IRepositoryWithEvents<LeadTag> leadTagRepo,
            IRepositoryWithEvents<LeadEnquiry> leadEnquiryRepo,
            IRepositoryWithEvents<Address> addressRepo,
            IRepositoryWithEvents<MasterPropertyType> propertyTypeRepo,
            //IRepositoryWithEvents<MasterLeadStatus> leadStatusRepo,
            IRepositoryWithEvents<PropertyDimension> dimensionRepo,
            IBlobStorageService blobStorageService,
            IRepositoryWithEvents<LeadHistory> leadHistoryRepo,
            IGooglePlacesService googlePlacesService,
            IUserService userService,
            INotificationSenderService notificationSenderService,
            INpgsqlRepository npgsqlRepo,
            ICurrentUser currentUser,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalsettingRepo,
            IJobService hangfireService,
            ITenantIndependentRepository repository,
             IRepositoryWithEvents<Lrb.Domain.Entities.Project> projectsRepo,
            IRepositoryWithEvents<Domain.Entities.Property> propertyRepo,
            IProjectRepository efProjectRepository,
            IPropertyRepository efPropertyRepository,
            Serilog.ILogger logger,
            IMasterPropertyRepository efMasterDataPropertyRepository,
            ILeadRepositoryAsync leadRepositoryAsync)
        {
            _leadRepo = leadRepo;
            _masterAreaUnitRepo = masterAreaUnitRepo;
            _leadEnquiryRepo = leadEnquiryRepo;
            _addressRepo = addressRepo;
            _propertyTypeRepo = propertyTypeRepo;
            //_leadStatusRepo = leadStatusRepo;
            _dimensionRepo = dimensionRepo;
            _leadTagRepo = leadTagRepo;
            //_masterLeadStatusRepo = masterLeadStatusRepo;
            _blobStorageService = blobStorageService;
            _leadHistoryRepo = leadHistoryRepo;
            _googlePlacesService = googlePlacesService;
            _userService = userService;
            _notificationSenderService = notificationSenderService;
            _npgsqlRepo = npgsqlRepo;
            _currentUser = currentUser;
            _globalsettingRepo = globalsettingRepo;
            _hangfireService = hangfireService;
            _repository = repository;
            _projectsRepo = projectsRepo;
            _propertyRepo = propertyRepo;
            _efProjectRepository = efProjectRepository;
            _efPropertyRepository = efPropertyRepository;
            _logger = logger;
            _efMasterDataPropertyRepository = efMasterDataPropertyRepository;
            _leadRepositoryAsync = leadRepositoryAsync;
        }
        public async Task<Response<BulkLeadDetail>> Handle(CreateBulkLeadRequest request, CancellationToken cancellationToken)
        {
            var existingProperties = await _propertyRepo.ListAsync(new GetAllDistinctPropertiesSpec());
            var existingProjects = await _projectsRepo.ListAsync(new GetAllNewProjectsV2Spec());
            Dictionary<DataColumns, string> keyValues = request.MappedColumnsData;
            List<CreateBulkLeadDto> leadRequests = new();
            DataTable dataTable = new();
            _logger.Information("LeadController -> BlobStorageEntry, File: " + request.S3BucketKey);
            Stream fileStream = await _blobStorageService.GetObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", request.S3BucketKey);
            _logger.Information("LeadController -> out, File: " + request.S3BucketKey);
            if (request.S3BucketKey.Split('.').LastOrDefault() == "csv")
            {
                using MemoryStream memoryStream = new();
                fileStream.CopyTo(memoryStream);
                dataTable = CSVHelper.CSVToDataTable(memoryStream);
            }
            else
            {
                //dataTable = ExcelHelper.ExcelToDataTable(fileStream);
                dataTable = EPPlusExcelHelper.ConvertExcelToDataTable(fileStream);
            }
            int totalRows = dataTable.Rows.Count;
            for (int i = totalRows - 1; i >= 0; i--)
            {
                var row = dataTable.Rows[i];
                if (row.ItemArray.All(i => string.IsNullOrEmpty(i.ToString())))
                {
                    row.Delete();
                }
            }
            if (dataTable.Rows.Count <= 0)
            {
                throw new Exception("Excel sheet is empty. Please fill some data in the excel sheet template.");
            }
            List<string> unMappedColumns = dataTable.GetColumnNames(request.MappedColumnsData);
            leadRequests = await CreateLeadRequests(dataTable, request.MappedColumnsData, unMappedColumns);
            List<CreateBulkLeadDto> ValidleadRequests = new();
            List<InvalidData> invalidLeads = new();
            var model = new BulkLeadDetail();
            model.TotalCount = leadRequests.Count;
            leadRequests.ForEach(s =>
            {
                var multiNumbers = s.ContactNo.Contains(',') ? s.ContactNo.Split(',')
                : s.ContactNo.Contains('\\') ? s.ContactNo.Split('\\')
                : s.ContactNo.Split('/');

                if (multiNumbers.Length > 1 && !request.MappedColumnsData.ContainsKey(DataColumns.AlternateContactNo))
                {
                    s.AlternateContactNo = multiNumbers[1];
                }
                s.ContactNo = multiNumbers[0];
                if (s.ContactNo.ToLower().Contains('e'))
                {
                    if (double.TryParse(s.ContactNo.Replace("+91", ""), out double cNumber))
                    {
                        s.ContactNo = (cNumber).ToString().Split('.')[0];
                    }
                }
                s.ContactNo = Regex.Replace(s.ContactNo, @"[^0-9]+", "");
                if (s.ContactNo.Length > 1) { s.ContactNo = s.ContactNo; }; s.ContactNo = $"+91{s.ContactNo.Trim()}";
            });
            ValidleadRequests.AddRange(leadRequests.Where(i => Regex.IsMatch(i.ContactNo, RegexPatterns.IndianPhoneNumberPattern) && !string.IsNullOrEmpty(i.Name.Trim())));
            model.DistictLeadCount = ValidleadRequests.GroupBy(x => x.ContactNo).Select(x => x.First()).ToList().Count;
            var bulkLeadRequests = ValidleadRequests.GroupBy(x => x.ContactNo).Select(x => x.First()).ToList();
            List<string> contactsInRequests = bulkLeadRequests.Select(i => i.ContactNo).ToList();
            IEnumerable<Domain.Entities.Lead> existingLeads = new List<Domain.Entities.Lead>();
            existingLeads = await _leadRepo.ListAsync(new BulkLeadByContactNoSpec(contactsInRequests.Select(i => i).ToList()), cancellationToken);
            List<string> existingContacts = existingLeads?.Select(i => i.ContactNo)?.ToList();

            if (existingLeads?.Any() ?? false)
            {
                bulkLeadRequests.RemoveAll(i => existingContacts.Contains(i.ContactNo));
                leadRequests.RemoveAll(i => existingContacts.Contains(i.ContactNo));
                foreach (var exsistingLead in existingLeads)
                {
                    var invalidLead = exsistingLead.Adapt<InvalidData>();
                    invalidLead.Errors = "duplicate Lead";
                    invalidLeads.Add(invalidLead);
                }
            }
            invalidLeads.AddRange(leadRequests.Where(j => !bulkLeadRequests.Select(i => i.ContactNo).Contains(j.ContactNo)).ToList().Adapt<List<InvalidData>>());
            invalidLeads.Where(p => p.Errors == null).ToList().ForEach(p => p.Errors = $"InvalidData");
            var bulkLeads = bulkLeadRequests.Adapt<List<Domain.Entities.Lead>>();
            //var newStatus = (await _leadStatusRepo.ListAsync(cancellationToken)).Where(i => i.Status == "new");
            try
            {
                bulkLeads.ForEach(i =>
                {
                    //i.Status = newStatus?.FirstOrDefault();
                    var lead = bulkLeadRequests.FirstOrDefault(a => a.ContactNo == i.ContactNo);
                    i.Projects = GetProjectList(lead.Project, existingProjects).Result;
                    i.Properties = GetPropertyList(lead.Property, existingProperties).Result;
                   // var propertyType = _propertyTypeRepo.GetByIdAsync(lead.Enquiry.PropertyTypeId, cancellationToken).Result;
                    var propertyTypes = _propertyTypeRepo.ListAsync(new Property.Web.Specs.GetMasterPropertyTypeSpec(lead.Enquiry.PropertyTypeIds)).Result;
                    i.Enquiries.FirstOrDefault().PropertyType = propertyTypes.FirstOrDefault();
                    i.Enquiries.FirstOrDefault().PropertyTypes = propertyTypes;
                });
            }
            catch (Exception e)
            {
                var error = new LrbError()
                {
                    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                    ErrorSource = e?.Source,
                    StackTrace = e?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CreateBulkLeadRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }

            List<ViewLeadDto> viewLeadDtos = new();
            try
            {
                bulkLeads = (await _leadRepo.AddRangeAsync(bulkLeads, cancellationToken)).ToList();
            }
            catch (Exception e)
            {
                _logger.Information($"BulkLeadAdd --> addrangeException->> {JsonConvert.SerializeObject(e)}");
                var error = new LrbError()
                {
                    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                    ErrorSource = e?.Source,
                    StackTrace = e?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CreateBulkLeadRequestHandler -> Handle() -> _leadRepo.AddRangeAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            try
            {
                if (bulkLeads.Any())
                {
                    //viewLeadDtos = bulkLeads.Adapt<List<ViewLeadDto>>();
                    //List<LeadHistory> leadHistories = new();
                    //foreach (var lead in viewLeadDtos)
                    //{
                    //    var leadDto = lead.Adapt<ViewLeadDto>();
                    //    await leadDto?.SetUsersInViewLeadDtoAsync(_userService, cancellationToken);
                    //    var leadHsitory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                    //    leadHistories.Add(leadHsitory);
                    //}
                    //try
                    //{
                    //    await _leadHistoryRepo.AddRangeAsync(leadHistories);
                    //}
                    //catch (Exception e)
                    //{

                    //    _logger.Information($"BulkLeadHistoriesAdd --> addrangeException->> {JsonConvert.SerializeObject(e)}");
                    //}

                    var tenantId = _currentUser.GetTenant();

                    var tInfo = await _repository.GetTenantInfoAsync(tenantId);
                    var dto = new BulkUploadbackgroundDto()
                    {
                        UserIds = request.UserIds,
                        LeadDtos = viewLeadDtos,
                        CancellationToken = cancellationToken,
                        TenantInfoDto = tInfo,
                        CurrentUserId = _currentUser.GetUserId()
                    };

                    _hangfireService.Enqueue(() => ExcecuteBangroundJob(dto));
                }

            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CreateBulkLeadRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            model.TotalUploadedCount = bulkLeads.Count;
            if (invalidLeads.Any())
            {
                int invalidLeadCount = invalidLeads.Where(i => i.Errors == "InvalidData").Count();
                int duplicateLeadCount = invalidLeads.Where(i => i.Errors == "duplicate Lead").Count();
                byte[] bytes = CreateLeadHelper.CreateExcelData(invalidLeads).ToArray();
                string fileName = $"InvalidLeads-{DateTime.Now:yyyy_MM_dd-HH_mm_ss}.xlsx";
                string folder = "Leads";
                var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", folder, fileName, bytes);
                string fileUrl = await _blobStorageService.GetPreSignedURL(_blobStorageService?.BucketName ?? "prd-leadratblack", key);
                model.DuplicateCount = duplicateLeadCount; model.InvalidCount = invalidLeadCount; model.ExcelUrl = fileUrl;
                return new Response<BulkLeadDetail>(model);
            }
            else
            {
                return new Response<BulkLeadDetail>(model);
            }
        }

        public async Task ExcecuteBangroundJob(BulkUploadbackgroundDto dto)
        {
            List<LeadHistory> leadHistories = new();
            foreach (var lead in dto.LeadDtos)
            {
                var leadDto = lead.Adapt<ViewLeadDto>();
                await leadDto?.SetUsersInViewLeadDtoAsync(_userService, CancellationToken.None);
                var leadHsitory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                leadHistories.Add(leadHsitory);
            }
            try
            {
                await _leadHistoryRepo.AddRangeAsync(leadHistories);
            }
            catch (Exception e)
            {

                _logger.Information($"BulkLeadHistoriesAdd --> addrangeException->> {JsonConvert.SerializeObject(e)}");
                var error = new LrbError()
                {
                    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                    ErrorSource = e?.Source,
                    StackTrace = e?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CreateBulkLeadRequestHandler -> ExcecuteBangroundJob()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }


            Dictionary<string, int> userIdsWithNoOfLeadsAssigned = new();
            List<Guid> newLeadIds = new List<Guid>();
            var bulkLeadContactNumbers = dto.LeadDtos.Select(i => i.ContactNo);
            if (bulkLeadContactNumbers != null && bulkLeadContactNumbers.Any())
            {
                newLeadIds = (await _leadRepo.ListAsync(new LeadByContactNoSpec(bulkLeadContactNumbers.ToList()), dto.CancellationToken)).Select(i => i.Id).ToList();
            }
            if (dto?.UserIds?.Any() ?? false)
            {
                #region Assigning User
                try
                {
                    if (dto.UserIds != null && dto.UserIds.Count > 0)
                    {
                        var filteredUserIds = new List<string>();
                        foreach (var item in dto.UserIds)
                        {
                            if (item != null)
                            {
                                filteredUserIds.Add(item);
                                userIdsWithNoOfLeadsAssigned.Add(item, 0);
                            }
                        }
                        if (filteredUserIds.Count > 0 && newLeadIds.Any())
                        {
                            int userCount = filteredUserIds.Count;
                            int index = 0;
                            foreach (var leadId in newLeadIds)
                            {
                                if (index < userCount)
                                {
                                    await AssignLead(filteredUserIds[index], leadId, dto.CancellationToken, dto.CurrentUserId);
                                    userIdsWithNoOfLeadsAssigned[filteredUserIds[index]]++;
                                }
                                else
                                {
                                    index = 0;
                                    await AssignLead(filteredUserIds[index], leadId, dto.CancellationToken, dto.CurrentUserId);
                                    userIdsWithNoOfLeadsAssigned[filteredUserIds[index]]++;
                                }
                                index++;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    var error = new LrbError()
                    {
                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                        ErrorSource = ex?.Source,
                        StackTrace = ex?.StackTrace,
                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                        ErrorModule = "CreateBulkLeadRequestHandler -> ExcecuteBangroundJob()"
                    };
                    await _leadRepositoryAsync.AddErrorAsync(error);
                }
                #endregion
            }

            #region Notification
            if (dto.LeadDtos?.Any() ?? false)
            {
                Domain.Entities.Lead? leadForNotification = new();
                try
                {
                    leadForNotification = dto.LeadDtos?.FirstOrDefault().Adapt<Domain.Entities.Lead>();
                }
                catch (Exception ex) { }
                if (!dto?.UserIds?.Any() ?? false)
                {
                    try
                    {
                        List<Guid> adminIds = await _npgsqlRepo.GetAdminIdsAsync(dto.TenantInfoDto.Id);
                        if (adminIds != null && adminIds.Any())
                        {
                            foreach (var adminId in adminIds)
                            {
                                var adminDetails = await _userService.GetAsync(adminId.ToString(), dto.CancellationToken);
                                if (adminDetails != null)
                                {
                                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.UnAssignedLeadUpdate, leadForNotification, adminId, adminDetails.FirstName + " " + adminDetails.LastName, topics: new List<string> { leadForNotification.CreatedBy.ToString(), leadForNotification.LastModifiedBy.ToString() }, newLeadIds.Count());
                                }
                            }
                        }

                    }
                    catch (Exception ex)
                    {
                        var error = new LrbError()
                        {
                            ErrorMessage = ex.Message,
                            ErrorSource = ex.Source,
                            StackTrace = ex.StackTrace,
                            ErrorModule = "CreateBulkLeadRequestHandler -> ExcecuteBangroundJob() -> Push Notification"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                }


                //Push Notification
                foreach (var userId in userIdsWithNoOfLeadsAssigned)
                {
                    try
                    {
                        var userDetails = await _userService.GetAsync(userId.Key, dto.CancellationToken);
                        if (userDetails.Id != _currentUser.GetUserId())
                        {
                            if (userId.Value == 1)
                            {
                                var leads = await _leadRepo.ListAsync(new LeadsByIdsSpec(newLeadIds));
                                var lead = leads.Where(i => i.AssignTo.ToString() == userId.Key).FirstOrDefault();
                                List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadAssignment, lead, userDetails.Id, userDetails.FirstName + " " + userDetails.LastName, null, userId.Value);
                            }
                            else
                            {
                                List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.MultipleLeadAssignment, leadForNotification, userDetails.Id, userDetails.FirstName + " " + userDetails.LastName, null, userId.Value);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        var error = new LrbError()
                        {
                            ErrorMessage = ex.Message,
                            ErrorSource = ex.Source,
                            StackTrace = ex.StackTrace,
                            ErrorModule = "CreateBulkLeadRequestHandler -> ExcecuteBangroundJob() -> Push Notification"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                }
                #endregion

            }
        }
        private async Task<bool> AssignLead(string userId, Guid leadId, CancellationToken cancellationToken, Guid currentUserId)
        {
            try
            {
                var user = await _userService.GetAsync(userId, cancellationToken);
                if (user == null) { throw new NotFoundException("No user found by the provided user id."); }
                var lead = await _leadRepo.GetByIdAsync(leadId);
                if (lead == null) { throw new NotFoundException("No lead found by the lead ids."); }
                lead.AssignTo = new Guid(userId);
                lead.AssignedFrom = currentUserId;
                await _leadRepo.UpdateAsync(lead);
                var fullLead = (await _leadRepo.ListAsync(new LeadByIdSpec(lead.Id), cancellationToken))?.FirstOrDefault();
                var leadDto = fullLead?.Adapt<ViewLeadDto>();
                leadDto.AssignedUser = user.Adapt<UserDto>();
                var leadHsitory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                var existingLeadHistory = await _leadHistoryRepo.GetByIdAsync(leadHsitory.Id);
                if (existingLeadHistory != null)
                {
                    await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHsitory));
                }
                else
                {
                    await _leadHistoryRepo.AddAsync(leadHsitory);
                }
                //try
                //{
                //    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadAssignment, lead, leadDto?.AssignTo, leadDto?.AssignedUser?.Name, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() });
                //}
                //catch (Exception ex) { }
                return true;
            }
            catch (Exception e)
            {
                var error = new LrbError()
                {
                    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                    ErrorSource = e?.Source,
                    StackTrace = e?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CreateBulkLeadRequestHandler -> AssignLead()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                return false;
            }
        }
        private bool ValidateContactNumber(string phoneNumber, out string result)
        {
            result = "";
            try
            {
                result = Regex.Replace(phoneNumber, @"[^\w\+]", "");
                try
                {
                    result = $"+91{result.Substring(result.Length - 10)}";

                }
                catch { }
                var phoneUtil = PhoneNumberUtil.GetInstance();
                PhoneNumber number = new();
                try
                {
                    number = phoneUtil.Parse(result, "");
                }
                catch (Exception e)
                {
                }
                try
                {
                    return phoneUtil.IsValidNumber(number);

                }
                catch { return false; }
            }
            catch (Exception ex) 
            {
                return false; 
            }

        }

        public async Task<List<CreateBulkLeadDto>> CreateLeadRequests1(DataTable dataTable, Dictionary<DataColumns, string> keyValues, List<string> unMappedColumns)
        {

            List<CreateBulkLeadDto> leadRequests = new();
            foreach (DataRow row in dataTable.Rows)
            {
                //if (row.ItemArray.All(i => string.IsNullOrEmpty(i.ToString())))
                //{
                //    row.Delete();
                //}
                //else
                // {
                var leadrequest = new CreateBulkLeadDto();

                leadrequest.Name = !keyValues.ContainsKey(DataColumns.Name) || string.IsNullOrEmpty(keyValues[DataColumns.Name]) ? string.Empty : row[keyValues[DataColumns.Name]].ToString();
                leadrequest.ContactNo = !keyValues.ContainsKey(DataColumns.ContactNo) || string.IsNullOrEmpty(keyValues[DataColumns.ContactNo]) ? string.Empty : row[keyValues[DataColumns.ContactNo]].ToString();
                leadrequest.Email = !keyValues.ContainsKey(DataColumns.Email) || string.IsNullOrEmpty(keyValues[DataColumns.Email]) ? string.Empty : row[keyValues[DataColumns.Email]].ToString();
                leadrequest.Rating = !keyValues.ContainsKey(DataColumns.Rating) || string.IsNullOrEmpty(keyValues[DataColumns.Rating]) ? string.Empty : row[keyValues[DataColumns.Rating]].ToString();
                leadrequest.Enquiry = new();

                leadrequest.Enquiry.LeadSource = !keyValues.ContainsKey(DataColumns.Source) || string.IsNullOrEmpty(keyValues[DataColumns.Source]) ? default : Enum.TryParse<LeadSource>(row[keyValues[DataColumns.Source]].ToString(), true, out var leadsource) ? leadsource : default;
                leadrequest.Enquiry.EnquiredFor = !keyValues.ContainsKey(DataColumns.EnquiredFor) || string.IsNullOrEmpty(keyValues[DataColumns.EnquiredFor]) ? default : Enum.TryParse<EnquiryType>(row[keyValues[DataColumns.EnquiredFor]].ToString(), true, out var enquiryType) ? enquiryType : default;
                leadrequest.Enquiry.BHKType = !keyValues.ContainsKey(DataColumns.BHKType) || string.IsNullOrEmpty(keyValues[DataColumns.BHKType]) ? default : Enum.TryParse<BHKType>(row[keyValues[DataColumns.BHKType]].ToString(), true, out var bHKType) ? bHKType : default;
                leadrequest.Enquiry.PropertyTypeId = !keyValues.ContainsKey(DataColumns.BasePropertyType) || string.IsNullOrEmpty(keyValues[DataColumns.BasePropertyType]) || !keyValues.ContainsKey(DataColumns.SubPropertyType) || string.IsNullOrEmpty(keyValues[DataColumns.SubPropertyType]) ? default : await GetPropertyTypeId(row[keyValues[DataColumns.BasePropertyType]].ToString(), row[keyValues[DataColumns.SubPropertyType]].ToString());
                leadrequest.Enquiry.NoOfBHK = !keyValues.ContainsKey(DataColumns.NoOfBHK) || string.IsNullOrEmpty(keyValues[DataColumns.NoOfBHK]) ? default : CreateLeadHelper.GetNoOfBHK(row[keyValues[DataColumns.NoOfBHK]].ToString());
                leadrequest.Enquiry.LowerBudget = !keyValues.ContainsKey(DataColumns.Budget) || string.IsNullOrEmpty(keyValues[DataColumns.Budget]) ? 0 : string.IsNullOrEmpty(row[keyValues[DataColumns.Budget]].ToString()) ? default : BudgetHelper.ConvertBugetV2(row[keyValues[DataColumns.Budget]].ToString());
                leadrequest.Enquiry.UpperBudget = !keyValues.ContainsKey(DataColumns.Budget) || string.IsNullOrEmpty(keyValues[DataColumns.Budget]) ? 0 : string.IsNullOrEmpty(row[keyValues[DataColumns.Budget]].ToString()) ? default : BudgetHelper.ConvertBugetV2(row[keyValues[DataColumns.Budget]].ToString());
                leadrequest.Enquiry.Address = new()
                {
                    City = !keyValues.ContainsKey(DataColumns.City) || string.IsNullOrEmpty(keyValues[DataColumns.City]) ? string.Empty : row[keyValues[DataColumns.City]].ToString(),
                    State = !keyValues.ContainsKey(DataColumns.State) || string.IsNullOrEmpty(keyValues[DataColumns.State]) ? string.Empty : row[keyValues[DataColumns.State]].ToString(),
                    SubLocality = !keyValues.ContainsKey(DataColumns.Location) || string.IsNullOrEmpty(keyValues[DataColumns.Location]) ? string.Empty : row[keyValues[DataColumns.Location]].ToString(),
                };
                //var project = keyValues.ContainsKey(DataColumns.Project);
                //var unmapped = unMappedColumns.Any(s => s.Contains(DataColumns.Project.ToString(), StringComparison.OrdinalIgnoreCase));
                leadrequest.Project = !keyValues.ContainsKey(DataColumns.Project) || string.IsNullOrEmpty(keyValues[DataColumns.Project]) ? string.Empty : row[keyValues[DataColumns.Project]].ToString();
                leadrequest.Property = !keyValues.ContainsKey(DataColumns.Property) || string.IsNullOrEmpty(keyValues[DataColumns.Property]) ? string.Empty : row[keyValues[DataColumns.Property]].ToString();
                leadrequest.AlternateContactNo = !keyValues.ContainsKey(DataColumns.AlternateContactNo) || string.IsNullOrEmpty(keyValues[DataColumns.AlternateContactNo]) ? string.Empty : row[keyValues[DataColumns.AlternateContactNo]].ToString();
                //var ii = keyValues.ContainsKey(DataColumns.Notes);
                //var aa = string.IsNullOrEmpty(row[DataColumns.Notes.ToString()].ToString());
                //var aaa = unMappedColumns.Any();
                //var abc = row[keyValues[DataColumns.Notes]].ToString();
                leadrequest.Notes = unMappedColumns.Any() ? string.Join(", \n", unMappedColumns.Select(column => !string.IsNullOrEmpty(row[column].ToString()) ? column + " - " + row[column] : null).Where(i => i != null)) : string.Empty;
                leadRequests.Add(leadrequest);
            };
            return leadRequests;
        }


        public async Task<List<CreateBulkLeadDto>> CreateLeadRequests(DataTable dataTable, Dictionary<DataColumns, string> keyValues, List<string> unMappedColumns)
        {
            List<CreateBulkLeadDto> leadRequests = new();
            Parallel.ForEach<DataRow>(dataTable.Rows.Cast<DataRow>(), row =>
            {

                leadRequests.Add(new CreateBulkLeadDto
                {
                    Name = !keyValues.ContainsKey(DataColumns.Name) || string.IsNullOrEmpty(keyValues[DataColumns.Name]) ? string.Empty : row[keyValues[DataColumns.Name]].ToString(),
                    ContactNo = !keyValues.ContainsKey(DataColumns.ContactNo) || string.IsNullOrEmpty(keyValues[DataColumns.ContactNo]) ? string.Empty : row[keyValues[DataColumns.ContactNo]].ToString(),
                    Email = !keyValues.ContainsKey(DataColumns.Email) || string.IsNullOrEmpty(keyValues[DataColumns.Email]) ? string.Empty : row[keyValues[DataColumns.Email]].ToString(),
                    Rating = !keyValues.ContainsKey(DataColumns.Rating) || string.IsNullOrEmpty(keyValues[DataColumns.Rating]) ? string.Empty : row[keyValues[DataColumns.Rating]].ToString(),
                    Enquiry = new()
                    {
                        EnquiredFor = !keyValues.ContainsKey(DataColumns.EnquiredFor) || string.IsNullOrEmpty(keyValues[DataColumns.EnquiredFor]) ? default : Enum.TryParse<EnquiryType>(row[keyValues[DataColumns.EnquiredFor]].ToString(), true, out var enquiryType) ? enquiryType : default,
                        BHKType = !keyValues.ContainsKey(DataColumns.BHKType) || string.IsNullOrEmpty(keyValues[DataColumns.BHKType]) ? default : Enum.TryParse<BHKType>(row[keyValues[DataColumns.BHKType]].ToString(), true, out var bHKType) ? bHKType : default,
                        LeadSource = !keyValues.ContainsKey(DataColumns.Source) || string.IsNullOrEmpty(keyValues[DataColumns.Source]) ? default : Enum.TryParse<LeadSource>(row[keyValues[DataColumns.Source]].ToString(), true, out var leadsource) ? leadsource : default,
                        PropertyTypeId = (!keyValues.ContainsKey(DataColumns.BasePropertyType) || string.IsNullOrEmpty(keyValues[DataColumns.BasePropertyType]) || !keyValues.ContainsKey(DataColumns.SubPropertyType) || string.IsNullOrEmpty(keyValues[DataColumns.SubPropertyType])) ? default : GetPropertyTypeId(row[keyValues[DataColumns.BasePropertyType]].ToString(), row[keyValues[DataColumns.SubPropertyType]].ToString()).Result,
                        LowerBudget = !keyValues.ContainsKey(DataColumns.Budget) || string.IsNullOrEmpty(keyValues[DataColumns.Budget]) ? 0 : string.IsNullOrEmpty(row[keyValues[DataColumns.Budget]].ToString()) ? default : BudgetHelper.ConvertBugetV2(row[keyValues[DataColumns.Budget]].ToString()),
                        UpperBudget = !keyValues.ContainsKey(DataColumns.Budget) || string.IsNullOrEmpty(keyValues[DataColumns.Budget]) ? 0 : string.IsNullOrEmpty(row[keyValues[DataColumns.Budget]].ToString()) ? default : BudgetHelper.ConvertBugetV2(row[keyValues[DataColumns.Budget]].ToString()),
                        NoOfBHK = !keyValues.ContainsKey(DataColumns.NoOfBHK) || string.IsNullOrEmpty(keyValues[DataColumns.NoOfBHK]) ? default : CreateLeadHelper.GetNoOfBHK(row[keyValues[DataColumns.NoOfBHK]].ToString()),
                        Address = new()
                        {
                            City = !keyValues.ContainsKey(DataColumns.City) || string.IsNullOrEmpty(keyValues[DataColumns.City]) ? string.Empty : row[keyValues[DataColumns.City]].ToString(),
                            State = !keyValues.ContainsKey(DataColumns.State) || string.IsNullOrEmpty(keyValues[DataColumns.State]) ? string.Empty : row[keyValues[DataColumns.State]].ToString(),
                            SubLocality = !keyValues.ContainsKey(DataColumns.Location) || string.IsNullOrEmpty(keyValues[DataColumns.Location]) ? string.Empty : row[keyValues[DataColumns.Location]].ToString(),
                        }
                    },
                    Project = !keyValues.ContainsKey(DataColumns.Project) || string.IsNullOrEmpty(keyValues[DataColumns.Project]) ? default : row[keyValues[DataColumns.Project]].ToString(),
                    Property = !keyValues.ContainsKey(DataColumns.Property) || string.IsNullOrEmpty(keyValues[DataColumns.Property]) ? default : row[keyValues[DataColumns.Property]].ToString(),
                    AlternateContactNo = !keyValues.ContainsKey(DataColumns.AlternateContactNo) || string.IsNullOrEmpty(keyValues[DataColumns.AlternateContactNo]) ? string.Empty : row[keyValues[DataColumns.AlternateContactNo]].ToString(),
                    Notes = unMappedColumns.Any() ? string.Join(", \n", unMappedColumns.Select(column => !string.IsNullOrEmpty(row[column].ToString()) ? column + " - " + row[column] : null).Where(i => i != null)) : string.Empty
                });

            });
            return leadRequests;
        }
        private async Task<List<Lrb.Domain.Entities.Project>?> GetProjectList(string project, List<Lrb.Domain.Entities.Project> existingProjects)
        {
            if (string.IsNullOrEmpty(project))
            {
                return null;
            }
            List<Lrb.Domain.Entities.Project> projectsList = new();
            var projects = project.Split(',');
            var existingProjectsNames = existingProjects.Select(i => i.Name?.ToLower()).ToList();

            foreach (var newProject in projects.Distinct())
            {
                if (existingProjectsNames.Contains(newProject.ToLower()))
                {
                    var existingProject = existingProjects.FirstOrDefault(i => i.Name.ToLower() == newProject.ToLower());
                    if (existingProject != null)
                    {
                        projectsList.Add(existingProject);
                    }
                }
                else
                {
                    Domain.Entities.Project tempProject = new() { Name = newProject.Trim() };
                    try
                    {
                        await _efProjectRepository.AddAsync(tempProject);
                    }
                    catch (Exception ex) 
                    {
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "CreateBulkLeadRequestHandler -> GetProjectList()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                    projectsList.Add(tempProject);
                }
            }
            return projectsList;
        }
        private async Task<List<Domain.Entities.Property>?> GetPropertyList(string property, List<Lrb.Domain.Entities.Property> existingProperties)
        {
            if (string.IsNullOrEmpty(property))
            {
                return null;
            }
            List<Domain.Entities.Property> propertys = new();
            var properties = property.Split(',');
            var existingPropertyNames = existingProperties.Select(i => i.Title?.ToLower()).ToList();
            foreach (var newProperty in properties.Distinct())
            {
                if (existingPropertyNames.Contains(newProperty.ToLower()))
                {
                    var existingProperty = existingProperties.FirstOrDefault(i => i.Title?.ToLower() == newProperty.ToLower());
                    if (existingProperty != null)
                    {
                        propertys.Add(existingProperty);
                    }
                }
                else
                {
                    Domain.Entities.Property newProp = new() { Title = newProperty };
                    try
                    {
                        await _efPropertyRepository.AddAsync(newProp);

                    }
                    catch (Exception ex) { }
                    propertys.Add(newProp);
                }
            }
            return propertys;

        }

        private async Task<Guid> GetPropertyTypeId(string? basepropertyTyp, string? subPropertyType)
        {
            if (string.IsNullOrEmpty(basepropertyTyp) && string.IsNullOrEmpty(subPropertyType))
            {
                return Guid.Empty;
            }
            string subProperty = string.Empty;
            string baseProperty = string.Empty;

            if (!string.IsNullOrEmpty(subPropertyType))
            {
                subProperty = subPropertyType;
            }
            else
            {
                if (!string.IsNullOrEmpty(basepropertyTyp))
                {
                    if (basepropertyTyp.ToLower().Contains("residential"))
                    {
                        subProperty = "flat";
                    }
                    if (basepropertyTyp.ToLower().Contains("commercial"))
                    {
                        subProperty = "Plot";
                    }
                    if (basepropertyTyp.ToLower().Contains("agricultural"))
                    {
                        subProperty = "land";
                    }
                }
            }

            var propertyId = await _efMasterDataPropertyRepository.GetPropertyTypeIdAsync(basepropertyTyp, subProperty);
            return propertyId;
        }
    }

}

