﻿using Lrb.Application.Agency.Web.Requests;
using Lrb.Application.ChannelPartner.Web.Request;
using Lrb.Shared.Extensions;

namespace Lrb.Application.ChannelPartner.Web.Specs
{
    public class GetAllChannelPartnerSpecs : Specification<Lrb.Domain.Entities.ChannelPartner>
    {
        public GetAllChannelPartnerSpecs()
        {
            Query.Where(i => !i.IsDeleted && !string.IsNullOrEmpty(i.FirmName)).Include(i => i.Address);
        }
        public GetAllChannelPartnerSpecs(Guid id)
        {
            Query.Where(i => !i.IsDeleted && !string.IsNullOrEmpty(i.FirmName) && id == i.Id).Include(i => i.Address);
        }
    }

    public class GetChannelPartnerSpecs : Specification<Lrb.Domain.Entities.ChannelPartner>
    {

        public GetChannelPartnerSpecs(List<string> firmNames)
        {
            Query.Where(i => !i.IsDeleted && !string.IsNullOrWhiteSpace(i.FirmName) && i.FirmName.Equals(firmNames))
     .Include(i => i.Address);
        }

    }
    public class GetCpCountBySpec : EntitiesByPaginationFilterSpec<Domain.Entities.ChannelPartner>
    {
        public GetCpCountBySpec(GetChannelPartnersRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted && !string.IsNullOrWhiteSpace(i.FirmName))
                 .Include(i => i.Address);
         /*   if (filter?.SearchByName?.Any() ?? false)
            {
                filter.SearchByName = filter.SearchByName.Select(i => i.ToLower().Trim()).ToList();
                Query.Where(i => filter.SearchByName.Contains(i.FirmName.ToLower().Trim()));
            }*/
        }


    }


    public class GetChannelPartnersByIdsSpec : Specification<Domain.Entities.ChannelPartner>
    {

        public GetChannelPartnersByIdsSpec(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted && ids.Contains(i.Id))
               .Include(i => i.Address);
        }
    }

    public class DeleteChannerlPartnerSpec : Specification<Domain.Entities.ChannelPartner>
    {
        public DeleteChannerlPartnerSpec(List<Guid> channelPartnerIds)
        {

            Query.Where(i => channelPartnerIds.Contains(i.Id) && !i.IsDeleted);

        }
    }

    public class DeleteLeadsForChannelPartnersSpec : Specification<Domain.Entities.Lead>
    {
        public DeleteLeadsForChannelPartnersSpec(Guid channelPartnerId)
        {
            //Query.Where(i => agencyIds.Contains(i.Id) && !i.IsDeleted).Include(i => i.Address).Include(i => i.Leads).Include(i => i.Prospects);
            Query.Where(i => !i.IsDeleted && i.ChannelPartners.Any(a => a.Id == channelPartnerId)).Include(i => i.Agencies);
            Query.Where(i => !i.IsDeleted)
                .Include(i => i.Agencies)
                .Include(i => i.ChannelPartners)
                .Include(i => i.CustomLeadStatus)
                .Where(i => !i.CustomLeadStatus.IsDeleted)
                 .Include(i => i.TagInfo)
                .Include(i => i.Enquiries)
                    .ThenInclude(i => i.Addresses)
                        .ThenInclude(i => i.Location)
                            .ThenInclude(i => i.Zone)
                .Include(i => i.Enquiries)
                    .ThenInclude(i => i.Addresses)
                        .ThenInclude(i => i.Location)
                            .ThenInclude(i => i.City)
                .Include(i => i.Enquiries)
                    .ThenInclude(i => i.PropertyType)
                .Include(i => i.Appointments)
                .ThenInclude(i => i.Location)
                 .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyTypes);
        }
        public DeleteLeadsForChannelPartnersSpec(List<Guid> channelPartnerIds)
        {
            //Query.Where(i => agencyIds.Contains(i.Id) && !i.IsDeleted).Include(i => i.Address).Include(i => i.Leads).Include(i => i.Prospects);
            Query.Where(i => !i.IsDeleted && i.Agencies.Any(a => channelPartnerIds.Contains(a.Id))).Include(i => i.Agencies);
            Query.Where(i => !i.IsDeleted)
                .Include(i => i.Agencies)
                .Include(i => i.CustomLeadStatus)
                .Where(i => !i.CustomLeadStatus.IsDeleted)
                 .Include(i => i.TagInfo)
                .Include(i => i.Enquiries)
                    .ThenInclude(i => i.Addresses)
                        .ThenInclude(i => i.Location)
                            .ThenInclude(i => i.Zone)
                .Include(i => i.Enquiries)
                    .ThenInclude(i => i.Addresses)
                        .ThenInclude(i => i.Location)
                            .ThenInclude(i => i.City)
                .Include(i => i.Enquiries)
                    .ThenInclude(i => i.PropertyType)
                .Include(i => i.Appointments)
                .ThenInclude(i => i.Location)
                 .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyTypes);
        }
    }


    public class DeleteProspectsForChannelPartnersSpec : Specification<Domain.Entities.Prospect>
    {
        public DeleteProspectsForChannelPartnersSpec(Guid channelPartnerId)
        {
            Query.Where(i => !i.IsDeleted && i.ChannelPartners.Any(a => a.Id == channelPartnerId))
              .Include(i => i.Agencies)
              .Include(i => i.ChannelPartners)
              .Include(i => i.Address)
              .ThenInclude(i => i.Location)
              .ThenInclude(i => i.Zone)
              .ThenInclude(i => i.City)
              .Include(i => i.Enquiries)
              .ThenInclude(i => i.Addresses)
              .ThenInclude(i => i.Location)
              .ThenInclude(i => i.Zone)
              .ThenInclude(i => i.City)
              .Include(i => i.Enquiries)
              .ThenInclude(i => i.PropertyType)
              .Include(i => i.Enquiries)
              .ThenInclude(i => i.Source)
              .Include(i => i.ChannelPartners)
              .Include(i => i.Projects)
              .Include(i => i.Properties)
              .Include(i => i.Status)
              .Include(i => i.Agencies)
              .Include(i => i.Enquiries)
              .ThenInclude(i => i.PropertyTypes);
        }

        public DeleteProspectsForChannelPartnersSpec(List<Guid> channelPartnerIds)
        {
            Query.Where(i => !i.IsDeleted && i.Agencies.Any(a => channelPartnerIds.Contains(a.Id))).Include(i => i.Agencies);
            Query.Where(i => !i.IsDeleted)
              .Include(i => i.Agencies)
             .Include(i => i.Address)
              .ThenInclude(i => i.Location)
              .ThenInclude(i => i.Zone)
              .ThenInclude(i => i.City)
              .Include(i => i.Enquiries)
              .ThenInclude(i => i.Addresses)
              .ThenInclude(i => i.Location)
              .ThenInclude(i => i.Zone)
              .ThenInclude(i => i.City)
              .Include(i => i.Enquiries)
              .ThenInclude(i => i.PropertyType)
              .Include(i => i.Enquiries)
              .ThenInclude(i => i.Source)
              .Include(i => i.ChannelPartners)
              .Include(i => i.Projects)
              .Include(i => i.Properties)
              .Include(i => i.Status)
              .Include(i => i.Agencies)
              .Include(i => i.Enquiries)
              .ThenInclude(i => i.PropertyTypes);
        }
    }

    public class ChannelPartnerAffiliatedCountFiltersSpec : Specification<Domain.Entities.ChannelPartner>
    {
        public ChannelPartnerAffiliatedCountFiltersSpec(GetChannelPartnersRequest request, List<Guid> ids)
        {
            var cps = request.SearchByName?.Select(name => name.Replace(" ", "").ToLower()).ToArray();
            var searchText = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText;
            var dateType = (int?)request.DateType;
            var localities = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize());
            var cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize());
            var states = request?.States?.ConvertAll<string>(i => i.LrbNormalize());
            var locations = request?.Location?.ConvertAll<string>(i => i.LrbNormalize());
            var fromDate = request.FromDate;
            var toDate = request.ToDate;
            Query.Where(a => !a.IsDeleted && ids.Contains(a.Id)).Include(i => i.Address).Include(i => i.Projects);
            /*   if (agencies.Any())
               {
                   Query.Where(a => agencies.Contains(a.Name.ToLower().Replace(" ", "")));
               }*/
            if (!string.IsNullOrEmpty(searchText))
            {
                Query.Where(a =>
                    a.FirmName.ToLower().Contains(searchText.ToLower()) ||
                    a.ContactNo.ToLower().Contains(searchText.ToLower()));
            }
            if (fromDate.HasValue && toDate.HasValue)
            {
                if (dateType == 0)
                {
                    Query.Where(a => (a.CreatedOn >= fromDate && a.CreatedOn <= toDate) ||
                                     (a.LastModifiedOn >= fromDate && a.LastModifiedOn <= toDate));
                }
                else if (dateType == 1)
                {
                    Query.Where(a => a.CreatedOn >= fromDate && a.CreatedOn <= toDate);
                }
                else if (dateType == 2)
                {
                    Query.Where(a => a.LastModifiedOn >= fromDate && a.LastModifiedOn <= toDate);
                }
            }
            if (localities?.Any() ?? false)
            {
                Query.Where(a => localities.Contains(a.Address.Locality));
            }

            if (cities?.Any() ?? false)
            {
                Query.Where(a => cities.Contains(a.Address.City));
            }

            if (states?.Any() ?? false)
            {
                Query.Where(a => states.Contains(a.Address.State));
            }
            if (locations != null && locations.Any())
            {
                locations = locations.Select(i => i.Replace(",", "").ToLower().Trim().Replace(" ", "")).ToList();
                Query.Where(i => locations.Contains(
                    (i.Address.SubLocality + i.Address.Locality + i.Address.District +
                     i.Address.City + i.Address.State + i.Address.Country + i.Address.PostalCode)
                    .ToLower().Trim().Replace(" ", "").Replace(",", "")
                ));
            }
            if (request.CreatedByIds?.Any() ?? false)
            {
                Query.Where(i => request.CreatedByIds.Contains(i.CreatedBy));
            }
            if(request.Projects?.Any() ?? false)
            {
                var projectNames = request.Projects.ConvertAll(i => i.ToLower().Trim());
                Query.Where(i => i.Projects.Any(i => projectNames.Contains(i.Name.ToLower())));
            }
            var a = Query.OrderByDescending(a => a.LastModifiedOn)
              .Skip(request.PageSize * (request.PageNumber - 1))
              .Take(request.PageSize);

        }
    }
    public class ExportChannelPartnerByFiltersSpec : Specification<Domain.Entities.ChannelPartner>
    {
        public ExportChannelPartnerByFiltersSpec(RunAWSBatchForMarketingChannelPartnerRequest request, List<Guid> ids)
        {
            var cps = request.SearchByName?.Select(name => name.Replace(" ", "").ToLower()).ToArray();
            var searchText = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText;
            var dateType = (int?)request.DateType;
            var localities = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize());
            var cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize());
            var states = request?.States?.ConvertAll<string>(i => i.LrbNormalize());
            var locations = request?.Location?.ConvertAll<string>(i => i.LrbNormalize());
            var fromDate = request.FromDate;
            var toDate = request.ToDate;
            Query.Where(a => !a.IsDeleted && ids.Contains(a.Id)).Include(i => i.Address);
            /*   if (agencies.Any())
               {
                   Query.Where(a => agencies.Contains(a.Name.ToLower().Replace(" ", "")));
               }*/
            if (!string.IsNullOrEmpty(searchText))
            {
                Query.Where(a =>
                    a.FirmName.ToLower().Contains(searchText.ToLower()) ||
                    a.ContactNo.ToLower().Contains(searchText.ToLower()));
            }
            if (fromDate.HasValue && toDate.HasValue)
            {
                if (dateType == 0)
                {
                    Query.Where(a => (a.CreatedOn >= fromDate && a.CreatedOn <= toDate) ||
                                     (a.LastModifiedOn >= fromDate && a.LastModifiedOn <= toDate));
                }
                else if (dateType == 1)
                {
                    Query.Where(a => a.CreatedOn >= fromDate && a.CreatedOn <= toDate);
                }
                else if (dateType == 2)
                {
                    Query.Where(a => a.LastModifiedOn >= fromDate && a.LastModifiedOn <= toDate);
                }
            }
            if (localities?.Any() ?? false)
            {
                Query.Where(a => localities.Contains(a.Address.Locality));
            }

            if (cities?.Any() ?? false)
            {
                Query.Where(a => cities.Contains(a.Address.City));
            }

            if (states?.Any() ?? false)
            {
                Query.Where(a => states.Contains(a.Address.State));
            }
            if (request.CreatedByIds?.Any() ?? false)
            {
                Query.Where(i => request.CreatedByIds.Contains(i.CreatedBy));
            }
            if (locations != null && locations.Any())
            {
                locations = locations.Select(i => i.Replace(",", "").ToLower().Trim().Replace(" ", "")).ToList();
                Query.Where(i => locations.Contains(
                    (i.Address.SubLocality + i.Address.Locality + i.Address.District +
                     i.Address.City + i.Address.State + i.Address.Country + i.Address.PostalCode)
                    .ToLower().Trim().Replace(" ", "").Replace(",", "")
                ));
            }
            var a = Query.OrderByDescending(a => a.LastModifiedOn)
              .Skip(request.PageSize * (request.PageNumber - 1))
              .Take(request.PageSize);

        }
    }

}
