﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.Property.Web;
using Lrb.Application.Utils;
using Lrb.Domain.Entities.ErrorModule;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using Serilog;

namespace Lrb.Application.Project.Web.Requests.Bulk_Upload
{
    public class GetExcelColumnsUsingEPPlusRequest : IRequest<Response<FileColumnDto>>
    {
        public IFormFile? File { get; set; }
        public GetExcelColumnsUsingEPPlusRequest(IFormFile? file)
        {
            File = file;
        }
    }
    public class GetExcelColumnsUsingEPPlusRequestHandler : IRequestHandler<GetExcelColumnsUsingEPPlusRequest, Response<FileColumnDto>>
    {
        private readonly IBlobStorageService _blobStorageService;
        private readonly ILogger _logger;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;

        public GetExcelColumnsUsingEPPlusRequestHandler(IBlobStorageService blobStorageService,
            Serilog.ILogger logger, ILeadRepositoryAsync leadRepositoryAsync)
        {
            _blobStorageService = blobStorageService;
            _logger = logger;
            _leadRepositoryAsync = leadRepositoryAsync;
        }
        public async Task<Response<FileColumnDto>> Handle(GetExcelColumnsUsingEPPlusRequest request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.Information("GetExcelColumnsUsingEPPlusRequestHandler -> Request: " + JsonConvert.SerializeObject(request));
                var file = request.File;
                if (file == null) { throw new ArgumentNullException(nameof(file)); }
                string fileExtension = string.Empty;
                if (!ExcelHelper.IsValidFile(file, out fileExtension))
                {
                    throw new InvalidOperationException("File format is invalid");
                }
                var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", "Project", file);

                List<string> columns = new List<string>();
                Dictionary<string, List<string>> multiSheetColumns = new();
                if (key.Split('.').LastOrDefault() == "csv")
                {
                    columns = await CSVHelper.GetCSVColumns(file);
                    multiSheetColumns.Add("Default", columns);
                }
                else
                {
                    columns = Lead.Web.Requests.Bulk_upload_new_implementation.EPPlusExcelHelper.GetFileColumns(file);
                    multiSheetColumns = Lead.Web.Requests.Bulk_upload_new_implementation.EPPlusExcelHelper.GetFileColumnsOfMultiSheets(file);
                }
                FileColumnDto excelColumnsViewModel = new()
                {
                    S3BucketKey = key,
                    ColumnNames = columns,
                    MultiSheetColumnNames = multiSheetColumns,
                };
                return new Response<FileColumnDto>(excelColumnsViewModel);
            }
            catch (Exception e)
            {
                _logger.Error("GetExcelColumnsUsingEPPlusRequestHandler -> Error: " + JsonConvert.SerializeObject(e));
                var error = new LrbError()
                {
                    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                    ErrorSource = e?.Source,
                    StackTrace = e?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "GetExcelColumnsUsingEPPlusRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
        }
    }
}
