﻿using DocumentFormat.OpenXml.Office2010.Excel;
using Lrb.Application.DailyStatusUpdates.Mappings;
using Lrb.Application.Integration.Web;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Mappings;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.MasterData;
using Lrb.Application.Project.Web.Specs;
using Lrb.Application.Property.Web.Specs;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Microsoft.Graph;
using Newtonsoft.Json;

namespace Lrb.Application.Lead.Web.Requests.UpdationRequests
{
    public class UpdateBookedDetailsRequest : UpdateBookedLeadDto, IRequest<Response<Guid>>
    {
    }
    public class UpdateBookedDetailsRequestHandler : LeadCommonRequestHandler, IRequestHandler<UpdateBookedDetailsRequest, Response<Guid>>
    {
        private readonly IRepositoryWithEvents<LeadBookedDetail> _leadBookedDetailRepo;
        private readonly IRepositoryWithEvents<LeadBrokerageInfo> _brokerageDetailRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        protected readonly ILeadRepositoryAsync _leadRepositoryAsync;
        protected readonly ICurrentUser _currentUserRepo;
        private readonly IRepositoryWithEvents<LeadHistory> _leadHistoryRepo;
        private readonly IReadRepository<Domain.Entities.Property> _propertyRepository;
        public readonly IReadRepository<Domain.Entities.Project> _projectRepo;
        protected readonly IRepositoryWithEvents<UnitType> _unitType;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customMasterLeadStatusRepo;
        private readonly IRepositoryWithEvents<MasterLeadStatus> _masterLeadStatusRepo;
        private readonly IRepositoryWithEvents<LeadHistoryHot> _newLeadHistoryRepo;
        public UpdateBookedDetailsRequestHandler(
            IRepositoryWithEvents<LeadBookedDetail> leadBookedDetailRepo,
            IRepositoryWithEvents<LeadBrokerageInfo> brokerageDetailRepo,
            ILeadRepositoryAsync leadRepositoryAsync,
            ICurrentUser currentUserRepo,
            IRepositoryWithEvents<LeadHistory> leadHistoryRepo,
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepo,
            IReadRepository<Domain.Entities.Property> propertyRepository,
            IReadRepository<Domain.Entities.Project> projectRepo,
            IRepositoryWithEvents<UnitType> unitType,
            IRepositoryWithEvents<CustomMasterLeadStatus> customMasterLeadStatusRepo,
            IRepositoryWithEvents<MasterLeadStatus> masterLeadStatusRepo,
            IServiceProvider serviceProvider,
            IRepositoryWithEvents<LeadHistoryHot> newLeadHistoryRepo) : base(serviceProvider, typeof(UpdateBookedDetailsRequestHandler).Name, "Handle")
        {
            _leadBookedDetailRepo = leadBookedDetailRepo;
            _leadRepositoryAsync = leadRepositoryAsync;
            _currentUserRepo = currentUserRepo;
            _leadHistoryRepo = leadHistoryRepo;
            _leadRepo = leadRepo;
            _propertyRepository = propertyRepository;
            _projectRepo = projectRepo;
            _brokerageDetailRepo = brokerageDetailRepo;
            _unitType = unitType;
            _customMasterLeadStatusRepo = customMasterLeadStatusRepo;
            _masterLeadStatusRepo = masterLeadStatusRepo;
            _newLeadHistoryRepo = newLeadHistoryRepo;
        }
        public async Task<Response<Guid>> Handle(UpdateBookedDetailsRequest request, CancellationToken cancellationToken)
        {
            var bookedDetail = (await _leadBookedDetailRepo.FirstOrDefaultAsync(new GetBookedDetailsByIdSpec(request.LeadId), cancellationToken)) ?? throw new NotFoundException("No BookedDetailLead found by this Id");
            var oldLeadDto = bookedDetail.Lead.Adapt<ViewLeadDto>();
            var currentUserId = _currentUserRepo.GetUserId();
            if (string.IsNullOrEmpty(request.BookedUnderName))
            {
                request.BookedUnderName = bookedDetail.BookedUnderName ?? null;
            }
            if (string.IsNullOrEmpty(request.BookedByUser))
            {
                request.BookedByUser = bookedDetail.BookedByUser ?? null;
            }
            if (request.UserId == default || request.UserId == Guid.Empty)
            {
                request.UserId = currentUserId;
            }
           // IList<Domain.Entities.Property>? properties = null;
            if (request.PropertyIds?.Any() ?? false)
            {
                bookedDetail.Properties = await _propertyRepository.ListAsync(new GetPropertiesByIdspecs(request.PropertyIds ?? new()));
            }
            //else
            //{
            //    properties = bookedDetail?.Properties ?? null;
            //}
            if (request.ProjectIds?.Any() ?? false)
            {
                bookedDetail.Projects = await _projectRepo.ListAsync(new ProjectByIdsSpec(request.ProjectIds ?? new()), cancellationToken);

            }
            UnitType? unitType = null;
            if(request.UnitType != null)
            {
                bookedDetail.UnitType = await _unitType.FirstOrDefaultAsync(new GetUnitInfoSpecs(request.UnitType.Id ?? Guid.Empty), cancellationToken) ?? null;

            }
            else
            {
                unitType = bookedDetail.UnitType ?? null;
            }
            if (request.BrokerageInfo != null)
            {
                bookedDetail.TeamHead = request.TeamHead ?? bookedDetail.TeamHead;
                bookedDetail.AgreementValue = request.AgreementValue ?? bookedDetail.AgreementValue;
                bookedDetail.AdditionalCharges = request.AdditionalCharges ?? bookedDetail.AdditionalCharges;
                bookedDetail.CarParkingCharges = request.CarParkingCharges ?? bookedDetail.CarParkingCharges;
                bookedDetail.Documents = request.Documents?.Adapt<IList<Document>>() ?? bookedDetail.Documents;
                bookedDetail.PaymentMode = request.PaymentMode == TokenType.None ? bookedDetail.PaymentMode : request.PaymentMode;
                bookedDetail.BookedDate = request?.BookedDate ?? bookedDetail.BookedDate;
                bookedDetail.Discount = request?.Discount ?? bookedDetail.Discount;
                bookedDetail.DiscountUnit = request?.DiscountUnit ?? bookedDetail.DiscountUnit;
                bookedDetail.RemainingAmount = request?.RemainingAmount ?? bookedDetail.RemainingAmount;
                bookedDetail.DiscountMode = request?.DiscountMode ?? bookedDetail.DiscountMode;
                bookedDetail.PaymentType = request?.PaymentType ?? bookedDetail.PaymentType;
                bookedDetail.TokenAmount = request?.TokenAmount ?? bookedDetail.TokenAmount;
                bookedDetail.LeadId = request?.LeadId ?? bookedDetail.LeadId;
                bookedDetail.BookedDate = request?.BookedDate ?? bookedDetail.BookedDate;
                bookedDetail.BookedBy = request?.BookedBy ?? bookedDetail.BookedBy;
                bookedDetail.SecondaryOwner = request?.SecondaryOwner ?? bookedDetail.SecondaryOwner;
                bookedDetail.UserId = request?.UserId ?? bookedDetail.UserId;
                bookedDetail.SoldPrice = request?.SoldPrice ?? bookedDetail.SoldPrice;
                bookedDetail.Notes = request?.Notes ?? bookedDetail.Notes;
                bookedDetail.ProjectsList = request.ProjectsList ?? bookedDetail.ProjectsList;
                bookedDetail.PropertiesList = request.PropertiesList ?? bookedDetail.PropertiesList;
                bookedDetail.LastModifiedOn = request.LastModifiedOn;
                bookedDetail.LastModifiedBy = request.LastModifiedBy;
                bookedDetail.BrokerageInfo = request.BrokerageInfo.Adapt<LeadBrokerageInfo>();
                bookedDetail.LeadBrokerageInfoId = bookedDetail?.BrokerageInfo?.Id ?? null;
                bookedDetail.Currency = request?.Currency ?? bookedDetail.Currency;
                bookedDetail.IsBookingCompleted = request?.IsBookingCompleted ?? bookedDetail.IsBookingCompleted;
            }
            else
            {               
                bookedDetail = request.Adapt(bookedDetail);
                bookedDetail.UnitType = unitType;
            }
            try
            {
                bookedDetail.IsBookingCompleted = true;
                await _leadBookedDetailRepo.UpdateAsync(bookedDetail);
                List<string> status = new List<string> { "invoiced" };
                var leadStatus = await _customMasterLeadStatusRepo.FirstOrDefaultAsync( new GetCustomStatusByNameSpec(status),cancellationToken);
                if (leadStatus != null) 
                {
                    bookedDetail.Lead.CustomLeadStatus = leadStatus;
                    await _leadRepo.UpdateAsync(bookedDetail.Lead);
                    var leadDto = bookedDetail.Lead.Adapt<ViewLeadDto>();
                    await UpdateLeadHistoryAsync(bookedDetail.Lead, cancellationToken: cancellationToken);

                    #region Update New Lead History Table
                    leadDto.LastModifiedBy = currentUserId;
                    await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken, currentUserId);
                    await oldLeadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken, currentUserId);
                    var leadStatues = await _customLeadStatusRepo.ListAsync(cancellationToken);
                    var masterPropertyTypes = await _propertyTypeRepo.ListAsync(cancellationToken);
                    var latestModificationVersion = await _dapperRepository.GetLatestLeadHistoryVersionAsync(leadDto.Id);
                    var histories = await LeadHistoryHelperV2.V2UpdateLeadHistoryForVM(leadDto, oldLeadDto, latestModificationVersion ?? 1, leadStatues, masterPropertyTypes, _userService, currentUserId, cancellationToken);
                    await _newLeadHistoryRepo.AddRangeAsync(histories);
                    #endregion
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = $"{typeof(UpdateBookedDetailsRequestHandler).Name} - Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);

            }
           
            return new(bookedDetail.Id);
        }


    }
}
