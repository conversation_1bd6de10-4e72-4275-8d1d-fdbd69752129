﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class ShortDbContext : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Code",
                schema: "LeadratBlack",
                table: "QRFormTemplates",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LongUrl",
                schema: "LeadratBlack",
                table: "QRFormTemplates",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ShortUrl",
                schema: "LeadratBlack",
                table: "QRFormTemplates",
                type: "text",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Code",
                schema: "LeadratBlack",
                table: "QRFormTemplates");

            migrationBuilder.DropColumn(
                name: "LongUrl",
                schema: "LeadratBlack",
                table: "QRFormTemplates");

            migrationBuilder.DropColumn(
                name: "ShortUrl",
                schema: "LeadratBlack",
                table: "QRFormTemplates");
        }
    }
}
