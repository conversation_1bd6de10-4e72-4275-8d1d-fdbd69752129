﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Reports.Web.Data.Dtos.ChannelPartner
{
    public class FormattedDataChannelPartnerDto : IDto
    {
        public string SlNo { get; set; } = default!;
        public string? Name { get; set; }
        public long ConvertedData { get; set; }
        public List<ViewFormattedDataReportByChannelPartnerDto>? Data { get; set; }
    }
    public class ViewFormattedDataReportByChannelPartnerDto : IDto
    {
        public string? Status { get; set; }
        public long Count { get; set; }
    }
}
