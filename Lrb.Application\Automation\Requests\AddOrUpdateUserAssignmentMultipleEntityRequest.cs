﻿using Lrb.Application.Automation.Dtos;
using Lrb.Application.Common.Atomation;
using Lrb.Application.Integration.Web;
using Lrb.Application.Project.Web.Specs;
using Lrb.Application.PropertyRefrenceInfomation.Web.Specs;
using Lrb.Application.Team.Web;
using Lrb.Application.ZonewiseLocation.Web.Specs;
using Lrb.Domain.Entities.Automation;
namespace Lrb.Application.Automation.Requests
{
    public class AddOrUpdateUserAssignmentMultipleEntityRequest : MultiEntityUserAssignmentDto, IRequest<Response<bool>>
    {
    }
    public class AddOrUpdateUserAssignmentMultipleEntityRequestHandler : IRequestHandler<AddOrUpdateUserAssignmentMultipleEntityRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Location> _locationRepo;
        private readonly IRepositoryWithEvents<Zone> _zoneRepo;
        private readonly IRepositoryWithEvents<City> _cityRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationRepo;
        private readonly IRepositoryWithEvents<FacebookAdsInfo> _fbAdsRepo;
        private readonly IRepositoryWithEvents<FacebookLeadGenForm> _fbFormRepo;
        private readonly IRepositoryWithEvents<AssignmentModule> _assignmentModuleRepo;
        private readonly IRepositoryWithEvents<UserAssignment> _userAssignmentRepo;
        private readonly IRepositoryWithEvents<PropertyReferenceInfo> _refrenceInfoRepo;
        private readonly IRepositoryWithEvents<UserAssignmentMetrics> _userAssignmentMetricsRepo;
        private readonly IRepositoryWithEvents<UserAssignmentHistory> _userAssignmentHistoryRepo;
        private readonly IMediator _mediator;
        private readonly IUserAssignmentMetricsService _userAssignmentMetrics;

        public AddOrUpdateUserAssignmentMultipleEntityRequestHandler(
            IRepositoryWithEvents<Location> locationRepo,
            IRepositoryWithEvents<Zone> zoneRepo,
            IRepositoryWithEvents<City> cityRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.Project> projectRepo,
            IRepositoryWithEvents<IntegrationAccountInfo> integrationRepo,
            IRepositoryWithEvents<FacebookAdsInfo> fbAdsRepo,
            IRepositoryWithEvents<FacebookLeadGenForm> fbFormRepo,
            IRepositoryWithEvents<AssignmentModule> assignmentModuleRepo,
            IRepositoryWithEvents<UserAssignment> userAssignmentRepo,
            IRepositoryWithEvents<PropertyReferenceInfo> refrenceInfoRepo,
            IRepositoryWithEvents<UserAssignmentMetrics> userAssignmentMetricsRepo,
            IRepositoryWithEvents<UserAssignmentHistory> userAssignmentHistoryRepo,
            IMediator mediator,
            IUserAssignmentMetricsService userAssignmentMetrics)
        {
            _locationRepo = locationRepo;
            _zoneRepo = zoneRepo;
            _cityRepo = cityRepo;
            _projectRepo = projectRepo;
            _integrationRepo = integrationRepo;
            _fbAdsRepo = fbAdsRepo;
            _fbFormRepo = fbFormRepo;
            _assignmentModuleRepo = assignmentModuleRepo;
            _userAssignmentRepo = userAssignmentRepo;
            _refrenceInfoRepo = refrenceInfoRepo;
            _userAssignmentHistoryRepo = userAssignmentHistoryRepo;
            _userAssignmentMetricsRepo = userAssignmentMetricsRepo;
            _mediator = mediator;
            _userAssignmentMetrics = userAssignmentMetrics;
        }
        public async Task<Response<bool>> Handle(AddOrUpdateUserAssignmentMultipleEntityRequest request, CancellationToken cancellationToken)
        {
            var userAssignments = new List<UserAssignment>();
            List<Guid>? duplicateUserIds = null;
            List<Guid>? secondaryUserIds = null;
            if (request.IsDuplicateAssignmentEnabled)
            {
                duplicateUserIds = request?.DuplicateUserIds;
            }
            if (request.IsDualAssignmentEnabled)
            {
                secondaryUserIds = request?.SecondaryUserIds;
            }
            List<UserAssignmentMetrics>? userAssignmentMetrics = null;
            if (request?.CategoryType == AssignmentCategoryType.PercentageBased && (request.UserAssignmentConfigurations?.Any() ?? false))
            {
                userAssignments = request.EntityIds.Select(i => new UserAssignment()
                {
                    UserIds = request.UserIds,
                    EntityId = i,
                    SecondaryUserIds = secondaryUserIds,
                    DuplicateUserIds = duplicateUserIds,
                    IsDualAssignmentEnabled = request?.IsDualAssignmentEnabled ?? default,
                    IsDuplicateAssignmentEnabled = request?.IsDuplicateAssignmentEnabled ?? default,
                    ShouldCreateMultipleDuplicates = request?.ShouldCreateMultipleDuplicates ?? default,
                    CategoryType = request?.CategoryType ?? default,
                    UserAssignmentType = request?.UserAssignmentType ?? default,
                    UserAssignmentConfigurations = userAssignmentMetrics,
                    ShouldConfigureLeadRotation = request?.ShouldConfigureLeadRotation ?? default,
                    TeamId = request?.TeamLeadRotationInfo?.Id,
                    TeamName = request?.TeamLeadRotationInfo?.Name,
                }).ToList();
            }
            else
            {
                userAssignments = request.EntityIds.Select(i => new UserAssignment()
                {
                    UserIds = request.UserIds,
                    EntityId = i,
                    SecondaryUserIds = secondaryUserIds,
                    DuplicateUserIds = duplicateUserIds,
                    IsDualAssignmentEnabled = request?.IsDualAssignmentEnabled ?? default,
                    IsDuplicateAssignmentEnabled = request?.IsDuplicateAssignmentEnabled ?? default,
                    ShouldCreateMultipleDuplicates = request?.ShouldCreateMultipleDuplicates ?? default,
                    CategoryType = request?.CategoryType ?? default,
                    UserAssignmentType = request?.UserAssignmentType ?? default,
                    ShouldConfigureLeadRotation = request?.ShouldConfigureLeadRotation ?? default,
                    TeamId = request?.TeamLeadRotationInfo?.Id,
                    TeamName = request?.TeamLeadRotationInfo?.Name,
                }).ToList();
            }

            AssignmentModule? module = null;
            module = await _assignmentModuleRepo.GetByIdAsync(request.ModuleId, cancellationToken);
            if (module != null)
            {
                userAssignments.ForEach(i => i.Module = module);
                switch (module.Name)
                {
                    case AssignmentModule.Project:
                        var projects = await _projectRepo.ListAsync(new GetProjectByIdSpecs(request.EntityIds), cancellationToken);
                        if (projects != null && projects.Any())
                        {
                            foreach (var project in projects)
                            {
                                Guid? teamId = null;
                                if (request?.ShouldConfigureLeadRotation == true && (request.TeamLeadRotationInfo?.Id != null && request.TeamLeadRotationInfo?.Id != Guid.Empty))
                                {
                                    request.TeamLeadRotationInfo.IntegrationAccountInfoId = project.Id;
                                    teamId = (await _mediator.Send(request?.TeamLeadRotationInfo?.Adapt<UpdateTeamRequest>(), cancellationToken))?.Data;
                                }
                                else if (request?.ShouldConfigureLeadRotation == true && request.TeamLeadRotationInfo != null)
                                {
                                    request.TeamLeadRotationInfo.IntegrationAccountInfoId = project.Id;
                                    teamId = (await _mediator.Send(request?.TeamLeadRotationInfo?.Adapt<CreateTeamRequest>(), cancellationToken))?.Data;
                                }
                                if (project.UserAssignment != null)
                                {
                                    project.UserAssignment.UserIds = request?.UserIds;
                                    project.UserAssignment.SecondaryUserIds = secondaryUserIds;
                                    project.UserAssignment.DuplicateUserIds = duplicateUserIds;
                                    project.UserAssignment.IsDualAssignmentEnabled = request?.IsDualAssignmentEnabled ?? default;
                                    project.UserAssignment.IsDuplicateAssignmentEnabled = request?.IsDuplicateAssignmentEnabled ?? default;
                                    project.UserAssignment.ShouldCreateMultipleDuplicates = request?.ShouldCreateMultipleDuplicates ?? default;
                                    project.UserAssignment.CategoryType = request?.CategoryType ?? default;
                                    project.UserAssignment.UserAssignmentType = request?.UserAssignmentType ?? default;
                                    project.UserAssignment.UserAssignmentConfigurations = userAssignmentMetrics ?? default;
                                    project.UserAssignment.ShouldConfigureLeadRotation = request?.ShouldConfigureLeadRotation ?? default;
                                    project.UserAssignment.TotalLeadsCount = 0;
                                    project.UserAssignment.TeamName = request?.TeamLeadRotationInfo?.Name;
                                    project.UserAssignment.TeamId = request.TeamLeadRotationInfo?.Id ?? project.UserAssignment.TeamId ?? teamId;
                                    project.UserAssignment.ProjectId = project.Id;
                                }
                                else
                                {
                                    var userAssignment = userAssignments.FirstOrDefault(i => i.EntityId == project.Id);
                                    if (userAssignment != null)
                                    {
                                        await _userAssignmentRepo.AddAsync(userAssignment, cancellationToken);
                                        project.UserAssignment = userAssignment;
                                    }
                                }
                            }
                            await _projectRepo.UpdateRangeAsync(projects, cancellationToken);
                            return new(true, "Assignment details updated successfully.");
                        }
                        else
                        {
                            return new(false, "Please provide valid Ids.");
                        }
                    case AssignmentModule.Location:
                        var locations = await _locationRepo.ListAsync(new LocationByIdSpec(request.EntityIds), cancellationToken);
                        if (locations != null && locations.Any())
                        {
                            foreach (var location in locations)
                            {
                                Guid? teamId = null;
                                if (request?.ShouldConfigureLeadRotation == true && (request.TeamLeadRotationInfo?.Id != null && request.TeamLeadRotationInfo?.Id != Guid.Empty))
                                {
                                    request.TeamLeadRotationInfo.IntegrationAccountInfoId = location.Id;
                                    teamId = (await _mediator.Send(request?.TeamLeadRotationInfo?.Adapt<UpdateTeamRequest>(), cancellationToken))?.Data;
                                }
                                else if (request?.ShouldConfigureLeadRotation == true && request.TeamLeadRotationInfo != null)
                                {
                                    request.TeamLeadRotationInfo.IntegrationAccountInfoId = location.Id;
                                    teamId = (await _mediator.Send(request?.TeamLeadRotationInfo?.Adapt<CreateTeamRequest>(), cancellationToken))?.Data;
                                }
                                if (location.UserAssignment != null)
                                {
                                    location.UserAssignment.UserIds = request.UserIds;
                                    location.UserAssignment.UserAssignmentType = request?.UserAssignmentType ?? default;
                                    location.UserAssignment.CategoryType = request?.CategoryType ?? default;
                                    location.UserAssignment.ShouldConfigureLeadRotation = request?.ShouldConfigureLeadRotation ?? default;
                                    location.UserAssignment.TeamName = request.TeamLeadRotationInfo?.Name;
                                    location.UserAssignment.TeamId = request.TeamLeadRotationInfo?.Id ?? location.UserAssignment.TeamId ?? teamId;
                                }
                                else
                                {
                                    var userAssignment = userAssignments.FirstOrDefault(i => i.EntityId == location.Id);
                                    if (userAssignment != null)
                                    {
                                        await _userAssignmentRepo.AddAsync(userAssignment, cancellationToken);
                                        location.UserAssignment = userAssignment;
                                    }
                                }
                            }
                            await _locationRepo.UpdateRangeAsync(locations, cancellationToken);
                            return new(true, "Assignment details updated successfully.");
                        }
                        else
                        {
                            return new(false, "Please provide valid Ids.");
                        }
                    case AssignmentModule.Zone:
                        var zones = await _zoneRepo.ListAsync(new ZoneByIdSpec(request.EntityIds), cancellationToken);
                        if (zones != null && zones.Any())
                        {
                            foreach (var zone in zones)
                            {
                                Guid? teamId = null;
                                if (request?.ShouldConfigureLeadRotation == true && (request.TeamLeadRotationInfo?.Id != null && request.TeamLeadRotationInfo?.Id != Guid.Empty))
                                {
                                    request.TeamLeadRotationInfo.IntegrationAccountInfoId = zone.Id;
                                    teamId = (await _mediator.Send(request?.TeamLeadRotationInfo?.Adapt<UpdateTeamRequest>(), cancellationToken))?.Data;
                                }
                                else if (request?.ShouldConfigureLeadRotation == true && request.TeamLeadRotationInfo != null)
                                {
                                    request.TeamLeadRotationInfo.IntegrationAccountInfoId = zone.Id;
                                    teamId = (await _mediator.Send(request?.TeamLeadRotationInfo?.Adapt<CreateTeamRequest>(), cancellationToken))?.Data;
                                }
                                if (zone.UserAssignment != null)
                                {
                                    zone.UserAssignment.UserIds = request.UserIds;
                                    zone.UserAssignment.UserAssignmentType = request?.UserAssignmentType ?? default;
                                    zone.UserAssignment.CategoryType = request?.CategoryType ?? default;
                                    zone.UserAssignment.ShouldConfigureLeadRotation = request?.ShouldConfigureLeadRotation ?? default;
                                    zone.UserAssignment.TeamName = request.TeamLeadRotationInfo?.Name;
                                    zone.UserAssignment.TeamId = request.TeamLeadRotationInfo?.Id ?? zone.UserAssignment.TeamId ?? teamId;
                                }
                                else
                                {
                                    var userAssignment = userAssignments.FirstOrDefault(i => i.EntityId == zone.Id);
                                    if (userAssignment != null)
                                    {
                                        await _userAssignmentRepo.AddAsync(userAssignment, cancellationToken);
                                        zone.UserAssignment = userAssignment;
                                    }
                                }
                            }
                            await _zoneRepo.UpdateRangeAsync(zones, cancellationToken);
                            return new(true, "Assignment details updated successfully.");
                        }
                        else
                        {
                            return new(false, "Please provide valid Ids.");
                        }
                    case AssignmentModule.City:
                        var cities = await _cityRepo.ListAsync(new CityByIdSpec(request.EntityIds), cancellationToken);
                        if (cities != null && cities.Any())
                        {
                            foreach (var city in cities)
                            {
                                Guid? teamId = null;
                                if (request?.ShouldConfigureLeadRotation == true && (request.TeamLeadRotationInfo?.Id != null && request.TeamLeadRotationInfo?.Id != Guid.Empty))
                                {
                                    request.TeamLeadRotationInfo.IntegrationAccountInfoId = city.Id;
                                    teamId = (await _mediator.Send(request?.TeamLeadRotationInfo?.Adapt<UpdateTeamRequest>(), cancellationToken))?.Data;
                                }
                                else if (request?.ShouldConfigureLeadRotation == true && request.TeamLeadRotationInfo != null)
                                {
                                    request.TeamLeadRotationInfo.IntegrationAccountInfoId = city.Id;
                                    teamId = (await _mediator.Send(request?.TeamLeadRotationInfo?.Adapt<CreateTeamRequest>(), cancellationToken))?.Data;
                                }
                                if (city.UserAssignment != null)
                                {
                                    city.UserAssignment.UserIds = request.UserIds;
                                    city.UserAssignment.UserAssignmentType = request?.UserAssignmentType ?? default;
                                    city.UserAssignment.CategoryType = request?.CategoryType ?? default;
                                    city.UserAssignment.ShouldConfigureLeadRotation = request?.ShouldConfigureLeadRotation ?? default;
                                    city.UserAssignment.TeamName = request.TeamLeadRotationInfo?.Name;
                                    city.UserAssignment.TeamId = request.TeamLeadRotationInfo?.Id ?? city.UserAssignment.TeamId ?? teamId;
                                }
                                else
                                {
                                    var userAssignment = userAssignments.FirstOrDefault(i => i.EntityId == city.Id);
                                    if (userAssignment != null)
                                    {
                                        await _userAssignmentRepo.AddAsync(userAssignment, cancellationToken);
                                        city.UserAssignment = userAssignment;
                                    }
                                }
                            }
                            await _cityRepo.UpdateRangeAsync(cities, cancellationToken);
                            return new(true, "Assignment details updated successfully.");
                        }
                        else
                        {
                            return new(false, "Please provide valid Ids.");
                        }
                    case AssignmentModule.SubSource:
                        var integrationAccounts = await _integrationRepo.ListAsync(new IntegrationAccountByIdSpec(request.EntityIds), cancellationToken);
                        if (integrationAccounts != null && integrationAccounts.Any())
                        {
                            foreach (var integrationAccount in integrationAccounts)
                            {
                                Guid? teamId = null;
                                if (request.ShouldConfigureLeadRotation == true && (request.TeamLeadRotationInfo?.Id != null && request.TeamLeadRotationInfo?.Id != Guid.Empty))
                                {
                                    request.TeamLeadRotationInfo.IntegrationAccountInfoId = integrationAccount.Id;
                                    teamId = (await _mediator.Send(request.TeamLeadRotationInfo?.Adapt<UpdateTeamRequest>(), cancellationToken))?.Data;
                                }
                                else if (request.ShouldConfigureLeadRotation == true && request.TeamLeadRotationInfo != null)
                                {
                                    request.TeamLeadRotationInfo.IntegrationAccountInfoId = integrationAccount.Id;
                                    teamId = (await _mediator.Send(request.TeamLeadRotationInfo?.Adapt<CreateTeamRequest>(), cancellationToken))?.Data;
                                }
                                if (request.CategoryType == AssignmentCategoryType.PercentageBased && (request.UserAssignmentConfigurations?.Any() ?? false))
                                {
                                    userAssignmentMetrics = await _userAssignmentMetrics.GetUserAssignmentMetrics(request.UserAssignmentConfigurations, integrationAccount.Adapt<AccountInfoDto>());
                                }
                                if (integrationAccount.UserAssignment != null)
                                {
                                    integrationAccount.UserAssignment.UserIds = request.UserIds;
                                    integrationAccount.UserAssignment.SecondaryUserIds = secondaryUserIds;
                                    integrationAccount.UserAssignment.DuplicateUserIds = duplicateUserIds;
                                    integrationAccount.UserAssignment.IsDualAssignmentEnabled = request?.IsDualAssignmentEnabled ?? default;
                                    integrationAccount.UserAssignment.IsDuplicateAssignmentEnabled = request?.IsDuplicateAssignmentEnabled ?? default;
                                    integrationAccount.UserAssignment.ShouldCreateMultipleDuplicates = request?.ShouldCreateMultipleDuplicates ?? default;
                                    integrationAccount.UserAssignment.CategoryType = request?.CategoryType ?? default;
                                    integrationAccount.UserAssignment.UserAssignmentType = request?.UserAssignmentType ?? default;
                                    integrationAccount.UserAssignment.UserAssignmentConfigurations = userAssignmentMetrics ?? default;
                                    integrationAccount.UserAssignment.ShouldConfigureLeadRotation = request?.ShouldConfigureLeadRotation ?? default;
                                    integrationAccount.UserAssignment.TotalLeadsCount = 0;
                                    integrationAccount.UserAssignment.TeamName = request.TeamLeadRotationInfo?.Name;
                                    integrationAccount.UserAssignment.TeamId = request.TeamLeadRotationInfo?.Id ?? integrationAccount.UserAssignment.TeamId ?? teamId;

                                }
                                else
                                {
                                    var userAssignment = userAssignments.FirstOrDefault(i => i.EntityId == integrationAccount.Id);
                                    if (userAssignment != null)
                                    {
                                        if (userAssignmentMetrics?.Any() ?? false)
                                        {
                                            userAssignment.UserAssignmentConfigurations = userAssignmentMetrics;
                                        }
                                        userAssignment.TeamId = teamId;
                                        await _userAssignmentRepo.AddAsync(userAssignment, cancellationToken);
                                        integrationAccount.UserAssignment = userAssignment;
                                    }
                                }
                            }

                            await _integrationRepo.UpdateRangeAsync(integrationAccounts, cancellationToken);
                            return new(true, "Assignment details updated successfully.");
                        }
                        else
                        {
                            var fbAds = await _fbAdsRepo.ListAsync(new FacebookAdsInfoByIdSpec(request.EntityIds), cancellationToken);
                            if (fbAds != null && fbAds.Any())
                            {
                                foreach (var fbAd in fbAds)
                                {
                                    Guid? teamId = null;
                                    if (request.ShouldConfigureLeadRotation == true && (request.TeamLeadRotationInfo?.Id != null && request.TeamLeadRotationInfo?.Id != Guid.Empty))
                                    {
                                        request.TeamLeadRotationInfo.IntegrationAccountInfoId = fbAd.Id;
                                        teamId = (await _mediator.Send(request.TeamLeadRotationInfo?.Adapt<UpdateTeamRequest>(), cancellationToken))?.Data;
                                    }
                                    else if (request.ShouldConfigureLeadRotation == true && request.TeamLeadRotationInfo != null)
                                    {
                                        request.TeamLeadRotationInfo.IntegrationAccountInfoId = fbAd.Id;
                                        teamId = (await _mediator.Send(request.TeamLeadRotationInfo?.Adapt<CreateTeamRequest>(), cancellationToken))?.Data;
                                    }
                                    if (request.CategoryType == AssignmentCategoryType.PercentageBased && (request.UserAssignmentConfigurations?.Any() ?? false))
                                    {
                                        userAssignmentMetrics = await _userAssignmentMetrics.GetUserAssignmentMetrics(request.UserAssignmentConfigurations, fbAd.Adapt<AccountInfoDto>());
                                    }
                                    if (fbAd.UserAssignment != null)
                                    {
                                        fbAd.UserAssignment.UserIds = request.UserIds;
                                        fbAd.UserAssignment.SecondaryUserIds = secondaryUserIds;
                                        fbAd.UserAssignment.DuplicateUserIds = duplicateUserIds;
                                        fbAd.UserAssignment.IsDualAssignmentEnabled = request?.IsDualAssignmentEnabled ?? default;
                                        fbAd.UserAssignment.IsDuplicateAssignmentEnabled = request?.IsDuplicateAssignmentEnabled ?? default;
                                        fbAd.UserAssignment.ShouldCreateMultipleDuplicates = request?.ShouldCreateMultipleDuplicates ?? default;
                                        fbAd.UserAssignment.CategoryType = request?.CategoryType ?? default;
                                        fbAd.UserAssignment.UserAssignmentType = request?.UserAssignmentType ?? default;
                                        fbAd.UserAssignment.UserAssignmentConfigurations = userAssignmentMetrics ?? default;
                                        fbAd.UserAssignment.ShouldConfigureLeadRotation = request?.ShouldConfigureLeadRotation ?? default;
                                        fbAd.UserAssignment.TotalLeadsCount = 0;
                                        fbAd.UserAssignment.TeamName = request.TeamLeadRotationInfo?.Name;
                                        fbAd.UserAssignment.TeamId = request.TeamLeadRotationInfo?.Id ?? fbAd.UserAssignment.TeamId ?? teamId;
                                    }
                                    else
                                    {
                                        var userAssignment = userAssignments.FirstOrDefault(i => i.EntityId == fbAd.Id);
                                        if (userAssignment != null)
                                        {
                                            if (userAssignmentMetrics?.Any() ?? false)
                                            {
                                                userAssignment.UserAssignmentConfigurations = userAssignmentMetrics;
                                            }
                                            userAssignment.TeamId = teamId;
                                            await _userAssignmentRepo.AddAsync(userAssignment, cancellationToken);
                                            fbAd.UserAssignment = userAssignment;
                                        }
                                    }
                                }
                                await _fbAdsRepo.UpdateRangeAsync(fbAds, cancellationToken);
                                return new(true, "Assignment details updated successfully.");
                            }
                            else
                            {
                                var fbForms = await _fbFormRepo.ListAsync(new FacebookFormByIdSpec(request.EntityIds), cancellationToken);
                                if (fbForms != null && fbForms.Any())
                                {
                                    foreach (var fbForm in fbForms)
                                    {
                                        Guid? teamId = null;
                                        if (request.ShouldConfigureLeadRotation == true && (request.TeamLeadRotationInfo?.Id != null && request.TeamLeadRotationInfo?.Id != Guid.Empty))
                                        {
                                            request.TeamLeadRotationInfo.IntegrationAccountInfoId = fbForm.Id;
                                            teamId = (await _mediator.Send(request.TeamLeadRotationInfo?.Adapt<UpdateTeamRequest>(), cancellationToken))?.Data;
                                        }
                                        else if (request.ShouldConfigureLeadRotation == true && request.TeamLeadRotationInfo != null)
                                        {
                                            request.TeamLeadRotationInfo.IntegrationAccountInfoId = fbForm.Id;
                                            teamId = (await _mediator.Send(request.TeamLeadRotationInfo?.Adapt<CreateTeamRequest>(), cancellationToken))?.Data;
                                        }
                                        if (request.CategoryType == AssignmentCategoryType.PercentageBased && (request.UserAssignmentConfigurations?.Any() ?? false))
                                        {
                                            userAssignmentMetrics = await _userAssignmentMetrics.GetUserAssignmentMetrics(request.UserAssignmentConfigurations, fbForm.Adapt<AccountInfoDto>());
                                        }
                                        if (fbForm.UserAssignment != null)
                                        {
                                            fbForm.UserAssignment.UserIds = request.UserIds;
                                            fbForm.UserAssignment.SecondaryUserIds = secondaryUserIds;
                                            fbForm.UserAssignment.DuplicateUserIds = duplicateUserIds;
                                            fbForm.UserAssignment.IsDualAssignmentEnabled = request?.IsDualAssignmentEnabled ?? default;
                                            fbForm.UserAssignment.IsDuplicateAssignmentEnabled = request?.IsDuplicateAssignmentEnabled ?? default;
                                            fbForm.UserAssignment.ShouldCreateMultipleDuplicates = request?.ShouldCreateMultipleDuplicates ?? default;
                                            fbForm.UserAssignment.CategoryType = request?.CategoryType ?? default;
                                            fbForm.UserAssignment.UserAssignmentType = request?.UserAssignmentType ?? default;
                                            fbForm.UserAssignment.UserAssignmentConfigurations = userAssignmentMetrics ?? default;
                                            fbForm.UserAssignment.ShouldConfigureLeadRotation = request?.ShouldConfigureLeadRotation ?? default;
                                            fbForm.UserAssignment.TotalLeadsCount = 0;
                                            fbForm.UserAssignment.TeamName = request.TeamLeadRotationInfo?.Name;
                                            fbForm.UserAssignment.TeamId = request.TeamLeadRotationInfo?.Id ?? fbForm.UserAssignment.TeamId ?? teamId;
                                        }
                                        else
                                        {
                                            var userAssignment = userAssignments.FirstOrDefault(i => i.EntityId == fbForm.Id);
                                            if (userAssignment != null)
                                            {
                                                if (userAssignmentMetrics?.Any() ?? false)
                                                {
                                                    userAssignment.UserAssignmentConfigurations = userAssignmentMetrics;
                                                }
                                                userAssignment.TeamId = teamId;
                                                await _userAssignmentRepo.AddAsync(userAssignment, cancellationToken);
                                                fbForm.UserAssignment = userAssignment;
                                            }
                                        }

                                    }
                                    await _fbFormRepo.UpdateRangeAsync(fbForms, cancellationToken);
                                    return new(true, "Assignment details updated successfully.");
                                }
                                else
                                {
                                    return new(false, "Please provide valid Id.");
                                }
                            }
                        }
                    case AssignmentModule.ReferenceId:
                        var refrenceInfos = await _refrenceInfoRepo.ListAsync(new GetRefrenceInfoUserAssignmentByIdSpecs(request.EntityIds), cancellationToken);
                        if (refrenceInfos != null && refrenceInfos.Any())
                        {
                            foreach (var refInfo in refrenceInfos)
                            {
                                if (refInfo.UserAssignment != null)
                                {
                                    refInfo.UserAssignment.UserIds = request.UserIds;
                                }
                                else
                                {
                                    var userAssignment = userAssignments.FirstOrDefault(i => i.EntityId == refInfo.Id);
                                    if (userAssignment != null)
                                    {
                                        await _userAssignmentRepo.AddAsync(userAssignment, cancellationToken);
                                        refInfo.UserAssignment = userAssignment;
                                    }
                                }
                            }
                            await _refrenceInfoRepo.UpdateRangeAsync(refrenceInfos, cancellationToken);
                            return new(true, "Assignment details updated successfully.");
                        }
                        else
                        {
                            return new(false, "Please provide valid Ids.");
                        }
                    default:
                        return new(false);
                }
            }
            else
            {
                return new(false, "Please provide a valid module Id.");
            }
        }
    }
}
