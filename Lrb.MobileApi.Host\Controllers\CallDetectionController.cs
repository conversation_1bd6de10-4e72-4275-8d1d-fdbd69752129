﻿namespace Lrb.MobileApi.Host.Controllers
{
    [Authorize]
    public class CallDetectionController : VersionedApiController
    {
        private readonly Lrb.Application.Common.ServiceBus.IServiceBus _serviceBus;
        public CallDetectionController(Lrb.Application.Common.ServiceBus.IServiceBus serviceBus)
        {
            _serviceBus = serviceBus;
        }
        [HttpPost("sync/{tenantId}")]
        public async Task<IActionResult> SyncTenant(string tenantId, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(tenantId))
                return BadRequest("TenantId is required.");

            var result = await _serviceBus.SyncDataByTenantAsync(tenantId);
            return Ok(result);
        }
    }
}
