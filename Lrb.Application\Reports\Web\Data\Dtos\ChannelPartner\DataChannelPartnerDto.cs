﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Reports.Web.Data.Dtos.ChannelPartner
{
    public class ViewDataChannelPartnerDto : IDto
    {
        public Guid Id { get; set; }
        public string? Name { get; set; }
        public List<ViewDataReportByChannelPartnerDto>? Data { get; set; }
        public long ConvertedDataCount { get; set; }
    }
    public class ViewDataReportByChannelPartnerDto : IDto
    {
        public Guid StatusId { get; set; }
        public string? StatusDisplayName { get; set; }
        public long DataCount { get; set; }
    }
    public class DataChannelPartnerDto : IDto
    {
        public Guid Id { get; set; }
        public string? Name { get; set; }
        public string? Statuses { get; set; }
        public List<StatusDto>? StatusDtos { get; set; }
        public long ConvertedDataCount { get; set; }
    }
}
