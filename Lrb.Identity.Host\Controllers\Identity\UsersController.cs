using Lrb.Application.Common.Persistence;
using Lrb.Application.CustomEmail.Web;
using Lrb.Application.GlobalSettings.Common;
using Lrb.Application.Identity;
using Lrb.Application.Identity.Tokens.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Identity.Users.AuthToken;
using Lrb.Application.Identity.Users.AuthTokenDbStore;
using Lrb.Application.Identity.Users.Password;
using Lrb.Application.Identity.Users.Web.Dto;
using Lrb.Application.Identity.Users.Web.Requests;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.User;
using Lrb.Infrastructure.DomainSettings;
using Mapster;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Primitives;
using Newtonsoft.Json;

namespace Lrb.Identity.Host.Controllers.Identity;
[AllowAnonymous]
public class UsersController : VersionNeutralApiController
{
    private readonly IUserService _userService;
    private readonly DomainSettings _domainSettings;
    private readonly Serilog.ILogger _logger;
    private readonly ILeadRepositoryAsync _leadRepositoryAsync;

    public UsersController(IUserService userService, IOptions<DomainSettings> options, Serilog.ILogger logger, ILeadRepositoryAsync leadRepositoryAsync)
    {
        _userService = userService;
        _domainSettings = options.Value;
        _logger = logger;
        _leadRepositoryAsync = leadRepositoryAsync;
    }

    [HttpGet]
    [TenantIdHeader]
    [MustHavePermission(LrbAction.View, LrbResource.Users)]
    [OpenApiOperation("Get list of all users.", "")]
    public async Task<Response<List<UserDetailsDto>>> GetListAsync(CancellationToken cancellationToken)
    {
        return new(await _userService.GetListAsync(cancellationToken));
    }

    [HttpGet("{id}")]
    [TenantIdHeader]
    [MustHavePermission(LrbAction.View, LrbResource.Users)]
    [OpenApiOperation("Get a user's details.", "")]
    public async Task<Response<UserDetailsDto>> GetByIdAsync(string id, CancellationToken cancellationToken)
    {
        return new(await _userService.GetAsync(id, cancellationToken));
    }
    [HttpGet("{id}/roles")]
    [TenantIdHeader]
    [MustHavePermission(LrbAction.View, LrbResource.UserRoles)]
    [OpenApiOperation("Get a user's roles.", "")]
    public async Task<Response<List<UserRoleDto>>> GetRolesAsync(string id, CancellationToken cancellationToken)
    {
        return new(await _userService.GetRolesAsync(id, cancellationToken));
    }

    [HttpPost("{id}/roles")]
    [TenantIdHeader]
    [MustHavePermission(LrbAction.Update, LrbResource.UserRoles)]
    [OpenApiOperation("Update a user's assigned roles.", "")]
    public async Task<Response<bool>> AssignRolesAsync(string id, UserRolesRequest request, CancellationToken cancellationToken)
    {
        string confirmationMessage = await _userService.AssignRolesAsync(id, request, cancellationToken);
        return new(true, confirmationMessage);
    }

    [HttpPost]
    [TenantIdHeader]
    [MustHavePermission(LrbAction.Create, LrbResource.Users)]
    [OpenApiOperation("Creates a new user.", "")]
    public async Task<Response<bool>> CreateAsync(CreateUserRequest request)
    {
        // TODO: check if registering anonymous users is actually allowed (should probably be an appsetting)
        // and return UnAuthorized when it isn't
        // Also: add other protection to prevent automatic posting (captcha?) 
        try
        {
            var res = await _userService.CreateAsync(request, GetOriginFromRequest());
            Application.UserDetails.Web.CreateUserDetailsRequest createUserDetailsRequest = request.Adapt<Application.UserDetails.Web.CreateUserDetailsRequest>();
            createUserDetailsRequest.UserId = Guid.Parse(res.userId);
            await Mediator.Send(createUserDetailsRequest);
            if (request?.UserRoles?.Any() ?? false)
            {
                UserRolesRequest userRolesRequest = request.Adapt<UserRolesRequest>();
                await _userService.AssignRolesAsync(res.userId, userRolesRequest, CancellationToken.None);
            }
            return new(true, res.messages);
        }
        catch (Exception ex)
        {

            _logger.Information($"Create User Request Dto ->> {JsonConvert.SerializeObject(request)}");
            _logger.Information($"Create User Exception ->> {JsonConvert.SerializeObject(ex)}");
            var error = new LrbError()
            {
                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                ErrorSource = ex?.Source,
                StackTrace = ex?.StackTrace,
                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                ErrorModule = "UsersController -> CreateAsync()"
            };
            await _leadRepositoryAsync.AddErrorAsync(error);
            throw;
        }

    }

    [HttpPost("self-register")]
    [TenantIdHeader]
    [AllowAnonymous]
    [OpenApiOperation("Anonymous user creates a user.", "")]
    public async Task<Response<bool>> SelfRegisterAsync(CreateUserRequest request)
    {
        // TODO: check if registering anonymous users is actually allowed (should probably be an appsetting)
        // and return UnAuthorized when it isn't
        // Also: add other protection to prevent automatic posting (captcha?)
        var res = await _userService.CreateAsync(request, GetOriginFromRequest());
        Application.UserDetails.Web.CreateUserDetailsRequest createUserDetailsRequest = request.Adapt<Application.UserDetails.Web.CreateUserDetailsRequest>();
        createUserDetailsRequest.UserId = Guid.Parse(res.userId);
        await Mediator.Send(createUserDetailsRequest);
        if (request?.UserRoles?.Any() ?? false)
        {
            UserRolesRequest userRolesRequest = request.Adapt<UserRolesRequest>();
            await _userService.AssignRolesAsync(res.userId, userRolesRequest, CancellationToken.None);
        }
        return new(true, res.messages);
    }

    [HttpPost("{id}/toggle-status")]
    [TenantIdHeader]
    [MustHavePermission(LrbAction.Delete, LrbResource.Users)]
    [OpenApiOperation("Toggle a user's active status.", "")]
    public async Task<ActionResult<Response<bool>>> ToggleStatusAsync(string id, ToggleUserStatusRequest request, CancellationToken cancellationToken)
    {
        if (id != request.UserId)
        {
            return BadRequest();
        }

        await _userService.ToggleStatusAsync(request, cancellationToken);
        var res = new Response<bool>(true);
        return Ok(res);
    }
    [HttpGet("send-confirmation-email")]
    [TenantIdHeader]
    [AllowAnonymous]
    [OpenApiOperation("Send Email for Email verification", "")]
    public async Task<string> SendConfirmationEmailAsync([FromQuery] string email)
    {
        string result = await _userService.SendConfirmationEmailAsync(email, GetOriginFromRequest());
        return result;
    }



    [HttpGet("confirm-email")]
    [AllowAnonymous]
    [OpenApiOperation("Confirm email address for a user.", "")]
    public async Task<IActionResult> ConfirmEmailAsync([FromQuery] string tenant, [FromQuery] string userId, [FromQuery] string code, CancellationToken cancellationToken)
    {
        string confirmationMessage = await _userService.ConfirmEmailAsync(userId, code, tenant, cancellationToken);
        string redirectionUrl = string.Format(_domainSettings.Web, tenant) + "/login";
        return new RedirectResult(redirectionUrl);
    }

    [HttpGet("confirm-phone-number")]
    [TenantIdHeader]
    [AllowAnonymous]
    [OpenApiOperation("Confirm phone number for a user.", "")]
    public async Task<Response<bool>> ConfirmPhoneNumberAsync([FromQuery] string userId, [FromQuery] string code)
    {
        string confirmationMessage = await _userService.ConfirmPhoneNumberAsync(userId, code);
        return new(true, confirmationMessage);
    }

    [HttpPost("forgot-password")]
    [AllowAnonymous]
    [TenantIdHeader]
    [OpenApiOperation("Request reset password for a user.", "")]
    public async Task<Response<bool>> ForgotPasswordAsync(ForgotPasswordRequest request)
    {
        string confirmationMessage = await _userService.ForgotPasswordAsync(request, GetOriginFromRequest());
        return new(true, confirmationMessage);
    }

    [HttpPost("reset-password")]
    [TenantIdHeader]
    [OpenApiOperation("Reset a user's password.", "")]
    public async Task<Response<bool>> ResetPasswordAsync(ResetPasswordRequest request)
    {
        string confirmationMessage = await _userService.ResetPasswordAsync(request);
        return new(true, confirmationMessage);
    }

    [HttpPost("default-password")]
    [TenantIdHeader]
    [MustHavePermission(LrbAction.DefaultPassword, LrbResource.Users)]
    [OpenApiOperation("Reset default password to user.", "")]
    public async Task<Response<bool>> SetDefaultPasswordAsync([FromBody] SetDefaultPasswordRequest request)
    {
        string result = await _userService.SetDefaultPasswordAsync(request);
        return new(true, result);
    }

    [HttpPost("change-password/{id}")]
    [TenantIdHeader]
    [OpenApiOperation("Reset a user's password.", "")]
    public async Task<Response<bool>> ChangePasswordAsync(string id, [FromBody] ChangePasswordRequest request)
    {
        string result = await _userService.ChangePasswordAsync(request, id);
        return new(true, result);
    }
    [HttpGet("username-exists/{name}")]
    [TenantIdHeader]
    [OpenApiOperation("Checks the username is already taken or not", "")]
    public async Task<Response<bool>> ExistsWithNameAsync(string name)
    {
        return new(await _userService.ExistsWithNameAsync(name));
    }
    [HttpGet("email-exists/{email}")]
    [TenantIdHeader]
    [OpenApiOperation("Checks the email is already taken or not", "")]
    public async Task<Response<bool>> ExistsWithEmailAsync(string email)
    {
        return new(await _userService.ExistsWithEmailAsync(email));
    }
    [HttpGet("phone-exists/{phone}")]
    [TenantIdHeader]
    [OpenApiOperation("Checks the phone number is already taken or not", "")]
    public async Task<Response<bool>> ExistsWithPhoneNumberAsync(string phone)
    {
        return new(await _userService.ExistsWithPhoneNumberAsync(phone));
    }
    [HttpGet("checkRedirection")]
    [TenantIdHeader]
    [OpenApiOperation("Checks the phone number is already taken or not", "")]
    public IActionResult RedirectAsync()
    {
        return new RedirectResult("https://google.com");
    }
    [HttpGet("username-verify/{name}")]
    [TenantIdHeader]
    [OpenApiOperation("Checks the username is valid or not and returns the user", "")]
    public async Task<Response<UserDetailsDto>> VerifyWithNameAsync(string name)
    {
        return await _userService.VerifyUserAsync(name);
    }
    [HttpPost("otp-Verification")]
    [TenantIdHeader]
    [OpenApiOperation("verifies the entered otp for forgot password", "")]
    public async Task<Response<bool>> VerifyOtpAsync(VerifyOtp model)
    {
        return await _userService.VerifyOtpAsync(model);
    }
    [HttpGet("tenant-independent-username-exists/{name}")]
    [TenantIdHeader]
    [OpenApiOperation("Checks the username is already taken or not", "")]
    public async Task<Response<bool>> CheckUserNameExists(string name)
    {
        return new(await _userService.UserNameExist(name));
    }

    [AllowAnonymous]
    [HttpGet("stored-tokens")]
    [TenantIdHeader]
    [OpenApiOperation("Fetch the stored tokens by userName and DeviceUDID", "")]
    public async Task<Response<UserAuthToken?>> GetStoredTokensAsync([FromQuery] GetAuthTokensRequest request)
    {
        return await Mediator.Send(request);
    }

    [AllowAnonymous]
    [HttpPost("stored-tokens")]
    [TenantIdHeader]
    [OpenApiOperation("Store the tokens by userName and DeviceUDID", "")]
    public async Task<Response<UserAuthToken>> PostStoredTokensAsync([FromBody] PostAuthTokensRequest request)
    {
        return await Mediator.Send(request);
    }

    [HttpPost("bulk-toggle-status")]
    [TenantIdHeader]
    [MustHavePermission(LrbAction.BulkDeactive, LrbResource.Users)]
    [OpenApiOperation("Toggle multiple users' status.", "")]
    public async Task<ActionResult<Response<bool>>> BulkToggleStatusAsync(BulkToggleUserStatusRequest request, CancellationToken cancellationToken)
    {
        var (IsUpdated, Message) = await _userService.BulkToggleStatusAsync(request, cancellationToken);

        var res = new Response<bool>(IsUpdated, Message);

        return Ok(res);
    }

    [HttpPost("bulk-toggle-MFA")]
    [TenantIdHeader]
    [MustHavePermission(LrbAction.Delete, LrbResource.Users)]
    [OpenApiOperation("Toggle multiple users' MFA.", "")]
    public async Task<Response<bool>> BulkToggleMFAAsync(List<Guid> userIds, CancellationToken cancellationToken)
    {
        var (IsUpdated, Message) = await _userService.BulkToggleMFAAsync(userIds, cancellationToken);

        return new(IsUpdated, Message);
    }

    [HttpPost("otp")]
    [AllowAnonymous]
    [TenantIdHeader]
    [OpenApiOperation("Send Login OTP", "")]
    public async Task<Response<bool>> SendOTPAsync(Guid userId, string otp)
    {
        _logger.Information($"SendOTPAsync() called for UserId: {userId} and otp: {otp}");
        return await Mediator.Send(new SendLoginOTPRequest(userId, otp));
    }
    [HttpPost("otp-direct")]
    [AllowAnonymous]
    [TenantIdHeader]
    [OpenApiOperation("Send Login OTP directly", "")]
    public async Task<Response<string>> SendDirectOTPAsync([FromBody] SendDirectLoginOTPRequest request)
    {
        _logger.Information($"SendDirectOTPAsync() called");
        return await Mediator.Send(request);
    }
    private string GetOriginFromRequest() => $"{Request.Scheme}://{Request.Host.Value}{Request.PathBase.Value}";

    [HttpGet("{userId}")]
    [AllowAnonymous]
    [TenantIdHeader]
    [OpenApiOperation("Get Token Details By User Id", "")]
    public async Task<Response<UserLoginDto>> GetTokenDetailsByUserIdAsync(Guid userId, CancellationToken cancellationToken)
    {
        return await Mediator.Send(new GetLoginDetailsByUserIdRequest(userId), cancellationToken);
    }

    [HttpGet("LoginInfo")]
    [AllowAnonymous]
    [TenantIdHeader]
    [OpenApiOperation("Get Token Details By User Id", "")]
    public async Task<Response<List<UserLoginDto>>> GetTokenDetailsAsync()
    {
        this.HttpContext.Request.Headers.TryGetValue("tenant", out StringValues tenantIdParam);
        var tenantId = !string.IsNullOrEmpty(tenantIdParam) ? tenantIdParam.FirstOrDefault() : "";
        return await Mediator.Send(new GetAllUserLoginDetailsRequest(tenantId ?? string.Empty));
    }
    [HttpPut("Revoke/RefreshToken")]
    [AllowAnonymous]
    [TenantIdHeader]
    [OpenApiOperation("Revoke Refresh Token Of User.", "")]
    public async Task<Response<bool>> RevokeRefreshAsync(RevokeUserRefreshTokenRequest request)
    {
        return await Mediator.Send(request);
    }
}
