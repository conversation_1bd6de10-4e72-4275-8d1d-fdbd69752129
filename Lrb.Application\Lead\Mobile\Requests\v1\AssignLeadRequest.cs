﻿using Lrb.Application.Lead.Mobile.Mappings;
using Lrb.Application.Lead.Mobile.Requests;
using Lrb.Domain.Entities.ErrorModule;
using Newtonsoft.Json;

namespace Lrb.Application.Lead.Mobile
{
    public class AssignLeadRequest : IRequest<Response<bool>>
    {
        public List<Guid> LeadIds { get; set; }
        public Guid UserId { get; set; }
    }
    public class AssigLeadRequestHandler : LeadCommonRequestHandler, IRequestHandler<AssignLeadRequest, Response<bool>>
    {
        public AssigLeadRequestHandler(IServiceProvider serviceProvider)
            : base(serviceProvider, nameof(AssigLeadRequestHandler).ToString(), nameof(Handle).ToString())
        {
        }
        public async Task<Response<bool>> Handle(AssignLeadRequest request, CancellationToken cancellationToken)
        {
            Domain.Entities.GlobalSettings? globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
            var currentUserId = _currentUser.GetUserId();
            var user = await _userService.GetAsync(request.UserId.ToString(), cancellationToken);
            if (user == null) { request.UserId = Guid.Empty; }
            var leads = await _leadRepo.ListAsync(new LeadByIdSpec(request.LeadIds ?? new()));
            if (!leads?.Any() ?? false) { throw new NotFoundException("No lead found by the lead ids."); }
            if (leads?.Any() ?? false)
            {
                foreach (var lead in leads)
                {
                    var existingAssignedUserId = lead.AssignTo;
                    lead.AssignedFrom = lead.AssignTo;
                    lead.AssignTo = user?.Id ?? Guid.Empty;
                    // Set OriginalOwner to the assigned user when first assigned
                    if (lead.AssignTo != Guid.Empty && lead.OriginalOwner == null)
                    {
                        lead.OriginalOwner = lead.AssignTo;
                    }
                    if (lead.AssignTo != lead.AssignedFrom)
                    {
                        lead.PickedDate = null;
                        lead.IsPicked = false;
                        lead.ShouldUpdatePickedDate = false;
                    }
                    await _leadRepo.UpdateAsync(lead);
                    if (request?.UserId != null && request?.UserId != Guid.Empty && (request?.UserId != existingAssignedUserId))
                    {
                        AssignLeadsBasedOnScenariosRequest assignmentRequest = new()
                        {
                            LeadIds = new() { lead.Id },
                            UserIds = new() { request?.UserId ?? Guid.Empty },
                            AssignmentType = LeadAssignmentType.WithHistory,
                            LeadSource = lead.Enquiries.FirstOrDefault(i => i.IsPrimary)?.LeadSource ?? default,
                        };
                        var assignmentResponse = await _mediator.Send(assignmentRequest);
                    }
                    var leadDto = lead?.Adapt<ViewLeadDto>();
                    await leadDto?.SetUsersInViewLeadDtoAsync(_userService, cancellationToken);
                    await UpdateLeadHistoryAsync(lead, leadDto, cancellationToken: cancellationToken);

                    try
                    {
                        await lead.SendLeadStatusChangeNotificationsAsync(leadDto, _notificationSenderService, globalSettings, _currentUser.GetUserId());
                        if(lead.AssignTo == Guid.Empty)
                        {
                            await _notificationSenderService.DeleteScheduledNotificationsAsync(lead);
                        }
                    }
                    catch (Exception ex)
                    {
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "AssigLeadRequestHandler -> Handle() ->SendLeadStatusChangeNotificationsAsync()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                }
            }
            try
            {
                var assignedUser = await _userService.GetAsync(leads[0]?.AssignTo.ToString() ?? string.Empty, cancellationToken);
                if (assignedUser != null && assignedUser.Id != _currentUser.GetUserId())
                {
                    if (leads.Count > 1)
                    {
                        List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.MultipleLeadAssignment, leads[0], leads[0].AssignTo, assignedUser.FirstName + " " + assignedUser.LastName, null, leads.Count, globalSettings: globalSettings);
                    }
                    else
                    {
                        List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadAssignment, leads[0], leads[0].AssignTo, assignedUser.FirstName + " " + assignedUser.LastName, globalSettings: globalSettings);
                    }
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "AssigLeadRequestHandler -. Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            return new(true);
        }
    }
}
