﻿using Finbuckle.MultiTenant.EntityFrameworkCore;
using Lrb.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Lrb.Infrastructure.Persistence.Configuration.Application
{
    public class LeadHistoryConfig : IEntityTypeConfiguration<LeadHistory>
    {
        public void Configure(EntityTypeBuilder<LeadHistory> builder)
        {
            builder.IsMultiTenant();
            builder.Property(i => i.ModifiedDate).Metadata.SetProviderClrType(null);
            builder.Property(i => i.AssignedTo).Metadata.SetProviderClrType(null);
            builder.Property(i => i.AssignedToUser).Metadata.SetProviderClrType(null);
            builder.Property(i => i.AssignmentType).Metadata.SetProviderClrType(null);
            builder.Property(i => i.LastModifiedBy).Metadata.SetProviderClrType(null);
            builder.Property(i => i.EnquiredFor).Metadata.SetProviderClrType(null);
            builder.Property(i => i.SaleType).Metadata.SetProviderClrType(null);
            builder.Property(i => i.BasePropertyType).Metadata.SetProviderClrType(null);
            builder.Property(i => i.SubPropertyType).Metadata.SetProviderClrType(null);
            builder.Property(i => i.BHKType).Metadata.SetProviderClrType(null);
            builder.Property(i => i.NoOfBHK).Metadata.SetProviderClrType(null);
            builder.Property(i => i.Name).Metadata.SetProviderClrType(null);
            builder.Property(i => i.Email).Metadata.SetProviderClrType(null);
            builder.Property(i => i.ContactNo).Metadata.SetProviderClrType(null);
            builder.Property(i => i.LowerBudget).Metadata.SetProviderClrType(null);
            builder.Property(i => i.UpperBudget).Metadata.SetProviderClrType(null);
            builder.Property(i => i.Area).Metadata.SetProviderClrType(null);
            builder.Property(i => i.AreaUnit).Metadata.SetProviderClrType(null);
            builder.Property(i => i.Notes).Metadata.SetProviderClrType(null);
            builder.Property(i => i.ConfidentialNotes).Metadata.SetProviderClrType(null);
            builder.Property(i => i.EnquiredCity).Metadata.SetProviderClrType(null);
            builder.Property(i => i.EnquiredState).Metadata.SetProviderClrType(null);
            builder.Property(i => i.EnquiredLocation).Metadata.SetProviderClrType(null);
            builder.Property(i => i.LeadSource).Metadata.SetProviderClrType(null);
            builder.Property(i => i.Rating).Metadata.SetProviderClrType(null);
            builder.Property(i => i.BaseLeadStatus).Metadata.SetProviderClrType(null);
            builder.Property(i => i.SubLeadStatus).Metadata.SetProviderClrType(null);
            builder.Property(i => i.ScheduledDate).Metadata.SetProviderClrType(null);
            builder.Property(i => i.LeadNumber).Metadata.SetProviderClrType(null);
            builder.Property(i => i.ChosenProject).Metadata.SetProviderClrType(null);
            builder.Property(i => i.ChosenProperty).Metadata.SetProviderClrType(null);
            builder.Property(i => i.BookedUnderName).Metadata.SetProviderClrType(null);
            builder.Property(i => i.SoldPrice).Metadata.SetProviderClrType(null);
            builder.Property(i => i.IsHighlighted).Metadata.SetProviderClrType(null);
            builder.Property(i => i.IsEscalated).Metadata.SetProviderClrType(null);
            builder.Property(i => i.IsAboutToConvert).Metadata.SetProviderClrType(null);
            builder.Property(i => i.IsIntegrationLead).Metadata.SetProviderClrType(null);
            builder.Property(i => i.ShareCount).Metadata.SetProviderClrType(null);
            builder.Property(i => i.IsHotLead).Metadata.SetProviderClrType(null);
            builder.Property(i => i.IsColdLead).Metadata.SetProviderClrType(null);
            builder.Property(i => i.IsWarmLead).Metadata.SetProviderClrType(null);
            builder.Property(i => i.AssignedFromUser).Metadata.SetProviderClrType(null);
            builder.Property(i => i.ContactRecords).Metadata.SetProviderClrType(null);
            builder.Property(i => i.Documents).Metadata.SetProviderClrType(null);
            builder.Property(i => i.LastModifiedByUser).Metadata.SetProviderClrType(null);
            builder.Property(i => i.IsMeetingDone).Metadata.SetProviderClrType(null);
            builder.Property(i => i.MeetingLocation).Metadata.SetProviderClrType(null);
            builder.Property(i => i.IsSiteVisitDone).Metadata.SetProviderClrType(null);
            builder.Property(i => i.SiteLocation).Metadata.SetProviderClrType(null);
            builder.Property(i => i.Properties).Metadata.SetProviderClrType(null);
            builder.Property(i => i.Projects).Metadata.SetProviderClrType(null);
            builder.Property(i => i.SubSource).Metadata.SetProviderClrType(null);
            builder.Property(i => i.ReferralName).Metadata.SetProviderClrType(null);
            builder.Property(i => i.ReferralContactNo).Metadata.SetProviderClrType(null);
            builder.Property(i => i.AgencyName).Metadata.SetProviderClrType(null);
            builder.Property(i => i.CompanyName).Metadata.SetProviderClrType(null);
            builder.Property(i => i.PossessionDate).Metadata.SetProviderClrType(null);
            builder.Property(i => i.CarpetArea).Metadata.SetProviderClrType(null);
            builder.Property(i => i.CarpetAreaUnit).Metadata.SetProviderClrType(null);
            builder.Property(i => i.ConversionFactor).Metadata.SetProviderClrType(null);
            builder.Property(i => i.SecondaryUserId).Metadata.SetProviderClrType(null);
            builder.Property(i => i.SecondaryUser).Metadata.SetProviderClrType(null);
            builder.Property(i => i.PickedDate).Metadata.SetProviderClrType(null);
            builder.Property(i => i.IsPicked).Metadata.SetProviderClrType(null);
            builder.Property(i => i.CustomFlags).Metadata.SetProviderClrType(null);
            builder.Property(i => i.Agencies).Metadata.SetProviderClrType(null);
            builder.Property(i => i.Beds).Metadata.SetProviderClrType(null);
            builder.Property(i => i.Baths).Metadata.SetProviderClrType(null);
            builder.Property(i => i.Floors).Metadata.SetProviderClrType(null);
            builder.Property(i => i.OfferType).Metadata.SetProviderClrType(null);
            builder.Property(i => i.Furnished).Metadata.SetProviderClrType(null);
            builder.Property(i => i.EnquiredCommunity).Metadata.SetProviderClrType(null);
            builder.Property(i => i.EnquiredSubCommunity).Metadata.SetProviderClrType(null);
            builder.Property(i => i.EnquiredTowerName).Metadata.SetProviderClrType(null);
            builder.Property(i => i.BuiltUpArea).Metadata.SetProviderClrType(null);
            builder.Property(i => i.BuiltUpAreaUnit).Metadata.SetProviderClrType(null);
            builder.Property(i => i.BuiltUpAreaConversionFactor).Metadata.SetProviderClrType(null);
            builder.Property(i => i.SaleableArea).Metadata.SetProviderClrType(null);
            builder.Property(i => i.SaleableAreaUnit).Metadata.SetProviderClrType(null);
            builder.Property(i => i.SaleableAreaConversionFactor).Metadata.SetProviderClrType(null);
            builder.Property(i => i.ReferralEmail).Metadata.SetProviderClrType(null);
            builder.Property(i => i.SecondaryFromUser).Metadata.SetProviderClrType(null);
            builder.Property(i => i.CustomerState).Metadata.SetProviderClrType(null);
            builder.Property(i => i.CustomerCity).Metadata.SetProviderClrType(null);
            builder.Property(i => i.CustomerLocation).Metadata.SetProviderClrType(null);
            builder.Property(i => i.CustomerCommunity).Metadata.SetProviderClrType(null);
            builder.Property(i => i.CustomerCountry).Metadata.SetProviderClrType(null);
            builder.Property(i => i.CustomerSubCommunity).Metadata.SetProviderClrType(null);
            builder.Property(i => i.EnquiredCountry).Metadata.SetProviderClrType(null);
            builder.Property(i => i.CustomerTowerName).Metadata.SetProviderClrType(null);
            builder.Property(i => i.Currency).Metadata.SetProviderClrType(null);
            builder.Property(i => i.PropertyArea).Metadata.SetProviderClrType(null);
            builder.Property(i => i.PropertyAreaUnit).Metadata.SetProviderClrType(null);
            builder.Property(i => i.PropertyAreaConversionFactor).Metadata.SetProviderClrType(null);
            builder.Property(i => i.NetArea).Metadata.SetProviderClrType(null);
            builder.Property(i => i.NetAreaUnit).Metadata.SetProviderClrType(null);
            builder.Property(i => i.NetAreaConversionFactor).Metadata.SetProviderClrType(null);
            builder.Property(i => i.UnitName).Metadata.SetProviderClrType(null);
            builder.Property(i => i.ClusterName).Metadata.SetProviderClrType(null);
            builder.Property(i => i.Nationality).Metadata.SetProviderClrType(null);
            builder.Property(i => i.Campaigns).Metadata.SetProviderClrType(null);
            builder.Property(i => i.Purpose).Metadata.SetProviderClrType(null);
            builder.Property(i => i.PossesionType).Metadata.SetProviderClrType(null);
            builder.Property(i => i.LandLine).Metadata.SetProviderClrType(null);


            builder.Property(i => i.Gender).Metadata.SetProviderClrType(null);
            builder.Property(i => i.DateOfBirth).Metadata.SetProviderClrType(null);
            builder.Property(i => i.MaritalStatus).Metadata.SetProviderClrType(null);
            builder.Property(i => i.AnniversaryDate).Metadata.SetProviderClrType(null);

        }
    }
}
