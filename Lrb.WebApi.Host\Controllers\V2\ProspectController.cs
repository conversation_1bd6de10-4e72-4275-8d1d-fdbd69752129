﻿using Lrb.Application.Common.Interfaces;
using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.DataManagement.Web.Request;
using Lrb.Infrastructure.DomainSettings;
using MediatR;
using Microsoft.Extensions.Options;

namespace Lrb.WebApi.Host.Controllers.V2
{
    [Authorize]
    [Route("api/v2/[controller]")]
    [ApiVersionNeutral]

    public class ProspectController : VersionedApiController
    {
        private readonly IMediator _mediator;
        private readonly Serilog.ILogger _logger;
        public ProspectController(IMediator mediator, Serilog.ILogger logger)
        {
            _mediator = mediator;
            _logger = logger;
        }
        [HttpPost]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get all Prospects.", "")]
        public async Task<PagedResponse<ViewProspectDto, long>> GetAllProspectAsync([FromBody] GetAllProspectRequest request)
        {
            return await _mediator.Send(request);
        }
        [AllowAnonymous]
        [HttpPost("anonymous")]
        [TenantIdHeader]
        [OpenApiOperation("Get all prospects using available filters.", "")]
        public Task<PagedResponse<PullViewProspectDto, long>> SearchProspectsAsync([FromBody] GetAllProspectsAnonymousRequest request)
        {
            if (HttpContext.Request.Headers.TryGetValue(Lrb.Shared.Multitenancy.MultitenancyConstants.TenantIdName, out var tenantIdValues))
            {
                string? tenantId = tenantIdValues.FirstOrDefault();
                _logger.Information("GetAllProspectsAnonymousRequest info: PageNumber={PageNumber}, PageSize={PageSize}, TenantId={TenantId}", request.PageNumber, request.PageSize, tenantId);

            }
            return Mediator.Send(request);
        }

        [HttpPost("count")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get All Prospect Count By Status Filter")]
        public async Task<Response<ProspectCountByFilterDto>> GetCountAsync([FromBody] GetProspectCountByFilterRequest request)
        {
            return await _mediator.Send(request);
        }
        [HttpPost("custom-filters-count-level1")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get prospects count by custom filters.", "")]
        public async Task<Response<List<Application.Lead.Web.CustomFiltersDto>>> GetLevel1DataAsync([FromBody] GetAllProspectsCountByCustomFiltersLevel1Request request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("basefilter-count")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("get Prospect Count by Status Filter")]
        public async Task<PagedResponse<ProspectCountByStatusFilterDto, string>> GetProspectCountByStatusCount([FromBody] GetProspectCountForAllBaseFiltersRequest request)
        {
            return await _mediator.Send(request);
        }

        [HttpPost("custom-filters")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get prospects by custom filters.", "")]
        public async Task<PagedResponse<ViewProspectDto, string>> GetAsync([FromBody] GetAllProspectsByCustomFiltersRequest request)
        {
            return await _mediator.Send(request);
        }

    }

}
