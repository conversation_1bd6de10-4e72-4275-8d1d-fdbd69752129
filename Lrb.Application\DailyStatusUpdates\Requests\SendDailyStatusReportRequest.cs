﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Common.Email;
using Lrb.Application.DailyStatusUpdates.Dtos;
using Lrb.Application.DailyStatusUpdates.Mappings;
using Lrb.Application.Email.Web;
using Lrb.Application.Email.Web.Dtos;
using Lrb.Application.Email.Web.Specs;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;

namespace Lrb.Application.DailyStatusUpdates
{
    public class SendDailyStatusReportRequest : IRequest<Response<List<EmailSenderDto>>>
    {
        public Event Event { get; set; }

        public SendDailyStatusReportRequest(Event @event)
        {
            Event = @event;
        }
    }
    public class SendDailyStatusReportRequestHandler : IRequestHandler<SendDailyStatusReportRequest, Response<List<EmailSenderDto>>>
    {
        private readonly IRepositoryWithEvents<DailyStatusUpdate> _dailyStatusUpdateRepo;
        private readonly IBlobStorageService _blobStorageService;
        private readonly IReadRepository<MasterEmailTemplates> _masterEmailTemplatesRepo;
        private readonly IReadRepository<MasterEmailServiceProvider> _masterEmailServiceProviderRepo;
        private readonly IGraphEmailService _graphEmailService;
        public SendDailyStatusReportRequestHandler(
            IRepositoryWithEvents<DailyStatusUpdate> dailyStatusUpdateRepo,
            IBlobStorageService blobStorageService,
            IReadRepository<MasterEmailTemplates> masterEmailTemplatesRepo,
            IReadRepository<MasterEmailServiceProvider> masterEmailServiceProviderRepo,
            IGraphEmailService graphEmailService)
        {
            _dailyStatusUpdateRepo = dailyStatusUpdateRepo;
            _blobStorageService = blobStorageService;
            _masterEmailServiceProviderRepo = masterEmailServiceProviderRepo;
            _masterEmailTemplatesRepo = masterEmailTemplatesRepo;
            _graphEmailService = graphEmailService;
        }

        public async Task<Response<List<EmailSenderDto>>> Handle(SendDailyStatusReportRequest request, CancellationToken cancellationToken)
        {
            var serviceProvider = (await _masterEmailServiceProviderRepo.ListAsync(new GetLREmailServiceProviderSpec(), CancellationToken.None)).FirstOrDefault();
            var template = (await _masterEmailTemplatesRepo.ListAsync(new GetMasterEmailTemplatesByEventSpec(request.Event), CancellationToken.None)).FirstOrDefault();
            List<EmailSenderDto> emailDtos = new List<EmailSenderDto>();
            if (template != null && template.IsPrimary && serviceProvider != null)
            {
                List<TenantInfoDto> tenantInfoDtos = new List<TenantInfoDto>();
                var tenants = (await _dailyStatusUpdateRepo.ListAsync()).FirstOrDefault();
                if (!string.IsNullOrWhiteSpace(tenants?.TenantsInfo))
                {
                    tenantInfoDtos = JsonConvert.DeserializeObject<List<TenantInfoDto>>(tenants?.TenantsInfo) ?? new List<TenantInfoDto>();
                }
                if (tenantInfoDtos.Count > 0)
                {
                    var users = tenantInfoDtos.SelectMany(i => i?.Users).ToList();
                    emailDtos = await GetUsersWithLeadsAsync(users, template, serviceProvider);
                }
            }
            if (emailDtos.Count > 0)
            {
                foreach (var emailDto in emailDtos)
                {
                    await _graphEmailService.SendEmail(emailDto);
                }
            }
            return new(emailDtos);
        }
        private async Task<List<EmailSenderDto>> GetUsersWithLeadsAsync(List<UserInfoDto> users, MasterEmailTemplates template, MasterEmailServiceProvider serviceProvider)
        {
            List<EmailSenderDto> emailDto = new List<EmailSenderDto>();
            foreach (var user in users)
            {
                if (user?.Leads != null && user?.Leads?.Count > 0)
                {
                    UpdateLeadsCount(user);
                    byte[] bytes = DailyUpdatesHelper.CreateExcelData(user).ToArray();
                    string fileName = $"DailyStatusUpdate.xlsx";
                    string folder = "Leads";
                    var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", folder, fileName, bytes);
                    string fileUrl = await _blobStorageService.GetPreSignedURL(_blobStorageService?.BucketName ?? "prd-leadratblack", key);
                    user.FileUrl = fileUrl;
                    emailDto.Add(GetEmailDto(user, template, serviceProvider));
                }
            }
            return emailDto;
        }
        private UserInfoDto UpdateLeadsCount(UserInfoDto user)
        {
            user.NewLeadsCount = user.Leads.Count(i => i.Status == LeadStatus.New);
            user.SiteVisitScheduledCount = user.Leads.Count(i => i.Status == LeadStatus.site_visit_scheduled);
            user.MeetingScheduledCount = user.Leads.Count(i => i.Status == LeadStatus.meeting_scheduled);
            user.PendingLeadsCount = user.Leads.Count(i => i.Status == LeadStatus.pending);
            user.CallbackMeetingScheduledCount = user.Leads.Count(i => i.Status == LeadStatus.to_schedule_a_meeting);
            user.CallbackSiteVisitCount = user.Leads.Count(i => i.Status == LeadStatus.to_schedule_site_visit);
            user.BookedLeadsCount = user.Leads.Count(i => i.Status == LeadStatus.booked);
            user.MeetingDoneCount = user.Leads.Count(i => i.Status == LeadStatus.meeting_done);
            user.MeetingNotDoneCount = user.Leads.Count(i => i.Status == LeadStatus.meeting_not_done);
            user.SiteVisitDoneCount = user.Leads.Count(i => i.Status == LeadStatus.site_visit_done);
            user.SiteVisitNotDoneCount = user.Leads.Count(i => i.Status == LeadStatus.site_visit_not_done);

            return user;
        }
        private EmailSenderDto GetEmailDto(UserInfoDto user, MasterEmailTemplates template, MasterEmailServiceProvider serviceProvider)
        {
            List<string> toEmails = new();
            List<string> ccEmails = new();
            EmailSenderDto emailDto = new();
            Dictionary<string, string> variableSet = new Dictionary<string, string>();
            variableSet.Add("#userName#", user.Name);
            variableSet.Add("#NewLeadsCount# ", user.NewLeadsCount.ToString() ?? string.Empty);
            variableSet.Add("#PendingLeadsCount#", user.PendingLeadsCount.ToString() ?? string.Empty);
            variableSet.Add("#MeetingScheduledCount#", user.MeetingScheduledCount.ToString() ?? string.Empty);
            variableSet.Add("#SiteVisitCount#", user.SiteVisitScheduledCount.ToString() ?? string.Empty);
            variableSet.Add("#CallbackMeetingScheduled#", user.CallbackMeetingScheduledCount.ToString() ?? string.Empty);
            variableSet.Add("#CallbackSiteVisitScheduled#", user.CallbackSiteVisitCount.ToString() ?? string.Empty);
            variableSet.Add("#BookedCount#", user.BookedLeadsCount.ToString() ?? string.Empty);
            variableSet.Add("#MeetingDoneCount#", user.MeetingDoneCount.ToString() ?? string.Empty);
            variableSet.Add("#MeetingNotDoneCount#", user.MeetingNotDoneCount.ToString() ?? string.Empty);
            variableSet.Add("#SiteVisitNotDoneCount#", user.SiteVisitNotDoneCount.ToString() ?? string.Empty);
            variableSet.Add("#SiteVisitDoneCount#", user.SiteVisitDoneCount.ToString() ?? string.Empty);
            variableSet.Add("#FileURL#", user.FileUrl ?? string.Empty);
            var updatedTemplate = ReplaceVariables(template.Body, variableSet);
            toEmails.Add(user.Email ?? string.Empty);
            ccEmails.Add(user.ReportsTo?.Email ?? string.Empty);
            if (serviceProvider != null)
            {
                emailDto.SenderEmailAddress = serviceProvider.SenderEmailAddress;
                emailDto.To = toEmails;
                emailDto.Cc = ccEmails;
                emailDto.EmailBody = updatedTemplate;
                emailDto.Subject = template.Subject;
            }
            return emailDto;
        }
        private string ReplaceVariables(string? template, Dictionary<string, string> variableSet)
        {
            if (string.IsNullOrEmpty(template))
            {
                return template;
            }
            foreach (var item in variableSet)
            {
                template = template.Replace(item.Key, item.Value ?? default);
            }
            return template;
        }
    }
}
