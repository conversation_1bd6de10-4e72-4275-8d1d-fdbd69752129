﻿using Finbuckle.MultiTenant.EntityFrameworkCore;
using Lrb.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;


namespace Lrb.Infrastructure.Persistence.Configuration.Application
{
    public class ProjectConfig : IEntityTypeConfiguration<Lrb.Domain.Entities.Project>
    {
        public void Configure(EntityTypeBuilder<Domain.Entities.Project> builder)
        {
            builder.IsMultiTenant();
            builder.HasMany(i => i.Blocks)
                .WithOne(i => i.Project);
            builder.HasMany(i => i.UnitTypes)
                .WithOne(i => i.Project);
            builder.HasMany(i => i.ProjectGalleries)
                .WithOne(i => i.Project);
            builder.HasMany(i => i.Amenities)
                .WithOne(i => i.Project)
                .HasForeignKey(i => i.ProjectId);
            builder.HasMany(i => i.Prospects)
                .WithMany(i => i.Projects);
            builder.HasMany(i => i.Leads)
                .WithMany(i => i.Projects);
            builder.HasMany(i => i.Properties)
                .WithOne(i => i.Project)
                .HasForeignKey(i => i.ProjectId);
            builder.HasMany(i => i.AssociatedBanks)
                .WithOne(i => i.Project);
            builder.HasMany(i => i.BookedDetails)
                .WithMany(i => i.Projects);
            builder.HasOne(p => p.UserAssignment) // one-to-one
                  .WithOne()
                  .HasForeignKey<Domain.Entities.Project>(p => p.UserAssignmentId);
            builder.HasMany(p => p.UserAssignments) // one-to-many
                .WithOne(ua => ua.Project)
                .HasForeignKey(ua => ua.ProjectId);
            builder.HasMany(i => i.ChannelPartners)
                 .WithMany(i => i.Projects);
        }
    }
}
