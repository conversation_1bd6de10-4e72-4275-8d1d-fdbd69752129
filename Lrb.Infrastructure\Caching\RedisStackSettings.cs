﻿namespace Lrb.Infrastructure.Caching
{
    public class RedisStackSettings
    {
        public string ConnectionString { get; set; } = string.Empty;
        public string InstanceName { get; set; } = string.Empty;
        public int DefaultDatabase { get; set; } = 0;
        public string Password { get; set; } = string.Empty;
        public bool SSL { get; set; } = false;
        public bool AbortOnConnectFail { get; set; } = true;
        public int ConnectTimeout { get; set; } = 10000;
        public int ConnectRetry { get; set; } = 3;
        public int KeepAlive { get; set; } = 60;
        public int SyncTimeout { get; set; } = 10000;
    }
}
