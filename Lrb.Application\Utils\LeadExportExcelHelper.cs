﻿using DocumentFormat.OpenXml;
using Lrb.Application.Lead.Web.Export;
using Lrb.Application.Reports.Web.Dtos.ExportTrackerDto;
using Microsoft.VisualBasic;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System;
using System.Globalization;
using System.Reflection;
using System.Text.RegularExpressions;

namespace Lrb.Application.Lead.Utils
{
    public static class ExcelGeneration<T>
    {
        private static List<string> mergedCells = new();
        private static void AddHeader(ExcelWorksheet ws, string cellAddress, string text, int fontSize, ExcelHorizontalAlignment alignment, bool isBold, int rowHeight)
        {
            var cell = ws.Cells[cellAddress];
            cell.Value = text;
            cell.Style.Font.Size = fontSize;
            cell.Style.Font.Bold = isBold;
            cell.Style.HorizontalAlignment = alignment;

            ws.Row(cell.Start.Row).Height = rowHeight;

            cell.Style.VerticalAlignment = ExcelVerticalAlignment.Center;
        }
        private static void ApplyCellBorderStyle(ExcelRangeBase range, ExcelBorderStyle borderStyle, System.Drawing.Color borderColor)
        {
            range.Style.Border.Left.Style = borderStyle;
            range.Style.Border.Left.Color.SetColor(borderColor);
            range.Style.Border.Top.Style = borderStyle;
            range.Style.Border.Top.Color.SetColor(borderColor);
            range.Style.Border.Bottom.Style = borderStyle;
            range.Style.Border.Bottom.Color.SetColor(borderColor);
            range.Style.Border.Right.Style = borderStyle;
            range.Style.Border.Right.Color.SetColor(borderColor);
        }
        private static int CountSubProperties(PropertyInfo property)
        {
            var subProperties = property.PropertyType.GetProperties();
            return subProperties.Length;
        }
        private static string ColumnLetter(int columnNumber)
        {
            int dividend = columnNumber;
            string columnLetter = String.Empty;

            while (dividend > 0)
            {
                int modulo = (dividend - 1) % 26;
                columnLetter = Convert.ToChar('A' + modulo) + columnLetter;
                dividend = (dividend - modulo) / 26;
            }

            return columnLetter;
        }
        private static string SplitCamelCase(string input)
        {
            return Regex.Replace(input, "(?<=[a-z])([A-Z])", " $1", RegexOptions.Compiled).Trim();
        }
        private static bool IsSimpleProperty(PropertyInfo property)
        {
            Type propertyType = property.PropertyType;

            return propertyType.IsPrimitive || propertyType == typeof(string) || propertyType == typeof(TimeOnly?) || propertyType == typeof(DateTime?)||propertyType.IsEnum|| propertyType == typeof(long?)||propertyType ==typeof(double?);

        }
        private static bool IsComplexProperty(PropertyInfo property)
        {
            Type propertyType = property.PropertyType;

            return !IsSimpleProperty(property) && propertyType.IsClass && propertyType != typeof(string);
        }
        private static string GetReportType(string name)
        {
            var reportTypes = new Dictionary<string, string>()
            {
                {"statusreportbysubsource", "Status report by Sub Source" },
                {"statusreportbyproject","Status Report By Project" },
                {"meetingandvisitreport","Meeting And Visit Report"},
                {"statusreportbysource","Status Report By source" },
                {"statusreportbyuser","Status Report By User" },
                {"statusreportbyagency","Status Report By Agency" },
                {"substatusreportbyuser","Sub Status Report By User" },
                {"substatusreportbysubsource","Sub Status Report By Subsource"},
                {"useractivityreport","User Activity Report" },
                {"projectreportbysubstatus","Project Report By Substatus" },
                {"exportproperties","Export Properties" },
                {"calllogreportbyuser","Call Log Report" }

            };
            return reportTypes[name];
        }
        public static byte[] GenerateExcel<T, TFiltersDto, TExportTrackerDto>(List<T> dtos, string reportName, TFiltersDto? filtersDto, TExportTrackerDto exportTrackerDto, int notesCount, bool? isWithFacebookProperties, string? timeZoneId ,TimeSpan baseUTcOffset = default)
        {
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

            using MemoryStream memoryStream = new();

            using var package = new ExcelPackage(memoryStream);
            var ws = package.Workbook.Worksheets.Add("Sheet");

            var properties = typeof(T)
                .GetProperties()
                .Where(p => p.Name != "ShouldRenameSiteVisitColumn")
                .ToArray();
            var headerRowCount = FormatHeaderRow(ws, properties, filtersDto, exportTrackerDto, reportName, notesCount, isWithFacebookProperties,timeZoneId, baseUTcOffset);
            PopulateDataRows(ws, dtos, properties, filtersDto, exportTrackerDto, reportName, headerRowCount, notesCount, isWithFacebookProperties,timeZoneId, baseUTcOffset);
            package.Save();

            return memoryStream.ToArray();
        }
        private static void PopulateDataRows<T, TFiltersDto, TExportTrackerDto>(ExcelWorksheet ws, List<T> data, PropertyInfo[] properties, TFiltersDto? filters, TExportTrackerDto? exportTrackerDto, string reportName, int headerRowCount, int notesCount, bool? isWithFacebookProperties, string? timeZoneId = null, TimeSpan baseUTcOffset = default)
        {
            int row = headerRowCount;
            int id = 1;

            foreach (var item in data)
            {
                int col = 1;

                ws.Cells[row, col].Value = id;
                var cell1 = ws.Cells[row, col];
                cell1.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                id++;
                properties = properties
                    .OrderBy(p =>
                        p.Name == "FacebookProperties" ? 2 :
                        p.Name == "NotesDetails" ? 3 : 1)
                    .ToArray();
                foreach (var property in properties)
                {
                    var value = property.GetValue(item);

                    if (value != null)
                    {
                        var cell = ws.Cells[row, col];

                        if (property != null && property.Name == "PossessionDate" && value != null)
                        {
                            if (DateTime.TryParseExact(value.ToString(), "MM-dd-yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime parsedDate))
                            {
                                cell.Value = parsedDate.ToParticularTimeZone(timeZoneId, baseUTcOffset).ToString("dd/MM/yyyy");
                            }
                            else if (property.Name == "PossessionDate" && value is DateTime possessionDate)

                            {
                                cell.Value = possessionDate.ToParticularTimeZone(timeZoneId, baseUTcOffset).ToString("dd-MM-yyyy");
                            }
                            cell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        }

                        else if (property.Name == "LastModifiedOn" && value is DateTime lastModifiedOn)
                        {
                            cell.Value = lastModifiedOn;
                            DateTime modifiedOnIndianStandardTime = lastModifiedOn.ToParticularTimeZone(timeZoneId,baseUTcOffset);
                            string datePart = modifiedOnIndianStandardTime.ToString("dd-MM-yyyy");
                            string timePart = modifiedOnIndianStandardTime.ToString("hh:mm:ss tt");
                            string dateTimeString = $"{datePart} {timePart}";
                            cell.Value = dateTimeString;
                            cell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        }
         
                        else if (property.Name == "ReceivedOn" && value is DateTime receivedOn)
                        {
                            cell.Value = receivedOn;
                            DateTime modifiedOnIndianStandardTime = receivedOn.ToParticularTimeZone(timeZoneId, baseUTcOffset);
                            string datePart = modifiedOnIndianStandardTime.ToString("dd-MM-yyyy");
                            string timePart = modifiedOnIndianStandardTime.ToString("hh:mm:ss tt");
                            string dateTimeString = $"{datePart} {timePart}";
                            cell.Value = dateTimeString;
                            cell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        }
                        else if (property.Name == "ScheduledDate" && value is DateTime scheduledDate)
                        {
                            cell.Value = scheduledDate;
                            DateTime modifiedOnIndianStandardTime = scheduledDate.ToParticularTimeZone(timeZoneId, baseUTcOffset);
                            string datePart = modifiedOnIndianStandardTime.ToString("dd-MM-yyyy");
                            string timePart = modifiedOnIndianStandardTime.ToString("hh:mm:ss tt");
                            string dateTimeString = $"{datePart} {timePart}";
                            cell.Value = dateTimeString;
                            cell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        }
                        else if (property.Name == "PickedDate" && value is DateTime pickedDate)
                        {
                            cell.Value = pickedDate;
                            DateTime modifiedOnIndianStandardTime = pickedDate.ToParticularTimeZone(timeZoneId, baseUTcOffset);
                            string datePart = modifiedOnIndianStandardTime.ToString("dd-MM-yyyy");
                            string timepart = modifiedOnIndianStandardTime.ToString("hh:mm:ss tt");
                            string dateTimeValue=$"{datePart} {timepart}";
                            cell.Value = dateTimeValue;
                            cell.Style.HorizontalAlignment=ExcelHorizontalAlignment.Center;

                        }

                        else if (property.Name == "RevertDate" && value is DateTime revertDate)
                        {
                            cell.Value = revertDate.ToString("dd-MM-yyyy");
                            cell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        }
                        else if (property.Name == "PostponedDate" && value is DateTime postponedDate)
                        {
                            cell.Value = postponedDate.ToString("dd-MM-yyyy");
                            cell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        }
                        else if(property != null && property.Name == "DateOfBirth" && value != null)
                        {
                            if (DateTime.TryParseExact(value.ToString(), "MM-dd-yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime parsedDate))
                            {
                                cell.Value = parsedDate.ToParticularTimeZone(timeZoneId, baseUTcOffset).ToString("dd/MM/yyyy");
                            }
                            else if (property.Name == "DateOfBirth" && value is DateTime DateOdBirth)
                            {
                                cell.Value = DateOdBirth.ToParticularTimeZone(timeZoneId, baseUTcOffset).ToString("dd-MM-yyyy");
                            }
                            cell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        }
                        else if (property.Name == "FacebookProperties")
                        {
                            if (isWithFacebookProperties == false)
                            {
                                continue;
                            }

                            PropertyInfo? fbProp = item?.GetType()?.GetProperty("FacebookProperties");
                            var fbData = fbProp?.GetValue(item);

                            if (fbData is List<FacebookDetailsDto> fbList && fbList.Any())
                            {
                                var fb = fbList.First();
                                var fbFields = typeof(FacebookDetailsDto).GetProperties();

                                foreach (var field in fbFields)
                                {
                                    var fieldvalue = field.GetValue(fb)?.ToString() ?? string.Empty;

                                    var cell2 = ws.Cells[row, col];
                                    cell2.Value = fieldvalue;
                                    cell2.Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;

                                    col++;
                                }
                            }
                            else
                            {
                                col += typeof(FacebookDetailsDto).GetProperties().Length;
                            }
                        }
                        else if (property.Name == "NotesDetails")
                        {
                            PropertyInfo? dataProperty = item?.GetType()?.GetProperty("NotesDetails");
                            var dataValues = dataProperty?.GetValue(item);
                            if (dataProperty != null && dataValues != null)
                            {
                                try
                                {
                                    foreach (var dataValue in dataValues as List<NotesDetails>)
                                    {
                                        var cell2 = ws.Cells[row, col];
                                        cell2.Value = dataValue?.Notes ?? string.Empty;
                                        cell2.Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
                                        col++;
                                        var cell3 = ws.Cells[row, col];
                                        DateTime modifiedOnUtc = dataValue?.ModifiedOn ?? DateTime.MinValue;
                                        modifiedOnUtc = DateTime.SpecifyKind(modifiedOnUtc, DateTimeKind.Utc);
                                        DateTime modifiedOnIndianStandardTime = modifiedOnUtc.ToIndianStandardTime();
                                        var res = modifiedOnUtc.ToString("dd-MM-yyyy");
                                        cell3.Value = res + " " + modifiedOnIndianStandardTime.ToString("hh:mm:ss tt") ?? string.Empty;
                                        cell3.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                                        col++;
                                        var cell4 = ws.Cells[row, col];
                                        cell4.Value = dataValue?.LastModifiedByUser ?? string.Empty;
                                        cell4.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                                        col++;
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"Error in NotesDetails: {ex.Message}");
                                }
                            }
                            else
                            {
                                col += (notesCount * 3);
                            }
                        }
                        else if (property != null && property.Name == "AnniversaryDate" && value != null)
                        {
                            if (DateTime.TryParseExact(value.ToString(), "MM-dd-yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime parsedDate))
                            {
                                cell.Value = parsedDate.ToParticularTimeZone(timeZoneId, baseUTcOffset).ToString("dd/MM/yyyy");
                            }
                            else if (property.Name == "AnniversaryDate" && value is DateTime DateOdBirth)
                            {
                                cell.Value = DateOdBirth.ToParticularTimeZone(timeZoneId, baseUTcOffset).ToString("dd-MM-yyyy");
                            }
                            cell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        }
                        else
                        {
                            cell.Value = value;
                            cell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;

                        }
                        if(property.Name == "FacebookProperties" && isWithFacebookProperties == true)
                        {
                            continue;
                        }
                        else
                        {
                            col++;
                        }
                    }
                    else if(value == null && property.Name == "FacebookProperties" && isWithFacebookProperties == false)
                    {
                        continue;
                    }
                    else if (value == null && property.Name == "FacebookProperties" && isWithFacebookProperties == true)
                    {
                        col += 14;
                    }
                    else 
                    {
                        col++;
                    }
                }

                row++;
            }
        }

        static object? FindItemByPropertyValue(object collection, string propertyName, string propertyValue)
        {
            var result = ((IEnumerable<object>)collection)
                ?.Where(item => GetPropertyValue<string>(item, propertyName) == propertyValue)?.ToList();
            return result?.FirstOrDefault() ?? default;
        }
        static long GetCountFromDynamicObject(object? dynamicObject)
        {
            PropertyInfo? countProperty = dynamicObject?.GetType().GetProperty("Count");
            if (countProperty != null)
            {
                object? countValue = countProperty.GetValue(dynamicObject);
                if (countValue != null && countValue is long)
                {
                    return (long)countValue;
                }
            }
            return 0;
        }
        static T GetPropertyValue<T>(object obj, string propertyName)
        {
            try
            {
                Type type = obj.GetType();
                PropertyInfo? property = type.GetProperty(propertyName);

                if (property != null)
                {
                    object value = property.GetValue(obj);
                    if (value is T)
                    {
                        return (T)value;
                    }
                }
            }
            catch (Exception ex) { }
            return default;
        }
        private static int FormatHeaderRow<TFiltersDto, TExportTrackerDto>(ExcelWorksheet ws, PropertyInfo[] properties, TFiltersDto? filters, TExportTrackerDto? exportTrackerDto, string reportName, int notesCount, bool? isWithFacebookProperties, string? timeZoneId = null, TimeSpan baseUTcOffset = default)
        {

            bool hasComplexProperty = properties.Any(IsComplexProperty);

            if (hasComplexProperty)
            {
                return FormatHeaderRowWithComplexProperties(ws, properties, filters, exportTrackerDto, reportName, notesCount, isWithFacebookProperties,timeZoneId, baseUTcOffset);
            }
            else
            {
                return FormatHeaderRowWithSimpleProperties(ws, properties, filters, exportTrackerDto, reportName, timeZoneId, baseUTcOffset);
            }

        }
        private static int FormatHeaderRowWithSimpleProperties<TFiltersDto, TExportTrackerDto>(ExcelWorksheet ws, PropertyInfo[] properties, TFiltersDto? filters, TExportTrackerDto? exportTrackerDto, string reportName, string? timeZoneId = null, TimeSpan baseUTcOffset = default)
        {

            var headerNames = new List<string>();
            int headerRowCount = AddFilters(ws, filters, exportTrackerDto,timeZoneId,baseUTcOffset);

            AddHeader(ws, $"A${headerRowCount + 1}", reportName, 16, ExcelHorizontalAlignment.Center, true, 30);

            foreach (var property in properties)
            {
                headerNames.Add(SplitCamelCase(property.Name));
            }
            string headerRange = $"A${headerRowCount + 2}:{ColumnLetter(headerNames.Count)}{headerRowCount + 2}";

            ws.Cells[$"A${headerRowCount + 1}:{ColumnLetter(headerNames.Count)}${headerRowCount + 1}"].Merge = true;
            ws.Cells[headerRange].LoadFromArrays(new List<object[]> { headerNames.ToArray() });
            var headerStyle = ws.Cells[headerRange].Style;
            headerStyle.Fill.PatternType = ExcelFillStyle.Solid;
            headerStyle.Fill.BackgroundColor.SetColor(System.Drawing.Color.Black);
            headerStyle.Font.Size = 12;
            headerStyle.Font.Color.SetColor(System.Drawing.Color.White);
            headerStyle.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            ApplyCellBorderStyle(ws.Cells[headerRowCount + 2, 1, headerRowCount + 2, ws.Dimension.Columns], ExcelBorderStyle.Thin, System.Drawing.Color.White);
            ApplyCellBorderStyle(ws.Cells[headerRowCount + 1, 1, headerRowCount + 1, ws.Dimension.Columns], ExcelBorderStyle.Thin, System.Drawing.Color.Black);
            return headerRowCount + 3;
        }
        private static int AddFilters<TFiltersDto, TExportTrackerDto>(ExcelWorksheet ws, TFiltersDto? filters, TExportTrackerDto? exportTrackerDto, string? timeZoneId = null, TimeSpan baseUTcOffset = default)
        {
            int startRow = AddExportTracker(ws, exportTrackerDto, 1);
            var filtersRow = startRow + 1;
            var properties = typeof(TFiltersDto).GetProperties();

            for (int i = 0; i < properties.Length; i++)
            {
                var property = properties[i];
                var value = property.GetValue(filters);

                ws.Cells[filtersRow + i, 1].Value = SplitCamelCase(property.Name) + ":";
                if (property.Name == "FromPossessionDate" && value is DateTime fromDate)
                {
                    ws.Cells[filtersRow + i, 2].Value = fromDate.ToParticularTimeZone(timeZoneId,baseUTcOffset).ToString("dd-MM-yyyy");
                }
                else if (property.Name == "ToPossessionDate" && value is DateTime todate)
                {
                    ws.Cells[filtersRow + i, 2].Value = todate.ToParticularTimeZone(timeZoneId,baseUTcOffset).ToString("dd-MM-yyyy");
                }
                else if (value != null)
                {
                    ws.Cells[filtersRow + i, 2].Value = value.ToString();
                }
                else
                {
                    ws.Cells[filtersRow + i, 2].Value = "N/A";
                }
            }

            var filterRange = ws.Cells[filtersRow, 1, filtersRow + properties.Length - 1, 2];
            var filterStyle = filterRange.Style;
            filterStyle.Font.Bold = true;
            filterStyle.Fill.PatternType = ExcelFillStyle.Solid;
            filterStyle.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
            ApplyCellBorderStyle(filterRange, ExcelBorderStyle.Thin, System.Drawing.Color.Black);

            return properties.Length + startRow;
        }
        private static int AddExportTracker<TExportTrackerDto>(ExcelWorksheet ws, TExportTrackerDto? exportTrackerDto, int startRow)
        {
            var filtersRow = startRow;
            var properties = typeof(ExportTrackerDto).GetProperties();

            for (int i = 0; i < properties.Length; i++)
            {
                var property = properties[i];
                var value = property.GetValue(exportTrackerDto);
                ws.Cells[filtersRow + i, 1].Value = SplitCamelCase(property.Name) + ":";

                if (property.Name == "Type" && value != null)
                {
                    ws.Cells[filtersRow + i, 2].Value = GetReportType(value.ToString());
                }
                else if (property.Name == "CreatedOn" && value is DateTime createdOn)
                {
                    ws.Cells[filtersRow + i, 2].Value = createdOn.ToString("dd-MM-yyyy");
                }
                else if (value != null)
                {
                    ws.Cells[filtersRow + i, 2].Value = value.ToString();
                }
                else
                {
                    ws.Cells[filtersRow + i, 2].Value = "N/A";
                }
            }

            var filterRange = ws.Cells[filtersRow, 1, filtersRow + properties.Length - 1, 2];
            var filterStyle = filterRange.Style;
            filterStyle.Font.Bold = true;
            filterStyle.Fill.PatternType = ExcelFillStyle.Solid;
            filterStyle.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
            ApplyCellBorderStyle(filterRange, ExcelBorderStyle.Thin, System.Drawing.Color.Black);

            return properties.Length;


        }
        private static int FormatHeaderRowWithComplexProperties<TFiltersDto, TExportTrackerDto>(ExcelWorksheet ws, PropertyInfo[] properties, TFiltersDto? filters, TExportTrackerDto? exportTrackerDto, string reportName, int notesCount, bool? isWithFacebookProperties, string? timeZoneId = null, TimeSpan baseUTcOffset = default)
        {

            mergedCells.Clear();
            var headerNames = new List<string>();
            var subHeaderNames = new List<string>();
            int headerRowCount = AddFilters(ws, filters, exportTrackerDto,timeZoneId,baseUTcOffset);
            AddHeader(ws, $"A${headerRowCount + 1}", reportName, 16, ExcelHorizontalAlignment.Center, true, 30);
            properties = properties
                        .OrderBy(p =>
                            p.Name == "FacebookProperties" ? 2 :
                            p.Name == "NotesDetails" ? 3 : 1 
                        ).ToArray();
            foreach (var property in properties)
            {
                if (IsSimpleProperty(property))
                {
                    headerNames.Add("");
                    subHeaderNames.Add(SplitCamelCase(property.Name));
                }
                else if (IsComplexProperty(property))
                {
                    // ✅ Skip else logic for known special complex types
                    if (property.Name == "FacebookProperties")
                    {
                        if (isWithFacebookProperties == false)
                            continue;
                        var facebookFields = typeof(FacebookDetailsDto).GetProperties();
                        int fieldCount = facebookFields.Length;

                        string startColLetter = ColumnLetter(headerNames.Count + 1);
                        string endColLetter = ColumnLetter(headerNames.Count + fieldCount);
                        mergedCells.Add($"{startColLetter}${headerRowCount + 2}:{endColLetter}${headerRowCount + 2}");

                        for (int i = 0; i < fieldCount; i++)
                        {
                            headerNames.Add(i == 0 ? "Facebook Properties" : "");
                        }

                        foreach (var field in facebookFields)
                        {
                            subHeaderNames.Add("Facebook " + SplitCamelCase(field.Name));
                        }

                        continue; 
                    }

                    if (property.Name == "NotesDetails")
                    {
                        for (int i = 1; i <= notesCount; i++)
                        {
                            subHeaderNames.Add(SplitCamelCase("Notes " + i));
                            subHeaderNames.Add(SplitCamelCase("Notes " + i + " AddedOn "));
                            subHeaderNames.Add(SplitCamelCase("Notes " + i + " AddedBy "));
                            headerNames.Add(SplitCamelCase(""));
                            headerNames.Add(SplitCamelCase(""));
                            headerNames.Add(SplitCamelCase(""));
                        }

                        continue;
                    }
                    var subProperties = property.PropertyType.GetProperties();
                    int subPropertyCount = CountSubProperties(property);
                    mergedCells.Add($"{ColumnLetter(headerNames.Count + 1)}${headerRowCount + 2}:{ColumnLetter(subHeaderNames.Count)}${headerRowCount + 2}");

                    for (int i = 0; i < subPropertyCount; i++)
                    {
                        headerNames.Add(SplitCamelCase(property.Name));
                    }

                    foreach (var subProperty in subProperties)
                    {
                        if (IsSimpleProperty(subProperty))
                        {
                            subHeaderNames.Add(SplitCamelCase(subProperty.Name));
                        }
                    }
                }
            }
            int maxColumns = 16384;

            foreach (var mergedCell in mergedCells)
            {
                ws.Cells[mergedCell].Merge = true;
            }
            if (subHeaderNames.Count > maxColumns)
            {
                subHeaderNames = subHeaderNames.GetRange(0, maxColumns);
            }
            if (headerNames.Count > maxColumns)
            {
                headerNames = headerNames.GetRange(0, maxColumns);
            }
            string headerRange = $"A${headerRowCount + 2}:{ColumnLetter(subHeaderNames.Count)}{headerRowCount + 3}";
            ws.Cells[$"A${headerRowCount + 1}:{ColumnLetter(subHeaderNames.Count)}${headerRowCount + 1}"].Merge = true;
            ws.Cells[headerRange].LoadFromArrays(new List<object[]> { headerNames.ToArray(), subHeaderNames.ToArray() });

            var headerStyle = ws.Cells[headerRange].Style;
            headerStyle.Fill.PatternType = ExcelFillStyle.Solid;
            headerStyle.Fill.BackgroundColor.SetColor(System.Drawing.Color.Black);
            headerStyle.Font.Size = 12;
            headerStyle.Font.Color.SetColor(System.Drawing.Color.White);
            headerStyle.HorizontalAlignment = ExcelHorizontalAlignment.Center;

            ApplyCellBorderStyle(ws.Cells[headerRowCount + 2, 1, headerRowCount + 3, ws.Dimension.Columns], ExcelBorderStyle.Thin, System.Drawing.Color.White);
            ApplyCellBorderStyle(ws.Cells[headerRowCount + 1, 1, headerRowCount + 1, ws.Dimension.Columns], ExcelBorderStyle.Thin, System.Drawing.Color.Black);

            return headerRowCount + 4;
        }
    }
    public static class DateTimeExtensions
    {
        public static DateTime ToIndianStandardTime(this DateTime utcDate)
        {
            TimeZoneInfo ist = TimeZoneInfo.FindSystemTimeZoneById("India Standard Time");
            return TimeZoneInfo.ConvertTimeFromUtc(utcDate, ist);
        }

        public static DateTime ToParticularTimeZone(this DateTime utcDate, string? timeZoneId,TimeSpan baseUTcOffset = default)
        {
            if (timeZoneId != null && baseUTcOffset != default)
            {                  
                var timeZone = TimeZoneInfo.FindSystemTimeZoneById(timeZoneId);
                if (timeZone.BaseUtcOffset.TotalSeconds < 0)
                {

                    return utcDate - baseUTcOffset;
                }
                else
                {
                    return utcDate + baseUTcOffset;
                }
                
            }
            else
            {
                return utcDate;
            }
        }
    }
}