﻿using System.Data;
using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Utils;

namespace Lrb.Application.Lead.Web
{
    public class CreateDataTableRequest : IRequest<DataTable>
    {
        public string? S3BucketKey { get; set; }
    }
    public class CreateDataTableRequestHandler : IRequestHandler<CreateDataTableRequest, DataTable>
    {
        IBlobStorageService _blobStorageService;
        public CreateDataTableRequestHandler(IBlobStorageService blobStorageService)
        {
            _blobStorageService = blobStorageService;
        }
        public async Task<DataTable> Handle(CreateDataTableRequest request, CancellationToken cancellationToken)
        {
            DataTable dataTable = new();
            Stream fileStream = await _blobStorageService.GetObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", request.S3BucketKey);
            if (request.S3BucketKey.Split('.').LastOrDefault() == "csv")
            {
                using MemoryStream memoryStream = new MemoryStream();
                fileStream.CopyTo(memoryStream);
                dataTable = CSVHelper.CSVToDataTable(memoryStream);
            }
            else
            {
                dataTable = ExcelHelper.ExcelToDataTable(fileStream);
            }
            int totalRows = dataTable.Rows.Count;
            for (int i = totalRows - 1; i >= 0; i--)
            {
                var row = dataTable.Rows[i];
                if (row.ItemArray.All(i => string.IsNullOrEmpty(i.ToString())))
                {
                    row.Delete();
                }
            }
            if (dataTable.Rows.Count <= 0)
            {
                 throw new Exception("Excel sheet is empty. Please fill some data in the excel sheet template.");
            }
            return dataTable;
        }
    }
}
