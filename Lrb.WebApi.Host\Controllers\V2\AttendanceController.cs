﻿using Lrb.Application.Attendance.Web;
using Lrb.Application.Attendance.Web.Dtos;
using MediatR;

namespace Lrb.WebApi.Host.Controllers.V2
{

    [Authorize]
    [Route("api/v2/[controller]")]
    [ApiVersionNeutral]
    public class AttendanceController : VersionedApiController
    {
        private readonly IMediator _mediator;
        public AttendanceController(IMediator mediator)
        {
            _mediator = mediator;
        }
        [HttpPost]
        [TenantIdHeader]
        // [MustHavePermission(LrbAction.ViewAll, LrbResource.Attendance)]
        [OpenApiOperation("Get All Attendance Logs.", "")]
        public async Task<PagedResponse<AttendanceLogWrapperDto, string>> GetAsync([FromBody] GetAttendanceLogsRequest request)
        {
            return await _mediator.Send(request);
        }
        [HttpPost("no-auth")]
        [TenantIdHeader]
        [OpenApiOperation("Get All Attendance Logs without permission.", "")]
        public async Task<PagedResponse<AttendanceLogWrapperDto, string>> GetAttedenceAsync([FromBody] GetAttendanceLogsRequest request)
        {
            return await _mediator.Send(request);
        }
    }
}
