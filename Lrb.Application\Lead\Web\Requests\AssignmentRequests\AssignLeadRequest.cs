﻿using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Lead.Web.Mappings;
using Lrb.Application.Lead.Web.Requests;

namespace Lrb.Application.Lead.Web
{
    public class AssignLeadRequest : IRequest<Response<DuplicateLeadAssigmentResponseDto>>
    {
        public List<Guid> LeadIds { get; set; } = default!;
        public Guid UserId { get; set; }
    }
    public class AssigLeadRequestHandler : LeadCommonRequestHandler, IRequestHandler<AssignLeadRequest, Response<DuplicateLeadAssigmentResponseDto>>
    {
        private readonly IRepositoryWithEvents<LeadHistoryHot> _newLeadHistoryRepo;
        public AssigLeadRequestHandler(IServiceProvider serviceProvider, IRepositoryWithEvents<LeadHistoryHot> newLeadHistoryRepo) : base(serviceProvider, typeof(AssigLeadRequestHandler).Name, "Handle")
        {
            _newLeadHistoryRepo = newLeadHistoryRepo;
        }

        public async Task<Response<DuplicateLeadAssigmentResponseDto>> Handle(AssignLeadRequest request, CancellationToken cancellationToken)
        {
            try
            {
                Domain.Entities.GlobalSettings? globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                var leads = await _leadRepo.ListAsync(new LeadsByIdsSpec(request.LeadIds ?? new()));

                if (leads != null && leads.Any())
                {
                    var oldViewLeads = leads.Adapt<List<ViewLeadDto>>();
                    Identity.Users.UserDetailsDto? user = null;

                    try
                    {
                        user = await _userService.GetAsync(request.UserId.ToString(), cancellationToken);
                    }
                    catch
                    {
                        user = null;
                    }

                    var leadsAlreadyAssigned = leads.Where(i => i.AssignTo == request.UserId).ToList();

                    leads.RemoveAll(i => i.AssignTo == request.UserId);

                    if (leads.Any())
                    {
                        foreach (var lead in leads)
                        {
                            lead.AssignedFrom = lead.AssignTo;

                            lead.AssignTo = request.UserId;

                            //await CreateLeadAssignmentHistory(lead, cancellationToken);

                            await _leadRepo.UpdateAsync(lead, cancellationToken);
                            await UpdateLeadHistoryAsync(lead, cancellationToken: cancellationToken);

                            #region Create New Lead Histories
                            var oldLeadDto = oldViewLeads.FirstOrDefault(i => i.Id == lead.Id);
                            var newLeadDto = lead.Adapt<ViewLeadDto>();
                            var currentUserId = _currentUserRepo.GetUserId();
                            await newLeadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken, currentUserId);
                            await oldLeadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken, currentUserId);
                            var leadStatues = await _customLeadStatusRepo.ListAsync(cancellationToken);
                            var masterPropertyTypes = await _propertyTypeRepo.ListAsync(cancellationToken);
                            var latestModificationVersion = await _dapperRepository.GetLatestLeadHistoryVersionAsync(newLeadDto.Id);
                            var histories = await LeadHistoryHelperV2.V2UpdateLeadHistoryForVM(newLeadDto, oldLeadDto, latestModificationVersion ?? 1, leadStatues, masterPropertyTypes, _userService, currentUserId, cancellationToken);
                            await _newLeadHistoryRepo.AddRangeAsync(histories);
                            #endregion
                        }
                    }

                    if (user != null)
                    {
                        await SendLeadAssignmentNotificationsAsync(leads[0], leads.Count, globalSettings, cancellationToken);

                        return new(new DuplicateLeadAssigmentResponseDto() { Leads = leadsAlreadyAssigned.Adapt<List<DuplicateAssigmentLeadDto>>(), User = new() { Name = user.FirstName + " " + user.LastName, Id = user.Id } });
                    }
                    else
                    {
                        return new(new DuplicateLeadAssigmentResponseDto() { Leads = leads.Adapt<List<DuplicateAssigmentLeadDto>>() });
                    }
                }
                else
                {
                    throw new NotFoundException("No lead found by the lead ids.");
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{typeof(AssigLeadRequestHandler).Name} - Handle()");
                return new() { Message = ex.Message };
            }

        }
    }
}
