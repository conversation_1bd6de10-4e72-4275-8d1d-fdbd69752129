﻿using DocumentFormat.OpenXml.Wordprocessing;
using Finbuckle.MultiTenant;
using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.Persistence;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Microsoft.Extensions.Options;
using Microsoft.Graph.TermStore;
using Newtonsoft.Json;
using Npgsql;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Infrastructure.Persistence.Repository
{
    public class DuplicateFeatureRepository : IDuplicateFeatureRepository
    {
        private readonly DatabaseSettings _settings;
        private readonly ITenantInfo? _tenantInfo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly List<Guid> StatusIds = new() { Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"), Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a") };

        public DuplicateFeatureRepository(IOptions<DatabaseSettings> options, 
            ITenantInfo tenantInfo, 
           ILeadRepositoryAsync leadRepositoryAsync)
        {
            _settings = options.Value;
            _tenantInfo = tenantInfo;
            _leadRepositoryAsync = leadRepositoryAsync;
        }

        public async Task<bool> CreateDuplicateFeatureSettings(DuplicateLeadFeatureInfo featureInfo)
        {

            var conn = new NpgsqlConnection(string.IsNullOrEmpty(_tenantInfo?.ConnectionString) ? _settings.ConnectionString : _tenantInfo.ConnectionString);
            try
            {
                await conn.OpenAsync();
                var query = $"INSERT INTO \"LeadratBlack\".\"DuplicateFeatureInfo\"( \"Id\", \"IsFeatureAdded\", \"TenantId\", \"IsDeleted\", \"IsLocationBased\", \"IsProjectBased\", \"IsSourceBased\",\"IsSubSourceBased\",\"StutusIds\") " +
                    $"VALUES ('{Guid.NewGuid()}', {featureInfo.IsFeatureAdded}, '{_tenantInfo?.Id ?? string.Empty}', 'false', 'false','false', 'false','false','{JsonConvert.SerializeObject(StatusIds)}');";
                var commamnd = new NpgsqlCommand(query, conn);
                commamnd.CommandType = System.Data.CommandType.Text;
                commamnd.ExecuteNonQuery();
                await conn.CloseAsync();
                return true;
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex.Message,
                    ErrorSource = ex.Source,
                    StackTrace = ex.StackTrace,
                    ErrorModule = "NpgsqlRepository -> CreateDuplicateFeatureSettings()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
            finally { conn.Close(); }
        }
        public async Task<bool> UpdateDuplicateFeatureSettings(DuplicateLeadFeatureInfo featureInfo)
        {
            if (!featureInfo.IsFeatureAdded)
            {
                featureInfo.IsLocationBased = false;
                featureInfo.IsProjectBased = false;
                featureInfo.IsSourceBased = false;
                featureInfo.IsSubSourceBased = false;
            }
            string sourcesArray = featureInfo.Sources != null
    ? $"ARRAY[{string.Join(",", featureInfo.Sources)}]"
    : "NULL";
            var conn = new NpgsqlConnection(string.IsNullOrEmpty(_tenantInfo?.ConnectionString) ? _settings.ConnectionString : _tenantInfo.ConnectionString);
            try
            {
                await conn.OpenAsync();
                var query = $"UPDATE \"LeadratBlack\".\"DuplicateFeatureInfo\" SET \"IsFeatureAdded\" ='{featureInfo.IsFeatureAdded}'," +
                    $" \"IsLocationBased\" ='{featureInfo.IsLocationBased}'," +
                    $" \"IsProjectBased\" ={featureInfo.IsProjectBased}," +
                    $" \"IsSourceBased\" ='{featureInfo.IsSourceBased}', \"IsSubSourceBased\" ='{featureInfo.IsSubSourceBased}', \"StutusIds\" ='{JsonConvert.SerializeObject(StatusIds)}',\"AllowAllDuplicates\" = '{featureInfo.AllowAllDuplicates}' ," +
                    $" \"Sources\" = {sourcesArray} where \"TenantId\" = '{_tenantInfo?.Id ?? string.Empty}'";
                var commamnd = new NpgsqlCommand(query, conn);
                commamnd.CommandType = System.Data.CommandType.Text;
                commamnd.ExecuteNonQuery();
                await conn.CloseAsync();
                return true;
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex.Message,
                    ErrorSource = ex.Source,
                    StackTrace = ex.StackTrace,
                    ErrorModule = "NpgsqlRepository -> CreateDuplicateFeatureSettings()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
            finally { conn.Close(); }
        }
    }
}
