#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:6.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

ARG ASP_ENVIRONMENT
ENV ASPNETCORE_ENVIRONMENT="qa"

FROM mcr.microsoft.com/dotnet/sdk:6.0 AS build
WORKDIR /src
COPY ["Lrb.WebApi.Host/Lrb.WebApi.Host.csproj", "Lrb.WebApi.Host/"]
RUN dotnet restore "Lrb.WebApi.Host/Lrb.WebApi.Host.csproj"
COPY . .
WORKDIR "/src/Lrb.WebApi.Host"
RUN dotnet build "Lrb.WebApi.Host.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "Lrb.WebApi.Host.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Lrb.WebApi.Host.dll"]