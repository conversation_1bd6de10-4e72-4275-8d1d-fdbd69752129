﻿namespace Lrb.Application.Reports.Web
{
    public class ViewDataReportByAgencyDto : IDto
    {
        public Guid StatusId { get; set; }
        public string? StatusDisplayName { get; set; }
        public long DataCount { get; set; }
    }

    public class ViewDataAgencyDto : IDto
    {
        public Guid Id { get; set; }
        public string? Name { get; set; }
        public List<ViewDataReportByAgencyDto>? Data { get; set; }
        public long ConvertedDataCount { get; set; }
    }

    public class DataAgencyDto : IDto
    {
        public Guid Id { get; set; }
        public string? Name { get; set; }
        public Guid StatusId { get; set; }
        public string? StatusDisplayName { get; set; }
        public long DataCount { get; set; }
        public long ConvertedDataCount { get; set; }
    }
}