﻿using Lrb.Application.Agency.Web;
using System.ComponentModel.DataAnnotations.Schema;

namespace Lrb.Application.Integration.Web
{
    public class IntegrationDto
    {
        public string AccountName { get; set; }
        public Guid AccountId { get; set; }
        public LeadSource LeadSource { get; set; }
        public long LeadCount { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid LastModifiedBy { get; set; }
        public IVRType? IVRType { get; set; }
        public bool IsPrimary { get; set; }
        public string? AgencyName { get; set; }
        public Dictionary<string, string>? Credentials { get; set; }
        public string? IVRServiceProvider { get; set; }
        public long? TotalLeadCount { get; set; }
        public string? EndPointUrl { get; set; }
        public List<string>? ToRecipients { get; set; }
        public List<string>? CcRecipients { get; set; }
        public List<string>? BccRecipients { get; set; } 
        public string? Status { get; set; }
        public string? LoginEmail { get; set; }
    }


    public class GoogleAdIntegrationDto
    {
        public Guid Id { get; set; }
        public string AccountName { get; set; }
        public Guid AccountId { get; set; }
        public LeadSource LeadSource { get; set; } = LeadSource.GoogleAds;
        public long LeadCount { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid LastModifiedBy { get; set; }
        public IVRType? IVRType { get; set; }
        public bool IsPrimary { get; set; }
        public string? AgencyName { get; set; }
        public AgencyDto? Agency { get; set; }
        public List<string>? ToRecipients { get; set; }
        public List<string>? CcRecipients { get; set; }
        public List<string>? BccRecipients { get; set; }
        public string? Status { get; set; }
        public string? LoginEmail { get; set; }
        public decimal? CplInr { get; set; }
        public decimal? RoiPercent { get; set; }
        public decimal? CampaignBudget { get; set; }
        public decimal? AdBudget { get; set; }
    }
    public class IntegrationInfoDto : IDto
    {
        public Guid LeadId { get; set; }
        public string? AccountName { get; set; }
        public LeadSource Source { get; set; }
        public string? LoginEmail { get; set; }
    }

    public class IntegrationInformationDto
    {
        public Guid? Id { get; set; }
        public string? AccountName { get; set; }
        public LeadSource LeadSource { get; set; }
        public List<BasicDto>? Infos { get; set; }
        public List<FacebookDto>? FacebookInfos { get; set; }

    }
    public class BasicDto
    {
        public Guid? Id { get; set; }
        public string? AccountName { get; set; }
    }
    public class FacebookDto
    {
        public List<BasicDto>? Ads { get; set; }
        public List<BasicDto>? Forms { get; set; }

    }
    public class RawIntegrationRow
    {
        public Guid? Id { get; set; }
        public string? AccountName { get; set; }
        public LeadSource LeadSource { get; set; } // Will cast to enum later
        public string? Infos { get; set; }  // Raw JSON
        public string? FacebookInfos { get; set; }  // Raw JSON
    }
}

