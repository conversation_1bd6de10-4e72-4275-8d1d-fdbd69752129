﻿using Azure.Core;
using Dapper;
using Finbuckle.MultiTenant;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Identity.Tokens;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Enums;
using Lrb.Infrastructure.Identity;
using Lrb.Infrastructure.Persistence.Repository.Interface;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Npgsql;
using System.Threading;

namespace Lrb.Infrastructure.Persistence.Repository
{
    public class UserRepository : IUserRepository
    {
        private readonly DatabaseSettings _settings;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly ITenantInfo? _tenantInfo;

        public UserRepository(IOptions<DatabaseSettings> options, ITenantInfo tenantInfo, ILeadRepositoryAsync leadRepositoryAsync)
        {
            _settings = options.Value;
            _tenantInfo = tenantInfo;
            _leadRepositoryAsync = leadRepositoryAsync;
        }

        public async Task<bool> UpdateUserRefreshToken(ApplicationUser user)
        {
            var conn = new NpgsqlConnection(string.IsNullOrEmpty(_tenantInfo?.ConnectionString) ? _settings.ConnectionString : _tenantInfo.ConnectionString);
            try
            {
                await conn.OpenAsync();
                var query = $"UPDATE \"Identity\".\"Users\" Set  \"RefreshToken\"= @refreshToken , \"RefreshTokenExpiryTime\" = @expiryTime where \"Id\" = @id";
                var command = new NpgsqlCommand(query, conn);
                command.Parameters.AddWithValue("@refreshToken", user.RefreshToken);
                command.Parameters.AddWithValue("@expiryTime", user.RefreshTokenExpiryTime);
                command.Parameters.AddWithValue("@id", user.Id);
                //command.Parameters.AddWithValue("@dateTime", DateTime.UtcNow);
                int affectedRows = command.ExecuteNonQuery();
                return true;
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "UserRepository -> UpdateUserRefreshToken()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
            finally
            {
                await conn.CloseAsync();
            }
        }

        public async Task<bool> UpdateUserLockAccount(bool isLocked, string userName, string tenantId)
        {
            var conn = new NpgsqlConnection(string.IsNullOrEmpty(_tenantInfo?.ConnectionString) ? _settings.ConnectionString : _tenantInfo.ConnectionString);
            try
            {
                var qyery = $"UPDATE \"Identity\".\"Users\" SET \"IsLocked\" = {isLocked} WHERE \"TenantId\" = '{tenantId}' AND \"UserName\" = '{userName}'";
                await conn.OpenAsync();
                var res = await conn.ExecuteAsync(qyery);
                return res > 0 ? true : false;
            }
            catch (Exception ex)
            {
                return false;
            }
            finally
            {
                await conn.CloseAsync();
            }
        }

        public async Task<bool> UpdateUserLogoutDetails(Guid? userId, LogoutType logoutType, DateTime? logoutTime,string tenantId)
        {
            var conn = new NpgsqlConnection(
                string.IsNullOrEmpty(_tenantInfo?.ConnectionString)
                    ? _settings.ConnectionString
                    : _tenantInfo.ConnectionString);

            try
            {

                const string query = @"
                 UPDATE ""LeadratBlack"".""UserDetails""
                 SET ""LastLogoutType"" = @LastLogoutType, 
                 ""LastLogoutStamp"" = @LastLogoutStamp
                 WHERE ""UserId"" = @UserId AND ""TenantId"" = @TenantId;";


                await conn.OpenAsync();
                var res = await conn.ExecuteAsync(query, new
                {
                    LastLogoutType = (int)logoutType,   // cast enum to int
                    LastLogoutStamp = logoutTime,
                    UserId = userId,
                    TenantId = tenantId
                });

                return res > 0;
            }
            catch (Exception ex)
            {
                // TODO: log ex if needed
                return false;
            }
            finally
            {
                await conn.CloseAsync();
            }
        }

        public async Task<Guid> StoringUserLoginDetails(CognitoTokenResponse tokenResponse, ApplicationUser user, DeviceInfo? deviceInfo, CancellationToken cancellationToken = default)
        {
            await using var conn = new NpgsqlConnection(
                string.IsNullOrEmpty(_tenantInfo?.ConnectionString)
                    ? _settings.ConnectionString
                    : _tenantInfo.ConnectionString);

            await conn.OpenAsync(cancellationToken);

            await using var transaction = await conn.BeginTransactionAsync(cancellationToken);

            try
            {
                var userLoginId = deviceInfo?.LoginId ?? Guid.Empty;

                var insertLoginSql = @"INSERT INTO ""Identity"".""UserLogins"" (""Id"", ""LoginProvider"", ""ProviderKey"", ""ProviderDisplayName"", ""UserId"", ""TenantId"", ""DeviceModel"", ""DeviceName"", ""DeviceUDID"", ""Platform"",""CreatedOn"")VALUES (@Id, @LoginProvider, @ProviderKey, @ProviderDisplayName, @UserId, @TenantId, @DeviceModel, @DeviceName, @DeviceUDID, @Platform,@CreatedOn);";

                await conn.ExecuteAsync(insertLoginSql, new
                {
                    Id = userLoginId.ToString(), // pass as Guid
                    LoginProvider = "Cognito",
                    ProviderKey = string.Empty,
                    ProviderDisplayName = "Cognito",
                    UserId = user.Id,
                    TenantId = _tenantInfo?.Id ?? string.Empty,
                    DeviceModel = deviceInfo?.DeviceModel,
                    DeviceName = deviceInfo?.DeviceName,
                    DeviceUDID = deviceInfo?.DeviceUDID,
                    Platform = deviceInfo?.PlatForm,
                    CreatedOn = DateTime.UtcNow

                }, transaction);

                // 2️⃣ Insert UserToken linked to UserLoginId
                var insertTokenSql = @"INSERT INTO ""Identity"".""UserTokens"" (""UserId"", ""LoginProvider"", ""Name"", ""Value"", ""TenantId"", ""IdToken"", ""RefreshToken"", ""TokenType"", ""UserLoginId"",""CreatedOn"",""AccessToken"")VALUES (@UserId, @LoginProvider, @Name, @Value, @TenantId, @IdToken, @RefreshToken, @TokenType, @UserLoginId,@CreatedOn,@AccessToken);";
                await conn.ExecuteAsync(insertTokenSql, new
                {
                    UserId = user.Id,
                    LoginProvider = "Cognito",
                    Name = userLoginId,
                    Value = "IdToken",
                    TenantId = _tenantInfo?.Id ?? string.Empty,
                    IdToken = tokenResponse.IdToken,
                    RefreshToken = tokenResponse.RefreshToken,
                    TokenType = IdentityTokenType.Id,
                    UserLoginId = userLoginId,
                    CreatedOn = DateTime.UtcNow,
                    AccessToken = tokenResponse.AccessToken,
                }, transaction);
                await transaction.CommitAsync(cancellationToken);
                return userLoginId;
            }
            catch
            {
                await transaction.RollbackAsync(cancellationToken);
                throw;
            }
        }
        public async Task<bool> StoringUserTokenInfo(CognitoTokenResponse tokenResponse, ApplicationUser user, Guid? loginId, CancellationToken cancellationToken = default)
        {
            await using var conn = new NpgsqlConnection(
                string.IsNullOrEmpty(_tenantInfo?.ConnectionString)
                    ? _settings.ConnectionString
                    : _tenantInfo.ConnectionString);

            await conn.OpenAsync(cancellationToken);

            await using var transaction = await conn.BeginTransactionAsync(cancellationToken);

            try
            {
                var userLoginId = loginId;

                if (loginId == null || loginId == Guid.Empty)
                {
                    userLoginId = Guid.NewGuid();
                    var insertLoginSql = @"INSERT INTO ""Identity"".""UserLogins"" (""Id"", ""LoginProvider"", ""ProviderKey"", ""ProviderDisplayName"", ""UserId"", ""TenantId"", ""DeviceModel"", ""DeviceName"", ""DeviceUDID"", ""Platform"",""CreatedOn"")VALUES (@Id, @LoginProvider, @ProviderKey, @ProviderDisplayName, @UserId, @TenantId, @DeviceModel, @DeviceName, @DeviceUDID, @Platform,@CreatedOn);";
                    await conn.ExecuteAsync(insertLoginSql, new
                    {
                        Id = userLoginId.ToString(), // pass as Guid
                        LoginProvider = "Cognito",
                        ProviderKey = string.Empty,
                        ProviderDisplayName = "Cognito",
                        UserId = user.Id,
                        TenantId = _tenantInfo?.Id ?? string.Empty,
                        DeviceModel = string.Empty,
                        DeviceName = string.Empty,
                        DeviceUDID = string.Empty,
                        Platform = 2,
                        CreatedOn = DateTime.UtcNow

                    }, transaction);

                    var insertTokenSql = @"INSERT INTO ""Identity"".""UserTokens"" (""UserId"", ""LoginProvider"", ""Name"", ""Value"", ""TenantId"", ""IdToken"", ""RefreshToken"", ""TokenType"", ""UserLoginId"",""CreatedOn"")VALUES (@UserId, @LoginProvider, @Name, @Value, @TenantId, @IdToken, @RefreshToken, @TokenType, @UserLoginId,@CreatedOn);";

                    await conn.ExecuteAsync(insertTokenSql, new
                    {
                        UserId = user.Id,
                        LoginProvider = "Cognito",
                        Name = userLoginId,
                        Value = "RefreshToken",
                        TenantId = _tenantInfo?.Id ?? string.Empty,
                        IdToken = tokenResponse.IdToken,
                        RefreshToken = tokenResponse.RefreshToken,
                        TokenType = IdentityTokenType.Id,
                        UserLoginId = userLoginId,
                        CreatedOn = DateTime.UtcNow,
                    }, transaction);
                    await transaction.CommitAsync(cancellationToken);
                }
                else
                {

                    var checkSql = @"SELECT COUNT(1) FROM ""Identity"".""UserTokens"" WHERE ""UserId"" = @UserId AND ""LoginProvider"" = @LoginProvider AND ""Name"" = @Name;";

                    var exists = await conn.ExecuteScalarAsync<int>(checkSql, new
                    {
                        UserId = user.Id,
                        LoginProvider = "Cognito",
                        Name = userLoginId
                    }, transaction);

                    if (exists > 0)
                    {
                        // Update
                        var updateSql = @"UPDATE ""Identity"".""UserTokens"" 
                      SET ""Value"" = @Value,
                          ""TenantId"" = @TenantId,
                          ""IdToken"" = @IdToken,
                          ""RefreshToken"" = @RefreshToken,
                          ""TokenType"" = @TokenType,
                          ""UserLoginId"" = @UserLoginId,
                          ""CreatedOn"" = @CreatedOn
                      WHERE ""UserId"" = @UserId 
                        AND ""LoginProvider"" = @LoginProvider 
                        AND ""Name"" = @Name;";

                        await conn.ExecuteAsync(updateSql, new
                        {
                            UserId = user.Id,
                            LoginProvider = "Cognito",
                            Name = userLoginId,
                            Value = "RefreshToken",
                            TenantId = _tenantInfo?.Id ?? string.Empty,
                            IdToken = tokenResponse.IdToken,
                            RefreshToken = tokenResponse.RefreshToken,
                            TokenType = IdentityTokenType.Id,
                            UserLoginId = userLoginId,
                            CreatedOn = DateTime.UtcNow
                        }, transaction);
                    }
                    else
                    {
                        // Insert
                        var insertSql = @"INSERT INTO ""Identity"".""UserTokens"" (""UserId"", ""LoginProvider"", ""Name"", ""Value"", ""TenantId"", ""IdToken"", ""RefreshToken"", ""TokenType"", ""UserLoginId"", ""CreatedOn"") VALUES (@UserId, @LoginProvider, @Name, @Value, @TenantId, @IdToken, @RefreshToken, @TokenType, @UserLoginId, @CreatedOn);";

                        await conn.ExecuteAsync(insertSql, new
                        {
                            UserId = user.Id,
                            LoginProvider = "Cognito",
                            Name = userLoginId,
                            Value = "RefreshToken",
                            TenantId = _tenantInfo?.Id ?? string.Empty,
                            IdToken = tokenResponse.IdToken,
                            RefreshToken = tokenResponse.RefreshToken,
                            TokenType = IdentityTokenType.Id,
                            UserLoginId = userLoginId,
                            CreatedOn = DateTime.UtcNow
                        }, transaction);
                    }
                }
                return true;
            }
            catch
            {
                await transaction.RollbackAsync(cancellationToken);
                throw;
            }
        }
    }
}
