﻿using Lrb.Application.Project.Mobile.Dtos;
using Lrb.Application.Property.Mobile;
using Lrb.Application.Property.Mobile.Dtos;

namespace Lrb.Application.Project.Mobile
{
    public class UpdateProjectDto : CreateProjectDto
    {

    }
    public class CreateProjectDto : BaseProjectDto
    {
        public Guid? ProjectTypeId { get; set; }
    }
    public class ViewProjectDto : BaseProjectDto
    {
        public DateTime? LastModifiedOn { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid LastModifiedBy { get; set; }
        public List<ViewUnitTypeDto>? UnitTypes { get; set; }
        public List<ViewBlockDto>? Blocks { get; set; }
        public List<Guid>? ProjectAmenities { get; set; }
        public List<ProjectGelleryDto>? ProjectGalleries { get; set; }
        public List<ProjectGelleryDto>? Images { get; set; }
        public List<ProjectGelleryDto>? Videos { get; set; }
        public ProjectTypeDto? ProjectType { get; set; }
        public List<Guid>? AssociatedBanks { get; set; }
        public string? SerialNo { get; set; }
        public Dictionary<ContactType, int>? ContactRecords { get; set; }
        public List<string>? Links { get; set; }
        public List<BrochureDto>? Brochures { get; set; }
        public string? ShortUrl { get; set; }
    }
    public class BaseProjectDto : IDto
    {
        public Guid Id { get; set; }
        public string? Name { get; set; }
        public string? Certificates { get; set; }
        public Facing? Facing { get; set; }
        public List<string>? ReraNumbers { get; set; }
        public string? Description { get; set; }
        public double? TotalFlats { get; set; }
        public double? TotalBlocks { get; set; }
        public double? TotalFloor { get; set; }
        public double? Area { get; set; }
        public Guid? AreaUnitId { get; set; }
        public ProjectMonetaryInfoDto? MonetaryInfo { get; set; }
        public ProjectBuilderDetailsDto? BuilderDetail { get; set; }
        public ProjectStatus Status { get; set; }
        public ProjectCurrentStatus CurrentStatus { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime? PossessionDate { get; set; }
        public AddressDto? Address { get; set; }
        public bool IsGlobal { get; set; }
        public bool IsArchived { get; set; }
        public DateTime? ArchivedOn { get; set; }
        public Guid? ArchivedBy { get; set; }
        public double? MinimumPrice { get; set; }
        public double? MaximumPrice { get; set; }
        public List<Guid>? AssignedUserIds { get; set; }
        public PossesionType PossesionType { get; set; }
        public List<Facing>? Facings { get; set; }
        public string? Notes { get; set; }
        public double? Length { get; set; }
        public double? Breadth { get; set; }

    }
    public class ProjectDto
    {
        public Guid? Id { get; set; }
        public string? Name { get; set; }
        public ProjectCurrentStatus CurrentStatus { get; set; }
    }
    public class BasicProjectDto
    {
        public Guid? Id { get; set; }
        public string? Name { get; set; }
        public ProjectBuilderInfosDto? BuilderDetail { get; set; }
        public ProjectMonetaryInfoDto? MonetaryInfo { get; set; }
    }
    public class ProjectTitleDto
    {
        public Guid Id { get; set; }
        public string? Name { get; set; }
        public int? NumberOfClones { get; set; }
    }

}
