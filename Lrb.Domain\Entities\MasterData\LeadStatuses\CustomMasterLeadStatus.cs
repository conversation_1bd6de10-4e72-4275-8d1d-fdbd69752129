﻿namespace Lrb.Domain.Entities.MasterData
{
    public class CustomMasterLeadStatus : AuditableEntity, IAggregateRoot
    {
        public DefaultIdType? BaseId { get; set; }
        public int Level { get; set; }
        public string? Status { get; set; }
        public string? DisplayName { get; set; }
        public string? ActionName { get; set; }
        public int OrderRank { get; set; }
        public Guid? MasterLeadStatusId { get; set; }
        public Guid? MasterLeadStatusBaseId { get; set; }
        public bool IsDefault { get; set; }
        public bool IsDefaultChild { get; set; }
        public bool IsLrbStatus { get; set; }
        public bool IsScheduled { get; set; }
        public bool ShouldBeHidden { get; set; }
        public bool ShouldUseForBooking { get; set; }
        public bool? ShouldUseForMeeting { get; set; }
        public bool ShouldUseForBookingCancel { get; set; }
        public bool ShouldOpenAppointmentPage { get; set; }
        public List<CustomFilter>? CustomFilters { get; set; }
        public List<CustomField>? CustomFields { get; set; }
        public List<Team>? Teams { get; set; }
        public List<Guid>? WhatsAppTemplateInfoIds { get; set; }
        public bool? ShouldUseForInvoice { get; set; }
        public List<FacebookAuthResponse>? FacebookAuthResponses { get; set; }
        public List<TeamConfiguration>? TeamConfigurations { get; set; }

    }
}
