﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.Lead.Web;
using Lrb.Application.ZonewiseLocation.Web.Specs;

namespace Lrb.Application.Integration.Web.Requests
{
    public class CreateIVRIntegrationAccountRequest : IRequest<Response<string>>
    {
        public string? AccountName { get; set; }
        public LeadSource Source { get; set; }
        public IVRType CallType { get; set; }
        public Dictionary<string, string>? Credentials { get; set; }
        public string? AuthToken { get; set; }
        public List<IVRAssignmentDto>? IVRAssignmentDtos { get; set; }
        public string? ServiceProvider { get; set; }
        public IVRApiConfigurationDto? IVRApiConfigurationDto { get; set; }
        public bool? IsVirtualNumberRequiredForOutbound { get; set; }
    }
    public class CreateIVRIntegrationAccountRequestHandler : IRequestHandler<CreateIVRIntegrationAccountRequest, Response<string>>
    {
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccountInfoRepositoryAsync;
        private readonly IBlobStorageService _blobStorageService;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<TempProjects> _tempProjectsRepo;
        private readonly IRepositoryWithEvents<Location> _locationRepo;
        private readonly IRepositoryWithEvents<IVRApiConfiguration> _ivrApiConfigurationRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        public CreateIVRIntegrationAccountRequestHandler(
            IRepositoryWithEvents<IntegrationAccountInfo> integrationAccountInfoRepositoryAsync,
            ICurrentUser currentUser,
            IBlobStorageService blobStorageService,
            IRepositoryWithEvents<TempProjects> tempProjectsRepo,
            IRepositoryWithEvents<Location> locationRepo,
            IRepositoryWithEvents<IVRApiConfiguration> ivrApiConfigurationRepo,
            IRepositoryWithEvents<Domain.Entities.Project> projectRepo)
        {
            _integrationAccountInfoRepositoryAsync = integrationAccountInfoRepositoryAsync;
            _blobStorageService = blobStorageService;
            _currentUser = currentUser;
            _tempProjectsRepo = tempProjectsRepo;
            _locationRepo = locationRepo;
            _ivrApiConfigurationRepo = ivrApiConfigurationRepo;
            _projectRepo = projectRepo;
        }
        public async Task<Response<string>> Handle(CreateIVRIntegrationAccountRequest request, CancellationToken cancellationToken)
        {
            request.Source = LeadSource.IVR;
            var tenant = _currentUser.GetTenant();
            //Todo fix user and tenant
            Guid userId = _currentUser.GetUserId();
            IntegrationAccountInfo? integrationAccount = null;
            IDictionary<string, string> data = null;
            IVRServiceProvider? ivrServiceProvider = Enum.TryParse<IVRServiceProvider>(request.ServiceProvider, out var serviceProv) ? serviceProv : default;
            integrationAccount = CreateIntegrationEntity(request, userId, ivrServiceProvider);
            var apiKey = ApiKeyHelper.GenerateApiKey(integrationAccount.Id);
            integrationAccount.ApiKey = apiKey;
            data = new Dictionary<string, string>(IntegrationTemplateBuilder.CreateIntegrationTemplate(tenant, integrationAccount, request.CallType, ivrServiceProvider ?? IVRServiceProvider.None));
            List<IVRAssignment> ivrAssignments = new();
            var tempProjectsToAssign = await _projectRepo.ListAsync(new GetNewProjectsByIdV2Spec(request.IVRAssignmentDtos?.Select(i => i.ProjectId ?? Guid.Empty).ToList() ?? new()), cancellationToken);
            var locationsToAssign = await _locationRepo.ListAsync(new LocationByIdSpec(request.IVRAssignmentDtos?.Select(i => i.LocationId ?? Guid.Empty).ToList() ?? new()), cancellationToken);
            IVRApiConfiguration? existingIVRApiConfiguration = await _ivrApiConfigurationRepo.FirstOrDefaultAsync(new GetIVRApiConfigurationByCustomFiltersSpec(request, ivrServiceProvider ?? default), cancellationToken);
            if (existingIVRApiConfiguration == null)
            {
                existingIVRApiConfiguration = request.IVRApiConfigurationDto?.Adapt<IVRApiConfiguration>() ?? new();
                existingIVRApiConfiguration.IVRServiceProvider = ivrServiceProvider ?? default;
            }
            else if(request.IVRApiConfigurationDto != null && existingIVRApiConfiguration.IVRServiceProvider == ivrServiceProvider)
            {
                existingIVRApiConfiguration.AuthToken = !string.IsNullOrWhiteSpace(request.IVRApiConfigurationDto.AuthToken ?? string.Empty) ? request.IVRApiConfigurationDto.AuthToken : existingIVRApiConfiguration.AuthToken;
                existingIVRApiConfiguration.ApiKey = !string.IsNullOrWhiteSpace(request.IVRApiConfigurationDto.ApiKey ?? string.Empty) ? request.IVRApiConfigurationDto.ApiKey : existingIVRApiConfiguration.ApiKey;
                existingIVRApiConfiguration.BaseUri = !string.IsNullOrWhiteSpace(request.IVRApiConfigurationDto.BaseUri ?? string.Empty) ? request.IVRApiConfigurationDto.BaseUri : existingIVRApiConfiguration.BaseUri;
                existingIVRApiConfiguration.CallerId = !string.IsNullOrWhiteSpace(request.IVRApiConfigurationDto.CallerId ?? string.Empty) ? request.IVRApiConfigurationDto.CallerId : existingIVRApiConfiguration.CallerId;
                await _ivrApiConfigurationRepo.UpdateAsync(existingIVRApiConfiguration);
            }
            if (request.CallType == IVRType.Outbound && (request.IsVirtualNumberRequiredForOutbound ?? false))
            {
                request.IVRAssignmentDtos?.ForEach(i =>
                {
                    ivrAssignments.Add(new()
                    {
                        VirtualNumber = i.VirtualNumber,
                        Assignment = new()
                        {
                            Project = tempProjectsToAssign.FirstOrDefault(j => j.Id == i.ProjectId),
                            Location = locationsToAssign.FirstOrDefault(j => j.Id == i.LocationId)
                        },
                        UserIds = i.UserIds
                    });
                });
                existingIVRApiConfiguration.IsVirtualNumberRequiredForOutbound = true;
            }
            else if(request.CallType == IVRType.Inbound)
            {
                request.IVRAssignmentDtos?.ForEach(i =>
                {
                    ivrAssignments.Add(new()
                    {
                        VirtualNumber = i.VirtualNumber,
                        AgencyName = i.AgencyName,
                        Assignment = new()
                        {
                            Project = tempProjectsToAssign.FirstOrDefault(j => j.Id == i.ProjectId),
                            Location = locationsToAssign.FirstOrDefault(j => j.Id == i.LocationId)
                        },
                    });
                });
            }
            
            integrationAccount.IVRAssignments = ivrAssignments;
            integrationAccount.IVRApiConfiguration = existingIVRApiConfiguration;
            await _integrationAccountInfoRepositoryAsync.AddAsync(integrationAccount);
            string key = string.Empty;
            byte[] bytes = IntegrationExcelHelper.CreateExcelData(data).ToArray();
            string fileName = $"{tenant}-{integrationAccount.Id}-{DateTime.Now.ToString("yyyy_MM_dd-HH_mm_ss")}.xlsx";
            string folder = "Integration";
            key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", folder, fileName, bytes);
            string fileUrl = await _blobStorageService.GetPreSignedURL(_blobStorageService?.BucketName ?? "prd-leadratblack", key);
            integrationAccount.FileUrl = key;
            await _integrationAccountInfoRepositoryAsync.UpdateAsync(integrationAccount);
            return new(fileUrl, default);
        }
        private IntegrationAccountInfo CreateIntegrationEntity(CreateIVRIntegrationAccountRequest command, Guid userId, IVRServiceProvider? serviceProvider)
        {
            return new IntegrationAccountInfo()
            {
                Id = Guid.NewGuid(),
                AccountName = command.AccountName,
                LeadSource = command.Source,
                LicenseId = Guid.NewGuid(),
                JsonTemplate = IntegrationTemplateBuilder.GetRequestBodyJsonFromFile(command.Source, serviceProvider ?? IVRServiceProvider.None),
                CreatedBy = userId,
                Credentials = command.Credentials,
                IVRCallType = command.CallType,
            };
        }
    }
}
