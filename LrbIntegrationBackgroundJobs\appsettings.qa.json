﻿{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "DatabaseSettings": {
    "DBProvider": "postgresql",
    //"ConnectionString": "Host=ls-d76bceb7cc3cad167845c334973fa18c0d93b476.coluzekxwdtv.ap-south-1.rds.amazonaws.com;Port=5432;Database=leadratBlackApp;Username=dbmasteruser;Password=********************************;Timeout=300;CommandTimeout=300;"
    "ConnectionString": "Host=lrb-qa-new.postgres.database.azure.com;Port=5432;Database=leadratBlackApp;Username=dbmasteruser;Password=********************************;Pooling=true;MinPoolSize=3;MaxPoolSize=5000;"
  },
  "WorkingEndPoint": {
    "LrbBaseUri": "https://connect.leadrat.info",
    "JustLeadBaseUri": "https://www.lms.justlead.in",
    "CommonFloorBaseuri": "https://www.commonfloor.com",
    "BayutBaseUri": "https://www.bayut.com/api-v7/stats",
    "PropertyFinderBaseUri": "http://api-v2.mycrm.com/",
    "DubizzleBaseUrl": "https://dubizzle.com/profolio/api-v7/stats",
    "V2PropertyFinderBaseUri": "https://atlas.propertyfinder.com"
  },
  "CosmosSettings": {
    "EndpointUri": "https://lrb-prd.documents.azure.com:443/",
    "PrimaryKey": "****************************************************************************************"
  }
}