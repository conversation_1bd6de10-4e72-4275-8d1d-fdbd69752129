﻿using DocumentFormat.OpenXml.Wordprocessing;
using Lrb.Application.ZonewiseLocation.Web.Dtos;
using Lrb.Application.ZonewiseLocation.Web.Requests;
using Lrb.Shared.Extensions;

namespace Lrb.Application.ZonewiseLocation.Web.Specs
{

    public class ZoneSpec : EntitiesByPaginationFilterSpec<Zone, ZoneDto>
    {
        public ZoneSpec(GetAllZonesRequest filter) : base(filter)
        {
            Query
                .Include(i => i.City)
                .Where(i => !i.IsDeleted)
                .OrderByDescending(i => i.LastModifiedOn);
            if (filter.Ids != null && filter.Ids.Any())
            {
                Query.Where(i => filter.Ids.Contains(i.Id));
            }
            if (filter.CityIds != null && filter.CityIds.Any())
            {
                Query.Where(i => i.City != null && filter.CityIds.Contains(i.City.Id));
            }
            if(filter.Names != null && filter.Names.Any())
            {
                var normalizedNames = filter.Names.Select(i => i.LrbNormalize());
                Query.Where(i =>  normalizedNames.Contains(i.NormalizedName));
            }
            if (!string.IsNullOrWhiteSpace(filter.SearchText))
            {
                string normalizedText = filter.SearchText.LrbNormalize();
                Query.Where(i => !string.IsNullOrEmpty(i.NormalizedName) && i.NormalizedName.Contains(normalizedText));
            }
        }
    }
    public class ZoneFilterSpec : Specification<Zone>
    {
        public ZoneFilterSpec(GetAllZonesRequest filter) 
        {
            Query
                .Include(i => i.City)
                    .ThenInclude(i => i.State)
                        .ThenInclude(i => i.Country)
                .Include(i => i.UserAssignment)
                .Where(i => !i.IsDeleted)
                .OrderByDescending(i => i.LastModifiedOn);
            if (filter.Ids != null && filter.Ids.Any())
            {
                Query.Where(i => filter.Ids.Contains(i.Id));
            }
            if (filter.CityIds != null && filter.CityIds.Any())
            {
                Query.Where(i => i.City != null && filter.CityIds.Contains(i.City.Id));
            }
            if (filter.Names != null && filter.Names.Any())
            {
                var normalizedNames = filter.Names.Select(i => i.LrbNormalize());
                Query.Where(i => normalizedNames.Contains(i.NormalizedName));
            }
            if (!string.IsNullOrWhiteSpace(filter.SearchText))
            {
                string normalizedText = filter.SearchText.LrbNormalize();
                Query.Where(i => !string.IsNullOrEmpty(i.NormalizedName) && i.NormalizedName.Contains(normalizedText));
            }
        }
    }
    public class ZoneCountSpec : Specification<Zone>
    {
        public ZoneCountSpec(GetAllZonesRequest filter)
        {
            Query
                .Include(i => i.City)
                    .ThenInclude(i => i.State)
                        .ThenInclude(i => i.Country)
                .Include(i => i.UserAssignment)
                .Where(i => !i.IsDeleted)
                .OrderByDescending(i => i.LastModifiedOn);

            if (filter.Ids != null && filter.Ids.Any())
            {
                Query.Where(i => filter.Ids.Contains(i.Id));
            }
            if (filter.CityIds != null && filter.CityIds.Any())
            {
                Query.Where(i => i.City != null && filter.CityIds.Contains(i.City.Id));
            }
            if (filter.Names != null && filter.Names.Any())
            {
                var normalizedNames = filter.Names.Select(i => i.LrbNormalize());
                Query.Where(i => normalizedNames.Contains(i.NormalizedName));
            }
            if (!string.IsNullOrWhiteSpace(filter.SearchText))
            {
                string normalizedText = filter.SearchText.LrbNormalize();
                Query.Where(i => !string.IsNullOrEmpty(i.NormalizedName) && i.NormalizedName.Contains(normalizedText));
            }
        }
    }
    public class ZoneByIdSpec : Specification<Zone>
    {
        public ZoneByIdSpec(Guid id)
        {
            Query
                .Include(i => i.City)
                    .ThenInclude(i => i.State)
                        .ThenInclude(i => i.Country)
                .Include(i => i.UserAssignment)
                .Where(i => !i.IsDeleted && i.Id == id)
                .OrderByDescending(i => i.LastModifiedOn);
        } 
        public ZoneByIdSpec(Guid id, Guid cityId, string cityName)
        {
            Query
                .Include(i => i.City)
                    .ThenInclude(i => i.State)
                        .ThenInclude(i => i.Country)
                .Include(i => i.UserAssignment)
                .Where(i => !i.IsDeleted && i.Id == id)
                .OrderByDescending(i => i.LastModifiedOn);
            if(cityId != default)
            {
                Query.Where(i => i.City != null && i.City.Id == cityId);
            }
            if (!string.IsNullOrWhiteSpace(cityName))
            {
                Query.Where(i => i.City != null && i.City.NormalizedName != null && i.City.NormalizedName.Contains(cityName.LrbNormalize()));
            }
        }
        public ZoneByIdSpec(List<Guid> ids)
        {
            Query
                .Include(i => i.City)
                    .ThenInclude(i => i.State)
                        .ThenInclude(i => i.Country)
                .Include(i => i.UserAssignment)
                .Where(i => !i.IsDeleted && ids.Contains(i.Id))
                .OrderByDescending(i => i.LastModifiedOn);
        }
    }
    public class ZoneByNameSpec : Specification<Zone>
    {
        public ZoneByNameSpec(string? name, Guid cityId, string cityName)
        {
            Query
                .Include(i => i.City)
                    .ThenInclude(i => i.State)
                        .ThenInclude(i => i.Country)
                .OrderByDescending(i => i.LastModifiedOn);

            var nornalizedName = name.LrbNormalize();
            Query.Where(i => !i.IsDeleted && i.NormalizedName != null && i.NormalizedName == nornalizedName);
            if (cityId != default)
            {
                Query.Where(i => i.City != null && i.City.Id == cityId);
            }
            if (!string.IsNullOrWhiteSpace(cityName))
            {
                Query.Where(i => i.City != null && i.City.NormalizedName != null && i.City.NormalizedName.Contains(cityName.LrbNormalize()));
            }
        }
        public ZoneByNameSpec(string? name)
        {
            Query
                .Include(i => i.City)
                    .ThenInclude(i => i.State)
                        .ThenInclude(i => i.Country)
                .OrderByDescending(i => i.LastModifiedOn);

            var nornalizedName = name.LrbNormalize();
            Query.Where(i => !i.IsDeleted && i.NormalizedName != null && i.NormalizedName == nornalizedName);
        }
        public ZoneByNameSpec(string name, string cityName)
        {
            Query
                .Include(i => i.City)
                    .ThenInclude(i => i.State)
                        .ThenInclude(i => i.Country)
                .OrderByDescending(i => i.LastModifiedOn);

            var nornalizedName = (cityName + "-" + (cityName != null ? name.Replace($"{cityName}-", "").Trim() : name[(name.IndexOf("-") + 1)..].Trim())).LrbNormalize();
            Query.Where(i => !i.IsDeleted && i.NormalizedName != null && i.NormalizedName == nornalizedName);
        }
    }
}
