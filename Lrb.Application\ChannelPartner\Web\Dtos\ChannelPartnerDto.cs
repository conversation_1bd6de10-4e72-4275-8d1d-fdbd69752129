﻿using Lrb.Application.Project.Web;
using Lrb.Application.Property.Web;

namespace Lrb.Application.ChannelPartner.Web.Dtos
{
    public class ChannelPartnerDto
    {
        public string? FirmName { get; set; }
        public Guid? Id { get; set; }
    }
    public class ViewChannelPartnerDto : BaseChannelPartnerDto
    {
        public int LeadsCount { get; set; }
        public int ProspectCount { get; set; }
        public List<ProjectDto>? Projects { get; set; }
    }
    public class CreateOrUpdateChannelPartnerDto : BaseChannelPartnerDto
    {
        public List<string>? ProjectsList { get; set; }
    }
    public class BaseChannelPartnerDto
    {
        public Guid CreatedBy { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid LastModifiedBy { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public string? FirmName { get; set; }
        public Guid Id { get; set; }
        public string? ContactNo { get; set; }
        public string? CountryCode { get; set; }
        public string? Email { get; set; }
        public AddressDto? Address { get; set; }
        public string? RERANumber { get; set; }
        public string? CompanyName { get; set; }
    }
}
