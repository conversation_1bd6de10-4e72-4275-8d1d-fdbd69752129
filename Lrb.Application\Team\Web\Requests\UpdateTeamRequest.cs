﻿using Lrb.Application.Automation.Specs;
using Lrb.Application.Identity.Users;
using Lrb.Application.Team.Web.Dtos;
using Lrb.Application.ZonewiseLocation.Web.Specs;
using System.Collections.Generic;

namespace Lrb.Application.Team.Web
{
    public class UpdateTeamRequest : IRequest<Response<Guid>>
    {
        public Guid? Id { get; set; }
        public string Name { get; set; }
        public List<Guid>? UserIds { get; set; }
        public Guid? Manager { get; set; }
        public TeamConfigurationDto? Configuration { get; set; }
        public Guid? IntegrationAccountInfoId { get; set; }
        public List<string>? IntegrationAccountNames { get; set; }

    }

    public class UpdateTeamRequestHandler : IRequestHandler<UpdateTeamRequest, Response<Guid>>
    {
        private readonly IUserService _userService;
        private readonly IRepositoryWithEvents<Domain.Entities.Team> _teamRepository;
        private readonly IRepositoryWithEvents<TeamConfiguration> _teamConfigRepository;
        private readonly IRepositoryWithEvents<UserAssignment> _userAssignmentRepo;
        private readonly IReadRepository<Location> _locationRepo;
        private readonly IReadRepository<Zone> _zoneRepo;
        private readonly IReadRepository<City> _cityRepo;

        public UpdateTeamRequestHandler(
            IUserService userService,
            IRepositoryWithEvents<Domain.Entities.Team> teamRepository,
            IRepositoryWithEvents<TeamConfiguration> teamConfigRepository,
            IRepositoryWithEvents<UserAssignment> userAssignmentRepo,
            IReadRepository<Location> locationRepo,
            IReadRepository<Zone> zoneRepo,
            IReadRepository<City> cityRepo
            )
        {
            _userService = userService;
            _teamRepository = teamRepository;
            _teamConfigRepository = teamConfigRepository;
            _userAssignmentRepo = userAssignmentRepo;
            _locationRepo = locationRepo;
            _zoneRepo = zoneRepo;
            _cityRepo = cityRepo;
        }

        public async Task<Response<Guid>> Handle(UpdateTeamRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var teamId = request.Id ?? throw new ArgumentException("Team ID is required.");
                var integrationAccountId = request.IntegrationAccountInfoId ?? Guid.Empty;

                var existingTeam = await _teamRepository.FirstOrDefaultAsync(new GetTeamByIdSpec(teamId), cancellationToken)
                    ?? throw new NotFoundException("No team found by this Id!");

                if (request.Configuration != null)
                {
                    if (request.Configuration.Id == null || request.Configuration.Id == Guid.Empty)
                    {
                        await AddNewTeamConfigurationAsync(request, existingTeam, integrationAccountId, cancellationToken);
                    }
                    else if (existingTeam.Configurations?.Any() ?? false)
                    {
                        await UpdateExistingConfigurationAsync(request, existingTeam, integrationAccountId, cancellationToken);
                    }
                }
                else if (request.Configuration == null && integrationAccountId != Guid.Empty)
                {
                    request.UserIds = existingTeam.UserIds;
                    request.Manager = existingTeam.Manager;
                    await RemoveIntegrationAccountFromOtherConfigsAsync(integrationAccountId, cancellationToken);
                }
                existingTeam = request.Adapt(existingTeam);

                if ((existingTeam.UserIds?.Count() ?? 0) <= 1)
                    existingTeam.IsRotationEnabled = false;

                if ((existingTeam.Configurations?.Any() ?? false) && (existingTeam.UserIds?.Any() ?? false))
                    await SyncUserAssignmentsAsync(existingTeam, cancellationToken);

                await _teamRepository.UpdateAsync(existingTeam, cancellationToken);

                return new(existingTeam.Id, "true");
            }
            catch (Exception ex)
            {
                // Log the exception if logging is enabled
                throw new InvalidOperationException($"Failed to update team: {ex.Message}", ex);
            }
        }
        private async Task AddNewTeamConfigurationAsync(UpdateTeamRequest request, Domain.Entities.Team existingTeam, Guid integrationAccountId, CancellationToken cancellationToken)
        {
            var locationsIds = request.Configuration?.LocationsIds;
            var zonesIds = request.Configuration?.ZonesIds;
            var citiesIds = request.Configuration?.CitiesIds;
            var newConfig = request.Configuration?.Adapt<TeamConfiguration>();
            newConfig.TeamId = existingTeam.Id;
            if (request.Configuration != null && (locationsIds?.Any() ?? false))
            {
              newConfig=  await AddOrUpdateTeamConfigurationLocationsAsync(integrationAccountId, locationsIds,newConfig, cancellationToken);
            }
            if (request.Configuration != null && (citiesIds?.Any() ?? false))
            {
              newConfig=  await AddOrUpdateTeamConfigurationCitiesAsync(integrationAccountId, citiesIds,newConfig, cancellationToken);
            }
            if (request.Configuration != null && (zonesIds?.Any() ?? false))
            {
               newConfig= await AddOrUpdateTeamConfigurationZonesAsync(integrationAccountId, zonesIds,newConfig, cancellationToken);
            }
            if (integrationAccountId != Guid.Empty)
            {
                await RemoveIntegrationAccountFromOtherConfigsAsync(integrationAccountId, cancellationToken);
                newConfig.IntegrationAccountIds = new List<Guid> { integrationAccountId };
            }
            await _teamConfigRepository.AddAsync(newConfig);
        }

        private async Task UpdateExistingConfigurationAsync(UpdateTeamRequest request, Domain.Entities.Team existingTeam, Guid integrationAccountId, CancellationToken cancellationToken)
        {
            var locationsIds = request.Configuration?.LocationsIds;
            var zonesIds = request.Configuration?.ZonesIds;
            var citiesIds = request.Configuration?.CitiesIds;
            var existingConfig = existingTeam.Configurations?.FirstOrDefault(i => i.IsForRetention == false);
            if (existingConfig == null) return;

            existingConfig = request.Configuration.Adapt(existingConfig);
            if (request.Configuration != null && (locationsIds?.Any() ?? false))
            {
                existingConfig = await AddOrUpdateTeamConfigurationLocationsAsync(integrationAccountId, locationsIds, existingConfig, cancellationToken);
            }
            if (request.Configuration != null && (citiesIds?.Any() ?? false))
            {
                existingConfig = await AddOrUpdateTeamConfigurationCitiesAsync(integrationAccountId, citiesIds, existingConfig, cancellationToken);
            }
            if (request.Configuration != null && (zonesIds?.Any() ?? false))
            {
                existingConfig = await AddOrUpdateTeamConfigurationZonesAsync(integrationAccountId, zonesIds, existingConfig, cancellationToken);
            }
            if (integrationAccountId != Guid.Empty)
            {
                await RemoveIntegrationAccountFromOtherConfigsAsync(integrationAccountId, cancellationToken);

                if (existingConfig.IntegrationAccountIds?.Any()?? false)
                {
                    existingConfig.IntegrationAccountIds.Add(integrationAccountId);
                    existingConfig.IntegrationAccountIds = existingConfig.IntegrationAccountIds.Distinct().ToList();
                }
                else
                {
                    existingConfig.IntegrationAccountIds = new List<Guid> { integrationAccountId };
                }
            }

            await _teamConfigRepository.UpdateAsync(existingConfig);
        }

        private async Task RemoveIntegrationAccountFromOtherConfigsAsync(Guid integrationAccountId, CancellationToken cancellationToken)
        {
            var existingConfigs = await _teamConfigRepository.ListAsync(new GetTeamConfigurationByByAccountIdSpec(integrationAccountId), cancellationToken);
            if (!(existingConfigs?.Any() ?? false)) return;

            foreach (var config in existingConfigs)
            {
                config.IntegrationAccountIds?.Remove(integrationAccountId);
            }

            await _teamConfigRepository.UpdateRangeAsync(existingConfigs);
        }

        private async Task SyncUserAssignmentsAsync(Domain.Entities.Team team, CancellationToken cancellationToken)
        {
            var assignments = await _userAssignmentRepo.ListAsync(new GetUserAssignmentByEntityIdSpec(team.Id), cancellationToken);
            var teamUserIds = team.UserIds?.ToHashSet() ?? new();

            foreach (var assignment in assignments)
            {
                if (assignment?.UserAssignmentType == UserAssignmentType.Team &&
                    (team.UserIds?.Count() ?? 0) <= (assignment?.UserIds?.Count() ?? 0) &&
                    !(assignment?.UserIds?.ToHashSet().SetEquals(teamUserIds) ?? false))
                {
                    double percentage = 100;
                    assignment.UserIds = teamUserIds.ToList();
                    assignment.UserAssignmentConfigurations = assignment.UserAssignmentConfigurations != null && assignment.UserAssignmentConfigurations.Any()
                        ? assignment.UserAssignmentConfigurations.Where(i => teamUserIds.Contains(i.Id)).ToList()
                        : assignment.UserAssignmentConfigurations;
                    int userCount = assignment?.UserAssignmentConfigurations?.Count ?? 0;

                    if (userCount > 0 && (assignment?.UserAssignmentConfigurations?.Any() ?? false))
                    {
                        int basePercentage = (int)(percentage / userCount);
                        int totalAssigned = basePercentage * userCount;
                        int remainder = (int)percentage - totalAssigned;

                        for (int i = 0; i < userCount; i++)
                        {
                            assignment.UserAssignmentConfigurations[i].Percentage = basePercentage;
                        }

                        for (int i = 0; i < remainder; i++)
                        {
                            assignment.UserAssignmentConfigurations[i].Percentage += 1;
                        }
                        await _userAssignmentRepo.UpdateAsync(assignment);
                    }
                }
                else if((assignment?.UserIds?.Any() ?? false) && (teamUserIds?.Any() ?? false))
                {
                    var userIds = teamUserIds.ToList();
                    assignment.UserIds = userIds;
                    if (assignment?.UserAssignmentConfigurations?.Any() ?? false)
                    {
                        assignment.UserAssignmentConfigurations = assignment.UserAssignmentConfigurations?
                                    .OrderBy(id => userIds.IndexOf(id.UserId))
                                    .ToList();
                    }
                    await _userAssignmentRepo.UpdateAsync(assignment);
                }
            }
        }
        private async Task<TeamConfiguration> AddOrUpdateTeamConfigurationLocationsAsync(Guid integrationAccountId, List<Guid> locationIds,TeamConfiguration config, CancellationToken cancellationToken)
        {
            var locations = await _locationRepo.ListAsync(new LocationByIdSpec(locationIds), cancellationToken);
            if (config?.Locations?.Any() ?? false)
            {
                config.Locations.AddRange(locations);
                config.Locations.DistinctBy(x => x.Id);
            }
            else
            {
                config.Locations = locations;
            }                
            return config;
        }
        private async Task<TeamConfiguration> AddOrUpdateTeamConfigurationCitiesAsync(Guid integrationAccountId, List<Guid> citiesIds, TeamConfiguration config, CancellationToken cancellationToken)
        {

            var cities = await _cityRepo.ListAsync(new CityByIdSpec(citiesIds), cancellationToken);
            if (config?.Cities?.Any() ?? false)
            {
                config.Cities.AddRange(cities);
                config.Cities.DistinctBy(x => x.Id);
            }
            else
            {
                config.Cities = cities;
            }
            return config;
        }
        private async Task<TeamConfiguration> AddOrUpdateTeamConfigurationZonesAsync(Guid integrationAccountId, List<Guid> zonesIds, TeamConfiguration config, CancellationToken cancellationToken)
        {
            var zones = await _zoneRepo.ListAsync(new ZoneByIdSpec(zonesIds), cancellationToken);
            if (config?.Zones?.Any() ?? false)
            {
                config.Zones.AddRange(zones);
                config.Zones.DistinctBy(x => x.Id);
            }
            else
            {
                config.Zones = zones;
            }
            return config;
        }
    }
}
