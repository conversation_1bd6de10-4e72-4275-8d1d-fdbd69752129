﻿using Lrb.Application.Utils;

namespace Lrb.Application.Lead.Mobile
{
    public class LeadByCustomFilterSpec : EntitiesByPaginationFilterSpec<Domain.Entities.Lead>
    {
        public LeadByCustomFilterSpec(GetAllLeadsRequest filter, LeadFilterTypeMobile category, ICurrentUser currentUser, List<Guid> propertiesLeadIds, List<Guid> projectLeadIds) : base(filter)
        {
            var subIds = currentUser.GetSubordinateIds() ?? new();
            var reporteeIds = subIds.Where(i => i != currentUser.GetUserId() && i != Guid.Empty);
            var selfWithReporteeIds = subIds.Where(i => i != Guid.Empty);
            //by default we are using  OrderByDescending here, It should get it done by filters later.
            //by default we are using OrderByDescending here, It should get it done by filters later.
            Query.Where(i => !i.IsDeleted)
                .Where(i => subIds.Contains(i.AssignTo))
                .Include(i => i.TagInfo)
                //.Include(i => i.Status)
                .Include(i => i.Enquiries)
                //.ThenInclude(i => i.Address)
                .ThenInclude(i => i.Addresses)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyType)
                .Include(i => i.Projects)
                .Include(i => i.Properties)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyTypes)
                .OrderByDescending(i => i.LastModifiedOn);

            switch (filter.LeadVisibility)
            {
                case BaseLeadVisibility.SelfWithReportee:
                    Query.Where(i => selfWithReporteeIds.Contains(i.AssignTo));
                    break;
                case BaseLeadVisibility.Self:
                    Query.Where(i => i.AssignTo == currentUser.GetUserId());
                    break;
                case BaseLeadVisibility.Reportee:
                    Query.Where(i => reporteeIds.Contains(i.AssignTo));
                    break;
                case BaseLeadVisibility.UnassignLead:
                    Query.Where(i => i.AssignTo == Guid.Empty);
                    break;
                case BaseLeadVisibility.ReEnquired:
                    Query.Where(i => i.IsReEnquired == true);
                    break;
                default:
                    break;
            }
            if (filter.Properties != null && filter.Properties.Any() && propertiesLeadIds.Any())
            {
                Query.Where(i => propertiesLeadIds.Contains(i.Id));
            }
            if (filter.Projects != null && filter.Projects.Any() && projectLeadIds.Any())
            {
                Query.Where(i => projectLeadIds.Contains(i.Id));
            }
            //if (filter.EnquiredFor != null)
            //{
            //    Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.EnquiredFor == filter.EnquiredFor && i.IsPrimary));
            //}
            if (filter.DateType != null && filter.FromDate != null && filter.FromDate.Value != default && filter.ToDate != null && filter.ToDate.Value != default)
            {
                filter.FromDate = filter.FromDate.Value.ConvertFromDateToUtc();
                filter.ToDate = filter.ToDate.Value.ConvertToDateToUtc();
                switch (filter.DateType)
                {
                    case DateType.ReceivedDate:
                        Query.Where(i => i.CreatedOn >= filter.FromDate.Value && i.CreatedOn <= filter.ToDate.Value);
                        break;
                    case DateType.ScheduledDate:
                        Query.Where(i => i.ScheduledDate != null && i.ScheduledDate.Value >= filter.FromDate.Value && i.ScheduledDate.Value <= filter.ToDate.Value);
                        break;
                    case DateType.ModifiedDate:
                        Query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value && i.LastModifiedOn.Value <= filter.ToDate.Value);
                        break;
                    default:
                        break;
                }
            }
            if (filter.AssignTo != null && filter.AssignTo != Guid.Empty)
            {
                Query.Where(i => i.AssignTo == filter.AssignTo.Value);
            }
            if (!string.IsNullOrWhiteSpace(filter.SearchByNameOrNumber))
            {
                Query.Where(i => i.ContactNo.Contains(filter.SearchByNameOrNumber.Replace(" ", "")) || i.Name.ToLower().Contains(filter.SearchByNameOrNumber.ToLower()) || i.SerialNumber.ToLower().Contains(filter.SearchByNameOrNumber.ToLower()));
            }

            if (filter?.Source?.Any() ?? false)
            {
                Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => filter.Source.Contains(i.LeadSource)));
            }

            switch (category)
            {
                //case LeadFilterTypeMobile.New:
                //    Query.Where(i => i.Status != null && i.Status.Status == "new");
                //    break;
                //case LeadFilterTypeMobile.Pending:
                //    Query.Where(i => i.Status != null && i.Status.Status == "pending");
                //    break;
                //case LeadFilterTypeMobile.NotInterested:
                //    Query.Where(i => i.Status != null && i.Status.BaseId == Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                //    break;
                //case LeadFilterTypeMobile.CallBack:
                //    Query.Where(i => i.Status != null && i.Status.BaseId == Guid.Parse("5ae346bc-c695-4af4-8c3b-c8648587fbd6"));
                //    break;
                //case LeadFilterTypeMobile.SiteVisitScheduled:
                //    Query.Where(i => i.Status != null && i.Status.Status == "site_visit_scheduled");
                //    break;
                //case LeadFilterTypeMobile.ScheduledMeeting:
                //    Query.Where(i => i.Status != null && i.Status.Status == "meeting_scheduled");
                //    break;
                //case LeadFilterTypeMobile.UnassignLeads:
                //    Query.Where(i => i.AssignTo == Guid.Empty);
                //    break;
                //case LeadFilterTypeMobile.Booked:
                //    Query.Where(i => i.Status != null && i.Status.Status == "booked");
                //    break;
                //case LeadFilterTypeMobile.Dropped:
                //    Query.Where(i => i.Status != null && i.Status.BaseId == Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"));
                //    break;
                //case LeadFilterTypeMobile.Escalated:
                //    Query.Where(i => i.TagInfo != null && i.TagInfo.IsEscalated);
                //    break;
                //case LeadFilterTypeMobile.HotLeads:
                //    Query.Where(i => i.TagInfo != null && i.TagInfo.IsHotLead);
                //    break;
                //case LeadFilterTypeMobile.AboutToConvert:
                //    Query.Where(i => i.TagInfo != null && i.TagInfo.IsAboutToConvert);
                //    break;
                //case LeadFilterTypeMobile.ScheduleToday:
                //    Query.Where(i => i.ScheduledDate != null && i.ScheduledDate.Value.Date >= DateTime.UtcNow.Date && i.ScheduledDate.Value.Date < DateTime.UtcNow.Date.AddDays(1));
                //    break;
                //case LeadFilterTypeMobile.Overdue:
                //    var dCNStatuses = new List<string>() { "not_interested", "dropped", "booked" };
                //    var dCNStatusesIds = new List<Guid?>() { Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"), Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99") };
                //    Query.Where(i => i.Status != null && !dCNStatuses.Contains(i.Status.Status) && !dCNStatusesIds.Contains(i.Status.BaseId) && i.ScheduledDate != null && i.ScheduledDate.Value.Date < DateTime.UtcNow.Date);
                //    break;
                //case LeadFilterTypeMobile.WarmLeads:
                //    Query.Where(i => i.TagInfo != null && i.TagInfo.IsWarmLead);
                //    break;
                //case LeadFilterTypeMobile.ColdLead:
                //    Query.Where(i => i.TagInfo != null && i.TagInfo.IsColdLead);
                //    break;
                //case LeadFilterTypeMobile.Highlighted:
                //    Query.Where(i => i.TagInfo != null && i.TagInfo.IsHighlighted);
                //    break;
                //case LeadFilterTypeMobile.All:
                //default:
                //    break;
            }
            //if (filter?.MinBudgets != null)
            //{
            //    Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.UpperBudget >= filter.MinBudgets));
            //}
            //if (filter?.MaxBudgets != null)
            //{
            //    filter.MinBudgets ??= 0;
            //    Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.UpperBudget >= filter.MinBudgets && i.UpperBudget <= filter.MaxBudgets));
            //}
            //if ((filter?.MinBudgets != null && filter?.MaxBudgets != null))
            //{
            //    Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.UpperBudget >= filter.MinBudgets && i.UpperBudget <= filter.MaxBudgets));
            //}
            //if (filter.AssignToIds != null && filter.AssignToIds.Any())
            //{
            //    Query.Where(i => filter.AssignToIds.Contains(i.AssignTo));
            //}

        }
        public LeadByCustomFilterSpec(GetLeadCategoryRequest filter, ICurrentUser currentUser, List<Guid> propertiesLeadIds, List<Guid> projectLeadIds) : base(filter)
        {
            var subIds = currentUser.GetSubordinateIds() ?? new();
            var reporteeIds = subIds.Where(i => i != currentUser.GetUserId() && i != Guid.Empty);
            var selfWithReporteeIds = subIds.Where(i => i != Guid.Empty);

            Query.Where(i => !i.IsDeleted)
                .Where(i => subIds.Contains(i.AssignTo))
                .Include(i => i.TagInfo)
                //.Include(i => i.Status)
                .Include(i => i.Enquiries)
                //.ThenInclude(i => i.Address)
                .ThenInclude(i => i.Addresses)
                .Include(i => i.Enquiries)
                .Include(i => i.Projects)
                .Include(i => i.Properties)
                .ThenInclude(i => i.PropertyType)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyTypes)
               .OrderByDescending(i => i.LastModifiedOn);
            switch (filter.LeadVisibility)
            {
                case BaseLeadVisibility.SelfWithReportee:
                    Query.Where(i => selfWithReporteeIds.Contains(i.AssignTo));
                    break;
                case BaseLeadVisibility.Self:
                    Query.Where(i => i.AssignTo == currentUser.GetUserId());
                    break;
                case BaseLeadVisibility.Reportee:
                    Query.Where(i => reporteeIds.Contains(i.AssignTo));
                    break;
                case BaseLeadVisibility.UnassignLead:
                    Query.Where(i => i.AssignTo == Guid.Empty);
                    break;
                case BaseLeadVisibility.ReEnquired:
                    Query.Where(i => i.IsReEnquired == true);
                    break;
                default:
                    break;
            }
            if (filter.DateType != null && filter.FromDate != null && filter.FromDate.Value != default && filter.ToDate != null && filter.ToDate.Value != default)
            {
                filter.FromDate = filter.FromDate.Value.ConvertFromDateToUtc();
                filter.ToDate = filter.ToDate.Value.ConvertToDateToUtc();
                switch (filter.DateType)
                {
                    case DateType.ReceivedDate:
                        Query.Where(i => i.CreatedOn >= filter.FromDate.Value && i.CreatedOn <= filter.ToDate.Value);
                        break;
                    case DateType.ScheduledDate:
                        Query.Where(i => i.ScheduledDate != null && i.ScheduledDate.Value >= filter.FromDate.Value && i.ScheduledDate.Value <= filter.ToDate.Value);
                        break;
                    case DateType.ModifiedDate:
                        Query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value && i.LastModifiedOn.Value <= filter.ToDate.Value);
                        break;
                    default:
                        break;
                }
            }
            if (filter.Properties != null && filter.Properties.Any() && propertiesLeadIds.Any())
            {
                Query.Where(i => propertiesLeadIds.Contains(i.Id));
            }
            if (filter.Projects != null && filter.Projects.Any() && projectLeadIds.Any())
            {
                Query.Where(i => projectLeadIds.Contains(i.Id));
            }
            switch (filter.FilterType)
            {
                //case LeadFilterTypeMobile.New:
                //    Query.Where(i => i.Status != null && i.Status.Status == "new");
                //    break;
                //case LeadFilterTypeMobile.Pending:
                //    Query.Where(i => i.Status != null && i.Status.Status == "pending");
                //    break;
                //case LeadFilterTypeMobile.NotInterested:
                //    Query.Where(i => i.Status != null && i.Status.BaseId == Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                //    break;
                //case LeadFilterTypeMobile.CallBack:
                //    Query.Where(i => i.Status != null && i.Status.BaseId == Guid.Parse("5ae346bc-c695-4af4-8c3b-c8648587fbd6"));
                //    break;
                //case LeadFilterTypeMobile.SiteVisitScheduled:
                //    Query.Where(i => i.Status != null && i.Status.Status == "site_visit_scheduled");
                //    break;
                //case LeadFilterTypeMobile.ScheduledMeeting:
                //    Query.Where(i => i.Status != null && i.Status.Status == "meeting_scheduled");
                //    break;
                //case LeadFilterTypeMobile.UnassignLeads:
                //    Query.Where(i => i.AssignTo == Guid.Empty);
                //    break;
                //case LeadFilterTypeMobile.Booked:
                //    Query.Where(i => i.Status != null && i.Status.Status == "booked");
                //    break;
                //case LeadFilterTypeMobile.Dropped:
                //    Query.Where(i => i.Status != null && i.Status.BaseId == Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"));
                //    break;
                //case LeadFilterTypeMobile.Escalated:
                //    Query.Where(i => i.TagInfo != null && i.TagInfo.IsEscalated);
                //    break;
                //case LeadFilterTypeMobile.HotLeads:
                //    Query.Where(i => i.TagInfo != null && i.TagInfo.IsHotLead);
                //    break;
                //case LeadFilterTypeMobile.AboutToConvert:
                //    Query.Where(i => i.TagInfo != null && i.TagInfo.IsAboutToConvert);
                //    break;
                //case LeadFilterTypeMobile.ScheduleToday:
                //    Query.Where(i => i.ScheduledDate != null && i.ScheduledDate.Value.Date >= DateTime.UtcNow.Date && i.ScheduledDate.Value.Date < DateTime.UtcNow.Date.AddDays(1));
                //    break;
                //case LeadFilterTypeMobile.Overdue:
                //    var dCNStatuses = new List<string>() { "not_interested", "dropped", "booked" };
                //    var dCNStatusesIds = new List<Guid?>() { Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"), Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99") };
                //    Query.Where(i => i.Status != null && !dCNStatuses.Contains(i.Status.Status) && !dCNStatusesIds.Contains(i.Status.BaseId) && i.ScheduledDate != null && i.ScheduledDate.Value.Date < DateTime.UtcNow.Date);
                //    break;
                //case LeadFilterTypeMobile.WarmLeads:
                //    Query.Where(i => i.TagInfo != null && i.TagInfo.IsWarmLead);
                //    break;
                //case LeadFilterTypeMobile.ColdLead:
                //    Query.Where(i => i.TagInfo != null && i.TagInfo.IsColdLead);
                //    break;
                //case LeadFilterTypeMobile.Highlighted:
                //    Query.Where(i => i.TagInfo != null && i.TagInfo.IsHighlighted);
                //    break;
                //case LeadFilterTypeMobile.All:
                //default:
                //    break;
            }
            if (filter?.Source?.Any() ?? false)
            {
                Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => filter.Source.Contains(i.LeadSource)));
            }
            //if (filter?.MinBudgets != null)
            //{
            //    Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.UpperBudget >= filter.MinBudgets));
            //}
            //if (filter?.MaxBudgets != null)
            //{
            //    filter.MinBudgets ??= 0;
            //    Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.UpperBudget >= filter.MinBudgets && i.UpperBudget <= filter.MaxBudgets));
            //}
            //if ((filter?.MinBudgets != null && filter?.MaxBudgets != null))
            //{
            //    Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.UpperBudget >= filter.MinBudgets && i.UpperBudget <= filter.MaxBudgets));
            //}
            //if (filter?.AssignToIds != null && filter.AssignToIds.Any())
            //{
            //    Query.Where(i => filter.AssignToIds.Contains(i.AssignTo));
            //}
            //if (filter?.EnquiredFor != null)
            //{
            //    Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.EnquiredFor == filter.EnquiredFor && i.IsPrimary));
            //}
        }
        public LeadByCustomFilterSpec(SearchLeadRequest filter, ICurrentUser currentUser) : base(filter)
        {
            var subIds = currentUser.GetSubordinateIds() ?? new();
            Query.Where(i => !i.IsDeleted)
                .Where(i => subIds.Contains(i.AssignTo))
                .Include(i => i.TagInfo)
                //.Include(i => i.Status)
                .Include(i => i.Enquiries)
                //.ThenInclude(i => i.Address)
                .ThenInclude(i => i.Addresses)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyType)
                .Include(i => i.Projects)
                .Include(i => i.Properties)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyTypes)
                .OrderByDescending(i => i.LastModifiedOn);
            if (!string.IsNullOrWhiteSpace(filter.SearchByNameOrNumber))
            {
                Query.Where(i => i.ContactNo.Contains(filter.SearchByNameOrNumber.Replace(" ", "")) || i.Name.ToLower().Contains(filter.SearchByNameOrNumber.ToLower()) || i.SerialNumber.ToLower().Contains(filter.SearchByNameOrNumber.ToLower()));
            }
        }
    }
    public class LeadCountByCustomFilterSpec : Specification<Domain.Entities.Lead>
    {
        public LeadCountByCustomFilterSpec(GetAllLeadsRequest filter, LeadFilterTypeMobile category, ICurrentUser currentUser, List<Guid> propertiesLeadIds, List<Guid> projectLeadIds)
        {
            var subIds = currentUser.GetSubordinateIds() ?? new();
            var reporteeIds = subIds.Where(i => i != currentUser.GetUserId() && i != Guid.Empty);
            var selfWithReporteeIds = subIds.Where(i => i != Guid.Empty);
            //by default we are using  OrderByDescending here, It should get it done by filters later.
            //by default we are using OrderByDescending here, It should get it done by filters later.
            Query.Where(i => !i.IsDeleted)
                .Where(i => subIds.Contains(i.AssignTo))
                .Include(i => i.TagInfo)
                //.Include(i => i.Status)
                .Include(i => i.Enquiries)
                //.ThenInclude(i => i.Address)
                .ThenInclude(i => i.Addresses)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyType)
                .Include(i => i.Projects)
                .Include(i => i.Properties)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyTypes)
                .OrderByDescending(i => i.LastModifiedOn);

            switch (filter.LeadVisibility)
            {
                case BaseLeadVisibility.SelfWithReportee:
                    Query.Where(i => selfWithReporteeIds.Contains(i.AssignTo));
                    break;
                case BaseLeadVisibility.Self:
                    Query.Where(i => i.AssignTo == currentUser.GetUserId());
                    break;
                case BaseLeadVisibility.Reportee:
                    Query.Where(i => reporteeIds.Contains(i.AssignTo));
                    break;
                case BaseLeadVisibility.UnassignLead:
                    Query.Where(i => i.AssignTo == Guid.Empty);
                    break;
                case BaseLeadVisibility.ReEnquired:
                    Query.Where(i => i.IsReEnquired == true);
                    break;
                default:
                    break;
            }
            if (filter.Properties != null && filter.Properties.Any() && propertiesLeadIds.Any())
            {
                Query.Where(i => propertiesLeadIds.Contains(i.Id));
            }
            if (filter.Projects != null && filter.Projects.Any() && projectLeadIds.Any())
            {
                Query.Where(i => projectLeadIds.Contains(i.Id));
            }
            //if (filter.EnquiredFor != null)
            //{
            //    Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.EnquiredFor == filter.EnquiredFor && i.IsPrimary));
            //}
            if (filter.DateType != null && filter.FromDate != null && filter.FromDate.Value != default && filter.ToDate != null && filter.ToDate.Value != default)
            {
                filter.FromDate = filter.FromDate.Value.ConvertFromDateToUtc();
                filter.ToDate = filter.ToDate.Value.ConvertToDateToUtc();
                switch (filter.DateType)
                {
                    case DateType.ReceivedDate:
                        Query.Where(i => i.CreatedOn >= filter.FromDate.Value && i.CreatedOn <= filter.ToDate.Value);
                        break;
                    case DateType.ScheduledDate:
                        Query.Where(i => i.ScheduledDate != null && i.ScheduledDate.Value >= filter.FromDate.Value && i.ScheduledDate.Value <= filter.ToDate.Value);
                        break;
                    case DateType.ModifiedDate:
                        Query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value && i.LastModifiedOn.Value <= filter.ToDate.Value);
                        break;
                    default:
                        break;
                }
            }
            if (filter.AssignTo != null && filter.AssignTo != Guid.Empty)
            {
                Query.Where(i => i.AssignTo == filter.AssignTo.Value);
            }
            if (!string.IsNullOrWhiteSpace(filter.SearchByNameOrNumber))
            {
                Query.Where(i => i.ContactNo.Contains(filter.SearchByNameOrNumber.Replace(" ", "")) || i.Name.ToLower().Contains(filter.SearchByNameOrNumber.ToLower()) || i.SerialNumber.ToLower().Contains(filter.SearchByNameOrNumber.ToLower()));
            }

            switch (category)
            {
                //case LeadFilterTypeMobile.New:
                //    Query.Where(i => i.Status != null && i.Status.Status == "new");
                //    break;
                //case LeadFilterTypeMobile.Pending:
                //    Query.Where(i => i.Status != null && i.Status.Status == "pending");
                //    break;
                //case LeadFilterTypeMobile.NotInterested:
                //    Query.Where(i => i.Status != null && i.Status.BaseId == Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                //    break;
                //case LeadFilterTypeMobile.CallBack:
                //    Query.Where(i => i.Status != null && i.Status.BaseId == Guid.Parse("5ae346bc-c695-4af4-8c3b-c8648587fbd6"));
                //    break;
                //case LeadFilterTypeMobile.SiteVisitScheduled:
                //    Query.Where(i => i.Status != null && i.Status.Status == "site_visit_scheduled");
                //    break;
                //case LeadFilterTypeMobile.ScheduledMeeting:
                //    Query.Where(i => i.Status != null && i.Status.Status == "meeting_scheduled");
                //    break;
                //case LeadFilterTypeMobile.UnassignLeads:
                //    Query.Where(i => i.AssignTo == Guid.Empty);
                //    break;
                //case LeadFilterTypeMobile.Booked:
                //    Query.Where(i => i.Status != null && i.Status.Status == "booked");
                //    break;
                //case LeadFilterTypeMobile.Dropped:
                //    Query.Where(i => i.Status != null && i.Status.BaseId == Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"));
                //    break;
                //case LeadFilterTypeMobile.Escalated:
                //    Query.Where(i => i.TagInfo != null && i.TagInfo.IsEscalated);
                //    break;
                //case LeadFilterTypeMobile.HotLeads:
                //    Query.Where(i => i.TagInfo != null && i.TagInfo.IsHotLead);
                //    break;
                //case LeadFilterTypeMobile.AboutToConvert:
                //    Query.Where(i => i.TagInfo != null && i.TagInfo.IsAboutToConvert);
                //    break;
                //case LeadFilterTypeMobile.ScheduleToday:
                //    Query.Where(i => i.ScheduledDate != null && i.ScheduledDate.Value.Date >= DateTime.UtcNow.Date && i.ScheduledDate.Value.Date < DateTime.UtcNow.Date.AddDays(1));
                //    break;
                //case LeadFilterTypeMobile.Overdue:
                //    var dCNStatuses = new List<string>() { "not_interested", "dropped", "booked" };
                //    var dCNStatusesIds = new List<Guid?>() { Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"), Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99") };
                //    Query.Where(i => i.Status != null && !dCNStatuses.Contains(i.Status.Status) && !dCNStatusesIds.Contains(i.Status.BaseId) && i.ScheduledDate != null && i.ScheduledDate.Value.Date < DateTime.UtcNow.Date);
                //    break;
                //case LeadFilterTypeMobile.WarmLeads:
                //    Query.Where(i => i.TagInfo != null && i.TagInfo.IsWarmLead);
                //    break;
                //case LeadFilterTypeMobile.ColdLead:
                //    Query.Where(i => i.TagInfo != null && i.TagInfo.IsColdLead);
                //    break;
                //case LeadFilterTypeMobile.Highlighted:
                //    Query.Where(i => i.TagInfo != null && i.TagInfo.IsHighlighted);
                //    break;
                //case LeadFilterTypeMobile.All:
                //default:
                //    break;
            }
            if (filter?.Source?.Any() ?? false)
            {
                Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => filter.Source.Contains(i.LeadSource)));
            }
            //if (filter?.MinBudgets != null)
            //{
            //    Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.UpperBudget >= filter.MinBudgets));
            //}
            //if (filter?.MaxBudgets != null)
            //{
            //    filter.MinBudgets ??= 0;
            //    Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.UpperBudget >= filter.MinBudgets && i.UpperBudget <= filter.MaxBudgets));
            //}
            //if ((filter?.MinBudgets != null && filter?.MaxBudgets != null))
            //{
            //    Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.UpperBudget >= filter.MinBudgets && i.UpperBudget <= filter.MaxBudgets));
            //}
            //if (filter.AssignToIds != null && filter.AssignToIds.Any())
            //{
            //    Query.Where(i => filter.AssignToIds.Contains(i.AssignTo));
            //}

        }
        public LeadCountByCustomFilterSpec(GetLeadCategoryRequest filter, ICurrentUser currentUser, List<Guid> propertiesLeadIds, List<Guid> projectLeadIds)
        {
            var subIds = currentUser.GetSubordinateIds() ?? new();
            var reporteeIds = subIds.Where(i => i != currentUser.GetUserId() && i != Guid.Empty);
            var selfWithReporteeIds = subIds.Where(i => i != Guid.Empty);

            Query.Where(i => !i.IsDeleted)
                .Where(i => subIds.Contains(i.AssignTo))
                .Include(i => i.TagInfo)
                //.Include(i => i.Status)
                .Include(i => i.Enquiries)
                //.ThenInclude(i => i.Address)
                .ThenInclude(i => i.Addresses)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyType)
                .Include(i => i.Projects)
                .Include(i => i.Properties)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyTypes)
               .OrderByDescending(i => i.LastModifiedOn);
            switch (filter.LeadVisibility)
            {
                case BaseLeadVisibility.SelfWithReportee:
                    Query.Where(i => selfWithReporteeIds.Contains(i.AssignTo));
                    break;
                case BaseLeadVisibility.Self:
                    Query.Where(i => i.AssignTo == currentUser.GetUserId());
                    break;
                case BaseLeadVisibility.Reportee:
                    Query.Where(i => reporteeIds.Contains(i.AssignTo));
                    break;
                case BaseLeadVisibility.UnassignLead:
                    Query.Where(i => i.AssignTo == Guid.Empty);
                    break;
                case BaseLeadVisibility.ReEnquired:
                    Query.Where(i => i.IsReEnquired == true);
                    break;
                default:
                    break;
            }
            if (filter.DateType != null && filter.FromDate != null && filter.FromDate.Value != default && filter.ToDate != null && filter.ToDate.Value != default)
            {
                filter.FromDate = filter.FromDate.Value.ConvertFromDateToUtc();
                filter.ToDate = filter.ToDate.Value.ConvertToDateToUtc();
                switch (filter.DateType)
                {
                    case DateType.ReceivedDate:
                        Query.Where(i => i.CreatedOn >= filter.FromDate.Value && i.CreatedOn <= filter.ToDate.Value);
                        break;
                    case DateType.ScheduledDate:
                        Query.Where(i => i.ScheduledDate != null && i.ScheduledDate.Value >= filter.FromDate.Value && i.ScheduledDate.Value <= filter.ToDate.Value);
                        break;
                    case DateType.ModifiedDate:
                        Query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value && i.LastModifiedOn.Value <= filter.ToDate.Value);
                        break;
                    default:
                        break;
                }
            }
            if (filter.Properties != null && filter.Properties.Any() && propertiesLeadIds.Any())
            {
                Query.Where(i => propertiesLeadIds.Contains(i.Id));
            }
            if (filter.Projects != null && filter.Projects.Any() && projectLeadIds.Any())
            {
                Query.Where(i => projectLeadIds.Contains(i.Id));
            }
            switch (filter.FilterType)
            {
                //case LeadFilterTypeMobile.New:
                //    Query.Where(i => i.Status != null && i.Status.Status == "new");
                //    break;
                //case LeadFilterTypeMobile.Pending:
                //    Query.Where(i => i.Status != null && i.Status.Status == "pending");
                //    break;
                //case LeadFilterTypeMobile.NotInterested:
                //    Query.Where(i => i.Status != null && i.Status.BaseId == Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                //    break;
                //case LeadFilterTypeMobile.CallBack:
                //    Query.Where(i => i.Status != null && i.Status.BaseId == Guid.Parse("5ae346bc-c695-4af4-8c3b-c8648587fbd6"));
                //    break;
                //case LeadFilterTypeMobile.SiteVisitScheduled:
                //    Query.Where(i => i.Status != null && i.Status.Status == "site_visit_scheduled");
                //    break;
                //case LeadFilterTypeMobile.ScheduledMeeting:
                //    Query.Where(i => i.Status != null && i.Status.Status == "meeting_scheduled");
                //    break;
                //case LeadFilterTypeMobile.UnassignLeads:
                //    Query.Where(i => i.AssignTo == Guid.Empty);
                //    break;
                //case LeadFilterTypeMobile.Booked:
                //    Query.Where(i => i.Status != null && i.Status.Status == "booked");
                //    break;
                //case LeadFilterTypeMobile.Dropped:
                //    Query.Where(i => i.Status != null && i.Status.BaseId == Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"));
                //    break;
                //case LeadFilterTypeMobile.Escalated:
                //    Query.Where(i => i.TagInfo != null && i.TagInfo.IsEscalated);
                //    break;
                //case LeadFilterTypeMobile.HotLeads:
                //    Query.Where(i => i.TagInfo != null && i.TagInfo.IsHotLead);
                //    break;
                //case LeadFilterTypeMobile.AboutToConvert:
                //    Query.Where(i => i.TagInfo != null && i.TagInfo.IsAboutToConvert);
                //    break;
                //case LeadFilterTypeMobile.ScheduleToday:
                //    Query.Where(i => i.ScheduledDate != null && i.ScheduledDate.Value.Date >= DateTime.UtcNow.Date && i.ScheduledDate.Value.Date < DateTime.UtcNow.Date.AddDays(1));
                //    break;
                //case LeadFilterTypeMobile.Overdue:
                //    var dCNStatuses = new List<string>() { "not_interested", "dropped", "booked" };
                //    var dCNStatusesIds = new List<Guid?>() { Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"), Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99") };
                //    Query.Where(i => i.Status != null && !dCNStatuses.Contains(i.Status.Status) && !dCNStatusesIds.Contains(i.Status.BaseId) && i.ScheduledDate != null && i.ScheduledDate.Value.Date < DateTime.UtcNow.Date);
                //    break;
                //case LeadFilterTypeMobile.WarmLeads:
                //    Query.Where(i => i.TagInfo != null && i.TagInfo.IsWarmLead);
                //    break;
                //case LeadFilterTypeMobile.ColdLead:
                //    Query.Where(i => i.TagInfo != null && i.TagInfo.IsColdLead);
                //    break;
                //case LeadFilterTypeMobile.Highlighted:
                //    Query.Where(i => i.TagInfo != null && i.TagInfo.IsHighlighted);
                //    break;
                //case LeadFilterTypeMobile.All:
                //default:
                //    break;
            }
            if (filter?.Source?.Any() ?? false)
            {
                Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => filter.Source.Contains(i.LeadSource)));
            }
            //if (filter?.MinBudgets != null)
            //{
            //    Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.UpperBudget >= filter.MinBudgets));
            //}
            //if (filter?.MaxBudgets != null)
            //{
            //    filter.MinBudgets ??= 0;
            //    Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.UpperBudget >= filter.MinBudgets && i.UpperBudget <= filter.MaxBudgets));
            //}
            //if ((filter?.MinBudgets != null && filter?.MaxBudgets != null))
            //{
            //    Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.UpperBudget >= filter.MinBudgets && i.UpperBudget <= filter.MaxBudgets));
            //}
            //if (filter?.AssignToIds != null && filter.AssignToIds.Any())
            //{
            //    Query.Where(i => filter.AssignToIds.Contains(i.AssignTo));
            //}
            //if (filter?.EnquiredFor != null)
            //{
            //    Query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.EnquiredFor == filter.EnquiredFor && i.IsPrimary));
            //}
        }

        public LeadCountByCustomFilterSpec(SearchLeadRequest filter, ICurrentUser currentUser)
        {
            var subIds = currentUser.GetSubordinateIds() ?? new();
            Query.Where(i => !i.IsDeleted)
               .Where(i => subIds.Contains(i.AssignTo))
               .Include(i => i.TagInfo)
               //.Include(i => i.Status)
               .Include(i => i.Enquiries);
            if (!string.IsNullOrWhiteSpace(filter.SearchByNameOrNumber))
            {
                Query.Where(i => i.ContactNo.Contains(filter.SearchByNameOrNumber.Replace(" ", "")) || i.Name.ToLower().Contains(filter.SearchByNameOrNumber.ToLower()) || i.SerialNumber.ToLower().Contains(filter.SearchByNameOrNumber.ToLower()));
            }
        }
    }

    public class EnquiryByEnquiryTypeSpec : Specification<LeadEnquiry>
    {
        public EnquiryByEnquiryTypeSpec(EnquiryType enquiredFor)
        {
            Query.Where(i => i.EnquiredFor == enquiredFor && i.IsPrimary);
        }
    }
    public class PrimaryEnquirySpec : Specification<LeadEnquiry>
    {
        public PrimaryEnquirySpec(Guid leadId)
        {
            Query.Where(i => i.IsPrimary && i.LeadId == leadId);
        }
    }
    public class LeadTagByTagNameAndValueSpec : Specification<LeadTag>
    {
        public LeadTagByTagNameAndValueSpec(Dictionary<string, bool> tags)
        {
            foreach (var tag in tags)
            {
                if (typeof(LeadTag).GetProperty(tag.Key) != null)
                {
                    Query.Where(i => bool.Parse(typeof(LeadTag).GetProperty(tag.Key).GetValue(i).ToString() ?? "false") == tag.Value);
                }
            }
        }
    }
}
