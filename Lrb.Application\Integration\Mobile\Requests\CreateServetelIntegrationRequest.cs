﻿using Lrb.Application.Common.BlobStorage;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Integration.Mobile.Requests
{
    public class CreateServetelIntegrationRequest : IRequest<Response<string>>
    {
        public string? AccountName { get; set; }
        public LeadSource Source { get; set; }
        public IVRType CallType { get; set; }
        public Dictionary<string, string>? Credentials { get; set; }
    }
    public class CreateServetelIntegrationRequestHandler : IRequestHandler<CreateServetelIntegrationRequest, Response<string>>
    {
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccountInfoRepositoryAsync;
        private readonly IBlobStorageService _blobStorageService;
        private readonly ICurrentUser _currentUser;

        public CreateServetelIntegrationRequestHandler(
            IRepositoryWithEvents<IntegrationAccountInfo> integrationAccountInfoRepositoryAsync,
            ICurrentUser currentUser,
            IBlobStorageService blobStorageService)
        {
            _integrationAccountInfoRepositoryAsync = integrationAccountInfoRepositoryAsync;
            _blobStorageService = blobStorageService;
            _currentUser = currentUser;
        }
        public async Task<Response<string>> Handle(CreateServetelIntegrationRequest request, CancellationToken cancellationToken)
        {
            request.Source = LeadSource.IVR;
            var tenant = _currentUser.GetTenant();
            //Todo fix user and tenant
            Guid userId = Guid.NewGuid();
            IntegrationAccountInfo? integrationAccount = null;
            IDictionary<string, string> data = null;
            integrationAccount = CreateIntegrationEntity(request, userId);
            data = new Dictionary<string, string>(IntegrationTemplateBuilder.CreateIntegrationTemplate(tenant, integrationAccount, request.CallType, true));
            await _integrationAccountInfoRepositoryAsync.AddAsync(integrationAccount);
            string key = string.Empty;
            byte[] bytes = IntegrationExcelHelper.CreateExcelData(data).ToArray();
            string fileName = $"{integrationAccount.Id}-{DateTime.Now.ToString("yyyy_MM_dd-HH_mm_ss")}.xlsx";
            string folder = "Integration";
            key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", folder, fileName, bytes);
            string fileUrl = await _blobStorageService.GetPreSignedURL(_blobStorageService?.BucketName ?? "prd-leadratblack", key);
            integrationAccount.FileUrl = key;
            await _integrationAccountInfoRepositoryAsync.UpdateAsync(integrationAccount);
            return new(fileUrl, default);
        }
        private IntegrationAccountInfo CreateIntegrationEntity(CreateServetelIntegrationRequest command, Guid userId)
        {
            return new IntegrationAccountInfo()
            {
                Id = Guid.NewGuid(),
                AccountName = command.AccountName,
                LeadSource = command.Source,
                LicenseId = Guid.NewGuid(),
                JsonTemplate = IntegrationTemplateBuilder.GetRequestBodyJsonFromFile(command.Source, true),
                CreatedBy = userId,
                Credentials = command.Credentials
            };
        }
    }
}
