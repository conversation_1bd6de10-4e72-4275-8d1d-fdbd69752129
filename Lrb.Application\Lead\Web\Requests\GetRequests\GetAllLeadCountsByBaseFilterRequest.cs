﻿using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;

namespace Lrb.Application.Lead.Web.Requests
{
    public class GetAllLeadCountsByBaseFilterRequest : GetAllLeadsParameters, IRequest<Response<LeadCountsByBaseFiltersDto>>
    {

    }
    public class GetAllLeadCountsByBaseFilterRequestHandler : IRequestHandler<GetAllLeadCountsByBaseFilterRequest, Response<LeadCountsByBaseFiltersDto>>
    {
        private readonly ILeadRepository _efLeadRepository;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        //private readonly IRepositoryWithEvents<MasterLeadStatus> _leadStatusRepo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customLeadStatusRepo;
        public GetAllLeadCountsByBaseFilterRequestHandler(
            IDapperRepository dapperRepository,
            ILeadRepository efLeadRepository,
            ICurrentUser currentUser,
            //IRepositoryWithEvents<MasterLeadStatus> leadStatusRepo,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<CustomMasterLeadStatus> customLeadStatusRepo)
        {
            _currentUser = currentUser;
            _efLeadRepository = efLeadRepository;
            _dapperRepository = dapperRepository;
            //_leadStatusRepo = leadStatusRepo;
            _leadRepositoryAsync = leadRepositoryAsync;
            _customLeadStatusRepo = customLeadStatusRepo;
        }
        public async Task<Response<LeadCountsByBaseFiltersDto>> Handle(GetAllLeadCountsByBaseFilterRequest request, CancellationToken cancellationToken)
        {
            if (request?.LeadTags?.Any() ?? false)
            {
                request.TagFilterDto = GetLeadTagFilter(request);
                request.LeadTags = null;
            }
            var userId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var isAdmin = _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty).Result;
            List<Guid> leadHistoryIds = new();
            List<Guid> subIds = new();
            try
            {
                if (request?.AssignTo?.Any() ?? false)
                {
                    if (request?.IsWithTeam ?? false)
                    {
                        subIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.AssignTo, tenantId ?? string.Empty)).ToList();
                    }
                    else
                    {
                        subIds = request?.AssignTo ?? new List<Guid>();
                    }
                }
                else
                {
                    subIds = (await _dapperRepository.GetSubordinateIdsAsync(userId, tenantId ?? string.Empty, request?.CanAccessAllLeads))?.ToList() ?? new();
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "GetAllLeadCountsByBaseFilterRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
            if (request?.SubStatusIds?.Any() ?? false)
            {
                var statuses = await _customLeadStatusRepo.ListAsync(new LeadStatusSpec(request.SubStatusIds), cancellationToken);
                request?.StatusIds?.RemoveAll(l => statuses.Select(i => i.BaseId).Contains(l));
            }
            LeadCountsByBaseFiltersDto leadCountDto = new();
            var types = typeof(LeadCountsByBaseFiltersDto).GetProperties();
            foreach (var type in types)
            {
                leadCountDto = await AddLeadsCount(leadCountDto, type, request, subIds, userId, leadHistoryIds,isAdmin);
            }
            return new(leadCountDto);
        }
        private async Task<LeadCountsByBaseFiltersDto> AddLeadsCount(LeadCountsByBaseFiltersDto leadsCount, System.Reflection.PropertyInfo property, GetAllLeadCountsByBaseFilterRequest request, IEnumerable<Guid> subIds, Guid userId, List<Guid> leadHistoryIds,bool? isAdmin = null)
        {
            var propertyName = property.Name;
            switch (propertyName)
            {
                case nameof(LeadCountsByBaseFiltersDto.ManageLeadsCount):
                    var requestCopy = request.Adapt<GetAllLeadsRequest>();
                    requestCopy.LeadVisibility = (BaseLeadVisibility)5;
                    requestCopy.FilterType = LeadFilterTypeWeb.AllWithNID;
                    leadsCount.ManageLeadsCount = _efLeadRepository.GetLeadsCountForWebAsync(requestCopy, subIds, userId,isAdmin : isAdmin).Result;
                    break;
                case nameof(LeadCountsByBaseFiltersDto.AllLeads):
                    requestCopy = request.Adapt<GetAllLeadsRequest>();
                    requestCopy.LeadVisibility = BaseLeadVisibility.SelfWithReportee;
                    requestCopy.FilterType = LeadFilterTypeWeb.AllWithNID;
                    leadsCount.AllLeads = _efLeadRepository.GetLeadsCountForWebAsync(requestCopy, subIds, userId, isAdmin: isAdmin).Result;
                    break;
                case nameof(LeadCountsByBaseFiltersDto.Teams):
                    requestCopy = request.Adapt<GetAllLeadsRequest>();
                    requestCopy.LeadVisibility = BaseLeadVisibility.Reportee;
                    requestCopy.FilterType = LeadFilterTypeWeb.AllWithNID;
                    leadsCount.Teams = _efLeadRepository.GetLeadsCountForWebAsync(requestCopy, subIds, userId, isAdmin: isAdmin).Result;
                    break;
                case nameof(LeadCountsByBaseFiltersDto.MyLeads):
                    requestCopy = request.Adapt<GetAllLeadsRequest>();
                    requestCopy.LeadVisibility = BaseLeadVisibility.Self;
                    requestCopy.FilterType = LeadFilterTypeWeb.AllWithNID;
                    leadsCount.MyLeads = _efLeadRepository.GetLeadsCountForWebAsync(requestCopy, subIds, userId, isAdmin: isAdmin).Result;
                    break;
                case nameof(LeadCountsByBaseFiltersDto.Unassigned):
                    requestCopy = request.Adapt<GetAllLeadsRequest>();
                    requestCopy.LeadVisibility = BaseLeadVisibility.UnassignLead;
                    requestCopy.FilterType = LeadFilterTypeWeb.AllWithNID;
                    leadsCount.Unassigned = _efLeadRepository.GetLeadsCountForWebAsync(requestCopy, subIds, userId, isAdmin: isAdmin).Result;
                    break;
                case nameof(LeadCountsByBaseFiltersDto.Deleted):
                    requestCopy = request.Adapt<GetAllLeadsRequest>();
                    requestCopy.LeadVisibility = BaseLeadVisibility.DeletedLeads;
                    requestCopy.FilterType = LeadFilterTypeWeb.AllWithNID;
                    leadsCount.Deleted = _efLeadRepository.GetLeadsCountForWebAsync(requestCopy, subIds, userId, isAdmin: isAdmin).Result;
                    break;
                case nameof(LeadCountsByBaseFiltersDto.ReEnquired):
                    requestCopy = request.Adapt<GetAllLeadsRequest>();
                    requestCopy.LeadVisibility = BaseLeadVisibility.ReEnquired;
                    requestCopy.FilterType = LeadFilterTypeWeb.AllWithNID;
                    leadsCount.ReEnquired = _efLeadRepository.GetLeadsCountForWebAsync(requestCopy, subIds, userId, isAdmin: isAdmin).Result;
                    break;
            }
            return leadsCount;
        }
        private LeadTagFilterDto? GetLeadTagFilter(GetAllLeadCountsByBaseFilterRequest request)
        {
            LeadTagFilterDto? tagFilterDto = null;
            if (request.LeadTags?.Any() ?? false)
            {
                tagFilterDto = new();
                foreach (var tag in request.LeadTags)
                    switch (tag)
                    {
                        case LeadTagEnum.IsHot:
                            tagFilterDto.IsHotLead = true;
                            break;
                        case LeadTagEnum.IsAboutToConvert:
                            tagFilterDto.IsAboutToConvert = true;
                            break;
                        case LeadTagEnum.IsEscalated:
                            tagFilterDto.IsEscalated = true;
                            break;
                        case LeadTagEnum.IsIntegrationLead:
                            tagFilterDto.IsIntegrationLead = true;
                            break;
                        case LeadTagEnum.IsHighlighted:
                            tagFilterDto.IsHighlighted = true;
                            break;
                        case LeadTagEnum.IsWarmLead:
                            tagFilterDto.IsWarmLead = true;
                            break;
                        case LeadTagEnum.IsColdLead:
                            tagFilterDto.IsColdLead = true;
                            break;
                    }
            }
            return tagFilterDto;
        }
    }
}
