﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class AddLocationZoneCityRelationInTeam : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "TeamConfigurationId",
                schema: "LeadratBlack",
                table: "Zones",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "TeamConfigurationId",
                schema: "LeadratBlack",
                table: "Locations",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "TeamConfigurationId",
                schema: "LeadratBlack",
                table: "Cities",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Zones_TeamConfigurationId",
                schema: "LeadratBlack",
                table: "Zones",
                column: "TeamConfigurationId");

            migrationBuilder.CreateIndex(
                name: "IX_Locations_TeamConfigurationId",
                schema: "LeadratBlack",
                table: "Locations",
                column: "TeamConfigurationId");

            migrationBuilder.CreateIndex(
                name: "IX_Cities_TeamConfigurationId",
                schema: "LeadratBlack",
                table: "Cities",
                column: "TeamConfigurationId");

            migrationBuilder.AddForeignKey(
                name: "FK_Cities_TeamConfigurations_TeamConfigurationId",
                schema: "LeadratBlack",
                table: "Cities",
                column: "TeamConfigurationId",
                principalSchema: "LeadratBlack",
                principalTable: "TeamConfigurations",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Locations_TeamConfigurations_TeamConfigurationId",
                schema: "LeadratBlack",
                table: "Locations",
                column: "TeamConfigurationId",
                principalSchema: "LeadratBlack",
                principalTable: "TeamConfigurations",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Zones_TeamConfigurations_TeamConfigurationId",
                schema: "LeadratBlack",
                table: "Zones",
                column: "TeamConfigurationId",
                principalSchema: "LeadratBlack",
                principalTable: "TeamConfigurations",
                principalColumn: "Id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Cities_TeamConfigurations_TeamConfigurationId",
                schema: "LeadratBlack",
                table: "Cities");

            migrationBuilder.DropForeignKey(
                name: "FK_Locations_TeamConfigurations_TeamConfigurationId",
                schema: "LeadratBlack",
                table: "Locations");

            migrationBuilder.DropForeignKey(
                name: "FK_Zones_TeamConfigurations_TeamConfigurationId",
                schema: "LeadratBlack",
                table: "Zones");

            migrationBuilder.DropIndex(
                name: "IX_Zones_TeamConfigurationId",
                schema: "LeadratBlack",
                table: "Zones");

            migrationBuilder.DropIndex(
                name: "IX_Locations_TeamConfigurationId",
                schema: "LeadratBlack",
                table: "Locations");

            migrationBuilder.DropIndex(
                name: "IX_Cities_TeamConfigurationId",
                schema: "LeadratBlack",
                table: "Cities");

            migrationBuilder.DropColumn(
                name: "TeamConfigurationId",
                schema: "LeadratBlack",
                table: "Zones");

            migrationBuilder.DropColumn(
                name: "TeamConfigurationId",
                schema: "LeadratBlack",
                table: "Locations");

            migrationBuilder.DropColumn(
                name: "TeamConfigurationId",
                schema: "LeadratBlack",
                table: "Cities");
        }
    }
}
