﻿using Lrb.Domain.Entities;

namespace Lrb.Application.Integration.Mobile
{
    public static class IntegrationTemplateBuilder
    {
        static string? env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
        public static IDictionary<string, string> CreateIntegrationTemplate(string tenant, IntegrationAccountInfo dataParams, IVRType callType = IVRType.Inbound, bool IsServetel = false)
        {
            string workingEndpointOpenUrlValue = string.Empty;
            switch (env)
            {
                case "dev":
                    workingEndpointOpenUrlValue = IntegrationTemplateValues.DevWorkingEndpointOpenUrlValue;
                    break;
                case "qa":
                    workingEndpointOpenUrlValue = IntegrationTemplateValues.QAWorkingEndpointOpenUrlValue;
                    break;
                case "prd":
                    workingEndpointOpenUrlValue = IntegrationTemplateValues.PrdWorkingEndpointOpenUrlValue;
                    break;
                default:
                    break;
            }
            IDictionary<string, string> excelIntegrationContent = new Dictionary<string, string>();
            excelIntegrationContent.Add(IntegrationTemplateKeys.HeaderKey, IntegrationTemplateValues.HeaderValue);
            excelIntegrationContent.Add(IntegrationTemplateKeys.Method, IntegrationTemplateValues.PushIntegrationTypeValue);
            excelIntegrationContent.Add(IntegrationTemplateKeys.CRMNameKey, IntegrationTemplateValues.CRMNameValue);
            excelIntegrationContent.Add(IntegrationTemplateKeys.APIKey, dataParams.Id.ToString());
            excelIntegrationContent.Add(IntegrationTemplateKeys.Tenant, tenant);
            excelIntegrationContent.Add(IntegrationTemplateKeys.WorkingEndpointOpenUrlKey, 
                string.Format(workingEndpointOpenUrlValue, 1, dataParams.LeadSource.ToString() + (IsServetel ? callType == IVRType.Inbound ? "/Servetel/Inbound" : "/Servetel/Outbound" : string.Empty)));
            excelIntegrationContent.Add(IntegrationTemplateKeys.PayloadKey, GetRequestBodyJsonFromFile(dataParams.LeadSource));
            excelIntegrationContent.Add(IntegrationTemplateKeys.SuccessReponseMessageSuccessKey, IntegrationTemplateValues.SuccessReponseMessageSuccessValue);
            excelIntegrationContent.Add(IntegrationTemplateKeys.FailReponseMessageSuccessKey, IntegrationTemplateValues.FailReponseMessageSuccessValue);
            excelIntegrationContent.Add(IntegrationTemplateKeys.AuthenticationTypeKey, IntegrationTemplateValues.AuthenticationTypeValue);
            excelIntegrationContent.Add(IntegrationTemplateKeys.IntegrationMannualKey, IntegrationTemplateValues.IntegrationMannualValue);
            return excelIntegrationContent;
        }
        public static string GetRequestBodyJsonFromFile(LeadSource leadSource, bool isServetel = false)
        {
            var currentDir = Directory.GetCurrentDirectory();
            string path = string.Empty;
            if (leadSource == LeadSource.Website)
            {
                path = Path.Combine(currentDir, "Files/Integration/WebsiteIntegrationPayload.json");
            }
            else if (leadSource == LeadSource.IVR && isServetel)
            {
                path = Path.Combine(currentDir, "Files/Integration/ServetelIntegrationPayload.json");
            }
            else if (leadSource == LeadSource.IVR)
            {
                path = Path.Combine(currentDir, "Files/Integration/IVRIntegrationPayload.json");
            }
            else
            {
                path = Path.Combine(currentDir, "Files/Integration/ListingSitesIntegrationPayload.json");
            }
            StreamReader r = new StreamReader(path);
            return r.ReadToEnd();
        }
        public static string GetWAWorkingEndPointUrl()
        {
            switch (env)
            {
                case "dev":
                    return WorkingEndPoints.DevWorkingEndpointOpenUrlValue;
                case "qa":
                    return WorkingEndPoints.QAWorkingEndpointOpenUrlValue;
                case "prd":
                    return WorkingEndPoints.PrdWorkingEndpointOpenUrlValue;
                default:
                    return string.Empty;
            }
        }
    }
}
