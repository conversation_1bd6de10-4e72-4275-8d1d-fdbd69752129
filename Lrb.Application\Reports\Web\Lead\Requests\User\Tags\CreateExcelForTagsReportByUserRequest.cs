﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Utils;

namespace Lrb.Application.Reports.Web
{
    public class CreateExcelForTagsReportByUserRequest : IRequest<Response<string>>
    {
        public DateTime? CreatedFrom { get; set; }
        public DateTime? CreatedTo { get; set; }
        public DateTime? UpdatedFrom { get; set; }
        public DateTime? UpdatedTo { get; set; }
        public DateTime? ScheduledFrom { get; set; }
        public DateTime? ScheduledTo { get; set; }
        public List<string>? Projects { get; set; }
        public bool IsWithTeam { get; set; }
        public List<Guid>? UserIds { get; set; }
        public string? SearchText { get; set; }
        public List<LeadSource>? Sources { get; set; }
        public List<string>? AgencyNames { get; set; }
        public List<string>? SubSources { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = int.MaxValue;
        public string? TimeZoneId { get; set; }
        public TimeSpan BaseUTcOffset { get; set; }
    }
    public class CreateExcelForTagsReportByUserRequestHandler : IRequestHandler<CreateExcelForTagsReportByUserRequest, Response<string>>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly IBlobStorageService _blobStorageService;
        private readonly ICurrentUser _currentUser;

        public CreateExcelForTagsReportByUserRequestHandler(IDapperRepository dapperRepository, IBlobStorageService blobStorageService, ICurrentUser currentUser)
        {
            _dapperRepository = dapperRepository;
            _blobStorageService = blobStorageService;
            _currentUser = currentUser;
        }

        public async Task<Response<string>> Handle(CreateExcelForTagsReportByUserRequest request, CancellationToken cancellationToken)
        {
            var tenantId = _currentUser.GetTenant();
            var userId = _currentUser.GetUserId();
            List<Guid> teamUserIds = new();
            if (request?.UserIds?.Any() ?? false)
            {
                if (request?.IsWithTeam ?? false)
                {
                    teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                }
                else
                {
                    teamUserIds = request?.UserIds ?? new List<Guid>();
                }
            }
            else
            {
                request.UserIds = new List<Guid>() { userId };
                teamUserIds = (await _dapperRepository.GetSubordinateIdsAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
            }
            request.UpdatedFrom = request.UpdatedFrom != null ? request.UpdatedFrom.Value.ConvertFromDateToUtc() : null;
            request.UpdatedTo = request.UpdatedTo != null ? request.UpdatedTo.Value.ConvertToDateToUtc() : null;
            request.CreatedFrom = request.CreatedFrom != null ? request.CreatedFrom.Value.ConvertFromDateToUtc() : null;
            request.CreatedTo = request.CreatedTo != null ? request.CreatedTo.Value.ConvertToDateToUtc() : null;
            request.ScheduledFrom = request.ScheduledFrom != null ? request.ScheduledFrom.Value.ConvertFromDateToUtc() : null;
            request.ScheduledTo = request.ScheduledTo != null ? request.ScheduledTo.Value.ConvertToDateToUtc() : null;
            var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<LeadTagsUserDto>("LeadratBlack", "GetTagsReportByUser", new
            {
                createdfrom = request.CreatedFrom,
                createdto = request.CreatedTo,
                updatedfrom = request.UpdatedFrom,
                updatedto = request.UpdatedTo,
                scheduledfrom = request.ScheduledFrom,
                scheduledto = request.ScheduledTo,
                tenantid = tenantId,
                userids = teamUserIds,
                searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                sources = request?.Sources?.ConvertAll(i => (int)i),
                subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                agencynames = request?.AgencyNames?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                pagesize = request.PageSize,
                pagenumber = request.PageNumber
            })).ToList();
            var fileBytes = ExcelHelper.CreateExcelFromList(res, new List<string>(), new List<string>() { "Id" }, request.TimeZoneId, request.BaseUTcOffset).ToArray();
            var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", $"Reports/{tenantId ?? "Default"}", $"Report-{DateTime.Now}.xlsx", fileBytes);
            var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
            return new(presignedUrl, null);
        }
    }
}
