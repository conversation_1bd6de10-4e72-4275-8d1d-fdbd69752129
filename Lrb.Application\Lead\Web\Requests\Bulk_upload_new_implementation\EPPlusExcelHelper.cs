﻿using Amazon.Runtime.Internal.Transform;
using DocumentFormat.OpenXml.Spreadsheet;
using DocumentFormat.OpenXml.Wordprocessing;
using Lrb.Application.Attendance.Web.Dtos;
using Lrb.Application.Reports.Web.Dtos.ExportTrackerDto;
using Lrb.Application.Reports.Web.Dtos.FiltersName;
using Lrb.Application.Utils;
using Microsoft.AspNetCore.Http;
using Microsoft.Graph;
using Newtonsoft.Json;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Globalization;
using System.Reflection;

namespace Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation
{
    public static class EPPlusExcelHelper
    {
        public static DataTable ConvertExcelToDataTable(Stream fileStream, string? sheetName = null)
        {
            Stopwatch sw = Stopwatch.StartNew();
            sw.Start();
            DataTable dt = new();
            if (fileStream == null || fileStream.Length <= 0)
            {
                throw new Exception("No data found in excel sheet.");
            }
            var package = new ExcelPackage(fileStream);
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            var sheetCount = package?.Workbook?.Worksheets.Count ?? 0;
            if (sheetCount <= 0) { throw new Exception("No sheet found in the excel File."); }
            ExcelWorksheet? worksheet = null;
           if (!string.IsNullOrWhiteSpace(sheetName))
            {
                string actualSheetName = sheetName.Contains('/') ? sheetName.Split('/').Last() : sheetName;
                worksheet = package?.Workbook?.Worksheets?[actualSheetName];
                if (worksheet == null) { throw new Exception($"No sheet found by the name \"{actualSheetName}\"."); }
            }
            else
            {
                worksheet = package?.Workbook?.Worksheets?.FirstOrDefault();
            }
            //var worksheetIndex = worksheet?.Index;
            var noOfColumns = worksheet?.Dimension?.Columns ?? 0;
            var noOfRows = worksheet?.Dimension?.Rows ?? 0;
            for (int row = 1; row <= noOfRows; row++)
            {
                if (row <= 1)
                {
                    for (int col = 1; col <= noOfColumns; col++)
                    {
                        var value = worksheet?.Cells[row, col]?.Value?.ToString()?.Trim() ?? $"UnSpecified {col}";
                        dt.Columns.Add(value);
                    }
                }
                else if (row > 1)
                {
                    var dataRow = dt.NewRow();
                    for (int col = 1; col <= noOfColumns; col++)
                    {
                        var value = worksheet?.Cells[row, col]?.Value?.ToString()?.Trim() ?? "";
                        dataRow[col - 1] = value;
                    }
                    dt.Rows.Add(dataRow);
                }
            }
            sw.Stop();
            var timeTaken = sw.Elapsed;
            return dt;
        }
        public static DataTable ConvertExcelToDataTableV1(Stream fileStream, string? sheetName = null)
        {
            int maxRows = 100001;
            Stopwatch sw = Stopwatch.StartNew();
            sw.Start();
            DataTable dt = new();
            if (fileStream == null || fileStream.Length <= 0)
            {
                throw new Exception("No data found in excel sheet.");
            }
            var package = new ExcelPackage(fileStream);
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            var sheetCount = package?.Workbook?.Worksheets.Count ?? 0;
            if (sheetCount <= 0) { throw new Exception("No sheet found in the excel File."); }
            ExcelWorksheet? worksheet = null;
            if (!string.IsNullOrWhiteSpace(sheetName))
            {
                string actualSheetName = sheetName.Contains('/') ? sheetName.Split('/').Last() : sheetName;
                worksheet = package?.Workbook?.Worksheets?[actualSheetName];
                if (worksheet == null) { throw new Exception($"No sheet found by the name \"{actualSheetName}\"."); }
            }
            else
            {
                worksheet = package?.Workbook?.Worksheets?.FirstOrDefault();
            }
            //var worksheetIndex = worksheet?.Index;
            var noOfColumns = worksheet?.Dimension?.Columns ?? 0;
            var noOfRows = worksheet?.Dimension?.Rows ?? 0;
            int rowsToRead = Math.Min(noOfRows, maxRows);
            for (int row = 1; row <= rowsToRead; row++)
            {
                if (row <= 1)
                {
                    for (int col = 1; col <= noOfColumns; col++)
                    {
                        var value = worksheet?.Cells[row, col]?.Value?.ToString()?.Trim() ?? $"UnSpecified {col}";
                        dt.Columns.Add(value);
                    }
                }
                else if (row > 1)
                {
                    var dataRow = dt.NewRow();
                    for (int col = 1; col <= noOfColumns; col++)
                    {
                        var value = worksheet?.Cells[row, col]?.Value?.ToString()?.Trim() ?? "";
                        dataRow[col - 1] = value;
                    }
                    dt.Rows.Add(dataRow);
                }
            }
            sw.Stop();
            var timeTaken = sw.Elapsed;
            return dt;
        }
        public static DataTable ConvertExcelToDataTable(IFormFile file, string? sheetName = null)
        {
            MemoryStream stream = new MemoryStream();
            // The stream now contains the file contents
            file.CopyTo(stream);

            if (stream.Length == 0)
            {
                throw new Exception("No data found in excel sheet.");
            }
            if (stream.Position != 0)
            {
                stream.Position = 0;
            }
            Stopwatch sw = Stopwatch.StartNew();
            sw.Start();
            DataTable dt = new();
            var package = new ExcelPackage(stream);
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            var sheetCount = package?.Workbook?.Worksheets.Count ?? 0;
            if (sheetCount <= 0) { throw new Exception("No sheet found in the excel File."); }
            ExcelWorksheet? worksheet = null;
            if (!string.IsNullOrWhiteSpace(sheetName))
            {
                worksheet = package?.Workbook?.Worksheets?[sheetName];
                if (worksheet == null) { throw new Exception($"No sheet found by the name \"{sheetName}\"."); }
            }
            else
            {
                worksheet = package?.Workbook?.Worksheets?.FirstOrDefault();
            }
            var worksheetIndex = worksheet.Index;
            //var worksheet = package.Workbook.Worksheets[0];
            var noOfColumns = worksheet?.Dimension?.Columns ?? 0;
            var noOfRows = worksheet?.Dimension?.Rows ?? 0;
            for (int row = 1; row <= noOfRows; row++)
            {
                if (row <= 1 && worksheetIndex < 1)
                {
                    for (int col = 1; col <= noOfColumns; col++)
                    {
                        var value = worksheet?.Cells[row, col]?.Value?.ToString()?.Trim() ?? $"UnSpecified {col}";
                        dt.Columns.Add(value);
                    }
                }
                else if (row > 1)
                {
                    var dataRow = dt.NewRow();
                    for (int col = 1; col <= noOfColumns; col++)
                    {
                        var value = worksheet?.Cells[row, col]?.Value?.ToString()?.Trim() ?? "";
                        dataRow[col - 1] = value;
                    }
                    dt.Rows.Add(dataRow);
                }
            }
            sw.Stop();
            var timeTaken = sw.Elapsed;
            return dt;
        }
        public static List<string> GetFileColumns(IFormFile file)
        {
            MemoryStream stream = new MemoryStream();
            // The stream now contains the file contents
            file.CopyTo(stream);

            if (stream.Length == 0)
            {
                throw new Exception("No data found in excel sheet.");
            }
            if (stream.Position != 0)
            {
                stream.Position = 0;
            }
            List<string> columns = new List<string>();
            var package = new ExcelPackage(stream);
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            var worksheet = package.Workbook.Worksheets.FirstOrDefault();
            if (worksheet != null)
            {
                var worksheetIndex = worksheet.Index;
                var noOfColumns = worksheet?.Dimension?.Columns ?? 0;
                var noOfRows = worksheet?.Dimension?.Rows ?? 0;
                if (noOfRows > 0 && noOfColumns > 0)
                {
                    for (int col = 1; col <= noOfColumns; col++)
                    {
                        var value = worksheet?.Cells[1, col]?.Value?.ToString()?.Trim() ?? $"UnSpecified {col}";
                        columns.Add(value);
                    }
                }
            }
            else
            {
                throw new Exception("no sheet available");
            }

            return columns.Distinct().ToList();
        }
        public static Dictionary<string, List<string>> GetFileColumnsOfMultiSheets(IFormFile file)
        {
            MemoryStream stream = new MemoryStream();
            // The stream now contains the file contents
            file.CopyTo(stream);

            if (stream.Length == 0)
            {
                throw new Exception("No data found in excel sheet.");
            }
            if (stream.Position != 0)
            {
                stream.Position = 0;
            }
            var package = new ExcelPackage(stream);
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            //var worksheet = package.Workbook.Worksheets.FirstOrDefault();
            Dictionary<string, List<string>> sheetColumns = new Dictionary<string, List<string>>();
            string fileName = Path.GetFileName(file.FileName);
            foreach (var worksheet in package.Workbook.Worksheets)
            {

                if (worksheet != null)
                {
                    List<string> columns = new List<string>();
                    var worksheetIndex = worksheet.Index;
                    var noOfColumns = worksheet?.Dimension?.Columns ?? 0;
                    var noOfRows = worksheet?.Dimension?.Rows ?? 0;
                    if (noOfRows > 0 && noOfColumns > 0)
                    {
                        for (int col = 1; col <= noOfColumns; col++)
                        {
                            var value = worksheet?.Cells[1, col]?.Value?.ToString()?.Trim() ?? $"UnSpecified {col}";
                            columns.Add(value);
                        }
                    }
                    sheetColumns.Add(worksheet.Name, columns);
                    //sheetColumns.Add($"{fileName}/{worksheet.Name}", columns);
                }
            }

            return sheetColumns;
        }
        public static byte[] GenarateExcelbyDataTable(DataTable dataTable)
        {
            using ExcelPackage package = new ExcelPackage();
            ExcelWorksheet worksheet = package.Workbook.Worksheets.Add("Sheet1");

            // Load data from DataTable to worksheet
            worksheet.Cells["A1"].LoadFromDataTable(dataTable, true);

            // Get the range of column names
            ExcelRange columnRange = worksheet.Cells[1, 1, 1, dataTable.Columns.Count];

            // Apply formatting to the column names
            columnRange.Style.Font.Bold = true;
            columnRange.Style.Fill.PatternType = ExcelFillStyle.Solid;
            columnRange.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.Yellow);

            // Auto-fit columns for better appearance
            worksheet.Cells.AutoFitColumns();

            // Create a memory stream to store the Excel package
            MemoryStream stream = new MemoryStream();

            // Save the Excel package to the memory stream
            package.SaveAs(stream);

            // Rewind the stream position to the beginning
            stream.Position = 0;

            // Return the memory stream
            return stream.ToArray();
        }

        /*
                public static byte[] GenerateExcelByObjectList<T>(List<List<T>> dataForSheets)
                {
                    if (dataForSheets == null || dataForSheets.Count == 0)
                    {
                        throw new ArgumentException("Data list is empty or null.");
                    }

                    ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                    using var package = new ExcelPackage();

                    for (int sheetIndex = 0; sheetIndex < dataForSheets.Count; sheetIndex++)
                    {
                        var sheetData = dataForSheets[sheetIndex];

                        if (sheetData == null || sheetData.Count == 0)
                        {
                            continue; // Skip empty sheets
                        }

                        //set sheet names
                            PropertyInfo userNameProperty = typeof(T).GetProperty("UserName");               
                            string userName = userNameProperty.GetValue(sheetData.FirstOrDefault())?.ToString();
                            var  worksheet= package.Workbook.Worksheets.Add(userName); 

                      *//*  var worksheet = package.Workbook.Worksheets.Add($"Sheet{sheetIndex + 1}"); *//*
                       var properties = typeof(T).GetProperties();


                        // Set the column headers
                        for (int col = 0; col < properties.Length; col++)
                        {
                            //worksheet.Cells[1, col + 1].Value = properties[col].Name;


                            var cell = worksheet.Cells[1, col + 1];
                            cell.Value = properties[col].Name;

                            var border = cell.Style.Border;
                            border.BorderAround(OfficeOpenXml.Style.ExcelBorderStyle.Thin, System.Drawing.Color.Black);

                            cell.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                            cell.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.Black);
                            cell.Style.Font.Color.SetColor(System.Drawing.Color.White);
                        }
        //====================
                        TimeSpan totalDuration = TimeSpan.Zero;
                        int totalDaysCount = 0;
                        DateTime? previousDate = null;
                        //=======================
                        // Populate the data for the sheet
                        for (int row = 0; row < sheetData.Count; row++)
                        {
                            for (int col = 0; col < properties.Length; col++)
                            {
                                var cell = worksheet.Cells[row + 2, col + 1];
                                var value = properties[col].GetValue(sheetData[row]);
                                //===============
                                if (properties[col].Name == "TotalHours")
                                {
                                    if (value != null)
                                    {
                                        if (TimeSpan.TryParse(value.ToString(), out TimeSpan hours))
                                        {
                                            totalDuration = totalDuration.Add(hours);
                                        }
                                    }
                                }
                                else if (properties[col].Name == "Date")
                                {
                                    if (value is DateTime dateValue)
                                    {
                                        if (!previousDate.HasValue || dateValue.Date != previousDate.Value.Date)
                                        {
                                            totalDaysCount++;
                                            previousDate = dateValue.Date;
                                        }
                                    }
                                }
        //========================
                                    if (value is IList<object> listValue && listValue.Count > 0)
                                {
                                    // Merge cells in the specified column for lists of objects
                                    var startCell = cell.Start;
                                    var endCell = cell.End;
                                    var mergeStartCell = worksheet.Cells[startCell.Row, startCell.Column];
                                    var mergeEndCell = worksheet.Cells[endCell.Row, endCell.Column];

                                    if (!mergeStartCell.Merge)
                                    {
                                        // Only merge cells if they are not already merged
                                        var mergedRange = worksheet.Cells[startCell.Row, startCell.Column, endCell.Row + listValue.Count - 1, endCell.Column];
                                        mergedRange.Merge = true;

                                        // Display each object in the list in a separate cell within the merged region
                                        int rowIndex = startCell.Row;
                                        foreach (var obj in listValue)
                                        {
                                            worksheet.Cells[rowIndex, startCell.Column].Value = obj;
                                            rowIndex++;
                                        }
                                    }
                                }
                                else
                                {
                                    cell.Value = value;

                                    cell.Style.Border.BorderAround(OfficeOpenXml.Style.ExcelBorderStyle.Thin, System.Drawing.Color.Black);
                                }
                            }
                        }
                    }

                    // Create a memory stream to store the Excel package
                    using MemoryStream stream = new MemoryStream();

                    // Save the Excel package to the memory stream
                    package.SaveAs(stream);

                    // Rewind the stream position to the beginning
                    stream.Position = 0;

                    // Return the memory stream
                    return stream.ToArray();
                }
        */






        public static byte[] GenerateExcelByObjectList<T>(List<List<T>> dataForSheets)
        {

            HashSet<DateTime> uniqueDates = new HashSet<DateTime>();

            if (dataForSheets == null || dataForSheets.Count == 0)
            {
                throw new ArgumentException("Data list is empty or null.");
            }

            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            using var package = new ExcelPackage();

            for (int sheetIndex = 0; sheetIndex < dataForSheets.Count; sheetIndex++)
            {


                try
                {


                    var sheetData = dataForSheets[sheetIndex];
                    /* if (sheetData == null || sheetData.Count == 0)
                     {
                         continue; // Skip empty sheets
                     }*/
                    PropertyInfo userNameProperty = typeof(T).GetProperty("UserName");
                    string userName = userNameProperty.GetValue(sheetData.FirstOrDefault())?.ToString();
                    var worksheet = package.Workbook.Worksheets.Add(userName);
                    PropertyInfo nameProperty = typeof(T).GetProperty("Name");
                    PropertyInfo dateProperty = typeof(T).GetProperty("Date");
                    var properties = new List<PropertyInfo> { nameProperty, dateProperty };
                    properties.AddRange(typeof(T).GetProperties().Where(p => p.Name != "UserName" && p.Name != "Name" && p.Name != "Date"));

                    // var properties = typeof(T).GetProperties();
                    //var properties = typeof(T).GetProperties().Where(p => p.Name != "UserName").ToList();

                    for (int col = 0; col < properties.Count(); col++)
                    {

                        var cell = worksheet.Cells[1, col + 1];

                        cell.Value = properties[col].Name;

                        var border = cell.Style.Border;
                        // border.BorderAround(OfficeOpenXml.Style.ExcelBorderStyle.Thin, System.Drawing.Color.Black);

                        cell.Style.Border.Right.Style = ExcelBorderStyle.Thin;
                        cell.Style.Border.Right.Color.SetColor(System.Drawing.Color.White);

                        cell.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                        cell.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.Black);
                        cell.Style.Font.Color.SetColor(System.Drawing.Color.White);
                    }

                    TimeSpan totalDuration = TimeSpan.Zero;
                    int totalDaysCount = 0;
                    DateTime? previousDate = null;

                    for (int row = 0; row < sheetData.Count; row++)
                    {
                        for (int col = 0; col < properties.Count(); col++)
                        {
                            var cell = worksheet.Cells[row + 2, col + 1];
                            var value = properties[col].GetValue(sheetData[row]);

                            if (properties[col].Name == "TotalWorkHours")
                            {
                                if (value != null)
                                {
                                    if (TimeSpan.TryParse(value.ToString(), out TimeSpan hours))
                                    {
                                        totalDuration = totalDuration.Add(hours);
                                    }
                                }
                            }
                            else if (properties[col].Name == "Date")
                            {
                                if (value != null)
                                {
                                    if (DateTime.TryParse(value.ToString(), out DateTime result))
                                    {
                                        uniqueDates.Add(result);
                                    }
                                }
                            }
                            //Stores Values in the cells
                            if (value is IList<object> listValue && listValue.Count > 0)
                            {
                                var startCell = cell.Start;
                                var endCell = cell.End;
                                var mergeStartCell = worksheet.Cells[startCell.Row, startCell.Column];
                                var mergeEndCell = worksheet.Cells[endCell.Row, endCell.Column];

                                if (!mergeStartCell.Merge)
                                {
                                    var mergedRange = worksheet.Cells[startCell.Row, startCell.Column, endCell.Row + listValue.Count - 1, endCell.Column];
                                    mergedRange.Merge = true;

                                    int rowIndex = startCell.Row;
                                    foreach (var obj in listValue)
                                    {
                                        worksheet.Cells[rowIndex, startCell.Column].Value = obj;
                                        rowIndex++;
                                    }
                                }
                            }
                            else
                            {
                                if (value == "" || value == null)
                                {
                                    cell.Value = "Missing";
                                }
                                else
                                {
                                    cell.Value = value;
                                }

                                cell.Style.Border.BorderAround(OfficeOpenXml.Style.ExcelBorderStyle.Thin, System.Drawing.Color.Black);
                            }
                        }
                    }

                    // Insert "Total Days" 
                    int lastRow = sheetData.Count + 2;
                    worksheet.Cells[lastRow, 1].Value = "Total Days:";
                    worksheet.Cells[lastRow, 2].Value = $"{uniqueDates.Count()} days";

                    //"Grand Total Hours" at the bottom of the sheet
                    worksheet.Cells[lastRow, 6].Value = "Total Hours:";
                    string totalHoursFormatted = $"{(int)totalDuration.TotalHours:D2}:{totalDuration.Minutes:D2}:{totalDuration.Seconds:D2}";
                    worksheet.Cells[lastRow, 7].Value = totalHoursFormatted;

                    int lastRow1 = sheetData.Count + 3;
                    worksheet.Cells[lastRow1, 6].Value = "Average Hours:";

                    var daysCount = uniqueDates.Count(); // Number of days (e.g., 17)
                    double totalHours = totalDuration.TotalHours;

                    if (daysCount > 0 && totalHours > 0)
                    {
                        // Calculate the average hours as an integer
                        int avgHoursInt = (int)(totalHours / daysCount);
                        // Calculate the remaining minutes and seconds
                        int remainingMinutes = (int)((totalHours / daysCount - avgHoursInt) * 60);
                        int remainingSeconds = (int)(((totalHours / daysCount - avgHoursInt) * 60 - remainingMinutes) * 60);
                        // Format the average hours as "hh:mm:ss.ss" with two decimal places for seconds
                        string avgHoursFormatted = $"{avgHoursInt:D2}:{remainingMinutes:D2}:{remainingSeconds:D2}";
                        worksheet.Cells[lastRow1, 7].Value = avgHoursFormatted;
                    }
                    else
                    {
                        worksheet.Cells[lastRow1, 7].Value = 0;
                    }

                    //string avgHrs = totalDuration.TotalHours / uniqueDates.Count(); // Calculate average hours
                    //string avgHoursFormatted = $"{avgTimeSpan.Hours:D2}:{avgTimeSpan.Minutes:D2}:{avgTimeSpan.Seconds:D2}"; // Format as "hh:mm:ss"

                    // Merge and format the cells for "Total Days" and "Grand Total Hours"
                    var totalDaysCellValue = worksheet.Cells[lastRow, 2];
                    var grandTotalHoursCellValue = worksheet.Cells[lastRow, 7];
                    var avgHrsValue = worksheet.Cells[lastRow1, 7];


                    var totalDaysCell = worksheet.Cells[lastRow, 1];
                    var grandTotalHoursCell = worksheet.Cells[lastRow, 6];
                    var avgHrsCell = worksheet.Cells[lastRow1, 6];



                    totalDaysCellValue.Merge = true;
                    grandTotalHoursCellValue.Merge = true;
                    avgHrsValue.Merge = true;


                    totalDaysCellValue.Style.Font.Bold = true;
                    grandTotalHoursCellValue.Style.Font.Bold = true;
                    avgHrsValue.Style.Font.Bold = true;


                    totalDaysCell.Style.Font.Bold = true;
                    grandTotalHoursCell.Style.Font.Bold = true;
                    avgHrsCell.Style.Font.Bold = true;

                    totalDaysCellValue.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    grandTotalHoursCellValue.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    avgHrsValue.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;

                    totalDaysCell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                    grandTotalHoursCell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                    avgHrsCell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;


                    // Clear totalDuration and totalDaysCount for the next sheet
                    totalDuration = TimeSpan.Zero;
                    totalDaysCount = 0;
                    uniqueDates.Clear();
                }
                catch(Exception e)
                {
                    Console.WriteLine($"Attendance Export  -> Exception {JsonConvert.SerializeObject(e, settings: new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                    throw;
                }
            }

            // Create a memory stream to store the Excel package
            using MemoryStream stream = new MemoryStream();

            // Save the Excel package to the memory stream
            package.SaveAs(stream);

            // Rewind the stream position to the beginning
            stream.Position = 0;

            // Return the memory stream
            return stream.ToArray();
        }

        public static byte[] GenerateExcel(List<List<AttendanceReportDto>> data, ExportedDetailsDto exportedDetailsDto,string? timeZoneId, TimeSpan baseUTcOffset)
        {
            // Create a new Excel package
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            using (var package = new ExcelPackage())
            {
                // Create a new worksheet
                var worksheet = package.Workbook.Worksheets.Add("Attendance Report");
               
                // Add user details at the top left
                worksheet.Cells[1, 1].Value = "Created By:";
                if(exportedDetailsDto.CreatedBy!= null)
                {
                    worksheet.Cells[1, 2].Value = exportedDetailsDto.CreatedBy;
                }
                else
                {
                    worksheet.Cells[1, 2].Value = "N/A";
                }
                
                worksheet.Cells[2, 1].Value = "Created On:";
                if(exportedDetailsDto.CreatedOn!=null)
                {
                    worksheet.Cells[2, 2].Value = exportedDetailsDto.CreatedOn;
                }
                else
                {
                    worksheet.Cells[2, 2].Value ="N/A";
                }
                
                worksheet.Cells[3, 1].Value = "From Date:";
                if(exportedDetailsDto.FromDate!=null)
                {
                    worksheet.Cells[3, 2].Value = exportedDetailsDto.FromDate;

                }
                else
                {
                    worksheet.Cells[3, 2].Value = "N/A";
                }
                
                worksheet.Cells[4, 1].Value = "To Date:";
                if(exportedDetailsDto.ToDate!=null)
                {
                    worksheet.Cells[4, 2].Value = exportedDetailsDto.ToDate;
                }
                else
                {
                    worksheet.Cells[4, 2].Value = "N/A";
                }
                worksheet.Cells[5, 1].Value = "User Names:";
                if (exportedDetailsDto.UsersNames != null)
                {
                    worksheet.Cells[5, 2].Value = exportedDetailsDto.UsersNames;
                }
                else
                {
                    worksheet.Cells[5, 2].Value ="N/A";
                }
               
                
                worksheet.Cells[6, 1].Value = "Designation:";
                if (exportedDetailsDto.Designation != null)
                {
                    worksheet.Cells[6, 2].Value = exportedDetailsDto.Designation;
                }
                else
                {
                    worksheet.Cells[6, 2].Value = "N/A";
                }
               
                worksheet.Cells[7, 1].Value = "Department:";
                if(exportedDetailsDto.Department!=null)
                {
                    worksheet.Cells[7, 2].Value = exportedDetailsDto.Department;
                }
                else
                {
                    worksheet.Cells[7, 2].Value = "N/A";
                }
                worksheet.Cells[8, 1].Value = "Report To:";
                if (exportedDetailsDto.ReportTo!=null)
                {
                    worksheet.Cells[8, 2].Value = exportedDetailsDto.ReportTo;
                }
                else
                {
                    worksheet.Cells[8, 2].Value = "N/A";
                }
                // Styling the title cell
                worksheet.Cells["A9:G9"].Merge = true;
                var titleCell = worksheet.Cells[9, 1];
                titleCell.Value = "Attendance Report";               
                titleCell.Style.Font.Bold = true;
                titleCell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                titleCell.Style.Font.Size = 20; // Set the desired font size
                var titleRange = worksheet.Cells["A9:G9"];
                titleRange.Style.Border.Top.Style = ExcelBorderStyle.Medium;
                titleRange.Style.Border.Bottom.Style = ExcelBorderStyle.Medium;
                titleRange.Style.Border.Left.Style = ExcelBorderStyle.Medium;
                titleRange.Style.Border.Right.Style = ExcelBorderStyle.Medium;
                worksheet.Row(9).Height = 30;

                var borderColor = System.Drawing.Color.Black;
                titleRange.Style.Border.Top.Color.SetColor(borderColor);
                titleRange.Style.Border.Bottom.Color.SetColor(borderColor);
                titleRange.Style.Border.Left.Color.SetColor(borderColor);
                titleRange.Style.Border.Right.Color.SetColor(borderColor);




                //defining blocks for colur and style
                var detailsBlock = worksheet.Cells["A1:B8"];
                int dataRowCount = data.Sum(list => list.Count); // Calculate the total number of data rows
                var dataBlock = worksheet.Cells["A10:G" + (dataRowCount + 10)];
                //var dataBlock = worksheet.Cells["A8:H" + (data.Count + 8)];
                var headerRow = worksheet.Cells["A10:G10"];
                
                //borders 
                detailsBlock.Style.Border.Top.Style = ExcelBorderStyle.Thin;
                detailsBlock.Style.Border.Left.Style = ExcelBorderStyle.Thin;
                detailsBlock.Style.Border.Right.Style = ExcelBorderStyle.Thin;
                detailsBlock.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                //bg colur for details block
                detailsBlock.Style.Fill.PatternType = ExcelFillStyle.Solid;
                detailsBlock.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                detailsBlock.Style.Font.Bold=true;

                dataBlock.Style.Border.Top.Style = ExcelBorderStyle.Thin;
                dataBlock.Style.Border.Left.Style = ExcelBorderStyle.Thin;
                dataBlock.Style.Border.Right.Style = ExcelBorderStyle.Thin;
                dataBlock.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                //data block header styles

                headerRow.Style.Fill.PatternType = ExcelFillStyle.Solid;
                headerRow.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.Black); // Background color
                headerRow.Style.Font.Color.SetColor(System.Drawing.Color.White); // Font color
                for (int col = 1; col <= 6; col++)
                {
                    worksheet.Cells[10, col].Style.Border.Right.Style = ExcelBorderStyle.Medium;
                    worksheet.Cells[10, col].Style.Border.Right.Color.SetColor(System.Drawing.Color.White);
                }
                worksheet.Cells[10, 7].Style.Border.Right.Style = ExcelBorderStyle.None;

                // Add headers
                worksheet.Cells[10, 1].Value = "Name";
                worksheet.Cells[10, 2].Value = "Date";              
                //worksheet.Cells[8, 3].Value = "UserName";
                worksheet.Cells[10, 3].Value = "ClockInTime";
                worksheet.Cells[10, 4].Value = "ClockInLocation";
                worksheet.Cells[10, 5].Value = "ClockOutTime";
                worksheet.Cells[10, 6].Value = "ClockOutLocation";
                worksheet.Cells[10, 7].Value = "TotalWorkHours";

                int row = 11; // Start inserting data from row 9
                foreach (var attendanceList in data)
                {
                    var userNameRange = worksheet.Cells[row, 1, row + attendanceList.Count - 1, 1];

                    userNameRange.Merge = true;
                    userNameRange.Value = attendanceList[0].Name; // Assuming user name is the same for all logs
                    userNameRange.Style.Font.Bold = true;
                    userNameRange.Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                    //userNameRange.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
                    foreach (var attendance in attendanceList)
                    {
                        worksheet.Cells[row, 1].Value = attendance.Name;
                        worksheet.Cells[row, 2].Value = attendance.Date;                      
                        //worksheet.Cells[row, 3].Value = attendance.UserName;
                        worksheet.Cells[row, 3].Value = attendance.ClockInTime;
                        worksheet.Cells[row, 4].Value = attendance.ClockInLocation;
                        worksheet.Cells[row, 5].Value = attendance.ClockOutTime;
                        worksheet.Cells[row, 6].Value = attendance.ClockOutLocation;
                        worksheet.Cells[row, 7].Value = attendance.TotalWorkHours;
                        row++;
                      
                    
                    }

                }
                using MemoryStream stream = new MemoryStream();
                worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();

                package.SaveAs(stream);
                stream.Position = 0;
                return stream.ToArray();
            }
        }


    }




}
