﻿using Lrb.Application.Property.Mobile;

namespace Lrb.Application.Lead.Mobile.Dtos.v2
{
    public class LeadAppointmentDto : IDto
    {
        public Guid Id { get; set; }
        public AppointmentType Type { get; set; }
        public bool IsDone { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public Guid? LocationId { get; set; }
        public AddressDto? Location { get; set; }
        public string? ProjectName { get; set; }
        public string? ExecutiveName { get; set; }
        public string? ExecutiveContactNo { get; set; }
        public string? Image { get; set; }
        public List<LeadDocument>? ImagesWithName { get; set; }
        public bool? IsManual { get; set; }
        public string? Notes { get; set; }
        public Guid LeadId { get; set; }
        public Guid CreatedBy { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid LastModifiedBy { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public DateTime? DeletedOn { get; set; }
        public Guid? DeletedBy { get; set; }
        public Guid UserId { get; set; }
        public Guid? UniqueKey { get; set; }
    }
    public class AppointmentDataDto : IDto
    {
        public string? ProjectName { get; set; }
        public string? ExecutiveName { get; set; }
        public string? ExecutiveContactNo { get; set; }
        public List<LeadDocument>? ImagesWithName { get; set; }
        public string? Notes { get; set; }
        public Guid LastModifiedBy { get; set; }
        public string? LastModifiedByUser { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public DateTime CreatedOn { get; set; }
        public Address? Location { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid UserId { get; set; }
        public Guid? UniqueKey { get; set; }
    }
    public class ViewAppointmentDataDto : IDto
    {
        public string? ProjectName { get; set; }
        public string? ExecutiveName { get; set; }
        public string? ExecutiveContactNo { get; set; }
        public List<LeadDocument>? ImagesWithName { get; set; }
        public string? Notes { get; set; }
        public Guid LastModifiedBy { get; set; }
        public string? LastModifiedByUser { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public DateTime CreatedOn { get; set; }
        public Address? Location { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid? UniqueKey { get; set; }
    }
    public class LeadAppointmentsByIdDto
    {
        public Guid Id { get; set; }
        private List<LeadAppointmentDto>? appointments;
        private List<LeadAppointmentDto>? meetingsDone;
        private List<LeadAppointmentDto>? meetingsNotDone;
        private List<LeadAppointmentDto>? siteVisitsDone;
        private List<LeadAppointmentDto>? siteVisitsNotDone;
        public List<LeadAppointmentDto>? Appointments
        {
            get => appointments;
            set
            {
                appointments = value;
                MeetingsDone = value?.Where(x => x.Type == AppointmentType.Meeting && x.IsDone)?.ToList();
                MeetingsNotDone = value?.Where(x => x.Type == AppointmentType.Meeting && !x.IsDone)?.ToList();
                SiteVisitsDone = value?.Where(x => x.Type == AppointmentType.SiteVisit && x.IsDone)?.ToList();
                SiteVisitsNotDone = value?.Where(x => x.Type == AppointmentType.SiteVisit && !x.IsDone)?.ToList();
            }
        }

        public List<LeadAppointmentDto>? MeetingsDone { get => Appointments?.Where(x => x.Type == AppointmentType.Meeting && x.IsDone)?.ToList(); set => meetingsDone = value; }
        public List<LeadAppointmentDto>? MeetingsNotDone { get => Appointments?.Where(x => x.Type == AppointmentType.Meeting && !x.IsDone)?.ToList(); set => meetingsNotDone = value; }
        public List<LeadAppointmentDto>? SiteVisitsDone { get => Appointments?.Where(x => x.Type == AppointmentType.SiteVisit && x.IsDone)?.ToList(); set => siteVisitsDone = value; }
        public List<LeadAppointmentDto>? SiteVisitsNotDone { get => Appointments?.Where(x => x.Type == AppointmentType.SiteVisit && !x.IsDone)?.ToList(); set => siteVisitsNotDone = value; }
    }
    public class LeadAppointmentsByIdCountDto
    {
        public Guid Id { get; set; }

        public int MeetingsDoneCount { get; set; }
        public int MeetingsNotDoneCount { get; set; }
        public int SiteVisitsDoneCount { get; set; }
        public int SiteVisitsNotDoneCount { get; set; }
        public int TotalAppointmentsCount { get; set; }
    }

}
