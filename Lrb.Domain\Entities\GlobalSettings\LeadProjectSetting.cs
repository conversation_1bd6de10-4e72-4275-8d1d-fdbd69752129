﻿namespace Lrb.Domain.Entities
{
    public class LeadProjectSetting
    {
        public bool IsProjectMandatoryEnabled { get; set; }
        public bool IsProjectMandatoryOnSiteVisitDone { get; set; }
        public bool IsProjectMandatoryOnMeetingDone { get; set; }
        public bool IsProjectMandatoryOnBooking { get; set; }
        public bool IsWaterMarksOnImagesEnabled { get; set; }
        public string? WaterMarkUrl { get; set; }
        public WaterMarkPosition WaterMarkPosition { get; set; }
        public string? Opacity { get; set; }
        public bool? Background { get; set; }
        public string? ImageSize { get; set; }
        public string? ImageName { get; set; }
        public bool? AllowDuplicate { get; set; }
        public bool? IsCloneEnabled { get; set; }
        public bool? AllowManualDuplicate { get; set; }
    }
}
