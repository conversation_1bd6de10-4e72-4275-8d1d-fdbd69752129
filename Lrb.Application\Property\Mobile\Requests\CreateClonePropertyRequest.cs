﻿using Lrb.Application.Identity.Users;

namespace Lrb.Application.Property.Mobile.Requests
{
    public class CreateClonePropertyRequest : IRequest<Response<Guid>>
    {
        public Guid PropertyId { get; set; }
    }
    public class CreateClonePropertyRequestHandler : IRequestHandler<CreateClonePropertyRequest, Response<Guid>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.Property> _propertyRepository;
        private readonly ICurrentUser _currentUser;
        private readonly IUserService _userService;



        public CreateClonePropertyRequestHandler(
            IRepositoryWithEvents<Lrb.Domain.Entities.Property> propertyRepository, ICurrentUser currentUser, IUserService userService)
        {
            _propertyRepository = propertyRepository;
            _currentUser = currentUser;
            _userService = userService;
        }

        public async Task<Response<Guid>> Handle(CreateClonePropertyRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var existingProperty = await _propertyRepository.FirstOrDefaultAsync(new PropertyByIdSpec(request.PropertyId), cancellationToken);

                if (existingProperty == null)
                {
                    return new Response<Guid>(Guid.Empty, "Property not found");
                }

                List<PropertyAssignment> propertyAssignments = new();
                var userName = await _userService.GetAsync(_currentUser.GetUserId().ToString() ?? string.Empty, cancellationToken);
                PropertyAssignment propertyAssigned = new();
                propertyAssigned.AssignedTo = _currentUser.GetUserId();
                propertyAssigned.AssignedUser = userName.FirstName + " " + userName.LastName;
                propertyAssigned.IsCurrentlyAssigned = true;
                propertyAssignments.Add(propertyAssigned);

                var clonedProperty = new Domain.Entities.Property
                {
                    Title = $"{existingProperty.Title}_COPY_{(existingProperty.NumberOfClones ?? 0) + 1}",
                    TitleWithLanguage = !string.IsNullOrWhiteSpace(existingProperty.TitleWithLanguage) ? $"{existingProperty.TitleWithLanguage}_COPY_{(existingProperty.NumberOfClones ?? 0) + 1}" : string.Empty,
                    SaleType = existingProperty.SaleType,
                    EnquiredFor = existingProperty.EnquiredFor,
                    Notes = existingProperty.Notes,
                    FurnishStatus = existingProperty.FurnishStatus,
                    Status = PropertyStatus.Active,
                    Rating = existingProperty.Rating,
                    ShareCount = existingProperty.ShareCount,
                    WhatsAppShareCount = 0,
                    CallShareCount = 0,
                    EmailShareCount = 0,
                    SMSShareCount = 0,
                    PossessionDate = existingProperty.PossessionDate,
                    IsGOListingEnabled = existingProperty.IsGOListingEnabled,
                    Facing = existingProperty.Facing,
                    GOPropertyId = existingProperty.GOPropertyId,
                    NoOfBHK = existingProperty.NoOfBHK,
                    NoOfBHKs = existingProperty.NoOfBHKs,
                    BHKType = existingProperty.BHKType,
                    AboutProperty = existingProperty.AboutProperty,
                    IsArchived = false,
                    Landmark = existingProperty.Landmark,
                    MaintenanceCost = existingProperty.MaintenanceCost,
                    MyProperty = existingProperty.MyProperty,
                    PropertySource = existingProperty.PropertySource,
                    UnitNo = existingProperty.UnitNo,
                    SerialNo = existingProperty.SerialNo,
                    ProjectId = existingProperty.ProjectId,
                    IsWaterMarkEnabled = existingProperty.IsWaterMarkEnabled,
                    ShouldVisisbleOnListing = false,
                    ListingStatus = ListingStatus.Draft,
                    PermitNumber = null,
                    RefrenceNo = existingProperty.RefrenceNo,
                    DTCMPermit = null,
                    ListingExpireDate = null,
                    Language = existingProperty.Language,
                    AboutPropertyWithLanguage = existingProperty.AboutPropertyWithLanguage,
                    ListingLevel = existingProperty.ListingLevel,
                    TaxationMode = existingProperty.TaxationMode,
                    PropertyType = existingProperty.PropertyType,
                    Address = existingProperty.Address,
                    UaeEmirate = existingProperty.UaeEmirate,
                    FinishingType = existingProperty.FinishingType,
                    Age = existingProperty.Age,
                    ListingOnBehalf = existingProperty.ListingOnBehalf,

                    Dimension = existingProperty.Dimension == null ? null : new PropertyDimension
                    {
                        Area = existingProperty.Dimension.Area,
                        AreaUnitId = existingProperty.Dimension.AreaUnitId,
                        AreaInSqMtr = existingProperty.Dimension.AreaInSqMtr,
                        Length = existingProperty.Dimension.Length,
                        Breadth = existingProperty.Dimension.Breadth,
                        CarpetArea = existingProperty.Dimension.CarpetArea,
                        CarpetAreaId = existingProperty.Dimension.CarpetAreaId,
                        CarpetAreaInSqMtr = existingProperty.Dimension.CarpetAreaInSqMtr,
                        BuildUpArea = existingProperty.Dimension.BuildUpArea,
                        BuildUpAreaId = existingProperty.Dimension.BuildUpAreaId,
                        BuildUpAreaInSqMtr = existingProperty.Dimension.BuildUpAreaInSqMtr,
                        SaleableArea = existingProperty.Dimension.SaleableArea,
                        SaleableAreaId = existingProperty.Dimension.SaleableAreaId,
                        SaleableAreaAreaInSqMtr = existingProperty.Dimension.SaleableAreaAreaInSqMtr,
                        CommonAreaCharges = existingProperty.Dimension.CommonAreaCharges,
                        CommonAreaChargesId = existingProperty.Dimension.CommonAreaChargesId,
                        CommonAreaChargesInSqMtr = existingProperty.Dimension.CommonAreaChargesInSqMtr,
                        Currency = existingProperty.Dimension.Currency,
                        NetArea = existingProperty.Dimension.NetArea,
                        NetAreaUnitId = existingProperty.Dimension.NetAreaUnitId,
                        NetAreaInSqMtr = existingProperty.Dimension.NetAreaInSqMtr
                    },
                    MonetaryInfo = existingProperty.MonetaryInfo == null ? null : new PropertyMonetaryInfo
                    {
                        ExpectedPrice = existingProperty.MonetaryInfo.ExpectedPrice,
                        IsNegotiable = existingProperty.MonetaryInfo.IsNegotiable,
                        Brokerage = existingProperty.MonetaryInfo.Brokerage,
                        BrokerageUnit = existingProperty.MonetaryInfo.BrokerageUnit,
                        Currency = existingProperty.MonetaryInfo.Currency,
                        BrokerageCurrency = existingProperty.MonetaryInfo.BrokerageCurrency,
                        DepositAmount = existingProperty.MonetaryInfo.DepositAmount,
                        MaintenanceCost = existingProperty.MonetaryInfo.MaintenanceCost,
                        IsPriceVissible = existingProperty.MonetaryInfo.IsPriceVissible,
                        NoOfChequesAllowed = existingProperty.MonetaryInfo.NoOfChequesAllowed,
                        MonthlyRentAmount = existingProperty.MonetaryInfo.MonthlyRentAmount,
                        EscalationPercentage = existingProperty.MonetaryInfo.EscalationPercentage,
                        PaymentFrequency = existingProperty.MonetaryInfo.PaymentFrequency,
                        ServiceChange = existingProperty.MonetaryInfo.ServiceChange,
                        Downpayment = existingProperty.MonetaryInfo.Downpayment
                    },
                    TagInfo = existingProperty.TagInfo == null ? null : new PropertyTagInfo
                    {
                        IsFeatured = existingProperty.TagInfo.IsFeatured,
                        IsGOListingEnabled = existingProperty.TagInfo.IsGOListingEnabled,
                        IsValidated = existingProperty.TagInfo.IsValidated,
                    },
                    SecurityDeposit = existingProperty.SecurityDeposit,
                    LockInPeriod = existingProperty.LockInPeriod,
                    NoticePeriod = existingProperty.NoticePeriod,

                    TenantContactInfo = existingProperty.TenantContactInfo == null ? null : new TenantContactInfo
                    {
                        Name = existingProperty.TenantContactInfo.Name,
                        Phone = existingProperty.TenantContactInfo.Phone,
                        Designation = existingProperty.TenantContactInfo.Designation,
                    },
                    OfferingType = existingProperty.OfferingType,
                    CompletionStatus = existingProperty.CompletionStatus,
                    Project = existingProperty.Project,
                    Amenities = existingProperty.Amenities?.Select(a => new PropertyAmenity
                    {
                        MasterPropertyAmenityId = a.MasterPropertyAmenityId,
                    }).ToList(),
                    Attributes = existingProperty.Attributes?.Select(a => new PropertyAttribute
                    {
                        MasterPropertyAttributeId = a.MasterPropertyAttributeId,
                        Value = a.Value,

                    }).ToList(),
                    PropertyOwnerDetails = existingProperty.PropertyOwnerDetails?.Select(a => new PropertyOwnerDetails
                    {
                        Name = a.Name,
                        Email = a.Email,
                        Phone = a.Phone,
                        AlternateContactNo = a.AlternateContactNo

                    }).ToList(),

                    Galleries = existingProperty.Galleries?.Select(a => new PropertyGallery
                    {
                        ImageKey = a.ImageKey,
                        ImageFilePath = a.ImageFilePath,
                        IsCoverImage = a.IsCoverImage,
                        Name = a.Name,
                        GalleryType = a.GalleryType,
                        ImageSegregationType = a.ImageSegregationType,
                        OrderRank = a.OrderRank,

                    }).ToList(),
                    Brochures = existingProperty.Brochures?.ToList(),
                    Links = existingProperty.Links?.ToList(),
                    NoOfFloorsOccupied = existingProperty.NoOfFloorsOccupied?.ToList(),
                    View360Url = existingProperty.View360Url?.ToList(),
                    ListingSourceAddresses = null,
                    RefrenceInfos = null,
                    AdditionalProperties = existingProperty.AdditionalProperties?.ToDictionary(x => x.Key, x => x.Value),
                    CoWorkingOperator = existingProperty.CoWorkingOperator,
                    CoWorkingOperatorName = existingProperty.CoWorkingOperatorName,
                    CoWorkingOperatorPhone = existingProperty.CoWorkingOperatorPhone,
                    ListingSources = null,
                    PropertyAssignments = propertyAssignments,
                    SecurityDepositUnit = existingProperty.SecurityDepositUnit,
                    SecurityDepositAmount = existingProperty.SecurityDepositAmount,
                    PossesionType = existingProperty.PossesionType,

                };

                existingProperty.NumberOfClones = (existingProperty.NumberOfClones ?? 0) + 1;
                await _propertyRepository.UpdateAsync(existingProperty, cancellationToken);

                await _propertyRepository.AddAsync(clonedProperty, cancellationToken);

                return new Response<Guid>(clonedProperty.Id, "Property cloned successfully");
            }
            catch (Exception ex)
            {
                return new Response<Guid>(Guid.Empty, "Failed to clone property");
            }
        }
    }
}
