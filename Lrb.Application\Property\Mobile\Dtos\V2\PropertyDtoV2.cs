﻿using Lrb.Application.Lead.Mobile;
using Lrb.Application.ListingManagement.Mobile.Dtos;

namespace Lrb.Application.Property.Mobile.Dtos.V2
{
    public class UpdatePropertyDtoV2 : CreatePropertyDtoV2
    {
    }
    public class CreatePropertyDtoV2 : PropertyDtoV2
    {
        public string? PlaceId { get; set; }
        public Guid? PropertyTypeId { get; set; }
        public List<string>? ProjectsList { get; set; }
        public List<CreateListingAddressDtoV2>? ListingAddresses { get; set; }
    }

    public class ViewPropertyDtoV2 : PropertyDtoV2
    {
        public DateTime? LastModifiedOn { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid LastModifiedBy { get; set; }
        public PropertyTypeDto? PropertyType { get; set; }
        public string? MicrositeURL { get; set; }
        public int? MicrositeUniqueNo { get; set; }
        public List<PropertyGalleryDtoV2>? Videos { get; set; }
        public ListingStatus ListingStatus { get; set; }
        public DateTime? ListingExpireDate { get; set; }
        public List<CustomListingSourceDto>? ListingSources { get; set; }
        public ListingLevel? ListingLevel { get; set; }
        public List<ViewListingSourceAddressDto>? ListingSourceAddresses { get; set; }
        public string? ShortUrl { get; set; }
    }
    public class PropertyDtoV2
    {
        public Guid Id { get; set; }
        public IEnumerable<PropertyAttributeDto>? Attributes { get; set; }
        public string? Title { get; set; }
        public EnquiryType EnquiredFor { get; set; }
        public string? Notes { get; set; }
        public FurnishStatus FurnishStatus { get; set; }
        public PropertyStatus Status { get; set; }
        public string? Rating { get; set; }
        public int ShareCount { get; set; }
        public DateTime? PossessionDate { get; set; }
        public Facing Facing { get; set; }
        //table references
        public double NoOfBHK { get; set; }
        public BHKType BHKType { get; set; }
        public PropertyMonetaryInfoDtoV2? MonetaryInfo { get; set; }
        public PropertyOwnerDetailsDto? OwnerDetails { get; set; }
        public PropertyDimensionDto? Dimension { get; set; }
        public List<PropertyGalleryDto>? Images { get; set; }
        public Dictionary<string, List<PropertyGalleryDtoV2>>? ImageUrls { get; set; }
        public IEnumerable<Guid>? Amenities { get; set; }
        public bool IsArchived { get; set; }
        public List<BrochureDto>? Brochures { get; set; }
        public int WhatsAppShareCount { get; set; }
        public int CallShareCount { get; set; }
        public int EmailShareCount { get; set; }
        public int SMSShareCount { get; set; }
        public string? AboutProperty { get; set; }
        public AddressDto? Address { get; set; }
        public double? MaintenanceCost { get; set; }
        public int MyProperty { get; set; }
        public PropertySource PropertySource { get; set; }
        public int UnitNo { get; set; }
        public List<string>? Links { get; set; }
        public List<string>? Projects { get; set; }
        public string? Project { get; set; }
        public string? SerialNo { get; set; }
        public List<Guid>? AssignedTo { get; set; }
        public bool? IsWaterMarkEnabled { get; set; }
        public bool? ShouldVisisbleOnListing { get; set; }
        public SecurityDeposit? SecurityDeposit { get; set; }
        public LockInPeriod? LockInPeriod { get; set; }
        public NoticePeriod? NoticePeriod { get; set; }
        public List<int>? NoOfFloorsOccupied { get; set; }
        public string? DLDPermitNumber { get; set; }
        public string? RefrenceNo { get; set; }
        public string? DTCMPermit { get; set; }
        public OfferingType? OfferingType { get; set; }
        public CompletionStatus? CompletionStatus { get; set; }
        public string? Language { get; set; }
        public string? TitleWithLanguage { get; set; }
        public string? AboutPropertyWithLanguage { get; set; }
        public List<string>? View360Url { get; set; }
        public List<Guid>? ListingOnBehalf { get; set; }
        public bool? IsListingOnBehalf { get; set; }
        public List<PropertyOwnerDetailsDto>? PropertyOwnerDetails { get; set; }
        public ComplianceDtoV2? Compliance { get; set; }
        public FinishingType? FinishingType { get; set; }
        public UaeEmirate? UaeEmirate { get; set; }
        public PossesionType? PossesionType { get; set; }
        public Dictionary<Guid, SourceReferenceInfoDto>? SourceReferenceIds { get; set; }
        public double? Age { get; set; }
    }

    public class CreateListingAddressDtoV2
    {
        public Guid SourceId { get; set; }
        public string? TowerName { get; set; }
        public string? SubCommunity { get; set; }
        public string? Community { get; set; }
        public string? City { get; set; }
        public string? LocationId { get; set; }
        public string? Longitude { get; set; }
        public string? Latitude { get; set; }
    }

    public class PFListingDtoV2
    {
        public List<Guid>? ListingIds { get; set; }
        public Guid? SourceId { get; set; }
    }

    public record ListingInputPayload(string TenantId, Guid CurrentUserId, object Entity);
}
