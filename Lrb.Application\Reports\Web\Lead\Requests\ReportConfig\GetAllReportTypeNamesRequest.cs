﻿using Amazon.Runtime.Internal.Transform;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Reports.Web.Lead.Requests.ReportConfig
{
    public class GetAllReportTypeNamesRequest : IRequest<Response<Dictionary<string, Dictionary<string, string>>>>
    {

    }
    public class GetAllReportTypeNamesRequestHandler : IRequestHandler<GetAllReportTypeNamesRequest, Response<Dictionary<string, Dictionary<string, string>>>>
    {

        public Task<Response<Dictionary<string, Dictionary<string, string>>>> Handle(GetAllReportTypeNamesRequest request, CancellationToken cancellationToken)
        {
            var reportTypes = new Dictionary<string, Dictionary<string, string>>
            {
                ["Lead"] = new Dictionary<string, string>
                {
                        {"Visit and Meeting","meetingandvisitreport"},
                        {"Status By Project","statusreportbyproject"},
                        {"Status By source","statusreportbysource"},
                        {"Status By User","statusreportbyuser"},
                        {"Status By Agency","statusreportbyagency"},
                        {"Status By Sub Source","statusreportbysubsource"},
                        {"User Activity" ,"useractivityreport"},
                        {"Sub Status By User","substatusreportbyuser"},
                        {"Sub Status By Subsource","substatusreportbysubsource"},
                        {"Project By Substatus" ,"projectreportbysubstatus"},
                        {"Call Log" , "calllogreportbyuser"},
                        {"Custom Status By Agency" ,"lead_statusreportbyagency"},
                        {"Custom Status By Project" ,"lead_statusreportbyproject"},
                        {"Custom Status By source" ,"lead_statusreportbysource"},
                        {"Custom Status By Sub Source" , "lead_statusreportbysubsource"},
                        {"Custom Status By User" ,"lead_statusreportbyuser"},
                        {"Received Date By Source","recieveddatebysource"},
                        {"User Vs Source" ,"uservssource"},
                        {"User Vs Sub Source" ,"uservssubsource"},
                        {"Revenue User Vs Source", "revenueuservssource"},
                        {"Revenue User Vs SubSource", "revenueuservssubsource" },
                        {"Status By Campagin", "leadcampaignreportbysubstatus"},
                        {"Status By ChannelPartner","leadchannelpartnerreportbysubstatus" }
                },
                ["Data"] = new Dictionary<string, string>
                {
                     {"Data Status By User" ,"datastatusreportbyuser"},
                     {"Data Status By Sub Source", "datasubsourcereportbystatus"},
                     {"Data Status By Source", "datasourcereportbystatus"},
                     {"Data Status By Project" , "dataprojectreportbystatus"},
                     {"Data Call Logs" ,"exportdatacalllogreportbyuser"},
                     {"User Data Activity" ,"userdataactivityreport"},
                     {"Data Status By Agency" , "dataagencyreportbystatus"},
                     {"Data Status By Campagin", "datacampaignreportbystatus"},
                     {"Data Status By ChannelPartner","datachannelpartnerreportbystatus" }
                }
            };
            return Task.FromResult(new Response<Dictionary<string, Dictionary<string, string>>>(reportTypes));
        }
    }
}
