﻿using Lrb.Application.Lead.Web.Mappings;
using Lrb.Application.Lead.Web.Requests;
using Lrb.Domain.Entities;

namespace Lrb.Application.Lead.Web
{
    public class UpdateLeadTagRequest : IRequest<Response<bool>>
    {
        public Guid Id { get; set; }
        public LeadTagEnum? LeadTags { get; set; }
    }
    public class UpdateLeadTagRequestHandler : LeadCommonRequestHandler, IRequestHandler<UpdateLeadTagRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<LeadHistoryHot> _newLeadHistoryRepo;
        public UpdateLeadTagRequestHandler(IServiceProvider serviceProvider, IRepositoryWithEvents<LeadHistoryHot> newLeadHistoryRepo) : base(serviceProvider, typeof(UpdateLeadTagRequestHandler).Name, "Handle")
        {
            _newLeadHistoryRepo = newLeadHistoryRepo;
        }

        public async Task<Response<bool>> Handle(UpdateLeadTagRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var fullLead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(request.Id), cancellationToken) ?? throw new NotFoundException("No lead found by the id.");
                var oldLeadDto = fullLead.Adapt<ViewLeadDto>();
                var originalLead = fullLead.CreateDeepCopy();

                var message = await UpdateLeadTagsByEnumAsync(fullLead, request.LeadTags, cancellationToken);

                try
                {
                    fullLead.ShouldUpdatePickedDate = await ShouldUpdatePickedDate(originalLead, fullLead.Adapt<PickedLeadDto>());

                }
                catch (Exception ex)
                {
                    throw;
                }

                await _leadRepo.UpdateAsync(fullLead, cancellationToken);

                await UpdateLeadHistoryAsync(fullLead, cancellationToken: cancellationToken);

                #region Create New Lead Histories
                fullLead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(fullLead.Id), cancellationToken) ?? throw new NotFoundException("No lead found by the id.");
                var leadDto = fullLead.Adapt<ViewLeadDto>();
                var currentUserId = _currentUserRepo.GetUserId();
                leadDto.LastModifiedBy = currentUserId;
                await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken, currentUserId);
                await oldLeadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken, currentUserId);
                var leadStatues = await _customLeadStatusRepo.ListAsync(cancellationToken);
                var masterPropertyTypes = await _propertyTypeRepo.ListAsync(cancellationToken);
                var latestModificationVersion = await _dapperRepository.GetLatestLeadHistoryVersionAsync(leadDto.Id);
                var histories = await LeadHistoryHelperV2.V2UpdateLeadHistoryForVM(leadDto, oldLeadDto, latestModificationVersion ?? 1, leadStatues, masterPropertyTypes, _userService, currentUserId, cancellationToken);
                await _newLeadHistoryRepo.AddRangeAsync(histories);
                #endregion

                await SendOnlyLeadInfoUpdateNotificationAsync(fullLead, cancellationToken);

                return new(true, message);
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{typeof(UpdateLeadTagRequestHandler).Name} - Handle()");
                throw;
            }
        }
    }
}
