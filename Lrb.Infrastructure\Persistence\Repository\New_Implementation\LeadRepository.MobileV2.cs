﻿using LinqKit;
using Lrb.Application.Lead.Mobile;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using Lrb.Infrastructure.Persistence.Context;
using Lrb.Shared.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Serilog;
using System.Linq.Expressions;

namespace Lrb.Infrastructure.Persistence.Repository.New_Implementation
{
    public partial class LeadRepository
    {
        public async Task<IEnumerable<Domain.Entities.Lead>> GetAllCategoryLeadsForMobileV2Async(GetAllLeadsRequest request, Guid userId, IList<Guid> subIds, LeadFilterTypeMobile category, List<Guid>? leadHistoryIds, List<Guid>? scheduledMeetingLeadHistoryIds, List<Guid>? scheduledVisitLeadHistoryIds, List<CustomMasterLeadStatus> custumStatuses, bool? isAdmin = null)
        {

            try
            {

                // STEP 1: BLAZING FAST - Get filtered lead IDs with minimal projection (50-100ms)
                var query = BuildQueryV2(request, userId, subIds, category, leadHistoryIds, scheduledMeetingLeadHistoryIds, scheduledVisitLeadHistoryIds, custumStatuses);

                // Apply sorting for maximum performance
                List<LeadFilterTypeMobile> scheduledFilterTypes = new()
                    {
                        LeadFilterTypeMobile.ScheduledMeeting, LeadFilterTypeMobile.ScheduleToday, LeadFilterTypeMobile.Overdue, LeadFilterTypeMobile.SiteVisitScheduled, LeadFilterTypeMobile.ScheduledTomorrow, LeadFilterTypeMobile.UpcomingSchedules, LeadFilterTypeMobile.CallBack
                    };
                if (scheduledFilterTypes.Contains(category))
                {
                    query = query.OrderBy(i => i.ScheduledDate - new DateTime(2000, 01, 01, 00, 00, 00, DateTimeKind.Utc));
                    //query = query.OrderBy(i => (i.ScheduledDate ?? DateTime.MinValue)).ThenBy(i => i.Name);
                }
                else
                {
                    query = query.OrderByDescending(i => i.LastModifiedOn - new DateTime(2000, 01, 01, 00, 00, 00, DateTimeKind.Utc));
                    //query = query.OrderByDescending(i => (i.LastModifiedOn ?? DateTime.MaxValue)).ThenBy(i => i.Name);
                }

                     var  leads = await query
                    .Skip(request.PageSize * (request.PageNumber - 1))
                    .Take(request.PageSize)
                     .Select(l => new Lead
                     {
                         Id = l.Id,
                         Name = l.Name ?? string.Empty,
                         ContactNo = l.ContactNo ?? string.Empty,
                         AlternateContactNo = l.AlternateContactNo,
                         IsPicked = l.IsPicked,
                         AssignTo = l.AssignTo,
                         SecondaryUserId = l.SecondaryUserId,
                         Notes = l.Notes,
                         ScheduledDate = l.ScheduledDate,
                         ContactRecords = l.ContactRecords,
                         IsMeetingDone = l.IsMeetingDone,
                         MeetingLocation = l.MeetingLocation,
                         IsSiteVisitDone = l.IsSiteVisitDone,
                         SiteLocation = l.SiteLocation,
                         AppointmentDoneOn = l.AppointmentDoneOn,
                         CreatedBy = l.CreatedBy,
                         CreatedOn = l.CreatedOn,
                         LastModifiedBy = l.LastModifiedBy,
                         LastModifiedOn = l.LastModifiedOn,
                         IsConvertedFromData = l.IsConvertedFromData,
                         BookedDate = l.BookedDate,
                         BookedBy = l.BookedBy,
                         CustomLeadStatus = l.CustomLeadStatus != null ? new CustomMasterLeadStatus
                         {
                             Id = l.CustomLeadStatus.Id,
                             BaseId = l.CustomLeadStatus.BaseId,
                          
                         } : null,
                         Appointments = l.Appointments != null ? l.Appointments.Select(a => new LeadAppointment
                         {
                             Id = a.Id,
                             LeadId = a.LeadId,
                             Type = a.Type,
                             IsDone = a.IsDone,
                         }).ToList() : null,
                         Enquiries = l.Enquiries != null ? l.Enquiries.Select(e => new LeadEnquiry
                         {
                             Id = e.Id,
                             LeadId = e.LeadId,
                             EnquiredFor = e.EnquiredFor,
                             LeadSource = e.LeadSource,
                             LowerBudget = e.LowerBudget,
                             UpperBudget = e.UpperBudget,
                             NoOfBHKs = e.NoOfBHKs,
                             EnquiryTypes = e.EnquiryTypes,
                             BHKs = e.BHKs,
                         })
                .ToList() : null,
                        
                     })
                    .ToListAsync();
                return leads;
            }
            catch (Exception ex)
            {
                throw;
            }
            finally
            {
            }
        }

        private IQueryable<Lead> BuildQueryV2(GetAllLeadsRequest request, Guid userId, IList<Guid> subIds, LeadFilterTypeMobile category, List<Guid>? leadHistoryIds, List<Guid>? scheduledMeetingLeadHistoryIds, List<Guid>? scheduledVisitLeadHistoryIds = null, List<CustomMasterLeadStatus> customStatuses = null, bool? isAdmin = null)
        {
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            var reporteeIds = subIds.Where(i => i != userId && i != Guid.Empty);
            var selfWithReporteeIds = subIds.Where(i => i != Guid.Empty);
            var query = context.Leads.Where(i => !i.IsDeleted)
                 .AsQueryable();

            if (request.CustomFlags?.Any() ?? false)
            {
                query = query.Where(i => i.CustomFlags != null && i.CustomFlags.Any(j => j.Flag != null && request.CustomFlags.Contains(j.Flag.Name ?? string.Empty)));
            }
            if (request.DataConverted != null)
            {
                if (request.DataConverted == true)
                {
                    query = query.Where(i => i.IsConvertedFromData == true);
                }
                else
                {
                    query = query.Where(i => i.IsConvertedFromData != true);
                }
            }
            if (request.QualifiedByIds?.Any() ?? false)
            {
                query = query.Where(i => i.QualifiedBy.HasValue && request.QualifiedByIds.Contains(i.QualifiedBy.Value));
            }
            if (request.SubStatuses?.Any() ?? false)
            {
                query = query.Where(i => i.CustomLeadStatus != null && request.SubStatuses.Any(j => j == i.CustomLeadStatus.Id));
            }

            if (request.IsPicked != null && request.IsPicked != default)
            {
                query = query.Where(i => i.IsPicked == request.IsPicked.Value);
            }

            if (!string.IsNullOrWhiteSpace(request.ConfidentialNotes))
            {
                request.ConfidentialNotes = request.ConfidentialNotes.ToLower().Trim();
                query = query.Where(i => i.ConfidentialNotes.ToLower().Trim().Contains(request.ConfidentialNotes));
            }

            if (request.CarpetArea != null && request.CarpetAreaUnitId != default)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.CarpetArea == request.CarpetArea && i.CarpetAreaUnitId == request.CarpetAreaUnitId));
            }
            else if (request.CarpetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.CarpetArea == request.CarpetArea || i.CarpetAreaInSqMtr == request.CarpetArea));
            }
            if (request.BuiltUpArea != null && request.BuiltUpAreaUnitId != default && request.BuiltUpAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.BuiltUpArea == request.BuiltUpArea && i.BuiltUpAreaUnitId == request.BuiltUpAreaUnitId));
            }
            else if (request.BuiltUpArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.BuiltUpArea == request.BuiltUpArea || i.BuiltUpAreaInSqMtr == request.BuiltUpArea));
            }
            if (request.SaleableArea != null && request.SaleableAreaUnitId != default && request.SaleableAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.SaleableArea == request.SaleableArea && i.SaleableAreaUnitId == request.SaleableAreaUnitId));
            }
            else if (request.SaleableArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.SaleableArea == request.SaleableArea || i.SaleableAreaInSqMtr == request.SaleableArea));
            }

            if (request.NetArea != null && request.NetAreaUnitId != default && request.NetAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.NetArea == request.NetArea && i.NetAreaUnitId == request.NetAreaUnitId));
            }
            else if (request.NetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.NetArea == request.NetArea || i.NetAreaInSqMtr == request.NetArea));
            }

            if (request.PropertyArea != null && request.PropertyAreaUnitId != default && request.PropertyAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.PropertyArea == request.PropertyArea && i.PropertyAreaUnitId == request.PropertyAreaUnitId));
            }
            else if (request.PropertyArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.PropertyArea == request.PropertyArea || i.PropertyAreaInSqMtr == request.PropertyArea));
            }
            if ((request.MinCarpetArea != null || request.MaxCarpetArea != null) && request.CarpetAreaUnitId != default)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.CarpetAreaUnitId == request.CarpetAreaUnitId &&
                    (request.MinCarpetArea == null || e.CarpetArea >= request.MinCarpetArea) &&
                    (request.MaxCarpetArea == null || e.CarpetArea <= request.MaxCarpetArea)));
            }
            else if (request.MinCarpetArea != null || request.MaxCarpetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinCarpetArea == null || e.CarpetArea >= request.MinCarpetArea) &&
                    (request.MaxCarpetArea == null || e.CarpetArea <= request.MaxCarpetArea)));
            }
            if ((request.MinBuiltUpArea != null || request.MaxBuiltUpArea != null) && request.BuiltUpAreaUnitId != default && request.BuiltUpAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.BuiltUpAreaUnitId == request.BuiltUpAreaUnitId &&
                    (request.MinBuiltUpArea == null || e.BuiltUpArea >= request.MinBuiltUpArea) &&
                    (request.MaxBuiltUpArea == null || e.BuiltUpArea <= request.MaxBuiltUpArea)));
            }
            else if (request.MinBuiltUpArea != null || request.MaxBuiltUpArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinBuiltUpArea == null || e.BuiltUpArea >= request.MinBuiltUpArea) &&
                    (request.MaxBuiltUpArea == null || e.BuiltUpArea <= request.MaxBuiltUpArea)));
            }
            if ((request.MinSaleableArea != null || request.MaxSaleableArea != null) && request.SaleableAreaUnitId != default && request.SaleableAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.SaleableAreaUnitId == request.SaleableAreaUnitId &&
                    (request.MinSaleableArea == null || e.SaleableArea >= request.MinSaleableArea) &&
                    (request.MaxSaleableArea == null || e.SaleableArea <= request.MaxSaleableArea)));
            }
            else if (request.MinSaleableArea != null || request.MaxSaleableArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinSaleableArea == null || e.SaleableArea >= request.MinSaleableArea) &&
                    (request.MaxSaleableArea == null || e.SaleableArea <= request.MaxSaleableArea)));
            }
            if ((request.MinPropertyArea != null || request.MaxPropertyArea != null) && request.PropertyAreaUnitId != default && request.PropertyAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.PropertyAreaUnitId == request.PropertyAreaUnitId &&
                    (request.MinPropertyArea == null || e.PropertyArea >= request.MinPropertyArea) &&
                    (request.MaxPropertyArea == null || e.PropertyArea <= request.MaxPropertyArea)));
            }
            else if (request.MinPropertyArea != null || request.MaxPropertyArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinPropertyArea == null || e.PropertyArea >= request.MinPropertyArea) &&
                    (request.MaxPropertyArea == null || e.PropertyArea <= request.MaxPropertyArea)));
            }

            if ((request.MinNetArea != null || request.MaxNetArea != null) && request.NetAreaUnitId != default && request.NetAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.NetAreaUnitId == request.NetAreaUnitId &&
                    (request.MinNetArea == null || e.NetArea >= request.MinNetArea) &&
                    (request.MaxNetArea == null || e.NetArea <= request.MaxNetArea)));
            }
            else if (request.MinNetArea != null || request.MaxNetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinNetArea == null || e.NetArea >= request.MinNetArea) &&
                    (request.MaxNetArea == null || e.NetArea <= request.MaxNetArea)));
            }
            if (request.FromMinBudget != null || request.ToMinBudget != null || request.FromMaxBudget != null || request.ToMaxBudget != null)
            {
                if (request.FromMinBudget != null && request.ToMinBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        (e.LowerBudget >= request.FromMinBudget && e.LowerBudget <= request.ToMinBudget) ||
                        (e.LowerBudget >= request.FromMinBudget && e.LowerBudget <= request.ToMinBudget)
                    ));
                }
                else if (request.FromMinBudget != null && request.ToMinBudget == null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        e.LowerBudget >= request.FromMinBudget ||
                        e.LowerBudget >= request.FromMinBudget
                    ));
                }
                else if (request.FromMinBudget == null && request.ToMinBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        e.LowerBudget <= request.ToMinBudget ||
                        e.LowerBudget <= request.ToMinBudget
                    ));
                }

                if (request.FromMaxBudget != null && request.ToMaxBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        (e.UpperBudget >= request.FromMaxBudget && e.UpperBudget <= request.ToMaxBudget) ||
                        (e.UpperBudget >= request.FromMaxBudget && e.UpperBudget <= request.ToMaxBudget)
                    ));
                }
                else if (request.FromMaxBudget != null && request.ToMaxBudget == null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        e.UpperBudget >= request.FromMaxBudget ||
                        e.UpperBudget >= request.FromMaxBudget
                    ));
                }
                else if (request.FromMaxBudget == null && request.ToMaxBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        e.UpperBudget <= request.ToMaxBudget ||
                        e.UpperBudget <= request.ToMaxBudget
                    ));
                }
            }
            if (!string.IsNullOrEmpty(request.UnitName))
            {
                query = query.Where(i => i.Enquiries.Any(e => e.UnitName != null && e.UnitName.ToLower().Trim() == request.UnitName.ToLower().Trim()));

            }
            if (request?.UnitNames?.Any() ?? false)
            {
                var unitnames = request.UnitNames.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Enquiries.Count > 0 && i.Enquiries.Any(i => unitnames.Contains(i.UnitName.ToLower()))).AsQueryable();
            }
            if (request?.Nationality?.Any() ?? false)
            {
                var nationality = request.Nationality.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Nationality != null && nationality.Contains(i.Nationality.ToLower())).AsQueryable();
            }

            if (request?.ClusterName?.Any() ?? false)
            {
                var ClusterName = request.ClusterName.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Enquiries.Count > 0 && i.Enquiries.Any(i => ClusterName.Contains(i.ClusterName.ToLower()))).AsQueryable();
            }
            if (!string.IsNullOrEmpty(request.ReferralName))
            {
                query = query.Where(i => (i.ReferralName.ToLower().Trim().Contains(request.ReferralName.ToLower().Trim())));
            }
            if (!string.IsNullOrEmpty(request.ReferralContactNo))
            {
                query = query.Where(i => !string.IsNullOrEmpty(i.ReferralContactNo) && request.ReferralContactNo.Contains(i.ReferralContactNo.Substring(i.ReferralContactNo.Length - 10)));
            }
            if (!string.IsNullOrEmpty(request.ReferralEmail))
            {
                query = query.Where(i => (i.ReferralEmail.ToLower().Trim().Contains(request.ReferralEmail.ToLower().Trim())));
            }

            if (request?.ShouldShowBookedDetails ?? false)
            {
                var customInvoiceStatus = customStatuses?.FirstOrDefault(i => i.Status == "invoiced") ?? new();
                query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.BaseId == customInvoiceStatus.Id || i.CustomLeadStatus.Id == customInvoiceStatus.Id));
                query = query.Where(i => ((i.BookedDetails != null) && (i.BookedDetails.OrderByDescending(i => i.LastModifiedOn).FirstOrDefault().IsDeleted == false)));
                query = query.OrderByDescending(i => i.BookedDetails.FirstOrDefault().LastModifiedOn);

            }
            if (request?.TeamHead != null)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().TeamHead == request.TeamHead);
            }
            if (request?.UpperAgreementLimit > 0)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().AgreementValue <= request.UpperAgreementLimit);
            }
            if (request?.LowerAgreementLimit > 0)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().AgreementValue >= request.LowerAgreementLimit);
            }
            if (request?.PaymentMode != null && request?.PaymentMode != TokenType.None)
            {
                switch (request.PaymentMode)
                {
                    case TokenType.Cheque:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().PaymentMode == TokenType.Cheque);
                        break;
                    case TokenType.Cash:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().PaymentMode == TokenType.Cash);
                        break;
                    case TokenType.NEFT:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().PaymentMode == TokenType.NEFT);
                        break;
                    case TokenType.RTGS:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().PaymentMode == TokenType.RTGS);
                        break;
                    case TokenType.UPI:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().PaymentMode == TokenType.UPI);
                        break;
                    case TokenType.DD:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().PaymentMode == TokenType.DD);
                        break;
                    case TokenType.IMPS:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().PaymentMode == TokenType.IMPS);
                        break;
                }
            }
            if (request?.UpperDiscountLimit > 0)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().Discount <= request.UpperDiscountLimit);
            }
            if (request?.LowerDiscountLimit > 0)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().Discount >= request.UpperDiscountLimit);
            }
            if (request.DiscountMode != null && request?.DiscountMode != DiscountType.None)
            {
                switch (request.DiscountMode)
                {
                    case DiscountType.DirectAdjustment:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().DiscountMode == DiscountType.DirectAdjustment);
                        break;
                    case DiscountType.Cashback:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().DiscountMode == DiscountType.Cashback);
                        break;
                }
            }
            if (request?.LeadBrokerageInfoId != null)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().LeadBrokerageInfoId == request.LeadBrokerageInfoId);
            }
            if (!string.IsNullOrEmpty(request.BookedUnderName))
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().BookedUnderName.ToLower() == request.BookedUnderName.ToLower());
            }
            if (!string.IsNullOrEmpty(request.DiscountUnit))
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().DiscountUnit == request.DiscountUnit);
            }
            if (request?.TotalBrokerage != null)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().BrokerageInfo.TotalBrokerage == request.TotalBrokerage);

            }
            if (request?.SoldPrice != null)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().BrokerageInfo.SoldPrice == request.SoldPrice);
            }
            if (request?.BrokerageCharges != null)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().BrokerageInfo.BrokerageCharges == request.BrokerageCharges);
            }
            if (request?.NetBrokerageAmount != null)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().BrokerageInfo.NetBrokerageAmount == request.NetBrokerageAmount);
            }
            if (request?.GST != null)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().BrokerageInfo.GST == request.GST);
            }
            if (request?.Commission != null)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().BrokerageInfo.Commission == request.Commission);
            }
            if (request?.EarnedBrokerage != null)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().BrokerageInfo.EarnedBrokerage == request.EarnedBrokerage);
            }
            if (!string.IsNullOrEmpty(request.CommissionUnit))
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().BrokerageInfo.CommissionUnit == request.CommissionUnit);
            }
            if (!string.IsNullOrEmpty(request.BrokerageUnit))
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().BrokerageInfo.BrokerageUnit == request.BrokerageUnit);
            }
            if (!string.IsNullOrEmpty(request.GSTUnit))
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().BrokerageInfo.GSTUnit == request.GSTUnit);
            }
            if (request.BrokerageType != null && request?.BrokerageType != BrokerageType.None)
            {
                switch (request.BrokerageType)
                {
                    case BrokerageType.AgreementValue:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().BrokerageInfo.BrokerageType == BrokerageType.AgreementValue);
                        break;
                    case BrokerageType.SoldPrice:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().BrokerageInfo.BrokerageType == BrokerageType.AgreementValue);
                        break;
                }
            }
            if (!string.IsNullOrEmpty(request.ReferralNumber))
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().BrokerageInfo.ReferralNumber == request.ReferralNumber);
            }
            if (request?.ReferredBy != null)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().BrokerageInfo.ReferredBy == request.ReferredBy);
            }

            if (request.BookedDate.HasValue && request.BookedDate.HasValue != default)
            {
                query = query.Where(i => i.BookedDate == request.BookedDate);
            }
            if (request?.UpperRemainingAmountLimit > 0)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().RemainingAmount <= request.UpperRemainingAmountLimit);
            }
            if (request?.LowerRemainingAmountLimit > 0)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().RemainingAmount >= request.LowerRemainingAmountLimit);
            }
            if (request.PaymentType != null && request?.PaymentType != PaymentType.None)
            {
                switch (request.PaymentType)
                {
                    case PaymentType.Cheque:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().PaymentType == PaymentType.Cheque);
                        break;
                    case PaymentType.Cash:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().PaymentType == PaymentType.Cash);
                        break;
                    case PaymentType.OnlineTransfer:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().PaymentType == PaymentType.OnlineTransfer);
                        break;
                    case PaymentType.BankLoan:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().PaymentType == PaymentType.BankLoan);
                        break;
                    case PaymentType.PartialLoanCash:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().PaymentType == PaymentType.PartialLoanCash);
                        break;
                    case PaymentType.DD:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().PaymentType == PaymentType.DD);
                        break;
                    case PaymentType.PendingLoanApproval:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().PaymentType == PaymentType.PendingLoanApproval);
                        break;
                    case PaymentType.LoanApplied:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().PaymentType == PaymentType.LoanApplied);
                        break;
                }
            }
            if (request?.CarParkingCharges != null)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().CarParkingCharges == request.CarParkingCharges);
            }
            if (request?.AdditionalCharges != null)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().AdditionalCharges == request.AdditionalCharges);
            }
            if (request?.RemainingAmount != null)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().RemainingAmount == request.RemainingAmount);
            }
            if (request?.MeetingOrVisitStatuses?.Any() ?? false)
            {
                DateTime? fromDateForMeetingOrVisit = request.FromDateForMeetingOrVisit != null ? request.FromDateForMeetingOrVisit.Value.ConvertFromDateToUtc() : null;
                DateTime? toDateForMeetingOrVisit = request.ToDateForMeetingOrVisit != null ? request.ToDateForMeetingOrVisit.Value.ConvertToDateToUtc() : null;
                (List<AppointmentType> appTypes, List<bool> appDoneStatuses) = GetAppointmentTypes(request);
                if (request.AppointmentDoneByUserIds?.Any() ?? false)
                {
                    query = query.Where(i => i.Appointments.Any(app => request.AppointmentDoneByUserIds.Contains(app.CreatedBy) && app.Type != AppointmentType.None && appTypes.Contains(app.Type) && appDoneStatuses.Contains(app.IsDone)));
                }
                else if ((isAdmin != null) && !isAdmin.Value && (!request.AppointmentDoneByUserIds?.Any() ?? true))
                {
                    query = query.Where(i => i.Appointments.Any(j => subIds != null && subIds.Contains(j.UserId) && j.Type != AppointmentType.None));
                }
                if (fromDateForMeetingOrVisit != null && toDateForMeetingOrVisit != null)
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                         && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDateForMeetingOrVisit && i.CreatedOn < toDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDateForMeetingOrVisit && i.CreatedOn < toDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDateForMeetingOrVisit && i.CreatedOn < toDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDateForMeetingOrVisit && i.CreatedOn < toDateForMeetingOrVisit)));
                }
                else if (fromDateForMeetingOrVisit != null)
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                         && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDateForMeetingOrVisit)));
                }
                else if (toDateForMeetingOrVisit != null)
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                         && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn < toDateForMeetingOrVisit.Value))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn < toDateForMeetingOrVisit.Value))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn < toDateForMeetingOrVisit.Value))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn < toDateForMeetingOrVisit.Value)));
                }
                else if ((request.AssignToIds?.Any() ?? false) && (request.OwnerSelection == OwnerSelectionType.Both) && (isAdmin ?? false))
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && (subIds.Contains(i.AssignTo) || request.AssignToIds.Contains(i.SecondaryUserId.Value))))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && !j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && (subIds.Contains(i.AssignTo) || request.AssignToIds.Contains(i.SecondaryUserId.Value))))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && (subIds.Contains(i.AssignTo) || request.AssignToIds.Contains(i.SecondaryUserId.Value))))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && !j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && (subIds.Contains(i.AssignTo) || request.AssignToIds.Contains(i.SecondaryUserId.Value)))));

                }
                else if ((request.OwnerSelection == OwnerSelectionType.SecondaryOwner) && (request.AssignToIds?.Any() ?? false) && (isAdmin ?? false))
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && j.IsDone && (j.UserId != Guid.Empty) && request.AssignToIds.Contains(i.SecondaryUserId.Value)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && !j.IsDone && (j.UserId != Guid.Empty) && request.AssignToIds.Contains(i.SecondaryUserId.Value)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && j.IsDone && (j.UserId != Guid.Empty) && request.AssignToIds.Contains(i.SecondaryUserId.Value)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && !j.IsDone && (j.UserId != Guid.Empty) && request.AssignToIds.Contains(i.SecondaryUserId.Value))));

                }
                else if ((request.AssignTo != null) && (isAdmin ?? false))
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.AssignTo)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && !j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.AssignTo)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.AssignTo)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && !j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.AssignTo))));

                }
                else
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                                && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                                && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                                && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                                && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId))));
                }
            }
            if ((request?.IsDualOwnershipEnabled ?? false) && (request?.AssignToIds?.Any() ?? false) && (request.OwnerSelection == OwnerSelectionType.Both))
            {
                switch (request.LeadVisibility)
                {
                    case BaseLeadVisibility.SelfWithReportee:
                        query = query.Where(i => (selfWithReporteeIds.Contains(i.AssignTo) || selfWithReporteeIds.Contains(i.SecondaryUserId ?? Guid.Empty))).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Self:
                        query = query.Where(i => (i.AssignTo == userId || i.SecondaryUserId == userId)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Reportee:
                        query = query.Where(i => (reporteeIds.Contains(i.AssignTo) || reporteeIds.Contains(i.SecondaryUserId ?? Guid.Empty))).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.UnassignLead:
                        query = query.Where(i => i.AssignTo == Guid.Empty).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.DeletedLeads:
                        query = query.Where(i => i.IsArchived && selfWithReporteeIds.Contains(i.AssignTo));
                        break;
                    case BaseLeadVisibility.ReEnquired:
                        query = query.Where(i => i.IsReEnquired == true && selfWithReporteeIds.Contains(i.AssignTo));
                        break;
                    default:
                        break;
                }
            }
            else if ((request?.IsDualOwnershipEnabled ?? false) && (request?.AssignToIds?.Any() ?? false) && (request.OwnerSelection == OwnerSelectionType.SecondaryOwner))
            {
                switch (request.LeadVisibility)
                {
                    case BaseLeadVisibility.SelfWithReportee:
                        query = query.Where(i => selfWithReporteeIds.Contains(i.SecondaryUserId.Value)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Self:
                        query = query.Where(i => i.SecondaryUserId == userId).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Reportee:
                        query = query.Where(i => reporteeIds.Contains(i.SecondaryUserId.Value)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.UnassignLead:
                        query = query.Where(i => i.AssignTo == Guid.Empty).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.DeletedLeads:
                        query = query.Where(i => i.IsArchived && selfWithReporteeIds.Contains(i.SecondaryUserId.Value));
                        break;
                    case BaseLeadVisibility.ReEnquired:
                        query = query.Where(i => i.IsReEnquired == true && selfWithReporteeIds.Contains(i.AssignTo));
                        break;
                    default:
                        break;
                }
            }
            else if ((request?.IsDualOwnershipEnabled ?? false) && (!request?.AssignToIds?.Any() ?? true))
            {
                switch (request.LeadVisibility)
                {
                    case BaseLeadVisibility.SelfWithReportee:
                        query = query.Where(i => (selfWithReporteeIds.Contains(i.AssignTo) || selfWithReporteeIds.Contains(i.SecondaryUserId ?? Guid.Empty))).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Self:
                        query = query.Where(i => (i.AssignTo == userId || i.SecondaryUserId == userId)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Reportee:
                        query = query.Where(i => (reporteeIds.Contains(i.AssignTo) || reporteeIds.Contains(i.SecondaryUserId ?? Guid.Empty))).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.UnassignLead:
                        query = query.Where(i => i.AssignTo == Guid.Empty).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.DeletedLeads:
                        query = query.Where(i => i.IsArchived && selfWithReporteeIds.Contains(i.AssignTo));
                        break;
                    case BaseLeadVisibility.ReEnquired:
                        query = query.Where(i => i.IsReEnquired == true && selfWithReporteeIds.Contains(i.AssignTo));
                        break;
                    default:
                        break;
                }
            }
            else
            {
                switch (request.LeadVisibility)
                {
                    case BaseLeadVisibility.SelfWithReportee:
                        query = query.Where(i => selfWithReporteeIds.Contains(i.AssignTo)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Self:
                        query = query.Where(i => i.AssignTo == userId).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Reportee:
                        query = query.Where(i => reporteeIds.Contains(i.AssignTo)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.UnassignLead:
                        query = query.Where(i => i.AssignTo == Guid.Empty).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.DeletedLeads:
                        query = query.Where(i => i.IsArchived && selfWithReporteeIds.Contains(i.AssignTo));
                        break;
                    case BaseLeadVisibility.ReEnquired:
                        query = query.Where(i => i.IsReEnquired == true && selfWithReporteeIds.Contains(i.AssignTo));
                        break;
                    default:
                        break;
                }
            }
            if (request.EnquiredFor != null && request.EnquiredFor.Any())
            {
                //query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.EnquiredFor.Contains(i.EnquiredFor)));
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.EnquiryTypes != null && i.EnquiryTypes.Any(j => request.EnquiredFor.Contains(j))));
            }

            if (request.DateType.HasValue && ((request.FromDate.HasValue && request.FromDate.Value != default) || (request.ToDate.HasValue && request.ToDate.Value != default)))
            {
                DateTime? fromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : DateTime.MinValue;
                DateTime? toDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : DateTime.MaxValue;
                switch (request.DateType)
                {
                    case DateType.ReceivedDate:
                        query = query.Where(i => i.CreatedOn >= fromDate.Value && i.CreatedOn <= toDate.Value);
                        break;
                    case DateType.ScheduledDate:
                        query = query.Where(i => i.ScheduledDate != null && i.ScheduledDate.Value >= fromDate.Value && i.ScheduledDate.Value <= toDate.Value);
                        break;
                    case DateType.ModifiedDate:
                        query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= fromDate.Value && i.LastModifiedOn.Value <= toDate.Value);
                        break;
                    case DateType.DeletedDate:
                        query = query.Where(i => i.ArchivedOn >= fromDate.Value && i.ArchivedOn < toDate.Value);
                        break;
                    case DateType.PickedDate:
                        query = query.Where(i => i.PickedDate >= fromDate.Value && i.PickedDate <= toDate.Value);
                        break;
                    case DateType.BookedDate:
                        query = query.Where(item => item.BookedDetails.Any(detail => detail.BookedDate.Value >= fromDate.Value && detail.BookedDate.Value <= toDate.Value));
                        break;
                    case DateType.AssignedDate:
                        query = query.Where(i => i.Assignments != null && i.Assignments.Any(i => i.AssignmentDate != null && i.AssignmentDate.Value >= fromDate.Value && i.AssignmentDate.Value <= toDate.Value));
                        break;
                    case DateType.PossessionDate:
                        query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(enquiry => enquiry.PossessionDate.Value >= request.FromDate.Value && enquiry.PossessionDate.Value <= request.ToDate.Value));
                        break;
                    default:
                        break;
                }
            }
            if (!string.IsNullOrEmpty(request?.DatesJsonFormattedString ?? string.Empty))
            {
                try
                {
                    var data = JsonConvert.DeserializeObject<List<Lrb.Application.Lead.Mobile.v2.Date>>(request?.DatesJsonFormattedString ?? string.Empty);
                    if (data?.Any() ?? false)
                    {
                        request.Dates = data;
                    }
                    else
                    {
                        request.Dates = null;
                    }
                }
                catch (Exception ex)
                {
                    request.Dates = null;
                    Console.WriteLine("Exception details while deserializing the date object " + ex.Serialize());
                }
            }
            if (request.Dates?.Any() ?? false)
            {
                foreach (var date in request.Dates)
                {
                    if (date.MultiDateType.HasValue && ((date.MultiFromDate.HasValue && date.MultiFromDate.Value != default) || (date.MultiToDate.HasValue && date.MultiToDate.Value != default)))
                    {
                        date.MultiFromDate = date.MultiFromDate.HasValue ? date.MultiFromDate.Value.ConvertFromDateToUtc() : DateTime.MinValue;
                        date.MultiToDate = date.MultiToDate.HasValue ? date.MultiToDate.Value.ConvertToDateToUtc() : DateTime.MaxValue;

                        switch (date.MultiDateType)
                        {
                            case DateType.ReceivedDate:
                                query = query.Where(i => i.CreatedOn >= date.MultiFromDate.Value && i.CreatedOn <= date.MultiToDate.Value);
                                break;
                            case DateType.ScheduledDate:
                                query = query.Where(i => i.ScheduledDate != null && i.ScheduledDate.Value >= date.MultiFromDate.Value && i.ScheduledDate.Value <= date.MultiToDate.Value);
                                break;
                            case DateType.ModifiedDate:
                                query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= date.MultiFromDate.Value && i.LastModifiedOn.Value <= date.MultiToDate.Value);
                                break;
                            case DateType.DeletedDate:
                                query = query.Where(i => i.ArchivedOn >= date.MultiFromDate.Value && i.ArchivedOn < date.MultiToDate.Value);
                                break;
                            case DateType.PickedDate:
                                query = query.Where(i => i.PickedDate >= date.MultiFromDate.Value && i.PickedDate <= date.MultiToDate.Value);
                                break;
                            case DateType.AssignedDate:
                                query = query.Where(i => i.Assignments != null && i.Assignments.Any(i => i.AssignmentDate != null && i.AssignmentDate.Value >= date.MultiFromDate.Value && i.AssignmentDate.Value <= date.MultiToDate.Value));
                                break;
                            default:
                                break;
                        }
                    }
                }
            }
            /*
                        if (!string.IsNullOrWhiteSpace(request.SearchByNameOrNumber))
                        {
                            query = query.Where(i => i.ContactNo.Contains(request.SearchByNameOrNumber.Replace(" ", "")) ||
                                                     i.Name.ToLower().Contains(request.SearchByNameOrNumber.ToLower()) ||
                                                     i.Email.ToLower().Contains(request.SearchByNameOrNumber.ToLower()) ||
                                                     i.AlternateContactNo.Contains(request.SearchByNameOrNumber.Replace(" ", "")) ||
                                                     i.SerialNumber.Contains(request.SearchByNameOrNumber.Replace(" ", "")));
                        }*/
            if (!string.IsNullOrWhiteSpace(request.SearchByNameOrNumber) && request.PropertyToSearch?.Any() == true)
            {
                var searchTerm = request.SearchByNameOrNumber.ToLower().Trim().Replace(" ", "");
                var isPurposeValid = TryParseEnum<Purpose>(searchTerm, out var parsedPurpose);
                bool isSourceValid = TryParseEnum<LeadSource>(searchTerm, out var parsedSource);
                var isEnquiryTypeValid = TryParseEnum<EnquiryType>(searchTerm, out var parsedEnquiryType);
                var statusId = customStatuses?.Where(i => i.DisplayName.ToLower().Trim().Replace(" ", "").Contains(searchTerm)).Select(i => i.Id).ToList();
                query = query.Where(i =>
                   (request.PropertyToSearch.Contains("LeadName") && i.Name != null && i.Name.ToLower().Trim().Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("ContactNo") && i.ContactNo != null && i.ContactNo.Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("SerialNumber") && i.SerialNumber != null && i.SerialNumber.Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("AlternateContactNo") && i.AlternateContactNo != null && i.AlternateContactNo.Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("Email") && i.Email != null && i.Email.ToLower().Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("Nationality") && i.Nationality != null && i.Nationality.ToLower().Trim().Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("PropertyName") && i.Properties.Any(a => a.Title.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("ProjectName") && i.Projects.Any(a => a.Name.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("SubSource") && i.Enquiries.Any(a => !string.IsNullOrEmpty(a.SubSource) && a.SubSource.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("Status") && (statusId.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || statusId.Contains(i.CustomLeadStatus.Id))) ||
                    (request.PropertyToSearch.Contains("Purpose") && isPurposeValid && i.Enquiries.Any(e => e.Purpose == parsedPurpose)) ||
                     (request.PropertyToSearch.Contains("Source") && isSourceValid && i.Enquiries.Any(e => e.LeadSource == parsedSource)) ||
                     (request.PropertyToSearch.Contains("EnquiredFor") && isEnquiryTypeValid && i.Enquiries.Any(e => e.EnquiryTypes.Contains(parsedEnquiryType))) ||
                   (request.PropertyToSearch.Contains("Location") && i.Enquiries.Any(e => e.Addresses.Any(j =>
                    (j.SubLocality +
                     j.Locality +
                     j.Community +
                     j.SubCommunity +
                     j.TowerName +
                     j.District +
                     j.City +
                     j.State +
                     j.Country +
                     j.PostalCode).Replace(",", "").ToLower().Trim().Replace(" ", "")
                    .Contains(searchTerm)))));
            }
            else if (!string.IsNullOrWhiteSpace(request.SearchByNameOrNumber))
            {
                query = query.Where(i => i.ContactNo.Contains(request.SearchByNameOrNumber.Replace(" ", "")) ||
                         i.Name.ToLower().Contains(request.SearchByNameOrNumber.ToLower()) ||
                         i.SerialNumber.ToLower().Contains(request.SearchByNameOrNumber.ToLower().Replace(" ", "")));
            }
            if (request.Source?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.Source.Contains(i.LeadSource)));
            }
            var Statuses = (customStatuses?.Where(i => i.Status is "not_interested" or "dropped" or "booked" or "booking_cancel" or "invoiced"))?.ToList() ?? new();
            var StatusesIds = Statuses.Select(i => i.Id).ToList();
            var ScheduledStatus = (customStatuses?.Where(i => i.Status is "site_visit_scheduled" or "callback" or "meeting_scheduled"))?.ToList() ?? new();
            var schuduledStatusIds = ScheduledStatus.Select(i => i.Id).ToList();
            switch (category)
            {
                case LeadFilterTypeMobile.New:
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.Status == "new");
                    break;
                #region ScheduleDate based categories
                case LeadFilterTypeMobile.ScheduleToday:
                    query = query.Where(i => (i.ScheduledDate ?? DateTime.MinValue) >= DateTime.UtcNow.Date.AddMinutes(-330) && (i.ScheduledDate ?? DateTime.MinValue) < DateTime.UtcNow.Date.AddDays(1).AddMinutes(-330))
                    .Where(i => i.CustomLeadStatus == null || schuduledStatusIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.CustomLeadStatus.Id)); ;
                    break;
                case LeadFilterTypeMobile.Overdue:
                    query = query.Where(i => (i.ScheduledDate ?? DateTime.MinValue) < DateTime.UtcNow.Date.AddMinutes(-330))
                    .Where(i => i.CustomLeadStatus != null && (schuduledStatusIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.CustomLeadStatus.Id)));
                    break;
                case LeadFilterTypeMobile.ScheduledTomorrow:
                    query = query.Where(i => (i.ScheduledDate ?? DateTime.MinValue) >= DateTime.UtcNow.Date.AddDays(1).AddMinutes(-330) && (i.ScheduledDate ?? DateTime.MinValue) < DateTime.UtcNow.Date.AddDays(2).AddMinutes(-330))
                    .Where(i => i.CustomLeadStatus != null && (schuduledStatusIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.CustomLeadStatus.Id)));
                    break;
                case LeadFilterTypeMobile.UpcomingSchedules:
                    query = query.Where(i => (i.ScheduledDate ?? DateTime.MinValue) >= DateTime.UtcNow.Date.AddDays(1).AddMinutes(-330))
                    .Where(i => i.CustomLeadStatus == null || schuduledStatusIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.CustomLeadStatus.Id));
                    break;
                #endregion
                case LeadFilterTypeMobile.Pending:
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.Status == "pending");
                    break;
                case LeadFilterTypeMobile.NotInterested:
                    var notInterestedStatus = customStatuses?.FirstOrDefault(i => i.Status == "not_interested") ?? new();
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.BaseId == notInterestedStatus.Id || i.CustomLeadStatus.Id == notInterestedStatus.Id));
                    break;
                case LeadFilterTypeMobile.CallBack:
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.MasterLeadStatusBaseId == Guid.Parse("5ae346bc-c695-4af4-8c3b-c8648587fbd6"));
                    break;
                case LeadFilterTypeMobile.SiteVisitScheduled:
                    var siteVisitScheduled = customStatuses?.FirstOrDefault(i => i.Status == "site_visit_scheduled") ?? new();
                    //query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.Status == "site_visit_scheduled" || i.CustomLeadStatus.MasterLeadStatusBaseId.ToString() == "59647294-09d6-44a2-a346-9de5ba829e04"));
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.BaseId == siteVisitScheduled.Id || i.CustomLeadStatus.Id == siteVisitScheduled.Id));
                    break;
                case LeadFilterTypeMobile.ScheduledMeeting:
                    var meetingScheduled = customStatuses?.FirstOrDefault(i => i.Status == "meeting_scheduled") ?? new();
                    //query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.Status == "meeting_scheduled" || i.CustomLeadStatus.MasterLeadStatusBaseId.ToString() == "99a7f794-9046-4a9d-b7e2-e0a2196b98dd"));
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.BaseId == meetingScheduled.Id || i.CustomLeadStatus.Id == meetingScheduled.Id));
                    break;
                case LeadFilterTypeMobile.UnassignLeads:
                    query = query.Where(i => i.CustomLeadStatus != null && i.AssignTo == Guid.Empty);
                    break;
                case LeadFilterTypeMobile.Booked:
                    var bookedStatus = customStatuses?.FirstOrDefault(i => i.Status == "booked") ?? new();
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.BaseId == bookedStatus.Id || i.CustomLeadStatus.Id == bookedStatus.Id));
                    break;
                case LeadFilterTypeMobile.Dropped:
                    var droppedStatus = customStatuses?.FirstOrDefault(i => i.Status == "dropped") ?? new();
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.BaseId == droppedStatus.Id || i.CustomLeadStatus.Id == droppedStatus.Id));
                    break;
                case LeadFilterTypeMobile.Escalated:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsEscalated)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.HotLeads:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsHotLead)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.AboutToConvert:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsAboutToConvert)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.WarmLeads:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsWarmLead)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.ColdLead:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsColdLead)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.Highlighted:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsHighlighted)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.Active:
                    query = query.Where(i => i.CustomLeadStatus != null && !StatusesIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) && !StatusesIds.Contains(i.CustomLeadStatus.Id));
                    break;
                case LeadFilterTypeMobile.AllWithNID:
                case LeadFilterTypeMobile.All:
                    query = query.Where(i => i.CustomLeadStatus != null);
                    break;
                case LeadFilterTypeMobile.BookingCancel:
                    var bookingCancelStatus = customStatuses?.FirstOrDefault(i => i.Status == "booking_cancel") ?? new();
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.BaseId == bookingCancelStatus.Id || i.CustomLeadStatus.Id == bookingCancelStatus.Id));
                    break;
                case LeadFilterTypeMobile.Invoiced:
                    var invoicestatus = customStatuses?.FirstOrDefault(i => i.Status == "invoiced") ?? new();
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.BaseId == invoicestatus.Id || i.CustomLeadStatus.Id == invoicestatus.Id));
                    break;
                //case LeadFilterTypeMobile.Invoice:
                //    invoicestatuses = customStatuses?.FirstOrDefault(i => i.Status == "invoiced") ?? new();
                //    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.BaseId == invoicestatus.Id || i.CustomLeadStatus.Id == invoicestatus.Id));
                //    break;
                case LeadFilterTypeMobile.Untouched:
                    query = query.Where(i => !i.IsPicked);
                    break;
                case LeadFilterTypeMobile.ExpressionOfInterest:
                    var customStatus = customStatuses?.FirstOrDefault(i => i.Status == "expression_of_interest") ?? new();
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.BaseId == customStatus.Id || i.CustomLeadStatus.Id == customStatus.Id));
                    break;
                case LeadFilterTypeMobile.MeetingDone:
                    query = query.Where(i => i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone));
                    break;
                case LeadFilterTypeMobile.SiteVisitDone:
                    query = query.Where(i => i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone));
                    break;
                default:
                    break;
            }

            if (request.Properties?.Any() ?? false)
            {
                var propertyNames = request.Properties.Select(i => i.ToLower());
                query = query.Where(i => i.Properties.Count > 0 && i.Properties.Any(i => propertyNames.Contains(i.Title.ToLower())));
            }
            if (request.Projects?.Any() ?? false)
            {
                var projectNames = request.Projects.Select(i => i.ToLower());
                query = query.Where(i => i.Projects.Count > 0 && i.Projects.Any(i => projectNames.Contains(i.Name.ToLower()))).AsQueryable();
            }
            if (request.NoOfBHKs != null && request.NoOfBHKs.Any())
            {
                //query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.NoOfBHKs.Contains(i.NoOfBHKs)));
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.BHKs != null && i.BHKs.Any(j => request.NoOfBHKs.Contains(j))));
            }
            if (request.Beds?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Beds != null && i.Beds.Any(j => request.Beds.Contains(j))));
            }
            if (request.Baths?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Baths != null && i.Baths.Any(j => request.Baths.Contains(j))));
            }
            if (request.Floors?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Floors != null && i.Floors.Any(j => request.Floors.Contains(j))));
            }
            if (request.OfferTypes?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.OfferType != null && request.OfferTypes.Contains(i.OfferType.Value)));
            }
            if (request.Purposes?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Purpose != null && request.Purposes.Contains(i.Purpose.Value)));
            }
            if (request.Furnished?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Furnished != null && request.Furnished.Contains(i.Furnished.Value)));
            }
            if (request.SubSources != null && request.SubSources.Any())
            {
                request.SubSources = request.SubSources.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.SubSources.Contains(i.SubSource.ToLower().Trim())));
            }
            if (request.BHKTypes != null && request.BHKTypes.Any())
            {
                //query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.BHKTypes.Contains(i.BHKType)));
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.BHKTypes != null && i.BHKTypes.Any(j => request.BHKTypes.Contains(j))));
            }
            if (request.PropertyType != null && request.PropertyType.Any())
            {
                query = query.Where(i => i.Enquiries.Any(e => e.PropertyTypes.Any(i => request.PropertyType.Contains(i.BaseId ?? Guid.Empty))));
            }
            if (request.PropertySubType != null && request.PropertySubType.Any())
            {
                query = query.Where(i => i.Enquiries.Any(e => e.PropertyTypes.Any(i => request.PropertySubType.Contains(i.Id))));
            }
            if (request.StatusIds?.Any() ?? false)
            {
                var statusIds = request.StatusIds;
                query = query.Where(i => i.CustomLeadStatus != null &&
                                          (statusIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) ||
                                           statusIds.Contains(i.CustomLeadStatus.Id)));

            }
            if (request.Budget != null && request.Budget.Any())
            {
                foreach (var budget in request.Budget)
                {
                    switch (budget)
                    {
                        case Budget.UpToTenLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget <= 1000000));
                            break;
                        case Budget.TenToTwentyLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 1000000 && j.UpperBudget <= 2000000));
                            break;
                        case Budget.TwentyToThirtyLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 2000000 && j.UpperBudget <= 3000000));
                            break;
                        case Budget.ThirtyToFourtyLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 3000000 && j.UpperBudget <= 4000000));
                            break;
                        case Budget.FourtyToFiftyLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 4000000 && j.UpperBudget <= 5000000));
                            break;
                        case Budget.FiftyToOneCrore:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 5000000 && j.UpperBudget <= 10000000));
                            break;
                        case Budget.MoreThanOneCrore:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 10000000));
                            break;
                    }
                }
            }
            if (request.Currency != null)
            {
                query = query.Where(i => i.Enquiries.Any(e => e.Currency == request.Currency));
            }
            if (request.MinBudget != null || request.MaxBudget != null)
            {
                if (request.MinBudget != null && request.MaxBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(i => (i.UpperBudget >= request.MinBudget && i.UpperBudget <= request.MaxBudget) || (i.LowerBudget >= request.MinBudget && i.LowerBudget <= request.MaxBudget)));
                }
                else if (request.MinBudget != null && request.MaxBudget == null)
                {
                    query = query.Where(i => i.Enquiries.Any(i => (i.UpperBudget >= request.MinBudget) || (i.LowerBudget >= request.MinBudget)));
                }
                else if (request.MinBudget == null && request.MaxBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(i => (i.UpperBudget <= request.MaxBudget) || (i.LowerBudget <= request.MaxBudget)));
                }
            }
            if (request.BudgetFilters != null && request.BudgetFilters.Any())
            {
                foreach (var budget in request.BudgetFilters)
                {
                    query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.UpperBudget >= budget.MinBudget && i.UpperBudget <= budget.MaxBudget));
                }
            }
            if (request.Locations != null && request.Locations.Any())
            {
                request.Locations = request.Locations.ConvertAll(i => Uri.UnescapeDataString(i).Replace(",", "").ToLower().Trim().Replace(" ", "")).ToList();
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Any(j =>
                request.Locations.Contains(
                    (j.SubLocality +
                    j.Locality +
                    j.Community +
                    j.SubCommunity +
                    j.TowerName +
                    j.District +
                    j.City +
                    j.State +
                    j.Country +
                    j.PostalCode).Replace(",", "").ToLower().Trim().Replace(" ", "")))));

            }
            if (request.IntegrationAccountIds != null && request.IntegrationAccountIds.Any())
            {
                query = query.Where(i => request.IntegrationAccountIds.Contains(i.AccountId));
            }
            if (request.AgencyNames != null && request.AgencyNames.Any())
            {
                request.AgencyNames = request.AgencyNames.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.Agencies.Any(i => i.Name != null && request.AgencyNames.Contains(i.Name.ToLower().Trim())));
            }
            if (request.CampaignNames != null && request.CampaignNames.Any())
            {
                request.CampaignNames = request.CampaignNames.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.Campaigns.Any(i => i.Name != null && request.CampaignNames.Contains(i.Name.ToLower().Trim())));
            }
            if (request.LeadIds != null)
            {
                query = query.Where(i => request.LeadIds.Contains(i.Id));
            }
            if (request?.SerialNumbers?.Any() ?? false)
            {
                query = query.Where(i => request.SerialNumbers.Contains(i.SerialNumber));
            }
            if (request?.IsUntouched != null && request.IsUntouched != default)
            {
                query = query.Where(i => !i.IsPicked == request.IsUntouched.Value);
            }

            if (request?.BookedByIds?.Any() ?? false)
            {
                query = query.Where(i => i.BookedDetails.Any(i => request.BookedByIds.Contains(i.BookedBy.Value)));
            }
            if ((request.AssignFromIds?.Any() ?? false) && !(request?.IsWithHistory ?? false))
            {
                query = query.Where(i => i.AssignedFrom != null && request.AssignFromIds.Contains(i.AssignedFrom.Value));
            }
            if ((request?.SecondaryUsers?.Any() ?? false) && (request?.IsDualOwnershipEnabled ?? false) && !(request?.IsWithHistory ?? false))
            {
                query = query.Where(i => i.SecondaryUserId != null && request.SecondaryUsers.Contains(i.SecondaryUserId.Value));
            }
            if (request?.OriginalOwnerIds?.Any() ?? false)
            {
                query = query.Where(i => i.OriginalOwner != null && request.OriginalOwnerIds.Contains(i.OriginalOwner.Value));
            }
            if ((request?.SecondaryFromIds?.Any() ?? false) && (request?.IsDualOwnershipEnabled ?? false) && !(request?.IsWithHistory ?? false))
            {
                query = query.Where(i => i.SecondaryFromUserId != null && request.SecondaryFromIds.Contains(i.SecondaryFromUserId.Value));
            }
            if (request?.IsWithHistory ?? false)
            {
                DateTime? fromDate = null;
                DateTime? toDate = null;

                if (request.DateType.HasValue && request.FromDate.HasValue && request.FromDate.Value != default && request.ToDate.HasValue && request.ToDate.Value != default)
                {
                    fromDate = request.FromDate.Value.ConvertFromDateToUtc();
                    toDate = request.ToDate.Value.ConvertToDateToUtc();
                }
                query = query.Where(i => i.Assignments != null && i.Assignments.Any(j =>
                    (request.HistoryAssignedToIds == null || !request.HistoryAssignedToIds.Any() || (j.AssignTo != null && request.HistoryAssignedToIds.Contains(j.AssignTo ?? Guid.Empty))) &&
                    (request.AssignFromIds == null || !request.AssignFromIds.Any() || (j.AssignedFrom != null && request.AssignFromIds.Contains(j.AssignedFrom ?? Guid.Empty))) &&
                    (request.SecondaryUsers == null || !request.SecondaryUsers.Any() || (j.SecondaryAssignTo != null && request.SecondaryUsers.Contains(j.SecondaryAssignTo ?? Guid.Empty))) &&
                    (request.SecondaryFromIds == null || !request.SecondaryFromIds.Any() || (j.SecondaryAssignFrom != null && request.SecondaryFromIds.Contains(j.SecondaryAssignFrom ?? Guid.Empty))) &&
                    (request.DoneBy == null || !request.DoneBy.Any() || request.DoneBy.Contains(j.LastModifiedBy)) &&
                    (fromDate == null || toDate == null ||
                    (j.AssignmentDate.HasValue && j.AssignmentDate.Value >= fromDate.Value && j.AssignmentDate.Value <= toDate.Value))
                ));
            }
            if (request?.DoneBy?.Any() ?? false)
            {
                DateTime? fromDate = null;
                DateTime? toDate = null;

                if (request.DateType.HasValue && request.FromDate.HasValue && request.FromDate.Value != default && request.ToDate.HasValue && request.ToDate.Value != default)
                {
                    fromDate = request.FromDate.Value.ConvertFromDateToUtc();
                    toDate = request.ToDate.Value.ConvertToDateToUtc();
                }

                query = query.Where(i => i.Assignments != null && i.Assignments.Any(j =>
                    request.DoneBy.Contains(j.LastModifiedBy) &&
                    (fromDate == null || toDate == null || (j.AssignmentDate.HasValue && j.AssignmentDate.Value >= fromDate.Value && j.AssignmentDate.Value <= toDate.Value))
                ));
            }
            if (request.Cities?.Any() ?? false)
            {
                var normalizedCityNames = request.Cities.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedCityNames.Contains(i.City.ToLower().Trim().Replace(" ", ""))).Any()));
            }

            if (request.States?.Any() ?? false)
            {
                var normalizedStateNames = request.States.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.State.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.Countries?.Any() ?? false)
            {
                var normalizedStateNames = request.Countries.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.Country.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.Communities?.Any() ?? false)
            {
                var normalizedStateNames = request.Communities.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.Community.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.SubCommunities?.Any() ?? false)
            {
                var normalizedStateNames = request.SubCommunities.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.SubCommunity.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.TowerNames?.Any() ?? false)
            {
                var normalizedStateNames = request.TowerNames.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.TowerName.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.PostalCodes?.Any() ?? false)
            {
                var normalizedStateNames = request.PostalCodes.ConvertAll(i => i.Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.PostalCode.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (!string.IsNullOrEmpty(request?.AdditionalPropertiesKey))
            {
                var key = request.AdditionalPropertiesKey;

                if (!string.IsNullOrEmpty(request.AdditionalPropertiesValue))
                {
                    var value = request.AdditionalPropertiesValue;
                    query = query
                        .Where(lead => EF.Functions.JsonContains(
                            lead.AdditionalProperties,
                            JsonConvert.SerializeObject(new Dictionary<string, string> { { key, value } })));
                }
                else
                {
                    query = query
                        .Where(lead => EF.Functions.JsonContains(
                            lead.AdditionalProperties,
                            JsonConvert.SerializeObject(new Dictionary<string, string> { { key, "" } })));
                }
            }
            if (request.ChannelPartnerNames != null && request.ChannelPartnerNames.Any())
            {
                request.ChannelPartnerNames = request.ChannelPartnerNames.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.ChannelPartners.Any(i => i.FirmName != null && request.ChannelPartnerNames.Contains(i.FirmName.ToLower().Trim())));
            }
            if (request.LastModifiedByIds?.Any() ?? false)
            {
                query = query.Where(i => request.LastModifiedByIds.Contains(i.LastModifiedBy));
            }
            if (request.CreatedByIds?.Any() ?? false)
            {
                query = query.Where(i => request.CreatedByIds.Contains(i.CreatedBy));
            }

            if (request.RestoredByIds?.Any() ?? false)
            {
                query = query.Where(i => request.RestoredByIds.Contains(i.RestoredBy.Value));
            }
            if (request.ArchivedByIds?.Any() ?? false)
            {
                query = query.Where(i => request.ArchivedByIds.Contains(i.ArchivedBy.Value));
            }
            if (request.SourcingManagers?.Any() ?? false)
            {
                query = query.Where(i => request.SourcingManagers.Contains(i.SourcingManager.Value));
            }
            if (request.ClosingManagers?.Any() ?? false)
            {
                query = query.Where(i => request.ClosingManagers.Contains(i.ClosingManager.Value));
            }
            if (request.Profession != null && request.Profession.Any())
            {
                query = query.Where(i => request.Profession.Contains(i.Profession));
            }
            if (!string.IsNullOrEmpty(request?.UploadTypeName))
            {
                query = query.Where(i => i.UploadTypeName != null && i.UploadTypeName.ToLower().Contains(request.UploadTypeName.ToLower().Trim()));
            }
            if (request?.LeadType != null && request.LeadType.Any())
            {
                var predicate = PredicateBuilder.New<Lead>(false);

                if (request.LeadType.Contains(LeadType.ShowPrimeLeads))
                {
                    predicate = predicate.Or(i => i.ChildLeadsCount == 0 && i.RootId == null && i.ParentLeadId == null);
                }

                if (request.LeadType.Contains(LeadType.ShowOnlyParentLeads))
                {
                    if (request.ChildLeadsCount != null && request.ChildLeadsCount > 0)
                    {
                        predicate = predicate.Or(i => i.ChildLeadsCount == request.ChildLeadsCount);
                    }
                    else
                    {
                        predicate = predicate.Or(i => i.ChildLeadsCount > 0);
                    }
                }

                if (request.LeadType.Contains(LeadType.ShowOnlyDuplicateLeads))
                {
                    predicate = predicate.Or(i => i.ChildLeadsCount == 0 && (i.RootId != null || i.ParentLeadId != null));
                }

                query = query.Where(predicate);
            }
            if (request?.PossesionType != null && request?.PossesionType != PossesionType.None)
            {
                switch (request?.PossesionType)
                {
                    case PossesionType.UnderConstruction:
                        query = query.Where(i => i.PossesionType == PossesionType.UnderConstruction);
                        break;


                    case PossesionType.SixMonth:
                        query = query.Where(i => i.PossesionType == PossesionType.SixMonth);
                        break;

                    case PossesionType.Year:
                        query = query.Where(i => i.PossesionType == PossesionType.Year);
                        break;

                    case PossesionType.TwoYears:
                        query = query.Where(i => i.PossesionType == PossesionType.TwoYears);
                        break;
                    case PossesionType.CustomDate:
                        DateTime? tempToPossesionDate = request?.ToPossesionDate?.ConvertToDateToUtc();
                        DateTime? tempFrompossesionDate = request?.FromPossesionDate?.ConvertFromDateToUtc();
                        query = query.Where(i => i.Enquiries.Any(j => j.PossessionDate != null && j.PossessionDate >= tempFrompossesionDate.Value && j.PossessionDate <= tempToPossesionDate.Value));
                        break;

                    case PossesionType.ThreeMonth:
                        query = query.Where(i => i.PossesionType == PossesionType.ThreeMonth);
                        break;

                    case PossesionType.Immediate:
                        query = query.Where(i => i.PossesionType == PossesionType.Immediate);
                        break;
                }
            }
            if (request?.LandLine != null && request.LandLine.Any())
            {
                query = query.Where(i => i.LandLine != null && request.LandLine.Contains(i.LandLine.Trim()));
            }
            if (request?.CountryCode?.Any() ?? false)
            {
                var codes = request.CountryCode
        .Where(c => !string.IsNullOrWhiteSpace(c))
        .Select(c => c.Trim().TrimStart('+'))
        .Distinct()
        .ToList();

                Expression<Func<Lead, bool>> filter = lead => false;

                foreach (var code in codes)
                {
                    var local = code;

                    filter = filter.Or(lead =>
                        (lead.CountryCode != null && lead.CountryCode.Trim().TrimStart('+') == local) ||
                        (lead.ContactNo != null &&
                            (lead.ContactNo.StartsWith("+" + local) || lead.ContactNo.StartsWith(local)))
                    );
                }

                query = query.Where(filter);
            }
            if (request?.AltCountryCode?.Any() ?? false)
            {
                query = query.Where(i => i.AltCountryCode != null && request.AltCountryCode.Contains(i.AltCountryCode));
            }
            if (request?.GenderTypes?.Any() ?? false)
            {
                query = query.Where(i => i.Gender != null && request.GenderTypes.Contains(i.Gender.Value));
            }
            if (request?.MaritalStatuses?.Any() ?? false)
            {
                query = query.Where(i => i.MaritalStatus != null && request.MaritalStatuses.Contains(i.MaritalStatus.Value));
            }
            if (request?.DateOfBirth != null)
            {
                query = query.Where(i => i.DateOfBirth != null && i.DateOfBirth == request.DateOfBirth);
            }

            if (request?.CallDirections != null && request.CallDirections.Any() && !request.CallDirections.Contains(CallDirection.None))
            {
                query = query.Where(i =>
                    i.LeadCallLogs != null && i.LeadCallLogs.Any(j => request.CallDirections.Contains(j.CallDirection)));
            }

            if (request?.CallStatuses != null && request.CallStatuses.Any() && !request.CallStatuses.Contains(CallStatus.None))
            {
                query = query.Where(i =>
                    i.LeadCallLogs != null && i.LeadCallLogs.Any(j => request.CallStatuses.Contains(j.CallStatus)));
            }

            if (request?.UserIds != null && request.UserIds.Any())
            {
                query = query.Where(i =>
                    i.LeadCallLogs != null && i.LeadCallLogs.Any(j => request.UserIds.Contains(j.UserId)));
            }
            if (request?.AnniversaryDate != null)
            {
                query = query.Where(i => i.AnniversaryDate != null && i.AnniversaryDate == request.AnniversaryDate);
            }
            return query.AsQueryable();
        }
        public async Task<IEnumerable<Domain.Entities.Lead>> GetAllLeadsByCustomFiltersForMobileV2Async(CustomFilter? filter, GetAllLeadsParametersNewFilters request, IEnumerable<Guid> subIds, Guid userId, bool isAdmin, List<Guid> leadHistoryIds = null)
        {
           
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

            try
            {
                var stopwatch = System.Diagnostics.Stopwatch.StartNew();

                // STEP 1: BLAZING FAST - Get filtered lead IDs with minimal projection (50-100ms)
                var query = MobileCustomFilterBuildQueryV2(filter, request, subIds, userId, leadHistoryIds, isAdmin);

                if (filter?.IsForward ?? false)
                {
                    query = query.OrderBy(i => (i.ScheduledDate ?? DateTime.MinValue)).ThenBy(i => i.Name);
                }
                else
                {
                    query = query.OrderByDescending(i => (i.LastModifiedOn ?? DateTime.MaxValue)).ThenBy(i => i.Name);
                }
                var leads = await query
                                   .Skip(request.PageSize * (request.PageNumber - 1))
                                   .Take(request.PageSize)
                                    .Select(l => new Lead
                                    {
                                        // Core properties only - BLAZING FAST
                                        Id = l.Id,
                                        Name = l.Name ?? string.Empty,
                                        ContactNo = l.ContactNo ?? string.Empty,
                                        AlternateContactNo = l.AlternateContactNo,
                                        IsPicked = l.IsPicked,
                                        AssignTo = l.AssignTo,
                                        SecondaryUserId = l.SecondaryUserId,
                                        Notes = l.Notes,
                                        ScheduledDate = l.ScheduledDate,
                                        ContactRecords = l.ContactRecords,
                                        IsMeetingDone = l.IsMeetingDone,
                                        MeetingLocation = l.MeetingLocation,
                                        IsSiteVisitDone = l.IsSiteVisitDone,
                                        SiteLocation = l.SiteLocation,
                                        AppointmentDoneOn = l.AppointmentDoneOn,
                                        CreatedBy = l.CreatedBy,
                                        CreatedOn = l.CreatedOn,
                                        LastModifiedBy = l.LastModifiedBy,
                                        LastModifiedOn = l.LastModifiedOn,
                                        IsConvertedFromData = l.IsConvertedFromData,
                                        BookedDate = l.BookedDate,
                                        BookedBy = l.BookedBy,
                                        CustomLeadStatus = l.CustomLeadStatus != null ? new CustomMasterLeadStatus
                                        {
                                            Id = l.CustomLeadStatus.Id,
                                            BaseId = l.CustomLeadStatus.BaseId,

                                        } : null,
                                        Appointments = l.Appointments != null ? l.Appointments.Select(a => new LeadAppointment
                                        {
                                            Id = a.Id,
                                            LeadId = a.LeadId,
                                            Type = a.Type,
                                            IsDone = a.IsDone,
                                        }).ToList() : null,
                                        Enquiries = l.Enquiries != null ? l.Enquiries.Select(e => new LeadEnquiry
                                        {
                                            Id = e.Id,
                                            LeadId = e.LeadId,
                                            EnquiredFor = e.EnquiredFor,
                                            LeadSource = e.LeadSource,
                                            LowerBudget = e.LowerBudget,
                                            UpperBudget = e.UpperBudget,
                                            NoOfBHKs = e.NoOfBHKs,
                                            EnquiryTypes = e.EnquiryTypes,
                                            BHKs = e.BHKs,
                                        })
                .ToList() : null,

                                    })
                    .ToListAsync();
                return leads;
            }
            catch (Exception ex)
            {
                throw;
            }
            finally
            {
            }
        }
        private IQueryable<Lead> MobileCustomFilterBuildQueryV2(CustomFilter? filter, GetAllLeadsParametersNewFilters request, IEnumerable<Guid> subIds, Guid userId, List<Guid> leadHistoryIds, bool isAdmin, List<CustomMasterLeadStatus>? customMasterLeadStatus = null)
        {
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            var reporteeIds = subIds.Where(i => i != userId && i != Guid.Empty);
            var selfWithReporteeIds = subIds.Where(i => i != Guid.Empty);
            var query = context.Leads.Where(i => !i.IsDeleted)
                .AsQueryable();

            if (request.CustomFlags?.Any() ?? false)
            {
                query = query.Where(i => i.CustomFlags != null && i.CustomFlags.Any(j => j.Flag != null && request.CustomFlags.Contains(j.Flag.Name ?? string.Empty)));
            }
            if (request.IsPicked != null && request.IsPicked != default)
            {
                query = query.Where(i => i.IsPicked == request.IsPicked.Value);
            }
            if (request.DataConverted != null)
            {
                if (request.DataConverted == true)
                {
                    query = query.Where(i => i.IsConvertedFromData == true);
                }
                else
                {
                    query = query.Where(i => i.IsConvertedFromData != true);
                }
            }
            if (request.QualifiedByIds?.Any() ?? false)
            {
                query = query.Where(i => i.QualifiedBy.HasValue && request.QualifiedByIds.Contains(i.QualifiedBy.Value));
            }
            if (request?.OriginalOwnerIds?.Any() ?? false)
            {
                query = query.Where(i => i.OriginalOwner != null && request.OriginalOwnerIds.Contains(i.OriginalOwner.Value));
            }
            if (!string.IsNullOrWhiteSpace(request.ConfidentialNotes))
            {
                request.ConfidentialNotes = request.ConfidentialNotes.ToLower().Trim();
                query = query.Where(i => i.ConfidentialNotes.ToLower().Trim().Contains(request.ConfidentialNotes));
            }

            if (request.CarpetArea != null && request.CarpetAreaUnitId != default)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.CarpetArea == request.CarpetArea && i.CarpetAreaUnitId == request.CarpetAreaUnitId));
            }
            else if (request.CarpetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.CarpetArea == request.CarpetArea || i.CarpetAreaInSqMtr == request.CarpetArea));
            }
            if (request.BuiltUpArea != null && request.BuiltUpAreaUnitId != default)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.BuiltUpArea == request.BuiltUpArea && i.BuiltUpAreaUnitId == request.BuiltUpAreaUnitId));
            }
            else if (request.BuiltUpArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.BuiltUpArea == request.BuiltUpArea || i.BuiltUpAreaInSqMtr == request.BuiltUpArea));
            }
            if (request.SaleableArea != null && request.SaleableAreaUnitId != default)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.SaleableArea == request.SaleableArea && i.SaleableAreaUnitId == request.SaleableAreaUnitId));
            }
            else if (request.SaleableArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.SaleableArea == request.SaleableArea || i.SaleableAreaInSqMtr == request.SaleableArea));
            }
            if (request.NetArea != null && request.NetAreaUnitId != default)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.NetArea == request.NetArea && i.NetAreaUnitId == request.NetAreaUnitId));
            }
            else if (request.NetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.NetArea == request.NetArea || i.NetAreaInSqMtr == request.NetArea));
            }
            if (request.PropertyArea != null && request.PropertyAreaUnitId != default)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.PropertyArea == request.PropertyArea && i.PropertyAreaUnitId == request.PropertyAreaUnitId));
            }
            else if (request.PropertyArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.PropertyArea == request.PropertyArea || i.PropertyAreaInSqMtr == request.PropertyArea));
            }
            if ((request.MinCarpetArea != null || request.MaxCarpetArea != null) && request.CarpetAreaUnitId != default)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.CarpetAreaUnitId == request.CarpetAreaUnitId &&
                    (request.MinCarpetArea == null || e.CarpetArea >= request.MinCarpetArea) &&
                    (request.MaxCarpetArea == null || e.CarpetArea <= request.MaxCarpetArea)));
            }
            else if (request.MinCarpetArea != null || request.MaxCarpetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinCarpetArea == null || e.CarpetArea >= request.MinCarpetArea) &&
                    (request.MaxCarpetArea == null || e.CarpetArea <= request.MaxCarpetArea)));
            }
            if ((request.MinBuiltUpArea != null || request.MaxBuiltUpArea != null) && request.BuiltUpAreaUnitId != default && request.BuiltUpAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.BuiltUpAreaUnitId == request.BuiltUpAreaUnitId &&
                    (request.MinBuiltUpArea == null || e.BuiltUpArea >= request.MinBuiltUpArea) &&
                    (request.MaxBuiltUpArea == null || e.BuiltUpArea <= request.MaxBuiltUpArea)));
            }
            else if (request.MinBuiltUpArea != null || request.MaxBuiltUpArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinBuiltUpArea == null || e.BuiltUpArea >= request.MinBuiltUpArea) &&
                    (request.MaxBuiltUpArea == null || e.BuiltUpArea <= request.MaxBuiltUpArea)));
            }
            if ((request.MinSaleableArea != null || request.MaxSaleableArea != null) && request.SaleableAreaUnitId != default && request.SaleableAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.SaleableAreaUnitId == request.SaleableAreaUnitId &&
                    (request.MinSaleableArea == null || e.SaleableArea >= request.MinSaleableArea) &&
                    (request.MaxSaleableArea == null || e.SaleableArea <= request.MaxSaleableArea)));
            }
            else if (request.MinSaleableArea != null || request.MaxSaleableArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinSaleableArea == null || e.SaleableArea >= request.MinSaleableArea) &&
                    (request.MaxSaleableArea == null || e.SaleableArea <= request.MaxSaleableArea)));
            }
            if ((request.MinPropertyArea != null || request.MaxPropertyArea != null) && request.PropertyAreaUnitId != default && request.PropertyAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.PropertyAreaUnitId == request.PropertyAreaUnitId &&
                    (request.MinPropertyArea == null || e.PropertyArea >= request.MinPropertyArea) &&
                    (request.MaxPropertyArea == null || e.PropertyArea <= request.MaxPropertyArea)));
            }
            else if (request.MinPropertyArea != null || request.MaxPropertyArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinPropertyArea == null || e.PropertyArea >= request.MinPropertyArea) &&
                    (request.MaxPropertyArea == null || e.PropertyArea <= request.MaxPropertyArea)));
            }

            if ((request.MinNetArea != null || request.MaxNetArea != null) && request.NetAreaUnitId != default && request.NetAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.NetAreaUnitId == request.NetAreaUnitId &&
                    (request.MinNetArea == null || e.NetArea >= request.MinNetArea) &&
                    (request.MaxNetArea == null || e.NetArea <= request.MaxNetArea)));
            }
            else if (request.MinNetArea != null || request.MaxNetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinNetArea == null || e.NetArea >= request.MinNetArea) &&
                    (request.MaxNetArea == null || e.NetArea <= request.MaxNetArea)));
            }
            if (request.FromMinBudget != null || request.ToMinBudget != null || request.FromMaxBudget != null || request.ToMaxBudget != null)
            {
                if (request.FromMinBudget != null && request.ToMinBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        (e.LowerBudget >= request.FromMinBudget && e.LowerBudget <= request.ToMinBudget) ||
                        (e.LowerBudget >= request.FromMinBudget && e.LowerBudget <= request.ToMinBudget)
                    ));
                }
                else if (request.FromMinBudget != null && request.ToMinBudget == null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        e.LowerBudget >= request.FromMinBudget ||
                        e.LowerBudget >= request.FromMinBudget
                    ));
                }
                else if (request.FromMinBudget == null && request.ToMinBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        e.LowerBudget <= request.ToMinBudget ||
                        e.LowerBudget <= request.ToMinBudget
                    ));
                }

                if (request.FromMaxBudget != null && request.ToMaxBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        (e.UpperBudget >= request.FromMaxBudget && e.UpperBudget <= request.ToMaxBudget) ||
                        (e.UpperBudget >= request.FromMaxBudget && e.UpperBudget <= request.ToMaxBudget)
                    ));
                }
                else if (request.FromMaxBudget != null && request.ToMaxBudget == null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        e.UpperBudget >= request.FromMaxBudget ||
                        e.UpperBudget >= request.FromMaxBudget
                    ));
                }
                else if (request.FromMaxBudget == null && request.ToMaxBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        e.UpperBudget <= request.ToMaxBudget ||
                        e.UpperBudget <= request.ToMaxBudget
                    ));
                }
            }

            if (!string.IsNullOrEmpty(request.UnitName))
            {
                query = query.Where(i => i.Enquiries.Any(e => e.UnitName != null && e.UnitName.ToLower().Trim() == request.UnitName.ToLower().Trim()));
            }
            if (request?.UnitNames?.Any() ?? false)
            {
                var unitnames = request.UnitNames.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Enquiries.Count > 0 && i.Enquiries.Any(i => unitnames.Contains(i.UnitName.ToLower()))).AsQueryable();
            }
            if (request?.Nationality?.Any() ?? false)
            {
                var nationality = request.Nationality.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Nationality != null && nationality.Contains(i.Nationality.ToLower())).AsQueryable();
            }

            if (request?.ClusterName?.Any() ?? false)
            {
                var ClusterName = request.ClusterName.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Enquiries.Count > 0 && i.Enquiries.Any(i => ClusterName.Contains(i.ClusterName.ToLower()))).AsQueryable();
            }
            if (!string.IsNullOrEmpty(request.ReferralName))
            {
                query = query.Where(i => (i.ReferralName.ToLower().Trim().Contains(request.ReferralName.ToLower().Trim())));
            }
            if (!string.IsNullOrEmpty(request.ReferralContactNo))
            {
                query = query.Where(i => !string.IsNullOrEmpty(i.ReferralContactNo) && request.ReferralContactNo.Contains(i.ReferralContactNo.Substring(i.ReferralContactNo.Length - 10)));
            }
            if (!string.IsNullOrEmpty(request.ReferralEmail))
            {
                query = query.Where(i => (i.ReferralEmail.ToLower().Trim().Contains(request.ReferralEmail.ToLower().Trim())));
            }
            if (leadHistoryIds?.Any() ?? false)
            {
                query = query.Where(i => leadHistoryIds.Contains(i.Id));
            }

            if (request?.MeetingOrVisitStatuses?.Any() ?? false)
            {
                DateTime? fromDateForMeetingOrVisit = request.FromDateForMeetingOrVisit != null ? request.FromDateForMeetingOrVisit.Value.ConvertFromDateToUtc() : null;
                DateTime? toDateForMeetingOrVisit = request.ToDateForMeetingOrVisit != null ? request.ToDateForMeetingOrVisit.Value.ConvertToDateToUtc() : null;
                (List<AppointmentType> appTypes, List<bool> appDoneStatuses) = GetAppointmentTypes(request);
                if (request.AppointmentDoneByUserIds?.Any() ?? false)
                {
                    query = query.Where(i => i.Appointments.Any(app => request.AppointmentDoneByUserIds.Contains(app.CreatedBy) && app.Type != AppointmentType.None && appTypes.Contains(app.Type) && appDoneStatuses.Contains(app.IsDone)));
                }
                else if (!isAdmin && (!request.AppointmentDoneByUserIds?.Any() ?? true))
                {
                    query = query.Where(i => i.Appointments.Any(j => j.UserId == i.AssignTo && j.Type != AppointmentType.None));
                }
                if (fromDateForMeetingOrVisit != null && toDateForMeetingOrVisit != null)
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                         && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDateForMeetingOrVisit && i.CreatedOn < toDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDateForMeetingOrVisit && i.CreatedOn < toDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDateForMeetingOrVisit && i.CreatedOn < toDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDateForMeetingOrVisit && i.CreatedOn < toDateForMeetingOrVisit)));
                }
                else if (fromDateForMeetingOrVisit != null)
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                         && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDateForMeetingOrVisit)));
                }
                else if (toDateForMeetingOrVisit != null)
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                         && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn < toDateForMeetingOrVisit.Value))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn < toDateForMeetingOrVisit.Value))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn < toDateForMeetingOrVisit.Value))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn < toDateForMeetingOrVisit.Value)));
                }
                else if ((request.AssignToIds?.Any() ?? false) && (request.OwnerSelection == OwnerSelectionType.Both))
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && (subIds.Contains(i.AssignTo) || request.AssignToIds.Contains(i.SecondaryUserId.Value))))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && !j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && (subIds.Contains(i.AssignTo) || request.AssignToIds.Contains(i.SecondaryUserId.Value))))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && (subIds.Contains(i.AssignTo) || request.AssignToIds.Contains(i.SecondaryUserId.Value))))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && !j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && (subIds.Contains(i.AssignTo) || request.AssignToIds.Contains(i.SecondaryUserId.Value)))));

                }
                else if ((request.OwnerSelection == OwnerSelectionType.SecondaryOwner) && (request.AssignToIds?.Any() ?? false))
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && j.IsDone && (j.UserId != Guid.Empty) && request.AssignToIds.Contains(i.SecondaryUserId.Value)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && !j.IsDone && (j.UserId != Guid.Empty) && request.AssignToIds.Contains(i.SecondaryUserId.Value)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && j.IsDone && (j.UserId != Guid.Empty) && request.AssignToIds.Contains(i.SecondaryUserId.Value)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && !j.IsDone && (j.UserId != Guid.Empty) && request.AssignToIds.Contains(i.SecondaryUserId.Value))));

                }
                else if ((request.AssignToIds?.Any() ?? false))
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.AssignTo)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && !j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.AssignTo)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.AssignTo)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && !j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.AssignTo))));

                }

                else
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                                && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                                && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                                && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                                && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId))));
                }
            }
            if ((request?.IsDualOwnershipEnabled ?? false) && (request?.AssignToIds?.Any() ?? false) && (request.OwnerSelection == OwnerSelectionType.Both))
            {
                switch (request.LeadVisibility)
                {
                    case BaseLeadVisibility.SelfWithReportee:
                        query = query.Where(i => (selfWithReporteeIds.Contains(i.AssignTo) || selfWithReporteeIds.Contains(i.SecondaryUserId ?? Guid.Empty))).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Self:
                        query = query.Where(i => (i.AssignTo == userId || i.SecondaryUserId == userId)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Reportee:
                        query = query.Where(i => (reporteeIds.Contains(i.AssignTo) || reporteeIds.Contains(i.SecondaryUserId ?? Guid.Empty))).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.UnassignLead:
                        query = query.Where(i => i.AssignTo == Guid.Empty).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.DeletedLeads:
                        query = query.Where(i => i.IsArchived && selfWithReporteeIds.Contains(i.AssignTo));
                        break;
                    case BaseLeadVisibility.ReEnquired:
                        query = query.Where(i => i.IsReEnquired == true && selfWithReporteeIds.Contains(i.AssignTo));
                        break;
                    default:
                        break;
                }
            }
            else if ((request?.IsDualOwnershipEnabled ?? false) && (request?.AssignToIds?.Any() ?? false) && (request.OwnerSelection == OwnerSelectionType.SecondaryOwner))
            {
                switch (request.LeadVisibility)
                {
                    case BaseLeadVisibility.SelfWithReportee:
                        query = query.Where(i => selfWithReporteeIds.Contains(i.SecondaryUserId.Value)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Self:
                        query = query.Where(i => i.SecondaryUserId == userId).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Reportee:
                        query = query.Where(i => reporteeIds.Contains(i.SecondaryUserId.Value)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.UnassignLead:
                        query = query.Where(i => i.AssignTo == Guid.Empty).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.DeletedLeads:
                        query = query.Where(i => i.IsArchived && selfWithReporteeIds.Contains(i.SecondaryUserId.Value));
                        break;
                    case BaseLeadVisibility.ReEnquired:
                        query = query.Where(i => i.IsReEnquired == true && selfWithReporteeIds.Contains(i.AssignTo));
                        break;
                    default:
                        break;
                }
            }
            else if ((request?.IsDualOwnershipEnabled ?? false) && (!request?.AssignToIds?.Any() ?? true))
            {
                switch (request.LeadVisibility)
                {
                    case BaseLeadVisibility.SelfWithReportee:
                        query = query.Where(i => (selfWithReporteeIds.Contains(i.AssignTo) || selfWithReporteeIds.Contains(i.SecondaryUserId ?? Guid.Empty))).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Self:
                        query = query.Where(i => (i.AssignTo == userId || i.SecondaryUserId == userId)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Reportee:
                        query = query.Where(i => (reporteeIds.Contains(i.AssignTo) || reporteeIds.Contains(i.SecondaryUserId ?? Guid.Empty))).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.UnassignLead:
                        query = query.Where(i => i.AssignTo == Guid.Empty).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.DeletedLeads:
                        query = query.Where(i => i.IsArchived && selfWithReporteeIds.Contains(i.AssignTo));
                        break;
                    case BaseLeadVisibility.ReEnquired:
                        query = query.Where(i => i.IsReEnquired == true && selfWithReporteeIds.Contains(i.AssignTo));
                        break;
                    default:
                        break;
                }
            }
            else
            {
                switch (request.LeadVisibility)
                {
                    case BaseLeadVisibility.SelfWithReportee:
                        query = query.Where(i => selfWithReporteeIds.Contains(i.AssignTo)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Self:
                        query = query.Where(i => i.AssignTo == userId).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Reportee:
                        query = query.Where(i => reporteeIds.Contains(i.AssignTo)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.UnassignLead:
                        query = query.Where(i => i.AssignTo == Guid.Empty).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.DeletedLeads:
                        query = query.Where(i => i.IsArchived && selfWithReporteeIds.Contains(i.AssignTo));
                        break;
                    case BaseLeadVisibility.ReEnquired:
                        query = query.Where(i => i.IsReEnquired == true && selfWithReporteeIds.Contains(i.AssignTo));
                        break;
                    default:
                        break;
                }
            }
            if (request.EnquiredFor != null && request.EnquiredFor.Any())
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.EnquiryTypes != null && i.EnquiryTypes.Any(j => request.EnquiredFor.Contains(j))));
            }

            if (request.DateType.HasValue && ((request.FromDate.HasValue && request.FromDate.Value != default) || (request.ToDate.HasValue && request.ToDate.Value != default)))
            {
                DateTime? fromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : DateTime.MinValue;
                DateTime? toDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : DateTime.MaxValue;
                switch (request.DateType)
                {
                    case DateType.ReceivedDate:
                        query = query.Where(i => i.CreatedOn >= fromDate.Value && i.CreatedOn <= toDate.Value);
                        break;
                    case DateType.ScheduledDate:
                        query = query.Where(i => i.ScheduledDate != null && i.ScheduledDate.Value >= fromDate.Value && i.ScheduledDate.Value <= toDate.Value);
                        break;
                    case DateType.ModifiedDate:
                        query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= fromDate.Value && i.LastModifiedOn.Value <= toDate.Value);
                        break;
                    case DateType.DeletedDate:
                        query = query.Where(i => i.ArchivedOn >= fromDate.Value && i.ArchivedOn < toDate.Value);
                        break;
                    case DateType.PickedDate:
                        query = query.Where(i => i.PickedDate >= fromDate.Value && i.PickedDate <= toDate.Value);
                        break;
                    case DateType.BookedDate:
                        query = query.Where(item => item.BookedDetails.Any(detail => detail.BookedDate.Value >= fromDate.Value && detail.BookedDate.Value <= toDate.Value));
                        break;
                    case DateType.AssignedDate:
                        query = query.Where(i => i.Assignments != null && i.Assignments.Any(i => i.AssignmentDate != null && i.AssignmentDate.Value >= fromDate.Value && i.AssignmentDate.Value <= toDate.Value));
                        break;
                    default:
                        break;
                }
            }
            /*  if (!string.IsNullOrWhiteSpace(request.SearchByNameOrNumber))
              {
                  query = query.Where(i => i.ContactNo.Contains(request.SearchByNameOrNumber.Replace(" ", "")) ||
                                           i.Name.ToLower().Contains(request.SearchByNameOrNumber.ToLower()) ||
                                           i.Email.ToLower().Contains(request.SearchByNameOrNumber.ToLower()) ||
                                           i.AlternateContactNo.Contains(request.SearchByNameOrNumber.Replace(" ", "")) ||
                                           i.SerialNumber.Contains(request.SearchByNameOrNumber.Replace(" ", "")));
              }*/
            if (!string.IsNullOrWhiteSpace(request.SearchByNameOrNumber) && request.PropertyToSearch?.Any() == true)
            {
                var searchTerm = request.SearchByNameOrNumber.ToLower().Trim().Replace(" ", "");
                var isPurposeValid = TryParseEnum<Purpose>(searchTerm, out var parsedPurpose);
                bool isSourceValid = TryParseEnum<LeadSource>(searchTerm, out var parsedSource);
                var isEnquiryTypeValid = TryParseEnum<EnquiryType>(searchTerm, out var parsedEnquiryType);
                var statusId = customMasterLeadStatus?.Where(i => i.DisplayName.ToLower().Trim().Replace(" ", "").Contains(searchTerm)).Select(i => i.Id).ToList();
                query = query.Where(i =>
                   (request.PropertyToSearch.Contains("LeadName") && i.Name != null && i.Name.ToLower().Trim().Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("ContactNo") && i.ContactNo != null && i.ContactNo.Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("SerialNumber") && i.SerialNumber != null && i.SerialNumber.Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("AlternateContactNo") && i.AlternateContactNo != null && i.AlternateContactNo.Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("Email") && i.Email != null && i.Email.ToLower().Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("Nationality") && i.Nationality != null && i.Nationality.ToLower().Trim().Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("PropertyName") && i.Properties.Any(a => a.Title.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("ProjectName") && i.Projects.Any(a => a.Name.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("SubSource") && i.Enquiries.Any(a => !string.IsNullOrEmpty(a.SubSource) && a.SubSource.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("Status") && (statusId.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || statusId.Contains(i.CustomLeadStatus.Id))) ||
                    (request.PropertyToSearch.Contains("Purpose") && isPurposeValid && i.Enquiries.Any(e => e.Purpose == parsedPurpose)) ||
                     (request.PropertyToSearch.Contains("Source") && isSourceValid && i.Enquiries.Any(e => e.LeadSource == parsedSource)) ||
                     (request.PropertyToSearch.Contains("EnquiredFor") && isEnquiryTypeValid && i.Enquiries.Any(e => e.EnquiryTypes.Contains(parsedEnquiryType))) ||
                   (request.PropertyToSearch.Contains("Location") && i.Enquiries.Any(e => e.Addresses.Any(j =>
                    (j.SubLocality +
                     j.Locality +
                     j.Community +
                     j.SubCommunity +
                     j.TowerName +
                     j.District +
                     j.City +
                     j.State +
                     j.Country +
                     j.PostalCode).Replace(",", "").ToLower().Trim().Replace(" ", "")
                    .Contains(searchTerm)))));
            }
            else if (!string.IsNullOrWhiteSpace(request.SearchByNameOrNumber))
            {
                query = query.Where(i => i.ContactNo.Contains(request.SearchByNameOrNumber.Replace(" ", "")) ||
                         i.Name.ToLower().Contains(request.SearchByNameOrNumber.ToLower()) ||
                         i.SerialNumber.ToLower().Contains(request.SearchByNameOrNumber.ToLower().Replace(" ", "")));
            }
            if (request.Source?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.Source.Contains(i.LeadSource)));
            }

            if (request.Properties?.Any() ?? false)
            {
                var propertyNames = request.Properties.Select(i => i.ToLower());
                query = query.Where(i => i.Properties.Count > 0 && i.Properties.Any(i => propertyNames.Contains(i.Title.ToLower())));
            }
            if (request.Projects?.Any() ?? false)
            {
                var projectNames = request.Projects.Select(i => i.ToLower());
                query = query.Where(i => i.Projects.Count > 0 && i.Projects.Any(i => projectNames.Contains(i.Name.ToLower()))).AsQueryable();
            }
            if (request.NoOfBHKs != null && request.NoOfBHKs.Any())
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.BHKs != null && i.BHKs.Any(j => request.NoOfBHKs.Contains(j))));
            }
            if (request.Beds?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Beds != null && i.Beds.Any(j => request.Beds.Contains(j))));
            }
            if (request.Baths?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Baths != null && i.Baths.Any(j => request.Baths.Contains(j))));
            }
            if (request.Floors?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Floors != null && i.Floors.Any(j => request.Floors.Contains(j))));
            }
            if (request.OfferTypes?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.OfferType != null && request.OfferTypes.Contains(i.OfferType.Value)));
            }
            if (request.Purposes?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Purpose != null && request.Purposes.Contains(i.Purpose.Value)));
            }
            if (request.Furnished?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Furnished != null && request.Furnished.Contains(i.Furnished.Value)));
            }
            if (request.SubSources != null && request.SubSources.Any())
            {
                request.SubSources = request.SubSources.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.SubSources.Contains(i.SubSource.ToLower().Trim())));
            }
            if (request.BHKTypes != null && request.BHKTypes.Any())
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.BHKTypes != null && i.BHKTypes.Any(j => request.BHKTypes.Contains(j))));
            }
            if (request.PropertyType != null && request.PropertyType.Any())
            {
                query = query.Where(i => i.Enquiries.Any(e => e.PropertyTypes.Any(i => request.PropertyType.Contains(i.BaseId ?? Guid.Empty))));
            }
            if (request.PropertySubType != null && request.PropertySubType.Any())
            {
                query = query.Where(i => i.Enquiries.Any(e => e.PropertyTypes.Any(i => request.PropertySubType.Contains(i.Id))));
            }
            #region Custom Filters
            if (filter != null)
            {
                if (!filter.Statuses?.Any() ?? true)
                {
                    query = query.Where(i => i.CustomLeadStatus != null);
                }
                else if (filter.IsForward != null)
                {
                    if (filter.IsForward ?? false)
                    {
                        if (filter.FromNoOfDays == 0 && filter.ToNoOfDays == 0)
                        {
                            query = query.Where(i => (i.ScheduledDate ?? DateTime.MinValue) >= DateTime.UtcNow.Date.AddMinutes(-330) && (i.ScheduledDate ?? DateTime.MinValue) < DateTime.UtcNow.Date.AddDays(1).AddMinutes(-330));
                        }
                        else if (filter.FromNoOfDays >= 0 && filter.ToNoOfDays > 0)
                        {
                            query = query.Where(i => (i.ScheduledDate ?? DateTime.MinValue) >= DateTime.UtcNow.Date.AddDays(filter.FromNoOfDays ?? 0).AddMinutes(-330) && (i.ScheduledDate ?? DateTime.MinValue) < DateTime.UtcNow.Date.AddDays(filter.ToNoOfDays ?? 0).AddMinutes(-330));
                        }
                        else if (filter.FromNoOfDays >= 0)
                        {
                            query = query.Where(i => (i.ScheduledDate ?? DateTime.MinValue) >= DateTime.UtcNow.Date.AddDays(filter.FromNoOfDays ?? 0).AddMinutes(-330));
                        }
                        else if (filter.ToNoOfDays >= 0)
                        {
                            query = query.Where(i => (i.ScheduledDate ?? DateTime.MinValue) >= DateTime.UtcNow.Date.AddMinutes(-330) && (i.ScheduledDate ?? DateTime.MinValue) < DateTime.UtcNow.Date.AddDays(filter.ToNoOfDays ?? 0).AddMinutes(-330));
                        }
                        else
                        {
                            query = query.Where(i => (i.ScheduledDate ?? DateTime.MinValue) >= DateTime.UtcNow.Date.AddMinutes(-330));
                        }
                        var statusId = filter.Statuses?.Select(i => i.Id)?.ToList() ?? new List<Guid>();
                        if (statusId?.Any() ?? false)
                        {
                            query = query.Where(i => i.CustomLeadStatus != null && statusId.Contains(i.CustomLeadStatus.Id));
                        }
                    }
                    else
                    {
                        if (filter.FromNoOfDays >= 0 && filter.ToNoOfDays > 0)
                        {
                            query = query.Where(i => (i.ScheduledDate ?? DateTime.MinValue) <= DateTime.UtcNow.Date.AddDays((-filter.FromNoOfDays) ?? 0).AddMinutes(-330) && (i.ScheduledDate ?? DateTime.MinValue) > DateTime.UtcNow.Date.AddDays((-filter.ToNoOfDays) ?? 0).AddMinutes(-330));
                        }
                        else if (filter.FromNoOfDays >= 0)
                        {
                            query = query.Where(i => (i.ScheduledDate ?? DateTime.MinValue) <= DateTime.UtcNow.Date.AddDays((-filter.FromNoOfDays) ?? 0).AddMinutes(-330));
                        }
                        else if (filter.ToNoOfDays >= 0)
                        {
                            query = query.Where(i => (i.ScheduledDate ?? DateTime.MinValue) <= DateTime.UtcNow.Date.AddMinutes(-330) && (i.ScheduledDate ?? DateTime.MinValue) > DateTime.UtcNow.Date.AddDays((-filter.ToNoOfDays) ?? 0).AddMinutes(-330));
                        }
                        else
                        {
                            query = query.Where(i => (i.ScheduledDate ?? DateTime.MinValue) < DateTime.UtcNow.Date.AddMinutes(-330));
                        }
                        var statusId = filter.Statuses?.Select(i => i.Id)?.ToList() ?? new List<Guid>();
                        if (statusId?.Any() ?? false)
                        {
                            query = query.Where(i => i.CustomLeadStatus != null && statusId.Contains(i.CustomLeadStatus.Id));
                        }
                    }
                }
                else if (filter.Statuses?.Any() ?? false)
                {
                    var statusId = filter.Statuses.Select(i => i.Id).ToList();
                    query = query.Where(i => i.CustomLeadStatus != null && statusId.Contains(i.CustomLeadStatus.Id));
                }
                else if (filter.Flags?.Any() ?? false)
                {
                    var flagNames = filter.Flags.Select(i => i.Name).ToList();
                    query = query.Where(i => i.CustomFlags != null && i.CustomFlags.Any(j => j.Flag != null && flagNames.Contains(j.Flag.Name ?? string.Empty)));
                }
            }
            #endregion
            var statusIds = new List<Guid>();
            statusIds.AddRange(request?.StatusIds ?? new List<Guid>());
            statusIds.AddRange(request?.SubStatuses ?? new List<Guid>());
            if (statusIds.Any())
            {
                query = query.Where(i => i.CustomLeadStatus != null && (statusIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) ||
                statusIds.Contains(i.CustomLeadStatus.Id)));
            }
            if (request.Budget != null && request.Budget.Any())
            {
                foreach (var budget in request.Budget)
                {
                    switch (budget)
                    {
                        case Budget.UpToTenLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget <= 1000000));
                            break;
                        case Budget.TenToTwentyLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 1000000 && j.UpperBudget <= 2000000));
                            break;
                        case Budget.TwentyToThirtyLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 2000000 && j.UpperBudget <= 3000000));
                            break;
                        case Budget.ThirtyToFourtyLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 3000000 && j.UpperBudget <= 4000000));
                            break;
                        case Budget.FourtyToFiftyLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 4000000 && j.UpperBudget <= 5000000));
                            break;
                        case Budget.FiftyToOneCrore:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 5000000 && j.UpperBudget <= 10000000));
                            break;
                        case Budget.MoreThanOneCrore:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 10000000));
                            break;
                    }
                }
            }
            if (request.Currency != null)
            {
                query = query.Where(i => i.Enquiries.Any(e => e.Currency == request.Currency));
            }
            if (request.MinBudget != null || request.MaxBudget != null)
            {
                if (request.MinBudget != null && request.MaxBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(i => (i.UpperBudget >= request.MinBudget && i.UpperBudget <= request.MaxBudget) || (i.LowerBudget >= request.MinBudget && i.LowerBudget <= request.MaxBudget)));
                }
                else if (request.MinBudget != null && request.MaxBudget == null)
                {
                    query = query.Where(i => i.Enquiries.Any(i => (i.UpperBudget >= request.MinBudget) || (i.LowerBudget >= request.MinBudget)));
                }
                else if (request.MinBudget == null && request.MaxBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(i => (i.UpperBudget <= request.MaxBudget) || (i.LowerBudget <= request.MaxBudget)));
                }
            }
            if (request.BudgetFilters != null && request.BudgetFilters.Any())
            {
                foreach (var budget in request.BudgetFilters)
                {
                    query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.UpperBudget >= budget.MinBudget && i.UpperBudget <= budget.MaxBudget));
                }
            }
            if (request.Locations != null && request.Locations.Any())
            {
                request.Locations = request.Locations.ConvertAll(i => Uri.UnescapeDataString(i).Replace(",", "").ToLower().Trim().Replace(" ", "")).ToList();
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Any(j =>
                request.Locations.Contains(
                    (j.SubLocality +
                    j.Locality +
                    j.Community +
                    j.SubCommunity +
                    j.TowerName +
                    j.District +
                    j.City +
                    j.State +
                    j.Country +
                    j.PostalCode).Replace(",", "").ToLower().Trim().Replace(" ", "")))));

            }
            if (request.IntegrationAccountIds != null && request.IntegrationAccountIds.Any())
            {
                query = query.Where(i => request.IntegrationAccountIds.Contains(i.AccountId));
            }
            if (request.AgencyNames != null && request.AgencyNames.Any())
            {
                request.AgencyNames = request.AgencyNames.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.Agencies.Any(i => i.Name != null && request.AgencyNames.Contains(i.Name.ToLower().Trim())));
            }
            if (request.CampaignNames != null && request.CampaignNames.Any())
            {
                request.CampaignNames = request.CampaignNames.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.Campaigns.Any(i => i.Name != null && request.CampaignNames.Contains(i.Name.ToLower().Trim())));
            }
            if (request?.LeadIds?.Any() ?? false)
            {
                query = query.Where(i => request.LeadIds.Contains(i.Id));
            }
            if (request?.SerialNumbers?.Any() ?? false)
            {
                query = query.Where(i => request.SerialNumbers.Contains(i.SerialNumber));
            }

            if (request?.BookedByIds?.Any() ?? false)
            {
                query = query.Where(i => i.BookedDetails.Any(i => request.BookedByIds.Contains(i.BookedBy.Value)));
            }
            if (request?.CallStatus != null)
            {
                query = query.Where(i => i.LeadCallLogs != null && i.LeadCallLogs.Any(j => j.CallStatus == request.CallStatus));
            }
            if (request?.CallDirection != null)
            {
                query = query.Where(i => i.LeadCallLogs != null && i.LeadCallLogs.Any(j => j.CallDirection == request.CallDirection));
            }
            if ((request.AssignFromIds?.Any() ?? false) && !(request?.IsWithHistory ?? false))
            {
                query = query.Where(i => i.AssignedFrom != null && request.AssignFromIds.Contains(i.AssignedFrom.Value));
            }
            if ((request?.SecondaryUsers?.Any() ?? false) && (request?.IsDualOwnershipEnabled ?? false) && !(request?.IsWithHistory ?? false))
            {
                query = query.Where(i => i.SecondaryUserId != null && request.SecondaryUsers.Contains(i.SecondaryUserId.Value));
            }
            if (request?.OriginalOwnerIds?.Any() ?? false)
            {
                query = query.Where(i => i.OriginalOwner != null && request.OriginalOwnerIds.Contains(i.OriginalOwner.Value));
            }
            if ((request?.SecondaryFromIds?.Any() ?? false) && (request?.IsDualOwnershipEnabled ?? false) && !(request?.IsWithHistory ?? false))
            {
                query = query.Where(i => i.SecondaryFromUserId != null && request.SecondaryFromIds.Contains(i.SecondaryFromUserId.Value));
            }
            if (request?.IsWithHistory ?? false)
            {
                DateTime? fromDate = null;
                DateTime? toDate = null;

                if (request.DateType.HasValue && request.FromDate.HasValue && request.FromDate.Value != default && request.ToDate.HasValue && request.ToDate.Value != default)
                {
                    fromDate = request.FromDate.Value.ConvertFromDateToUtc();
                    toDate = request.ToDate.Value.ConvertToDateToUtc();
                }
                query = query.Where(i => i.Assignments != null && i.Assignments.Any(j =>
                    (request.HistoryAssignedToIds == null || !request.HistoryAssignedToIds.Any() || (j.AssignTo != null && request.HistoryAssignedToIds.Contains(j.AssignTo ?? Guid.Empty))) &&
                    (request.AssignFromIds == null || !request.AssignFromIds.Any() || (j.AssignedFrom != null && request.AssignFromIds.Contains(j.AssignedFrom ?? Guid.Empty))) &&
                    (request.SecondaryUsers == null || !request.SecondaryUsers.Any() || (j.SecondaryAssignTo != null && request.SecondaryUsers.Contains(j.SecondaryAssignTo ?? Guid.Empty))) &&
                    (request.SecondaryFromIds == null || !request.SecondaryFromIds.Any() || (j.SecondaryAssignFrom != null && request.SecondaryFromIds.Contains(j.SecondaryAssignFrom ?? Guid.Empty))) &&
                    (request.DoneBy == null || !request.DoneBy.Any() || request.DoneBy.Contains(j.LastModifiedBy)) &&
                    (fromDate == null || toDate == null ||
                    (j.AssignmentDate.HasValue && j.AssignmentDate.Value >= fromDate.Value && j.AssignmentDate.Value <= toDate.Value))
                ));
            }
            if (request?.DoneBy?.Any() ?? false)
            {
                DateTime? fromDate = null;
                DateTime? toDate = null;

                if (request.DateType.HasValue && request.FromDate.HasValue && request.FromDate.Value != default && request.ToDate.HasValue && request.ToDate.Value != default)
                {
                    fromDate = request.FromDate.Value.ConvertFromDateToUtc();
                    toDate = request.ToDate.Value.ConvertToDateToUtc();
                }

                query = query.Where(i => i.Assignments != null && i.Assignments.Any(j =>
                    request.DoneBy.Contains(j.LastModifiedBy) &&
                    (fromDate == null || toDate == null || (j.AssignmentDate.HasValue && j.AssignmentDate.Value >= fromDate.Value && j.AssignmentDate.Value <= toDate.Value))
                ));
            }
            if (request.Cities?.Any() ?? false)
            {
                var normalizedCityNames = request.Cities.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedCityNames.Contains(i.City.ToLower().Trim().Replace(" ", ""))).Any()));
            }

            if (request.States?.Any() ?? false)
            {
                var normalizedStateNames = request.States.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.State.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.Countries?.Any() ?? false)
            {
                var normalizedStateNames = request.Countries.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.Country.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.Communities?.Any() ?? false)
            {
                var normalizedStateNames = request.Communities.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.Community.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.SubCommunities?.Any() ?? false)
            {
                var normalizedStateNames = request.SubCommunities.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.SubCommunity.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.TowerNames?.Any() ?? false)
            {
                var normalizedStateNames = request.TowerNames.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.TowerName.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.PostalCodes?.Any() ?? false)
            {
                var normalizedStateNames = request.PostalCodes.ConvertAll(i => i.Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.PostalCode.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (!string.IsNullOrEmpty(request?.AdditionalPropertiesKey))
            {
                var key = request.AdditionalPropertiesKey;

                if (!string.IsNullOrEmpty(request.AdditionalPropertiesValue))
                {
                    var value = request.AdditionalPropertiesValue;
                    query = query
                        .Where(lead => EF.Functions.JsonContains(
                            lead.AdditionalProperties,
                            JsonConvert.SerializeObject(new Dictionary<string, string> { { key, value } })));
                }
                else
                {
                    query = query
                        .Where(lead => EF.Functions.JsonContains(
                            lead.AdditionalProperties,
                            JsonConvert.SerializeObject(new Dictionary<string, string> { { key, "" } })));
                }
            }
            if (request.ChannelPartnerNames != null && request.ChannelPartnerNames.Any())
            {
                request.ChannelPartnerNames = request.ChannelPartnerNames.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.ChannelPartners.Any(i => i.FirmName != null && request.ChannelPartnerNames.Contains(i.FirmName.ToLower().Trim())));
            }
            if (request.LastModifiedByIds?.Any() ?? false)
            {
                query = query.Where(i => request.LastModifiedByIds.Contains(i.LastModifiedBy));
            }
            if (request.CreatedByIds?.Any() ?? false)
            {
                query = query.Where(i => request.CreatedByIds.Contains(i.CreatedBy));
            }

            if (request.RestoredByIds?.Any() ?? false)
            {
                query = query.Where(i => request.RestoredByIds.Contains(i.RestoredBy.Value));
            }
            if (request.ArchivedByIds?.Any() ?? false)
            {
                query = query.Where(i => request.ArchivedByIds.Contains(i.ArchivedBy.Value));
            }
            if (request.SourcingManagers?.Any() ?? false)
            {
                query = query.Where(i => request.SourcingManagers.Contains(i.SourcingManager.Value));
            }
            if (request.ClosingManagers?.Any() ?? false)
            {
                query = query.Where(i => request.ClosingManagers.Contains(i.ClosingManager.Value));
            }
            if (request.Profession != null && request.Profession.Any())
            {
                query = query.Where(i => request.Profession.Contains(i.Profession));
            }
            if (!string.IsNullOrEmpty(request?.UploadTypeName))
            {
                query = query.Where(i => i.UploadTypeName != null && i.UploadTypeName.ToLower().Contains(request.UploadTypeName.ToLower().Trim()));
            }
            if (request?.LeadType != null && request.LeadType.Any())
            {
                var predicate = PredicateBuilder.New<Lead>(false);

                if (request.LeadType.Contains(LeadType.ShowPrimeLeads))
                {
                    predicate = predicate.Or(i => i.ChildLeadsCount == 0 && i.RootId == null && i.ParentLeadId == null);
                }

                if (request.LeadType.Contains(LeadType.ShowOnlyParentLeads))
                {
                    if (request.ChildLeadsCount != null && request.ChildLeadsCount > 0)
                    {
                        predicate = predicate.Or(i => i.ChildLeadsCount == request.ChildLeadsCount);
                    }
                    else
                    {
                        predicate = predicate.Or(i => i.ChildLeadsCount > 0);
                    }
                }

                if (request.LeadType.Contains(LeadType.ShowOnlyDuplicateLeads))
                {
                    predicate = predicate.Or(i => i.ChildLeadsCount == 0 && (i.RootId != null || i.ParentLeadId != null));
                }

                query = query.Where(predicate);
            }
            if (request?.IsUntouched != null && request.IsUntouched != default)
            {
                query = query.Where(i => !i.IsPicked == request.IsUntouched.Value);
            }
            if (request?.PossesionType != null && request?.PossesionType != PossesionType.None)
            {

                switch (request?.PossesionType)
                {
                    case PossesionType.UnderConstruction:
                        query = query.Where(i => i.PossesionType == PossesionType.UnderConstruction);
                        break;


                    case PossesionType.SixMonth:
                        query = query.Where(i => i.PossesionType == PossesionType.SixMonth);
                        break;

                    case PossesionType.Year:
                        query = query.Where(i => i.PossesionType == PossesionType.Year);
                        break;

                    case PossesionType.TwoYears:
                        query = query.Where(i => i.PossesionType == PossesionType.TwoYears);
                        break;
                    case PossesionType.CustomDate:
                        DateTime? tempToPossesionDate = request?.ToPossesionDate?.ConvertToDateToUtc();
                        DateTime? tempFrompossesionDate = request?.FromPossesionDate?.ConvertFromDateToUtc();
                        query = query.Where(i => i.Enquiries.Any(j => j.PossessionDate != null && j.PossessionDate >= tempFrompossesionDate.Value && j.PossessionDate <= tempToPossesionDate.Value));
                        break;

                    case PossesionType.ThreeMonth:
                        query = query.Where(i => i.PossesionType == PossesionType.ThreeMonth);
                        break;

                    case PossesionType.Immediate:
                        query = query.Where(i => i.PossesionType == PossesionType.Immediate);
                        break;
                }
            }
            
            if (request?.LandLine != null && request.LandLine.Any())
            {
                query = query.Where(i => i.LandLine != null && request.LandLine.Contains(i.LandLine.Trim()));
            }
            if (request?.CountryCode?.Any() ?? false)
            {
                var codes = request.CountryCode
        .Where(c => !string.IsNullOrWhiteSpace(c))
        .Select(c => c.Trim().TrimStart('+'))
        .Distinct()
        .ToList();

                Expression<Func<Lead, bool>> leadFilter = lead => false;

                foreach (var code in codes)
                {
                    var local = code;

                    leadFilter = leadFilter.Or(lead =>
                        (lead.CountryCode != null && lead.CountryCode.Trim().TrimStart('+') == local) ||
                        (lead.ContactNo != null &&
                            (lead.ContactNo.StartsWith("+" + local) || lead.ContactNo.StartsWith(local)))
                    );
                }

                query = query.Where(leadFilter);
            }
            if (request?.AltCountryCode?.Any() ?? false)
            {
                query = query.Where(i => i.AltCountryCode != null && request.AltCountryCode.Contains(i.AltCountryCode));
            }
            if (request?.GenderTypes?.Any() ?? false)
            {
                query = query.Where(i => i.Gender != null && request.GenderTypes.Contains(i.Gender.Value));
            }
            if (request?.MaritalStatuses?.Any() ?? false)
            {
                query = query.Where(i => i.MaritalStatus != null && request.MaritalStatuses.Contains(i.MaritalStatus.Value));
            }
            if (request?.DateOfBirth != null)
            {
                query = query.Where(i => i.DateOfBirth != null && i.DateOfBirth == request.DateOfBirth);
            }

            if (request?.CallDirections != null && request.CallDirections.Any() && !request.CallDirections.Contains(CallDirection.None))
            {
                query = query.Where(i =>
                    i.LeadCallLogs != null && i.LeadCallLogs.Any(j => request.CallDirections.Contains(j.CallDirection)));
            }

            if (request?.CallStatuses != null && request.CallStatuses.Any() && !request.CallStatuses.Contains(CallStatus.None))
            {
                query = query.Where(i =>
                    i.LeadCallLogs != null && i.LeadCallLogs.Any(j => request.CallStatuses.Contains(j.CallStatus)));
            }

            if (request?.UserIds != null && request.UserIds.Any())
            {
                query = query.Where(i =>
                    i.LeadCallLogs != null && i.LeadCallLogs.Any(j => request.UserIds.Contains(j.UserId)));
            }
            if (request?.AnniversaryDate != null)
            {
                query = query.Where(i => i.AnniversaryDate != null && i.AnniversaryDate == request.AnniversaryDate);
            }
            return query.AsQueryable();
        }
       
    }
}
