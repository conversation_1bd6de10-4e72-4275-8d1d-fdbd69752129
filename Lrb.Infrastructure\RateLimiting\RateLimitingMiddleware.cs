using System.Text.Json;
using Lrb.Infrastructure.RateLimiting.Configuration;
using Lrb.Infrastructure.RateLimiting.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Lrb.Infrastructure.RateLimiting;

/// <summary>
/// Middleware for rate limiting HTTP requests
/// </summary>
public class RateLimitingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly RateLimitingSettings _settings;
    private readonly ILogger<RateLimitingMiddleware> _logger;

    public RateLimitingMiddleware(
        RequestDelegate next,
        IOptions<RateLimitingSettings> settings,
        ILogger<RateLimitingMiddleware> logger)
    {
        _next = next;
        _settings = settings.Value;
        _logger = logger;
    }

    public async Task InvokeAsync(
        HttpContext context,
        IRateLimitingService rateLimitingService,
        IClientIdentifierService clientIdentifierService)
    {
        // Skip rate limiting if disabled
        if (!_settings.Enabled)
        {
            await _next(context);
            return;
        }

        // Check if path is excluded
        if (clientIdentifierService.IsPathExcluded(context.Request.Path))
        {
            await _next(context);
            return;
        }

        // Check if client is whitelisted
        if (clientIdentifierService.IsWhitelisted(context))
        {
            await _next(context);
            return;
        }

        var clientId = clientIdentifierService.GetClientIdentifier(context);

        try
        {
            var result = await rateLimitingService.CheckRateLimitAsync(clientId, context.RequestAborted);

            // Add rate limit headers if enabled
            if (_settings.IncludeHeaders)
            {
                AddRateLimitHeaders(context, result);
            }

            if (result.IsAllowed)
            {
                // Request is allowed, continue to next middleware
                await _next(context);
                return;
            }

            if (result.IsQueued && result.QueueTask != null)
            {
                // Request is queued, wait for it to be processed
                _logger.LogInformation("Request queued for client {ClientId}. Position: {Position}",
                    clientId, result.QueuePosition);

                try
                {
                    var queueResult = await result.QueueTask;
                    if (queueResult)
                    {
                        // Request was processed from queue, continue
                        await _next(context);
                        return;
                    }
                }
                catch (OperationCanceledException)
                {
                    _logger.LogWarning("Queued request cancelled for client {ClientId}", clientId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing queued request for client {ClientId}", clientId);
                }
            }

            // Rate limit exceeded, return 429 response
            await HandleRateLimitExceeded(context, result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in rate limiting middleware for client {ClientId}", clientId);

            // Continue to next middleware on error to avoid blocking requests
            await _next(context);
        }
    }

    private void AddRateLimitHeaders(HttpContext context, RateLimitResult result)
    {
        var headers = context.Response.Headers;

        headers["X-RateLimit-Limit"] = result.RateLimitInfo.RequestLimit.ToString();
        headers["X-RateLimit-Remaining"] = result.RateLimitInfo.RemainingRequests.ToString();
        headers["X-RateLimit-Reset"] = ((DateTimeOffset)result.RateLimitInfo.WindowEnd).ToUnixTimeSeconds().ToString();

        if (result.RetryAfterSeconds.HasValue)
        {
            headers["Retry-After"] = result.RetryAfterSeconds.Value.ToString();
        }

        if (result.IsQueued)
        {
            headers["X-RateLimit-Queue-Position"] = result.QueuePosition?.ToString() ?? "0";
            headers["X-RateLimit-Queue-Time"] = result.EstimatedWaitTime?.TotalSeconds.ToString("F0") ?? "0";
        }
    }

    private async Task HandleRateLimitExceeded(HttpContext context, RateLimitResult result)
    {
        context.Response.StatusCode = _settings.StatusCode;
        context.Response.ContentType = "application/json";

        var response = new
        {
            error = "Rate limit exceeded",
            message = _settings.CustomMessage ?? "Too many requests. Please try again later.",
            details = new
            {
                limit = result.RateLimitInfo.RequestLimit,
                remaining = result.RateLimitInfo.RemainingRequests,
                resetTime = result.RateLimitInfo.WindowEnd,
                retryAfterSeconds = result.RetryAfterSeconds,
                isQueued = result.IsQueued,
                queuePosition = result.QueuePosition,
                estimatedWaitTime = result.EstimatedWaitTime?.TotalSeconds
            }
        };

        var json = JsonSerializer.Serialize(response, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        });

        _logger.LogWarning("Rate limit exceeded for client. Limit: {Limit}, Remaining: {Remaining}",
            result.RateLimitInfo.RequestLimit, result.RateLimitInfo.RemainingRequests);

        await context.Response.WriteAsync(json);
    }
}