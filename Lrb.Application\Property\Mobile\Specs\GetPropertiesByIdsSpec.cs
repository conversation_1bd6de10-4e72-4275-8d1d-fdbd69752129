﻿using Lrb.Application.Property.Mobile.Dtos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Property.Mobile.Specs
{
    public class GetPropertiesByIdsSpec : Specification<Domain.Entities.Property>
    {
        public GetPropertiesByIdsSpec(List<Guid> propertyIds)
        {
            Query.Where(i => propertyIds.Contains(i.Id) && !i.IsDeleted);
        }
    }
    public class GetPropertyByNameSpecs : Specification<Lrb.Domain.Entities.Property, PropertyTitleDto>
    {
        public GetPropertyByNameSpecs(string? propertyName)
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived && i.Title.ToLower().Trim() == propertyName.ToLower().Trim());
        }
    }
}
