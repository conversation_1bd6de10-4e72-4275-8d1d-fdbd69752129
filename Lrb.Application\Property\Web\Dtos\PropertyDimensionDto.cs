﻿namespace Lrb.Application.Property.Web
{
    public class PropertyDimensionDto : IDto
    {
        public Guid Id { get; set; }
        public double? Area { get; set; }
        public Guid? AreaUnitId { get; set; }
        public float? ConversionFactor { get; set; }
        public string? Unit { get; set; }
        public double? Length { get; set; }
        public double? Breadth { get; set; }
        public double? CarpetArea { get; set; }
        public double? MinCarpetArea { get; set; }
        public double? MaxCarpetArea { get; set; }
        public Guid? CarpetAreaId { get; set; }
        public float? CarpetAreaConversionFactor { get; set; }
        public double? BuildUpArea { get; set; }
        public double? MinBuildUpArea { get; set; }
        public double? MaxBuildUpArea { get; set; }
        public Guid? BuildUpAreaId { get; set; }
        public float? BuildUpConversionFactor { get; set; }
        public double? SaleableArea { get; set; }
        public double? MinSaleableArea { get; set; }
        public double? MaxSaleableArea { get; set; }
        public Guid? SaleableAreaId { get; set; }
        public float? SaleableAreaConversionFactor { get; set; }
        public double? CommonAreaCharges { get; set; }
        public Guid? CommonAreaChargesId { get; set; }
        public double? CommonAreaChargesInSqMtr { get; set; }
        public string? Currency { get; set; } = "INR";
        public double? NetArea { get; set; }
        public double? MinNetArea { get; set; }
        public double? MaxNetArea { get; set; }
        public Guid? NetAreaUnitId { get; set; }
        public float? NetAreaConversionFactor { get; set; }
        public double? PropertyArea { get; set; }
        public double? MinPropertyArea { get; set; }
        public double? MaxPropertyArea { get; set; }
        public Guid? PropertyAreaUnitId { get; set; }
    }
}
