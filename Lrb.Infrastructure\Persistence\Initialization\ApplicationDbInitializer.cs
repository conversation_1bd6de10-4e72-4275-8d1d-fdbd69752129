using Finbuckle.MultiTenant;
using Lrb.Infrastructure.Persistence.Context;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Lrb.Infrastructure.Persistence.Initialization;

internal class ApplicationDbInitializer
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ITenantInfo _currentTenant;
    private readonly ApplicationDbSeeder _dbSeeder;
    private readonly ILogger<ApplicationDbInitializer> _logger;

    public ApplicationDbInitializer(ApplicationDbContext dbContext, ITenantInfo currentTenant, ApplicationDbSeeder dbSeeder, ILogger<ApplicationDbInitializer> logger)
    {
        _dbContext = dbContext;
        _currentTenant = currentTenant;
        _dbSeeder = dbSeeder;
        _logger = logger;
    }

    public async Task InitializeAsync(CancellationToken cancellationToken)
    {
#if !DEBUG
        var migrations = _dbContext.Database.GetMigrations();
        if (migrations.Any())
        {
            var pendingMigrations = await _dbContext.Database.GetPendingMigrationsAsync(cancellationToken);
            if (pendingMigrations.Any())
            {
                _logger.LogInformation("Applying Migrations for '{tenantId}' tenant.", _currentTenant.Id);
                try
                {
                    await _dbContext.Database.MigrateAsync(cancellationToken);
                }
                catch (Exception ex)
                {
                }

            }

            //if (await _dbContext.Database.CanConnectAsync(cancellationToken))
            //{
            //    _logger.LogInformation("Connection to {tenantId}'s Database Succeeded.", _currentTenant.Id);

            //    await _dbSeeder.SeedDatabaseAsync(_dbContext, cancellationToken);
            //}
        }
#endif
    }
    public async Task SeedDatabaseAsync(CancellationToken cancellationToken)
    {
        if (await _dbContext.Database.CanConnectAsync(cancellationToken))
        {
            _logger.LogInformation("Connection to {tenantId}'s Database Succeeded.", _currentTenant.Id);

            await _dbSeeder.SeedDatabaseAsync(_dbContext, cancellationToken);
        }
    }
}
