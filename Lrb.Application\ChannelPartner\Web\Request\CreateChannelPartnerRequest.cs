﻿using Lrb.Application.ChannelPartner.Web.Dtos;
using Lrb.Application.ChannelPartner.Web.Specs;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Lead.Web;
using Lrb.Application.Project.Web.Requests.CommonHandler;
using GetChannelPartnerByNameSpecs = Lrb.Application.ChannelPartner.Web.Specs.GetChannelPartnerByNameSpecs;

namespace Lrb.Application.ChannelPartner.Web.Request
{
    public class CreateChannelPartnerRequest : CreateOrUpdateChannelPartnerDto, IRequest<Response<Guid>>
    {
    }

    public class CreateChannelPartnerRequestHandler : ProjectCommonRequestHandler, IRequestHandler<CreateChannelPartnerRequest, Response<Guid>>
    {
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.ChannelPartner> _cpRepository;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.GlobalSettings> _globalsettingRepo;
        public CreateChannelPartnerRequestHandler(IRepositoryWithEvents<Domain.Entities.ChannelPartner> cpRepository,
            IServiceProvider serviceProvider,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalsettingRepo) : base(serviceProvider)
        {
            _cpRepository = cpRepository;
            _globalsettingRepo = globalsettingRepo;
        }

        public async Task<Response<Guid>> Handle(CreateChannelPartnerRequest request, CancellationToken cancellationToken)
        {
            var channelPartner = await _cpRepository.ListAsync(new GetChannelPartnerByNameSpecs(request.FirmName));
            if (!channelPartner?.Any() ?? false)
            {
                var address = await CreateAddressAsync(request.Address, cancellationToken);
                Domain.Entities.ChannelPartner cp = request.Adapt<Domain.Entities.ChannelPartner>();
                cp.Address = address;
                List<Lrb.Domain.Entities.Project>? projects = new();
                Domain.Entities.GlobalSettings? globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                var projectList = (request.ProjectsList?.Any() ?? false) ? request.ProjectsList?.Where(i => !string.IsNullOrWhiteSpace(i)).ToList() : null;
                if (projectList?.Any() ?? false)
                {
                    foreach (var newProject in projectList)
                    {
                        Lrb.Domain.Entities.Project? existingProject = (await _projectRepo.ListAsync(new GetNewProjectsByIdV2Spec(newProject), cancellationToken)).FirstOrDefault();
                        if (existingProject != null)
                        {
                            projects.Add(existingProject);
                        }
                        else
                        {
                            Lrb.Domain.Entities.Project project = new()
                            {
                                Name = newProject,
                                MonetaryInfo = new ProjectMonetaryInfo
                                {
                                    Currency = globalSettings?.Countries?.FirstOrDefault()?.DefaultCurrency ?? "INR"
                                }
                            };
                            project = await _projectRepo.AddAsync(project, cancellationToken);
                            projects.Add(project);
                        }
                    }
                    cp.Projects = projects;
                }
                else if ((cp.Projects?.Any() ?? false) && projectList == null)
                {
                    cp.Projects = projects;
                }
                var newChannelPartner = await _cpRepository.AddAsync(cp, cancellationToken);
                return new(newChannelPartner.Id);
            }
            else
            {
                throw new Exception("Channel Partner already exists");
            }
        }
    }
}
