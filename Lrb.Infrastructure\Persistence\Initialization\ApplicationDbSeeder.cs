﻿using Ardalis.Specification;
using Ardalis.Specification.EntityFrameworkCore;
using Dapper;
using DocumentFormat.OpenXml.Wordprocessing;
using Lrb.Application.Common.SecretsManager;
using Lrb.Infrastructure.Identity;
using Lrb.Infrastructure.Multitenancy;
using Lrb.Infrastructure.Persistence.Context;
using Lrb.Shared.Authorization;
using Lrb.Shared.Multitenancy;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Npgsql;

namespace Lrb.Infrastructure.Persistence.Initialization;

internal class ApplicationDbSeeder
{
    private readonly LrbTenantInfo _currentTenant;
    private readonly RoleManager<ApplicationRole> _roleManager;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly CustomSeederRunner _seederRunner;
    private readonly ILogger<ApplicationDbSeeder> _logger;
    private readonly ISecretsManagerService _secretsManagerService;
    private readonly DatabaseSettings _settings;

    public ApplicationDbSeeder(LrbTenantInfo currentTenant, RoleManager<ApplicationRole> roleManager, UserManager<ApplicationUser> userManager, CustomSeederRunner seederRunner, ILogger<ApplicationDbSeeder> logger, ISecretsManagerService secretsManagerService, IOptions<DatabaseSettings> options)
    {
        _currentTenant = currentTenant;
        _roleManager = roleManager;
        _userManager = userManager;
        _seederRunner = seederRunner;
        _logger = logger;
        _secretsManagerService = secretsManagerService;
        _settings = options.Value;
    }

    public async Task SeedDatabaseAsync(ApplicationDbContext dbContext, CancellationToken cancellationToken)
    {
        await SeedRolesAsync(dbContext);
        await SeedAdminUserAsync();
        await _seederRunner.RunSeedersAsync(cancellationToken);
        await SeedStoredProceduresAndFunctionsAsync(dbContext);
        // await SeedMasterTemplatesAsync();
    }

    private async Task SeedRolesAsync(ApplicationDbContext dbContext)
    {
        foreach (string roleName in LrbRoles.DefaultRoles)
        {
            if (await _roleManager.Roles.SingleOrDefaultAsync(r => r.Name == roleName)
                is not ApplicationRole role)
            {
                // Create the role
                _logger.LogInformation("Seeding {role} Role for '{tenantId}' Tenant.", roleName, _currentTenant.Id);
                role = new ApplicationRole(roleName, $"{roleName} Role for {_currentTenant.Id} Tenant");
                await _roleManager.CreateAsync(role);
            }

            // Assign permissions
            switch (roleName)
            {
                case LrbRoles.Basic:
                    await AssignPermissionsToRoleAsync(dbContext, LrbPermissions.Basic, role);
                    break;
                case LrbRoles.Admin:
                    await AssignPermissionsToRoleAsync(dbContext, LrbPermissions.Admin, role);
                    if (_currentTenant.Id == MultitenancyConstants.Root.Id)
                    {
                        await AssignPermissionsToRoleAsync(dbContext, LrbPermissions.Root, role);
                    }
                    break;
                case LrbRoles.HR:
                    await AssignPermissionsToRoleAsync(dbContext, LrbPermissions.HR, role);
                    break;
                case LrbRoles.Manager:
                    await AssignPermissionsToRoleAsync(dbContext, LrbPermissions.Manager, role);
                    break;
                case LrbRoles.SalesExecutive:
                    await AssignPermissionsToRoleAsync(dbContext, LrbPermissions.SalesExecutive, role);
                    break;
                case LrbRoles.Default:
                    await AssignPermissionsToRoleAsync(dbContext, LrbPermissions.Default, role);
                    break;
                default:
                    break;
            }
        }
    }

    private async Task AssignPermissionsToRoleAsync(ApplicationDbContext dbContext, IReadOnlyList<LrbPermission> permissions, ApplicationRole role)
    {
        var currentClaims = await _roleManager.GetClaimsAsync(role);
        var seededPermissions = dbContext.SeededRolePermissions.Where(i => i.RoleId == role.Id).ToList();
        var seededClaimValues = seededPermissions.Select(i => i.ClaimValue).ToList();
        var maxClaimId = await GetRoleClaimsMaxIdAsync();
        foreach (var permission in permissions)
        {
            if (!currentClaims.Any(c => c.Type == LrbClaims.Permission && c.Value == permission.Name)
                && !seededClaimValues.Contains(permission.Name))
            {
                _logger.LogInformation("Seeding {role} Permission '{permission}' for '{tenantId}' Tenant.", role.Name, permission.Name, _currentTenant.Id);
                dbContext.RoleClaims.Add(new ApplicationRoleClaim
                {
                    Id = maxClaimId + 1,
                    RoleId = role.Id,
                    ClaimType = LrbClaims.Permission,
                    ClaimValue = permission.Name,
                    CreatedBy = "ApplicationDbSeeder"
                });
                await dbContext.SaveChangesAsync();
                maxClaimId++;
            }
            if (!seededClaimValues.Contains(permission.Name))
            {
                dbContext.SeededRolePermissions.Add(new()
                {
                    RoleId = role.Id,
                    ClaimValue = permission.Name,
                });
                await dbContext.SaveChangesAsync();
            }
        }
    }
    public async Task<int> GetRoleClaimsMaxIdAsync()
    {
        var setting = string.IsNullOrEmpty(_currentTenant?.ConnectionString) ? _settings.ConnectionString : _currentTenant.ConnectionString;
        var conn = new NpgsqlConnection(setting);
        var query = $"Select Max(\"Id\") from \"Identity\".\"RoleClaims\"";
        var res = await conn.QueryFirstOrDefaultAsync<int>(query);
        return res;
    }
    private async Task SeedAdminUserAsync()
    {
        if (string.IsNullOrWhiteSpace(_currentTenant.Id) || string.IsNullOrWhiteSpace(_currentTenant.AdminEmail))
        {
            return;
        }

        if (await _userManager.Users.FirstOrDefaultAsync(u => u.Email == _currentTenant.AdminEmail)
            is not ApplicationUser adminUser)
        {
            string adminUserName = $"{_currentTenant.Id.Trim()}.{LrbRoles.Admin}".ToLowerInvariant();
            adminUser = new ApplicationUser
            {
                FirstName = _currentTenant.Id.Trim().ToLowerInvariant(),
                LastName = LrbRoles.Admin,
                Email = _currentTenant.AdminEmail,
                UserName = adminUserName,
                EmailConfirmed = true,
                PhoneNumberConfirmed = true,
                NormalizedEmail = _currentTenant.AdminEmail?.ToUpperInvariant(),
                NormalizedUserName = adminUserName.ToUpperInvariant(),
                IsActive = true
            };

            _logger.LogInformation("Seeding Default Admin User for '{tenantId}' Tenant.", _currentTenant.Id);
            var password = new PasswordHasher<ApplicationUser>();
            adminUser.PasswordHash = password.HashPassword(adminUser, MultitenancyConstants.DefaultPassword);
            await _userManager.CreateAsync(adminUser);
        }

        // Assign role to user
        if (!await _userManager.IsInRoleAsync(adminUser, LrbRoles.Admin))
        {
            _logger.LogInformation("Assigning Admin Role to Admin User for '{tenantId}' Tenant.", _currentTenant.Id);
            await _userManager.AddToRoleAsync(adminUser, LrbRoles.Admin);
        }
    }

    private async Task SeedStoredProceduresAndFunctionsAsync(ApplicationDbContext dbContext)
    {
        try
        {
           // var secretString = await _secretsManagerService.GetSecretAsync("Lrb/HaveToUpdateSP");
            //var haveToUpdateSP = bool.Parse(string.IsNullOrWhiteSpace(secretString) ? "false" : secretString);
            if (false)
            {
                var conn = dbContext.Connection;
                var createScriptPath = Path.Combine(Directory.GetCurrentDirectory(), "Files\\Database\\StoredProceduresAndFunctions\\CreateScripts\\");
                var createWithDropScriptPath = Path.Combine(Directory.GetCurrentDirectory(), "Files\\Database\\StoredProceduresAndFunctions\\CreateWithDropScripts\\");
                var sqlFiles = Directory.EnumerateFiles(createScriptPath, "*.txt");
                var sqlFilesWithDrop = Directory.EnumerateFiles(createWithDropScriptPath, "*.txt");

                foreach (var sqlFile in sqlFiles)
                {
                    try
                    {
                        if (!string.IsNullOrWhiteSpace(sqlFile))
                        {
                            string contents = await File.ReadAllTextAsync(sqlFile);
                            await conn.ExecuteAsync(contents);
                        }
                    }
                    catch (Exception ex)
                    {
                        if (ex.Message.Contains("cannot change return type of existing function"))
                        {
                            var values = ex.Data.Values;
                            var dropStatement = values.Cast<string>().ToList().Where(i => i.Contains("Use DROP FUNCTION")).FirstOrDefault();
                            if (!string.IsNullOrWhiteSpace(dropStatement))
                            {
                                var originalFileName = (sqlFile.Split('\\')).ToList().Last();
                                var file = (sqlFilesWithDrop.Where(i => i.Contains(originalFileName))).FirstOrDefault();
                                if (!string.IsNullOrWhiteSpace(file))
                                {
                                    string contents = await File.ReadAllTextAsync(file);
                                    await conn.ExecuteAsync(contents);
                                }
                            }
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {

        }
    }
}