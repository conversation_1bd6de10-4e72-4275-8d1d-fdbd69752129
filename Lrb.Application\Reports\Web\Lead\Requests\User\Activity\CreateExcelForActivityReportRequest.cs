﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Identity.Users;
using Lrb.Application.Utils;

namespace Lrb.Application.Reports.Web
{
    public class CreateExcelForActivityReportRequest : IRequest<Response<string>>
    {
        public string? SearchText { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public bool IsWithTeam { get; set; }
        public List<Guid>? UserIds { get; set; }
        public UserStatus? UserStatus { get; set; }
        public ReportPermission? ExportPermission { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = int.MaxValue;
        public bool? ShouldShowDataReport { get; set; }
        public string? TimeZoneId { get; set; }
        public TimeSpan BaseUTcOffset { get; set; }
    }
    public class CreateExcelForActivityReportRequestHandler : IRequestHandler<CreateExcelForActivityReportRequest, Response<string>>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        private readonly IUserService _userService;
        private readonly IBlobStorageService _blobStorageService;
        public CreateExcelForActivityReportRequestHandler(IDapperRepository dapperRepository, 
            ICurrentUser currentUser, IUserService userService, IBlobStorageService blobStorageService)
        {
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
            _userService = userService;
            _blobStorageService = blobStorageService;

        }
        public async Task<Response<string>> Handle(CreateExcelForActivityReportRequest request, CancellationToken cancellationToken)
        {
            var tenantId = _currentUser.GetTenant();
            var userId = _currentUser.GetUserId();
            List<Guid> teamUserIds = new();
            List<Guid> permittedUserIds = new();
            var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
            if (isAdmin)
            {
                permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
            }
            else if (request.ExportPermission != null)
            {
                switch (request.ExportPermission)
                {
                    case ReportPermission.All:
                        permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        break;
                    case ReportPermission.Reportees:
                        permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                        break;
                }
            }
            if (request?.UserIds?.Any() ?? false)
            {
                if (request?.IsWithTeam ?? false)
                {
                    teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                }
                else
                {
                    teamUserIds = request?.UserIds ?? new List<Guid>();
                }
            }
            else
            {
                if (!isAdmin)
                {
                    //request.UserIds = new List<Guid>() { userId };
                    //teamUserIds = (await _dapperRepository.GetSubordinateIdsAsync(request.UserIds, tenantId ?? string.Empty)).ToList();

                    teamUserIds = permittedUserIds;
                }
            }
            if (teamUserIds.Any())
            {
                teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
            }
            else
            {
                teamUserIds = permittedUserIds;
            }
            List<UserActivityReportDto> userActivityReportDtos = new();
            request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : DateTime.UtcNow.Date.ConvertFromDateToUtc();
            request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : DateTime.UtcNow.Date.ConvertToDateToUtc();
            var dataFromHistory = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<UserActivityReportDto>("LeadratBlack", "GetActivityReportFromHistory", new
            {
                pagesize = request.PageSize,
                pagenumber = request.PageNumber,
                from_date = request.FromDate,
                to_date = request.ToDate,
                tenant_id = tenantId,
                user_ids = teamUserIds,
                searchtext = request.SearchText,
                userstatus = (request?.UserStatus ?? 0)
            })).ToList();
            var userIdsToTake = dataFromHistory.Select(i => i.UserId).ToList();
            var userIds = dataFromHistory.Select(i => i.UserId.ToString()).ToList();
            var users = await _userService.GetListOfUsersByIdsAsync(userIds, cancellationToken);
            var logDtos = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<AttendanceLogReportDto>("LeadratBlack", "GetActivityReportFromAttendance", new
            {
                user_ids = userIdsToTake,
                tenant_id = tenantId,
                from_date = request.FromDate,
                to_date = request.ToDate
            })).ToList();
            var dataFromLeadAppointment = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<LeadAppointmentDtoReport>("LeadratBlack", "GetActivityReportFromLeadAppointment", new
            {
                user_ids = userIdsToTake,
                tenant_id = tenantId,
                from_date = request.FromDate,
                to_date = request.ToDate
            })).ToList();
            dataFromHistory.ForEach(i =>
            {
                var logsForCurrentUser = logDtos.Where(log => log.UserId == i.UserId).OrderByDescending(l => l.CreatedOn).ToList();
                var logsByDay = GetLogsByDateRangeAsync(logsForCurrentUser, request.FromDate.Value, request.ToDate.Value).Result;
                i.AverageWorkingHours = GetWorkingHoursByUser(logsByDay).Result;
                i.MeetingDoneCount = dataFromLeadAppointment.Where(app => app.UserId == i.UserId)?.FirstOrDefault()?.MeetingDoneCount ?? 0;
                i.SiteVisitDoneCount = dataFromLeadAppointment.Where(app => app.UserId == i.UserId)?.FirstOrDefault()?.SiteVisitDoneCount ?? 0;
            });
            var fileBytes = ExcelHelper.CreateExcelFromList(dataFromHistory, new List<string>(), new List<string>() { "UserId" },request.TimeZoneId, request.BaseUTcOffset).ToArray();
            var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", $"Reports/{tenantId ?? "Default"}", $"Report-{DateTime.Now}.xlsx", fileBytes);
            var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
            return new(presignedUrl);
        }
        private async static Task<TimeSpan> GetWorkingHoursByUser(List<LogsByDayReportDto> logsByDayDtos)
        {
            var timeSpanList = logsByDayDtos.Where(i => i.WorkingHours != null).Select(i => i.WorkingHours).ToList();
            TimeSpan sumTotal = new();
            int noOfDays = 0;
            timeSpanList.ForEach(i =>
            {
                sumTotal += i.Value;
                noOfDays++;
            });
            return sumTotal / (double)noOfDays;
        }
        private async static Task<List<LogsByDayReportDto>> GetLogsByDateRangeAsync(List<AttendanceLogReportDto> userEntries, DateTime fromDate, DateTime toDate)
        {
            List<LogsByDayReportDto> logsByDay = new();
            while (fromDate <= toDate)
            {
                var startDate = fromDate;
                var endDate = fromDate.AddDays(1).AddSeconds(-1);
                var entriesPerDay = userEntries.Where(i => (i.ClockInTime >= startDate && i.ClockInTime <= endDate)
                                                               || (i.ClockOutTime >= startDate && i.ClockOutTime <= endDate));
                logsByDay.Add(new LogsByDayReportDto()
                {
                    Day = endDate.Date,
                    logDtos = entriesPerDay.ToList(),
                    WorkingHours = entriesPerDay.Any(i => i.ClockOutTime == null) ? TimeSpan.Zero : GetWorkingHours(entriesPerDay.ToList()).Result,
                    AreSwipesMissing = entriesPerDay.Any(i => i.ClockOutTime == null) ? true : false
                });
                fromDate = fromDate.AddDays(1);
            }
            return logsByDay;
        }
        private async static Task<TimeSpan> GetWorkingHours(List<AttendanceLogReportDto> logDtos)
        {
            TimeSpan hours = new();
            foreach (var dto in logDtos)
            {
                if (dto.ClockInTime != null && dto.ClockOutTime != null)
                {
                    var time = (dto.ClockOutTime.Value) - (dto.ClockInTime.Value);
                    hours = hours + time;
                }
            }
            return hours;
        }
    }
}
