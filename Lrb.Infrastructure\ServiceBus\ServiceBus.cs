﻿using Azure.Messaging.ServiceBus;
using Lrb.Application.Common.ServiceBus;
using Lrb.Application.Lead.Mobile.Dtos.v1;
using Lrb.Infrastructure.Queue;
using Lrb.Infrastructure.ServiceBus;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using RestSharp;
using System.Text;

namespace AzureServiceBusDemo.Repositories
{
    public class ServiceBus : IServiceBus
    {
        private readonly AzureServiceBusSettings _settings;
        private readonly LeadRotationAzureServiceBusSetting _leadRotationSetting;
        private static readonly HttpClient _httpClient = new(); // Reused across calls
        private readonly LeadRetentionQueueService _queueService;
        public ServiceBus(IOptions<AzureServiceBusSettings> options, IOptions<LeadRotationAzureServiceBusSetting> leadRotationSetting, LeadRetentionQueueService queueService)
        {
            _settings = options.Value;
            _leadRotationSetting = leadRotationSetting.Value;
            _queueService = queueService;
        }

        public async Task SendMessageAsync(object dto)
        {
            try
            {
                var client = new ServiceBusClient(_settings.AzureServiceBusConnectionString);

                // create the sender
                ServiceBusSender sender = client.CreateSender(_settings.QueueName);

                // message body
                var messageBody = JsonConvert.SerializeObject(dto, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented });


                // create a message that we can send. UTF-8 encoding is used when providing a string.
                ServiceBusMessage message = new ServiceBusMessage(messageBody ?? string.Empty);

                // send the message
                await sender.SendMessageAsync(message);
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task RunExcelUploadJobAsync(object dto)
        {
            try
            {
                var _client = new RestClient(_settings.BulkUploadUrl ?? string.Empty);
                var restRequest = new RestRequest();
                restRequest.AddJsonBody(dto);
                Task.Run(() => _client.ExecuteAsync(restRequest));
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public Task RunNotificationJobAsync(object dto)
        {
            FireAndForgetPost(_settings.NotificationUrl ?? string.Empty, dto);
            return Task.CompletedTask;
        }

        public Task RunLeadHistoryJobAsync(object dto)
        {
            FireAndForgetPost(_settings.LeadHistoryUrl ?? string.Empty, dto);
            return Task.CompletedTask;
        }

        public Task RunAssignLeadsbasedOnScenariosAsync(object dto)
        {
            FireAndForgetPost(_settings.AssignLeadsbasedOnScenariosUrl ?? string.Empty, dto);
            return Task.CompletedTask;
        }

        public Task RunUpdateLeadStatusAsync(object dto)
        {
            FireAndForgetPost(_settings.UpdateLeadStatusUrl ?? string.Empty, dto);
            return Task.CompletedTask;
        }

        public Task RunStatusChangeNotificationJobAsync(object dto)
        {
            FireAndForgetPost(_settings.StatusChangeNotificationUrl ?? string.Empty, dto);
            return Task.CompletedTask;
        }


        public Task RunLeadRotationHttpTriggerJobAsync(object dto)
        {
            FireAndForgetPost(_leadRotationSetting.LeadRotationUrl ?? string.Empty, dto);
            return Task.CompletedTask; 
        }

        //public async Task RunNotificationJobAsync(object dto)
        //{
        //    try
        //    {
        //        var _client = new RestClient(_settings.NotificationUrl ?? string.Empty);
        //        var restRequest = new RestRequest();
        //        restRequest.AddJsonBody(dto);
        //        Task.Run(() => _client.ExecuteAsync(restRequest));
        //    }
        //    catch (Exception ex)
        //    {
        //        throw;
        //    }
        //}
        //public async Task RunLeadHistoryJobAsync(object dto)
        //{
        //    try
        //    {
        //        var _client = new RestClient(_settings.LeadHistoryUrl ?? string.Empty);
        //        var restRequest = new RestRequest();
        //        restRequest.AddJsonBody(dto);
        //        Task.Run(() => _client.ExecuteAsync(restRequest));
        //    }
        //    catch (Exception ex)
        //    {
        //        throw;
        //    }
        //}
        //public async Task RunAssignLeadsbasedOnScenariosAsync(object dto)
        //{
        //    try
        //    {
        //        var _client = new RestClient(_settings.AssignLeadsbasedOnScenariosUrl ?? string.Empty);
        //        var restRequest = new RestRequest();
        //        restRequest.AddJsonBody(dto);
        //        Task.Run(() => _client.ExecuteAsync(restRequest));
        //    }
        //    catch (Exception ex)
        //    {
        //        throw;
        //    }
        //}
        //public async Task RunUpdateLeadStatusAsync(object dto)
        //{
        //    try
        //    {
        //        var _client = new RestClient(_settings.UpdateLeadStatusUrl ?? string.Empty);
        //        var restRequest = new RestRequest();
        //        restRequest.AddJsonBody(dto);
        //        Task.Run(() => _client.ExecuteAsync(restRequest));
        //    }
        //    catch (Exception ex)
        //    {
        //        throw;
        //    }
        //}

        //public async Task RunStatusChangeNotificationJobAsync(object dto)
        //{
        //    try
        //    {
        //        var _client = new RestClient(_settings.StatusChangeNotificationUrl ?? string.Empty);
        //        var restRequest = new RestRequest();
        //        restRequest.AddJsonBody(dto);
        //        Task.Run(() => _client.ExecuteAsync(restRequest));
        //    }
        //    catch (Exception ex)
        //    {
        //        throw;
        //    }
        //}

        //public async Task RunLeadRotationHttpTriggerJobAsync(object dto)
        //{
        //    try
        //    {
        //        var _client = new RestClient(_leadRotationSetting.LeadRotationUrl ?? string.Empty);
        //        var restRequest = new RestRequest();
        //        restRequest.AddJsonBody(dto);
        //        Task.Run(() => _client.ExecuteAsync(restRequest));
        //    }
        //    catch (Exception ex)
        //    {
        //        throw;
        //    }
        //}

        public async Task RunBulkOpeartionJobAsync(object dto)
        {
            try
            {
                var _client = new RestClient(_settings.BulkOperationUrl ?? string.Empty);
                var restRequest = new RestRequest();
                restRequest.AddJsonBody(dto);
                Task.Run(() => _client.ExecuteAsync(restRequest));
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        private void FireAndForgetPost(string url, object dto)
        {
            if (string.IsNullOrWhiteSpace(url)) return;
            _ = Task.Run(async () =>
            {
                try
                {
                    var json = JsonConvert.SerializeObject(dto);
                    using var content = new StringContent(json, Encoding.UTF8, "application/json");
                    // Add timeout to prevent long-held connections
                    using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
                    await _httpClient.PostAsync(url, content, cts.Token);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Background POST to {url} failed: {ex.Message}");
                }
            });
        }

        public async Task SendLeadRotationMessageAsync(object dto)
        {
            try
            {
                var client = new ServiceBusClient(_leadRotationSetting.AzureServiceBusConnectionString);

                // create the sender
                ServiceBusSender sender = client.CreateSender(_leadRotationSetting.QueueName);

                // message body
                var messageBody = JsonConvert.SerializeObject(dto, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented });


                // create a message that we can send. UTF-8 encoding is used when providing a string.
                ServiceBusMessage message = new ServiceBusMessage(messageBody ?? string.Empty);

                // send the message
                await sender.SendMessageAsync(message);


                //IQueueClient client = new QueueClient(_leadRotationSetting.AzureServiceBusConnectionString, _leadRotationSetting.QueueName);

                //var messageBody = JsonConvert.SerializeObject(dto, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented });

                //var message = new Message(Encoding.UTF8.GetBytes(messageBody ?? string.Empty))
                //{
                //    MessageId = Guid.NewGuid().ToString(),
                //    ContentType = "application/json"
                //};
                //await client.SendAsync(message);
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task ScheduleJobAsync(object dto, DateTimeOffset shiftStartTime)
        {
            try
            {
                var client = new ServiceBusClient(_leadRotationSetting.AzureServiceBusConnectionString);

                // create the sender
                ServiceBusSender sender = client.CreateSender(_leadRotationSetting.QueueName);

                // message body
                var messageBody = JsonConvert.SerializeObject(dto, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented });


                // create a message that we can send. UTF-8 encoding is used when providing a string.
                ServiceBusMessage message = new ServiceBusMessage(messageBody ?? string.Empty);

                // send the message
                await sender.ScheduleMessageAsync(message, shiftStartTime);


                //IQueueClient client = new QueueClient(_leadRotationSetting.AzureServiceBusConnectionString, _leadRotationSetting.QueueName);

                //var messageBody = JsonConvert.SerializeObject(dto, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented });

                //var message = new Message(Encoding.UTF8.GetBytes(messageBody ?? string.Empty))
                //{
                //    MessageId = Guid.NewGuid().ToString(),
                //    ContentType = "application/json"
                //};

                //await client.ScheduleMessageAsync(message, shiftStartTime);
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task RunExcelExportJobAsync(object dto)
        {
            var _client = new RestClient(_settings.BulkExportUrl ?? string.Empty);
            var restRequest = new RestRequest();
            restRequest.AddJsonBody(dto);
            Task.Run(() => _client.ExecuteAsync(restRequest));
        }

        public async Task RunBulkStatusUpdateJobAsync(object dto)
        {
            try
            {
                var _client = new RestClient(_settings.BulkStatusUpdateUrl ?? string.Empty);
                var restRequest = new RestRequest();
                restRequest.AddJsonBody(dto);
                Task.Run(() => _client.ExecuteAsync(restRequest));
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task RunBulkOpeartionJobToSyncPropertyAsync(object dto)
        {
            try
            {
                var _client = new RestClient(_settings.SyncPropertyUrl ?? string.Empty);
                var restRequest = new RestRequest();
                restRequest.AddJsonBody(dto);
                Task.Run(() => _client.ExecuteAsync(restRequest));
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task RunBulkProspectAssignmentJobAsync(object dto)
        {
            try
            {
                var _client = new RestClient(_settings.BulkProspectAssignmentUrl ?? string.Empty);
                var restRequest = new RestRequest();
                restRequest.AddJsonBody(dto);
                Task.Run(() => _client.ExecuteAsync(restRequest));
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task RunBulkSecondaryLeadAssignmentJobAsync(object dto)
        {
            try
            {
                var _client = new RestClient(_settings.BulkSecondaryLeadAssignmentUrl ?? string.Empty);
                var restRequest = new RestRequest();
                restRequest.AddJsonBody(dto);
                Task.Run(() => _client.ExecuteAsync(restRequest));
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task RunBulkLeadSourceUpdateJobAsync(object dto)
        {
            try
            {
                var _client = new RestClient(_settings.BulkLeadSourceUpdateUrl ?? string.Empty);
                var restRequest = new RestRequest();
                restRequest.AddJsonBody(dto);
                Task.Run(() => _client.ExecuteAsync(restRequest));
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task RunBulkProspectStatusUpdateJobAsync(object dto)
        {
            try
            {
                var _client = new RestClient(_settings.BulkProspectStatusUpdateUrl ?? string.Empty);
                var restRequest = new RestRequest();
                restRequest.AddJsonBody(dto);
                Task.Run(() => _client.ExecuteAsync(restRequest));
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task RunBulkProjectUpdateJobAsync(object dto)
        {
            try
            {
                var _client = new RestClient(_settings.BulkProjectUpdateUrl ?? string.Empty);
                var restRequest = new RestRequest();
                restRequest.AddJsonBody(dto);
                Task.Run(() => _client.ExecuteAsync(restRequest));
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task RunPFPropertyAssignementJobAsync(object dto)
        {
            try
            {
                var _client = new RestClient(_settings.PFPropertyAssignmentUrl ?? string.Empty);
                var restRequest = new RestRequest();
                restRequest.AddJsonBody(dto);
                Task.Run(() => _client.ExecuteAsync(restRequest));
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task RunFbConversionApiEventAsync(object dto)
        {
            try
            {
                var _client = new RestClient(_settings.FbConverionApiUrl ?? string.Empty);
                var restRequest = new RestRequest();
                restRequest.AddJsonBody(dto);
                Task.Run(() => _client.ExecuteAsync(restRequest));
            }
            catch (Exception ex)
            {
                throw;
            }
        }


        public async Task RunXMLFeedListingUrlSyncJobAsync(object dto)
        {
            try
            {
                var _client = new RestClient(_settings.FetchXmlFeedListingUrl ?? string.Empty);
                var restRequest = new RestRequest();
                restRequest.AddJsonBody(dto);
                Task.Run(() => _client.ExecuteAsync(restRequest));
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task<string?> ScheduleReportsAutomationAsync(object dto)
        {
            try
            {
                var _client = new RestClient(_settings.CreateAndUpdateReportConfigUrl ?? string.Empty);
                var restRequest = new RestRequest();
                restRequest.AddJsonBody(dto);
                var instaceId = await _client.ExecuteAsync(restRequest);
                return instaceId?.Content?.Replace("\"", "") ?? "0000";
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task DeleteScheduledReportAutomationAsync(List<string> jobIds)
        {
            try
            {
                var _client = new RestClient(_settings.DeleteReportConfigUrl ?? string.Empty);
                var restRequest = new RestRequest();
                restRequest.Method = Method.Delete;
                restRequest.AddJsonBody(jobIds);
                var response = await _client.ExecuteAsync(restRequest);
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task DeleteTenantAllReportAutomationAsync(string tenantId)
        {
            try
            {
                var _client = new RestClient(_settings.DeleteTenantAllReportConfigJobsUrl ?? string.Empty);
                var restRequest = new RestRequest();
                restRequest.Method = Method.Delete;
                restRequest.AddJsonBody(tenantId);
                var response = await _client.ExecuteAsync(restRequest);
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task DeleteAllReportAutomationAsync()
        {
            try
            {
                var _client = new RestClient(_settings.DeleteAllReportConfigJobsUrl ?? string.Empty);
                var restRequest = new RestRequest();
                restRequest.Method = Method.Delete;
                var response = await _client.ExecuteAsync(restRequest);
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public  Task RunLeadRotationTriggerJobAsync(object payload)
        {
            try
            {
                _queueService.Enqueue(new QueueTriggerMessage { Payload = payload,Url = _settings.TriggerLeadRotationUrl});
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in LeadRetentionTriggerAsync: {ex.Message}");
                throw;
            }
        }
        public async Task RunBulkProspectSourceUpdateJobAsync(object dto)
        {
            try
            {
                var _client = new RestClient(_settings.BulkProspectSourceUpdateUrl ?? string.Empty);
                var restRequest = new RestRequest();
                restRequest.AddJsonBody(dto);
                Task.Run(() => _client.ExecuteAsync(restRequest));
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task RunBulkSourceUpdateToDirectJobAsync(object dto)
        {
            try
            {
                var _client = new RestClient(_settings.BulkSourceUpdateToDirectUrl ?? string.Empty);
                var restRequest = new RestRequest();
                restRequest.AddJsonBody(dto);
                Task.Run(() => _client.ExecuteAsync(restRequest));
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public Task RunLeadRetentionTriggerJobAsync(object payload)
        {
            try
            {
                _queueService.Enqueue(new QueueTriggerMessage { Payload = payload,Url = _settings.TriggerLeadRetentionUrl });
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in LeadRetentionTriggerAsync: {ex.Message}");
                throw;
            }
        }
        public Task RunAutoDialerTriggerJobAsync(object payload)
        {
            try
            {
                _queueService.Enqueue(new QueueTriggerMessage { Payload = payload, Url = _settings.AutoDailerTriggerUrl });
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in RunAutoDialerTriggerJobAsync: {ex.Message}");
                throw;
            }
        }
        #region Listing Jobs
        public async Task RunPublishListingJobAsync(object dto)
        {
            try
            {
                var _client = new RestClient(_settings.PublishListingUrl ?? string.Empty);
                var restRequest = new RestRequest();
                restRequest.AddJsonBody(dto);
                Task.Run(() => _client.ExecuteAsync(restRequest));
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task RunUpdateListingJobAsync(object dto)
        {
            try
            {
                var _client = new RestClient(_settings.UpdateListingUrl ?? string.Empty);
                var restRequest = new RestRequest();
                restRequest.AddJsonBody(dto);
                Task.Run(() => _client.ExecuteAsync(restRequest));
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task RunDeleteListingJobAsync(object dto)
        {
            try
            {
                var _client = new RestClient(_settings.DeletedListingUrl ?? string.Empty);
                var restRequest = new RestRequest();
                restRequest.AddJsonBody(dto);
                Task.Run(() => _client.ExecuteAsync(restRequest));
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task RunPermanentDeleteListingJobAsync(object dto)
        {
            try
            {
                var _client = new RestClient(_settings.PermanentDeleteListingUrl ?? string.Empty);
                var restRequest = new RestRequest();
                restRequest.AddJsonBody(dto);
                Task.Run(() => _client.ExecuteAsync(restRequest));
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task RunBulkAgencyUpdateJobAsync(object dto)
        {
            try
            {
                var _client = new RestClient(_settings.BulkAgencyUpdateUrl ?? string.Empty);
                var restRequest = new RestRequest();
                restRequest.AddJsonBody(dto);
                Task.Run(() => _client.ExecuteAsync(restRequest));
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task RunBulkChannelPartenerUpdateJobAsync(object dto)
        {
            try
            {
                var _client = new RestClient(_settings.BulkChannelPartenerUpdateUrl ?? string.Empty);
                var restRequest = new RestRequest();
                restRequest.AddJsonBody(dto);
                Task.Run(() => _client.ExecuteAsync(restRequest));
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task RunBulkCampaignUpdateJobAsync(object dto)
        {
            try
            {
                var _client = new RestClient(_settings.BulkCampaignUpdateUrl ?? string.Empty);
                var restRequest = new RestRequest();
                restRequest.AddJsonBody(dto);
                Task.Run(() => _client.ExecuteAsync(restRequest));
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task RunListingToLeadMappingJobAsync(object dto)
        {
            try
            {
                var _client = new RestClient(_settings.PFListingToLeadMappingUrl ?? string.Empty);
                var restRequest = new RestRequest();
                restRequest.AddJsonBody(dto);
                Task.Run(() => _client.ExecuteAsync(restRequest));
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task RunBulkProspectAgencyUpdateJobAsync(object dto)
        {
            try
            {
                var _client = new RestClient(_settings.ProspectBulkAgencyUpdateUrl ?? string.Empty);
                var restRequest = new RestRequest();
                restRequest.AddJsonBody(dto);
                Task.Run(() => _client.ExecuteAsync(restRequest));
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task RunBulkProspectChannelPartenerUpdateJobAsync(object dto)
        {
            try
            {
                var _client = new RestClient(_settings.ProspectBulkChannelPartenerUpdateUrl ?? string.Empty);
                var restRequest = new RestRequest();
                restRequest.AddJsonBody(dto);
                Task.Run(() => _client.ExecuteAsync(restRequest));
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task RunBulkProspectCampaignUpdateJobAsync(object dto)
        {
            try
            {
                var _client = new RestClient(_settings.ProspectBulkCampaignUpdateUrl ?? string.Empty);
                var restRequest = new RestRequest();   
                restRequest.AddJsonBody(dto);
                Task.Run(() => _client.ExecuteAsync(restRequest));
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task RunAutoDialerBulkAddJobAsync(object dto)
        {
            try
            {
                var _client = new RestClient(_settings.AutoDailerBulkAddUrl ?? string.Empty);
                var restRequest = new RestRequest();
                restRequest.AddJsonBody(dto);
                Task.Run(() => _client.ExecuteAsync(restRequest));
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        #endregion
        public async Task<string?> SyncDataByTenantAsync(string tenantId, CancellationToken cancellationToken = default)
        {
            try
            {
                var baseUrl = _settings.SyncDataByTenantUrl ?? string.Empty;
                var url = $"{baseUrl.TrimEnd('/')}/call-detection/sync/{tenantId}";
                var client = new RestClient(url);
                var request = new RestRequest();   
                request.Method = Method.Post;
                var response = await client.ExecuteAsync(request, cancellationToken);

                if (!response.IsSuccessful)
                {
                    return $"Error: {response.StatusCode} - {response.Content}";
                }

                return response.Content;
            }
            catch (Exception)
            {
                throw;
            }
        }
  /*      public async Task<List<T>> GetCallDirectionDataAsync<T>(object dto)
        {
            var client = new RestClient(_settings.GetCallDirectionDataUrl ?? string.Empty);
            var restRequest = new RestRequest()
                .AddJsonBody(dto);

            var response = await client.ExecuteAsync(restRequest);

            if (!response.IsSuccessful || string.IsNullOrEmpty(response.Content))
            {
                throw new Exception($"API call failed with status: {response.StatusCode}");
            }

            var items = JsonConvert.DeserializeObject<List<T>>(response.Content);
            return items ?? new List<T>();
        }*/
        public async Task<List<T>> GetCallDirectionDataAsync<T>(string tenant, Guid? userId, bool sendOnlyAssignedLeads, DateTime? fromDate, DateTime? toDate, int pageNumber, int pageSize, string type)
        {
            string? urlTemplate = _settings.GetCallDirectionDataUrl ?? string.Empty;

            // Use proper DateTime formatting and URL encoding
            var effectiveFromDate = fromDate?.ToString("yyyy-MM-ddTHH:mm:ssZ") ?? "2025-08-31T18:30:00Z";
            var effectiveToDate = toDate?.ToString("yyyy-MM-ddTHH:mm:ssZ") ?? "2025-09-25T18:30:00Z";

            var url = urlTemplate
                .Replace("{tenant}", Uri.EscapeDataString(tenant))
                .Replace("{userId}", userId?.ToString() ?? "")
                .Replace("{sendOnlyAssignedLeads}", sendOnlyAssignedLeads.ToString().ToLower())
                .Replace("{pageNumber}", pageNumber.ToString())
                .Replace("{pageSize}", pageSize.ToString())
                .Replace("{type}", type);

            var client = new RestClient(url);
            var restRequest = new RestRequest { Method = Method.Get };

            try
            {
                var response = await client.ExecuteAsync(restRequest);

                if (!response.IsSuccessful)
                {
                    throw new Exception($"API call failed with status: {response.StatusCode}. Response: {response.Content}");
                }

                if (string.IsNullOrEmpty(response.Content))
                {
                    throw new Exception("API returned empty response");
                }

                var items = JsonConvert.DeserializeObject<List<T>>(response.Content);
                return items ?? new List<T>();
            }
            catch (Exception ex)
            {
                // Log the actual URL being called for debugging
                throw new Exception($"Failed to call API: {url}. Error: {ex.Message}", ex);
            }
        }
        public async Task<List<T>> GetCallDirectionDataAsync<T>(object dto)
        {
            try
            {
                var _client = new RestClient(_settings.GetCallDirectionDataUrl ?? string.Empty);
                var restRequest = new RestRequest
                {
                    Method = Method.Get
                };

                restRequest.AddJsonBody(dto);

                var response = await _client.ExecuteAsync(restRequest);

                if (!response.IsSuccessful || string.IsNullOrEmpty(response.Content))
                {
                    throw new Exception($"API call failed with status: {response.StatusCode}, Response: {response.Content}");
                }

                // Deserialize the response into a list of T
                var items = JsonConvert.DeserializeObject<List<T>>(response.Content);

                return items ?? new List<T>();
            }
            catch (Exception ex)
            {
                throw new Exception($"Error calling API: {ex.Message}", ex);
            }
        }
    }
}
