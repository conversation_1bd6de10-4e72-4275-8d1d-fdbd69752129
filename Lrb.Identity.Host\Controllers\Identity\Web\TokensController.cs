using Amazon.Extensions.CognitoAuthentication;
using Lrb.Application.Common.Exceptions;
using Lrb.Application.Common.Identity;
using Lrb.Application.Identity.Cognito;
using Lrb.Application.Identity.Tokens;
using Lrb.Application.Identity.Users;
using Lrb.Domain.Enums;
using Newtonsoft.Json;

namespace Lrb.Identity.Host.Controllers.Identity.Web;

public sealed class TokensController : VersionNeutralApiController
{
    private readonly ITokenService _tokenService;
    private readonly ICognitoService<WebClient> _cognitoService;
    private readonly Serilog.ILogger _logger;
    private readonly IUserService _userService;

    public TokensController(ITokenService tokenService, ICognitoService<WebClient> cognitoService, IUserService userService, Serilog.ILogger logger)
    {
        _tokenService = tokenService;
        _cognitoService = cognitoService;
        _logger = logger;
        _userService = userService;
    }

    [HttpPost]
    [AllowAnonymous]
    [TenantIdHeader]
    [OpenApiOperation("Request an access token using credentials.", "")]
    public async Task<Response<CognitoTokenResponse>> GetTokenAsync(TokenRequest request, CancellationToken cancellationToken)
    {
        if (request == null || string.IsNullOrWhiteSpace(request.Username) || string.IsNullOrWhiteSpace(request.Password))
        {
            throw new ArgumentNullException("Request is not valid");
        }
        _logger.Information("TokensController - > GetTokenAsync(), Request: " + JsonConvert.SerializeObject(request));
        return new(await _tokenService.GetTokenAsync(request, GetIpAddress(), cancellationToken));
    }

    [HttpPost("otp")]
    [AllowAnonymous]
    [TenantIdHeader]
    [OpenApiOperation("Request an access token using OTP.", "")]
    public async Task<Dictionary<string, string>> GetOTPAsync(string userName, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(userName))
        {
            throw new ArgumentNullException("Request is not valid");
        }
        _logger.Information("TokensController - > GetOTPAsync(), Request: " + JsonConvert.SerializeObject(userName));
        var res = await _cognitoService.TryLoginAsync(userName, cancellationToken);
        await _userService.UpdateOTPAsync(res.UserId.ToString(), res.SecretLoginCode);
        return new() { { nameof(res.SessionId), res.SessionId } };
    }

    [HttpPost("verify-otp")]
    [AllowAnonymous]
    [TenantIdHeader]
    [OpenApiOperation("verify the send OTP with the sessionId.", "")]
    public async Task<Response<CognitoTokenResponse?>> VerifyOTPAsync(string userName, string sessionId, string otp, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(userName))
        {
            throw new ArgumentNullException("Request is not valid");
        }
        _logger.Information("TokensController - > VerifyOTPAsync(), Request: " + JsonConvert.SerializeObject(userName));
        var user = await _userService.GetByUserNameAsync(userName, cancellationToken);
        if (user.OTPUpdatedOn.HasValue && user.OTPUpdatedOn > DateTime.UtcNow.AddMinutes(-3))
        {
            if (user.OTP != otp)
            {
                throw new UnauthorizedException("Invalid OTP!", Domain.Enums.ErrorActionCode.Logout);
            }
            else
            {
                var res = await _cognitoService.VerifyAuthChallenge(userName, sessionId, otp, cancellationToken);
                if (res?.AuthenticationResult != null)
                {
                    return new()
                    {
                        Succeeded = true,
                        Data = new(res.AuthenticationResult.IdToken, res.AuthenticationResult.AccessToken, res.AuthenticationResult.RefreshToken, sessionId)
                    };
                }
                else
                {
                    throw new UnauthorizedException("Invalid OTP", Domain.Enums.ErrorActionCode.Logout);
                }
            }
        }
        else
        {
            throw new UnauthorizedException("Session Expired! Try with new OTP", Domain.Enums.ErrorActionCode.Logout);
        }
    }

    [HttpPost("refresh")]
    [AllowAnonymous]
    [TenantIdHeader]
    [OpenApiOperation("Request an access token using a refresh token.", "")]
    public async Task<Response<CognitoTokenResponse>> RefreshAsync(RefreshTokenRequest request)
    {
        if (request == null || string.IsNullOrWhiteSpace(request.Token) || string.IsNullOrWhiteSpace(request.RefreshToken))
        {
            return new ("Request is not valid");
        }
        _logger.Information("TokensController - > RefreshAsync(), Request: " + JsonConvert.SerializeObject(request));
        return new(await _tokenService.RefreshTokenAsync(request, GetIpAddress()));
    }

    [HttpPost("reset-failed-attempts")]
    [AllowAnonymous]
    [TenantIdHeader]
    [OpenApiOperation("Reset failed login attempt", "")]
    public async Task<Response<bool>> ResetFailedLoginAttemptsAsync(string userName, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(userName))
        {
            throw new ArgumentNullException("Request is not valid");
        }
        return new(await _tokenService.ResetFailedLoginAttemptsAsync(userName, cancellationToken));
    }

    [HttpPost("get-failed-attempts")]
    [AllowAnonymous]
    [TenantIdHeader]
    [OpenApiOperation("Get failed login attempt info", "")]
    public async Task<Response<AccountLockoutInfo>> GetFailedLoginAttemptsAsync(string userName, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(userName))
        {
            throw new ArgumentNullException("Request is not valid");
        }
        var attempts = await _tokenService.GetFailedLoginAttemptsAsync(userName, cancellationToken);
        return new(attempts);
    }
    [HttpPost("logout")]
    [AllowAnonymous]
    [TenantIdHeader]
    [OpenApiOperation("Update logout details", "")]
    public async Task<Response<bool>> UpdateLogOutDetails([FromBody] LogoutRequest request, CancellationToken cancellationToken)
    {
        var tenant = this.HttpContext.Request.Headers["tenant"];
        if (request.UserId == null)
        {
            _logger.Information($"WebTokenService : UpdateLogOutDetails({request.UserId}) => Request is not valid");
            throw new ArgumentNullException("Request is not valid");
        }
        _logger.Information($"WebTokenService : UpdateLogoutDetails( {request.UserId} , {request.LogoutType.ToString()})  - > Token: " + request.IdToken + "/n RefreshToken :" + request.RefreshToken);
        return new(await _tokenService.UpdateLogoutDetails(request, tenant, cancellationToken));
    }
    private string GetIpAddress() =>
        Request.Headers.ContainsKey("X-Forwarded-For")
            ? Request.Headers["X-Forwarded-For"]
            : HttpContext.Connection.RemoteIpAddress?.MapToIPv4().ToString() ?? "N/A";
}