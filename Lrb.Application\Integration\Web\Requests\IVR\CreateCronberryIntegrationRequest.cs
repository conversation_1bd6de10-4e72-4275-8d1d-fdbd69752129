﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Domain.Entities.ErrorModule;
using Newtonsoft.Json;

namespace Lrb.Application.Integration.Web.Requests
{
    public class CreateCronberryIntegrationRequest : IRequest<Response<string>>
    {
        public string? AccountName { get; set; }
        public LeadSource Source { get; set; }
        public IVRType CallType { get; set; }
        public Dictionary<string, string>? Credentials { get; set; }
    }
    public class CreateCronberryIntegrationRequestHandler : IRequestHandler<CreateCronberryIntegrationRequest, Response<string>>
    {
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccountInfoRepositoryAsync;
        private readonly IBlobStorageService _blobStorageService;
        private readonly ICurrentUser _currentUser;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        public CreateCronberryIntegrationRequestHandler(IRepositoryWithEvents<IntegrationAccountInfo> integrationAccountInfoRepositoryAsync,
            IBlobStorageService blobStorageService,
            ICurrentUser currentUser,
            ILeadRepositoryAsync leadRepositoryAsync)
        {
            _integrationAccountInfoRepositoryAsync = integrationAccountInfoRepositoryAsync;
            _blobStorageService = blobStorageService;
            _currentUser = currentUser;
            _leadRepositoryAsync = leadRepositoryAsync;
        }
        public async Task<Response<string>> Handle(CreateCronberryIntegrationRequest request, CancellationToken cancellationToken)
        {
            string fileUrl = string.Empty;
            try
            {
                request.Source = LeadSource.IVR;
                var tenant = _currentUser.GetTenant();
                Guid userId = Guid.NewGuid();
                IntegrationAccountInfo? integrationAccount;
                IDictionary<string, string> data = null;
                integrationAccount = CreateIntegrationEntity(request, userId);
                var apiKey = ApiKeyHelper.GenerateApiKey(integrationAccount.Id);
                integrationAccount.ApiKey = apiKey;
                data = new Dictionary<string, string>(IntegrationTemplateBuilder.CreateCronberryIntegrationTemplate(tenant, integrationAccount, request.CallType, IVRServiceProvider.Cronberry));
                await _integrationAccountInfoRepositoryAsync.AddAsync(integrationAccount);
                string key = string.Empty;
                byte[] bytes = IntegrationExcelHelper.CreateExcelData(data).ToArray();
                string fileName = $"{tenant}-{integrationAccount.Id}-{DateTime.Now.ToString("yyyy_MM_dd-HH_mm_ss")}.xlsx";
                string folder = "Integration";
                key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", folder, fileName, bytes);
                fileUrl = await _blobStorageService.GetPreSignedURL(_blobStorageService?.BucketName ?? "prd-leadratblack", key);
                integrationAccount.FileUrl = key;
                await _integrationAccountInfoRepositoryAsync.UpdateAsync(integrationAccount);
                return new(fileUrl, default);
            }
            catch(Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CreateCronberryIntegrationRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                return new(fileUrl, default);
            }
        }
        private IntegrationAccountInfo CreateIntegrationEntity(CreateCronberryIntegrationRequest command, Guid userId)
        {
            return new IntegrationAccountInfo()
            {
                Id = Guid.NewGuid(),
                AccountName = command.AccountName,
                LeadSource = command.Source,
                LicenseId = Guid.NewGuid(),
                JsonTemplate = IntegrationTemplateBuilder.GetRequestBodyJsonFromFile(command.Source, IVRServiceProvider.Cronberry),
                CreatedBy = userId,
                Credentials = command.Credentials
            };
        }
    }
}
