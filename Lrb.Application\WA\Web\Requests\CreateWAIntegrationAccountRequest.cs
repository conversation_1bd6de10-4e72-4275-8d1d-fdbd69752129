﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Common.WhatsApp;
using Lrb.Application.Integration.Web;
using Lrb.Application.WA.common;
using Newtonsoft.Json;

namespace Lrb.Application.WA.Web
{
    public class CreateWAIntegrationAccountRequest : IRequest<Response<string>>
    {
        public string? AccountName { get; set; }
        public string? ServiceProviderName { get; set; } // FreeText to add any name of the service provider which will get added to the callback url
        public bool? ShouldSetPrimary { get; set; }
        public Dictionary<string, string>? WebhookMapping { get; set; }
        public Dictionary<string, string>? OutboundMapping { get; set; }
        public Dictionary<WAEvent, string>? MessageStatusMapping { get; set; }
        public Dictionary<string, string>? ResponseMapping { get; set; }
        public List<WAApiInfoDto>? WAApiInfos { get; set; }
        public Dictionary<WAApiAction, IList<PropertyMapping>>? TemplateMapping { get; set; }
        public LeadSource? LeadSource { get; set; } = Domain.Enums.LeadSource.WhatsApp; //Adding default value to Whatsapp
        public string? Tenant { get; set; }
    }
    public class CreateWAIntegrationAccountRequestHandler : IRequestHandler<CreateWAIntegrationAccountRequest, Response<string>>
    {
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccountInfoRepoAsync;
        private readonly IBlobStorageService _blobStorageService;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<TempProjects> _tempProjectsRepo;
        private readonly IRepositoryWithEvents<Location> _locationRepo;
        private readonly ISender _mediator;
        private readonly IRepositoryWithEvents<Domain.Entities.Project> _projectsRepo;
        private readonly IWhatsAppService _whatsAppService;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingsRepo;
        private readonly IDapperRepository _dapperRepository;


        public CreateWAIntegrationAccountRequestHandler(
            IRepositoryWithEvents<IntegrationAccountInfo> integrationAccountInfoRepoAsync,
            IBlobStorageService blobStorageService,
            ICurrentUser currentUser,
            IRepositoryWithEvents<TempProjects> tempProjectsRepo,
            IRepositoryWithEvents<Location> locationRepo,
            ISender mediator,
            IRepositoryWithEvents<Domain.Entities.Project> projectsRepo,
            IWhatsAppService whatsAppService,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingsRepo,
            IDapperRepository dapperRepository)
        {
            _integrationAccountInfoRepoAsync = integrationAccountInfoRepoAsync;
            _blobStorageService = blobStorageService;
            _currentUser = currentUser;
            _tempProjectsRepo = tempProjectsRepo;
            _locationRepo = locationRepo;
            _mediator = mediator;
            _projectsRepo = projectsRepo;
            _whatsAppService = whatsAppService;
            _globalSettingsRepo = globalSettingsRepo;
            _dapperRepository = dapperRepository;
        }

        public async Task<Response<string>> Handle(CreateWAIntegrationAccountRequest request, CancellationToken cancellationToken)
        {
            var tenant = string.IsNullOrWhiteSpace(request.Tenant) ? _currentUser.GetTenant() : request.Tenant;
            await _dapperRepository.EnableWADeepIntegration(tenant);
            Guid userId = _currentUser.GetUserId();
            IntegrationAccountInfo? integrationAccount = null;
            IDictionary<string, string> data = null;
            request.ServiceProviderName = request.ServiceProviderName?.Replace(" ", "").ToLower();
            integrationAccount = CreateIntegrationEntity(request, userId, request.ServiceProviderName); 
            var apiKey = ApiKeyHelper.GenerateApiKey(integrationAccount.Id);
            integrationAccount.ApiKey = apiKey;
            var payloadForExcel = request.WebhookMapping?.ToDictionary(i => i.Value, j => j.Key);
            data = new Dictionary<string, string>(IntegrationTemplateBuilder.CreateIntegrationTemplate(tenant, integrationAccount, serviceProvider: request.ServiceProviderName, payloadForExcel: payloadForExcel));
            data[IntegrationTemplateKeys.PayloadKey] = JsonConvert.SerializeObject(payloadForExcel, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented });
            await _integrationAccountInfoRepoAsync.AddAsync(integrationAccount);
            string key = string.Empty;
            byte[] bytes = IntegrationExcelHelper.CreateExcelData(data).ToArray();
            string fileName = $"{tenant}-{integrationAccount.Id}-{DateTime.Now.ToString("yyyy_MM_dd-HH_mm_ss")}.xlsx";
            string folder = "Integration";
            key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", folder, fileName, bytes);
            string fileUrl = await _blobStorageService.GetPreSignedURL(_blobStorageService?.BucketName ?? "prd-leadratblack", key);
            integrationAccount.FileUrl = key;
            var existingWAAccounts = await _integrationAccountInfoRepoAsync.ListAsync(new IntegrationAccountInfoBySourceSpec(LeadSource.WhatsApp), cancellationToken);
            if ((request.ShouldSetPrimary ?? false) || (!existingWAAccounts.Any()))
            {
                integrationAccount.IsPrimary = true;
                if (existingWAAccounts.Any())
                {
                    existingWAAccounts.ForEach(i => i.IsPrimary = false);
                    await _integrationAccountInfoRepoAsync.UpdateRangeAsync(existingWAAccounts);
                }
            }
            await _integrationAccountInfoRepoAsync.UpdateAsync(integrationAccount);
            ViewWAIntegrationAccountDto dto = new()
            {
                Id = integrationAccount.Id,
                AccountName = request.AccountName,
                ApiKey = apiKey,
                TenantId = request.Tenant

            };
            if (integrationAccount?.WAPayloadMapping != null)
            {
                dto.WAPayloadMapping = integrationAccount?.WAPayloadMapping?.Adapt<WAPayloadMappingDto>() ?? new();

            }            
            await _whatsAppService.UpdateOrCreateIntegrationAccountAsync(dto);
            return new(fileUrl, default);
        }
        private IntegrationAccountInfo CreateIntegrationEntity(CreateWAIntegrationAccountRequest command, Guid userId, string? serviceProvider)
        {
            return new IntegrationAccountInfo()
            {
                Id = Guid.NewGuid(),
                AccountName = command.AccountName,
                LeadSource = command.LeadSource ?? LeadSource.WhatsApp,
                LicenseId = Guid.NewGuid(),
                JsonTemplate = JsonConvert.SerializeObject(command.WebhookMapping, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                CreatedBy = userId,
                WAPayloadMapping = new()
                {
                    OutboundMapping = command.OutboundMapping ?? new(),
                    WebhookMapping = command.WebhookMapping,
                    StatusMapping = command.MessageStatusMapping,
                    ResponseMapping = command.ResponseMapping,
                    ServiceProviderName = serviceProvider,
                    TemplateMapping = command.TemplateMapping,
                },
                WAApiInfo = command.WAApiInfos?.Adapt<List<WAApiInfo>>()
            };
            
        }
    }
}
