﻿using Lrb.Application.Common.PushNotification;
using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.DataManagement.Web.Mapping;
using Lrb.Application.DataManagement.Web.Specs;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.Property.Web.Specs;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;
using System.Collections.Concurrent;

namespace Lrb.Application.DataManagement.Web.Request
{
    public class BulkLeadAssignmentCheckRequest : IRequest<Response<Dictionary<Guid, List<Guid>>>>
    {
        public List<Guid> Ids { get; set; }
        public List<Guid> AssignTo { get; set; }
        public List<string>? Projects { get; set; }
        public DateTime? ScheduledDate { get; set; }
        public Guid? LeadStatusId { get; set; }
        public string? Notes { get; set; }
    }

    public class BulkLeadAssignmentCheckRequestHandler : IRequestHandler<BulkLeadAssignmentCheckRequest, Response<Dictionary<Guid, List<Guid>>>>
    {
        private readonly IRepositoryWithEvents<Prospect> _prospectRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Lead> _leadRepo;
        private readonly IRepositoryWithEvents<MasterProspectSource> _prospectSourceRepo;
        private readonly IRepositoryWithEvents<CustomProspectStatus> _prospectStatusRepo;
        //private readonly IRepositoryWithEvents<MasterLeadStatus> _masterLeadStatusRepo;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.LeadHistory> _leadHistoryRepo;
        private readonly INotificationSenderService _notificationSenderService;
        private readonly IUserService _userService;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<MasterPropertyType> _propertyTypeRepo;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customLeadStatusRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.TempProjects> _tempProjectRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.ProspectHistory> _prospcetHistoryRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        public BulkLeadAssignmentCheckRequestHandler( 
            IRepositoryWithEvents<Prospect> prospectRepo,
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepo,
            IRepositoryWithEvents<MasterProspectSource> prospectSourceRepo,
            IRepositoryWithEvents<CustomProspectStatus> prospectStatusRepo,
            ICurrentUser currentUser,
            //IRepositoryWithEvents<MasterLeadStatus> masterLeadStatusRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.LeadHistory> leadHistoryRepo,
            INotificationSenderService notificationSenderService,
            IUserService userService,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<MasterPropertyType> propertyTypeRepo,
            IRepositoryWithEvents<CustomMasterLeadStatus> customLeadStatusRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.TempProjects> tempProjectRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.ProspectHistory> prospcetHistoryRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.Project> projectRepo
            )
        {
            _prospectRepo = prospectRepo;
            _leadRepo = leadRepo;
            _prospectSourceRepo = prospectSourceRepo;
            _prospectStatusRepo = prospectStatusRepo;
            _currentUser = currentUser;
            //_masterLeadStatusRepo = masterLeadStatusRepo;
            _leadHistoryRepo = leadHistoryRepo;
            _notificationSenderService = notificationSenderService;
            _userService = userService;
            _leadRepositoryAsync = leadRepositoryAsync;
            _propertyTypeRepo = propertyTypeRepo;
            _customLeadStatusRepo = customLeadStatusRepo;
            _tempProjectRepo = tempProjectRepo;
            _prospcetHistoryRepo = prospcetHistoryRepo;
            _projectRepo = projectRepo;
        }
        public async Task<Response<Dictionary<Guid, List<Guid>>>> Handle(BulkLeadAssignmentCheckRequest request, CancellationToken cancellationToken)
        {
            if (!(request?.Ids?.Any() ?? false))
            {
                throw new InvalidOperationException("Provide Ids to Convert To Lead");
            }
            var userLeadAssignments = new Dictionary<Guid, List<Guid>>();
            List<Guid> prospectIds = new List<Guid> ();
            var existingProspects = await _prospectRepo.ListAsync(new GetProspectByIdsSpecs(request.Ids), cancellationToken);
            var existingConvertedProspects = existingProspects.Where(i => i.IsConvertedToLead).ToList();
            existingProspects.RemoveAll(i => i.IsConvertedToLead);
            var AssignedUsers = await _userService.GetListOfUsersByIdsAsync(request.AssignTo.Select(id => id.ToString()).ToList(), cancellationToken);
            int i = 0;
            var response = new DuplicateLeadAssigmentResponseDto();
            if (existingProspects?.Any() ?? true)
            {

                foreach (var prospect in existingProspects)
                {
                    var oldProspect = prospect?.Adapt<ViewProspectDto>();
                    var lead = CreateLeadFromProspect1(prospect);
                    if (!string.IsNullOrEmpty(request.Notes))
                    {
                        lead.Notes = request.Notes;
                    }
                    var currentUserId = _currentUser.GetUserId();

                    #region Status
                    // var status = await _masterLeadStatusRepo.ListAsync(cancellationToken);
                    var newStatus = (await _customLeadStatusRepo.ListAsync(cancellationToken));

                    if (request.LeadStatusId != null && request.LeadStatusId != Guid.Empty)
                    {
                        var customLeadStatus = newStatus.Where(i => i.Id == request.LeadStatusId).FirstOrDefault();
                        if (customLeadStatus?.Level < 1)
                        {
                            throw new ArgumentException("Provide Child Id of this Status");
                        }
                        lead.CustomLeadStatus = customLeadStatus ?? newStatus?.FirstOrDefault(i => i.IsDefault) ?? newStatus?.FirstOrDefault(i => i.Status == "new");
                        lead.ScheduledDate = request.ScheduledDate;
                    }
                    else
                    {
                        lead.CustomLeadStatus = newStatus?.FirstOrDefault(i => i.IsDefault) ?? newStatus?.FirstOrDefault(i => i.Status == "new");
                    }
                    #endregion 

                    #region Projects
                    List<Lrb.Domain.Entities.Project> tempProjects = new();
                    request.Projects = (request.Projects?.Any() ?? false) ? request.Projects.Where(i => !string.IsNullOrWhiteSpace(i)).ToList() : null;
                    if (request.Projects?.Any() ?? false)
                    {
                        foreach (var newProject in request.Projects)
                        {
                            Lrb.Domain.Entities.Project? existingProject = (await _projectRepo.ListAsync(new GetNewProjectsByIdV2Spec(newProject), cancellationToken)).FirstOrDefault();
                            if (existingProject != null)
                            {
                                tempProjects.Add(existingProject);
                            }
                            else
                            {
                                Domain.Entities.Project tempProject = new() { Name = newProject };
                                tempProject = await _projectRepo.AddAsync(tempProject, cancellationToken);
                                tempProjects.Add(tempProject);
                            }
                        }
                        lead.Projects = tempProjects;
                    }
                    #endregion

                    #region Enquiry
                    lead.TagInfo = new LeadTag();
                    var enquiry = prospect?.Enquiries?.Where(i => i.IsPrimary).FirstOrDefault();
                    List<MasterPropertyType>? propertyTypes = null;
                    if (enquiry?.PropertyTypes != null)
                    {
                        propertyTypes = await _propertyTypeRepo.ListAsync(new GetMasterPropertyTypeSpec(enquiry.PropertyTypes.Select(i => i.Id).ToList()));
                    }
                    if (enquiry != null)
                    {
                        var leadEnquiry = CreateLeadEnquiryFromProspectEnquiry1(enquiry);
                        leadEnquiry.PropertyType = propertyTypes?.FirstOrDefault();
                        leadEnquiry.PropertyTypes = propertyTypes;
                        lead.Enquiries = new List<LeadEnquiry>();
                        lead.Enquiries.Add(leadEnquiry);
                    }

                    #endregion

                    #region User
                    UserDetailsDto user = new();
                    if (!(request.AssignTo.Any(i => i == Guid.Empty)))
                    {
                        user = AssignedUsers[i];
                    }
                    lead.AssignTo = user.Id;
                    lead.AssignedFrom = _currentUser.GetUserId();
                    #endregion
                    try
                    {

                        Guid? parentLeadId = null;
                        var currentUserId1 =  _currentUser.GetUserId();
                        var rootLead = await _leadRepo.FirstOrDefaultAsync(new GetContcactNoSpec(lead.ContactNo, lead.AlternateContactNo), cancellationToken);
                        if (rootLead != null)
                        {
                            if (request.AssignTo.Contains(lead.AssignTo))
                            {
                                /* response.User = new DuplicateAssigmentUserDto
                                 {
                                     Id = lead.AssignTo
                                 };
                                 response.Leads.Add(new DuplicateAssigmentLeadDto
                                 {
                                     Id = prospect.Id
                                 });*/
                                prospectIds.Add(prospect.Id);

                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "ConvertToLeadRequestHander -> Handle() -> ScheduleNotificationsAsync()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                    
                }
            }
            if(prospectIds != null)
            {
                var users = await _userService.GetListOfUsersByIdsAsync(request.AssignTo.Select(i => i.ToString()).ToList(), cancellationToken);
                int leadsPerchunk = prospectIds.Count > 50 ? 50 : prospectIds.Count;
                var chunks = prospectIds.Chunk(leadsPerchunk).Select(i => new ConcurrentBag<Guid>(i));
                int skippedLeadCount = 0;
                int totalLeadCount = prospectIds.Count();
                var unAssignedCount = 0;
                List<DuplicateLeadAssigmentResponseDto>? SkippedLeadsInfo = new();
                List<Domain.Entities.Lead>? Leads = new List<Domain.Entities.Lead>();
                foreach (var chunk in chunks.ToList())
                {
                /*    var leads = await _leadRepo.ListAsync(new LeadsByIdsSpec(chunk.ToList() ?? new()));
                    if (leads != null && leads.Any())
                    {
                        (Leads, SkippedLeadsInfo) = await ReassignLeadsAsync(leads, request, users, cancellationToken, currentUserId: currentUserId1);
                        skippedLeadCount += SkippedLeadsInfo.FirstOrDefault()?.Leads?.Count ?? default;
                        unAssignedCount += SkippedLeadsInfo.DefaultIfEmpty()?.Select(i => i?.Leads?.Count ?? 0)?.Aggregate((a, b) => a + b) ?? 0;
                    }*/
                }
            }
            //return new Response<string>() { Succeeded = true, Message = $"{request.Ids.Count() - existingConvertedProspects.Count()} data converted. {existingConvertedProspects.Count()} were already converted to Leads." };
            return new Response<Dictionary<Guid, List<Guid>>> ()
            {
                Succeeded = true,
                Message = $"{response.Leads.Count} out of {request.Ids.Count} prospects(s) are already assigned to selected user in leads.",
                Data = userLeadAssignments
            };
        }

        #region Create Lead

        public Lrb.Domain.Entities.Lead CreateLeadFromProspect1(Prospect prospect)
        {
            Lrb.Domain.Entities.Lead lead = new();
            lead.Name = prospect?.Name?.Trim() ?? string.Empty;
            lead.ContactNo = prospect?.ContactNo?.Trim() ?? string.Empty;
            lead.AlternateContactNo = prospect?.AlternateContactNo?.Trim();
            lead.Notes = prospect?.Notes;
            lead.Email = prospect?.Email;
            lead.Address = new Address()
            {
                SubLocality = prospect?.Address?.SubLocality,
                Locality = prospect?.Address?.Locality,
                PlaceId = prospect?.Address?.PlaceId,
                City = prospect?.Address?.City,
                District = prospect?.Address?.District,
                State = prospect?.Address?.State,
                Country = prospect?.Address?.Country
            };
            lead.LeadNumber = lead?.Name?[0].ToString().ToUpper() + new Random().Next(1000, 9999).ToString() + DateTime.UtcNow.ToString("FFFFFFF");
            lead.AgencyName = prospect?.AgencyName?.Trim() ?? string.Empty;
            lead.Agencies = prospect?.Agencies;
            lead.Designation = prospect?.Designation;
            lead.ChannelPartners = prospect?.ChannelPartners;
            lead.ClosingManager = prospect?.ClosingManager;
            lead.SourcingManager = prospect?.SourcingManager;
            lead.ContactRecords = prospect?.ContactRecords;
            lead.CompanyName = prospect?.CompanyName;
            lead.ScheduledDate = DateTime.UtcNow;
            lead.Properties = prospect?.Properties;
            lead.Projects = prospect?.Projects;
            lead.Profession = prospect?.Profession ?? Profession.None;
            lead.ReferralEmail = prospect?.ReferralEmail;
            lead.ReferralContactNo = prospect?.ReferralContactNo;
            lead.ReferralName = prospect?.ReferralName;
            lead.Nationality = prospect?.Nationality;
            lead.OriginalOwner = prospect?.AssignTo;
            lead.PossesionType = prospect?.Enquiries?.FirstOrDefault()?.PossesionType;
            lead.LandLine = prospect?.LandLine;
            lead.Gender = prospect?.Gender;
            lead.MaritalStatus = prospect?.MaritalStatus;
            lead.DateOfBirth = prospect?.DateOfBirth;
            lead.AnniversaryDate = prospect?.AnniversaryDate;

            return lead;
        }
        #endregion
        #region Create Lead Enquiry

        public Lrb.Domain.Entities.LeadEnquiry CreateLeadEnquiryFromProspectEnquiry1(ProspectEnquiry enquiry)
        {
            var leadEnquiry = new LeadEnquiry();
            LeadSource? source = EnumFromDescription.GetValueFromDescription<LeadSource>(enquiry?.Source.DisplayName ?? string.Empty);
            leadEnquiry.LeadSource = source ?? LeadSource.Direct;
            leadEnquiry.IsPrimary = true;
            var address = new Address()
            {
                SubLocality = enquiry?.Address?.SubLocality,
                Locality = enquiry?.Address?.Locality,
                PlaceId = enquiry?.Address?.PlaceId,
                City = enquiry?.Address?.City,
                District = enquiry?.Address?.District,
                State = enquiry?.Address?.State,
                Country = enquiry?.Address?.Country,
            };
            leadEnquiry.BHKs = enquiry?.BHKs;
            leadEnquiry.BHKTypes = enquiry?.BHKTypes;
            leadEnquiry.EnquiryTypes = enquiry?.EnquiryTypes;
            leadEnquiry.EnquiredFor = enquiry?.EnquiryType ?? EnquiryType.None;
            leadEnquiry.SubSource = enquiry?.SubSource;
            leadEnquiry.LowerBudget = enquiry?.LowerBudget;
            leadEnquiry.UpperBudget = enquiry?.UpperBudget;
            leadEnquiry.CarpetArea = enquiry?.CarpetArea;
            leadEnquiry.CarpetAreaInSqMtr = enquiry?.CarpetAreaInSqMtr;
            leadEnquiry.NoOfBHKs = enquiry?.NoOfBhks ?? default;
            leadEnquiry.Address = address;
            leadEnquiry.BuiltUpArea = enquiry?.BuiltUpArea;
            leadEnquiry.BuiltUpAreaInSqMtr = enquiry?.BuiltUpAreaInSqMtr;
            leadEnquiry.SaleableArea = enquiry?.SaleableArea;
            leadEnquiry.SaleableAreaInSqMtr = enquiry?.SaleableAreaInSqMtr;
            leadEnquiry.NetArea = enquiry?.NetArea;
            leadEnquiry.NetAreaInSqMtr = enquiry?.NetAreaInSqMtr;
            leadEnquiry.NetAreaUnitId = enquiry?.NetAreaUnitId ?? Guid.Empty;
            leadEnquiry.PropertyArea = enquiry?.PropertyArea;
            leadEnquiry.PropertyAreaInSqMtr = enquiry?.PropertyAreaInSqMtr;
            leadEnquiry.PropertyAreaUnitId = enquiry?.PropertyAreaUnitId ?? Guid.Empty;
            leadEnquiry.UnitName = enquiry?.UnitName;
            leadEnquiry.ClusterName = enquiry?.ClusterName;

            if (enquiry?.Addresses?.Any() ?? false)
            {
                var addresses = new List<Address>();
                foreach (var item in enquiry.Addresses)
                {
                    addresses.Add(new Address()
                    {
                        SubLocality = item?.SubLocality,
                        Locality = item?.Locality,
                        PlaceId = item?.PlaceId,
                        City = item?.City,
                        District = item?.District,
                        State = item?.State,
                        Country = item?.Country,
                    });
                }
                leadEnquiry.Addresses = addresses;
            }
            return leadEnquiry;
        }

        #endregion

    }
}
