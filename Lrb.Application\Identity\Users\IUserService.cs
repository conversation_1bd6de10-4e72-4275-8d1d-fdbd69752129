using System.Security.Claims;
using DocumentFormat.OpenXml.Wordprocessing;
using Lrb.Application.Identity.Roles;
using Lrb.Application.Identity.Users.Password;
using Lrb.Application.Identity.Users.Web.Dto;

namespace Lrb.Application.Identity.Users;

public interface IUserService : ITransientService
{
    Task<PagedResponse<UserDetailsDto, string>> SearchAsync(UserListFilter filter, CancellationToken cancellationToken);

    Task<bool> ExistsWithNameAsync(string name);
    Task<bool> ExistsWithEmailAsync(string email, string? exceptId = null);
    Task<bool> ExistsWithPhoneNumberAsync(string phoneNumber, string? exceptId = null);

    Task<List<UserDetailsDto>> GetListAsync(CancellationToken cancellationToken);

    List<UserDetailsDto> GetList();
    Task<List<UserDetailsDto>> GetListAsync(int pageNumber, int pageSize, CancellationToken cancellationToken);
    Task<List<UserDetailsDto>> GetListAsync(int pageNumber, int pageSize);
    Task<int> GetCountAsync(CancellationToken cancellationToken);
    Task<UserDetailsDto> GetAsync(string userId, CancellationToken cancellationToken);
    Task<UserDetailsDto> GetByUserNameAsync(string userName, CancellationToken cancellationToken);
    Task<UserDetailsDto> GetByPhoneAsync(string phoneNumber, CancellationToken cancellationToken);
    Task<UserDetailsDto> GetByEmailAsync(string emailAddress, CancellationToken cancellationToken);
    Task<List<UserDetailsDto>> GetListOfUsersByIdsAsync(List<string> userIds, CancellationToken cancellationToken);

    Task<List<UserRoleDto>> GetRolesAsync(string userId, CancellationToken cancellationToken);

    Task<string> AssignRolesAsync(string userId, UserRolesRequest request, CancellationToken cancellationToken);

    Task<string> AssignRolesForBulkUpdateAsync(string userId, List<UserRoleDto> userRoles, CancellationToken cancellationToken);
    Task UpdateUserAsync(string userId);
    Task<List<string>> GetPermissionsAsync(string userId, string TenantId, CancellationToken cancellationToken);

    Task<List<RolePermissionDto>> GetRolesWithPermissionsAsync(string userId, CancellationToken cancellationToken);

    List<RolePermissionDto> GetRolesWithPermissions(string userId);

    Task<bool> HasPermissionAsync(string userId,string TenantId, string permission, CancellationToken cancellationToken = default);
    Task InvalidatePermissionCacheAsync(string userId, CancellationToken cancellationToken);

    Task ToggleStatusAsync(ToggleUserStatusRequest request, CancellationToken cancellationToken);

    Task<string> GetOrCreateFromPrincipalAsync(ClaimsPrincipal principal);
    Task<(string messages, string userId)> CreateAsync(CreateUserRequest request, string origin);
    Task UpdateAsync(UpdateUserRequest request, string userId);
    Task<bool> UpdateOTPAsync(string userId, string otp);
    Task<bool> UpdateImageAsync(string userId, string imagePath);
    Task<string> ConfirmEmailAsync(string userId, string code, string tenant, CancellationToken cancellationToken);
    Task<string> ConfirmPhoneNumberAsync(string userId, string code);
    Task<string> SendConfirmationEmailAsync(string email, string origin);
    Task<string> ForgotPasswordAsync(ForgotPasswordRequest request, string origin);
    Task<string> ResetPasswordAsync(ResetPasswordRequest request);
    Task<string> SetDefaultPasswordAsync(SetDefaultPasswordRequest request);
    Task<string> ChangePasswordAsync(ChangePasswordRequest request, string userId);
    Task<bool> DeleteAsync(string userId, CancellationToken cancellationToken);
    Task<object> PermanentDeleteAsync(string userId, CancellationToken cancellationToken);
    
    Task<Response<UserDetailsDto>> VerifyUserAsync(string name);
    Task<Response<bool>> VerifyOtpAsync(VerifyOtp modal);
    Task<List<UserDetailsDto>> GetListOfActiveUsersByIdsAsync(List<string> userIds, CancellationToken cancellationToken);
    Task<List<UserDetailsDto>> GetListOfActiveandDeactiveUsersByIdsAsync(List<string> userIds, CancellationToken cancellationToken);
    Task<List<Guid>> GetListOfActiveUserIdsAsync(CancellationToken cancellationToken);
    Task<List<Guid>> GetListOfInActiveUserIdsAsync(CancellationToken cancellationToken);
    Task<List<UserDetailsDto>> GetAdminsMFAEnabledUsersAsync(List<string>? userIds, CancellationToken cancellationToken);
    Task<List<Guid>> GetListOfAllUserIdsAsync(CancellationToken cancellationToken);
    Task<List<Guid>> GetMFADisabledUserIdsAsync(CancellationToken cancellationToken);
    Task<bool> UserNameExist(string userName);
    Task<(bool IsUpdated, string Message)> BulkToggleStatusAsync(BulkToggleUserStatusRequest request, CancellationToken cancellationToken);
    Task<(bool IsUpdated, string Message)> BulkToggleMFAAsync(List<Guid> userIds, CancellationToken cancellationToken);

    Task<bool> UpdateOrAddAttendanceClaimAsync(bool? IsEnabledForAllUsers, List<Guid>? userIds, Claim claim, string tenantId, CancellationToken cancellationToken);
    Task<bool> UpdateUserShiftTimingClaimAsync(string? user, Claim claim, string? tenantId, CancellationToken cancellationToken);
    Task<bool> RemoveClaimForAllAsync(Claim claim, string? tenantId, CancellationToken cancellationToken);
    Task<bool> IsInAdminRoleAsync(string? user, CancellationToken cancellationToken);
    Task<UserDetailsDto> GetUserAsync(string userId, CancellationToken cancellationToken);
    Task<List<Lrb.Application.UserDetails.Web.UserDetailsDto>> GetUserBasicDetailsAsync(List<Guid>? userIds, CancellationToken cancellationToken);
    Task<bool> RevokeUserRefreshToken (string userId,Guid? loginId, CancellationToken cancellationToken);
}
//public interface IMobileUserService : ITransientService
//{
//    Task<PagedResponse<UserDetailsDto, string>> SearchAsync(UserListFilter filter, CancellationToken cancellationToken);

//    Task<bool> ExistsWithNameAsync(string name);
//    Task<bool> ExistsWithEmailAsync(string email, string? exceptId = null);
//    Task<bool> ExistsWithPhoneNumberAsync(string phoneNumber, string? exceptId = null);

//    Task<List<UserDetailsDto>> GetListAsync(CancellationToken cancellationToken);

//    Task<int> GetCountAsync(CancellationToken cancellationToken);

//    Task<UserDetailsDto> GetAsync(string userId, CancellationToken cancellationToken);

//    Task<List<UserDetailsDto>> GetListOfUsersByIdsAsync(List<string> userIds, CancellationToken cancellationToken);

//    Task<List<UserRoleDto>> GetRolesAsync(string userId, CancellationToken cancellationToken);
//    Task<string> AssignRolesAsync(string userId, UserRolesRequest request, CancellationToken cancellationToken);

//    Task<List<string>> GetPermissionsAsync(string userId, CancellationToken cancellationToken);
//    Task<bool> HasPermissionAsync(string userId, string permission, CancellationToken cancellationToken = default);
//    Task InvalidatePermissionCacheAsync(string userId, CancellationToken cancellationToken);

//    Task ToggleStatusAsync(ToggleUserStatusRequest request, CancellationToken cancellationToken);

//    Task<string> GetOrCreateFromPrincipalAsync(ClaimsPrincipal principal);
//    Task<(string messages, string userId)> CreateAsync(CreateUserRequest request, string origin);
//    Task UpdateAsync(UpdateUserRequest request, string userId);

//    Task<string> ConfirmEmailAsync(string userId, string code, string tenant, CancellationToken cancellationToken);
//    Task<string> ConfirmPhoneNumberAsync(string userId, string code);

//    Task<string> ForgotPasswordAsync(ForgotPasswordRequest request, string origin);
//    Task<string> ResetPasswordAsync(ResetPasswordRequest request);
//    Task ChangePasswordAsync(ChangePasswordRequest request, string userId);
//}