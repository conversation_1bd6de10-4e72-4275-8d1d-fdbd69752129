﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Reports.Web.Data.Dtos.Campaign
{
    public class ViewDataCampaignDto : IDto
    {
        public Guid Id { get; set; }
        public string? Name { get; set; }
        public List<ViewDataReportByCampaignDto>? Data { get; set; }
        public long ConvertedDataCount { get; set; }
    }
    public class ViewDataReportByCampaignDto : IDto
    {
        public Guid StatusId { get; set; }
        public string? StatusDisplayName { get; set; }
        public long DataCount { get; set; }
    }
    public class DataCampaignDto : IDto
    {
        public Guid Id { get; set; }
        public string? Name { get; set; }
        public string? Statuses { get; set; }
        public List<StatusDto>? StatusDtos { get; set; }
        public long ConvertedDataCount { get; set; }
    }
}
