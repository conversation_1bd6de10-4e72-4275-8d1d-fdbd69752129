﻿using Lrb.Domain.Constants;
using System.Globalization;
using Lrb.Application.Property.Mobile.Specs;

namespace Lrb.Application.Property.Mobile.Requests
{
    public class GetGalleryDropdownDataRequest : PaginationFilter, IRequest<PagedResponse<string, string>>
    {

    }
    public class GetGalleryDropdownDataRequestHandler : IRequestHandler<GetGalleryDropdownDataRequest, PagedResponse<string, string>>
    {
        private readonly IReadRepository<Domain.Entities.Property> _propertyRepo;
        public GetGalleryDropdownDataRequestHandler(IReadRepository<Domain.Entities.Property> propertyRepo)
        {
            _propertyRepo = propertyRepo;
        }

        public async Task<PagedResponse<string, string>> Handle(GetGalleryDropdownDataRequest request, CancellationToken cancellationToken)
        {
            var properties = await _propertyRepo.ListAsync(new GetPropertyForGalleryDropdownSpec(request), cancellationToken);
            var imageKeys = GalleryImageDefaultKeys.defaultKeys ?? new List<string>();
            var galleryKeys = properties.Where(p => p.Galleries != null)
                .SelectMany(p => p.Galleries)
                .Select(g => g.ImageKey?.Trim())
                .Where(k => !imageKeys.Any(d => string.Equals(d, k, StringComparison.OrdinalIgnoreCase)))
                .GroupBy(k => k, StringComparer.OrdinalIgnoreCase)
                .Select(g => g.First())
                .Concat(imageKeys)
                .ToList();
            return new PagedResponse<string, string>(galleryKeys, 0);
        }
    }
}
