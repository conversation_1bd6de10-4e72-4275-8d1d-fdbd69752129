﻿using Lrb.Application.GlobalSettings.Web.Dto;
using Lrb.Domain.Entities.ErrorModule;
using Newtonsoft.Json;

namespace Lrb.Application.GlobalSettings.Web.Requests
{
    public class UpdateDuplicateFeatureInfoRequest : DuplicateFeatureDto, IRequest<Response<bool>>
    {
    }
    public class UpdateDuplicateFeatureInfoRequestHandler : IRequestHandler<UpdateDuplicateFeatureInfoRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<DuplicateLeadFeatureInfo> _duplicateInfoRepo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IDuplicateFeatureRepository _duplicateRepo;
        public UpdateDuplicateFeatureInfoRequestHandler(IRepositoryWithEvents<DuplicateLeadFeatureInfo> duplicateInfoRepo,
            ILeadRepositoryAsync leadRepositoryAsync,
            IDuplicateFeatureRepository duplicateRepo)
        {

            _duplicateInfoRepo = duplicateInfoRepo;
            _leadRepositoryAsync = leadRepositoryAsync;
            _duplicateRepo = duplicateRepo;
        }
        public async Task<Response<bool>> Handle(UpdateDuplicateFeatureInfoRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var existingFeatureInfo = (await _duplicateInfoRepo.ListAsync(cancellationToken))?.FirstOrDefault() ?? throw new Exception("duplicate feature settings not configured for this tenant");
                var statusIds = existingFeatureInfo.StutusIds;
                var sources = existingFeatureInfo.Sources;
                var duplicateFeatureInfo = request.Adapt(existingFeatureInfo);
                duplicateFeatureInfo.StutusIds = request.StutusIds?.Any() ?? false ? request.StutusIds : statusIds;
                duplicateFeatureInfo.Sources = request.Sources?.Any() ?? false  ?  request.Sources : sources;
                await _duplicateRepo.UpdateDuplicateFeatureSettings(duplicateFeatureInfo);
                return new(true);
            }
            catch (Exception e)
            {
                var error = new LrbError()
                {
                    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                    ErrorSource = e?.Source,
                    StackTrace = e?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "UpdateDuplicateFeatureInfoRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
        }
    }

}
