﻿using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Integration.Web.Helpers;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.LeadGenRequests;
using Lrb.Shared.Extensions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.WebUtilities;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Serilog;
using System.Reflection;

namespace Lrb.Application.Integration.Web.Requests.Webhook
{
    public class ListingSitesWebhookIntegrationRequest : IRequest<Response<bool>>
    {
        public string ApiKey { get; set; }
        public HttpRequest HttpRequest { get; set; }
        public string TenantId { get; set; }

        public ListingSitesWebhookIntegrationRequest(HttpRequest request, string tenant, string base64)
        {
            HttpRequest = request;
            TenantId = tenant;
            ApiKey = base64;
        }


        public class ListingSitesWebhookIntegrationHandler : IRequestHandler<ListingSitesWebhookIntegrationRequest, Response<bool>>
        {
            private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccRepo;
            private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Lead> _leadRepo;
            private readonly IRepositoryWithEvents<Lrb.Domain.Entities.GlobalSettings> _globalSettingsRepo;
            private readonly IRepositoryWithEvents<Domain.Entities.LeadHistory> _leadHistoryRepo;
            private readonly IUserService _userService;

            private readonly IMediator _mediator;
            private readonly ILogger _logger;
            public ListingSitesWebhookIntegrationHandler(
                IRepositoryWithEvents<IntegrationAccountInfo> integrationAccRepo,
                IMediator mediator,
                ILogger logger,
                IRepositoryWithEvents<Lrb.Domain.Entities.Lead> leadRepo,
                IRepositoryWithEvents<Lrb.Domain.Entities.GlobalSettings> globalSettingsRepo,
                IRepositoryWithEvents<LeadHistory> leadHistoryRepo,
                IUserService userService)
            {
                _integrationAccRepo = integrationAccRepo;
                _mediator = mediator;
                _logger = logger;
                _leadRepo = leadRepo;
                _globalSettingsRepo = globalSettingsRepo;
                _leadHistoryRepo = leadHistoryRepo;
                _userService = userService;
            }
            public async Task<Response<bool>> Handle(ListingSitesWebhookIntegrationRequest request, CancellationToken cancellationToken)
            {
                var accountId = request.ApiKey.GetAccountId();
                var integrationAccount = await _integrationAccRepo.FirstOrDefaultAsync(new GetWebhookIntegrationAccInfoWithAgencySpec(accountId), cancellationToken);
                if (integrationAccount == null)
                {
                    throw new InvalidOperationException("Integration account not found.");
                };
                //var accInc = await IntegrationAccountInfo.Api.ToString(), connection);
                var webhookMapping = integrationAccount.WebhookPayloadMapping?.WebhookMappings;
                if (webhookMapping == null)
                {
                    throw new InvalidOperationException("No Payload Info was found for the given account");
                };
                var mappings = await GetMappedValues(request.HttpRequest, webhookMapping);
                var mappingData = GetWebhookDto(mappings);
                if (mappingData.LeadSource == LeadSource.WhatsApp)
                {
                    var globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                    var mobile = ListingSitesHelper.ConcatenatePhoneNumberV2(null, mappingData.Mobile, globalSettings ?? new(), integrationAccount?.CountryCode ?? string.Empty);
                    var duplicateLead = await _leadRepo.FirstOrDefaultAsync(new LeadByContactNoSpec((mobile?.Length >= 1 ? mobile : "invalid ContactNo") ?? "invalid ContactNo", mappingData.Mobile ?? "invalid ContactNo"), cancellationToken);
                    if (duplicateLead != null)
                    {
                        if (!string.IsNullOrEmpty(mappingData.Notes) && mappingData.ApiKey != null)
                        {
                            duplicateLead.Notes = mappingData.Notes;
                            await _leadRepo.UpdateAsync(duplicateLead, cancellationToken);
                            var leadDto = duplicateLead.Adapt<ViewLeadDto>();
                            var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                            await UpdateLeadHistoryAsync(duplicateLead, cancellationToken: cancellationToken);
                        }
                        return new("false");
                    }
                }
                ListingSitesIntegrationRequest req = mappingData;
                _logger.Information("ListingSitesWebhookIntegrationHandler -> creating lead after mapping: " + JsonConvert.SerializeObject(req));
                /*                if (string.IsNullOrWhiteSpace(req?.Mobile?.Trim()))
                                {
                                    throw new Exception("Mobile number cannot be null or empty");
                                }*/
                req.ApiKey = request.ApiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.Webhook, req);
                bool shouldCreateNewLead = true;
                try
                {
                    CheckDuplicateLeadRequest checkDuplicateLeadRequest = req.Adapt<CheckDuplicateLeadRequest>();
                    checkDuplicateLeadRequest.SerializedData = req.Serialize();
                    //checkDuplicateLeadRequest.LeadSource = req.LeadSource;
                    var result = await _mediator.Send(checkDuplicateLeadRequest);
                    shouldCreateNewLead = result.Data;
                }
                catch (Exception ex) { }
                try
                {
                    await _mediator.Send(leadGenRequest);
                    if (shouldCreateNewLead)
                    {
                        await _mediator.Send(req);
                    }
                }
                catch (Exception ex)
                {
                    _logger.Information("ListingSitesWebhookIntegrationHandler -> Exception: " + ex.Message);
                }

                return new Response<bool>(true);
            }

            private async Task UpdateLeadHistoryAsync(Domain.Entities.Lead lead, CancellationToken cancellationToken = default)
            {
                try
                {
                    var fullLead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(lead.Id), cancellationToken) ?? throw new NotFoundException("Lead not found by the provided id.");
                    fullLead.LastModifiedBy = Guid.Empty;
                    var leadDto = fullLead.Adapt<ViewLeadDto>();
                    await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken);
                    var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                    var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignTo));
                    if (existingLeadHistory != null)
                    {
                        await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, null, true), cancellationToken);

                    }
                    else
                    {
                        await _leadHistoryRepo.AddAsync(leadHistory, cancellationToken);
                    }
                }
                catch (Exception ex)
                {
                    _logger.Information($"CheckDuplicateLeadRequestHandler -> UpdateLeadHistoryAsync -> Exception while updating notes of existing leads : {JsonConvert.SerializeObject(ex, settings: new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                    throw;
                }
            }
        }

        public async static Task<Dictionary<string, string>> GetMappedValues(HttpRequest httpRequest, IDictionary<string, string> waWebhookMapping)
        {
            //Stream stream = httpRequest.Body;
            // HttpRequestStreamReader reader = new(stream, System.Text.Encoding.UTF8);
            //var bodyInString = await reader.ReadToEndAsync();
            var bodyInString = "";
            if (httpRequest.HasFormContentType)
            {
                var form = await httpRequest.ReadFormAsync();
                var formData = form.ToDictionary(x => x.Key, x => x.Value.ToString());
                bodyInString = JsonConvert.SerializeObject(formData);
                if (string.IsNullOrWhiteSpace(bodyInString))
                {
                    throw new ArgumentNullException("Payload Cannot be empty");
                }
            }
            else if (httpRequest.QueryString.HasValue)
            {
                var queryParamsData = httpRequest.Query.ToDictionary(x => x.Key, x => x.Value.ToString());
                bodyInString = JsonConvert.SerializeObject(queryParamsData);
                if (string.IsNullOrWhiteSpace(bodyInString))
                {
                    throw new ArgumentNullException("Payload Cannot be empty");
                }
            }
            else
            {
                Stream stream = httpRequest.Body;
                HttpRequestStreamReader reader = new(stream, System.Text.Encoding.UTF8);
                bodyInString = await reader.ReadToEndAsync();
                if (string.IsNullOrWhiteSpace(bodyInString))
                {
                    throw new ArgumentNullException("Payload Cannot be empty");
                }
            }
            var parsedObj = JObject.Parse(bodyInString);
            var keyValues = GetKeysAndValues(parsedObj);
            Dictionary<string, string> mappedBody = GetUnMappedParameters(keyValues, waWebhookMapping);
            return (mappedBody ?? new());
        }

        public static ListingSitesIntegrationRequest GetWebhookDto(Dictionary<string, string> mappedValues)
        {
            ListingSitesIntegrationRequest dto = new ListingSitesIntegrationRequest();

            PropertyInfo[] properties = typeof(ListingSitesIntegrationRequest).GetProperties();

            foreach (var property in properties)
            {
                string key = $"#{property.Name}#";
                if (mappedValues.ContainsKey(key))
                {
                    string value = mappedValues[key];
                    if (property.PropertyType.IsEnum)
                    {
                        if (Enum.TryParse(property.PropertyType, value, true, out var enumValue))
                        {
                            property.SetValue(dto, enumValue);
                        }
                    }
                    else
                    {
                        property.SetValue(dto, value);
                    }
                }
                else if (key == "#LeadSource#")
                {
                    if (property.PropertyType == typeof(LeadSource))
                    {
                        property.SetValue(dto, LeadSource.Webhook);
                    }
                }

            }

            return dto;
        }


        public static Dictionary<string, string> GetKeysAndValues(JObject obj, string prefix = "")
        {
            Dictionary<string, string> keyValues = new Dictionary<string, string>();
            foreach (var property in obj.Properties())
            {
                if (property.Value.Type == JTokenType.Object)
                {
                    var values = GetKeysAndValues((JObject)property.Value, prefix + property.Name + ".");
                    foreach (var value in values)
                    {
                        keyValues.Add(value.Key, value.Value);
                    }
                }
                else if (property.Value.Type == JTokenType.Array)
                {
                    int index = 0;
                    foreach (var item in property.Value)
                    {
                        if (item.Type == JTokenType.Object)
                        {
                            var values = GetKeysAndValues((JObject)item, prefix + property.Name + "[" + index + "].");
                            foreach (var value in values)
                            {
                                keyValues.Add(value.Key, value.Value);
                            }
                        }
                        else
                        {
                            keyValues.Add(prefix + property.Name + "[" + index + "]", item.ToString());
                        }
                        index++;
                    }
                }
                else
                {
                    keyValues.Add(prefix + property.Name, property.Value.ToString());
                }
            }
            return keyValues;
        }
        public static Dictionary<string, string> GetUnMappedParameters(IDictionary<string, string> requestPayload, IDictionary<string, string> waWebhookMapping)
        {
            Dictionary<string, string> mappedBody = new();
            string? value = string.Empty;
            foreach (var map in waWebhookMapping)
            {
                if (map.Key != null)
                {
                    var splitValues = map.Value.Split(',', StringSplitOptions.RemoveEmptyEntries);

                    if (splitValues.Length > 1)
                    {
                        List<string> combinedValues = new();

                        foreach (var item in splitValues)
                        {
                            string trimmedItem = item.Trim();

                            if (requestPayload.TryGetValue(trimmedItem, out value) && !string.IsNullOrWhiteSpace(value))
                            {
                                combinedValues.Add(value);
                            }
                            else
                            {
                                combinedValues.Add(trimmedItem);
                            }
                        }
                        string finalValue = string.Join(", ", combinedValues);
                        if (map.Key == "#Name#")
                        {
                            finalValue = finalValue.Replace(",", " ");
                        }

                        if (mappedBody.ContainsKey(map.Key))
                        {
                            mappedBody[map.Key] = finalValue;
                        }
                        else
                        {
                            mappedBody.Add(map.Key, finalValue);
                        }
                        /* if (mappedBody.ContainsKey(map.Key))
                         {
                             mappedBody[map.Key] = string.Join(", ", combinedValues);
                         }
                         else
                         {
                             mappedBody.Add(map.Key, string.Join(", ", combinedValues));
                         }*/
                    }
                    else if (map.Key == "#LeadSource#" && Enum.TryParse<LeadSource>(map.Value, true, out var leadSourceEnum))
                    {
                        string leadSourceValue = GetEnumDescription(leadSourceEnum);

                        string actualSourceValue = leadSourceValue.Contains('/') ? leadSourceValue.Split('/')[1] : leadSourceValue;

                        if (mappedBody.ContainsKey(map.Key))
                        {
                            mappedBody[map.Key] = leadSourceValue;
                        }
                        else
                        {
                            mappedBody.Add(map.Key, leadSourceValue);
                        }
                    }
                    else if (requestPayload.TryGetValue(map.Value, out value) && !string.IsNullOrWhiteSpace(value))
                    {
                        if (mappedBody.ContainsKey(map.Key))
                        {
                            mappedBody[map.Key] = value;
                        }
                        else
                        {
                            mappedBody.Add(map.Key, value);
                        }
                    }
                    else if (map.Key == "#ApiKey#")
                    {
                        mappedBody[map.Key] = map.Value;
                    }
                    else
                    {
                        if (requestPayload.TryGetValue(map.Key.Trim('#'), out value) && !string.IsNullOrWhiteSpace(value))
                        {
                            if (mappedBody.ContainsKey(map.Key))
                            {
                                mappedBody[map.Key] = value;
                            }
                            else
                            {
                                mappedBody.Add(map.Key, value);
                            }
                        }
                        else
                        {
                            if (!mappedBody.ContainsKey(map.Key))
                            {
                                mappedBody.Add(map.Key, string.Empty);
                            }
                        }
                    }
                }
                else if (requestPayload.TryGetValue(map.Value, out value))
                {
                    var splitValues = value?.Split(',');

                    if (splitValues != null && splitValues.Length > 1)
                    {
                        List<string> replacedValues = new();

                        foreach (var item in splitValues)
                        {
                            string trimmedItem = item.Trim();
                            if (requestPayload.TryGetValue(trimmedItem, out string? replacedValue))
                            {
                                replacedValues.Add(replacedValue);
                            }
                            else
                            {
                                replacedValues.Add(trimmedItem);
                            }
                        }

                        if (mappedBody.ContainsKey(map.Key))
                        {
                            mappedBody[map.Key] = string.Join(", ", replacedValues);
                        }
                        else
                        {
                            mappedBody.Add(map.Key, string.Join(", ", replacedValues));
                        }
                    }
                    else
                    {
                        if (mappedBody.ContainsKey(map.Key))
                        {
                            mappedBody[map.Key] = value ?? string.Empty;
                        }
                        else
                        {
                            mappedBody.Add(map.Key, value ?? string.Empty);
                        }
                    }
                }
                else
                {
                    if (!mappedBody.ContainsKey(map.Key))
                    {
                        mappedBody.Add(map.Key, string.Empty);
                    }
                }
            }

            mappedBody = mappedBody.Where(i => !string.IsNullOrWhiteSpace(i.Value)).ToDictionary(i => i.Key, j => j.Value);
            return mappedBody;

        }
        public static string GetEnumDescription(Enum value)
        {
            var field = value.GetType().GetField(value.ToString());
            var attribute = field.Name;
            if (attribute != null)
            {
                return attribute.Replace(" ", "");
            }
            return value.ToString();
        }
    }
}

