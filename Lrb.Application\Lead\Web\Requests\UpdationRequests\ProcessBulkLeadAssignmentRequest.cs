﻿using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Requests.AssignmentRequests;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Shared.Extensions;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using System.Collections.Concurrent;

namespace Lrb.Application.Lead.Web.Requests.UpdationRequests
{
    public class ProcessBulkLeadAssignmentRequest : LeadAssignmentDto, IRequest<PagedResponse<DuplicateLeadAssigmentResponseDto, CountDto>>
    {
    }
    public class ProcessBulkLeadAssignmentRequestHandler : IRequestHandler<ProcessBulkLeadAssignmentRequest, PagedResponse<DuplicateLeadAssigmentResponseDto, CountDto>>
    {
        private readonly IRepositoryWithEvents<BulkCommonTracker> _bulkCommonTracker;
        protected readonly ILeadRepositoryAsync _leadRepositoryAsync;
        protected readonly IRepositoryWithEvents<DuplicateLeadFeatureInfo> _duplicateInfoRepo;
        protected readonly IUserService _userService;
        protected readonly IServiceProvider _serviceProvider;
        protected readonly ICurrentUser _currentUser;
        protected readonly INpgsqlRepository _npgsqlRepo;
        private string _className = typeof(ProcessBulkLeadAssignmentRequest).Name;
        private string _methodName = "Handle";
        private readonly IDapperRepository _dapperRepository;

        public ProcessBulkLeadAssignmentRequestHandler(IServiceProvider serviceProvider, IRepositoryWithEvents<BulkCommonTracker> bulkCommonTracker,
            ILeadRepositoryAsync leadRepositoryAsync, IRepositoryWithEvents<DuplicateLeadFeatureInfo> duplicateInfoRepo,
            ICurrentUser currentUser,
            INpgsqlRepository npgsqlRepo,
            IUserService userService,
            IDapperRepository dapperRepository
            )
        {


            _bulkCommonTracker = bulkCommonTracker;
            _leadRepositoryAsync = leadRepositoryAsync;
            _duplicateInfoRepo = duplicateInfoRepo;
            _serviceProvider = serviceProvider;
            _currentUser = currentUser;
            _npgsqlRepo = npgsqlRepo;
            _userService = userService;
            _dapperRepository = dapperRepository;
        }
        public async Task<PagedResponse<DuplicateLeadAssigmentResponseDto, CountDto>> Handle(ProcessBulkLeadAssignmentRequest request, CancellationToken cancellationToken)
        {
            var SkippedLeadsInfo = new List<DuplicateLeadAssigmentResponseDto>();
            BulkCommonTracker? tracker = null;
            try
            {
                int leadsPerchunk = request.LeadIds.Count > 50 ? 100 : request.LeadIds.Count;
                var currentUser = request.CurrentUserId ?? _currentUser.GetUserId();
                string tenantId = request.TenantId ?? _currentUser.GetTenant();
                var commonTracker = new BulkCommonTracker();
                if (request.LeadIds.Any() && request.LeadIds.Count >= 50)
                {
                    commonTracker = new BulkCommonTracker()
                    {
                        TotalCount = request.LeadIds.Count(),
                        Status = UploadStatus.InProgress,
                        RawJson = request.Serialize(),
                        ClassType = $"LeadsAssignment {request.AssignmentType}",
                        Module = "lead",
                        CreatedBy = currentUser,
                        LastModifiedBy = currentUser,
                    };
                    tracker = await _bulkCommonTracker.AddAsync(commonTracker);
                    request.TrackerId = tracker?.Id ?? default;
                }

                request.CurrentUserId = request.CurrentUserId ?? currentUser;
                var chunks = request.LeadIds.Chunk(leadsPerchunk).Select(i => new ConcurrentBag<Guid>(i));
                List<Task> tasks = new List<Task>();
                var totalLeadCount = request.LeadIds.Count;
                var skippedLeadCount = 0;
                List<Guid> adminIds = (await _npgsqlRepo.GetAdminIdsAsync(tenantId ?? string.Empty)).Where(i => i != (request.CurrentUserId)).ToList();
                var users = await _userService.GetListOfUsersByIdsAsync(request.UserIds.Select(i => i.ToString()).ToList(), cancellationToken);
                var adminDetails = await _dapperRepository.GetUserDetailsAsync(tenantId ?? string.Empty, adminIds);
                var userDetails = await _dapperRepository.GetUserDetailsAsync(tenantId ?? string.Empty, request.UserIds);
                foreach (var chunk in chunks.ToList())
                {
                    var newRequest = request.Adapt<BulkAssignmetDto>();
                    newRequest.LeadIds = chunk.ToList();
                    newRequest.AdminIds = adminIds ?? default;
                    newRequest.Users = users ?? default;
                    newRequest.AdminDetails = adminDetails.ToList() ?? default;
                    newRequest.UserBasicDetails = userDetails.ToList() ?? default;

                    var task = Task.Run(async () =>
                    {

                        using (var scope = _serviceProvider.CreateScope())
                        {
                            var mediator = scope.ServiceProvider.GetService<IMediator>();
                            if (request.CreateDuplicate == true)
                            {

                                var result = await mediator.Send(newRequest.Adapt<BulkAssignAndCreateDuplicateLeadRequest>());
                                skippedLeadCount = skippedLeadCount + result.Data.AssignedUsersCount;
                                SkippedLeadsInfo = result.Items.ToList();
                            }
                            else
                            {
                                var result = await mediator.Send(newRequest.Adapt<BulkLeadAssignmentRequest>());
                                skippedLeadCount = skippedLeadCount + result.Data.UnAssignedCount;
                                SkippedLeadsInfo = result.Items.ToList();
                            }   
                        }

                    });
                    tasks.Add(task);

                }
                await Task.WhenAll(tasks);
                #region Update Common Bulk Upload Tracker
                if (request.TrackerId != null && request.TrackerId != Guid.Empty)
                {
                    tracker = await _bulkCommonTracker.GetByIdAsync(request.TrackerId);
                    tracker.TotalUploadedCount = tracker.TotalUploadedCount + totalLeadCount;
                    tracker.Status = tracker.TotalUploadedCount < tracker.TotalCount ? UploadStatus.InProgress : UploadStatus.Completed;
                    tracker.DistinctCount = tracker.DistinctCount + (totalLeadCount - skippedLeadCount);
                    tracker.UpdatedCount = tracker.UpdatedCount + (totalLeadCount - skippedLeadCount);
                    tracker.DuplicateCount = tracker.DuplicateCount + skippedLeadCount;
                    await _bulkCommonTracker.UpdateAsync(tracker);
                }
                #endregion
            }
            catch (Exception ex)
            {
                if (tracker != null)
                {
                    tracker.Message = ex.ToString();
                    await _bulkCommonTracker.UpdateAsync(tracker);
                }
                throw new NotFoundException("No leads found by the provided lead Ids.");

            }

            return new(SkippedLeadsInfo, SkippedLeadsInfo.Count);
        }

        protected async Task AddLrbErrorAsync(Exception ex, string moduleName)
        {
            try
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = moduleName
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            catch
            {
                throw;
            }
        }
    }
}

