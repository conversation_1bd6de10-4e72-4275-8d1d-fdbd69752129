﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Domain.Entities;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;

namespace Lrb.Application.Integration.Mobile
{
    public class CreateIntegrationRequest : IRequest<Response<string>>
    {
        public string? AccountName { get; set; }
        public LeadSource Source { get; set; }
    }
    public class CreateIntegrationCommandHandler : IRequestHandler<CreateIntegrationRequest, Response<string>>
    {
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccountInfoRepositoryAsync;
        private readonly IBlobStorageService _blobStorageService;
        private readonly ICurrentUser _currentUser;

        public CreateIntegrationCommandHandler(
            IRepositoryWithEvents<IntegrationAccountInfo> integrationAccountInfoRepositoryAsync,
            ICurrentUser currentUser,
            IBlobStorageService blobStorageService)
        {
            _integrationAccountInfoRepositoryAsync = integrationAccountInfoRepositoryAsync;
            _blobStorageService = blobStorageService;
            _currentUser = currentUser;
        }
        public async Task<Response<string>> Handle(CreateIntegrationRequest request, CancellationToken cancellationToken)
        {
            var tenant = _currentUser.GetTenant();
            //Todo fix user and tenant
            Guid userId = Guid.NewGuid();
            IntegrationAccountInfo? integrationAccount = null;
            IDictionary<string, string> data = null;
            integrationAccount = CreateIntegrationEntity(request, userId);
            data = new Dictionary<string, string>(IntegrationTemplateBuilder.CreateIntegrationTemplate(tenant, integrationAccount));
            await _integrationAccountInfoRepositoryAsync.AddAsync(integrationAccount);
            string key = string.Empty;
            byte[] bytes = IntegrationExcelHelper.CreateExcelData(data).ToArray();
            string fileName = $"{integrationAccount.Id}-{DateTime.Now.ToString("yyyy_MM_dd-HH_mm_ss")}.xlsx";
            string folder = "Integration";
            key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", folder, fileName, bytes);
            string fileUrl = await _blobStorageService.GetPreSignedURL(_blobStorageService?.BucketName ?? "prd-leadratblack", key);
            integrationAccount.FileUrl = key;
            await _integrationAccountInfoRepositoryAsync.UpdateAsync(integrationAccount);
            return new(fileUrl, default);
        }
        private IntegrationAccountInfo CreateIntegrationEntity(CreateIntegrationRequest command, Guid userId)
        {
            return new IntegrationAccountInfo()
            {
                Id = Guid.NewGuid(),
                AccountName = command.AccountName,
                LeadSource = command.Source,
                LicenseId = Guid.NewGuid(),
                JsonTemplate = IntegrationTemplateBuilder.GetRequestBodyJsonFromFile(command.Source),
                CreatedBy = userId
            };
        }
    }
}
