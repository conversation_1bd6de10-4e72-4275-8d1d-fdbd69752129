namespace Lrb.Infrastructure.RateLimiting.Models;

/// <summary>
/// Information about rate limiting for a specific client
/// </summary>
public class RateLimitInfo
{
    /// <summary>
    /// Unique identifier for the client (IP, user ID, etc.)
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// Current request count in the time window
    /// </summary>
    public int RequestCount { get; set; }

    /// <summary>
    /// Maximum requests allowed in the time window
    /// </summary>
    public int RequestLimit { get; set; }

    /// <summary>
    /// Time when the current window started
    /// </summary>
    public DateTime WindowStart { get; set; }

    /// <summary>
    /// Time when the current window ends
    /// </summary>
    public DateTime WindowEnd { get; set; }

    /// <summary>
    /// Time when this entry was last updated
    /// </summary>
    public DateTime LastUpdated { get; set; }

    /// <summary>
    /// List of request timestamps for sliding window calculation
    /// </summary>
    public Queue<DateTime> RequestTimestamps { get; set; } = new();

    /// <summary>
    /// Whether the rate limit has been exceeded
    /// </summary>
    public bool IsLimitExceeded => RequestCount >= RequestLimit;

    /// <summary>
    /// Number of requests remaining in the current window
    /// </summary>
    public int RemainingRequests => Math.Max(0, RequestLimit - RequestCount);

    /// <summary>
    /// Time until the window resets (for fixed windows)
    /// </summary>
    public TimeSpan TimeUntilReset => WindowEnd > DateTime.UtcNow ? WindowEnd - DateTime.UtcNow : TimeSpan.Zero;

    /// <summary>
    /// Reset the rate limit info for a new window
    /// </summary>
    public void Reset(DateTime newWindowStart, TimeSpan windowDuration)
    {
        RequestCount = 0;
        WindowStart = newWindowStart;
        WindowEnd = newWindowStart.Add(windowDuration);
        LastUpdated = DateTime.UtcNow;
        RequestTimestamps.Clear();
    }

    /// <summary>
    /// Add a new request to the tracking
    /// </summary>
    public void AddRequest()
    {
        RequestCount++;
        LastUpdated = DateTime.UtcNow;
        RequestTimestamps.Enqueue(DateTime.UtcNow);
    }

    /// <summary>
    /// Clean up old timestamps for sliding window
    /// </summary>
    public void CleanupOldTimestamps(TimeSpan windowDuration)
    {
        var cutoffTime = DateTime.UtcNow.Subtract(windowDuration);
        while (RequestTimestamps.Count > 0 && RequestTimestamps.Peek() < cutoffTime)
        {
            RequestTimestamps.Dequeue();
        }
        RequestCount = RequestTimestamps.Count;
    }
}