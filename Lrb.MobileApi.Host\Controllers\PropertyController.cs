﻿using Lrb.Application.Common.Persistence;
using Lrb.Application.Property.Mobile;
using Lrb.Application.Property.Mobile.Dtos;
using Lrb.Application.Property.Mobile.Requests;
using Lrb.Application.Property.Mobile.Requests.V2;
using Lrb.Application.UserDetails.Mobile.Dtos;
using Lrb.Application.UserDetails.Mobile.Request;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.MasterData;
using Mapster;

namespace Lrb.MobileApi.Host.Controllers
{
    [Authorize]
    public class PropertyController : VersionedApiController
    {
        private readonly IRepository<Property> _repository;
        private readonly IRepository<Address> _addressRepo;
        private readonly IRepository<MasterPropertyType> _masterPrRepo;

        public PropertyController(IRepository<Property> repository, IRepository<Address> addressRepo, IRepository<MasterPropertyType> masterPrRepo)
        {
            _repository = repository;
            _addressRepo = addressRepo;
            _masterPrRepo = masterPrRepo;
        }
        [HttpGet]
        [TenantIdHeader]
        // [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get all property details.", "")]
        public Task<PagedResponse<GetAllPropertyDTO, string>> SearchAsync([FromQuery] GetAllPropertyRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpGet("{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get property details.", "")]
        public Task<Response<ViewPropertyDto>> GetAsync(Guid id)
        {
            return Mediator.Send(new GetProrpertyRequest(id));
        }

        [HttpPost]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Properties)]
        [OpenApiOperation("Create a new property.", "")]
        public Task<Response<UpdatePropertyDto>> CreateAsync([FromBody] CreatePropertyDto dto)
        {
            CreatePropertyRequest request = dto.Adapt<CreatePropertyRequest>();
            return Mediator.Send(request);
        }

        [HttpPut("{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Properties)]
        [OpenApiOperation("Update a property.", "")]
        public async Task<ActionResult<Response<Guid>>> UpdateAsync(UpdatePropertyDto dto, Guid id)
        {
            return id != dto.Id
                ? BadRequest()
                : Ok(await Mediator.Send(dto.Adapt<UpdatePropertyRequest>()));
        }

        [HttpDelete("{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Delete, LrbResource.Properties)]
        [OpenApiOperation("Delete a property.", "")]
        public Task<Response<Guid>> DeleteAsync(Guid id)
        {
            return Mediator.Send(new DeletePropertyByIdRequest(id));
        }

        [HttpPut("status/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Properties)]
        [OpenApiOperation("Update status of a property.", "")]
        public async Task<ActionResult<Response<bool>>> UpdateStatusAsync(UpdatePropertyStatusRequest request, Guid id)
        {
            return id != request.Id
                ? BadRequest()
                : Ok(await Mediator.Send(request));
        }

        [HttpPut("shareCount/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Properties)]
        [OpenApiOperation("Update shareCount of a property.", "")]
        public async Task<ActionResult<Response<bool>>> UpdateShareCountAsync(UpdatePropertyShareCountRequest request, Guid id)
        {
            return id != request.Id
                ? BadRequest()
                : Ok(await Mediator.Send(request));
        }

        [HttpPut("shareCount/new")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Properties)]
        [OpenApiOperation("Update shareCount of a property new.", "")]
        public async Task<ActionResult<Response<bool>>> UpdateShareCountAsync(UpdatePropertyShareCountNewRequest request)
        {
            return Ok(await Mediator.Send(request));
        }

        [HttpPut("tagInfo/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Properties)]
        [OpenApiOperation("Update tagInfo of a property.", "")]
        public async Task<ActionResult<Response<bool>>> UpdateTagInfoAsync(UpdatePropertyTagInfoRequest request, Guid id)
        {
            return id != request.Id
                ? BadRequest()
                : Ok(await Mediator.Send(request));
        }
        [HttpPut("Brochures/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Properties)]
        [OpenApiOperation("Update Brochures.", "")]
        public async Task<ActionResult<bool>> UpdateBrochuresAsync(Application.Property.Mobile.Requests.UpdateBrochureRequest request, Guid id)
        {
            return id != request.Id
                ? BadRequest()
                : Ok(await Mediator.Send(request));
        }

        [HttpGet("Brochures/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get Brochures.", "")]
        public Task<List<BrochureDto>> GetBrochuresAsync(Guid id)
        {
            return Mediator.Send(new Application.Property.Mobile.Requests.GetBrochuresRequest(id));
        }
        [HttpGet("matchingLeads")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get Matching Leads.", "")]
        public async Task<PagedResponse<LeadWithDegreeMatched, string>> GetMatchingLeadsAsync([FromQuery] Application.Property.Mobile.Requests.GetMatchingLeadsByPropertyIdRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("archived")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get Archived Properties.", "")]
        public async Task<PagedResponse<GetAllPropertyDTO, string>> GetArchivedProperties([FromQuery] GetArchivedPropertiesRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("ownernames")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get owner names in properties", "")]
        public async Task<Response<List<string>>> GetAgencyAsync()
        {
            return await Mediator.Send(new GetOwnerNamesRequest());
        }

        [HttpGet("addresses")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get all property address.", "")]
        public async Task<Response<List<string>>> GetAsync()
        {
            return await Mediator.Send(new GetAllPropertiesAddressRequest());
        }

        [HttpGet("lead-count")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get lead count for proeprties by ids.", "")]
        public async Task<Response<IEnumerable<PropertyLeadCountDto>>> GetAsync([FromQuery] GetLeadsCountByPropertyIdsRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("idwithname")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get all property ids and names.", "")]
        public async Task<Response<List<PropertiesWithIds>>> GetIdsWithNamesAsync()
        {
            return await Mediator.Send(new GetAllPropertiesByIAndNamesRequest());
        }

        [HttpPut("UpdatePropertyAssignedTo/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Assign, LrbResource.Properties)]
        [OpenApiOperation("Update AssignedTo of a Property", "")]
        public async Task<ActionResult<bool>> UpdateAsync(UpdatePropertyAssignedToRequest request, Guid id)
        {
            return id != request.PropertyId
                 ? BadRequest()
                 : Ok(await Mediator.Send(request));
        }
        [HttpPut("AssignToMultipleProperties")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Assign, LrbResource.Properties)]
        [OpenApiOperation("Assign properties to users.", "")]
        public async Task<Response<bool>> AssignPropertiessAsyncV2(PropertiesUserAssignmentsRequest request)
        {
            var res = await Mediator.Send(request);
            return res;
        }

        [HttpGet("Currency")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get All Currency .", "")]
        public async Task<Response<List<string>>> GetAllCuurencyy()
        {
            return await Mediator.Send(new GetAllPrpertyCurrencyRequest());
        }


        [HttpDelete("SoftdeleteProperties")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Delete, LrbResource.Properties)]
        [OpenApiOperation("Delete Properties")]
        public async Task<ActionResult<Response<bool>>> SoftDeleteAsync(PropertySoftDeleteRequest request)
        {
            return Ok(await Mediator.Send(request));
        }

        [HttpPut("RestoreDeletedProperties")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Delete, LrbResource.Properties)]
        [OpenApiOperation("Restore Deleted Properties")]

        public async Task<ActionResult<Response<bool>>> RetriveDeletedPropertiesAsync(DeletedPropertyRestoreRequest request)
        {
            return Ok(await Mediator.Send(request));
        }
        [HttpPost("multiplePropertiesByIds")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get multiple property details by IDs", "")]
        public Task<Response<List<ViewPropertyDto>>> GetMultipleAsync([FromBody] GetMultiplePropertiesRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpGet("GalleryDropdownData")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get Data Gallery Dropdown.", "")]
        public async Task<PagedResponse<string, string>> GetGalleryDropdownDataAsync([FromQuery] GetGalleryDropdownDataRequest request)
        {
            return await Mediator.Send(request);
        }
        #region Listing Management
        [HttpGet("all/listing")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get all property details.", "")]
        public Task<PagedResponse<GetAllPropertyForListingManagementDTO, string>> SearchAsync([FromQuery] GetAllPropertyForListingManagementRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpGet("listing/count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get all property count for listing.", "")]
        public Task<Response<GetPropertyCountForListingManagementDto>> CountAsync([FromQuery] GetPropertyTopLevelCountForListingManagementRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpPost("publish")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.PublishProperty, LrbResource.Properties)]
        [OpenApiOperation("send properties for listing", "")]
        public Task<Response<bool>> ListPropertyAsync(SendPropertyForListingApprovalRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpPost("delist")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.PublishProperty, LrbResource.Properties)]
        [OpenApiOperation("delist properties from listing.", "")]
        public Task<Response<bool>> DelistPropertyAsync(DelistPropertyFromPortalRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpPost("cloneProperty")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.CloneProperty, LrbResource.ListingIntegration)]
        [OpenApiOperation("Create a Clone property.", "")]
        public async Task<ActionResult<Guid>> CreateAsync(CreateClonePropertyRequest request)
        {
            var result = await Mediator.Send(request);
            return Ok(result);
        }
        [HttpGet("listing/matchingLeads")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get Matching Leads.", "")]
        public async Task<PagedResponse<LeadWithDegreeMatched, string>> GetMatchingLeadsAsync([FromQuery] GetMatchingLeadsByPropertyIdForListingRequest request)
        {
            return await Mediator.Send(request);
        }
        #endregion
        [HttpGet("all/properties")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.SetGeoFence, LrbResource.Users)]
        [OpenApiOperation("Get all lead properties.", "")]
        public async Task<Response<List<UserPropertyInfoDto>>> GetAllPropertiesAsync()
        {
            return await Mediator.Send(new GetAllPropertyInfoRequest());
        }
    }
}
