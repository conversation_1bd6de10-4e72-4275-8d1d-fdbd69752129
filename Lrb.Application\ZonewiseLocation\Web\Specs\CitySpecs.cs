﻿using Lrb.Application.ZonewiseLocation.Web.Dtos;
using Lrb.Application.ZonewiseLocation.Web.Requests;
using Lrb.Shared.Extensions;

namespace Lrb.Application.ZonewiseLocation.Web.Specs
{
    public class CitySpec : EntitiesByPaginationFilterSpec<City, CityDto>
    {
        public CitySpec(GetAllCitiesRequest filter) : base(filter)
        {
            Query.Include(i => i.State)
                 .Where(i => !i.IsDeleted)
             .OrderByDescending(i => i.LastModifiedOn);
            if (filter.Ids?.Any() ?? false)
            {
                Query.Where(i => filter.Ids.Contains(i.Id));
            }
            if (filter.StateIds?.Any() ?? false)
            {
                Query.Where(i => i.State != null && filter.StateIds.Contains(i.State.Id));
            }
            if (filter.Names?.Any() ?? false)
            {
                var normalizedNames = filter.Names.Select(i => i.LrbNormalize());
                Query.Where(i => normalizedNames.Contains(i.NormalizedName));
            }
            if (!string.IsNullOrWhiteSpace(filter.SearchText))
            {
                string normalizedText = filter.SearchText.LrbNormalize();
                Query.Where(i => !string.IsNullOrEmpty(i.NormalizedName) && i.NormalizedName.Contains(normalizedText));
            }
        }
    }
    public class CityCountSpec : Specification<City>
    {
        public CityCountSpec(GetAllCitiesRequest filter)
        {
            Query.Include(i => i.State)
                    .ThenInclude(i => i.Country)
                 .Include(i => i.Zones)
            .Include(i => i.UserAssignment)
                 .Where(i => !i.IsDeleted)
             .OrderByDescending(i => i.LastModifiedOn);
            if (filter.Ids?.Any() ?? false)
            {
                Query.Where(i => filter.Ids.Contains(i.Id));
            }
            if (filter.StateIds?.Any() ?? false)
            {
                Query.Where(i => i.State != null && filter.StateIds.Contains(i.State.Id));
            }
            if (filter.Names?.Any() ?? false)
            {
                var normalizedNames = filter.Names.Select(i => i.LrbNormalize());
                Query.Where(i => normalizedNames.Contains(i.NormalizedName));
            }
        }
    }
    public class CityByIdSpec : Specification<City>
    {
        public CityByIdSpec(Guid id)
        {
            Query.Include(i => i.State)
                    .ThenInclude(i => i.Country)
                 .Include(i => i.Zones)
                 .Include(i => i.UserAssignment)
                   .Include(i => i.Communities)
              .ThenInclude(j => j.SubCommunities)
                 .Where(i => !i.IsDeleted && i.Id == id)
             .OrderByDescending(i => i.LastModifiedOn);
        }
        public CityByIdSpec(List<Guid> ids)
        {
            Query.Include(i => i.State)
                    .ThenInclude(i => i.Country)
                 .Include(i => i.Zones)
                 .Include(i => i.UserAssignment)
                   .Include(i => i.Communities)
              .ThenInclude(j => j.SubCommunities)
                 .Where(i => !i.IsDeleted && ids.Contains(i.Id))
             .OrderByDescending(i => i.LastModifiedOn);
        }
    }
    public class CityByNameSpec : Specification<City>
    {
        public CityByNameSpec(string? name)
        {
            Query
                .Include(i => i.State)
                    .ThenInclude(i => i.Country)
                 .Include(i => i.Zones)
                   .Include(i => i.Communities)
              .ThenInclude(j => j.SubCommunities)
             .OrderByDescending(i => i.LastModifiedOn);

            var nornalizedName = name.LrbNormalize();
            Query.Where(i => !i.IsDeleted && i.NormalizedName != null && i.NormalizedName.Contains(nornalizedName));
        }
    }
}
