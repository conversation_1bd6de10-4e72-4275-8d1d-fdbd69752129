﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class AddStatusTeamConfigRelation : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CustomMasterLeadStatuses_TeamConfigurations_TeamConfigurati~",
                schema: "LeadratBlack",
                table: "CustomMasterLeadStatuses");

            migrationBuilder.DropIndex(
                name: "IX_CustomMasterLeadStatuses_TeamConfigurationId",
                schema: "LeadratBlack",
                table: "CustomMasterLeadStatuses");

            migrationBuilder.DropColumn(
                name: "TeamConfigurationId",
                schema: "LeadratBlack",
                table: "CustomMasterLeadStatuses");

            migrationBuilder.CreateTable(
                name: "CustomMasterLeadStatusTeamConfiguration",
                schema: "LeadratBlack",
                columns: table => new
                {
                    StatusesId = table.Column<Guid>(type: "uuid", nullable: false),
                    TeamConfigurationsId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomMasterLeadStatusTeamConfiguration", x => new { x.StatusesId, x.TeamConfigurationsId });
                    table.ForeignKey(
                        name: "FK_CustomMasterLeadStatusTeamConfiguration_CustomMasterLeadSta~",
                        column: x => x.StatusesId,
                        principalSchema: "LeadratBlack",
                        principalTable: "CustomMasterLeadStatuses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CustomMasterLeadStatusTeamConfiguration_TeamConfigurations_~",
                        column: x => x.TeamConfigurationsId,
                        principalSchema: "LeadratBlack",
                        principalTable: "TeamConfigurations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_CustomMasterLeadStatusTeamConfiguration_TeamConfigurationsId",
                schema: "LeadratBlack",
                table: "CustomMasterLeadStatusTeamConfiguration",
                column: "TeamConfigurationsId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CustomMasterLeadStatusTeamConfiguration",
                schema: "LeadratBlack");

            migrationBuilder.AddColumn<Guid>(
                name: "TeamConfigurationId",
                schema: "LeadratBlack",
                table: "CustomMasterLeadStatuses",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_CustomMasterLeadStatuses_TeamConfigurationId",
                schema: "LeadratBlack",
                table: "CustomMasterLeadStatuses",
                column: "TeamConfigurationId");

            migrationBuilder.AddForeignKey(
                name: "FK_CustomMasterLeadStatuses_TeamConfigurations_TeamConfigurati~",
                schema: "LeadratBlack",
                table: "CustomMasterLeadStatuses",
                column: "TeamConfigurationId",
                principalSchema: "LeadratBlack",
                principalTable: "TeamConfigurations",
                principalColumn: "Id");
        }
    }
}
