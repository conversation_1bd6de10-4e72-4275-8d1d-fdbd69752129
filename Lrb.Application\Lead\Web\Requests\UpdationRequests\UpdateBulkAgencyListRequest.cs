﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Lead.Web.Requests.UpdationRequests
{
    public class UpdateBulkAgencyListRequest : IRequest<Response<int>>
    {
        public List<string>? AgencyNames { get; set; }
        public List<Guid> Ids { get; set; } = default!;
        public bool? ShouldRemoveExistingAgency { get; set; }
        public Guid? CurrentUserId { get; set; }
        public string? TenantId { get; set; }


    }
    public class UpdateBulkAgencyListRequestRequestHandler : LeadCommonRequestHandler, IRequestHandler<UpdateBulkAgencyListRequest, Response<int>>
    {
        public UpdateBulkAgencyListRequestRequestHandler(IServiceProvider serviceProvider) : base(serviceProvider, typeof(UpdateBulkAgencyListRequest).Name, "Handle")
        {
        }
        public async Task<Response<int>> Handle(UpdateBulkAgencyListRequest request, CancellationToken cancellationToken)
        {
            try
            {
                List<Domain.Entities.Agency> agency = await _agencyRepo.ListAsync(new GetAllAgencySpec(request?.AgencyNames?.ConvertAll(i => i.ToLower()) ?? new()), cancellationToken);

                List<Domain.Entities.Lead> existingLeads = await _leadRepo.ListAsync(new LeadByIdSpec(request?.Ids ?? new()), cancellationToken);

                var oldLeadsDto = existingLeads.Adapt<List<ViewLeadDto>>();

                if (agency.Count >= 0 && existingLeads.Count > 0)
                {
                    await UpdateAgencyForMultipleLeadsAsync(existingLeads, agency, request?.ShouldRemoveExistingAgency ?? false, cancellationToken, request?.CurrentUserId, request?.TenantId);

                    return new(existingLeads.Count());
                }
                else
                {
                    return new(existingLeads.Count());
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{typeof(UpdateBulkAgencyListRequestRequestHandler).Name} - Handle()");
                throw;
            }
        }
    }
}