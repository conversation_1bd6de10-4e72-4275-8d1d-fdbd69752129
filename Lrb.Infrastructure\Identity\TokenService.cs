using Lrb.Application.Common.Exceptions;
using Lrb.Application.Common.Identity;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Identity.Cognito;
using Lrb.Application.Identity.Tokens;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Enums;
using Lrb.Infrastructure.Auth;
using Lrb.Infrastructure.Auth.Jwt;
using Lrb.Infrastructure.Multitenancy;
using Lrb.Infrastructure.OpenApi;
using Lrb.Infrastructure.Persistence.Repository.Interface;
using Lrb.Shared.Authorization;
using Lrb.Shared.Extensions;
using Lrb.Shared.Multitenancy;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Microsoft.Identity.Client;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Serilog;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;

namespace Lrb.Infrastructure.Identity;

internal class TokenService : ITokenService
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly SecuritySettings _securitySettings;
    private readonly JwtSettings _jwtSettings;
    private readonly LrbTenantInfo? _currentTenant;
    private readonly ICognitoService<WebClient> _cognitoService;
    private readonly ILogger _logger;
    private readonly IUserRepository _userRepo;
    private readonly ILeadRepositoryAsync _leadRepositoryAsync;
    private readonly IMemoryCache _memoryCache;

    public TokenService(
        UserManager<ApplicationUser> userManager,
        IOptions<JwtSettings> jwtSettings,
        LrbTenantInfo? currentTenant,
        IOptions<SecuritySettings> securitySettings,
        ICognitoService<WebClient> cognitoService,
        Serilog.ILogger logger, IUserRepository userRepo, ILeadRepositoryAsync leadRepositoryAsync,
        IMemoryCache memoryCache)
    {
        _userManager = userManager;
        _jwtSettings = jwtSettings.Value;
        _currentTenant = currentTenant;
        _securitySettings = securitySettings.Value;
        _cognitoService = cognitoService;
        _logger = logger;
        _userRepo = userRepo;
        _leadRepositoryAsync = leadRepositoryAsync;
        _memoryCache = memoryCache;
    }

    public async Task<CognitoTokenResponse> GetTokenAsync(TokenRequest request, string ipAddress, CancellationToken cancellationToken)
    {
        var lockedUser = await _userManager.FindByNameAsync(request.Username.Trim());
        if (lockedUser != null && lockedUser?.IsLocked == true)
        {
            throw new UnauthorizedException("Account is locked, Please reset your password", ErrorActionCode.Logout);
        }
        if (string.IsNullOrWhiteSpace(_currentTenant?.Id)
            || await _userManager.FindByNameAsync(request.Username.Trim()) is not { } user //await _userManager.FindByEmailAsync(request.Email.Trim().Normalize()) is not { } user
            || !await _userManager.CheckPasswordAsync(user, request.Password))
        {
            if (IsAccountLocked(request.Username))
            {
                var lockoutInfo = GetLockoutInfo(request.Username);
                LockAccount(request.Username, lockoutInfo.LockoutExpiry);
                lockoutInfo = GetLockoutInfo(request.Username);
                var remainingTime = lockoutInfo.LockoutExpiry - DateTime.UtcNow;
                throw new UnauthorizedException($"Account is locked. Try again in {remainingTime.Minutes} minutes {remainingTime.Seconds} sec.", ErrorActionCode.Logout);
            }
            await HandleFailedAuthentication(request.Username);

            throw new UnauthorizedException("Incorrect Credentials!", ErrorActionCode.Logout);
        }

        #region Check If Account is Locked
        if (IsAccountLocked(request.Username))
        {
            var lockoutInfo = GetLockoutInfo(request.Username);
            var remainingTime = lockoutInfo.LockoutExpiry - DateTime.UtcNow;
            throw new UnauthorizedException($"Account is locked. Try again in {remainingTime.Minutes} minutes {remainingTime.Seconds} sec.", ErrorActionCode.Logout);
        }
        #endregion

        #region Reset Failed Attempts 
        var retyInfo = GetAttemptsCacheKey(request.Username);
        if (retyInfo != null)
        {
            ResetFailedAttempts(request.Username);
        }
        #endregion

        if (!user.IsActive)
        {
            throw new UnauthorizedException("User Not Active. Please contact the administrator.", ErrorActionCode.Logout);
        }

        var IsNotAllowed = await _cognitoService.GetUserShiftTimeAsync(null, user.UserName);
        if (IsNotAllowed == true)
        {
            throw new UnauthorizedException(StringExtentions.NotAllowed, ErrorActionCode.Logout);
        }
        //Todo: Check this condition when Email Confirmation is mandatory
        //if (_securitySettings.RequireConfirmedAccount && !user.EmailConfirmed) 
        //{
        //    throw new UnauthorizedException("E-Mail not confirmed.");
        //}

        if (_currentTenant.Id != MultitenancyConstants.Root.Id)
        {
            if (!_currentTenant.IsActive)
            {
                throw new UnauthorizedException("Tenant is not Active. Please contact the Application Administrator.", ErrorActionCode.Logout);
            }

            if (DateTime.UtcNow > _currentTenant.ValidUpto)
            {
                throw new UnauthorizedException("Tenant Validity Has Expired. Please contact the Application Administrator.", ErrorActionCode.Logout);
            }
        }
        return await GenerateCognitoTokensAndUpdateUser(user, request.Password, ipAddress, cancellationToken);
    }

    public async Task<CognitoTokenResponse> RefreshTokenAsync(RefreshTokenRequest request, string ipAddress)
    {
        var userName = GetUsernameFromExpiredCognitoToken(request.Token);
        var user = await _userManager.FindByNameAsync(userName);
        if (user is null)
        {
            throw new UnauthorizedException("Incorrect Credentials!", ErrorActionCode.Logout);
        }

        //if (user.RefreshToken != request.RefreshToken || user.RefreshTokenExpiryTime <= DateTime.UtcNow)
        //{
        //    throw new UnauthorizedException("Invalid Refresh Token.");
        //}
        return await GenerateCognitoTokenFromRefreshToken(request.RefreshToken, user, ipAddress);
    }
    private async Task<CognitoTokenResponse> GenerateCognitoTokenFromRefreshToken(string refreshToken, ApplicationUser user, string ipAddress)
    {
        try
        {
            var result = await _cognitoService.GetTokensFromRefreshToken(refreshToken, user.UserName);
            try
            {
                user.RefreshToken = result.RefreshToken;
                user.RefreshTokenExpiryTime = DateTime.UtcNow.AddDays(30);

                await _userRepo.UpdateUserRefreshToken(user);
            }
            catch (Exception ex)
            {
                // ignore
            }
            return result;
        }
        catch (Exception ex)
        {
            _logger.Information("Exception GenerateCognitoTokenFromRefreshToken() => " + ex.Message ?? ex.InnerException?.Message ?? string.Empty + " UserName " + user.UserName + " RefreshToken " + refreshToken);
            throw new UnauthorizedException("Unauthorized Action.", ErrorActionCode.Logout);
        }

    }
    private async Task<CognitoTokenResponse> GenerateCognitoTokensAndUpdateUser(ApplicationUser user, string password, string ipAddress, CancellationToken cancellationToken)
    {
        var result = await _cognitoService.TryLoginAsync(user.UserName, password, cancellationToken);
        user.RefreshToken = result.RefreshToken;
        user.RefreshTokenExpiryTime = DateTime.UtcNow.AddDays(30);

        try
        {
            await _userRepo.UpdateUserRefreshToken(user);
        }
        catch (Exception ex)
        {
            var error = new LrbError()
            {
                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                ErrorSource = ex?.Source,
                StackTrace = ex?.StackTrace,
                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                ErrorModule = "TokenService -> GenerateCognitoTokensAndUpdateUser()"
            };
            await _leadRepositoryAsync.AddErrorAsync(error);
        }

        return result;
    }

    private async Task<TokenResponse> GenerateTokensAndUpdateUser(ApplicationUser user, string ipAddress)
    {
        string token = GenerateJwt(user, ipAddress);

        user.RefreshToken = GenerateRefreshToken();
        user.RefreshTokenExpiryTime = DateTime.UtcNow.AddDays(_jwtSettings.RefreshTokenExpirationInDays);

        try
        {
            await _userRepo.UpdateUserRefreshToken(user);
        }
        catch (Exception ex)
        {
            var error = new LrbError()
            {
                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                ErrorSource = ex?.Source,
                StackTrace = ex?.StackTrace,
                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                ErrorModule = "TokenService -> GenerateTokensAndUpdateUser()"
            };
            await _leadRepositoryAsync.AddErrorAsync(error);
        }

        return new TokenResponse(token, user.RefreshToken, user.RefreshTokenExpiryTime);
    }

    private string GenerateJwt(ApplicationUser user, string ipAddress) =>
        GenerateEncryptedToken(GetSigningCredentials(), GetClaims(user, ipAddress));

    private IEnumerable<Claim> GetClaims(ApplicationUser user, string ipAddress) =>
        new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, user.Id),
            new(ClaimTypes.Email, user.Email),
            new(LrbClaims.Fullname, $"{user.FirstName} {user.LastName}"),
            new(ClaimTypes.Name, user.FirstName ?? string.Empty),
            new(ClaimTypes.Surname, user.LastName ?? string.Empty),
            new(LrbClaims.IpAddress, ipAddress),
            new(LrbClaims.Tenant, _currentTenant!.Id),
            new(LrbClaims.ImageUrl, user.ImageUrl ?? string.Empty),
            new(ClaimTypes.MobilePhone, user.PhoneNumber ?? string.Empty)
        };

    private string GenerateRefreshToken()
    {
        byte[] randomNumber = new byte[32];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(randomNumber);
        return Convert.ToBase64String(randomNumber);
    }

    private string GenerateEncryptedToken(SigningCredentials signingCredentials, IEnumerable<Claim> claims)
    {
        var token = new JwtSecurityToken(
           claims: claims,
           expires: DateTime.UtcNow.AddMinutes(_jwtSettings.TokenExpirationInMinutes),
           signingCredentials: signingCredentials);
        var tokenHandler = new JwtSecurityTokenHandler();
        return tokenHandler.WriteToken(token);
    }

    private ClaimsPrincipal GetPrincipalFromExpiredToken(string token)
    {
        var tokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtSettings.Key)),
            ValidateIssuer = false,
            ValidateAudience = false,
            RoleClaimType = ClaimTypes.Role,
            ClockSkew = TimeSpan.Zero,
            ValidateLifetime = false
        };
        var tokenHandler = new JwtSecurityTokenHandler();
        var principal = tokenHandler.ValidateToken(token, tokenValidationParameters, out var securityToken);
        if (securityToken is not JwtSecurityToken jwtSecurityToken ||
            !jwtSecurityToken.Header.Alg.Equals(
                SecurityAlgorithms.HmacSha256,
                StringComparison.InvariantCultureIgnoreCase))
        {
            throw new UnauthorizedException("Invalid Token.");
        }

        return principal;
    }

    private string GetUsernameFromExpiredCognitoToken(string token)
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        _logger.Information("TokenService: GetUsernameFromExpiredCognitoToken() - > Token: " + token);
        var jwtToken = tokenHandler.ReadJwtToken(token);
        if (jwtToken != null)
        {
            var tokenUse = jwtToken.Claims.First(x => x.Type == IdTokenClaimsKey.TokenUse).Value;
            if (tokenUse == "id")
            {

                try
                {
                    return jwtToken.Claims.First(x => x.Type == IdTokenClaimsKey.CognitoUserId).Value;
                }
                catch
                {
                    throw new UnauthorizedException("Invalid Token.");
                }
            }
        }
        throw new UnauthorizedException("Invalid Token.");
    }

    private SigningCredentials GetSigningCredentials()
    {
        byte[] secret = Encoding.UTF8.GetBytes(_jwtSettings.Key);
        return new SigningCredentials(new SymmetricSecurityKey(secret), SecurityAlgorithms.HmacSha256);
    }

    #region Retry Attempt
    private async Task HandleFailedAuthentication(string username)
    {
        var failedAttempts = IncrementFailedAttempts(username);

        if (failedAttempts >= _securitySettings.MaxRetry)
        {
            // Lock the account
            var lockoutExpiry = DateTime.UtcNow.AddMinutes(_securitySettings.LockoutDuration);
            LockAccount(username, lockoutExpiry);
            if (failedAttempts >= 7)
            {
                await _userRepo.UpdateUserLockAccount(true, username, _currentTenant?.Id ?? string.Empty);
                await _cognitoService.AddUserLockedSettingClaimAync(username, true);
                throw new UnauthorizedException("Account is locked, Please reset your password", ErrorActionCode.Logout);
            }
            else
            {
                var lockoutInfo = GetLockoutInfo(username);
                var remainingTime = lockoutInfo.LockoutExpiry - DateTime.UtcNow;
                throw new UnauthorizedException($"Account has been locked for {remainingTime.Minutes} minutes {remainingTime.Seconds} second due to multiple failed login attempts.");
            }
        }

        var remainingAttempts = _securitySettings.MaxRetry - failedAttempts;

        throw new UnauthorizedException($"Incorrect credential!, You have {remainingAttempts} attempts left before your account is locked.", ErrorActionCode.Logout);
    }

    private bool IsAccountLocked(string username)
    {
        var lockoutInfo = GetLockoutInfo(username);
        return lockoutInfo != null && DateTime.UtcNow < lockoutInfo.LockoutExpiry;
    }

    private LockoutInfo GetLockoutInfo(string username)
    {
        var lockoutKey = GetLockoutCacheKey(username);
        return _memoryCache.Get<LockoutInfo>(lockoutKey);
    }

    private int IncrementFailedAttempts(string username)
    {
        var attemptsKey = GetAttemptsCacheKey(username);
        var currentAttempts = _memoryCache.Get<int?>(attemptsKey) ?? 0;
        var newAttempts = currentAttempts + 1;

        // Cache failed attempts for lockout duration + buffer
        var cacheExpiry = new TimeSpan();
        if (currentAttempts <= _securitySettings.MaxRetry)
        {
            cacheExpiry = TimeSpan.FromMinutes(60);
        }
        else
        {
            var lockoutInfo = GetLockoutInfo(username);
            cacheExpiry = TimeSpan.FromMinutes(60);
        }
        _memoryCache.Set(attemptsKey, newAttempts, cacheExpiry);

        return newAttempts;
    }

    private void ResetFailedAttempts(string username)
    {
        var attemptsKey = GetAttemptsCacheKey(username);
        var lockoutKey = GetLockoutCacheKey(username);

        _memoryCache.Remove(attemptsKey);
        _memoryCache.Remove(lockoutKey);

        _logger.Information("Reset failed attempts for user: {Username}", username);
    }

    private void LockAccount(string username, DateTime lockoutExpiry)
    {
        var lockoutKey = GetLockoutCacheKey(username);
        var lockInfo = GetLockoutInfo(username);
        var attemptsKey = GetAttemptsCacheKey(username);
        var currentAttempts = _memoryCache.Get<int?>(attemptsKey) ?? 0;
        LockoutInfo? lockoutInfo = null;
        if (lockInfo != null)
        {
            lockoutInfo = new()
            {
                Username = lockInfo.Username,
                LockoutExpiry = lockoutExpiry.AddMinutes(1),
                LockedAt = DateTime.UtcNow
            };
        }
        else
        {
            lockoutInfo = new LockoutInfo
            {
                Username = username,
                LockoutExpiry = lockoutExpiry,
                LockedAt = DateTime.UtcNow
            };
        }
        TimeSpan? cacheExpiry = null;
        if (currentAttempts == 5)
        {
            lockoutInfo.LockoutExpiry = DateTime.UtcNow.AddMinutes(3);
            cacheExpiry = TimeSpan.FromMinutes(3);
        }
        else if (currentAttempts == 6)
        {
            lockoutInfo.LockoutExpiry = DateTime.UtcNow.AddMinutes(5);
            cacheExpiry = TimeSpan.FromMinutes(5);
        }
        else
        {
            lockoutInfo.LockoutExpiry = DateTime.UtcNow.AddMinutes(1);
            cacheExpiry = TimeSpan.FromMinutes(_securitySettings.LockoutDuration);
        }
        _memoryCache.Set(lockoutKey, lockoutInfo, cacheExpiry.Value);
    }

    private string GetAttemptsCacheKey(string username) => $"failed_attempts_{username.ToLower()}";
    private string GetLockoutCacheKey(string username) => $"lockout_{username.ToLower()}";
    #endregion

    public async Task<bool> ResetFailedLoginAttemptsAsync(string userName, CancellationToken cancellationToken)
    {
        await _userRepo.UpdateUserLockAccount(false, userName, _currentTenant?.Id ?? string.Empty);
        var retyInfo = GetLockoutInfo(userName);
        if (retyInfo != null)
        {
            ResetFailedAttempts(userName);
            return true;
        }
        return false;
    }

    public async Task<AccountLockoutInfo> GetFailedLoginAttemptsAsync(string userName, CancellationToken cancellationToken)
    {
        var lockoutInfo = GetLockoutInfo(userName);
        if (lockoutInfo != null)
        {
            var remainingTime = lockoutInfo.LockoutExpiry - DateTime.UtcNow;
            AccountLockoutInfo lockedAccount = new()
            {
                Username = userName,
                LockoutExpiry = lockoutInfo.LockoutExpiry,
                RemainingTime = remainingTime,
                LockedAt = lockoutInfo.LockedAt
            };
            return lockedAccount;
        }
        return null;
    }
    public async Task<bool> UpdateLogoutDetails(LogoutRequest request, string tenantId, CancellationToken cancellationToken)
    {
        if (request.UserId != null)
        {
            return await _userRepo.UpdateUserLogoutDetails(request.UserId, request.LogoutType, DateTime.Now,tenantId);
        }
        return false;
    }

}



internal class MobileTokenService : IMobileTokenService
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly SecuritySettings _securitySettings;
    private readonly JwtSettings _jwtSettings;
    private readonly LrbTenantInfo? _currentTenant;
    private readonly ICognitoService<MobileClient> _cognitoService;
    private readonly ILogger _logger;
    private readonly IUserRepository _userRepo;
    private readonly ILeadRepositoryAsync _leadRepositoryAsync;
    private readonly IMemoryCache _memoryCache;

    public MobileTokenService(
        UserManager<ApplicationUser> userManager,
        IOptions<JwtSettings> jwtSettings,
        LrbTenantInfo? currentTenant,
        IOptions<SecuritySettings> securitySettings,
        ICognitoService<MobileClient> cognitoService,
        Serilog.ILogger logger,
        IUserRepository userRepo,
        ILeadRepositoryAsync leadRepositoryAsync,
        IMemoryCache memoryCache)
    {
        _userManager = userManager;
        _jwtSettings = jwtSettings.Value;
        _currentTenant = currentTenant;
        _securitySettings = securitySettings.Value;
        _cognitoService = cognitoService;
        _logger = logger;
        _userRepo = userRepo;
        _leadRepositoryAsync = leadRepositoryAsync;
        _memoryCache = memoryCache;
    }

    public async Task<CognitoTokenResponse> GetTokenAsync(TokenRequest request, string ipAddress, CancellationToken cancellationToken)
    {
        var lockedUser = await _userManager.FindByNameAsync(request.Username.Trim());
        if (lockedUser != null && lockedUser?.IsLocked == true)
        {
            throw new UnauthorizedException("Account is locked, Please reset your password", ErrorActionCode.Logout);
        }
        if (string.IsNullOrWhiteSpace(_currentTenant?.Id)
            || await _userManager.FindByNameAsync(request.Username.Trim()) is not { } user //await _userManager.FindByEmailAsync(request.Email.Trim().Normalize()) is not { } user
            || !await _userManager.CheckPasswordAsync(user, request.Password))
        {
            if (IsAccountLocked(request.Username))
            {
                var lockoutInfo = GetLockoutInfo(request.Username);
                LockAccount(request.Username, lockoutInfo.LockoutExpiry);
                lockoutInfo = GetLockoutInfo(request.Username);
                var remainingTime = lockoutInfo.LockoutExpiry - DateTime.UtcNow;
                throw new UnauthorizedException($"Account is locked. Try again in {remainingTime.Minutes} minutes {remainingTime.Seconds} sec.", ErrorActionCode.Logout);
            }
            await HandleFailedAuthentication(request.Username);

            throw new UnauthorizedException("Unauthorized Action.", ErrorActionCode.Logout);
        }

        #region Check If Account is Locked
        if (IsAccountLocked(request.Username))
        {
            var lockoutInfo = GetLockoutInfo(request.Username);
            var remainingTime = lockoutInfo.LockoutExpiry - DateTime.UtcNow;
            throw new UnauthorizedException($"Account is locked. Try again in {remainingTime.Minutes} minutes {remainingTime.Seconds} sec.", ErrorActionCode.Logout);
        }
        #endregion

        #region Reset Failed Attempts 
        var retyInfo = GetAttemptsCacheKey(request.Username);
        if (retyInfo != null)
        {
            ResetFailedAttempts(request.Username);
        }
        #endregion

        if (!user.IsActive)
        {
            throw new UnauthorizedException("User Not Active. Please contact the administrator.", ErrorActionCode.Logout);
        }

        //Todo: Check this condition when Email Confirmation is mandatory
        //if (_securitySettings.RequireConfirmedAccount && !user.EmailConfirmed)
        //{
        //    throw new UnauthorizedException("E-Mail not confirmed.");
        //}
        var IsNotAllowed = await _cognitoService.GetUserShiftTimeAsync(null, user.UserName);
        if (IsNotAllowed == true)
        {
            throw new UnauthorizedException(StringExtentions.NotAllowed, ErrorActionCode.Logout);
        }
        if (_currentTenant.Id != MultitenancyConstants.Root.Id)
        {
            if (!_currentTenant.IsActive)
            {
                throw new UnauthorizedException("Tenant is not Active. Please contact the Application Administrator.", ErrorActionCode.Logout);
            }

            if (DateTime.UtcNow > _currentTenant.ValidUpto)
            {
                throw new UnauthorizedException("Tenant Validity Has Expired. Please contact the Application Administrator.", ErrorActionCode.Logout);
            }
        }
        return await GenerateCognitoTokensAndUpdateUser(user, request.Password, ipAddress, cancellationToken);
    }

    public async Task<CognitoTokenResponse> RefreshTokenAsync(RefreshTokenRequest request, string ipAddress)
    {
        var userName = GetUsernameFromExpiredCognitoToken(request.Token);
        var user = await _userManager.FindByNameAsync(userName);
        if (user is null)
        {
            throw new UnauthorizedException("Unauthorized Action.", ErrorActionCode.Logout);
        }

        //if (user.RefreshToken != request.RefreshToken || user.RefreshTokenExpiryTime <= DateTime.UtcNow)
        //{
        //    throw new UnauthorizedException("Invalid Refresh Token.");
        //}
        return await GenerateCognitoTokenFromRefreshToken(request.RefreshToken, user, ipAddress);
    }
    private async Task<CognitoTokenResponse> GenerateCognitoTokenFromRefreshToken(string refreshToken, ApplicationUser user, string ipAddress)
    {
        try
        {
            var result = await _cognitoService.GetTokensFromRefreshToken(refreshToken, user.UserName);

            try
            {
                user.RefreshToken = result.RefreshToken;
                user.RefreshTokenExpiryTime = DateTime.UtcNow.AddDays(30);


                await _userRepo.UpdateUserRefreshToken(user);
            }
            catch (Exception ex)
            {
                // ignore
            }
            return result;
        }
        catch (Exception ex)
        {
            _logger.Information("Exception GenerateCognitoTokenFromRefreshToken() => " + ex.Message ?? ex.InnerException?.Message ?? string.Empty + " UserName " + user.UserName + " RefreshToken " + refreshToken);
            throw new UnauthorizedException("Unauthorized Action.", ErrorActionCode.Logout);
        }
    }
    private async Task<CognitoTokenResponse> GenerateCognitoTokensAndUpdateUser(ApplicationUser user, string password, string ipAddress, CancellationToken cancellationToken)
    {
        var result = await _cognitoService.TryLoginAsync(user.UserName, password, cancellationToken);
        user.RefreshToken = result.RefreshToken;
        user.RefreshTokenExpiryTime = DateTime.UtcNow.AddDays(30);

        try
        {
            await _userRepo.UpdateUserRefreshToken(user);
        }
        catch (Exception ex)
        {
            var error = new LrbError()
            {
                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                ErrorSource = ex?.Source,
                StackTrace = ex?.StackTrace,
                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                ErrorModule = "MobileTokenService -> GenerateCognitoTokensAndUpdateUser()"
            };
            await _leadRepositoryAsync.AddErrorAsync(error);
        }

        return result;
    }

    private async Task<TokenResponse> GenerateTokensAndUpdateUser(ApplicationUser user, string ipAddress)
    {
        string token = GenerateJwt(user, ipAddress);

        user.RefreshToken = GenerateRefreshToken();
        user.RefreshTokenExpiryTime = DateTime.UtcNow.AddDays(_jwtSettings.RefreshTokenExpirationInDays);

        try
        {
            await _userRepo.UpdateUserRefreshToken(user);
        }
        catch (Exception ex)
        {
            var error = new LrbError()
            {
                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                ErrorSource = ex?.Source,
                StackTrace = ex?.StackTrace,
                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
            };
            await _leadRepositoryAsync.AddErrorAsync(error);
        }

        return new TokenResponse(token, user.RefreshToken, user.RefreshTokenExpiryTime);
    }

    private string GenerateJwt(ApplicationUser user, string ipAddress) =>
        GenerateEncryptedToken(GetSigningCredentials(), GetClaims(user, ipAddress));

    private IEnumerable<Claim> GetClaims(ApplicationUser user, string ipAddress) =>
        new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, user.Id),
            new(ClaimTypes.Email, user.Email),
            new(LrbClaims.Fullname, $"{user.FirstName} {user.LastName}"),
            new(ClaimTypes.Name, user.FirstName ?? string.Empty),
            new(ClaimTypes.Surname, user.LastName ?? string.Empty),
            new(LrbClaims.IpAddress, ipAddress),
            new(LrbClaims.Tenant, _currentTenant!.Id),
            new(LrbClaims.ImageUrl, user.ImageUrl ?? string.Empty),
            new(ClaimTypes.MobilePhone, user.PhoneNumber ?? string.Empty)
        };

    private string GenerateRefreshToken()
    {
        byte[] randomNumber = new byte[32];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(randomNumber);
        return Convert.ToBase64String(randomNumber);
    }

    private string GenerateEncryptedToken(SigningCredentials signingCredentials, IEnumerable<Claim> claims)
    {
        var token = new JwtSecurityToken(
           claims: claims,
           expires: DateTime.UtcNow.AddMinutes(_jwtSettings.TokenExpirationInMinutes),
           signingCredentials: signingCredentials);
        var tokenHandler = new JwtSecurityTokenHandler();
        return tokenHandler.WriteToken(token);
    }

    private ClaimsPrincipal GetPrincipalFromExpiredToken(string token)
    {
        var tokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtSettings.Key)),
            ValidateIssuer = false,
            ValidateAudience = false,
            RoleClaimType = ClaimTypes.Role,
            ClockSkew = TimeSpan.Zero,
            ValidateLifetime = false
        };
        var tokenHandler = new JwtSecurityTokenHandler();
        var principal = tokenHandler.ValidateToken(token, tokenValidationParameters, out var securityToken);
        if (securityToken is not JwtSecurityToken jwtSecurityToken ||
            !jwtSecurityToken.Header.Alg.Equals(
                SecurityAlgorithms.HmacSha256,
                StringComparison.InvariantCultureIgnoreCase))
        {
            throw new UnauthorizedException("Invalid Token.");
        }

        return principal;
    }

    private string GetUsernameFromExpiredCognitoToken(string token)
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        _logger.Information("MobileTokenService: GetUsernameFromExpiredCognitoToken() - > Token: " + token);
        var jwtToken = tokenHandler.ReadJwtToken(token);
        if (jwtToken != null)
        {
            var tokenUse = jwtToken.Claims.First(x => x.Type == IdTokenClaimsKey.TokenUse).Value;
            if (tokenUse == "id")
            {

                try
                {
                    return jwtToken.Claims.First(x => x.Type == IdTokenClaimsKey.CognitoUserId).Value;
                }
                catch (Exception ex)
                {
                    var error = new LrbError()
                    {
                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                        ErrorSource = ex?.Source,
                        StackTrace = ex?.StackTrace,
                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    };
                    _leadRepositoryAsync.AddErrorAsync(error);
                    throw new UnauthorizedException("Invalid Token.");
                }
            }
        }
        throw new UnauthorizedException("Invalid Token.");
    }

    private SigningCredentials GetSigningCredentials()
    {
        byte[] secret = Encoding.UTF8.GetBytes(_jwtSettings.Key);
        return new SigningCredentials(new SymmetricSecurityKey(secret), SecurityAlgorithms.HmacSha256);
    }

    #region Retry Attempt
    private async Task HandleFailedAuthentication(string username)
    {
        var failedAttempts = IncrementFailedAttempts(username);

        if (failedAttempts >= _securitySettings.MaxRetry)
        {
            // Lock the account
            var lockoutExpiry = DateTime.UtcNow.AddMinutes(_securitySettings.LockoutDuration);
            LockAccount(username, lockoutExpiry);
            if (failedAttempts >= 7)
            {
                await _userRepo.UpdateUserLockAccount(true, username, _currentTenant?.Id ?? string.Empty);
                await _cognitoService.AddUserLockedSettingClaimAync(username, true);
                throw new UnauthorizedException("Account is locked, Please reset your password", ErrorActionCode.Logout);
            }
            else
            {
                var lockoutInfo = GetLockoutInfo(username);
                var remainingTime = lockoutInfo.LockoutExpiry - DateTime.UtcNow;
                throw new UnauthorizedException($"Account has been locked for {remainingTime.Minutes} minutes {remainingTime.Seconds} second due to multiple failed login attempts.");
            }
        }

        var remainingAttempts = _securitySettings.MaxRetry - failedAttempts;

        throw new UnauthorizedException($"Incorrect credential !, You have {remainingAttempts} attempts left before your account is locked.", ErrorActionCode.Logout);
    }

    private bool IsAccountLocked(string username)
    {
        var lockoutInfo = GetLockoutInfo(username);
        return lockoutInfo != null && DateTime.UtcNow < lockoutInfo.LockoutExpiry;
    }

    private LockoutInfo GetLockoutInfo(string username)
    {
        var lockoutKey = GetLockoutCacheKey(username);
        return _memoryCache.Get<LockoutInfo>(lockoutKey);
    }

    private int IncrementFailedAttempts(string username)
    {
        var attemptsKey = GetAttemptsCacheKey(username);
        var currentAttempts = _memoryCache.Get<int?>(attemptsKey) ?? 0;
        var newAttempts = currentAttempts + 1;

        // Cache failed attempts for lockout duration + buffer
        var cacheExpiry = new TimeSpan();
        if (currentAttempts <= _securitySettings.MaxRetry)
        {
            cacheExpiry = TimeSpan.FromMinutes(60);
        }
        else
        {
            var lockoutInfo = GetLockoutInfo(username);
            cacheExpiry = TimeSpan.FromMinutes(60);
        }
        _memoryCache.Set(attemptsKey, newAttempts, cacheExpiry);

        return newAttempts;
    }

    private void ResetFailedAttempts(string username)
    {
        var attemptsKey = GetAttemptsCacheKey(username);
        var lockoutKey = GetLockoutCacheKey(username);

        _memoryCache.Remove(attemptsKey);
        _memoryCache.Remove(lockoutKey);

        _logger.Information("Reset failed attempts for user: {Username}", username);
    }

    private void LockAccount(string username, DateTime lockoutExpiry)
    {
        var lockoutKey = GetLockoutCacheKey(username);
        var lockInfo = GetLockoutInfo(username);
        var attemptsKey = GetAttemptsCacheKey(username);
        var currentAttempts = _memoryCache.Get<int?>(attemptsKey) ?? 0;
        LockoutInfo? lockoutInfo = null;
        if (lockInfo != null)
        {
            lockoutInfo = new()
            {
                Username = lockInfo.Username,
                LockedAt = DateTime.UtcNow
            };
        }
        else
        {
            lockoutInfo = new LockoutInfo
            {
                Username = username,
                LockedAt = DateTime.UtcNow
            };
        }
        TimeSpan? cacheExpiry = null;
        if (currentAttempts == 5)
        {
            lockoutInfo.LockoutExpiry = DateTime.UtcNow.AddMinutes(3);
            cacheExpiry = TimeSpan.FromMinutes(3);
        }
        else if (currentAttempts == 6)
        {
            lockoutInfo.LockoutExpiry = DateTime.UtcNow.AddMinutes(5);
            cacheExpiry = TimeSpan.FromMinutes(5);
        }
        else
        {
            lockoutInfo.LockoutExpiry = DateTime.UtcNow.AddMinutes(1);
            cacheExpiry = TimeSpan.FromMinutes(_securitySettings.LockoutDuration);
        }
        _memoryCache.Set(lockoutKey, lockoutInfo, cacheExpiry.Value);
    }

    private string GetAttemptsCacheKey(string username) => $"failed_attempts_{username.ToLower()}";
    private string GetLockoutCacheKey(string username) => $"lockout_{username.ToLower()}";
    #endregion

    public async Task<bool> ResetFailedLoginAttemptsAsync(string userName, CancellationToken cancellationToken)
    {
        await _userRepo.UpdateUserLockAccount(false, userName, _currentTenant?.Id ?? string.Empty);
        var retyInfo = GetLockoutInfo(userName);
        if (retyInfo != null)
        {
            ResetFailedAttempts(userName);
            return true;
        }
        return false;
    }

    public async Task<AccountLockoutInfo> GetFailedLoginAttemptsAsync(string userName, CancellationToken cancellationToken)
    {
        var lockoutInfo = GetLockoutInfo(userName);
        if (lockoutInfo != null)
        {
            var remainingTime = lockoutInfo.LockoutExpiry - DateTime.UtcNow;
            AccountLockoutInfo lockedAccount = new()
            {
                Username = userName,
                LockoutExpiry = lockoutInfo.LockoutExpiry,
                RemainingTime = remainingTime,
                LockedAt = lockoutInfo.LockedAt
            };
            return lockedAccount;
        }
        return null;
    }

    public async Task<bool> UpdateLogoutDetails(LogoutRequest request,string tenantId, CancellationToken cancellationToken)
    {
        if (request.UserId != null)
        {
            return await _userRepo.UpdateUserLogoutDetails(request.UserId, request.LogoutType, DateTime.Now,tenantId);
        }
        return false;
    }
}