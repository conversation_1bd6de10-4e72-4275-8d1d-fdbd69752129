﻿using Lrb.Application.Lead.Web.Mappings;

namespace Lrb.Application.Lead.Web.Requests
{
    public class DeleteDocumentRequest : IRequest<Response<bool>>
    {
        public Guid LeadId { get; set; }
        public List<Guid>? DocumentIds { get; set; }
    }
    public class DeleteDocumentRequestHandler : LeadCommonRe<PERSON>Hand<PERSON>, IRequestHandler<DeleteDocumentRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<LeadHistoryHot> _newLeadHistoryRepo;
        public DeleteDocumentRequestHandler(IServiceProvider serviceProvider, IRepositoryWithEvents<LeadHistoryHot> newLeadHistoryRepo) : base(serviceProvider, typeof(DeleteDocumentRequestHandler).Name, "Handle")
        {
            _newLeadHistoryRepo = newLeadHistoryRepo;
        }
        public async Task<Response<bool>> Handle(DeleteDocumentRequest request, CancellationToken cancellationToken)
        {
            try
            {
                if (request.DocumentIds != null && request.DocumentIds.Any())
                {
                    var lead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(request.LeadId), cancellationToken) ?? throw new InvalidDataException("Lead with the given Id does not exist.");

                    var oldLeadDto = lead.Adapt<ViewLeadDto>();

                    if (lead.AssignTo == _currentUser.GetUserId())
                    {
                        lead.ShouldUpdatePickedDate = true;
                    }
                    var documents = lead.Documents;

                    if (documents != null && documents.Any())
                    {
                        documents.RemoveAll(i => request.DocumentIds.Contains(i.Id));
                    }
                    lead.Documents = documents;
                    var documentsOfbookedLeads = lead.BookedDetails?.OrderByDescending(i => i.LastModifiedOn)?.FirstOrDefault()?.Documents?.ToList() ?? null;
                    if(documentsOfbookedLeads != null && (documentsOfbookedLeads?.Any() ?? false))
                    {
                        documentsOfbookedLeads.RemoveAll(i => request.DocumentIds.Contains(i.Id));
                    }
                    if (lead.BookedDetails != null && (lead.BookedDetails?.Any() ?? false)) 
                    {
                        lead.BookedDetails.OrderByDescending(i => i.LastModifiedOn).FirstOrDefault().Documents = documentsOfbookedLeads;
                    }
                    if (lead.Appointments?.Any() ?? false)
                    {
                        lead.Appointments.ToList().ForEach(i =>
                        {
                            i.ImagesWithName?.RemoveAll(i => request.DocumentIds.Contains(i.Id));
                        });
                        await _appointmentRepo.UpdateRangeAsync(lead.Appointments, cancellationToken);
                    }

                    await _leadRepo.UpdateAsync(lead, cancellationToken);

                    await UpdateLeadHistoryAsync(lead, cancellationToken: cancellationToken);

                    #region Create New Lead Histories
                    lead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(lead.Id), cancellationToken) ?? throw new NotFoundException("No lead found by the id.");
                    var leadDto = lead.Adapt<ViewLeadDto>();
                    var currentUserId = _currentUserRepo.GetUserId();
                    leadDto.LastModifiedBy = currentUserId;
                    await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken, currentUserId);
                    await oldLeadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken, currentUserId);
                    var leadStatues = await _customLeadStatusRepo.ListAsync(cancellationToken);
                    var masterPropertyTypes = await _propertyTypeRepo.ListAsync(cancellationToken);
                    var latestModificationVersion = await _dapperRepository.GetLatestLeadHistoryVersionAsync(leadDto.Id);
                    var histories = await LeadHistoryHelperV2.V2UpdateLeadHistoryForVM(leadDto, oldLeadDto, latestModificationVersion ?? 1, leadStatues, masterPropertyTypes, _userService, currentUserId, cancellationToken);
                    await _newLeadHistoryRepo.AddRangeAsync(histories);
                    #endregion

                    await SendOnlyLeadInfoUpdateNotificationAsync(lead, cancellationToken: cancellationToken);

                    return new(true);
                }
                else
                {
                    throw new InvalidOperationException("No valid document ids provided.");
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{typeof(DeleteDocumentRequestHandler).Name} - Handle()");
                return new(false);
            }
        }
    }
}
