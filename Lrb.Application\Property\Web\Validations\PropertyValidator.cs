﻿using Lrb.Application.Integration.Web.Requests.Pabbly;
using System;

namespace Lrb.Application.Property.Web
{
    public static class PropertyValidator
    {
        public static bool IsValid(this CreatePropertyRequest property)
        {
            bool isValidEnquiry = property.EnquiredFor == EnquiryType.Buy || property.EnquiredFor == EnquiryType.Rent || property.EnquiredFor == EnquiryType.Sale;
            bool isValidPossesionDate = property.PossessionDate != default;
            bool isValidPropertyType = property.PropertyTypeId != default;
            bool isValidAddress = property.PlaceId != default;
            bool isValidPrice = property.MonetaryInfo?.ExpectedPrice > 0;
            bool isValidOwnerDetails = property.OwnerDetails?.Phone == null || (property.OwnerDetails?.Phone != null && property.OwnerDetails?.Phone?.Length == 10);
            return isValidEnquiry && isValidPropertyType && isValidPossesionDate && isValidPrice && isValidAddress && isValidOwnerDetails;
        }
        public static bool IsValid(this UpdatePropertyRequest property)
        {
            bool isValidEnquiry = property.EnquiredFor == EnquiryType.Buy || property.EnquiredFor == EnquiryType.Rent || property.EnquiredFor == EnquiryType.Sale;
            bool isValidPossesionDate = property.PossessionDate != default;
            bool isValidPropertyType = property.PropertyTypeId != default;
            bool isValidAddress = property.PlaceId != default;
            bool isValidPrice = property.MonetaryInfo?.ExpectedPrice > 0;
            bool isValidOwnerDetails = property.OwnerDetails?.Phone == null || (property.OwnerDetails?.Phone != null && property.OwnerDetails?.Phone?.Length == 10);
            return isValidEnquiry && isValidPropertyType && isValidPossesionDate && isValidPrice && isValidAddress && isValidOwnerDetails;
        }

        public static bool IsValid( Lrb.Domain.Entities.Property property)
        {
            bool isValidEnquiry = property.EnquiredFor == EnquiryType.Buy || property.EnquiredFor == EnquiryType.Rent || property.EnquiredFor == EnquiryType.Sale;
            bool isValidPossesionDate = property.PossessionDate != default;
            bool isValidPropertyType = property.PropertyType.Id != default;
            bool isValidAddress = property.Address.PlaceId != default;
            bool isValidPrice = property.MonetaryInfo?.ExpectedPrice > 0;
            bool isValidOwnerDetails = property.OwnerDetails?.Phone == null || (property.OwnerDetails?.Phone != null && property.OwnerDetails?.Phone?.Length == 10);
            return isValidEnquiry && isValidPropertyType && isValidPossesionDate && isValidPrice && isValidAddress && isValidOwnerDetails;
        }
        public static bool IsValid(this CreatePropertyFromPabblyRequest property)
        {
            bool isValidEnquiry = property.EnquiredFor == EnquiryType.Buy || property.EnquiredFor == EnquiryType.Rent || property.EnquiredFor == EnquiryType.Sale;
            bool isValidPossesionDate = property.PossessionDate != default;
            bool isValidPropertyType = property.PropertyTypeId != default;
            bool isValidAddress = property.PlaceId != default;
            bool isValidPrice = property.MonetaryInfo?.ExpectedPrice > 0;
            bool isValidOwnerDetails = property.OwnerDetails?.Phone == null || (property.OwnerDetails?.Phone != null && property.OwnerDetails?.Phone?.Length == 10);
            return isValidEnquiry && isValidPropertyType && isValidPossesionDate && isValidPrice && isValidAddress && isValidOwnerDetails;
        }
    }
}
