﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Property.Web.Dtos
{
    public class PropertyGalleryDto : IDto
    {
        public int? Height { get; set; }
        public int? Width { get; set; }
        public string? ImageFilePath { get; set; }
        public bool IsCoverImage { get; set; }
        public int? OrderRank { get; set; }
    }
    public class PropertyVideoGallaryDto : IDto
    {
        public string? Name { get; set; }
        public string? ImageFilePath { get; set; }
        public bool IsCoverImage { get; set; }
        public PropertyGalleryType? GalleryType { get; set; }
        public ImageSegregationType? ImageSegregationType { get; set; }
        public int? OrderRank { get; set; }
        public int? Height { get; set; }
        public int? Width { get; set; }
    }
}
