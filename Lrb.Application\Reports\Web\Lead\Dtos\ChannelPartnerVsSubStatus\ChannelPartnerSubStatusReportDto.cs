﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Reports.Web.Lead.Dtos.ChannelPartnerVsSubStatus
{
    public class ChannelPartnervsSubStatusReportDto : IDto
    {
        public string? ChannelPartnerFirmName { get; set; }
        public Dictionary<string, object>? BaseStatusWithSubStatusCount { get; set; }
    }
    public class LeadChannelPartnerBySubStatusDto : IDto
    {
        public string? ChannelPartnerFirmName { get; set; }
        public string? Status { get; set; }
        public List<StatusDto>? StatusDtos { get; set; }
    }
}
