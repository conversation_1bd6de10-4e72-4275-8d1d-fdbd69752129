﻿using Lrb.Application.Common.Persistence;
using Lrb.Application.CustomStatus.Web;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Integration.Web;
using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Mappings;
using Lrb.Application.Lead.Web.Requests.CommonHandler;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Lrb.Shared.Extensions;
using Newtonsoft.Json;
using System.Text;

namespace Lrb.Application.Lead.Web
{
    public class UpdateBulkLeadStatusRequest : UpdateBulkLeadStatusDto, IRequest<Response<int>>
    {
        public List<NotificationContent>? Contents { get; set; }
    }
    public class UpdateBulkLeadStatusRequestHndler : Bulk<PERSON>tatus<PERSON>and<PERSON>, IRequestHandler<UpdateBulkLeadStatusRequest, Response<int>>
    {
        private IServiceProvider _serviceProvider;
        protected readonly IRepositoryWithEvents<FacebookAuthResponse> _fbAuthResponseRepo;
        private readonly IRepositoryWithEvents<LeadHistoryHot> _newLeadHistoryRepo;
        private readonly IDapperRepository _dapperRepository;

        public UpdateBulkLeadStatusRequestHndler(IServiceProvider serviceProvider,
            IRepositoryWithEvents<FacebookAuthResponse> fbAuthResponseRepo,
            IRepositoryWithEvents<LeadHistoryHot> newLeadHistoryRepo,
            IDapperRepository dapperRepository)
            : base(serviceProvider, typeof(UpdateBulkLeadStatusRequestHndler).Name, "Handle")
        {
            _serviceProvider = serviceProvider;
            _fbAuthResponseRepo = fbAuthResponseRepo;
            _newLeadHistoryRepo = newLeadHistoryRepo;
            _dapperRepository = dapperRepository;
        }
        public async Task<Response<int>> Handle(UpdateBulkLeadStatusRequest request, CancellationToken cancellationToken)
        {
            try
            {
                bool IsDocumentUploaded = false;
                List<Domain.Entities.Lead>? updatedLeads = new();
                List<Domain.Entities.LeadHistory>? existingLeadHistories = new();
                List<Domain.Entities.LeadHistory>? newLeadHistories = new();
                int updatedCount = 0;
                var payloadAssignTo = request.AssignTo;
                var payloadSecondaryAssignTo = request.SecondaryUserId;
                var payloadNotes = request.Notes;
                bool hasToSendAssignmentNotification = false;
                List<Domain.Entities.Lead> existingLeads = await _leadRepo.ListAsync(new LeadByIdSpec(request?.LeadIds), cancellationToken) ?? throw new NotFoundException("No Leads found by the Ids");
                var oldViewLeads = existingLeads.Adapt<List<ViewLeadDto>>();
                var globalSetting = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec());
                var baseLeadStatus = await _customLeadStatusRepo.ListAsync(new GetAllCustomStatusSpec(request.LeadStatusId), cancellationToken);
                
                var status = baseLeadStatus?.FirstOrDefault(i => i.Id ==
                            (baseLeadStatus.FirstOrDefault(x => x.Id == request.LeadStatusId)?.BaseId ?? request.LeadStatusId));

                var leadAssignedCount = 0;

                foreach (var existingLead in existingLeads)
                {
                    request.SecondaryUserId = payloadSecondaryAssignTo;
                    try
                    {
                        existingLead.ShouldUpdatePickedDate = await ShouldUpdatePickedDate(existingLead, request.Adapt<PickedLeadDto>(), currentUser: request.CurrentUserId);
                    }
                    catch (Exception ex)
                    {
                        throw;
                    }

                    if ((globalSetting != null) && globalSetting.IsTeamLeadRotationEnabled)
                    {
                        var newAssignTo = await GetAutoRetentionAssignmentId(existingLead, request.LeadStatusId);

                        if (newAssignTo != Guid.Empty)
                        {
                            request.AssignTo = newAssignTo;
                            payloadAssignTo = newAssignTo;
                        }
                    }
                    Guid? existingAssignedUserId = null;
                    var isAssignToPresent = false;
                    if ((payloadAssignTo != null) && (payloadAssignTo != existingLead.AssignTo) && (existingLead.SecondaryUserId != payloadAssignTo))
                    {

                        existingAssignedUserId = existingLead.AssignTo;
                        existingLead.AssignedFrom = existingLead.AssignTo;
                        existingLead.AssignTo = request?.AssignTo ?? Guid.Empty;
                        leadAssignedCount++;
                        if (existingLead.AssignTo != existingAssignedUserId)
                        {
                            existingLead.PickedDate = null;
                            existingLead.IsPicked = false;
                            existingLead.ShouldUpdatePickedDate = false;
                            isAssignToPresent = true;
                        }

                    }
                    else if (payloadAssignTo == null)
                    {
                        request.AssignTo = existingLead.AssignTo;
                    }
                    if ((payloadSecondaryAssignTo != null) && (payloadSecondaryAssignTo != existingLead.AssignTo))
                    {
                        existingLead.SecondaryUserId = request?.SecondaryUserId ?? Guid.Empty;
                    }
                    else if (payloadSecondaryAssignTo == null)
                    {
                        request.SecondaryUserId = existingLead.SecondaryUserId;
                    }
                    else if (payloadSecondaryAssignTo == existingLead.AssignTo)
                    {
                        request.SecondaryUserId = existingLead.SecondaryUserId;
                    }
                    if (payloadNotes == null)
                    {
                        request.Notes = existingLead.Notes;
                    }
                    var existingBookedDate = existingLead.BookedDate;
                    var existingBookedBy = existingLead.BookedBy;
                    await SetLeadStatusAsync(existingLead, request.LeadStatusId, cancellationToken, allStatuses: baseLeadStatus);
                    var baseLead = baseLeadStatus.FirstOrDefault(i => i.Id == existingLead.CustomLeadStatus?.BaseId);
                    var existingUnmatchedBudget = existingLead.UnmatchedBudget;
                    var existingPurchasedFrom = existingLead.PurchasedFrom;
                    try 
                    {
                        if (request.Documents != null && request.Documents.Any())
                        {
                            IsDocumentUploaded = true;
                            await SetLeadDocsAsync(existingLead, request.Documents, Domain.Enums.LeadDocumentType.Lead, cancellationToken);
                            
                        }
                        request.Documents = existingLead.Documents;
                    }
                    catch (Exception ex)
                    {
                        //ignor
                    }
                    var oldlead = request.Adapt(existingLead);

                    if (request.PostponedDate != null && request.PostponedDate != default)
                    {
                        oldlead.ScheduledDate = request.PostponedDate;
                    }
                    else if (oldlead.CustomLeadStatus != null && oldlead.CustomLeadStatus.MasterLeadStatusBaseId == Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    {
                        oldlead.ScheduledDate = null;
                    }

                    if (payloadAssignTo != null && payloadAssignTo == Guid.Empty)
                    {
                        oldlead.SecondaryUserId = Guid.Empty;
                    }

                    if (payloadAssignTo != null && isAssignToPresent)
                    {
                        if (payloadAssignTo != Guid.Empty && payloadAssignTo != existingAssignedUserId)
                        {
                            hasToSendAssignmentNotification = true;
                        }
                    }
                    if (request.MeetingOrSiteVisit != AppointmentType.None)
                    {
                        var appointment = request.Adapt<LeadAppointment>();
                        await SetLeadAppointmentAsync(oldlead, appointment, request.MeetingOrSiteVisit, cancellationToken: cancellationToken);
                    }

                    if (request.Addresses != null && (request?.Addresses?.Any() ?? false))
                    {
                        List<Address>? differentLocationAddresses = await CreateAddressesAsync(request.Addresses, cancellationToken);

                        var primaryEnquiry = oldlead.Enquiries.FirstOrDefault(i => i.IsPrimary);

                        if (primaryEnquiry != null && primaryEnquiry.Addresses != null)
                        {
                            primaryEnquiry.Addresses = differentLocationAddresses;
                        }
                    }
                    if (baseLead != null && baseLead?.Status == "booked" || oldlead.CustomLeadStatus?.Status == "booked" || oldlead.CustomLeadStatus?.Status == "invoiced" || baseLead?.ShouldUseForBooking == true || baseLead?.ShouldUseForInvoice == true)
                    {
                        if (request?.BookedDate != null)
                        {
                            oldlead.BookedDate = request.BookedDate;
                        }
                        else
                        {
                            oldlead.BookedDate = DateTime.UtcNow;
                        }
                        oldlead.BookedBy = request.CurrentUserId ?? _currentUserRepo.GetUserId();
                        await SetBookedDetailsAsync(oldlead, request.Adapt<UpdateBulkLeadStatusDto>(), cancellationToken);
                    }
                    else
                    {
                        oldlead.BookedDate = existingBookedDate;
                        oldlead.BookedBy = existingBookedBy;
                    }
                    try
                    {
                        if (string.IsNullOrWhiteSpace(request.Notes) || (request?.UnmatchedBudget != null && request?.UnmatchedBudget != 0) || !string.IsNullOrWhiteSpace(request?.PurchasedFrom))
                        {
                            oldlead.Notes = (request?.UnmatchedBudget != null && request?.UnmatchedBudget != 0 && request?.UnmatchedBudget != existingUnmatchedBudget)
                                ? $"Unmatched Budget : {request?.UnmatchedBudget}  \n {(request?.IsNotesUpdated == true ? (request?.Notes ?? string.Empty) : string.Empty)}"
                                : (!string.IsNullOrWhiteSpace(request?.PurchasedFrom) && request?.PurchasedFrom != existingPurchasedFrom)
                                    ? $"Purchased From : {request?.PurchasedFrom} \n {(request?.IsNotesUpdated == true ? (request?.Notes ?? string.Empty) : string.Empty)}"
                                        : request.Notes;
                        }

                    }
                    catch (Exception ex)
                    {
                        //ignore
                    }
                    if (request?.PropertiesList?.Any() ?? false)
                    {
                        await SetLeadPropertiesAsync(oldlead, request.PropertiesList, cancellationToken, globalSettings: globalSetting, currentUser: request.CurrentUserId);
                    }
                    if (request?.ProjectsList?.Any() ?? false)
                    {
                        await SetLeadProjectsAsync(oldlead, request.ProjectsList, cancellationToken, globalSettings: globalSetting, currentUser: request.CurrentUserId);
                    }
                    if ((request?.IsFullyCompleted ?? false) && (request?.Projects?.Any() ?? false))
                    {
                        var appointments = await _appointmentRepo.ListAsync(new GetAppointmentsByProjectsSpec(existingLead.Id, (request?.Projects?.ConvertAll(i => i.Trim().ToLower()) ?? new List<string>())));
                        appointments?.ForEach(i => i.IsFullyCompleted = true);
                        if (appointments?.Any() ?? false)
                        {
                            try
                            {
                                await _appointmentRepo.UpdateRangeAsync(appointments);
                            }
                            catch (Exception ex)
                            {
                                var error = new LrbError()
                                {
                                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                    ErrorSource = ex?.Source,
                                    StackTrace = ex?.StackTrace,
                                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                    ErrorModule = "UpdateSiteVisitOrMeetingDoneRequestHandler -> Handle()"
                                };
                                await _leadRepositoryAsync.AddErrorAsync(error);
                            }
                        }
                    }
                    
                    oldlead.LastModifiedBy = request?.CurrentUserId ?? _currentUserRepo.GetUserId();
                    updatedLeads.Add(oldlead);
                    updatedCount++;
                    if (!IsDocumentUploaded)
                    {
                        request.Documents = null;
                    }
                }
                await _leadRepo.UpdateRangeAsync(updatedLeads, cancellationToken);
                await CreateLeadAssignmentsHistory(updatedLeads, LeadAssignmentType.WithHistory);
                updatedLeads = await _leadRepo.ListAsync(new LeadByIdSpec(request?.LeadIds), cancellationToken) ?? throw new NotFoundException("No Leads found by the Ids");
                var userIds = updatedLeads.Where(i => request.AssignTo != null && request.AssignTo == i.AssignTo).Select(i => i.AssignTo).DistinctBy(i => i).ToList();
                userIds.Add(request.CurrentUserId ?? _currentUserRepo.GetUserId());
                var users = await _userService.GetListOfUsersByIdsAsync(userIds.Select(i => i.ToString()).ToList(), cancellationToken);
                List<Guid> adminIds = (await _npgsqlRepo.GetAdminIdsAsync(request.TenantId ?? _currentUserRepo.GetTenant() ?? string.Empty)).Where(i => i != (request.CurrentUserId ?? _currentUserRepo.GetUserId())).ToList();
                List<ViewLeadDto> leadDtos = new List<ViewLeadDto>();
                List<LeadHistoryHot> newLeadHistory = new();
                var leadStatues = await _customLeadStatusRepo.ListAsync(cancellationToken);
                var masterPropertyTypes = await _propertyTypeRepo.ListAsync(cancellationToken);
                foreach (var oldlead in updatedLeads)
                {
                    var leadDto = await GetFullLeadDtoAsync(oldlead, cancellationToken, currentUserId: request.CurrentUserId);
                    leadDtos.Add(leadDto);
                    var leadHistories = await UpdateLeadHistoryAsync(oldlead, leadDto: leadDto, appointmentType: request.MeetingOrSiteVisit, cancellationToken: cancellationToken, isLeadUpdateRequest: request.IsNotesUpdated, currentUserId: request.CurrentUserId);
                    if (leadHistories.Item1 != null)
                    {
                        existingLeadHistories.Add(leadHistories.Item1);
                    }
                    if (leadHistories.Item2 != null)
                    {
                        newLeadHistories.Add(leadHistories.Item2);
                    }

                    #region Update New Lead History Table
                    var oldLeadDto = oldViewLeads.FirstOrDefault(i => i.Id == oldlead.Id);
                    var newLeadDto = oldlead.Adapt<ViewLeadDto>();
                    var currentUserId = _currentUserRepo.GetUserId();
                    leadDto.LastModifiedBy = currentUserId;
                    await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken, currentUserId);
                    await oldLeadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken, currentUserId);
                    var latestModificationVersion = await _dapperRepository.GetLatestLeadHistoryVersionAsync(leadDto.Id);
                    var histories = await LeadHistoryHelperV2.V2UpdateLeadHistoryForVM(newLeadDto, oldLeadDto, latestModificationVersion ?? 1, leadStatues, masterPropertyTypes, _userService, currentUserId, cancellationToken);
                    newLeadHistory.AddRange(histories);
                    #endregion
                }
                if (newLeadHistory?.Any() ?? false)
                {
                    await _newLeadHistoryRepo.AddRangeAsync(newLeadHistory, cancellationToken);
                }

                if (existingLeadHistories?.Any() ?? false)
                {
                    await _leadHistoryRepo.UpdateRangeAsync(existingLeadHistories, cancellationToken);
                }
                if (newLeadHistories?.Any() ?? false)
                {
                    await _leadHistoryRepo.AddRangeAsync(newLeadHistories, cancellationToken);
                }
                if (hasToSendAssignmentNotification && request.AssignTo != null)
                {
                    var updatedLead = updatedLeads?.FirstOrDefault(i => request.AssignTo == i.AssignTo);
                    var assignedUser = users.FirstOrDefault(i => i.Id == updatedLead?.AssignTo);
                    await SendLeadAssignmentNotificationsAsync(updatedLead, leadAssignedCount, cancellationToken, tenantId: request.TenantId, currentUser: request.CurrentUserId, adminIds: adminIds, assignedUser: assignedUser, allUsersDetails: users, contents: request.Contents, globalSetting: globalSetting);
                }
                foreach (var oldlead in updatedLeads)
                {
                    var leadDto = leadDtos.FirstOrDefault(i => i.Id == oldlead.Id);
                    await SendLeadAppointmentNotificationAsync(oldlead, request.MeetingOrSiteVisit, request.IsDone, cancellationToken, currentUser: request.CurrentUserId, globalSettings: globalSetting, leadDto: leadDto, allUsersDetails: users, contents: request.Contents);

                    //if (hasToSendAssignmentNotification)
                    //{
                    //    var assignedUser = users.FirstOrDefault(i => i.Id == oldlead?.AssignTo);
                    //    await SendLeadAssignmentNotificationsAsync(oldlead, 1, cancellationToken, tenantId: request.TenantId, currentUser: request.CurrentUserId, adminIds: adminIds, assignedUser: assignedUser,allUsersDetails:users,contents:request.Contents);
                    //}
                    if (request.TenantId == "dreamyard" || request.TenantId == "kroft" || request.TenantId == "custom")         
                    {
                        await AutoReassignmentHandler(oldlead, cancellationToken, tenantId: request.TenantId);
                    }
                    if (globalSetting != null && globalSetting.IsTeamLeadRotationEnabled)
                    {
                        //var updatedLead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(request.Id), cancellationToken) ?? throw new NotFoundException("No Lead found by this Id");
                        await ScheduleLeadRetentionRotation(new List<Domain.Entities.Lead>() { oldlead }, cancellationToken);
                    }
                }

                #region Facebook Conversion event trigger
                try
                {
                    if (globalSetting != null && globalSetting.EnableFacebookConversion)
                    {
                        Dictionary<string, List<string>> metaLeadDetails = existingLeads.Where(i => !(string.IsNullOrWhiteSpace(i.PixelId)) && !(string.IsNullOrWhiteSpace(i.MetaLeadId)))
                            .GroupBy(i => i.PixelId)
                            .ToDictionary(
                                group => group.Key,
                                group => group.Select(lead => lead.MetaLeadId).ToList()
                            );
                        if (metaLeadDetails != null)
                        {
                            foreach (var details in metaLeadDetails)
                            {
                                if ((!string.IsNullOrWhiteSpace(details.Key)))
                                {
                                    
                                    var fbPageAccount = await _fbAuthResponseRepo.FirstOrDefaultAsync(new GetFacebookAuthResoponseAccountSpec(details.Key));
                                    var metaStatuses = fbPageAccount?.MetaLeadStatusMapping?
                                        .FirstOrDefault(kv => kv.Value.Contains(status?.DisplayName ?? "")).Key; 
                                    FbConversionApiDto fbConversionApiDto = new();
                                    fbConversionApiDto.AccessToken = fbPageAccount?.ConversionsAccessToken;
                                    fbConversionApiDto.MetaLeadIds = details.Value;
                                    fbConversionApiDto.StatusName = status?.Status ?? "new";
                                    fbConversionApiDto.PixelId = details.Key;
                                    fbConversionApiDto.MetaStatus = metaStatuses ?? MetaLeadUnifiedStatus.None;
                                    var payload = new InputPayload(request.TenantId ?? string.Empty, request.CurrentUserId ?? Guid.Empty, fbConversionApiDto);
                                    await _serviceBus.RunFbConversionApiEventAsync(payload);
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.Information($"LeadController -> UpdateBulkLeadStatusRequest, Facebook Conversion event trigger:" + JsonConvert.SerializeObject(ex.Message));
                }
                #endregion

                return new(updatedCount);
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{typeof(UpdateBulkLeadStatusRequestHndler).Name} - Handle()");
                throw;
            }
        }

        public record InputPayload(string TenantId, Guid CurrentUserId, object Entity);
        }
}