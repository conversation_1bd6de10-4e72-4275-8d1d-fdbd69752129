﻿using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Mobile.Dtos;
using Lrb.Application.Lead.Mobile.Dtos.v1;
using Lrb.Application.Lead.Mobile.Dtos.v2;
using Lrb.Application.Lead.Mobile.Dtos.v4;
using Lrb.Application.Lead.Mobile.Requests;
using Lrb.Application.Lead.Mobile.v2;
using Lrb.Application.Lead.Web.Mappings;
using Lrb.Application.Property.Mobile;
using Lrb.Domain.Entities.MasterData;
using Microsoft.Extensions.DependencyInjection;
using System;

namespace Lrb.Application.Lead.Mobile
{
    public static class LeadMappings
    {
        public static IRepository<MasterPropertyType> _masterPropertyTypeRepo = null;
        public static IRepository<MasterAreaUnit> _masterAreaUnitRepo = null;
        //public static IRepository<MasterLeadStatus> _masterLeadStatusRepo = null;

        public static void Configure(IServiceProvider serviceProvider)
        {
            _masterPropertyTypeRepo = (IRepository<MasterPropertyType>)serviceProvider.GetRequiredService(typeof(IRepository<>).MakeGenericType(typeof(MasterPropertyType)));
            _masterAreaUnitRepo = (IRepository<MasterAreaUnit>)serviceProvider.GetRequiredService(typeof(IRepository<>).MakeGenericType(typeof(MasterAreaUnit)));
            //_masterLeadStatusRepo = (IRepository<MasterLeadStatus>)serviceProvider.GetRequiredService(typeof(IRepository<>).MakeGenericType(typeof(MasterLeadStatus)));
            List<MasterPropertyType>? propertytypes = null;
            if (_masterPropertyTypeRepo != null)
            {
                propertytypes = _masterPropertyTypeRepo.ListAsync().Result;
            }
            List<MasterAreaUnit>? areaUnits = null;
            if (_masterAreaUnitRepo != null)
            {
                areaUnits = _masterAreaUnitRepo.ListAsync().Result;
            }
            //List<LeadStatusDto>? masterLeadStatuses = null;
            //if (_masterLeadStatusRepo != null)
            //{
            //    masterLeadStatuses = _masterLeadStatusRepo.ListAsync().Result.Adapt<List<LeadStatusDto>>();
            //}
            List<LeadStatusDto>? masterLeadStatuses = null;
            TypeAdapterConfig<Domain.Entities.Lead, ViewLeadDto>
                .NewConfig()
                .Map(dest => dest.LeadTags, src => src.TagInfo)
                .Map(dest => dest.Enquiry, src => src.Enquiries != null && src.Enquiries.Any() ? src.Enquiries.FirstOrDefault(i => i.IsPrimary) : null)
                .Map(dest => dest.Status, src => MapLeadStatus(src, masterLeadStatuses))
                .Map(dest => dest.CallRecordingUrls, src => (src.CallRecordingUrls != null && src.CallRecordingUrls.Any()) ? src.CallRecordingUrls.GroupBy(i => i.Key.Year).ToDictionary(i => i.Key, i => i.GroupBy(i => i.Key.Month).ToDictionary(i => i.Key, i => i.ToList().ToDictionary(i => i.Key, i => i.Value))) : null)
                .Map(dest => dest.Documents, src => src.Documents);
            //.Ignore(i => i.ContactRecords);
            TypeAdapterConfig<LeadEnquiry, ViewLeadEnquiryDto>
                .NewConfig()
                .Map(dest => dest.PropertyType, src => src.PropertyType != null ? new PropertyTypeDto
                {
                    Id = src.PropertyType.BaseId != null ? src.PropertyType.BaseId.Value : default,
                    BaseId = null,
                    DisplayName = src.PropertyType.BaseId != null ? propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).DisplayName : default,
                    Type = src.PropertyType.BaseId != null ? propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).Type : default,
                    Level = 0,
                    ChildType = new()
                    {
                        Id = src.PropertyType.Id,
                        BaseId = src.PropertyType.BaseId != null ? src.PropertyType.BaseId : default,
                        DisplayName = src.PropertyType.DisplayName,
                        Level = src.PropertyType.Level,
                        Type = src.PropertyType.Type,
                        ChildType = null
                    }

                } : null)
                .Map(dest => dest.AreaUnit, src => areaUnits.FirstOrDefault(i => i.Id == src.AreaUnitId))
                .Map(dest => dest.CarpetAreaUnit, src => src.CarpetAreaUnitId != default && areaUnits.FirstOrDefault(i => i.Id == src.CarpetAreaUnitId) != null ? areaUnits.FirstOrDefault(i => i.Id == src.CarpetAreaUnitId).Unit : default)
                .Map(dest => dest.NoOfBHK, src => src.NoOfBHKs)
                .Map(dest => dest.BuiltUpAreaUnit, src => src.BuiltUpAreaUnitId != default && areaUnits.FirstOrDefault(i => i.Id == src.BuiltUpAreaUnitId) != null ? areaUnits.FirstOrDefault(i => i.Id == src.BuiltUpAreaUnitId).Unit : default)
                .Map(dest => dest.SaleableAreaUnit, src => src.SaleableAreaUnitId != default && areaUnits.FirstOrDefault(i => i.Id == src.SaleableAreaUnitId) != null ? areaUnits.FirstOrDefault(i => i.Id == src.SaleableAreaUnitId).Unit : default)
                .Map(dest => dest.PropertyAreaUnit, src => src.PropertyAreaUnitId != default && areaUnits != null && areaUnits.FirstOrDefault(i => i.Id == src.PropertyAreaUnitId) != null ? areaUnits.FirstOrDefault(i => i.Id == src.PropertyAreaUnitId).Unit : default)
                .Map(dest => dest.NetAreaUnit, src => src.NetAreaUnitId != default && areaUnits != null && areaUnits.FirstOrDefault(i => i.Id == src.NetAreaUnitId) != null ? areaUnits.FirstOrDefault(i => i.Id == src.NetAreaUnitId).Unit : default)

                .Map(dest => dest.SaleableAreaUnit, src => src.SaleableAreaUnitId != default && areaUnits.FirstOrDefault(i => i.Id == src.SaleableAreaUnitId) != null ? areaUnits.FirstOrDefault(i => i.Id == src.SaleableAreaUnitId).Unit : default)
                .Map(dest => dest.PropertyTypes, src => src.PropertyTypes != null ? src.PropertyTypes.Select(pt => new PropertyTypeDto
                {
                       Id = pt.BaseId.HasValue ? pt.BaseId.Value : default,
                       BaseId = null,
                       DisplayName = pt.BaseId.HasValue ? (propertytypes.FirstOrDefault(i => i.Id == pt.BaseId.Value) != null ? propertytypes.FirstOrDefault(i => i.Id == pt.BaseId.Value).DisplayName : default) : default,
                       Type = pt.BaseId.HasValue ? (propertytypes.FirstOrDefault(i => i.Id == pt.BaseId.Value) != null ? propertytypes.FirstOrDefault(i => i.Id == pt.BaseId.Value).Type : default) : default,
                       Level = 0,
                       ChildType = new PropertyTypeDto
                       {
                           Id = pt.Id,
                           BaseId = pt.BaseId,
                           DisplayName = pt.DisplayName,
                           Level = pt.Level,
                           Type = pt.Type,
                           ChildType = null
                       }
                }).ToList() : null); ;

            ;
            TypeAdapterConfig<UpdateLeadRequest, Domain.Entities.Lead>
                .NewConfig()
                .Ignore(i => i.Address)
                .Ignore(i => i.Enquiries)
                .Ignore(i => i.TagInfo);


            TypeAdapterConfig<Domain.Entities.Lead, V4GetAllLeadDto>
            .NewConfig()
            .Map(dest => dest.StatusId, src => src.CustomLeadStatus != null ? src.CustomLeadStatus.Id : Guid.Empty)
            .Map(dest => dest.PropertyTypeId, src => (src.Enquiries != null && src.Enquiries.Any())
            ? src.Enquiries.FirstOrDefault(i => i.IsPrimary) != null && src.Enquiries.FirstOrDefault(i => i.IsPrimary).PropertyType != null
                ? src.Enquiries.FirstOrDefault(i => i.IsPrimary).PropertyType.Id
                : src.Enquiries.FirstOrDefault() != null && src.Enquiries.FirstOrDefault().PropertyType != null
                    ? src.Enquiries.FirstOrDefault().PropertyType.Id
                    : Guid.Empty
            : Guid.Empty)
            .Map(dest => dest.NoOfBHK, src => src.Enquiries != null && src.Enquiries.Any() ? src.Enquiries.FirstOrDefault(i => i.IsPrimary) == null ? src.Enquiries.FirstOrDefault().NoOfBHKs : src.Enquiries.FirstOrDefault(i => i.IsPrimary).NoOfBHKs : 0)
            .Map(dest => dest.BHKs, src => src.Enquiries != null && src.Enquiries.Any() ? src.Enquiries.FirstOrDefault(i => i.IsPrimary) == null ? src.Enquiries.FirstOrDefault().BHKs : src.Enquiries.FirstOrDefault(i => i.IsPrimary).BHKs : default)
            .Map(dest => dest.Source, src => src.Enquiries != null && src.Enquiries.Any() ? src.Enquiries.FirstOrDefault(i => i.IsPrimary) == null ? src.Enquiries.FirstOrDefault().LeadSource : src.Enquiries.FirstOrDefault(i => i.IsPrimary).LeadSource : LeadSource.Direct)
            .Map(dest => dest.UpperBudget, src => src.Enquiries != null && src.Enquiries.Any() ? src.Enquiries.FirstOrDefault(i => i.IsPrimary) == null ? src.Enquiries.FirstOrDefault().UpperBudget : src.Enquiries.FirstOrDefault(i => i.IsPrimary).UpperBudget : default)
            .Map(dest => dest.LowerBudget, src => src.Enquiries != null && src.Enquiries.Any() ? src.Enquiries.FirstOrDefault(i => i.IsPrimary) == null ? src.Enquiries.FirstOrDefault().LowerBudget : src.Enquiries.FirstOrDefault(i => i.IsPrimary).LowerBudget : default)
            .Map(dest => dest.LeadFilterKey, src => LeadHelper.Find(src))
            .Map(dest => dest.MeetingsDone, src => src.Appointments != null && src.Appointments.Any() ? src.Appointments.Where(x => x.Type == AppointmentType.Meeting && x.IsDone && x.UserId == src.AssignTo).Count() : default)
            .Map(dest => dest.MeetingsNotDone, src => src.Appointments != null && src.Appointments.Any() ? src.Appointments.Where(x => x.Type == AppointmentType.Meeting && !x.IsDone && x.UserId == src.AssignTo).Count() : default)
            .Map(dest => dest.SiteVisitsDone, src => src.Appointments != null && src.Appointments.Any() ? src.Appointments.Where(x => x.Type == AppointmentType.SiteVisit && x.IsDone && x.UserId == src.AssignTo).Count() : default)
            .Map(dest => dest.SiteVisitsNotDone, src => src.Appointments != null && src.Appointments.Any() ? src.Appointments.Where(x => x.Type == AppointmentType.SiteVisit && !x.IsDone && x.UserId == src.AssignTo).Count() : default);
            TypeAdapterConfig<Domain.Entities.Lead, LeadAppointmentsByIdDto>
           .NewConfig()
           .Map(dest => dest.MeetingsDone, src => src.Appointments != null && src.Appointments.Any() ? src.Appointments.Where(x => x.Type == AppointmentType.Meeting && x.IsDone && x.UserId == src.AssignTo).Count() : default)
           .Map(dest => dest.MeetingsNotDone, src => src.Appointments != null && src.Appointments.Any() ? src.Appointments.Where(x => x.Type == AppointmentType.Meeting && !x.IsDone && x.UserId == src.AssignTo).Count() : default)
           .Map(dest => dest.SiteVisitsDone, src => src.Appointments != null && src.Appointments.Any() ? src.Appointments.Where(x => x.Type == AppointmentType.SiteVisit && x.IsDone && x.UserId == src.AssignTo).Count() : default)
           .Map(dest => dest.SiteVisitsNotDone, src => src.Appointments != null && src.Appointments.Any() ? src.Appointments.Where(x => x.Type == AppointmentType.SiteVisit && !x.IsDone && x.UserId == src.AssignTo).Count() : default);
            TypeAdapterConfig<Domain.Entities.Lead, GetAllLeadDto>
            .NewConfig()
            .Map(dest => dest.PropertyType, src => src.Enquiries[0].PropertyType != null ? new PropertyTypeDto
            {
                Id = src.Enquiries[0].PropertyType.BaseId != null ? src.Enquiries[0].PropertyType.BaseId.Value : default,
                BaseId = null,
                DisplayName = src.Enquiries[0].PropertyType.BaseId != null ? propertytypes.FirstOrDefault(i => i.Id == src.Enquiries[0].PropertyType.BaseId.Value).DisplayName : default,
                Type = src.Enquiries[0].PropertyType.BaseId != null ? propertytypes.FirstOrDefault(i => i.Id == src.Enquiries[0].PropertyType.BaseId.Value).Type : default,
                Level = 0,
                ChildType = new()
                {
                    Id = src.Enquiries[0].PropertyType.Id,
                    BaseId = src.Enquiries[0].PropertyType.BaseId != null ? src.Enquiries[0].PropertyType.BaseId : default,
                    DisplayName = src.Enquiries[0].PropertyType.DisplayName,
                    Level = src.Enquiries[0].PropertyType.Level,
                    Type = src.Enquiries[0].PropertyType.Type,
                    ChildType = null
                }
            } : null)

            //.Map(dest => dest.Status, src => src.Status.BaseId == null || src.Status.BaseId == default
            //                                ? src.Status.Adapt<LeadStatusDto>()
            //                                : new LeadStatusDto()
            //                                {
            //                                    Id = src.Status.BaseId.Value,
            //                                    BaseId = null,
            //                                    Status = masterLeadStatuses.FirstOrDefault(i => i.Id == src.Status.BaseId).Status,
            //                                    DisplayName = masterLeadStatuses.FirstOrDefault(i => i.Id == src.Status.BaseId).DisplayName,
            //                                    ActionName = masterLeadStatuses.FirstOrDefault(i => i.Id == src.Status.BaseId).ActionName,
            //                                    Level = 0,
            //                                    ChildType = src.Status.Adapt<LeadStatusDto>()
            //                                }
            //                                ?? null)
            .Map(dest => dest.NoOfBHK, src => src.Enquiries[0].NoOfBHKs)
            .Map(dest=>dest.BHKs, src => src.Enquiries[0].BHKs)
            .Map(dest => dest.Source, src => src.Enquiries[0].LeadSource)
            .Map(dest => dest.Budget, src => src.Enquiries[0].UpperBudget)
            .Map(dest => dest.AssignedUserId, src => src.AssignTo)
            .Map(dest => dest.AssignedFromUserId, src => src.AssignedFrom)
            .Map(dest => dest.CallRecordingUrls, src => (src.CallRecordingUrls != null && src.CallRecordingUrls.Any()) ? src.CallRecordingUrls.GroupBy(i => i.Key.Year).ToDictionary(i => i.Key, i => i.GroupBy(i => i.Key.Month).ToDictionary(i => i.Key, i => i.ToList().ToDictionary(i => i.Key, i => i.Value))) : null)
            .Map(dest => dest.LeadFilterKey, src => LeadHelper.Find(src));

            TypeAdapterConfig<GetAllLeadFromSPDto, GetAllLeadDto>
                .NewConfig()
                .Map(dest => dest.Source, src => src.LeadSource)
                //.Map(dest => dest.Status, src => masterLeadStatuses.FirstOrDefault(i => i.Id == src.StatusId).BaseId == null
                //? masterLeadStatuses.FirstOrDefault(i => i.Id == src.StatusId)
                //: new LeadStatusDto()
                //{
                //    Id = masterLeadStatuses.FirstOrDefault(i => i.Id == src.StatusId).BaseId ?? Guid.Empty,
                //    BaseId = null,
                //    ActionName = masterLeadStatuses.FirstOrDefault(i => masterLeadStatuses.FirstOrDefault(i => i.Id == src.StatusId).BaseId == i.Id).ActionName,
                //    DisplayName = masterLeadStatuses.FirstOrDefault(i => masterLeadStatuses.FirstOrDefault(i => i.Id == src.StatusId).BaseId == i.Id).DisplayName,
                //    Level = 0,
                //    Status = masterLeadStatuses.FirstOrDefault(i => masterLeadStatuses.FirstOrDefault(i => i.Id == src.StatusId).BaseId == i.Id).Status,
                //    ChildType = masterLeadStatuses.FirstOrDefault(i => i.Id == src.StatusId)
                //})
                .Map(dest => dest.PropertyType, src => src.PropertyTypeId == null ? null : new PropertyTypeDto()
                {
                    Id = propertytypes.FirstOrDefault(i => i.Id == src.PropertyTypeId).BaseId ?? Guid.Empty,
                    BaseId = null,
                    DisplayName = propertytypes.FirstOrDefault(i => propertytypes.FirstOrDefault(i => i.Id == src.PropertyTypeId).BaseId == i.Id).DisplayName,
                    Type = propertytypes.FirstOrDefault(i => propertytypes.FirstOrDefault(i => i.Id == src.PropertyTypeId).BaseId == i.Id).Type,
                    ChildType = propertytypes.FirstOrDefault(i => i.Id == src.PropertyTypeId).Adapt<PropertyTypeDto>()
                });
            TypeAdapterConfig<UserDetailsDto, Team.Web.UserDto>
             .NewConfig()
             .Map(dest => dest.Name, src => src.FirstName + " " + src.LastName)
             .Map(dest => dest.ContactNo, src => src.PhoneNumber);

            TypeAdapterConfig<UpdateLeadStatusRequest, Domain.Entities.Lead>
                .NewConfig()
                .Ignore(src => src.Address)
                .Ignore(src => src.Projects)
                .Map(dest => dest.ScheduledDate, src => src.PostponedDate != null && src.PostponedDate != default ? src.PostponedDate : src.ScheduledDate);
            TypeAdapterConfig<CreateLeadEnquiryDto, LeadEnquiry>
                .NewConfig()
                .Map(dest => dest.SubSource, src => string.IsNullOrWhiteSpace(src.SubSource) ? null : src.SubSource.Trim().ToLower());
            TypeAdapterConfig<UpdateLeadEnquiryDto, LeadEnquiry>
               .NewConfig()
               .Map(dest => dest.SubSource, src => string.IsNullOrWhiteSpace(src.SubSource) ? null : src.SubSource.Trim().ToLower());
            TypeAdapterConfig<UpdateLeadStatusRequest, PickedLeadDto>
                .NewConfig()
                .Map(dest => dest.Projects, src => (src.Projects != null && src.Projects.Any()) ? src.Projects.Select(i => new V2ProjectsDto() { Name = i }).ToList() : new());
            TypeAdapterConfig<AddDocumentsRequest, PickedLeadDto>
                .NewConfig()
                .Map(dest => dest.Documents, src => src.Documents);
            TypeAdapterConfig<AddProjectsInLeadRequest, PickedLeadDto>
                .NewConfig()
                .Map(dest => dest.ProjectNames, src => src.Projects);
            TypeAdapterConfig<DeleteDocumentRequest, PickedLeadDto>
                .NewConfig()
                .Map(dest => dest.Documents, src => src.DocumentIds);
            TypeAdapterConfig<UpdateLeadRequest, PickedLeadDto>
                .NewConfig();
            TypeAdapterConfig<UpdateLeadTagRequest, PickedLeadDto>
                .NewConfig();
            TypeAdapterConfig<UpdateLeadStatusRequest, PickedLeadDto>
                .NewConfig()
                .Ignore(src => src.Projects);
            TypeAdapterConfig<UpdateSiteVisitOrMeetingDoneRequest, PickedLeadDto>
                .NewConfig()
                .Map(dest => dest.Appointments, src => new List<LeadAppointmentDto>(){ new() {
                   Type = src.MeetingOrSiteVisit,
                   IsDone = src.IsDone,
                   Longitude = src.Longitude,
                   Latitude = src.Latitude,
                   ProjectName = src.ProjectName,
                   ExecutiveName = src.ExecutiveName,
                   ExecutiveContactNo = src.ExecutiveContactNo,
                   Image = src.Image,
                   ImagesWithName = src.ImagesWithName,
                   IsManual = src.IsManual,
                   Notes = src.Notes,
                   Location = src.Address
                } });
            TypeAdapterConfig<UploadLeadDocumentRequest, PickedLeadDto>
                .NewConfig();
            TypeAdapterConfig<LeadBookedDetail, BookedDetailsDto>
                .NewConfig()
                .Map(dest => dest.PropertyName, src => src.Properties != null && src.Properties.Any() ? src.Properties.FirstOrDefault().Title ?? string.Empty : string.Empty)
                .Map(dest => dest.ProjectName, src => src.Projects != null && src.Projects.Any() ? src.Projects.FirstOrDefault().Name ?? string.Empty : string.Empty);
            TypeAdapterConfig<Lrb.Domain.Entities.Property, BasicPropertyInfoDto>
               .NewConfig()
               .Map(dest => dest.Dimension, src => src.Dimension != null ? src.Dimension : default);
            TypeAdapterConfig<CreateLeadRequest, DuplicateLeadSpecDto>
              .NewConfig()
              .Map(dest => dest.LeadSource, src => src.Enquiry != null ? src.Enquiry.LeadSource : LeadSource.Direct);
        } 
        public static LeadStatusDto? MapLeadStatus(Lrb.Domain.Entities.Lead src, List<LeadStatusDto>? masterLeadStatuses)
        {
            if (src.CustomLeadStatus != null)
            {
                if (masterLeadStatuses == null)
                {
                    IRepository<CustomMasterLeadStatus> _customMasterLeadStatusRepo = null;
                    var ServiceProvider = ServiceLocator.GetService();
                    _customMasterLeadStatusRepo = (IRepository<CustomMasterLeadStatus>)ServiceProvider.GetRequiredService(typeof(IRepository<>).MakeGenericType(typeof(CustomMasterLeadStatus)));
                    masterLeadStatuses = _customMasterLeadStatusRepo.ListAsync().Result.Adapt<List<LeadStatusDto>>();
                }
                if (masterLeadStatuses != null)
                {
                    if (src?.CustomLeadStatus?.BaseId == null || src.CustomLeadStatus.BaseId == default)
                    {
                        return src.CustomLeadStatus.Adapt<LeadStatusDto>();
                    }
                    else
                    {
                        var parentStatus = masterLeadStatuses.FirstOrDefault(i => i.Id == src.CustomLeadStatus.BaseId);
                        return new LeadStatusDto()
                        {
                            Id = src.CustomLeadStatus.BaseId.Value,
                            BaseId = null,
                            Status = parentStatus?.Status,
                            DisplayName = parentStatus?.DisplayName,
                            ActionName =  parentStatus?.ActionName,
                            Level = 0,
                            ChildType = src?.CustomLeadStatus.Adapt<LeadStatusDto>(),
                            IsDefault = parentStatus?.IsDefault ?? false,
                            IsChildDefault = parentStatus?.IsChildDefault ?? false,
                            WhatsAppTemplateInfoIds = parentStatus?.WhatsAppTemplateInfoIds,
                            ShouldUseForBooking = parentStatus?.ShouldUseForBooking ?? false,
                            ShouldUseForBookingCancel = parentStatus?.ShouldUseForBookingCancel ?? false,
                            ShouldOpenAppointmentPage = parentStatus?.ShouldOpenAppointmentPage ?? false,
                            ShouldUseForMeeting = parentStatus?.ShouldUseForMeeting,
                            IsScheduled = parentStatus?.IsScheduled ?? false,
                        };

                    }
                }

            }
            return null;
        }
    }
}

