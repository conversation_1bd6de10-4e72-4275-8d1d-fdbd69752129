﻿using Lrb.Application.Common.GooglePlaces;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Mobile.Dtos.v1;
using Lrb.Application.Lead.Mobile.Mappings;
using Lrb.Application.Lead.Mobile.Mappings.v1;
using Lrb.Application.Lead.Mobile.Requests;
using Lrb.Application.Lead.Mobile.Specs.v1;
using Lrb.Application.Project.Mobile;
using Lrb.Application.Project.Mobile.Specs;
using Lrb.Application.Property.Mobile;
using Lrb.Application.Utils;
using Lrb.Application.ZonewiseLocation.Mobile.Helpers;
using Lrb.Application.ZonewiseLocation.Mobile.Requests;
using Lrb.Application.ZonewiseLocation.Mobile.Specs;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Lrb.Shared.Extensions;
using Newtonsoft.Json;
using Serilog;

namespace Lrb.Application.Lead.Mobile
{
    public class UpdateLeadStatusRequest : IRequest<Response<bool>>
    {
        public Guid Id { get; set; }
        public Guid LeadStatusId { get; set; }
        public string? Notes { get; set; }
        public DateTime? ScheduledDate { get; set; }
        public DateTime? RevertDate { get; set; }
        public string? SoldPrice { get; set; }
        public string? BookedUnderName { get; set; }
        public DateTime? BookedDate { get; set; }
        public string? Rating { get; set; }
        public List<string>? ProjectsList { get; set; }
        public List<string>? PropertiesList { get; set; }
        public DateTime? PostponedDate { get; set; }
        public long? UnmatchedBudget { get; set; }
        public string? PurchasedFrom { get; set; }
        public string? PreferredLocation { get; set; }
        public AppointmentType MeetingOrSiteVisit { get; set; }
        public bool IsDone { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public string? ChosenProject { get; set; }
        public string? ChosenProperty { get; set; }
        public List<string>? Projects { get; set; }
        public bool? IsFullyCompleted { get; set; }
        public AddressDto? Address { get; set; }
        public List<AddressDto>? Addresses { get; set; }
        public Guid? LocationId { get; set; }
        public string? SoldPriceCurrency { get; set; } = "INR";
        public double? AgreementValue { get; set; }
        public List<Guid>? ProjectIds { get; set; }
        public List<Guid>? PropertyIds { get; set; }
        public bool? IsChoosenProperty { get; set; }
        public Guid? UnitTypeId { get; set; }
        public string? Currency { get; set; }
        public bool? IsNotesUpdated { get; set; }

    }
    public class UpdateLeadStatusRequestHandler : LeadCommonRequestHandler, IRequestHandler<UpdateLeadStatusRequest, Response<bool>>
    {
        protected readonly IRepositoryWithEvents<Domain.Entities.Property> _propertyRepository;
        public UpdateLeadStatusRequestHandler(IServiceProvider serviceProvider, IRepositoryWithEvents<Domain.Entities.Property> propertyRepository) : base(serviceProvider, typeof(UpdateLeadStatusRequestHandler).Name, "Handle")
        {
            _propertyRepository = propertyRepository;
        }
        public async Task<Response<bool>> Handle(UpdateLeadStatusRequest request, CancellationToken cancellationToken)
        {
            try
            {
                Domain.Entities.GlobalSettings? globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                var existingLead = (await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(request.Id), cancellationToken));
                if (existingLead == null)
                {
                    throw new NotFoundException("No Lead found by this Id");
                }
                if (request.LeadStatusId == default)
                {
                    throw new ArgumentException("The Status Id is not valid");
                }
                var parentStatus = (await _leadStatusRepo.ListAsync(new ParentLeadStatusSpecs(request.LeadStatusId), cancellationToken)).FirstOrDefault();
                if (parentStatus != null)
                {
                    var child = (await _leadStatusRepo.ListAsync(new LeadStatusSpec(request.LeadStatusId), cancellationToken));
                    if (parentStatus.Status.Contains("meeting_scheduled"))
                    {
                        var statusId = (child.Where(i => i.Status.Contains("others")).Select(i => i.Id)).FirstOrDefault();
                        request.LeadStatusId = statusId;
                    }
                    else if (parentStatus.Status.Contains("site_visit_scheduled"))
                    {
                        var statusId = (child.Where(i => i.Status.Contains("first_visit")).Select(i => i.Id)).FirstOrDefault();
                        request.LeadStatusId = statusId;
                    }
                }
                var chaildStatuses = await _leadStatusRepo.ListAsync(new LeadStatusSpec(request.LeadStatusId), cancellationToken);
                if (chaildStatuses?.Any() ?? false)
                {
                    throw new ArgumentException("Please provide child status id.");
                }
                var leadStatus = await _leadStatusRepo.GetByIdAsync(request.LeadStatusId, cancellationToken);
                if (leadStatus == null)
                {
                    throw new ArgumentException("The Status Id is not valid");
                }
                var baseLeadStatus = await _leadStatusRepo?.GetByIdAsync(leadStatus.BaseId, cancellationToken);

                #region PickedLead
                try
                {
                    existingLead.ShouldUpdatePickedDate = await ShouldUpdatePickedDate(existingLead, request.Adapt<PickedLeadDto>());
                }
                catch (Exception ex)
                {
                    throw;
                }
                #endregion
                var existingBookedDate = existingLead.BookedDate;
                var existingBookedBy = existingLead.BookedBy;
                existingLead = request.Adapt(existingLead);
                #region Updating Meeting and SiteVisit Done
                if (request.MeetingOrSiteVisit != AppointmentType.None)
                {
                    try
                    {
                        var appointment = request.Adapt<LeadAppointment>();                     

                        appointment.Id = Guid.Empty;
                        appointment.UserId = existingLead.AssignTo;
                        switch (request.MeetingOrSiteVisit)
                        {
                            case AppointmentType.Meeting:
                                existingLead.IsMeetingDone = request.IsDone;
                                appointment.Type = AppointmentType.Meeting;
                                var places = await _googlePlacesService.GetPlaceDetailsByCoordinatesAsync(request.Latitude, request.Longitude);
                                var place = places.FirstOrDefault();
                                if (place != null && place.PlaceId != null)
                                {
                                    var address = (await _addressRepo.ListAsync(new GetAddressByPlaceIdSpec(place.PlaceId), cancellationToken)).FirstOrDefault();
                                    if (address != null)
                                    {
                                        existingLead.MeetingLocation = address.Id;
                                        appointment.Location = address;
                                    }
                                    else
                                    {
                                        address = place.Adapt<Address>();
                                        address = await _addressRepo.AddAsync(address);
                                        existingLead.MeetingLocation = address.Id;
                                        appointment.Location = address;
                                    }
                                }
                                break;
                            case AppointmentType.SiteVisit:
                                existingLead.IsSiteVisitDone = request.IsDone;
                                appointment.Type = AppointmentType.SiteVisit;
                                places = await _googlePlacesService.GetPlaceDetailsByCoordinatesAsync(request.Latitude, request.Longitude);
                                place = places.FirstOrDefault();
                                if (place != null && place.PlaceId != null)
                                {
                                    var address = (await _addressRepo.ListAsync(new GetAddressByPlaceIdSpec(place.PlaceId), cancellationToken)).FirstOrDefault();
                                    if (address != null)
                                    {
                                        existingLead.SiteLocation = address.Id;
                                        appointment.Location = address;
                                    }
                                    else
                                    {
                                        address = place.Adapt<Address>();
                                        address = await _addressRepo.AddAsync(address);
                                        existingLead.SiteLocation = address.Id;
                                        appointment.Location = address;
                                    }
                                }
                                break;
                        }
                        if (existingLead.Appointments?.Any() ?? false)
                        {
                            existingLead.Appointments.Add(appointment);
                        }
                        else
                        {
                            existingLead.Appointments = new List<LeadAppointment>() { appointment };
                        }
                    }
                    catch (Exception ex)
                    {
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "UpdateLeadStatusRequestHandler -> Handle()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                }
                #endregion

                existingLead.CustomLeadStatus = leadStatus;
                if (request.PostponedDate != null && request.PostponedDate != default)
                {
                    existingLead.ScheduledDate = request.PostponedDate;
                }
                else if (leadStatus != null && leadStatus.MasterLeadStatusBaseId == Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                {
                    existingLead.ScheduledDate = null;
                }
                //Address? diffrentLocationAddress = null;
                //if (request.Address?.LocationId != null && request.Address.LocationId != Guid.Empty)
                //{
                //    diffrentLocationAddress = await _addressRepo.FirstOrDefaultAsync(new AddressByLocationIdSpec(new() { request.Address.LocationId ?? Guid.Empty }), cancellationToken);
                //    if (diffrentLocationAddress == null)
                //    {
                //        var location = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(request.Address.LocationId ?? Guid.Empty), cancellationToken);
                //        if (location != null)
                //        {
                //            diffrentLocationAddress = location.MapToAddress();
                //            if (diffrentLocationAddress != null)
                //            {
                //                diffrentLocationAddress.Id = Guid.Empty;
                //                diffrentLocationAddress = await _addressRepo.AddAsync(diffrentLocationAddress);
                //            }
                //        }
                //    }
                //}
                //else if (!string.IsNullOrEmpty(request.Address?.PlaceId) && diffrentLocationAddress == null)
                //{
                //    diffrentLocationAddress = (await _addressRepo.ListAsync(new GetAddressByPlaceIdSpec(request.Address.PlaceId), cancellationToken)).FirstOrDefault();
                //    if (diffrentLocationAddress == null)
                //    {
                //        try
                //        {
                //            diffrentLocationAddress = (await _googlePlacesService.GetPlaceDetailsByPlaceIdAsync(request.Address.PlaceId))?.Adapt<Address>() ?? null;
                //        }
                //        catch (Exception ex) { }
                //        diffrentLocationAddress = await _addressRepo.AddAsync(diffrentLocationAddress);
                //    }
                //    if (existingLead?.Enquiries?.FirstOrDefault(i => i.IsPrimary) != null)
                //    {
                //        existingLead.Enquiries.FirstOrDefault(i => i.IsPrimary).Address = diffrentLocationAddress;
                //    }
                //}
                //else if (diffrentLocationAddress == null && (request.Address?.Adapt<Address>()?.Validate(out Address? newAddress) ?? false))
                //{
                //    if (newAddress != null)
                //    {
                //        var address = await _addressRepo.GetByIdAsync(request?.Address?.Id ?? Guid.Empty);
                //        if (address == null)
                //        {
                //            diffrentLocationAddress = await _addressRepo.AddAsync(newAddress);
                //        }
                //        else
                //        {
                //            diffrentLocationAddress = address;
                //        }
                //        await MapAddressToLocationAndSaveAsync(diffrentLocationAddress);
                //    }
                //}
                List<Address> diffrentLocationAddresses = new List<Address>();
                if (request?.Addresses?.Any() ?? false)
                {
                    foreach (var address in request.Addresses)
                    {
                        Address? diffrentLocationAddress = null;

                        if (address.LocationId != null && address.LocationId != Guid.Empty)
                        {
                            diffrentLocationAddress = await _addressRepo.FirstOrDefaultAsync(new AddressByLocationIdSpec(new() { address.LocationId ?? Guid.Empty }), cancellationToken);
                            if (diffrentLocationAddress == null)
                            {
                                var location = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(address.LocationId ?? Guid.Empty), cancellationToken);
                                if (location != null)
                                {
                                    diffrentLocationAddress = location.MapToAddress();
                                    if (diffrentLocationAddress != null)
                                    {
                                        diffrentLocationAddress.Id = Guid.Empty;
                                        diffrentLocationAddress = await _addressRepo.AddAsync(diffrentLocationAddress);
                                    }
                                }
                            }
                        }
                        else if (!string.IsNullOrEmpty(address.PlaceId) && diffrentLocationAddress == null)
                        {
                            diffrentLocationAddress = (await _addressRepo.ListAsync(new GetAddressByPlaceIdSpec(address.PlaceId), cancellationToken)).FirstOrDefault();
                            if (diffrentLocationAddress == null)
                            {
                                try
                                {
                                    diffrentLocationAddress = (await _googlePlacesService.GetPlaceDetailsByPlaceIdAsync(address.PlaceId))?.Adapt<Address>() ?? null;
                                }
                                catch (Exception ex) { }
                                diffrentLocationAddress = await _addressRepo.AddAsync(diffrentLocationAddress);
                            }

                        }

                        else if (diffrentLocationAddress == null && (address.Adapt<Address>()?.ValidateAddress(out Address? newAddress) ?? false))
                        {
                            if (newAddress != null)
                            {
                                var existingAddress = await _addressRepo.GetByIdAsync(address?.Id ?? Guid.Empty);
                                if (existingAddress == null)
                                {
                                    diffrentLocationAddress = await _addressRepo.AddAsync(newAddress);
                                }
                                else
                                {
                                    diffrentLocationAddress = existingAddress;
                                }
                                await MapAddressToLocationAndSaveAsync(diffrentLocationAddress);
                            }
                        }
                        diffrentLocationAddresses.Add(diffrentLocationAddress);
                    }

                }
                //if (existingLead?.Enquiries?.FirstOrDefault(i => i.IsPrimary) != null && diffrentLocationAddress != null)
                //{
                //    existingLead.Enquiries.FirstOrDefault(i => i.IsPrimary).Address = diffrentLocationAddress;
                //}
                if ((existingLead?.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.Addresses?.Any() ?? false) && (diffrentLocationAddresses?.Any() ?? false))
                {
                    existingLead.Enquiries.FirstOrDefault(i => i.IsPrimary).Addresses = diffrentLocationAddresses;
                }
                #region projects
                if (request.ProjectsList?.Any() ?? false)
                {
                    List<Lrb.Domain.Entities.Project>? projects = new();
                    request.ProjectsList = request.ProjectsList.Where(i => !string.IsNullOrWhiteSpace(i)).ToList();
                    if (request?.ProjectsList?.Any() ?? false)
                    {
                        foreach (var newProject in request.ProjectsList)
                        {
                            Lrb.Domain.Entities.Project? existingProject = (await _projectRepo.ListAsync(new GetProjectByIdSpecsV2(newProject), cancellationToken)).FirstOrDefault();
                            if (existingProject != null)
                            {
                                projects.Add(existingProject);
                            }
                            else
                            {
                                Domain.Entities.Project project = new() { Name = newProject };
                                project = await _projectRepo.AddAsync(project, cancellationToken);
                                projects.Add(project);
                            }
                        }
                    }
                    existingLead.Projects = projects;
                }

                #endregion

                #region Properties

                if (request?.PropertiesList?.Any() ?? false)
                {
                    List<Domain.Entities.Property>? properties = new();
                    foreach (var newProperty in request.PropertiesList)
                    {
                        var existingProperty = (await _propertyRepo.ListAsync(new GetPropertyByTitleSpec(newProperty), cancellationToken)).FirstOrDefault();
                        if (existingProperty != null)
                        {
                            properties.Add(existingProperty);
                        }
                        else
                        {
                            Domain.Entities.Property property = new() { Title = newProperty };
                            property = await _propertyRepo.AddAsync(property, cancellationToken);
                            properties.Add(property);
                        }
                    }
                    existingLead.Properties = properties;
                }
                #endregion

                if (baseLeadStatus != null && baseLeadStatus.Status == "booked" || existingLead.CustomLeadStatus?.Status == "booked")
                {
                    if (request?.BookedDate != null)
                    {
                        existingLead.BookedDate = request.BookedDate;
                    }
                    else
                    {
                        existingLead.BookedDate = DateTime.UtcNow;
                    }
                    existingLead.BookedBy = _currentUser.GetUserId();
                    var userName = await _userService.GetAsync(existingLead.BookedBy.ToString() ?? string.Empty, cancellationToken);
                    var bookedDetailInfo = await _leadBookedDetailRepo.FirstOrDefaultAsync(new GetBookedDetailsByIdSpec(existingLead.Id), cancellationToken);
                    if (bookedDetailInfo != null)
                    {
                        bookedDetailInfo.BookedDate = existingLead?.BookedDate;
                        bookedDetailInfo.BookedBy = existingLead?.BookedBy;
                        bookedDetailInfo.BookedByUser = userName?.FirstName?? string.Empty + " " + userName?.LastName ?? string.Empty;
                        bookedDetailInfo.BookedUnderName = existingLead?.BookedUnderName;
                        bookedDetailInfo.UserId = existingLead?.AssignTo ?? Guid.Empty;
                        bookedDetailInfo.SoldPrice = request?.SoldPrice;
                        bookedDetailInfo.Notes = request?.Notes;
                        bookedDetailInfo.ProjectsList = request?.ProjectsList;
                        bookedDetailInfo.PropertiesList = request?.PropertiesList;
                        bookedDetailInfo.AgreementValue = request?.AgreementValue ?? default;
                        bookedDetailInfo.Properties = await _propertyRepository.ListAsync(new GetPropertiesByIdspecs(request?.PropertyIds ?? new()));
                        bookedDetailInfo.Projects = await _projectRepo.ListAsync(new ProjectByIdsSpec(request?.ProjectIds ?? new()), cancellationToken);
                        bookedDetailInfo.IsChoosenProperty = request?.IsChoosenProperty ?? false;
                        bookedDetailInfo.UnitType = await _unitType.GetBySpecAsync(new GetUnitInfoSpecs(request?.UnitTypeId ?? Guid.Empty)) ?? null;
                        await _leadBookedDetailRepo.UpdateAsync(bookedDetailInfo);

                    }
                    else
                    {
                        LeadBookedDetail bookedDetail = new();
                        bookedDetail.LeadId = existingLead.Id;
                        bookedDetail.BookedDate = existingLead?.BookedDate;
                        bookedDetail.BookedBy = existingLead?.BookedBy;
                        bookedDetail.BookedByUser = userName?.FirstName ?? string.Empty + " " + userName?.LastName ?? string.Empty;
                        bookedDetail.BookedUnderName = existingLead?.BookedUnderName;
                        bookedDetail.UserId = existingLead?.AssignTo ?? Guid.Empty;
                        bookedDetail.SoldPrice = request?.SoldPrice;
                        bookedDetail.Notes = request?.Notes;
                        bookedDetail.AgreementValue = request?.AgreementValue ?? default;
                        bookedDetail.ProjectsList = request?.ProjectsList;
                        bookedDetail.PropertiesList = request?.PropertiesList;
                        bookedDetail.Properties = await _propertyRepository.ListAsync(new GetPropertiesByIdspecs(request?.PropertyIds ?? new()));
                        bookedDetail.Projects = await _projectRepo.ListAsync(new ProjectByIdsSpec(request?.ProjectIds ?? new()), cancellationToken);
                        bookedDetail.IsChoosenProperty = request?.IsChoosenProperty ?? false;
                        existingLead?.BookedDetails?.Add(bookedDetail);
                        await _leadBookedDetailRepo.AddAsync(bookedDetail);
                    }
                }
                else
                {
                    existingLead.BookedDate = existingBookedDate;
                    existingLead.BookedBy = existingBookedBy;
                }
                await _leadRepo.UpdateAsync(existingLead);
                if ((request?.IsFullyCompleted ?? false) && (request?.Projects?.Any() ?? false))
                {
                    var appointments = await _appointmentRepo.ListAsync(new GetAppointmentsByProjectsSpec(request?.Id ?? Guid.Empty, (request?.Projects?.ConvertAll(i => i.Trim().ToLower()) ?? new List<string>())));
                    appointments?.ForEach(i => i.IsFullyCompleted = true);
                    if (appointments?.Any() ?? false)
                    {
                        try
                        {
                            await _appointmentRepo.UpdateRangeAsync(appointments);
                        }
                        catch (Exception ex)
                        {
                            var error = new LrbError()
                            {
                                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                ErrorSource = ex?.Source,
                                StackTrace = ex?.StackTrace,
                                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                ErrorModule = "UpdateSiteVisitOrMeetingDoneRequestHandler -> Handle()"
                            };
                            await _leadRepositoryAsync.AddErrorAsync(error);
                        }
                    }
                }
                var fullLead = (await _leadRepo.ListAsync(new LeadByIdSpec(request.Id), cancellationToken))?.FirstOrDefault();
                var leadDto = fullLead?.Adapt<ViewLeadDto>();
                await leadDto?.SetUsersInViewLeadDtoAsync(_userService, cancellationToken);
                await UpdateLeadHistoryAsync(fullLead, leadDto, cancellationToken: cancellationToken, shouldUpdateNotes: request.IsNotesUpdated ?? false);
                try
                {
                    await existingLead.SendLeadStatusChangeNotificationsAsync(leadDto, _notificationSenderService, globalSettings, _currentUser.GetUserId());
                    Event? @event = null;
                    if (request.MeetingOrSiteVisit != AppointmentType.None)
                    {
                        switch (request.MeetingOrSiteVisit)
                        {
                            case AppointmentType.SiteVisit:
                                switch (request.IsDone)
                                {
                                    case true:
                                        @event = Event.LeadSiteVisitDone;
                                        break;
                                    case false:
                                        @event = Event.LeadSiteVisitNotDone;
                                        break;
                                }
                                break;
                            case AppointmentType.Meeting:
                                switch (request.IsDone)
                                {
                                    case true:
                                        @event = Event.LeadMeetingDone;
                                        break;
                                    case false:
                                        @event = Event.LeadMeetingNotDone;
                                        break;
                                }
                                break;
                        }
                        if (@event != null)
                        {
                            Event updatedEvent = (Event)@event;
                            var userDetails = await _userService.GetAsync(existingLead.AssignTo.ToString(), cancellationToken);
                            if (userDetails.Id != _currentUser.GetUserId())
                            {
                                await _notificationSenderService.ScheduleNotificationsAsync(updatedEvent, existingLead, userDetails.Id, userDetails.FirstName + " " + userDetails.LastName, globalSettings: globalSettings);
                            }
                            if(existingLead.AssignTo == Guid.Empty)
                            {
                                await _notificationSenderService.DeleteScheduledNotificationsAsync(existingLead);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    var error = new LrbError()
                    {
                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                        ErrorSource = ex?.Source,
                        StackTrace = ex?.StackTrace,
                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                        ErrorModule = "UpdateLeadStatusRequestHandler -> Handle()"
                    };
                    await _leadRepositoryAsync.AddErrorAsync(error);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("UpdateLeadStatusRequestHandler exception details  => " + ex.Serialize());
                Log.Information("UpdateLeadStatusRequestHandler exception details  => " + ex.Serialize());
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = ex?.InnerException?.Serialize() ?? default,
                    ErrorModule = "UpdateLeadStatusRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            return new(true);
        }
        private async Task MapAddressToLocationAndSaveAsync(Address address)
        {
            var location = address.MapToLocationRequest();
            if (location != null)
            {
                var locationRes = await _mediator.Send(location.Adapt<AddLocationRequest>());
                var createdLocation = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(locationRes.Data), default);
                address.Location = createdLocation;
                await _addressRepo.UpdateAsync(address);
            }
        }
    }
}
