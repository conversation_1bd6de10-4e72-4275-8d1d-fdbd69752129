﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Common.Email;
using Lrb.Application.Common.Facebook;
using Lrb.Application.Common.GoogleAuth;
using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.Models;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.Common.WhatsApp;
using Lrb.Application.GlobalSettings.Web.Dto;
using Lrb.Application.Identity.Users;
using Lrb.Application.Integration.Mobile.Specs;
using Lrb.Application.Integration.Web;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.UserDetails.Web;
using Lrb.Application.Utils;
using Lrb.Application.ZonewiseLocation.Web.Helpers;
using Lrb.Application.ZonewiseLocation.Web.Requests;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.CustomAddress;
using Lrb.Domain.Entities.DataManagement;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.Integration;
using Lrb.Domain.Entities.Integration.GoogleSheet;
using Lrb.Domain.Entities.Marketing;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Entities.User;
using Lrb.Domain.Enums;
using Mapster;
using MediatR;
using Newtonsoft.Json;
using PhoneNumbers;
using System.Collections.Concurrent;
using System.Data;
using System.Text.RegularExpressions;
using Lrb.Domain.Entities.User;
using Lrb.Domain.Entities.DataManagement;
using Lrb.Domain.Entities.AmenitiesAttributes;
using Lrb.Infrastructure.BlobStorage;
using Microsoft.Extensions.Options;
using Lrb.Application.Common.GoogleAds;
using Lrb.Infrastructure.GoogleAds;


namespace ExcelUpload
{
    public class CreateBulkLeadleadUploadTrackerUsingEPPlus
    {
        public string? S3BucketKey { get; set; }
        public Dictionary<DataColumns, string>? MappedColumnsData { get; set; }
        public List<string>? UserIds { get; set; }
        public Guid TrackerId { get; set; }
    }
    public partial class FunctionEntryPoint : IFunctionEntryPoint
    {
        private readonly IBlobStorageService _blobStorageService;
        private readonly INotificationSenderService _notificationSenderService;
        //private readonly IRepositoryWithEvents<TempProjects> _tempProjectsRepo;
        private readonly IRepositoryWithEvents<Property> _propertyRepo;
        private readonly IRepositoryWithEvents<MasterPropertyType> _propertyTypeRepo;
        //private readonly IRepositoryWithEvents<MasterLeadStatus> _leadStatusRepo;
        private readonly IReadRepository<MasterAreaUnit> _masterAreaUnitRepo;
        private readonly IUserService _userService;
        private readonly ICurrentUser _currentUser;
        private readonly INpgsqlRepository _npgsqlRepo;
        private readonly ITenantIndependentRepository _tenantIndependentRepo;
        private readonly IBulkLeadUploadTrackerRepository _bulkLeadUploadTrackerRepository;
        private readonly IRepositoryWithEvents<Lead> _leadRepo;
        private readonly IRepositoryWithEvents<LeadHistory> _leadHistoryRepo;
        private readonly IDapperRepository _dapperRepository;
        private readonly IRepositoryWithEvents<UserDetails> _userDetailsRepo;
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccountInfoRepositoryAsync;
        private readonly IBulkPropertyUploadTrackerRepository _bulkPropertyUploadTrackerRepo;
        private readonly IReadRepository<CustomMasterAttribute> _masterPropertyAttributeRepo;
        private readonly IReadRepository<PropertyDimension> _propertyDimensionRepo;
        private readonly IRepositoryWithEvents<ExportLeadTracker> _exportLeadRepo;
        private readonly IGraphEmailService _graphEmailService;
        private readonly IReadRepository<MasterEmailTemplates> _masterEmailTemplatesRepo;
        private readonly IReadRepository<MasterEmailServiceProvider> _masterEmailServiceProviderRepo;
        private readonly ILeadRepository _leadRepository;
        private readonly IReadRepository<ExportTemplate> _exportTemplateRepo;
        private readonly IReadRepository<GlobalSettings> _globalSettingsRepository;
        private readonly IRepositoryWithEvents<ExportReportsTracker> _exportReportsTrackerRepo;
        //private readonly IRepositoryWithEvents<TempProjects> _projectRepo;
        private readonly IRepositoryWithEvents<FacebookBulkLeadFetchTracker> _fbBulkLeadFetchTrackerRepository;
        private readonly IRepositoryWithEvents<IntegrationAssignmentInfo> _integrationAssignmentInfoRepo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<ExportFacebookBulkLeadsTracker> _exportFbBulkLeadsTrackerRepo;
        private readonly IRepositoryWithEvents<IntegrationAssignment> _integrationAssignmentRepo;
        private readonly IRepositoryWithEvents<AssignmentModule> _assignmentModuleRepo;
        private readonly IRepositoryWithEvents<UserAssignment> _userAssignmentRepo;
        private readonly IMediator _mediator;
        private readonly IRepositoryWithEvents<Address> _addressRepo;
        private readonly IRepositoryWithEvents<DuplicateLeadFeatureInfo> _duplicateFeatureInfo;
        private readonly IRepositoryWithEvents<ValidatedExcel> _excelRepo;
        private readonly IWebGoogleOAuth2Service _googleAuthService;
        private readonly IRepositoryWithEvents<GoogleSheetIntegrationData> _googelSheetIntegrationRepo;
        private readonly IRepositoryWithEvents<SpreadSheet> _spreadSheetRepo;
        private readonly IRepositoryWithEvents<Sheet> _sheetRepo;
        private readonly IRepositoryWithEvents<GoogleSheetUploadTracker> _googleSheetTrackerRepo;
        private readonly IRepositoryWithEvents<ChannelPartner> _cpRepository;
        private readonly IRepositoryWithEvents<BulkChannelPartnerUploadTracker> _bulkChannelPartnerUploadRepo;
        private readonly IRepositoryWithEvents<GoogleSpreadSheetTracker> _googleSpreadSheetTrackerRepo;
        private readonly IRepositoryWithEvents<ExportAttendanceTracker> _exportAttendanceTrackerRepo;
        private readonly IReadRepository<AttendanceLog> _attendanceRepository;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customMastereadStatus;
        private readonly IRepositoryWithEvents<WhatsAppBulkTemplateTracker> _whatsAppBulkTemplateTrackerRepo;
        private readonly IWhatsAppSenderService _whatsAppSenderService;
        private readonly IRepositoryWithEvents<WhatsAppTemplateInfo> _whatsAppTemplateInfoRepo;
        private readonly IRepositoryWithEvents<BulkProspectUploadTracker> _bulkProspectUploadRepo;
        private readonly IRepositoryWithEvents<Prospect> _prospectRepo;
        private readonly IRepositoryWithEvents<CustomProspectStatus> _prospectStatusRepo;
        private readonly IRepositoryWithEvents<MasterProspectSource> _prospectSourceRepo;
        private readonly IRepositoryWithEvents<ProspectHistory> _prospectHistoryRepo;
        private readonly IRepositoryWithEvents<ExportPropertyTracker> _exportPropertyRepo;
        private readonly IReadRepository<CustomMasterAmenity> _masterPropertyAmenityRepo;
        private readonly IReadRepository<MasterPropertyType> _masterPropertyTypeRepo;
        private readonly IRepositoryWithEvents<ExportProspectTracker> _exportProspectTrackerRepo;
        private readonly IProspectRepository _prospectRepository;
        private readonly IRepositoryWithEvents<LeadMigrateTracker> _leadMigrateTrackerRepo;
        private readonly IRepositoryWithEvents<CustomFilter> _customFilterRepo;
        private readonly IRepositoryWithEvents<Flag> _flagRepo;
        private readonly IRepositoryWithEvents<Agency> _agencyRepo;
        private readonly IRepositoryWithEvents<CustomFlag> _customflags;
        private readonly IRepositoryWithEvents<BulkUnitUploadTracker> _bulkUnitUploadRepo;
        private readonly IRepositoryWithEvents<MasterProjectType> _projectTypeRepo;
        private readonly IRepositoryWithEvents<MasterLeadSource> _masterLeadSource;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _newProjectRepo;
        private readonly IRepositoryWithEvents<ExportUserTracker> _exportUserRepo;
        private readonly IRepositoryWithEvents<UserView> _userViewRepo;
        private readonly IRepositoryWithEvents<UnitType> _unitRepo;
        private readonly IReadRepository<CustomMasterAttribute> _masterProjectUnitAttributeRepo;
        private readonly IRepositoryWithEvents<DataMigrateTracker> _dataMigrateTrackerRepo;
        private readonly IRepositoryWithEvents<UserDetails> _userRepository;
        private readonly IRepositoryWithEvents<Project> _projectRepo;
        private readonly IRepositoryWithEvents<UserAssignment> _assignmentRepo;
        private readonly IRepositoryWithEvents<DeletedUser> _deleteUsers;
        private readonly IRepositoryWithEvents<UserDeletedTracker> _userDeletedRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Attendance.AttendenceSettings> _attendenceRepo;
        private readonly IRepositoryWithEvents<CustomEmailInfo> _customemailRepo;
        private readonly IRepositoryWithEvents<LeadsAssignRotationInfo> _leadrotationRepo;
        private readonly IRepositoryWithEvents<Team> _teamRepo;
        private readonly Serilog.ILogger _logger;
        private readonly IRepositoryWithEvents<BulkMarketingAgencyUploadTracker> _bulkMarketingUploadRepo;
        private readonly IRepositoryWithEvents<ExportMarketingTracker> _exportMarketingTrackerRepo;
        private readonly IRepositoryWithEvents<WATemplate> _watemplateRepo;
        private readonly ITemplateNotificationService _templateNotificationService;
        private readonly IRepositoryWithEvents<Department> _departmentRepo;
        private readonly IRepositoryWithEvents<Designation> _designationRepo;
        private readonly IRepositoryWithEvents<UserDetails> _userDetailsRepository;
        private readonly IRepositoryWithEvents<BulkUserUploadTracker> _userImportRepo;
        private readonly IRepositoryWithEvents<BulkListingSourceAddressTracker> _listingSourceAddresstrackerRepo;
        private readonly IRepositoryWithEvents<ListingSourceAddress> _listingSourceAddressRepo;
        private readonly IRepositoryWithEvents<CustomListingSource> _customListingSource;
        private readonly IRepositoryWithEvents<Location> _locationRepo;
        private readonly IRepositoryWithEvents<CustomAddressTracker> _customAddresstrackerRepo;
        private readonly IRepositoryWithEvents<CustomAddressDirectory> _customAddressRepo;
        private readonly IRepositoryWithEvents<ExportProjectTracker> _exportProjectRepo;
        private readonly IRepositoryWithEvents<MasterAssociatedBank> _associatesBankRepo;
        private readonly IRepositoryWithEvents<BulkProjectUploadTracker> _bulkProjectUploadTrackerRepo;
        private readonly IRepositoryWithEvents<CustomMasterAmenity> _projectAmenityRepo;
        private readonly IRepositoryWithEvents<MasterWATemplate> _masterWatemplateRepo;

        private readonly IRepositoryWithEvents<Campaign> _campaignRepo;
        private readonly IRepositoryWithEvents<BulkUploadRefrenceInfoTracker> _referenceIdTrackerRepo;
        private readonly IRepositoryWithEvents<PropertyReferenceInfo> _referenceIdRepo;
        private readonly IRepositoryWithEvents<ReportsConfiguration> _reportsConfigurationRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Source> _sourceRepo;
        private readonly IRepositoryWithEvents<PropertyAssignment> _propertyAssignment;
        private readonly IProjectRepository _projectRepository;
        private readonly IPropertyRepository _propertyRepository;
        private readonly IRepositoryWithEvents<GoogleAdsBulkLeadFetchTracker> _googleadsBulkLeadFetchTrackerRepository;
        private readonly GoogleAdsSettings _googleAdsSettings;
        private readonly IRepositoryWithEvents<CustomFormFields> _customFormRepo;
        private readonly IRepositoryWithEvents<CustomFieldValue> _customFormValueRepo;
        private readonly IRepositoryWithEvents<PropertyAmenity> _amenityRepo;
        private readonly S3Settings _s3Settings;

        public FunctionEntryPoint(
            IBlobStorageService blobStorageService,
            INotificationSenderService notificationSenderService,
            //IRepositoryWithEvents<TempProjects> tempProjectsRepo,
            IRepositoryWithEvents<Property> propertyRepo,
            IRepositoryWithEvents<MasterPropertyType> propertyTypeRepo,
            //IRepositoryWithEvents<MasterLeadStatus> leadStatusRepo,
            IReadRepository<MasterAreaUnit> masterAreaUnitRepo,
            INpgsqlRepository npgsqlRepo,
            IUserService userService,
            ICurrentUser currentUser,
            ITenantIndependentRepository tenantIndependentRepo,
            IBulkLeadUploadTrackerRepository bulkLeadUploadTrackerRepository,
            IRepositoryWithEvents<Lead> LeadRepo,
            IRepositoryWithEvents<LeadHistory> leadHistoryRepo,
            IDapperRepository dapperRepository,
            IRepositoryWithEvents<UserDetails> userDetailsRepo,
            IBulkPropertyUploadTrackerRepository bulkPropertyUploadTrackerRepo,
            IReadRepository<CustomMasterAttribute> masterPropertyAttributeRepo,
            IReadRepository<PropertyDimension> propertyDimensionRepo,
            IRepositoryWithEvents<IntegrationAccountInfo> integrationAccountInfoRepositoryAsync,
            IRepositoryWithEvents<ExportLeadTracker> exportLeadRepo,
            IGraphEmailService graphEmailService,
            IReadRepository<MasterEmailTemplates> masterEmailTemplatesRepo,
            IReadRepository<MasterEmailServiceProvider> masterEmailServiceProviderRepo,
            ILeadRepository leadRepository,
            IReadRepository<ExportTemplate> exportTemplateRepo,
            IReadRepository<GlobalSettings> globalSettingsRepository,
            IRepositoryWithEvents<FacebookAuthResponse> facebookAuthResponseRepo,
            IRepositoryWithEvents<FacebookConnectedPageAccount> facebookConnectedPageAccountRepo,
            IRepositoryWithEvents<FacebookLeadGenForm> facebookLeadGenFormRepo,
            IFacebookService facebookService,
            IJobService hangfireService,
            ITenantIndependentRepository repository,
            IRepositoryWithEvents<IntegrationAccountInfo> integrationAccInfoRepo,
            IRepositoryWithEvents<FacebookAdsInfo> fbAdsRepo,
            ITenantIndependentRepository tenantIndependentRepository,
            IRepositoryWithEvents<ExportReportsTracker> exportReportsTrackerRepo,
            //IRepositoryWithEvents<TempProjects> projectRepo,
            IRepositoryWithEvents<FacebookBulkLeadFetchTracker> fbBulkLeadFetchTrackerRepository,
            IRepositoryWithEvents<IntegrationAssignmentInfo> integrationAssignmentInfoRepo,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<DuplicateLeadFeatureInfo> featureInfo,
            IRepositoryWithEvents<ExportFacebookBulkLeadsTracker> exportFbBulkLeadsTracker,
            IRepositoryWithEvents<ValidatedExcel> excelRepo,
            IRepositoryWithEvents<IntegrationAssignment> integrationAssignmentRepo,
            IRepositoryWithEvents<AssignmentModule> assignmentModuleRepo,
            IRepositoryWithEvents<UserAssignment> userAssignmentRepo,
            IMediator mediator,
            IRepositoryWithEvents<Address> addressRepo,
            IRepositoryWithEvents<CustomFlag> customflags,
            IWebGoogleOAuth2Service googleAuthService,
            IRepositoryWithEvents<GoogleSheetIntegrationData> googelSheetIntegrationRepo,
            IRepositoryWithEvents<SpreadSheet> spreadSheetRepo,
            IRepositoryWithEvents<Sheet> sheetRepo,
            IRepositoryWithEvents<GoogleSheetUploadTracker> googleSheetTrackerRepo,
            IRepositoryWithEvents<ChannelPartner> cpRepository,
            IRepositoryWithEvents<BulkChannelPartnerUploadTracker> bulkChannelPartnerUploadRepo,
            IRepositoryWithEvents<GoogleSpreadSheetTracker> googleSpreadSheetTrackerRepo,
            IRepositoryWithEvents<CustomMasterLeadStatus> customMastereadStatus,
            IRepositoryWithEvents<WhatsAppBulkTemplateTracker> whatsAppBulkTemplateTrackerRepo,
            IWhatsAppSenderService whatsAppSenderService,
            IRepositoryWithEvents<WhatsAppTemplateInfo> whatsAppTemplateInfoRepo,

            IRepositoryWithEvents<ExportAttendanceTracker> exportAttendanceTrackerRepo,
            IReadRepository<AttendanceLog> attendanceRepository,

            IRepositoryWithEvents<BulkProspectUploadTracker> bulkProspectUploadTracker,
            IRepositoryWithEvents<Prospect> prospectRepo,
            IRepositoryWithEvents<CustomProspectStatus> prospectStatusRepo,
            IRepositoryWithEvents<MasterProspectSource> prospectSourceRepo,
            IRepositoryWithEvents<ExportPropertyTracker> exportPropertyRepo,
            IRepositoryWithEvents<ProspectHistory> prospectHistoryRepo,
            IRepositoryWithEvents<ExportProspectTracker> exportProspectTrackerRepo,
            IRepositoryWithEvents<LeadMigrateTracker> leadMigrateTrackerRepo,
            IRepositoryWithEvents<Flag> flagRepo,
            IRepositoryWithEvents<CustomFilter> customFilterRepo,
            IRepositoryWithEvents<Project> newProjectRepo,
            IProspectRepository prospectRepository,
            IRepositoryWithEvents<MasterLeadSource> masterLeadSource,
            IRepositoryWithEvents<Agency> agencyRepo,
            IRepositoryWithEvents<ExportUserTracker> exportUserRepo,
            IRepositoryWithEvents<BulkUnitUploadTracker> bulkUnitUploadRepo,
            IRepositoryWithEvents<UnitType> unitRepo,
            IRepositoryWithEvents<MasterProjectType> projectTypeRepo,
            IReadRepository<CustomMasterAttribute> masterProjectUnitAttributeRepo,
            IRepositoryWithEvents<DataMigrateTracker> dataMigrateTrackerRepo,
            IRepositoryWithEvents<UserView> userViewRepo,
            IRepositoryWithEvents<UserDeletedTracker> userDeleteRepo,
            IRepositoryWithEvents<UserDetails> userRepository,
            IRepositoryWithEvents<Project> projectRepo,
            IRepositoryWithEvents<UserAssignment> assignmentRepo,
            IRepositoryWithEvents<DeletedUser> deleteUsers,
            IRepositoryWithEvents<Lrb.Domain.Entities.Attendance.AttendenceSettings> attendenceRepo,
            IRepositoryWithEvents<CustomEmailInfo> customemailRepo,
            IRepositoryWithEvents<LeadsAssignRotationInfo> leadrotationRepo,
            IRepositoryWithEvents<Team> teamRepo,
            Serilog.ILogger logger,
            IRepositoryWithEvents<BulkMarketingAgencyUploadTracker> bulkMarketingUploadRepo,
            IRepositoryWithEvents<ExportMarketingTracker> exportMarketingTrackerRepo,
            IRepositoryWithEvents<WATemplate> watemplateRepo,
            ITemplateNotificationService templateNotificationService,
            IRepositoryWithEvents<Department> departmentRepo,
            IRepositoryWithEvents<Designation> designationRepo,
            IRepositoryWithEvents<UserDetails> userDetailsRepository,
            IRepositoryWithEvents<BulkUserUploadTracker> userImportRepo,
            IRepositoryWithEvents<BulkListingSourceAddressTracker> listingSourceAddresstrackerRepo,
            IRepositoryWithEvents<ListingSourceAddress> listingSourceAddressRepo,
            IRepositoryWithEvents<CustomListingSource> customListingSource,
            IReadRepository<MasterPropertyType> masterPropertyTypeRepo,
            IRepositoryWithEvents<Location> locationRepo,
            IRepositoryWithEvents<CustomAddressTracker> customAddresstrackerRepo,
            IRepositoryWithEvents<CustomAddressDirectory> customAddressRepo,
            IRepositoryWithEvents<ExportProjectTracker> exportProjectRepo,
            IRepositoryWithEvents<MasterAssociatedBank> associatesBankRepo,
            IRepositoryWithEvents<BulkProjectUploadTracker> bulkProjectUploadTrackerRepo,
            IRepositoryWithEvents<CustomMasterAmenity> projectAmenityRepo,
            IReadRepository<CustomMasterAmenity> masterPropertyAmenityRepo,
            IRepositoryWithEvents<Campaign> campaignRepo,
            IRepositoryWithEvents<BulkUploadRefrenceInfoTracker> referenceIdTrackerRepo,
            IRepositoryWithEvents<PropertyReferenceInfo> referenceIdRepo,
            IRepositoryWithEvents<ReportsConfiguration> reportsConfigurationRepo,
            IRepositoryWithEvents<MasterWATemplate> masterWatemplateRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.Source> sourceRepo,
            IRepositoryWithEvents<PropertyAssignment> propertyAssignment,
            IGoogleAdsService googleAdsService,
            IRepositoryWithEvents<GoogleAdsAuthResponse> googleAdsAuthResponseRepo,
            IRepositoryWithEvents<GoogleAdsBulkLeadFetchTracker> googleadsBulkLeadFetchTrackerRepository,
            IRepositoryWithEvents<GoogleAdsInfo> googleAdsRepo, IRepositoryWithEvents<GoogleCampaign> googleCampaignsRepo,
            IProjectRepository projectRepository,
            IPropertyRepository propertyRepository,
            IOptions<GoogleAdsSettings> googleAdsSettings,
            IRepositoryWithEvents<CustomFormFields> customFormRepo,
            IRepositoryWithEvents<CustomFieldValue> customFormValueRepo,
            IRepositoryWithEvents<PropertyAmenity> amenityRepo,
            IOptions<S3Settings> s3Settings)
            : base(facebookAuthResponseRepo, facebookConnectedPageAccountRepo, facebookLeadGenFormRepo, facebookService,
                  hangfireService, repository, currentUser, integrationAccInfoRepo, fbAdsRepo, leadRepositoryAsync, googleAdsService, googleAdsAuthResponseRepo, googleAdsRepo, googleCampaignsRepo)
        {
            _blobStorageService = blobStorageService;
            _notificationSenderService = notificationSenderService;
            //_tempProjectsRepo = tempProjectsRepo;
            _propertyRepo = propertyRepo;
            _propertyTypeRepo = propertyTypeRepo;
            //_leadStatusRepo = leadStatusRepo;
            _masterAreaUnitRepo = masterAreaUnitRepo;
            _npgsqlRepo = npgsqlRepo;
            _userService = userService;
            _currentUser = currentUser;
            _tenantIndependentRepo = tenantIndependentRepo;
            _bulkLeadUploadTrackerRepository = bulkLeadUploadTrackerRepository;
            _leadRepo = LeadRepo;
            _leadHistoryRepo = leadHistoryRepo;
            _dapperRepository = dapperRepository;
            _userDetailsRepo = userDetailsRepo;
            _bulkPropertyUploadTrackerRepo = bulkPropertyUploadTrackerRepo;
            _masterPropertyAttributeRepo = masterPropertyAttributeRepo;
            _propertyDimensionRepo = propertyDimensionRepo;
            _integrationAccountInfoRepositoryAsync = integrationAccountInfoRepositoryAsync;
            _exportLeadRepo = exportLeadRepo;
            _graphEmailService = graphEmailService;
            _masterEmailTemplatesRepo = masterEmailTemplatesRepo;
            _masterEmailServiceProviderRepo = masterEmailServiceProviderRepo;
            _leadRepository = leadRepository;
            _exportTemplateRepo = exportTemplateRepo;
            _globalSettingsRepository = globalSettingsRepository;
            _exportReportsTrackerRepo = exportReportsTrackerRepo;
            //_projectRepo = projectRepo;
            _fbBulkLeadFetchTrackerRepository = fbBulkLeadFetchTrackerRepository;
            _integrationAssignmentInfoRepo = integrationAssignmentInfoRepo;
            _leadRepository = leadRepository;
            _duplicateFeatureInfo = featureInfo;
            _leadRepositoryAsync = leadRepositoryAsync;
            _excelRepo = excelRepo;
            _exportFbBulkLeadsTrackerRepo = exportFbBulkLeadsTracker;
            _integrationAssignmentRepo = integrationAssignmentRepo;
            _assignmentModuleRepo = assignmentModuleRepo;
            _userAssignmentRepo = userAssignmentRepo;
            _mediator = mediator;
            _addressRepo = addressRepo;
            _googleAuthService = googleAuthService;
            _googelSheetIntegrationRepo = googelSheetIntegrationRepo;
            _spreadSheetRepo = spreadSheetRepo;
            _sheetRepo = sheetRepo;
            _googleSheetTrackerRepo = googleSheetTrackerRepo;
            _cpRepository = cpRepository;
            _bulkChannelPartnerUploadRepo = bulkChannelPartnerUploadRepo;
            _googleSpreadSheetTrackerRepo = googleSpreadSheetTrackerRepo;
            _exportAttendanceTrackerRepo = exportAttendanceTrackerRepo;
            _attendanceRepository = attendanceRepository;
            _customMastereadStatus = customMastereadStatus;
            _whatsAppBulkTemplateTrackerRepo = whatsAppBulkTemplateTrackerRepo;
            _whatsAppSenderService = whatsAppSenderService;
            _whatsAppTemplateInfoRepo = whatsAppTemplateInfoRepo;
            _bulkProspectUploadRepo = bulkProspectUploadTracker;
            _prospectRepo = prospectRepo;
            _prospectStatusRepo = prospectStatusRepo;
            _prospectSourceRepo = prospectSourceRepo;
            _prospectHistoryRepo = prospectHistoryRepo;
            _exportPropertyRepo = exportPropertyRepo;
            _leadMigrateTrackerRepo = leadMigrateTrackerRepo;
            _exportProspectTrackerRepo = exportProspectTrackerRepo;
            _prospectRepository = prospectRepository;
            _customFilterRepo = customFilterRepo;
            _flagRepo = flagRepo;
            _newProjectRepo = newProjectRepo;
            _agencyRepo = agencyRepo;
            _customflags = customflags;
            _masterLeadSource = masterLeadSource;
            _exportUserRepo = exportUserRepo;
            _userViewRepo = userViewRepo;
            _bulkUnitUploadRepo = bulkUnitUploadRepo;
            _unitRepo = unitRepo;
            _projectTypeRepo = projectTypeRepo;
            _masterProjectUnitAttributeRepo = masterProjectUnitAttributeRepo;
            _dataMigrateTrackerRepo = dataMigrateTrackerRepo;
            _userDeletedRepo = userDeleteRepo;
            _userRepository = userRepository;
            _deleteUsers = deleteUsers;
            _projectRepo = projectRepo;
            _assignmentRepo = assignmentRepo;
            _attendenceRepo = attendenceRepo;
            _customemailRepo = customemailRepo;
            _leadrotationRepo = leadrotationRepo;
            _teamRepo = teamRepo;
            _logger = logger;
            _bulkMarketingUploadRepo = bulkMarketingUploadRepo;
            _exportMarketingTrackerRepo = exportMarketingTrackerRepo;
            _watemplateRepo = watemplateRepo;
            _templateNotificationService = templateNotificationService;
            _departmentRepo = departmentRepo;
            _designationRepo = designationRepo;
            _userDetailsRepository = userDetailsRepository;
            _userImportRepo = userImportRepo;
            _listingSourceAddressRepo = listingSourceAddressRepo;
            _listingSourceAddresstrackerRepo = listingSourceAddresstrackerRepo;
            _customListingSource = customListingSource;
            _masterPropertyTypeRepo = masterPropertyTypeRepo;

            _locationRepo = locationRepo;
            _customAddresstrackerRepo = customAddresstrackerRepo;
            _customAddressRepo = customAddressRepo;
            _exportProjectRepo = exportProjectRepo;
            _associatesBankRepo = associatesBankRepo;
            _bulkProjectUploadTrackerRepo = bulkProjectUploadTrackerRepo;
            _projectAmenityRepo = projectAmenityRepo;
            _masterPropertyAmenityRepo = masterPropertyAmenityRepo;
            _referenceIdTrackerRepo = referenceIdTrackerRepo;
            _referenceIdRepo = referenceIdRepo;

            _campaignRepo = campaignRepo;
            _reportsConfigurationRepo = reportsConfigurationRepo;
            _masterWatemplateRepo = masterWatemplateRepo;
            _sourceRepo = sourceRepo;
            _propertyAssignment = propertyAssignment;
            _projectRepository = projectRepository;
            _propertyRepository = propertyRepository;
            _googleAdsSettings = googleAdsSettings.Value;
            _customFormValueRepo = customFormValueRepo;
            _customFormRepo = customFormRepo;
            _amenityRepo = amenityRepo;
            _s3Settings = s3Settings.Value;
        }

        public async Task ImportLeadHandler(InputPayload input)
        {


            CancellationToken cancellationToken = CancellationToken.None;
            //GEt the Tracker by Id 
            BulkLeadUploadTracker leadUploadTracker = await _bulkLeadUploadTrackerRepository.GetByIdAsync(input.TrackerId);
            Console.WriteLine($"handler() -> BulkLeadUploadTracker GetById(): {JsonConvert.SerializeObject(leadUploadTracker)}");
            try
            {
                if (leadUploadTracker != null)
                {
                    try
                    {
                        leadUploadTracker.MappedColumnsData = leadUploadTracker.MappedColumnsData?.ToDictionary(i => i.Key, j => j.Value?.Trim() ?? string.Empty);
                        leadUploadTracker.Status = UploadStatus.Started;
                        leadUploadTracker.LastModifiedBy = input.CurrentUserId;
                        leadUploadTracker.CreatedBy = input.CurrentUserId;
                        leadUploadTracker.SheetName = leadUploadTracker.S3BucketKey.Split('/').Last() + "/" + leadUploadTracker.SheetName;
                        var createType = leadUploadTracker.CreateType;
                        await _bulkLeadUploadTrackerRepository.UpdateAsync(leadUploadTracker);
                        Console.WriteLine($"handler() -> BulkLeadUploadTracker Updated Status: {leadUploadTracker.Status} \n {JsonConvert.SerializeObject(leadUploadTracker)}");
                        #region Fetch all required MasterData and Other data
                        List<Lrb.Domain.Entities.Project> tempProjects = new(await _newProjectRepo.ListAsync(cancellationToken));
                        List<Property> properties = new(await _propertyRepo.ListAsync(cancellationToken));
                        List<MasterPropertyType> propetyTypes = new(await _propertyTypeRepo.ListAsync(cancellationToken));
                        List<MasterAreaUnit> areaUnits = new(await _masterAreaUnitRepo.ListAsync(cancellationToken));
                        List<Lrb.Application.Identity.Users.UserDetailsDto> users = new(await _userService.GetListAsync(cancellationToken));
                        DuplicateLeadFeatureInfo? duplicateFeatureInfo = (await _duplicateFeatureInfo.ListAsync()).FirstOrDefault();
                        List<CustomMasterLeadStatus> leadStatuses = new List<CustomMasterLeadStatus>(await _customMastereadStatus.ListAsync(CancellationToken.None));
                        List<Agency> agencies = new((await _agencyRepo.ListAsync(CancellationToken.None)).Where(i => !string.IsNullOrWhiteSpace(i.Name)));
                        List<ChannelPartner> channelPartner = new((await _cpRepository.ListAsync(cancellationToken)).Where(i => !string.IsNullOrWhiteSpace(i.FirmName)));
                        List<Campaign> campaigns = new((await _campaignRepo.ListAsync(cancellationToken)).Where(i => !string.IsNullOrWhiteSpace(i.Name)));

                        //  List<CustomMasterLeadStatus> leadStatuses = new(await _customMastereadStatus.ListAsync(CancellationToken.None));

                        List<Lead> existingLeads = new();
                        var subSources = (await _dapperRepository.GetAllIntegrationSubSourceAsync<SourceDto>(input.TenantId)).ToList();
                        #endregion

                        #region Convert file to Datatable
                        DataTable dataTable = new();
                        List<InvalidData> invalids = new();
                        int totalRows = 0;
                        UploadType uploadType = UploadType.None;
                        if (!string.IsNullOrWhiteSpace(leadUploadTracker.S3BucketKey))
                        {
                            Stream fileStream = await _blobStorageService.GetObjectAsync(_blobStorageService.BucketName ?? "leadrat-black", leadUploadTracker.S3BucketKey);
                            if (leadUploadTracker.S3BucketKey.Split('.').LastOrDefault() == "csv")
                            {
                                using MemoryStream memoryStream = new();
                                fileStream.CopyTo(memoryStream);
                                dataTable = CSVHelper.CSVToDataTable(memoryStream);
                                uploadType = UploadType.CSV;
                            }
                            else
                            {
                                dataTable = EPPlusExcelHelper.ConvertExcelToDataTable(fileStream, leadUploadTracker.SheetName);
                                uploadType = UploadType.Excel;
                            }
                            totalRows = dataTable.Rows.Count;
                            for (int i = totalRows - 1; i >= 0; i--)
                            {
                                DataRow row = dataTable.Rows[i];
                                if (row.ItemArray.All(i => string.IsNullOrEmpty(i?.ToString())))
                                {
                                    row.Delete();
                                }
                                else if (string.IsNullOrEmpty(row[leadUploadTracker.MappedColumnsData?[DataColumns.Name] ?? string.Empty].ToString()) && string.IsNullOrEmpty(row[leadUploadTracker.MappedColumnsData?[DataColumns.ContactNo] ?? string.Empty].ToString()))
                                {
                                    var notes = string.Join(",", row.ItemArray.Where(i => !string.IsNullOrEmpty(i?.ToString())));
                                    var invalidData = new InvalidData
                                    {
                                        Errors = "Contact number and name are empty.",
                                        Notes = notes
                                    };
                                    if (!invalids.Any(i => i.Notes == invalidData.Notes))
                                    {
                                        invalids.Add(invalidData);
                                    }
                                    row.Delete();
                                }
                            }
                            if (dataTable.Rows.Count <= 0)
                            {
                                throw new Exception("Excel sheet is empty. Please fill some data in the excel sheet template.");
                            }
                            totalRows = dataTable.Rows.Count;
                            Console.WriteLine($"handler() -> Total Rows in the Excel: {dataTable.Rows.Count}");
                        }
                        #endregion

                        #region checking For new Properties or projects
                        List<Property> newProperties = new();
                        List<Lrb.Domain.Entities.Project> newProjects = new();
                        List<Agency> newAgencies = new();
                        List<ChannelPartner> newChannels = new();
                        List<Campaign> newCampaign = new();

                        if (((leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.Property) ?? false) && (leadUploadTracker.MappedColumnsData[DataColumns.Property] != null))
                            || ((leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.Project) ?? false) && (leadUploadTracker.MappedColumnsData[DataColumns.Project] != null))
                            || ((leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.AgencyName) ?? false) && (leadUploadTracker.MappedColumnsData[DataColumns.AgencyName] != null))
                            || ((leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.ChannelPartnerName) ?? false) && (leadUploadTracker.MappedColumnsData[DataColumns.ChannelPartnerName] != null))
                            || ((leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.CampaignName) ?? false) && (leadUploadTracker.MappedColumnsData[DataColumns.CampaignName] != null)))
                        {
                            var existingPropertynames = properties.Where(i => i != null && !string.IsNullOrEmpty(i.Title)).Select(i => i.Title?.ToLower().Trim()).ToList();
                            var existingprojctnames = tempProjects.Where(i => i != null && !string.IsNullOrEmpty(i.Name)).Select(i => i.Name?.ToLower().Trim()).ToList();
                            var existingAgencyNames = agencies.Where(i => i != null && !string.IsNullOrEmpty(i.Name)).Select(i => i.Name?.ToLower().Trim()).ToList();
                            var exstingChannelPartners = channelPartner.Where(i => i != null && !string.IsNullOrEmpty(i.FirmName)).Select(i => i.FirmName?.ToLower().Trim()).ToList();
                            var exstingCampaigns = campaigns.Where(i => i != null && !string.IsNullOrEmpty(i.Name)).Select(i => i.Name?.ToLower().Trim()).ToList();

                            var isPropertyPresent = ((leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.Property) ?? false) && (leadUploadTracker.MappedColumnsData[DataColumns.Property] != null));
                            var isProjectPresent = ((leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.Project) ?? false) && (leadUploadTracker.MappedColumnsData[DataColumns.Project] != null));
                            var isAgencyNamePresent = ((leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.AgencyName) ?? false) && (leadUploadTracker.MappedColumnsData[DataColumns.AgencyName] != null));
                            var isChannelPresent = ((leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.ChannelPartnerName) ?? false) && (leadUploadTracker.MappedColumnsData[DataColumns.ChannelPartnerName] != null));
                            var isCampaignPresent = ((leadUploadTracker.MappedColumnsData?.ContainsKey(DataColumns.CampaignName) ?? false) && (leadUploadTracker.MappedColumnsData[DataColumns.CampaignName] != null));
                            dataTable.AsEnumerable().ToList().ForEach(row =>
                            {
                                if (isPropertyPresent)
                                {
                                    var propertyName = row[leadUploadTracker.MappedColumnsData[DataColumns.Property]]?.ToString();
                                    if (!string.IsNullOrWhiteSpace(propertyName) && !(existingPropertynames?.Contains(propertyName.ToLower().Trim()) ?? false) && !newProperties.Select(i => i.Title).Contains(propertyName))
                                    {
                                        newProperties.Add(new()
                                        {
                                            Title = propertyName.Trim(),
                                        });
                                    }
                                }
                                if (isProjectPresent)
                                {
                                    var projectName = row[leadUploadTracker.MappedColumnsData[DataColumns.Project]]?.ToString();
                                    if (!string.IsNullOrWhiteSpace(projectName) && !(existingprojctnames?.Contains(projectName.ToLower()) ?? false) && !newProjects.Select(i => i.Name).Contains(projectName))
                                    {
                                        newProjects.Add(new()
                                        {
                                            Name = projectName.Trim(),
                                        });
                                    }
                                }
                                if (isAgencyNamePresent)
                                {
                                    var agencyName = row[leadUploadTracker.MappedColumnsData[DataColumns.AgencyName]]?.ToString();
                                    if (!string.IsNullOrWhiteSpace(agencyName) && !(existingAgencyNames?.Contains(agencyName.ToLower()) ?? false) && !newAgencies.Select(i => i.Name).Contains(agencyName))
                                    {
                                        newAgencies.Add(new()
                                        {
                                            Name = agencyName,
                                            CreatedBy = leadUploadTracker.CreatedBy,
                                            LastModifiedBy = leadUploadTracker.LastModifiedBy,
                                        });
                                    }
                                }
                                if (isChannelPresent)
                                {
                                    var cpName = row[leadUploadTracker.MappedColumnsData[DataColumns.ChannelPartnerName]]?.ToString();
                                    if (!string.IsNullOrWhiteSpace(cpName) && !(exstingChannelPartners?.Contains(cpName.ToLower().Trim()) ?? false) && !newChannels.Select(i => i.FirmName).Contains(cpName))
                                    {
                                        newChannels.Add(new()
                                        {
                                            FirmName = cpName.Trim(),
                                            CreatedBy = leadUploadTracker.CreatedBy,
                                            LastModifiedBy = leadUploadTracker.LastModifiedBy,
                                        });
                                    }
                                }
                                if (isCampaignPresent)
                                {
                                    var campaignName = row[leadUploadTracker.MappedColumnsData[DataColumns.CampaignName]]?.ToString();
                                    if (!string.IsNullOrWhiteSpace(campaignName) && !(exstingCampaigns?.Contains(campaignName.ToLower()) ?? false) && !newCampaign.Select(i => i.Name).Contains(campaignName))
                                    {
                                        newCampaign.Add(new()
                                        {
                                            Name = campaignName,
                                            CreatedBy = leadUploadTracker.CreatedBy,
                                            LastModifiedBy = leadUploadTracker.LastModifiedBy,
                                        });
                                    }
                                }

                            });
                        }
                        if (newProperties.Any())
                        {
                            await _propertyRepo.AddRangeAsync(newProperties);
                            properties.AddRange(newProperties);
                        }
                        if (newProjects.Any())
                        {
                            await _newProjectRepo.AddRangeAsync(newProjects);
                            tempProjects.AddRange(newProjects);
                        }
                        if (newAgencies.Any())
                        {
                            await _agencyRepo.AddRangeAsync(newAgencies);
                            agencies.AddRange(newAgencies);
                        }
                        if (newChannels.Any())
                        {
                            await _cpRepository.AddRangeAsync(newChannels);
                            channelPartner.AddRange(newChannels);
                        }
                        if (newCampaign.Any())
                        {
                            await _campaignRepo.AddRangeAsync(newCampaign);
                            campaigns.AddRange(newCampaign);
                        }
                        #endregion

                        List<IntegrationInfoDto> integrationAccounts = new();
                        var unMappedColumns = dataTable.GetUnmappedColumnNames(leadUploadTracker.MappedColumnsData ?? new());
                        var globalSettingInfoList = await _globalSettingsRepository.FirstOrDefaultAsync(new Lrb.Application.GlobalSettings.Web.GetGlobalSettingsSpec(), cancellationToken);
                        List<Lead> leads = dataTable.ConvertToLeads2(leadUploadTracker.MappedColumnsData ?? new(), unMappedColumns, tempProjects.DistinctBy(i => i.Name).ToList(), properties.DistinctBy(i => i.Title).ToList(),
                                                   propetyTypes, areaUnits, leadStatuses, users, subSources, integrationAccounts, agencies, globalSettingInfoList, channelPartner, leadUploadTracker, campaigns, input.JsonData ?? string.Empty);
                        leads = leads.DistinctBy(i => i.ContactNo).ToList();
                        string callingCode = globalSettingInfoList?.Countries?.FirstOrDefault().DefaultCallingCode;
                        foreach (var lead in leads)
                        {
                            if (!string.IsNullOrWhiteSpace(lead.ContactNo))
                            {
                                var contactNo = BulkUploadHelper.ConcatenatePhoneNumber(lead.CountryCode, lead.ContactNo, globalSettingInfoList);
                                if (string.IsNullOrWhiteSpace(contactNo))
                                {
                                    var invalidLead = lead.Adapt<InvalidData>();
                                    invalidLead.Errors = "Invalid ContactNo";
                                    invalids.Add(invalidLead);
                                }
                                else
                                {
                                    lead.ContactNo = contactNo;
                                    if (!string.IsNullOrWhiteSpace(lead.AlternateContactNo))
                                    {
                                        var altcontactno = BulkUploadHelper.ConcatenatePhoneNumber(lead.AltCountryCode, lead.AlternateContactNo, globalSettingInfoList);
                                        lead.AlternateContactNo = altcontactno;
                                    }
                                    if (!string.IsNullOrWhiteSpace(lead.ReferralContactNo))
                                    {
                                        var refferalcontactno = BulkUploadHelper.ConcatenatePhoneNumber(lead.CountryCode, lead.ReferralContactNo, globalSettingInfoList);
                                        lead.ReferralContactNo = refferalcontactno;
                                    }
                                    if (!string.IsNullOrWhiteSpace(lead.ChannelPartnerContactNo))
                                    {
                                        var refferalcontactno = BulkUploadHelper.ConcatenatePhoneNumber(lead.CountryCode, lead.ChannelPartnerContactNo, globalSettingInfoList);
                                        lead.ChannelPartnerContactNo = refferalcontactno;
                                    }
                                }
                            }
                            else
                            {
                                var invalidLead = lead.Adapt<InvalidData>();
                                invalidLead.Errors = "Invalid ContactNo";
                                invalids.Add(invalidLead);
                            }
                        }

                        var distinctLeadCount = leads.Count();
                        if (!(createType == LeadCreateType.CreateDuplicateLead && duplicateFeatureInfo != null && duplicateFeatureInfo.IsFeatureAdded))
                        {
                            var contactNos = leads.SelectMany(i => new[] { i.ContactNo, i.AlternateContactNo })
                        .Where(i => !string.IsNullOrWhiteSpace(i)).ToList();

                            foreach (var contactNo in contactNos)
                            {
                                string mobileNumber = Regex.Replace(contactNo, "[^0-9]", "");
                                string defaultRegion = string.Empty;
                                PhoneNumberUtil phoneUtil = PhoneNumberUtil.GetInstance();
                                try
                                {
                                    if (contactNo.StartsWith("+") && contactNo.Length > 6 && contactNo.Length < 20)
                                    {
                                        PhoneNumber number = phoneUtil.Parse("+" + contactNo, null);
                                        defaultRegion = phoneUtil.GetRegionCodeForNumber(number);
                                        string countryCode = phoneUtil.GetCountryCodeForRegion(defaultRegion).ToString();
                                        countryCode = Regex.Replace(countryCode, "[^0-9]", "");
                                        if (mobileNumber.StartsWith(countryCode))
                                        {
                                            mobileNumber = mobileNumber.Substring(countryCode.Length);
                                        }
                                    }
                                }
                                catch
                                {
                                }
                                var leadsForNumber = await _leadRepo.ListAsync(new CheckDuplicateLeadsSpecsV1(mobileNumber), cancellationToken);
                                if (leadsForNumber != null)
                                {
                                    existingLeads.AddRange(leadsForNumber);
                                }

                            }

                        }
                        Console.WriteLine($"handler() -> existingLeads(Ids, Count): ({JsonConvert.SerializeObject(existingLeads.Select(i => i.Id))}, {existingLeads.Count})");


                        Console.WriteLine($"handler() -> Total Distinct Lead: {distinctLeadCount}");
                        var existingContactNos = existingLeads.SelectMany(i => new[] { i.ContactNo, i.AlternateContactNo })
                        .Where(i => !string.IsNullOrWhiteSpace(i)).ToList(); ;
                     
                        List<Lead> leadsToUpdate = new();
                        List<Lead> existingLeadsToUpdate = new();
                        foreach (Lead lead in leads)
                        {

                            if (existingContactNos.Any(i => !string.IsNullOrWhiteSpace(lead.ContactNo)))
                            {
                                if (createType == LeadCreateType.UpdateExistingLead)
                                {
                                    existingLeadsToUpdate.Add(lead);
                                }

                                else
                                {
                                    var invalidLead = lead.Adapt<InvalidData>();
                                    invalidLead.Errors = "Duplicate Lead";
                                    var duplicateLead = existingLeads.FirstOrDefault(i => (!string.IsNullOrWhiteSpace(lead.ContactNo) && ((i.ContactNo?.Contains(lead.ContactNo) == true) || (i.AlternateContactNo?.Contains(lead.ContactNo) == true))) || (!string.IsNullOrWhiteSpace(lead.AlternateContactNo) && ((i.ContactNo?.Contains(lead.AlternateContactNo) == true) || (i.AlternateContactNo?.Contains(lead.AlternateContactNo) == true))));

                                    if (duplicateLead != null)
                                    {
                                        var user = users.FirstOrDefault(i => i.Id == duplicateLead.AssignTo);
                                        invalidLead.AssignTo = user != null ? user.FirstName + " " + user.LastName : string.Empty;
                                        leadsToUpdate.Add(lead);
                                        invalidLead.Source = duplicateLead.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.LeadSource.ToString() ?? duplicateLead.Enquiries?.FirstOrDefault()?.LeadSource.ToString();
                                        invalidLead.SubSource = duplicateLead.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.SubSource ?? duplicateLead.Enquiries?.FirstOrDefault()?.LeadSource.ToString();
                                        invalidLead.Created = duplicateLead.CreatedOn.Date;
                                        invalids.Add(invalidLead);
                                    }
                                }
                            }
                        };
                        leads.RemoveAll(i => invalids.Select(i => i.ContactNo).Contains(i.ContactNo));
                        leads.RemoveAll(i => existingContactNos.Select(i => i).Contains(i.ContactNo));

                        Parallel.ForEach(leads, lead =>
                        {
                            lead.UploadType = uploadType;
                            lead.UploadTypeName = leadUploadTracker.S3BucketKey;
                        });
                        leads.RemoveAll(i => leadsToUpdate.Select(i => i.ContactNo).Contains(i.ContactNo));
                        leads.RemoveAll(i => invalids.Select(i => i.ContactNo).Contains(i.ContactNo));

                        //update Tracker
                        leadUploadTracker.Status = UploadStatus.InProgress;
                        leadUploadTracker.TotalCount = totalRows;
                        leadUploadTracker.DistinctLeadCount = distinctLeadCount;
                        leadUploadTracker.LastModifiedBy = input.CurrentUserId;
                        leadUploadTracker.CreatedBy = input.CurrentUserId;

                        if (invalids.Any())
                        {
                            leadUploadTracker.DuplicateCount = invalids.Where(i => i.Errors == "Duplicate Lead").Count();
                            leadUploadTracker.InvalidCount = invalids.Where(i => i.Errors == "Invalid ContactNo" || i.Errors == "Invalid Name" || i.Errors == "contact number and name are empty").Count();
                            byte[] bytes = CreateLeadHelper.CreateExcelData(invalids).ToArray();
                            string fileName = input.TenantId + $"InvalidLeads-{DateTime.Now:yyyy_MM_dd-HH_mm_ss}.xlsx";
                            string folder = "Leads";
                            var key = await _blobStorageService.UploadObjectAsync(_blobStorageService.BucketName ?? "leadrat-black", folder, fileName, bytes) ?? string.Empty;
                            var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                            leadUploadTracker.InvalidDataS3BucketKey = key;
                        }
                        if (existingLeadsToUpdate.Any())
                        {
                            leadUploadTracker.LeadsUpdatedCount = existingLeadsToUpdate?.Count() ?? 0;
                        }
                        await _bulkLeadUploadTrackerRepository.UpdateAsync(leadUploadTracker);
                        Console.WriteLine($"handler() -> BulkLeadUploadTracker Updated Status: {leadUploadTracker.Status} \n {JsonConvert.SerializeObject(leadUploadTracker)}");
                        BulkUploadbackgroundDto backgroundDto = new();
                        if (leads.Count > 0)
                        {
                            integrationAccounts.ForEach(i => leads.Any(j => i.LeadId == j.Id));
                            await CreateIntegrationAccountAsync(integrationAccounts, input);
                            int leadsPerchunk = leads.Count > 5000 ? 5000 : leads.Count;
                            var chunks = leads.Chunk(leadsPerchunk).Select(i => new ConcurrentBag<Lead>(i));
                            List<Task> tasks = new();
                            var currentUserId = _currentUser.GetUserId();
                            var tenantInfo = await _tenantIndependentRepo.GetTenantInfoAsync(input.TenantId);

                            if (currentUserId == Guid.Empty)
                            {
                                currentUserId = input.CurrentUserId;
                            }
                            Console.WriteLine($"handler() -> CurrentUserId: {currentUserId}");
                            var chunkIndex = 1;
                            foreach (var chunk in chunks.ToList())
                            {
                                backgroundDto = new BulkUploadbackgroundDto()
                                {
                                    CurrentUserId = currentUserId,
                                    TrackerId = leadUploadTracker.Id,
                                    TenantInfoDto = tenantInfo,
                                    CancellationToken = CancellationToken.None,
                                    Leads = new(chunk),
                                    UserIds = new(leadUploadTracker.UserIds ?? new()),
                                    Users = users.ToList(),
                                    CreateType = createType
                                };
                                await ExecuteDBOperationsAsync(backgroundDto);
                                chunkIndex++;
                            }
                        }
                        if (leadsToUpdate?.Any() ?? false)
                        {
                            Parallel.ForEach(leadsToUpdate, lead =>
                            {
                                lead.UploadType = uploadType;
                                lead.UploadTypeName = leadUploadTracker.S3BucketKey;
                            });
                        }
                        /* int leadsPerchunk = leadsToUpdate.Count > 5000 ? 5000 : leadsToUpdate.Count;
                         var chunks = leadsToUpdate.Chunk(leadsPerchunk).Select(i => new ConcurrentBag<Lead>(i));

                         List<Task> tasks = new();
                         var currentUserId = _currentUser.GetUserId();
                         var tenantInfo = await _tenantIndependentRepo.GetTenantInfoAsync(input.TenantId);

                         if (currentUserId == Guid.Empty)
                         {
                             currentUserId = input.CurrentUserId;
                         }
                         Console.WriteLine($"handler() -> CurrentUserId: {currentUserId}");
                         var chunkIndex = 1;

                         foreach (var chunk in chunks.ToList())
                         {
                             backgroundDto = new BulkUploadbackgroundDto()
                             {
                                 CurrentUserId = currentUserId,
                                 TrackerId = leadUploadTracker.Id,
                                 TenantInfoDto = tenantInfo,
                                 CancellationToken = CancellationToken.None,
                                 Leads = new(chunk),
                                 UserIds = new(leadUploadTracker.UserIds ?? new()),
                                 Users = users.ToList(),
                                 CreateType = createType
                             };
                             if (backgroundDto.Leads.Any())
                             {
                                 await UpdateDuplicateLeadsAsync(backgroundDto, tempProjects, properties);
                                 chunkIndex++;
                             }


                         }
                     }*/
                        if (existingLeadsToUpdate?.Any() ?? false)
                        {
                            Parallel.ForEach(existingLeadsToUpdate, lead =>
                            {
                                lead.UploadType = uploadType;
                                lead.UploadTypeName = leadUploadTracker.S3BucketKey;
                            });
                        }

                        /* int leadsPerchunk = existingLeadsToUpdate.Count > 5000 ? 5000 : existingLeadsToUpdate.Count;
                         var chunks = existingLeadsToUpdate.Chunk(leadsPerchunk).Select(i => new ConcurrentBag<Lead>(i));

                         List<Task> tasks = new();
                         var currentUserId = _currentUser.GetUserId();
                         var tenantInfo = await _tenantIndependentRepo.GetTenantInfoAsync(input.TenantId);

                         if (currentUserId == Guid.Empty)
                         {
                             currentUserId = input.CurrentUserId;
                         }
                         Console.WriteLine($"handler() -> CurrentUserId: {currentUserId}");
                         var chunkIndex = 1;

                         foreach (var chunk in chunks.ToList())
                         {

                             if (backgroundDto.Leads.Any())
                             {
                                 backgroundDto = new BulkUploadbackgroundDto()
                                 {
                                     CurrentUserId = currentUserId,
                                     TrackerId = leadUploadTracker.Id,
                                     TenantInfoDto = tenantInfo,
                                     CancellationToken = CancellationToken.None,
                                     Leads = new(chunk),
                                     UserIds = new(leadUploadTracker.UserIds ?? new()),
                                     Users = users.ToList(),
                                     CreateType = createType
                                 };
                                 if (backgroundDto.Leads.Any())
                                 {
                                     await UpdateDuplicateLeadsAsync(backgroundDto, tempProjects, properties);
                                     chunkIndex++;
                                 }

                             }


                         }
                     }*/
                        leadUploadTracker.Status = UploadStatus.Completed;
                        leadUploadTracker.LastModifiedBy = input.CurrentUserId;
                        leadUploadTracker.CreatedBy = input.CurrentUserId;
                        leadUploadTracker.TotalUploadedCount = leads?.Count ?? 0;

                        await _bulkLeadUploadTrackerRepository.UpdateAsync(leadUploadTracker);
                        if (leads.Any() || (leadsToUpdate?.Any() ?? false) || (existingLeadsToUpdate?.Any() ?? false))
                        {
                            var tracker = await _bulkLeadUploadTrackerRepository.GetByIdAsync(backgroundDto.TrackerId);
                            try
                            {
                                await SendNotificationsAsync(backgroundDto);
                            }
                            catch (Exception e)
                            {
                                Console.WriteLine($"ExecuteDBOperationsAsync() -> Exception(Notification): {JsonConvert.SerializeObject(e)}");
                                if (tracker != null)
                                {
                                    if (tracker.TotalUploadedCount == (tracker.DistinctLeadCount - tracker.InvalidCount - tracker.DuplicateCount))
                                    {
                                        tracker.Status = UploadStatus.Completed;
                                    }
                                    else
                                    {
                                        tracker.Status = UploadStatus.Failed;
                                        tracker.Message = e?.InnerException?.Message ?? e?.Message;
                                    }
                                    tracker.LastModifiedBy = backgroundDto.CurrentUserId;
                                    tracker.CreatedBy = backgroundDto.CurrentUserId;
                                    await _bulkLeadUploadTrackerRepository.UpdateAsync(tracker);
                                    var error = new LrbError()
                                    {
                                        ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                                        ErrorSource = e?.Source,
                                        StackTrace = e?.StackTrace,
                                        InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),

                                        ErrorModule = "CreateBulkLeadleadUploadTrackerUsingEPPlus -> ExecuteDBOperationsAsync() -> SendNotificationsAsync()",
                                    };
                                    await _leadRepositoryAsync.AddErrorAsync(error);
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        leadUploadTracker = await _bulkLeadUploadTrackerRepository.GetByIdAsync(leadUploadTracker.Id);
                        leadUploadTracker.Status = UploadStatus.Failed;
                        leadUploadTracker.Message = ex.Message;
                        leadUploadTracker.LastModifiedBy = input.CurrentUserId;
                        leadUploadTracker.CreatedBy = input.CurrentUserId;
                        await _bulkLeadUploadTrackerRepository.UpdateAsync(leadUploadTracker);
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "CreateBulkLeadleadUploadTrackerUsingEPPlus -> LeadHandler()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                        throw;
                    }
                }
                else
                {
                    Console.WriteLine($"handler() -> tracker not found by the Id : {input.TrackerId}");
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CreateBulkLeadleadUploadTrackerUsingEPPlus -> LeadHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                Console.WriteLine($"handler() -> Exception:  {JsonConvert.SerializeObject(ex, settings: new() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                throw;
            }

            //return new(leadUploadTracker);
        }

        private async Task ExecuteDBOperationsAsync(BulkUploadbackgroundDto dto)
        {
            if (!(dto.Leads?.Any() ?? false))
            {
                return;
            }
            BulkLeadUploadTracker? tracker = new();
            tracker = await _bulkLeadUploadTrackerRepository.GetByIdAsync(dto.TrackerId);
            try
            {
                if (dto.UserIds?.Any() ?? false)
                {
                    var users = await _userDetailsRepo.ListAsync(new GetUsersSpec(dto.UserIds.Select(i => Guid.Parse(i)).ToList() ?? new()));
                    dto.Leads.AssignLead(users.Where(i => i.IsAutomationEnabled).Select(i => i.UserId).ToList(), dto.CurrentUserId);
                }
                if (dto.CreateType == LeadCreateType.CreateDuplicateLead)
                {
                    foreach (var lead in dto.Leads)
                    {
                        var parentLead = await _leadRepo.FirstOrDefaultAsync(new GetRootLeadSpec(lead.ContactNo, lead.AlternateContactNo));
                        if (parentLead != null)
                        {
                            lead.RootId = parentLead.Id;
                            lead.DuplicateLeadVersion = "D" + (parentLead.ChildLeadsCount + 1);
                            lead.ParentLeadId = parentLead.Id;
                            parentLead.ChildLeadsCount += 1;
                            try
                            {
                                await _leadRepo.UpdateAsync(parentLead);
                            }
                            catch (Exception e)
                            {
                                var error = new LrbError()
                                {
                                    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                                    ErrorSource = e?.Source,
                                    StackTrace = e?.StackTrace,
                                    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),

                                    ErrorModule = "CreateBulkLeadleadUploadTrackerUsingEPPlus -> ExecuteDBOperationsAsync() -> SendNotificationsAsync()",
                                };
                                await _leadRepositoryAsync.AddErrorAsync(error);
                            }
                        }
                    }
                }
                var leads = await _leadRepo.AddRangeAsync(dto.Leads);
                var addresses = ExtractAddressFromLeads(leads.ToList());
                await AddBulkLocations(addresses);
                var leadDtos = dto.Leads.Adapt<List<ViewLeadDto>>();
                dto.LeadDtos = leadDtos;
                List<LeadHistory> leadHistories = new();
                leadDtos.ForEach(leadDto =>
                {
                    leadDto.SetUsersInViewLeadDto(dto.Users, currentUserId: dto.CurrentUserId);
                    leadHistories.Add(LeadHistoryHelper.LeadHistoryMapper(leadDto));
                });
                leadHistories = (await _leadHistoryRepo.AddRangeAsync(leadHistories)).ToList();
                if (tracker != null)
                {
                    tracker.TotalUploadedCount += dto.Leads.Count;
                    tracker.LastModifiedBy = dto.CurrentUserId;
                    tracker.CreatedBy = dto.CurrentUserId;
                    await _bulkLeadUploadTrackerRepository.UpdateAsync(tracker);
                }
            }

            catch (Exception e)
            {
                Console.WriteLine($"ExecuteDBOperationsAsync() -> Exception: {JsonConvert.SerializeObject(e)}");
                if (tracker.TotalUploadedCount == (tracker.DistinctLeadCount - tracker.InvalidCount - tracker.DuplicateCount))
                {
                    tracker.Status = UploadStatus.Completed;
                }
                else
                {
                    tracker.Status = UploadStatus.Failed;
                    tracker.Message = e?.InnerException?.Message ?? e?.Message;
                }
                tracker.LastModifiedBy = dto.CurrentUserId;
                tracker.CreatedBy = dto.CurrentUserId;
                await _bulkLeadUploadTrackerRepository.UpdateAsync(tracker);
                var error = new LrbError()
                {
                    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                    ErrorSource = e?.Source,
                    StackTrace = e?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CreateBulkLeadleadUploadTrackerUsingEPPlus -> ExecuteDBOperationsAsync()",
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }

        }
        public async Task UpdateDuplicateLeadsAsync(BulkUploadbackgroundDto dto, List<Lrb.Domain.Entities.Project> projects, List<Property> properties)
        {

            List<Lead> leadsToUpdate = new();
            BulkLeadUploadTracker? tracker = new();
            tracker = await _bulkLeadUploadTrackerRepository.GetByIdAsync(dto.TrackerId);
            try
            {
                if (dto.Leads?.Any() ?? false)
                {
                    foreach (var lead in dto.Leads)
                    {
                        // TODO : need to remove this code once custom status is done
                        var duplicateLeadsToUpdate = await _leadRepo.ListAsync(new GetLeadByContactNoSpec(lead.ContactNo ?? string.Empty));
                        var updatedLeads = MapObjects(lead, duplicateLeadsToUpdate, projects, properties);

                        leadsToUpdate.AddRange(updatedLeads);
                    }
                    try
                    {
                        await _leadRepo.UpdateRangeAsync(leadsToUpdate);
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine($"ExecuteDBOperationsAsync() -> Exception: {JsonConvert.SerializeObject(e)}");
                        if (tracker.TotalUploadedCount == (tracker.DistinctLeadCount - tracker.InvalidCount - tracker.DuplicateCount))
                        {
                            tracker.Status = UploadStatus.Completed;
                        }
                        else
                        {
                            tracker.Status = UploadStatus.Failed;
                            tracker.Message = e?.InnerException?.Message ?? e?.Message;
                        }
                        tracker.LastModifiedBy = dto.CurrentUserId;
                        tracker.CreatedBy = dto.CurrentUserId;
                        await _bulkLeadUploadTrackerRepository.UpdateAsync(tracker);
                        var error = new LrbError()
                        {
                            ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                            ErrorSource = e?.Source,
                            StackTrace = e?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "CreateBulkLeadleadUploadTrackerUsingEPPlus -> ExecuteDBOperationsAsync()",
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                    var leadDtos = leadsToUpdate.Adapt<List<ViewLeadDto>>();
                    dto.LeadDtos = leadDtos;
                    List<LeadHistory> leadHistories = new();
                    leadDtos.ForEach(leadDto =>
                    {
                        leadDto.SetUsersInViewLeadDto(dto.Users);
                        leadHistories.Add(LeadHistoryHelper.LeadHistoryMapper(leadDto));

                    });
                    if (leadHistories.Any())
                    {
                        await UpdateDuplicateLeadsHistoryAsync(leadHistories);
                    }
                    if (tracker != null)
                    {
                        //tracker.TotalUploadedCount += dto.Leads.Count;
                        tracker.LastModifiedBy = dto.CurrentUserId;
                        tracker.CreatedBy = dto.CurrentUserId;
                        await _bulkLeadUploadTrackerRepository.UpdateAsync(tracker);
                    }
                }
            }
            catch (Exception e)
            {
                Console.WriteLine($"ExecuteDBOperationsAsync() -> Exception: {JsonConvert.SerializeObject(e)}");
                if (tracker.TotalUploadedCount == (tracker.DistinctLeadCount - tracker.InvalidCount - tracker.DuplicateCount))
                {
                    tracker.Status = UploadStatus.Completed;
                }
                else
                {
                    tracker.Status = UploadStatus.Failed;
                    tracker.Message = e?.InnerException?.Message ?? e?.Message;
                }
                tracker.LastModifiedBy = dto.CurrentUserId;
                tracker.CreatedBy = dto.CurrentUserId;
                await _bulkLeadUploadTrackerRepository.UpdateAsync(tracker);
            }
        }
        public async Task UpdateDuplicateLeadsHistoryAsync(List<LeadHistory> leadHistories)
        {
            foreach (var leadHistory in leadHistories)
            {
                var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(leadHistory.LeadId, leadHistory.UserId));
                if (existingLeadHistory != null)
                {
                    await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory));
                }
                else
                {
                    await _leadHistoryRepo.AddAsync(leadHistory);
                }
            }

        }
        public async Task CreateIntegrationAccountAsync(List<IntegrationInfoDto> integrationInfoDto, InputPayload input)
        {
            try
            {
                List<IntegrationAccountInfo> newIntegrationAccounts = new();
                if (integrationInfoDto.Any())
                {
                    foreach (var integrationInfo in integrationInfoDto)
                    {
                        IntegrationAccountInfo? integrationAccount = null;
                        IDictionary<string, string> data = null;
                        integrationAccount = CreateIntegrationEntity(integrationInfo, input.CurrentUserId);
                        integrationAccount.ApiKey = ApiKeyHelper.GenerateApiKey(integrationAccount.Id);
                        data = new Dictionary<string, string>(IntegrationTemplateBuilder.CreateIntegrationTemplate(input.TenantId, integrationAccount));
                        string key = string.Empty;
                        byte[] bytes = IntegrationExcelHelper.CreateExcelData(data).ToArray();
                        string fileName = $"{integrationAccount.Id}-{DateTime.Now.ToString("yyyy_MM_dd-HH_mm_ss")}.xlsx";
                        string folder = "Integration";
                        key = await _blobStorageService.UploadObjectAsync(_blobStorageService.BucketName ?? "leadrat-black", folder, fileName, bytes);
                        string fileUrl = await _blobStorageService.GetPreSignedURL(_blobStorageService.BucketName ?? "leadrat-black", key);
                        integrationAccount.FileUrl = key;
                        newIntegrationAccounts.Add(integrationAccount);
                    }
                    await _integrationAccountInfoRepositoryAsync.AddRangeAsync(newIntegrationAccounts);
                }
            }
            catch (Exception e)
            {
                var error = new LrbError()
                {
                    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                    ErrorSource = e?.Source,
                    StackTrace = e?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CreateBulkLeadleadUploadTrackerUsingEPPlus -> CreateIntegrationAccountAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
        }
        private async Task SendNotificationsAsync(BulkUploadbackgroundDto dto)
        {
            Console.WriteLine("BulkLeadUpload -> FunctionEntryPoint -> SendNotificationsAsync() called -> BulkUploadbackgroundDto -> UserIds = " + JsonConvert.SerializeObject(dto.UserIds, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }));
            Console.WriteLine("BulkLeadUpload -> FunctionEntryPoint -> SendNotificationsAsync() called -> BulkUploadbackgroundDto -> CurrentUserId = " + JsonConvert.SerializeObject(dto.CurrentUserId, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }));
            if (dto?.UserIds?.Any() ?? false)
            {
                var userIdsWithNoOfLeadsAssigned = dto.Leads.Where(i => i.AssignTo != Guid.Empty).GroupBy(i => i.AssignTo).ToDictionary(i => i.Key, j => j.Count());
                if (userIdsWithNoOfLeadsAssigned.Count() > 0)
                {
                    foreach (var item in userIdsWithNoOfLeadsAssigned)
                    {
                        try
                        {
                            var userDetails = dto.Users.FirstOrDefault(i => i.Id == item.Key);
                            if (userDetails.Id != dto.CurrentUserId)
                            {
                                var lead = dto.Leads.FirstOrDefault(i => i.AssignTo == item.Key);
                                if (item.Value == 1)
                                {
                                    Console.WriteLine($"BulkLeadUpload -> FunctionEntryPoint -> SendNotificationsAsync() -> Event: {Lrb.Domain.Enums.Event.LeadAssignment}, lead: {JsonConvert.SerializeObject(lead, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}, UserDetails: {JsonConvert.SerializeObject(userDetails, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}, NoOfLeads: {item.Value}");
                                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Lrb.Domain.Enums.Event.LeadAssignment, lead, userDetails.Id, userDetails.FirstName + " " + userDetails.LastName, null, item.Value, dto.CurrentUserId);
                                }
                                else
                                {
                                    Console.WriteLine($"BulkLeadUpload -> FunctionEntryPoint -> SendNotificationsAsync() -> Event: {Lrb.Domain.Enums.Event.MultipleLeadAssignment}, lead: {JsonConvert.SerializeObject(lead, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}, UserDetails: {JsonConvert.SerializeObject(userDetails, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}, NoOfLeads: {item.Value}");
                                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Lrb.Domain.Enums.Event.MultipleLeadAssignment, lead, userDetails.Id, userDetails.FirstName + " " + userDetails.LastName, null, item.Value, dto.CurrentUserId);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            var error = new LrbError()
                            {
                                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                ErrorSource = ex?.Source,
                                StackTrace = ex?.StackTrace,
                                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                ErrorModule = "CreateBulkLeadleadUploadTrackerUsingEPPlus -> SendNotificationsAsync()"
                            };
                            await _leadRepositoryAsync.AddErrorAsync(error);
                        }
                    }
                }
            }
            else
            {
                List<Guid>? adminIds = await _npgsqlRepo.GetAdminIdsAsync(dto?.TenantInfoDto?.Id ?? string.Empty);
                var leadForNotification = dto?.Leads?.FirstOrDefault();
                if (adminIds != null && adminIds.Any())
                {
                    //foreach (var adminId in adminIds)
                    //{
                    //    var adminDetails = await _userService.GetAsync(adminId.ToString(), dto?.CancellationToken ?? new());
                    //    if (adminDetails != null)
                    //    {
                    //        List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Lrb.Domain.Enums.Event.UnAssignedLeadUpdate, leadForNotification, adminId, adminDetails.FirstName + " " + adminDetails.LastName, topics: new List<string> { leadForNotification.CreatedBy.ToString(), leadForNotification.LastModifiedBy.ToString() }, dto.Leads.Count());
                    //    }
                    //}
                    var response = await _notificationSenderService.ScheduleNotificationsAsync(Lrb.Domain.Enums.Event.UnAssignedLeadUpdate, leadForNotification, Guid.Empty, null, topics: new List<string> { leadForNotification?.CreatedBy.ToString() ?? string.Empty, leadForNotification?.LastModifiedBy.ToString() ?? string.Empty }, dto?.Leads?.Count(), dto?.CurrentUserId, adminIds);
                }
            }
        }
        private IntegrationAccountInfo CreateIntegrationEntity(IntegrationInfoDto command, Guid userId)
        {
            return new IntegrationAccountInfo()
            {
                Id = Guid.NewGuid(),
                AccountName = command.AccountName,
                LeadSource = command.Source,
                LicenseId = Guid.NewGuid(),
                JsonTemplate = IntegrationTemplateBuilder.GetRequestBodyJsonFromFile(command.Source),
                CreatedBy = userId,
            };
        }

        private List<Lead> MapObjects(Lead src, List<Lead> destLeads, List<Lrb.Domain.Entities.Project> projects, List<Property> properties)
        {
            foreach (var dest in destLeads)
            {
                if (!dest.Enquiries.Any())
                {
                    dest.Enquiries = new List<LeadEnquiry>();
                }
                try
                {
                    if (src.Projects?.Any() ?? false)
                    {
                        dest.Projects = new List<Lrb.Domain.Entities.Project>() { projects.FirstOrDefault(i => i.Name == src.Projects?.FirstOrDefault()?.Name) ?? new() };
                    }
                    if (src.Properties?.Any() ?? false)
                    {
                        dest.Properties = new List<Property>() { properties.FirstOrDefault(i => i.Title == src.Properties?.FirstOrDefault()?.Title) ?? new() };
                    }


                    dest.Name = src.Name;
                    dest.ContactNo = src.ContactNo;
                    dest.Email = string.IsNullOrEmpty(src.Email) ? dest.Email : src.Email;
                    dest.Notes = string.IsNullOrEmpty(src.Notes) ? dest.Notes : src.Notes;
                    dest.ConfidentialNotes = string.IsNullOrEmpty(src.ConfidentialNotes) ? dest.ConfidentialNotes : src.ConfidentialNotes;
                    dest.AlternateContactNo = string.IsNullOrEmpty(src.AlternateContactNo) ? dest.AlternateContactNo : src.AlternateContactNo;
                    dest.AssignTo = (src.AssignTo != default && src.AssignTo != null) ? src.AssignTo : dest.AssignTo;
                    dest.AssignedFrom = (src.AssignedFrom != default && src.AssignedFrom != null) ? src.AssignedFrom : dest.AssignedFrom;
                    dest.AgencyName = string.IsNullOrEmpty(src.AgencyName) ? dest.AgencyName : src.AgencyName;
                    dest.ReferralContactNo = string.IsNullOrEmpty(src.ReferralContactNo) ? dest.ReferralContactNo : src.ReferralContactNo;
                    dest.ReferralName = string.IsNullOrEmpty(src.ReferralName) ? dest.ReferralName : src.ReferralName;
                    dest.Email = string.IsNullOrEmpty(src.Email) ? dest.Email : src.Email;
                    dest.CreatedBy = src.CreatedBy;
                    dest.LastModifiedBy = src.LastModifiedBy;
                    dest.ReferralEmail = string.IsNullOrEmpty(src.ReferralEmail) ? dest.ReferralEmail : src.ReferralEmail;
                    dest.Enquiries.FirstOrDefault().NoOfBHKs = src.Enquiries?.FirstOrDefault()?.NoOfBHKs != default ? src.Enquiries.FirstOrDefault().NoOfBHKs : dest.Enquiries[0].NoOfBHKs;
                    dest.Enquiries.FirstOrDefault().BHKs = src.Enquiries?.FirstOrDefault()?.BHKs != default ? src.Enquiries?.FirstOrDefault()?.BHKs : dest.Enquiries[0].BHKs;
                    dest.Enquiries.FirstOrDefault().EnquiredFor = src.Enquiries?.FirstOrDefault()?.EnquiredFor != default ? src.Enquiries.FirstOrDefault().EnquiredFor : dest.Enquiries[0].EnquiredFor;
                    dest.Enquiries.FirstOrDefault().EnquiryTypes = src.Enquiries?.FirstOrDefault()?.EnquiryTypes != default ? src.Enquiries?.FirstOrDefault()?.EnquiryTypes : dest.Enquiries[0].EnquiryTypes;
                    dest.Enquiries.FirstOrDefault().SaleType = dest.Enquiries[0].SaleType;
                    dest.Enquiries.FirstOrDefault().SubSource = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.SubSource) ? dest.Enquiries[0].SubSource : src.Enquiries?.FirstOrDefault()?.SubSource;
                    dest.Enquiries.FirstOrDefault().LeadSource = src.Enquiries?.FirstOrDefault()?.LeadSource != default ? src.Enquiries.FirstOrDefault().LeadSource : dest.Enquiries[0].LeadSource;
                    dest.Enquiries.FirstOrDefault().LowerBudget = (src.Enquiries?.FirstOrDefault()?.LowerBudget != default && src.Enquiries?.FirstOrDefault()?.LowerBudget != null) ? src.Enquiries?.FirstOrDefault()?.LowerBudget : dest.Enquiries[0].LowerBudget;
                    dest.Enquiries.FirstOrDefault().UpperBudget = (src.Enquiries?.FirstOrDefault()?.UpperBudget != default && src.Enquiries?.FirstOrDefault()?.UpperBudget != null) ? src.Enquiries?.FirstOrDefault()?.UpperBudget : dest.Enquiries[0].UpperBudget;
                    dest.Enquiries.FirstOrDefault().BHKType = src.Enquiries?.FirstOrDefault()?.BHKType != default ? src.Enquiries.FirstOrDefault().BHKType : dest.Enquiries[0].BHKType;
                    dest.Enquiries.FirstOrDefault().BHKTypes = src.Enquiries?.FirstOrDefault()?.BHKTypes != default ? src.Enquiries?.FirstOrDefault()?.BHKTypes : dest.Enquiries[0].BHKTypes;
                    dest.Enquiries.FirstOrDefault().ClusterName = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.ClusterName) ? dest.Enquiries[0].ClusterName : src.Enquiries?.FirstOrDefault()?.ClusterName;
                    try
                    {
                        dest.Enquiries.FirstOrDefault().Address.City = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Address?.City) ? dest.Enquiries[0]?.Address?.City : src.Enquiries?.FirstOrDefault()?.Address?.City;
                        dest.Enquiries.FirstOrDefault().Address.State = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Address?.State) ? dest.Enquiries[0]?.Address?.State : src.Enquiries?.FirstOrDefault()?.Address?.State;
                        dest.Enquiries.FirstOrDefault().Address.Country = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Address?.Country) ? dest.Enquiries[0]?.Address?.Country : src.Enquiries?.FirstOrDefault()?.Address?.Country;
                    }
                    catch
                    {

                    }
                    dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().Locality = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.Locality) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.Locality : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.Locality;
                    dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().City = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.City) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.City : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.City;
                    dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().State = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.State) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.State : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.State;
                    dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().Country = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.Country) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.Country : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.Country;
                    dest.Enquiries.FirstOrDefault().PropertyType = src.Enquiries?.FirstOrDefault()?.PropertyType != null ? src.Enquiries?.FirstOrDefault()?.PropertyType : dest.Enquiries[0].PropertyType;
                    dest.ChannelPartnerName = string.IsNullOrEmpty(src.ChannelPartnerName) ? dest.ChannelPartnerName : src.ChannelPartnerName;
                    dest.ChannelPartnerExecutiveName = string.IsNullOrEmpty(src.ChannelPartnerExecutiveName) ? dest.ChannelPartnerExecutiveName : src.ChannelPartnerExecutiveName;
                    dest.ChannelPartnerContactNo = string.IsNullOrEmpty(src.ChannelPartnerContactNo) ? dest.ChannelPartnerContactNo : src.ChannelPartnerContactNo;
                    dest.UploadType = src?.UploadType ?? dest.UploadType;
                    dest.UploadTypeName = string.IsNullOrEmpty(src.UploadTypeName) ? dest.UploadTypeName : src.UploadTypeName;
                    dest.Nationality = string.IsNullOrEmpty(src.Nationality) ? dest.Nationality : src.Nationality;
                    dest.Enquiries.FirstOrDefault().PropertyTypes = src.Enquiries?.FirstOrDefault()?.PropertyTypes != null ? src.Enquiries?.FirstOrDefault()?.PropertyTypes : dest.Enquiries[0].PropertyTypes;
                    dest.Enquiries.FirstOrDefault().Purpose = src.Enquiries?.FirstOrDefault()?.Purpose != default ? src.Enquiries.FirstOrDefault().Purpose : dest.Enquiries[0].Purpose;
                    dest.Enquiries.FirstOrDefault().OfferType = src.Enquiries?.FirstOrDefault()?.OfferType != default ? src.Enquiries.FirstOrDefault().OfferType : dest.Enquiries[0].OfferType;

                }
                catch
                {

                }
            }
            return destLeads;
        }
        private async Task AddBulkLocations(List<Address> addresses)
        {
            foreach (var address in addresses)
            {
                if (address != null)
                {
                    var locationRequest = address.MapToLocationRequest();
                    if (locationRequest != null)
                    {
                        Response<Guid> res = await _mediator.Send(locationRequest.Adapt<AddLocationRequest>());
                        if (res.Data != Guid.Empty)
                        {
                            address.LocationId = res.Data;
                            await _addressRepo.UpdateAsync(address);
                        }
                    }
                }
            }
        }
        private List<Address> ExtractAddressFromLeads(List<Lead> leads)
        {
            var result = new List<Address>();
            foreach (var lead in leads)
            {
                if (lead != null && lead.Enquiries != null && lead.Enquiries.Count > 0 && lead.Enquiries[0].Addresses != null)
                {
                    result.AddRange(lead.Enquiries[0].Addresses);
                }
            }
            return result;
        }
        public async Task<bool> ProcessBulkOperationsAsync(InputPayload payload, ReportsConfigurationDto? reportSchedule = null)
        {
            try
            {
                switch (payload.Type.ToLower())
                {
                    case "leadimport":
                    case "lead":
                        await ImportLeadHandlerV2(payload);
                        break;
                    case "leadmigrate":
                        await MigrateLeadHandlerV2(payload);
                        break;
                    case "property":
                        await PropertyHandler(payload);
                        break;
                    case "exportleads":
                        await ExportLeadsHandler(payload);
                        break;
                    case "meetingandvisitreport":
                        await ExportLeadMeetingAndVisitReportByUserHandler(payload, reportSchedule);
                        break;
                    case "statusreportbyproject":
                        await ExportLeadStatusReportByProjectHandler(payload, reportSchedule);
                        break;
                    case "statusreportbysource":
                        await ExportLeadStatusReportBySourceHandler(payload, reportSchedule);
                        break;
                    case "statusreportbyuser":
                        await ExportLeadStatusReportByUserHandler(payload, reportSchedule);
                        break;
                    case "statusreportbyagency":
                        await ExportLeadStatusReportByAgencyHandler(payload, reportSchedule);
                        break;
                    case "statusreportbysubsource":
                        await ExportLeadStatusReportBySubSourceHandler(payload, reportSchedule);
                        break;
                    case "exportleadsbynewfilters":
                        await ExportLeadsByNewFiltersHandler(payload);
                        break;
                    case "facebooksync":
                        await FacebookSyncHandler(payload);
                        break;
                    case "fbbulkleadfetch":
                        await FacebookBulkLeadsFetchHandler(payload);
                        break;
                    case "validateexcel":
                        await ValidateExcelHandler(payload);
                        break;
                    case "exportfbbulkleads":
                        await ExportFacebookBulkLeadsHandler(payload);
                        break;
                    case "useractivityreport":
                    case "leadanddatauseractivityreport":
                        await ExportUserActivityReportHandler(payload, reportSchedule);
                        break;
                    case "googlesheet":
                        await GoogleSheetHandler(payload);
                        break;
                    case "channelpartner":
                        await ChannelPartnerHandler(payload);
                        break;
                    case "substatusreportbyuser":
                        await ExportLeadSubStatusReportByUserHandler(payload, reportSchedule);
                        break;
                    case "substatusreportbysubsource":
                        await ExportLeadSubStatusReportBySubSourceHandler(payload, reportSchedule);
                        break;
                    case "sendwhatsapptemplateinbulk":
                        await SendWhatsAppBulkTemplateHandler(payload);
                        break;
                    case "fblogin":
                        await FacebookLoginHandler(payload);
                        break;
                    case "prospect":
                        await ProspectHandlerV2(payload);
                        break;
                    case "projectreportbysubstatus":
                        await ExportLeadProjectReportBySubStatusHandler(payload, reportSchedule);
                        break;
                    case "exportattendance":
                        await ExportAttendanceHandler(payload);
                        break;
                    case "exportproperties":
                        await ExportPropertiesHandler(payload);
                        break;
                    case "exportprospects":
                        await ExportProspectsHandler(payload);
                        break;
                    case "calllogreportbyuser":
                        await ExportCallLogReportsHandler(payload, reportSchedule);
                        break;
                    case "datastatusreportbyuser":
                        await ExportDataStatusReportByUserHandler(payload, reportSchedule);
                        break;
                    case "datasubsourcereportbystatus":
                        await ExportDataStatusReportBySubSourceHandler(payload, reportSchedule);
                        break;
                    case "datasourcereportbystatus":
                        await ExportDataStatusReportBySourceHandler(payload, reportSchedule);
                        break;
                    case "dataprojectreportbystatus":
                        await ExportDataStatusReportByProjectHandler(payload, reportSchedule);
                        break;
                    case "exportdatacalllogreportbyuser":
                        await ExportDataCallLogReportsHandler(payload, reportSchedule);
                        break;
                    case "recieveddatebysource":
                        await ExportLeadDatewiseSourceCountHandler(payload, reportSchedule);
                        break;
                    case "userdataactivityreport":
                        await ExportDataUserActivityReportHandler(payload, reportSchedule);
                        break;
                    #region Custom Filter 
                    case "exportleadsbycustomfilters":
                        await ExportLeadsByCustomFiltersHandler(payload);
                        break;
                    case "lead_statusreportbyagency":
                        await ExportAgencyReportByLeadStatusHandler(payload, reportSchedule);
                        break;
                    case "lead_statusreportbyproject":
                        await ExportProjectReportByLeadStatusHandler(payload, reportSchedule);
                        break;
                    case "lead_statusreportbysource":
                        await ExportSourceReportByLeadStatusHandler(payload, reportSchedule);
                        break;
                    case "lead_statusreportbysubsource":
                        await ExportSubsourceReportByLeadStatusHandler(payload, reportSchedule);
                        break;
                    case "lead_statusreportbyuser":
                        await ExportUserReportByLeadStatusHandler(payload, reportSchedule);
                        break;
                    case "exportprospectsbycustomfilters":
                        await ExportProspectsByCustomFiltersHandler(payload);
                        break;
                    #endregion
                    case "uservssource":
                        await ExportUserVsSourceReportHandler(payload, reportSchedule);
                        break;
                    case "uservssubsource":
                        await ExportUserVsSubSourceReportHandler(payload, reportSchedule);
                        break;
                    case "exportusers":
                        await ExportUserReportHandler(payload);
                        break;
                    case "unit":
                        await UnitHandler(payload);
                        break;
                    case "datamigrate":
                        await MigrateDataHandlerV2(payload);
                        break;
                    case "userdelete":
                        await UserDeleteHandler(payload);
                        break;
                    case "marketingagency":
                        await MarketingAgencyHandler(payload);
                        break;
                    case "exportmarketingagency":
                        await ExportMarketingAgencyHandler(payload);
                        break;
                    case "exportmarketingchannelpartner":
                        await ExportMarketingChannelPartnerHandler(payload);
                        break;
                    case "marketingchannelpartner":
                        await MarketingChannelPartnerHandler(payload);
                        break;
                    case "userimport":
                        await BulkUserImportHandler(payload);
                        break;

                    case "exportteam":
                        await ExportTeamReportHandler(payload);
                        break;
                    case "listingaddress":
                        await ListingSourceAddressHandler(payload);
                        break;
                    case "exportpropertieslisting":
                        await ExportPropertiesListingHandler(payload);
                        break;
                    case "customaddress":
                        await ImportCustomAddressFromExcelHandler(payload);
                        break;
                    case "exportprojects":
                        await ExportProjectsHandler(payload);
                        break;
                    case "project":
                        await ProjectHandler(payload);
                        break;
                    case "marketingcamapign":
                        await MarketingCampaignHandler(payload);
                        break;
                    case "exportmarketingcampaign":
                        await ExportMarketingCampaignHandler(payload);
                        break;
                    case "refrenceid":
                        await BulkImportReferenceIdHandler(payload);
                        break;
                    case "googleadslogin":
                        await GoogleAdsLoginHandler(payload);
                        break;
                    case "googleadsbulkleadfetch":
                        await GoogleAdsBulkLeadsFetchHandler(payload);
                        break;
                    case "revenueuservssubsource":
                        await ExportRevenueUserVsSourceReportHandler(payload, reportSchedule);
                        break;
                    case "revenueuservssource":
                        await ExportRevenueUserReportHandler(payload, reportSchedule);
                        break;
                    case "dataagencyreportbystatus": 
                        await ExportDataStatusReportByAgencyHandler(payload, reportSchedule);
                        break;
                    case "datachannelpartnerreportbystatus":
                        await ExportDataStatusReportByChannelPartnerHandler(payload);
                        break;
                    case "datacampaignreportbystatus":
                        await ExportDataStatusReportByCampaignHandler(payload);
                        break;
                    case "leadchannelpartnerreportbysubstatus":
                        await ExportLeadChannelPartnerReportBySubStatusHandler(payload);
                        break;
                    case "leadcampaignreportbysubstatus":
                        await ExportLeadCampaignReportBySubStatusHandler(payload);
                        break;
                }

                return true;
            }
            catch (Exception e)
            {
                throw;
            }
        }


        private async Task V2AddBulkLocations(List<Address> addresses)
        {
            foreach (var address in addresses)
            {
                if (address != null)
                {
                    var locationRequest = address.MapToLocationRequest();
                    if (locationRequest != null)
                    {
                        Response<Guid> res = await _mediator.Send(locationRequest.Adapt<AddLocationRequest>());
                        if (res.Data != Guid.Empty)
                        {
                            address.LocationId = res.Data;
                        }
                    }
                }
            }
            var newAddress = addresses.Where(i => i.LocationId != Guid.Empty).ToList();
            if (newAddress?.Any() ?? false)
            {
                await _addressRepo.UpdateRangeAsync(newAddress);
            }
        }
        private List<Address> V2ExtractAddressFromLeads(List<Lead> leads)
        {
            return leads
                .SelectMany(lead => lead.Enquiries)
                .Where(enquiry => enquiry.Addresses?.Any() ?? false)
                .SelectMany(enquiry => enquiry.Addresses ?? new()).ToList();
        }


        //public async Lead UpdateParentLead(Lead lead)
        //{
        //    var parentLead = await _leadRepo.FirstOrDefaultAsync(new GetParentLeadSpec(lead.ContactNo ?? string.Empty));
        //    if (parentLead != null)
        //    {
        //        lead.RootId = parentLead.Id;
        //        lead.DuplicateLeadVersion = "D" + (parentLead.ChildLeadsCount + 1);
        //        lead.ParentLeadId = parentLead.Id;
        //        parentLead.ChildLeadsCount += 1;
        //        try
        //        {
        //            await _leadRepo.UpdateAsync(parentLead);
        //        }
        //        catch (Exception ex) { }

        //    }
        //}
    }
}
