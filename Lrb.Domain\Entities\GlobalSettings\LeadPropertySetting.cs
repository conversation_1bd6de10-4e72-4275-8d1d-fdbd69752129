﻿namespace Lrb.Domain.Entities
{
    public class LeadPropertySetting
    {
        public bool IsPropertyMandatoryEnabled { get; set; }
        public bool IsPropertyMandatoryOnSiteVisitDone { get; set; }
        public bool IsPropertyMandatoryOnMeetingDone { get; set; }
        public bool IsPropertyMandatoryOnBooking { get; set; }
        public bool IsWaterMarksOnImagesEnabled { get; set; }
        public string? WaterMarkUrl { get; set; }
        public WaterMarkPosition WaterMarkPosition { get; set; }
        public string? Opacity { get; set; }
        public bool? Background { get; set; }
        public string? ImageSize { get; set; }
        public string? ImageName { get; set; }
        public bool? AminitySettings { get; set; }
        public bool? AllowDuplicate { get; set; }
        public bool? IsCloneEnabled { get; set; }
        public bool? AllowManualDuplicate { get; set; }

    }
}
