﻿using System.ComponentModel.DataAnnotations.Schema;

namespace Lrb.Domain.Entities
{
    public class DuplicateLeadFeatureInfo : AuditableEntity, IAggregateRoot
    {
        public bool AllowAllDuplicates { get; set; }
        public bool IsFeatureAdded { get; set; }
        public bool IsSourceBased { get; set; }
        public bool IsSubSourceBased { get; set; }
        public bool IsProjectBased { get; set; }
        public bool IsLocationBased { get; set; }
        [Column(TypeName = "jsonb")]
        public List<Guid?>? StutusIds { get; set; }
        public List<int>? Sources { get; set; }
    }
}
