﻿using Lrb.Application.Reports.Web;
using Lrb.Application.Reports.Web.Data.Dtos.User;
using Lrb.Application.Reports.Web.Data.Requests.User;

namespace Lrb.WebApi.Host.Controllers.V2
{

    [Authorize]
    [Route("api/v2/[controller]")]
    [ApiVersionNeutral]
    public class DataReportController : VersionedApiController
    {

        private readonly Serilog.ILogger _logger;
        [HttpPost("user/status")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.ViewAllUsers, LrbResource.Reports)]
        [OpenApiOperation("Get data status report based on users", "")]
        public async Task<PagedResponse<BaseDataReportByUserDto, string>> GetReportAsync([FromBody] GetDataStatusReportByUsersRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("user/status-count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get data status report based on users", "")]
        public async Task<int> GetReportAsync([FromBody] GetDataStatusReportByUsersCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("project/status")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get data status report based on projects", "")]
        public async Task<PagedResponse<ViewDataProjectDto, string>> GetReportAsync([FromBody] GetDataStatusReportByProjectsRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("project/status-count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get data status report based on projects", "")]
        public async Task<int> GetReportAsync([FromBody] GetDataStatusReportByProjectsCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("source/status")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get data status report based on source", "")]
        public async Task<PagedResponse<ViewDataSourceDto, string>> GetReportAsync([FromBody] GetDataStatusReportBySourceRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("source/status-count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get data status report based on source", "")]
        public async Task<int> GetReportAsync([FromBody] GetDataStatusReportBySourceCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("subsource/status")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get data status report based on subsource", "")]
        public async Task<PagedResponse<ViewDataSubSourceDto, string>> GetReportAsync([FromBody] GetDataStatusReportBySubSourceRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("subsource/status-count")]
        [TenantIdHeader]
        [OpenApiOperation("Get data status report based on subsource", "")]
        public async Task<int> GetReportAsync([FromBody] GetDataStatusReportBySubSourceCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("user/data-call-log")]
        [TenantIdHeader]
        [OpenApiOperation("Get call-log report based on users", "")]
        public async Task<PagedResponse<ViewCallLogReportDto, string>> GetReportAsync([FromBody] GetProspectCallReportRequest request)
        {
            return await Mediator.Send(request);
        }


        [HttpPost("user/call-log/new-count")]
        [TenantIdHeader]
        [OpenApiOperation("Get call-log report based on users count", "")]
        public async Task<int> GetReportAsync([FromBody] GetDataCallLogReportRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("activity/Communication")]
        [TenantIdHeader]
        [OpenApiOperation("View ProspectCommunication Activity Report of all Users", "")]
        public async Task<PagedResponse<DataCommunicationReportDto, string>> GetProspectCommunicatonReportAsync([FromBody] GetActivityProspectCommunicationReportRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("activity/All")]
        [TenantIdHeader]
        [OpenApiOperation("View Prospect Activity Report of all Users", "")]
        public async Task<PagedResponse<UserActivityDataReportDto, string>> GetProspectActivityReportAsync([FromBody] GetActivityDataReportRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("activity/count")]
        [TenantIdHeader]
        [OpenApiOperation("View activity report count of all users", "")]
        public async Task<int> GetReportAsync([FromBody] GetDataActivityReportCountRequest request)
        {
            return await Mediator.Send(request);
        }
    }
    
}
