﻿using Lrb.Application.Reports.Web;
using Lrb.Application.Reports.Web.Data.Dtos.Campaign;
using Lrb.Application.Reports.Web.Data.Dtos.ChannelPartner;
using Lrb.Application.Reports.Web.Data.Dtos.User;
using Lrb.Application.Reports.Web.Data.Requests.Campaign;
using Lrb.Application.Reports.Web.Data.Requests.ChannelPartner;
using Lrb.Application.Reports.Web.Data.Requests.User;

namespace Lrb.WebApi.Host.Controllers
{
    [Authorize]
    public class DataReportController : VersionedApiController
    {
        [HttpGet("user/status")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.ViewAllUsers, LrbResource.Reports)]
        [OpenApiOperation("Get data status report based on users", "")]
        public async Task<PagedResponse<BaseDataReportByUserDto, string>> GetReportAsync([FromQuery] GetDataStatusReportByUsersRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("user/status-count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get data status report based on users", "")]
        public async Task<int> GetReportAsync([FromQuery] GetDataStatusReportByUsersCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("user/status/export")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.ExportAllUsers, LrbResource.Reports)]
        [OpenApiOperation("Export data status report based on users", "")]
        public async Task<Response<Guid>> GetReportAsync(RunAWSBatchForDataStatusReportByUsersRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("project/status")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get data status report based on projects", "")]
        public async Task<PagedResponse<ViewDataProjectDto, string>> GetReportAsync([FromQuery] GetDataStatusReportByProjectsRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("project/status-count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get data status report based on projects", "")]
        public async Task<int> GetReportAsync([FromQuery] GetDataStatusReportByProjectsCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("project/status/export")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Export, LrbResource.Reports)]
        [OpenApiOperation("Export data status report based on projects", "")]
        public async Task<Response<Guid>> GetReportAsync(RunAWSBatchForDataStatusReportByProjectRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("source/status")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get data status report based on source", "")]
        public async Task<PagedResponse<ViewDataSourceDto, string>> GetReportAsync([FromQuery] GetDataStatusReportBySourceRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("source/status-count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get data status report based on source", "")]
        public async Task<int> GetReportAsync([FromQuery] GetDataStatusReportBySourceCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("source/status/export")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Export, LrbResource.Reports)]
        [OpenApiOperation("Export data status report based on source", "")]
        public async Task<Response<Guid>> GetReportAsync(RunAWSBatchForDataStatusReportBySourceRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("subsource/status")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get data status report based on subsource", "")]
        public async Task<PagedResponse<ViewDataSubSourceDto, string>> GetReportAsync([FromQuery] GetDataStatusReportBySubSourceRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("subsource/status-count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get data status report based on subsource", "")]
        public async Task<int> GetReportAsync([FromQuery] GetDataStatusReportBySubSourceCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("subsource/status/export")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Export, LrbResource.Reports)]
        [OpenApiOperation("Export data status report based on subsource", "")]
        public async Task<Response<Guid>> GetReportAsync(RunAWSBatchForDataStatusReportBySubSourceRequest request)
        {
            return await Mediator.Send(request);
        }

        #region CallReport
        [HttpGet("user/data-call-log")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get call-log report based on users", "")]
        //public async Task<PagedResponse<ViewCallLogReportDto, string>> GetReportAsync([FromQuery] V2GetCallLogReportByUserRequest request)
        public async Task<PagedResponse<ViewCallLogReportDto, string>> GetReportAsync([FromQuery] GetProspectCallReportRequest request)
        {
            return await Mediator.Send(request);
        }


        [HttpGet("user/call-log/new-count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get call-log report based on users count", "")]
        public async Task<int> GetReportAsync([FromQuery] GetDataCallLogReportRequest request)
        {
            return await Mediator.Send(request);
        }


        [HttpPost("user/call-log/export")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Export, LrbResource.Reports)]
        [OpenApiOperation("Export cal-log report by based on users", "")]
        public async Task<Response<Guid>> GetReportAsync(RunAWSBatchForDataCallLogReportRequest request)
        {
            return await Mediator.Send(request);
        }
        #endregion

        [HttpGet("activity/Communication")]
        [TenantIdHeader]
        [OpenApiOperation("View ProspectCommunication Activity Report of all Users", "")]
        public async Task<PagedResponse<DataCommunicationReportDto, string>> GetProspectCommunicatonReportAsync([FromQuery] GetActivityProspectCommunicationReportRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("activity/All")]
        [TenantIdHeader]
        [OpenApiOperation("View Prospect Activity Report of all Users", "")]
        public async Task<PagedResponse<UserActivityDataReportDto, string>> GetProspectActivityReportAsync([FromQuery] GetActivityDataReportRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("activity/export/email")]
        [TenantIdHeader]
        [OpenApiOperation("Export Activity Report of all Users", "")]
        public async Task<Response<Guid>> GetReportAsync(RunAWSBatchForDataActivityReportRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("activity/count")]
        [TenantIdHeader]
        [OpenApiOperation("View activity report count of all users", "")]
        public async Task<int> GetReportAsync([FromQuery] GetDataActivityReportCountRequest request)
        {
            return await Mediator.Send(request);
        }

        #region data-agency-report

        [HttpGet("agency/status")]
        [TenantIdHeader]
        [OpenApiOperation("Get data status report based on agencies", "")]
        public async Task<PagedResponse<ViewDataAgencyDto, string>> GetDataAgencyReportAsync([FromQuery] GetDataStatusReportByAgenciesRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("agency/status-count")]
        [TenantIdHeader]
        [OpenApiOperation("Get data status report based on agencies", "")]
        public async Task<int> GetDataAgencyReportCountAsync([FromQuery] GetDataStatusReportByAgenciesCountRequest request) 
        {
            return await Mediator.Send(request);
        }

        [HttpPost("agency/status/export")]
        [TenantIdHeader]
        [OpenApiOperation("Export data status report based on agencies", "")]
        public async Task<Response<Guid>> ExportDataAgencyReportAsync(RunAWSBatchForDataStatusReportByAgencyRequest request)
        {
            return await Mediator.Send(request);
        }

        #endregion
        [HttpGet("channelpartner/status")]
        [TenantIdHeader]
        [OpenApiOperation("Get data status report based on channelpartners", "")]
        public async Task<PagedResponse<ViewDataChannelPartnerDto, string>> GetChannelPartnerReportAsync([FromQuery] GetDataStatusReportByChannelPartnersRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("channelpartner/status-count")]
        [TenantIdHeader]
        [OpenApiOperation("Get data status count report based on channelpartners", "")]
        public async Task<int> GetChannelPartnerCountReportAsync([FromQuery] GetDataStatusCountReportByChannelPartnersRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("channelpartner/status/export")]
        [TenantIdHeader]
        [OpenApiOperation("Export data status report based on channelpartners", "")]
        public async Task<Response<Guid>> GetReportAsync(RunAWSBatchForDataStatusReportByChannelPartnerRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("campaign/status")]
        [TenantIdHeader]
        [OpenApiOperation("Get data status report based on campaigns", "")]
        public async Task<PagedResponse<ViewDataCampaignDto, string>> GetCampaignAsync([FromQuery] GetDataStatusReportByCampaignsRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("campaign/status-count")]
        [TenantIdHeader]
        [OpenApiOperation("Get data status count report based on campaigns", "")]
        public async Task<int> GetCampaignCountReportAsync([FromQuery] GetDataStatusCountReportByCampaignsRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("campaign/status/export")]
        [TenantIdHeader]
        [OpenApiOperation("Export data status report based on campaigns", "")]
        public async Task<Response<Guid>> GetReportAsync(RunAWSBatchForDataStatusReportByCampaignRequest request)
        {
            return await Mediator.Send(request);
        }
    }
}
