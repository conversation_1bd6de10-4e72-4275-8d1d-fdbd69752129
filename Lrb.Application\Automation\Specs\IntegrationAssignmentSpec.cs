﻿using Lrb.Domain.Entities.Integration;

namespace Lrb.Application.Automation.Specs
{
    public class IntegrationAssignmentByIdSpec : Specification<IntegrationAssignment>
    {
        public IntegrationAssignmentByIdSpec(Guid id)
        {
            Query
                .Include(i => i.Location)
                    .ThenInclude(location => location.UserAssignment)
               .Include(i => i.Location)
                    .ThenInclude(location => location.Zone)
                        .ThenInclude(zone => zone.UserAssignment)
                .Include(i => i.Location)
                    .ThenInclude(location => location.City)
                        .ThenInclude(city => city.UserAssignment)
                .Include(i => i.Project)
                    .ThenInclude(i => i.UserAssignment)
                 .Include(i => i.Project)
                    .ThenInclude(i => i.UserAssignments)
                .Where(i => !i.IsDeleted && i.Id == id);
        }
    }
}
