using Lrb.Infrastructure.RateLimiting.Configuration;
using Lrb.Infrastructure.RateLimiting.Services;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Lrb.Infrastructure.RateLimiting;

internal static class Startup
{
    /// <summary>
    /// Add rate limiting services to the DI container
    /// </summary>
    internal static IServiceCollection AddRateLimiting(this IServiceCollection services, IConfiguration config)
    {
        // Configure rate limiting settings
        services.Configure<RateLimitingSettings>(config.GetSection(nameof(RateLimitingSettings)));

        // Register rate limiting services
        services.AddSingleton<IRateLimitingService, RateLimitingService>();
        services.AddSingleton<IClientIdentifierService, ClientIdentifierService>();

        return services;
    }

    /// <summary>
    /// Add rate limiting middleware to the pipeline
    /// </summary>
    internal static IApplicationBuilder UseRateLimiting(this IApplicationBuilder app)
    {
        return app.UseMiddleware<RateLimitingMiddleware>();
    }
}