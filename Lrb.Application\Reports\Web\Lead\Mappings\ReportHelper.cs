﻿using Google.Apis.Sheets.v4.Data;
using Lrb.Application.Lead.Web;
using Lrb.Application.Reports.Web.Lead.Requests;
using System.ComponentModel;
using System.Globalization;
using System.Reflection;
using System.Text.RegularExpressions;

namespace Lrb.Application.Reports.Web
{
    public static class ReportHelper
    {
        public static LeadSourceInfo GetLeadSourceInfo(string inputSource)
        {
            var source = inputSource.Replace(" ", "");
            var leadSource = string.Empty;
            LeadSourceInfo leadSourceInfo = new();
            if (!string.IsNullOrEmpty(inputSource))
            {

                try
                {
                    List<string?> leadSources = typeof(Lrb.Domain.Constants.EnumDescription.LeadSource)
                   .GetFields(BindingFlags.Public | BindingFlags.Static | BindingFlags.FlattenHierarchy)
               .Where(f => f.IsLiteral && !f.IsInitOnly && f.FieldType == typeof(string))
               .Select(f => (string)f.GetValue(null))
               .ToList();
                    foreach (var s in leadSources)
                    {
                        if ((s?.ToLower().Replace(" ", "").Contains(source.ToLower().Trim()) ?? false) || (source?.ToLower().Contains(s.ToLower().Replace(" ", "").Trim()) ?? false))
                        {
                            leadSource = s;
                            break;
                        }
                    }
                }
                catch (Exception e)
                {
                    leadSource = source;
                }
                Domain.Enums.LeadSource leadSourceValue = leadSource.GetValueFromDescription<Domain.Enums.LeadSource>();
                if (Enum.TryParse<Domain.Enums.LeadSource>(leadSourceValue.ToString(), true, out var sourc))
                {
                    leadSourceInfo.LeadSource = sourc;
                    leadSourceInfo.IsValidInfo = true;
                    return leadSourceInfo;
                }
                else
                {
                    leadSourceInfo.InvalidLeadSource = inputSource;
                    leadSourceInfo.IsValidInfo = false;
                    return leadSourceInfo;
                }

            }
            else
            {
                leadSourceInfo.InvalidLeadSource = inputSource;
                leadSourceInfo.IsValidInfo = false;
                return leadSourceInfo;
            }
        }
        public static T GetValueFromDescription<T>(this string description) where T : Enum
        {
            foreach (var field in typeof(T).GetFields())
            {
                if (Attribute.GetCustomAttribute(field,
                typeof(DescriptionAttribute)) is DescriptionAttribute attribute)
                {
                    if (attribute.Description == description)
                        return (T)field.GetValue(null);
                }
                else
                {
                    if (field.Name == description)
                        return (T)field.GetValue(null);
                }
            }

            return default(T);
        }
        public static string ConstructStringByKeywords(this string inputString, List<string> keywords)
        {
            // string pattern = @"\b(" + string.Join("|", keywords) + @")\b";
            string pattern = string.Join("|", keywords.Select(w => Regex.Escape(w)));
            List<string> splittedString = new();
            TextInfo textInfo = CultureInfo.InvariantCulture.TextInfo;

            foreach (Match match in Regex.Matches(inputString, pattern, RegexOptions.IgnoreCase))
            {
                string matchValue = textInfo.ToTitleCase(match.Value);
                splittedString.Add(matchValue);
            }

            string constructedString = string.Join(" ", splittedString);
            return constructedString;
        }
        public static string GetReportDisplayName(this string reportType)
        {
            List<string> keywords = new() { "meeting", "visit", "site", "report", "status", "agency", "subsource", "source", "project", "user", "and", "by", "in", "to", "using", "activity", "substatus", "call", "log", "data","lead", "received", "date", "revenue", "channelpartner", "campaign" };
            return string.IsNullOrWhiteSpace(reportType) ? reportType : reportType.ConstructStringByKeywords(keywords);
        }
        
        public static List<ViewStatusDto> MapStatusAndChildStatus(List<ViewStatusDto> filters)
        {
            filters.ForEach(child =>
            {
                if (TryGetChildData(child, filters, out List<ViewStatusDto>? childType))
                {
                    child.Count = child.Count + (childType?.Sum(i => i.Count) ?? 0);
                }
            });
            return filters;
        }
        public static List<ViewStatusDto> MapCustomStatusAndChildStatus(List<ViewStatusDto> filters)
        {
            var result = new List<ViewStatusDto>();
            try
            {
                var groupedData = filters.GroupBy(i => (i.BaseStatusDisplayName ?? i.StatusDisplayName ?? string.Empty))?.ToDictionary(i => i.Key, i => i.ToList());
                if (groupedData?.Any() ?? false)
                {
                    foreach (var item in groupedData)
                    {
                        result.Add(new ViewStatusDto() { StatusDisplayName = item.Key, Count = item.Value?.Sum(i => i.Count) ?? 0 });
                    }
                }
            }
            catch { }
            return result;
        }
        public static bool TryGetChildData(ViewStatusDto filter, List<ViewStatusDto> filters, out List<ViewStatusDto>? childDatas)
        {
            var data = filters.Where(i => filter.StatusId == i.BaseStatusId)?.ToList();
            childDatas = data;
            return data?.Any() ?? false;
        }
        public static List<UserActivityFlagsDto> MapUserActivityFlagsData(List<ActivityFlagsDto>? allData, List<Flag>? flags)
        {
            var result = new List<UserActivityFlagsDto>();
            if ((allData?.Any() ?? false) && (flags?.Any() ?? false))
            {
                var newData = allData.GroupBy(i => new Tuple<Guid, string>(i.UserId, i?.UserName ?? string.Empty)).ToDictionary(i => i.Key, i => i.Select(j => j).ToList());
                foreach (var data in newData)
                {
                    var item = new UserActivityFlagsDto();
                    item.UserId = data.Key.Item1;
                    item.UserName = data.Key.Item2;
                    item.Flags = GetFlagsData(data.Value, flags);
                    result.Add(item);
                }
            }
            return result;
        }
        public static List<FlagCountDto>? GetFlagsData(List<ActivityFlagsDto> items, List<Flag> flags)
        {
            var result = new List<FlagCountDto>();
            if (flags?.Any() ?? false)
            {
                flags?.ForEach(flag =>
                {
                    var data = new FlagCountDto();
                    data.Name = flag.Name;
                    data.UniqueLeadsCount = items?.Where(i => i.Flags != null && i.Flags.Contains(flag.Name))?.DistinctBy(i => i.LeadId)?.Count() ?? default;
                    if (items?.Any() ?? false)
                    {
                        items = items.OrderBy(i => i.ModifiedDate).ToList();
                        data.Count = CountConsecutiveTagOccurrences(items, flag.Name);
                    }
                    result.Add(data);
                });
            }
            return result;
        }
        public static int CountConsecutiveTagOccurrences(List<ActivityFlagsDto> tagList, string tagToCount)
        {
            int count = 0;
            try
            {
                if (tagList.Any() && tagList.Any(i => i.Flags != null && i.Flags.Contains(tagToCount)))
                {
                    var groupedData = tagList?.GroupBy(i => i.LeadId)?.ToDictionary(i => i?.Key ?? Guid.NewGuid(), i => i.ToList()) ?? default;
                    if (groupedData?.Any() ?? false)
                    {
                        foreach (var item in groupedData)
                        {
                            bool previousTagFound = false;
                            item.Value.ForEach(tagString =>
                            {
                                if (tagString.Flags != null && tagString.Flags.Contains(tagToCount))
                                {
                                    if (!previousTagFound)
                                    {
                                        count++;
                                    }
                                    previousTagFound = true;
                                }
                                else
                                {
                                    previousTagFound = false;
                                }
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
            }
            return count;
        }
    }
}
