﻿using System.Runtime.CompilerServices;

namespace Lrb.Domain.Constants
{
    public static class EnumDescription
    {
        public static class LeadSource
        {
            public const string IVR = "IVR";
            public const string Facebook = "Facebook";
            public const string LinkedIn = "LinkedIn";
            public const string GoogleAds = "Google Ads";
            public const string MagicBricks = "Magic Bricks";
            public const string NinetyNineAcres = "99 Acres/Ninety Nine Acres";
            public const string Housing = "Housing.com";
            public const string GharOffice = "GharOffice";
            public const string Referral = "Referral";
            public const string WalkIn = "Walk In";
            public const string Website = "Website";
            public const string Direct = "Direct";
            public const string Gmail = "Gmail";
            public const string PropertyMicrosite = "Microsite";
            public const string PortfolioMicrosite = "Portfolio";
            public const string Phonebook = "Phonebook";
            public const string CallLogs = "Call Logs";
            public const string LeadPool = "Lead Pool";
            public const string SquareYards = "Square Yards";
            public const string JustLead = "Just Lead";
            public const string QuikrHomes = "Quikr Homes";
            public const string WhatsApp = "WhatsApp";
            public const string YouTube = "YouTube";
            public const string QRCode = "QR Code";
            public const string Instagram = "Instagram";
            public const string OLX = "OLX";
            public const string EstateDekho = "Estate Dekho";
            public const string GoogleSheet = "Google Sheets";
            public const string ChannelPartner = "Channel Partner";
            public const string RealEstateIndia = "Real Estate India";
            public const string CommonFloor = "Common Floor";
            public const string Data = "Data";
            public const string RoofandFloor = "Roof & Floor";
            public const string MicrosoftAds = "Microsoft Ads";
            public const string PropertyWala = "PropertyWala";
            public const string ProjectMicrosite = "Project Microsite";
            public const string MyGate = "MyGate";
            public const string Flipkart = "Flipkart";
            public const string PropertyFinder = "Property Finder";
            public const string Bayut = "Bayut";
            public const string Dubizzle = "Dubizzle";
            public const string Webhook = "Webhook";
            public const string TikTok = "TikTok";
            public const string Snapchat = "Snapchat";
            public const string GoogleAdsCampaign = "GoogleAdsCampaign";
            public const string Pabbly = "Pabbly";
        }

        public static class ErrorActionCode
        {
            public const string NoOp = "Do nothing.";
            public const string StayOn = "Stay on the same screen.";
            public const string ChangeRoute = "Navigate to any other screen, if needed.";
            public const string FallBack = "Navigate back to the parent screen.";
            public const string ReturnToHome = "Navigate to main/home screen.";
            public const string Refresh = "Refresh token data.";
            public const string Logout = "Log the user out.";
            public const string ShowToast = "Show toast.";
        }
        public static class MediaType
        {
            public const string None = "None";
            public const string Image = "Image";
            public const string Video = "Video";
            public const string Audio = "Audio";
            public const string PDF = "PDF";
        }
    }
}
