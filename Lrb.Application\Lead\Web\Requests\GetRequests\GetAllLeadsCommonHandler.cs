﻿using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Domain.Entities.MasterData;

namespace Lrb.Application.Lead.Web
{
    public class GetAllLeadsCommonHandler
    {
        protected readonly ICurrentUser _currentUser;
        protected readonly IDapperRepository _dapperRepository;
        protected readonly ILeadRepository _efLeadRepository;
        protected readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customMasterLeadStatusRepo;

        public GetAllLeadsCommonHandler(
            ICurrentUser currentUser,
            IDapperRepository dapperRepository,
            ILeadRepository efLeadRepository,
            IRepositoryWithEvents<CustomMasterLeadStatus> customMasterLeadStatusRepo)
        {
            _currentUser = currentUser;
            _dapperRepository = dapperRepository;
            _efLeadRepository = efLeadRepository;
            _customMasterLeadStatusRepo = customMasterLeadStatusRepo;
        }
        protected LeadTagFilterDto GetLeadTagFilter(GetAllLeadsByNewFiltersRequest request)
        {
            LeadTagFilterDto tagFilterDto = new LeadTagFilterDto();
            foreach (var tag in request.LeadTags)
                switch (tag)
                {
                    case LeadTagEnum.IsHot:
                        tagFilterDto.IsHotLead = true;
                        break;
                    case LeadTagEnum.IsAboutToConvert:
                        tagFilterDto.IsAboutToConvert = true;
                        break;
                    case LeadTagEnum.IsEscalated:
                        tagFilterDto.IsEscalated = true;
                        break;
                    case LeadTagEnum.IsIntegrationLead:
                        tagFilterDto.IsIntegrationLead = true;
                        break;
                    case LeadTagEnum.IsHighlighted:
                        tagFilterDto.IsHighlighted = true;
                        break;
                    case LeadTagEnum.IsWarmLead:
                        tagFilterDto.IsWarmLead = true;
                        break;
                    case LeadTagEnum.IsColdLead:
                        tagFilterDto.IsColdLead = true;
                        break;
                }
            return tagFilterDto;
        }

        protected LeadTagFilterDto GetLeadTagFilter(GetAllLeadsAnonymousRequest request)
        {
            LeadTagFilterDto tagFilterDto = new LeadTagFilterDto();
            foreach (var tag in request.LeadTags)
                switch (tag)
                {
                    case LeadTagEnum.IsHot:
                        tagFilterDto.IsHotLead = true;
                        break;
                    case LeadTagEnum.IsAboutToConvert:
                        tagFilterDto.IsAboutToConvert = true;
                        break;
                    case LeadTagEnum.IsEscalated:
                        tagFilterDto.IsEscalated = true;
                        break;
                    case LeadTagEnum.IsIntegrationLead:
                        tagFilterDto.IsIntegrationLead = true;
                        break;
                    case LeadTagEnum.IsHighlighted:
                        tagFilterDto.IsHighlighted = true;
                        break;
                    case LeadTagEnum.IsWarmLead:
                        tagFilterDto.IsWarmLead = true;
                        break;
                    case LeadTagEnum.IsColdLead:
                        tagFilterDto.IsColdLead = true;
                        break;
                }
            return tagFilterDto;
        }
        protected async Task<LeadCountsByNewFilterDto> AddLeadsCount(LeadCountsByNewFilterDto leadCounts, System.Reflection.PropertyInfo property, GetAllLeadsByNewFiltersRequest request, IEnumerable<Guid> subIds, Guid userId, List<Guid> leadHistoryIds, List<CustomMasterLeadStatus> customStatus, IQueryable<Domain.Entities.Lead> query, bool? isAdmin = null)
        {
            var propertyName = property.Name;
            switch (propertyName)
            {
                case nameof(LeadCountsByNewFilterDto.ManageLeadsCount):
                    request.LeadVisibility = (BaseLeadVisibility)(-1);
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    leadCounts.ManageLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.MyLeadsCount):
                    request.LeadVisibility = BaseLeadVisibility.Self;
                    if (request.ShouldShowBookedDetails != true)
                    {
                        request.FirstLevelFilter = FirstLevelFilter.All;
                    }
                    leadCounts.MyLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.TeamLeadsCount):
                    request.LeadVisibility = BaseLeadVisibility.Reportee;
                    if (request.ShouldShowBookedDetails != true)
                    {
                        request.FirstLevelFilter = FirstLevelFilter.All;
                    }
                    leadCounts.TeamLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.AllLeadsCount):
                    request.LeadVisibility = BaseLeadVisibility.SelfWithReportee;
                    if (request.ShouldShowBookedDetails != true)
                    {
                        request.FirstLevelFilter = FirstLevelFilter.All;
                    }
                    leadCounts.AllLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.UnassignLeadsCount):
                    request.LeadVisibility = BaseLeadVisibility.UnassignLead;
                    if (request.ShouldShowBookedDetails != true)
                    {
                        request.FirstLevelFilter = FirstLevelFilter.All;
                    }
                    leadCounts.UnassignLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.DeletedLeadsCount):
                    request.LeadVisibility = BaseLeadVisibility.DeletedLeads;
                    if (request.ShouldShowBookedDetails != true)
                    {
                        request.FirstLevelFilter = FirstLevelFilter.All;
                    }
                    leadCounts.DeletedLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.DuplicateLeadsCount):
                    request.LeadVisibility = BaseLeadVisibility.DuplicateLeads;
                    //request.FilterType = LeadFilterTypeWeb.All;
                    if (request.ShouldShowBookedDetails != true)
                    {
                        request.FirstLevelFilter = FirstLevelFilter.All;
                    }
                    leadCounts.DuplicateLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.NewLeadsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.New;
                    leadCounts.NewLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.ActiveLeadsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.All;
                    leadCounts.ActiveLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.PendingLeadsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.Pending;
                    leadCounts.PendingLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.ScheduledLeadsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.Scheduled;
                    request.ScheduledDateTypeFilter = ScheduledDateTypeFilter.All;
                    request.ScheduledType = ScheduledType.None;
                    leadCounts.ScheduledLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.OverdueLeadsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.Overdue;
                    leadCounts.OverdueLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.BookedLeadsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Booked;
                    leadCounts.BookedLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.ScheduledTodayLeadsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.Scheduled;
                    request.ScheduledDateTypeFilter = ScheduledDateTypeFilter.Today;
                    request.ScheduledType = ScheduledType.None;
                    leadCounts.ScheduledTodayLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.ScheduledTomorrowLeadsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.Scheduled;
                    request.ScheduledDateTypeFilter = ScheduledDateTypeFilter.Tomorrow;
                    request.ScheduledType = ScheduledType.None;
                    leadCounts.ScheduledTomorrowLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.ScheduledNextTwoDaysLeadsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.Scheduled;
                    request.ScheduledDateTypeFilter = ScheduledDateTypeFilter.NextTwoDays;
                    request.ScheduledType = ScheduledType.None;
                    leadCounts.ScheduledNextTwoDaysLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.UpcomingScheduledLeadsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.Scheduled;
                    request.ScheduledDateTypeFilter = ScheduledDateTypeFilter.Upcoming;
                    request.ScheduledType = ScheduledType.None;
                    leadCounts.UpcomingScheduledLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.MeetingsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.Scheduled;
                    request.ScheduledType = ScheduledType.Meeting;
                    leadCounts.MeetingsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.SiteVisitsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.Scheduled;
                    request.ScheduledType = ScheduledType.SiteVisit;
                    leadCounts.SiteVisitsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.CallbackCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.Scheduled;
                    request.ScheduledType = ScheduledType.Callback;
                    leadCounts.CallbackCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.NotInterestedLeadsCount):
                    request.FirstLevelFilter = FirstLevelFilter.NotInterested;
                    request.SecondLevelFilter = SecondLevelFilter.All;
                    leadCounts.NotInterestedLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.DifferentRequirementsCount):
                    request.FirstLevelFilter = FirstLevelFilter.NotInterested;
                    request.SecondLevelFilter = SecondLevelFilter.DifferentRequirements;
                    leadCounts.DifferentRequirementsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.DifferentLocationCount):
                    request.FirstLevelFilter = FirstLevelFilter.NotInterested;
                    request.SecondLevelFilter = SecondLevelFilter.DifferentLocation;
                    leadCounts.DifferentLocationCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.UnmatchedBudgetCount):
                    request.FirstLevelFilter = FirstLevelFilter.NotInterested;
                    request.SecondLevelFilter = SecondLevelFilter.UnmatchedBudget;
                    leadCounts.UnmatchedBudgetCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.DroppedLeadsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Dropped;
                    request.SecondLevelFilter = SecondLevelFilter.All;
                    leadCounts.DroppedLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.NotLookingCount):
                    request.FirstLevelFilter = FirstLevelFilter.Dropped;
                    request.SecondLevelFilter = SecondLevelFilter.NotLooking;
                    leadCounts.NotLookingCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.WrongOrInvalidNoCount):
                    request.FirstLevelFilter = FirstLevelFilter.Dropped;
                    request.SecondLevelFilter = SecondLevelFilter.WrongOrInvalidNo;
                    leadCounts.WrongOrInvalidNoCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.PurchasedFromOthersCount):
                    request.FirstLevelFilter = FirstLevelFilter.Dropped;
                    request.SecondLevelFilter = SecondLevelFilter.PurchasedFromOthers;
                    leadCounts.PurchasedFromOthersCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.RingingNotReceivedCount):
                    request.FirstLevelFilter = FirstLevelFilter.Dropped;
                    request.SecondLevelFilter = SecondLevelFilter.RingingNotReceived;
                    leadCounts.RingingNotReceivedCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.EscalatedLeadsFlagCount):
                    //request.FirstLevelFilter = FirstLevelFilter.Active;
                    //request.SecondLevelFilter = SecondLevelFilter.All;
                    request.LeadTags = new List<LeadTagEnum> { LeadTagEnum.IsEscalated };
                    leadCounts.EscalatedLeadsFlagCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.HotLeadsFlagCount):
                    //request.FirstLevelFilter = FirstLevelFilter.Active;
                    //request.SecondLevelFilter = SecondLevelFilter.All;
                    request.LeadTags = new List<LeadTagEnum> { LeadTagEnum.IsHot };
                    leadCounts.HotLeadsFlagCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.WarmLeadsFlagCount):
                    //request.FirstLevelFilter = FirstLevelFilter.Active;
                    //request.SecondLevelFilter = SecondLevelFilter.All;
                    request.LeadTags = new List<LeadTagEnum> { LeadTagEnum.IsWarmLead };
                    leadCounts.WarmLeadsFlagCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.ColdLeadsFlagCount):
                    //request.FirstLevelFilter = FirstLevelFilter.Active;
                    //request.SecondLevelFilter = SecondLevelFilter.All;
                    request.LeadTags = new List<LeadTagEnum> { LeadTagEnum.IsColdLead };
                    leadCounts.ColdLeadsFlagCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.AboutToConvertLeadsFlagCount):
                    //request.FirstLevelFilter = FirstLevelFilter.Active;
                    //request.SecondLevelFilter = SecondLevelFilter.All;
                    request.LeadTags = new List<LeadTagEnum> { LeadTagEnum.IsAboutToConvert };
                    leadCounts.AboutToConvertLeadsFlagCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.HighlightedLeadsFlagCount):
                    //request.FirstLevelFilter = FirstLevelFilter.Active;
                    //request.SecondLevelFilter = SecondLevelFilter.All;
                    request.LeadTags = new List<LeadTagEnum> { LeadTagEnum.IsHighlighted };
                    leadCounts.HighlightedLeadsFlagCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.AllFirstLevelLeadsCount):
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    leadCounts.AllFirstLevelLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.BookingCancelLeadCount):
                    request.FirstLevelFilter = FirstLevelFilter.BookingCancel;
                    leadCounts.BookingCancelLeadCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.ExpressionOfInterestLeadCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.ExpressionOfInterest;
                    leadCounts.ExpressionOfInterestLeadCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;

                case nameof(LeadCountsByNewFilterDto.ReEnquiredLeadsCount):
                    request.LeadVisibility = BaseLeadVisibility.ReEnquired;
                    if (request.ShouldShowBookedDetails != true)
                    {
                        request.FirstLevelFilter = FirstLevelFilter.All;
                    }
                    leadCounts.ReEnquiredLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, customStatus, isAdmin));
                    break;
            }
            return leadCounts;
        }
        protected async Task<int> AddLeadsCount(string? flagName, GetAllLeadsByNewFiltersRequest request, IEnumerable<Guid> subIds, Guid userId, List<Guid> leadHistoryIds, List<CustomMasterLeadStatus> customStatus, bool? isAdmin = null)
        {
            if (!string.IsNullOrEmpty(flagName))
            {
                request.CustomFlags = new List<string>() { flagName };
                var count = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, new(), customStatus, isAdmin: isAdmin)).Result;
                return count;
            }
            else
            {
                return 0;
            }
        }

        protected async Task<LeadCountsByNewFilterDto> AddLeadsCount(LeadCountsByNewFilterDto leadCounts, System.Reflection.PropertyInfo property, GetActiveLeadCountsRequest request, IEnumerable<Guid> subIds, Guid userId, List<Guid> leadHistoryIds, List<CustomMasterLeadStatus> masterLeadStatus, IQueryable<Domain.Entities.Lead> query, bool? isAdmin = null)
        {
            var propertyName = property.Name;
            switch (propertyName)
            {
                case nameof(LeadCountsByNewFilterDto.ManageLeadsCount):
                    request.LeadVisibility = (BaseLeadVisibility)(-1);
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    leadCounts.ManageLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, masterLeadStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.MyLeadsCount):
                    request.LeadVisibility = request.LeadVisibility != BaseLeadVisibility.UnassignLead && request.LeadVisibility != BaseLeadVisibility.DeletedLeads ? BaseLeadVisibility.Self : request.LeadVisibility;
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    leadCounts.MyLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, masterLeadStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.TeamLeadsCount):
                    request.LeadVisibility = request.LeadVisibility != BaseLeadVisibility.UnassignLead && request.LeadVisibility != BaseLeadVisibility.DeletedLeads ? BaseLeadVisibility.Reportee : request.LeadVisibility;
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    leadCounts.TeamLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, masterLeadStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.AllLeadsCount):
                    request.LeadVisibility = /*request.LeadVisibility != BaseLeadVisibility.UnassignLead && request.LeadVisibility != BaseLeadVisibility.DeletedLeads ? BaseLeadVisibility.SelfWithReportee :*/ request.LeadVisibility;
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    leadCounts.AllLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, masterLeadStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.UnassignLeadsCount):
                    request.LeadVisibility = request.LeadVisibility != BaseLeadVisibility.UnassignLead && request.LeadVisibility != BaseLeadVisibility.DeletedLeads ? BaseLeadVisibility.UnassignLead : request.LeadVisibility;
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    leadCounts.UnassignLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, masterLeadStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.DeletedLeadsCount):
                    request.LeadVisibility = request.LeadVisibility != BaseLeadVisibility.UnassignLead && request.LeadVisibility != BaseLeadVisibility.DeletedLeads ? BaseLeadVisibility.DeletedLeads : request.LeadVisibility;
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    leadCounts.DeletedLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, masterLeadStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.NewLeadsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.New;
                    leadCounts.NewLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, masterLeadStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.ActiveLeadsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.All;
                    leadCounts.ActiveLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, masterLeadStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.PendingLeadsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.Pending;
                    leadCounts.PendingLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, masterLeadStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.ScheduledLeadsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.Scheduled;
                    request.ScheduledDateTypeFilter = ScheduledDateTypeFilter.All;
                    request.ScheduledType = ScheduledType.None;
                    leadCounts.ScheduledLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, masterLeadStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.OverdueLeadsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.Overdue;
                    request.ScheduledType = ScheduledType.None;
                    leadCounts.OverdueLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, masterLeadStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.OverdueMeetingCount):
                    if (request.FirstLevelFilter == FirstLevelFilter.Active && request.SecondLevelFilter == SecondLevelFilter.Overdue)
                    {
                        request.ScheduledType = ScheduledType.Meeting;
                        leadCounts.OverdueMeetingCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, masterLeadStatus, isAdmin));
                    }
                    break;
                case nameof(LeadCountsByNewFilterDto.OverdueSiteVisitCount):
                    if (request.FirstLevelFilter == FirstLevelFilter.Active && request.SecondLevelFilter == SecondLevelFilter.Overdue)
                    {
                        request.ScheduledType = ScheduledType.SiteVisit;
                        leadCounts.OverdueSiteVisitCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, masterLeadStatus, isAdmin));
                    }
                    break;
                case nameof(LeadCountsByNewFilterDto.OverdueCallbackCount):
                    if (request.FirstLevelFilter == FirstLevelFilter.Active && request.SecondLevelFilter == SecondLevelFilter.Overdue)
                    {
                        request.ScheduledType = ScheduledType.Callback;
                        leadCounts.OverdueCallbackCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, masterLeadStatus, isAdmin));
                    }
                    break;
                case nameof(LeadCountsByNewFilterDto.BookedLeadsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Booked;
                    leadCounts.BookedLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, masterLeadStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.ScheduledTodayLeadsCount):
                    if (request.FirstLevelFilter == FirstLevelFilter.Active && request.SecondLevelFilter == SecondLevelFilter.Scheduled)
                    {
                        request.ScheduledDateTypeFilter = ScheduledDateTypeFilter.Today;
                        request.ScheduledType = ScheduledType.None;
                        leadCounts.ScheduledTodayLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, masterLeadStatus, isAdmin));
                    }
                    break;
                case nameof(LeadCountsByNewFilterDto.ScheduledTomorrowLeadsCount):
                    if (request.FirstLevelFilter == FirstLevelFilter.Active && request.SecondLevelFilter == SecondLevelFilter.Scheduled)
                    {
                        request.ScheduledDateTypeFilter = ScheduledDateTypeFilter.Tomorrow;
                        request.ScheduledType = ScheduledType.None;
                        leadCounts.ScheduledTomorrowLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, masterLeadStatus, isAdmin));
                    }
                    break;
                case nameof(LeadCountsByNewFilterDto.ScheduledNextTwoDaysLeadsCount):
                    if (request.FirstLevelFilter == FirstLevelFilter.Active && request.SecondLevelFilter == SecondLevelFilter.Scheduled)
                    {
                        request.ScheduledDateTypeFilter = ScheduledDateTypeFilter.NextTwoDays;
                        request.ScheduledType = ScheduledType.None;
                        leadCounts.ScheduledNextTwoDaysLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, masterLeadStatus, isAdmin));
                    }
                    break;
                case nameof(LeadCountsByNewFilterDto.UpcomingScheduledLeadsCount):
                    if (request.FirstLevelFilter == FirstLevelFilter.Active && request.SecondLevelFilter == SecondLevelFilter.Scheduled)
                    {
                        request.ScheduledDateTypeFilter = ScheduledDateTypeFilter.Upcoming;
                        request.ScheduledType = ScheduledType.None;
                        leadCounts.UpcomingScheduledLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, masterLeadStatus, isAdmin));
                    }
                    break;
                case nameof(LeadCountsByNewFilterDto.MeetingsCount):
                    if (request.FirstLevelFilter == FirstLevelFilter.Active && request.SecondLevelFilter == SecondLevelFilter.Scheduled)
                    {
                        request.ScheduledType = ScheduledType.Meeting;
                        leadCounts.MeetingsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, masterLeadStatus, isAdmin));
                    }
                    break;
                case nameof(LeadCountsByNewFilterDto.SiteVisitsCount):
                    if (request.FirstLevelFilter == FirstLevelFilter.Active && request.SecondLevelFilter == SecondLevelFilter.Scheduled)
                    {
                        request.ScheduledType = ScheduledType.SiteVisit;
                        leadCounts.SiteVisitsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, masterLeadStatus, isAdmin));
                    }
                    break;
                case nameof(LeadCountsByNewFilterDto.CallbackCount):
                    if (request.FirstLevelFilter == FirstLevelFilter.Active && request.SecondLevelFilter == SecondLevelFilter.Scheduled)
                    {
                        request.ScheduledType = ScheduledType.Callback;
                        leadCounts.CallbackCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, masterLeadStatus, isAdmin));
                    }
                    break;
                case nameof(LeadCountsByNewFilterDto.ExpressionOfInterestLeadCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.ExpressionOfInterest;
                    leadCounts.ExpressionOfInterestLeadCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, masterLeadStatus, isAdmin));
                    break;
                case nameof(LeadCountsByNewFilterDto.ReEnquiredLeadsCount):
                    request.LeadVisibility = request.LeadVisibility != BaseLeadVisibility.ReEnquired && request.LeadVisibility != BaseLeadVisibility.ReEnquired ? BaseLeadVisibility.ReEnquired : request.LeadVisibility;
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    leadCounts.ReEnquiredLeadsCount = (await _efLeadRepository.GetAllLeadsCountForWebAsync(request, query, subIds, userId, masterLeadStatus, isAdmin));
                    break;
            }
            return leadCounts;
        }

        //protected async Task<LeadCountsByNewFilterDto> AddLeadsCount(LeadCountsByNewFilterDto leadCounts, Guid secondLevelFilterId, GetAllLeadsByNewFiltersRequest request, IEnumerable<Guid> subIds, Guid userId, List<Guid> leadHistoryIds, List<CustomMasterLeadStatus> customStatus)
        //{

        //}
    }
}
