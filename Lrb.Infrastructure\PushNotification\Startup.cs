﻿using Lrb.Application.Common.PushNotification;
using Lrb.Infrastructure.BlobStorage;
using Lrb.Infrastructure.PushNotification.Firebase;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Lrb.Infrastructure.PushNotification
{
    public static class Startup
    {
        public static IServiceCollection AddNotification(this IServiceCollection services, IConfiguration config)
        {
            services.Configure<AWSSettings>(config.GetSection(nameof(AWSSettings)));
            services.Configure<AwsSecretManagerSettings>(config.GetSection(nameof(AwsSecretManagerSettings)));
            services.AddTransient<IPinpointService, PinpointService>();
            services.AddTransient<INotificationService, NotificationService>();
            services.AddTransient<INotificationMessageBuilder, NotificationMessageBuilder>();
            services.AddTransient<INotificationSenderService, NotificationSenderService>();
            services.AddTransient<ITemplateNotificationService, TemplateNotificationService>();
            services.Configure<FirebaseSetting>(config.GetSection(nameof(FirebaseSetting)));
            services.Configure<MobileFirebaseSetting>(config.GetSection(nameof(MobileFirebaseSetting)));
            return services;
        }
    }
}
