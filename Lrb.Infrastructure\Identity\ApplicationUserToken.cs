﻿using Lrb.Domain.Enums;
using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Infrastructure.Identity
{
    public class ApplicationUserToken : IdentityUserToken<string>
    {
        public Guid? UserLoginId { get; set; }
        public string? IdToken { get; set; }
        public string? RefreshToken { get; set; }
        public string? AccessToken { get; set; }
        public IdentityTokenType? TokenType { get; set; }
        public DateTime CreatedOn { get; set; } = DateTime.UtcNow;

    }
}
