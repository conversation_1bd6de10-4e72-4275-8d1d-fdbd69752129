﻿using Lrb.Application.CustomStatus.Mobile;
using Lrb.Application.CustomStatus.Mobile.Request;
using Lrb.Domain.Entities;

namespace Lrb.MobileApi.Host.Controllers
{
    [Authorize]
    public class StatusController : VersionedApiController
    {
        [HttpGet]
        [TenantIdHeader]
        [OpenApiOperation("Get all status.", "")]
        public async Task<PagedResponse<ViewCustomStatusDto, string>> GetAsync([FromQuery] GetAllStatusRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPut]
        [TenantIdHeader]
        [OpenApiOperation("Update a status.", "")]
        public async Task<Response<bool>> UpdateAsync(UpdateStatusRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Get status.", "")]
        public async Task<Response<ViewCustomStatusDto>> GetAsync(Guid id)
        {
            return await Mediator.Send(new GetStatusByIdRequest(id));
        }
        [HttpDelete("{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Delete a status.", "")]
        public async Task<Response<bool>> DeleteAsync(Guid id)
        {
            return await Mediator.Send(new DeleteStatusRequest(id));
        }
        [HttpPost]
        [TenantIdHeader]
        [OpenApiOperation("Create a new status.", "")]
        public async Task<Response<Guid>> CreateAsync(CreateStatusRequest request)
        {
            return await Mediator.Send(request);
        }

        [AllowAnonymous]
        [ApiKey]
        [HttpGet("GetAllStatusAnonymous")]
        [TenantIdHeader]
        [OpenApiOperation("Get all status.", "")]
        public async Task<Response<List<StatusNamesDto>>> GetAllStatusAnonymous([FromQuery] GetAllStatusAnonymousRequest request)
        {
            var apiKey = this.HttpContext.Request.Headers["API-Key"];
            request.ApiKey = apiKey;
            return await Mediator.Send(request);
        }
        [HttpGet("Offline")]
        [TenantIdHeader]
        [OpenApiOperation("Get all status v2.", "")]
        public async Task<PagedResponse<ViewCustomStatusDto, string>> GetAsync([FromQuery] GetAllStatusV2Request request)
        {
            return await Mediator.Send(request);
        }
    }
}
