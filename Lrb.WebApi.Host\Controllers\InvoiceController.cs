﻿using Lrb.Application.Invoice.Web.Dtos;
using Lrb.Application.Invoice.Web.Requests;
namespace Lrb.WebApi.Host.Controllers
{
    [Authorize]
    public class InvoiceController : VersionedApiController
    {
        [HttpPost("addinvoicedetails")]
        [TenantIdHeader]
        [OpenApiOperation("Add Invoice Deatils.", "")]
        public async Task<Response<InvoiceDetailDto>> AddInvoiceDetails(CreateInvoiceDetailRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("getallinvoicetemplates")]
        [TenantIdHeader]
        [OpenApiOperation("Get All Invoice Templates.", "")]
        public async Task<Response<List<InvoiceDetailDto>>> GetAllInvoiceTemplates()
        {
            return await Mediator.Send(new GetAllInvoiceDetailsRequest());
        }
        [HttpPut("updateinvoicedetails/{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Update Invoice Detail.", "Updates an existing invoice detail")]
        public async Task<Response<InvoiceDetailDto>> UpdateInvoiceDetails([FromBody] UpdateInvoiceDetailRequest request, Guid id, CancellationToken cancellationToken)
        {
            request.Id = id;
            return await Mediator.Send(request, cancellationToken);
        }
        [HttpDelete("{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Delete Invoice Detail.", "")]
        public async Task<Response<Guid>> DeleteAsync(Guid id, CancellationToken cancellationToken)
        {
            var request = new DeleteInvoiceDetailRequest { Id = id };
            return await Mediator.Send(request, cancellationToken);
        }
        [HttpGet("getinvoicedetails")]
        [TenantIdHeader]
        [OpenApiOperation("Fetch invoice details with mapped variables")]
        public async Task<Response<Dictionary<string, string>>> GetInvoiceDetails([FromQuery] GetInvoiceDetailsRequest request, CancellationToken cancellationToken)
        {
            return await Mediator.Send(request, cancellationToken);
        }
        [HttpGet("checkduplicate")]
        [TenantIdHeader]
        [OpenApiOperation("Check if invoice template name already exists.", "")]
        public async Task<Response<bool>> CheckDuplicate(string name)
        {
            return await Mediator.Send(new CheckDuplicateTemplateRequest(name));
        }
        [HttpPost("sharemailtolead")]
        [TenantIdHeader]
        [OpenApiOperation("Send mail to lead.", "")]
        public async Task<Response<bool>> ShareMailToLead(Guid leadId, [FromBody] ShareMailToLeadRequest request)
        {
            request.LeadId = leadId;
            return await Mediator.Send(request);
        }
    }
}