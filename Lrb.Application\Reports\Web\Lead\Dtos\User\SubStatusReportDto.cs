﻿namespace Lrb.Application.Reports.Web
{
    public class SubStatusReportDto : IDto
    {
        public User? User { get; set; }
        public List<StatusDto>? Status { get; set; }
    }
    public class StatusDto
    {
        public Guid? Id { get; set; }
        public int Count { get; set; }
        public string? SubStatus { get; set; }
        public string? BaseStatus { get; set; }
        public int MeetingDoneCount { get; set; }
        public int MeetingDoneUniqueCount { get; set; }
        public int MeetingNotDoneCount { get; set; }
        public int MeetingNotDoneUniqueCount { get; set; }
        public int SiteVisitDoneCount { get; set; }
        public int SiteVisitDoneUniqueCount { get; set; }
        public int SiteVisitNotDoneCount { get; set; }
        public int SiteVisitNotDoneUniqueCount { get; set; }
        public int OverdueCount { get; set; }
  
    }
    public class ModifiedSubStatusReportDto : IDto
    {
        public Guid UserId { get; set; }
        private string? _firstName;
        private string? _lastName;
        private string? _userName;

        public string? FirstName
        {
            get => _firstName;
            set
            {
                _firstName = value;
                UpdateUserName();
            }
        }

        public string? LastName
        {
            get => _lastName;
            set
            {
                _lastName = value;
                UpdateUserName();
            }
        }

        public string? UserName
        {
            get => _userName;
            set => _userName = value;
        }
        private void UpdateUserName()
        {
            _userName = $"{_firstName} {_lastName}".Trim();
        }
        public Dictionary<string, object>? BaseStatusWithSubStatusCount { get; set; }
    }

    public class SubStatusByUserDto : IDto
    {
        public Guid UserId { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public long All { get; set; }
        public long Active { get; set; }
        public long Overdue { get; set; }
        public long Callback { get; set; }
        public long Busy { get; set; }
        public long ToScheduleAMeeting { get; set; }
        public long FollowUp { get; set; }
        public long ToScheduleSiteVisit { get; set; }
        public long PlanPostponed { get; set; }
        public long NeedMoreInfo { get; set; }
        public long NotAnswered { get; set; }
        public long NotReachable { get; set; }
        public long Dropped { get; set; }
        public long NotLooking { get; set; }
        public long RingingNotReceived { get; set; }
        public long WrongOrInvalidNo { get; set; }
        public long PurchasedFromOthers { get; set; }
        public long MeetingScheduled { get; set; }
        public long OnCall { get; set; }
        public long Online { get; set; }
        public long InPerson { get; set; }
        public long Others { get; set; }
        public long NotInterested { get; set; }
        public long DifferentLocation { get; set; }
        public long DifferentRequirements { get; set; }
        public long UnmatchedBudget { get; set; }
        public long SiteVisitScheduled { get; set; }
        public long FirstVisit { get; set; }
        public long ReVisit { get; set; }
        public long Pending { get; set; }
        public long Booked { get; set; }
        public long New { get; set; }
        public long BookingCancel { get; set; }
        public long ExpressionOfInterestLeadCount { get; set; }
        public long InvoicedLeadsCount { get; set; }
    }
    public class ModifiedSubStatusvsSubSourceReportDto : IDto
    {
        public string? SubSource { get; set; }
        public Dictionary<string, object>? BaseStatusWithSubStatusCount { get; set; }
    }
    public class ModifiedProjectvsSubStatusReportDto : IDto
    {
        public string? ProjectTitle { get; set; }
        public Dictionary<string, object>? BaseStatusWithSubStatusCount { get; set; }
    }
    public class ModifiedAllReportDto : IDto
    {
        public Guid UserId { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? SubSource { get; set; }
        public string? ProjectTitle { get; set; }
        public Dictionary<string, object>? BaseStatusWithSubStatusCount { get; set; }
    }
    public class ChannelPartnerReportDto : IDto
    {
        public Guid UserId { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? ChannelPartnerFirmName { get; set; }
        public Dictionary<string, object>? BaseStatusWithSubStatusCount { get; set; }
    }
    public class CampaignReportDto : IDto
    {
        public Guid UserId { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? CampaignName { get; set; }
        public Dictionary<string, object>? BaseStatusWithSubStatusCount { get; set; }
    }
}
