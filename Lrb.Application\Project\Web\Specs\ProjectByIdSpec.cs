﻿using Lrb.Domain.Entities;

namespace Lrb.Application.Project.Web.Specs
{
    public class ProjectByIdSpec : Specification<Domain.Entities.Project>
    {
        public ProjectByIdSpec(Guid id)
        {
            Query.Where(i => !i.IsDeleted).Where(i => !i.IsArchived)
               .Include(i => i.UnitTypes)
                   .ThenInclude(i => i.Attributes)
               .Include(i => i.UnitTypes)
                   .ThenInclude(i => i.MasterUnitType)
               .Include(i => i.UnitTypes)
                   .ThenInclude(i => i.UnitInfoGalleries)
               .Include(i => i.Blocks)
               .Include(i => i.ProjectGalleries)
               .Include(i => i.ProjectType)
               .Include(i => i.MonetaryInfo)
               .Include(i => i.Address)
                   .ThenInclude(i => i.Location)
                       .ThenInclude(i => i.Zone)
                           .ThenInclude(i => i.City)
               .Include(i => i.BuilderDetail)
               .Include(i => i.AssociatedBanks)
               .Include (i => i.UserAssignment)
               .Where(i => i.Id == id);
        }
        public ProjectByIdSpec(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted)
               .Include(i => i.UnitTypes)
               .Include(i => i.Address)
               .Include(i => i.UserAssignment)
                 .Where(i => !i.IsDeleted && ids.Contains(i.Id));
        }
    }

    public class GetProjectByIdSpecs : Specification<Domain.Entities.Project>
    {
        public GetProjectByIdSpecs(Guid id)
        {
            Query.Include(i => i.UserAssignment).Where(i => !i.IsDeleted && !i.IsArchived && i.Id == id)
                 .Include(i => i.UserAssignments);
        }
        public GetProjectByIdSpecs(List<Guid> ids)
        {
            Query.Include(i => i.UserAssignment).Where(i => !i.IsDeleted && ids.Contains(i.Id))
                 .Include(i => i.UserAssignments);
        }
    }

    public class GetProjectByIdToUpdateSpecs : Specification<Domain.Entities.Project>
    {
        public GetProjectByIdToUpdateSpecs(Guid id)
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived)
                .Where(i => i.Id == id);
        }
    }

    public class GetProjectByIdForUnitAndBlockSpecs : Specification<Domain.Entities.Project>
    {
        public GetProjectByIdForUnitAndBlockSpecs(Guid id)
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived)
                .Where(i => i.Id == id);
        }
    }

    public class GetProjectUnitByProjectIdSpecs : Specification<Domain.Entities.Project>
    {
        public GetProjectUnitByProjectIdSpecs(Guid id)
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived)
                .Include(i => i.UnitTypes)
                .Where(i => i.Id == id);
        }
    }

    public class GetAllProjectNamesSpecs : Specification<Domain.Entities.Project>
    {
        public GetAllProjectNamesSpecs()
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived && i.CurrentStatus == ProjectCurrentStatus.Active);
        }
    }
    public class ProjectByIdsSpec : Specification<Domain.Entities.Project>
    {
        public ProjectByIdsSpec(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted && ids.Contains(i.Id));

        }
    }
    public class GetProjectByIdSpec: Specification<Domain.Entities.Project>
    {
        public GetProjectByIdSpec(Guid id)
        {
            Query.Where(i => !i.IsDeleted).Where(i => !i.IsArchived)
               .Include(i => i.Address)
                   .ThenInclude(i => i.Location)
                       .ThenInclude(i => i.Zone)
                           .ThenInclude(i => i.City)
                .Include(i => i.MonetaryInfo)
               .Where(i => i.Id == id);
        }
    }
    public class GetProjectByIdForGallerySpec : Specification<Domain.Entities.Project>
    {
        public GetProjectByIdForGallerySpec(Guid id)
        {
            Query.Where(i => i.Id == id && !i.IsDeleted).Where(i => !i.IsArchived)
               .Include(i => i.ProjectGalleries).Where(i => !i.IsDeleted);
        }
    }
    public class GetProjectAssignmentSpec : Specification<Domain.Entities.Project>
    {
        public GetProjectAssignmentSpec(Guid id) =>
           Query.Where(p => !p.IsDeleted && p.Id == id)
           .Include(i => i.UserAssignment);
    }

    public class GetProjectByIdToUpdateSpecsV1 : Specification<Domain.Entities.Project>
    {
        public GetProjectByIdToUpdateSpecsV1(Guid id)
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived)
                .Where(i => i.Id == id)
                .Include(i => i.ProjectType)
                .Include(i => i.Blocks)
                .Include(i => i.UnitTypes)
                .ThenInclude(i=>i.Attributes)
                .Include(i => i.AssociatedBanks)
                .Include(i => i.ProjectGalleries)
                .Include(i=>i.UnitTypes)
                .ThenInclude(i=>i.UnitInfoGalleries)
                .Include(i => i.Amenities)
                .Include(i => i.UserAssignment)
                .ThenInclude(i => i.Module)
                .Include(i => i.UserAssignments)
                .ThenInclude(i => i.Module)
                .Include(id => id.BuilderDetail)
                .Include(i => i.MonetaryInfo)
                .Include(i => i.Address)
                ;
        }
    }
    public class GetCustomFieldValuesByProjectId : Specification<Domain.Entities.CustomFieldValue>
    {
        public GetCustomFieldValuesByProjectId(Guid projectId)
        {
            Query.Where(i => !i.IsDeleted && i.EntityId==projectId);
        }
    }
}
