﻿using Lrb.Application.Lead.Web;
using Lrb.Application.Property.Web;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.MasterData;

namespace Lrb.Application.Integration.Web.Helpers
{
        public static class MIcrositeHelper
        {
            public static LeadEnquiry MicrositeEnquiryMapping(Lrb.Domain.Entities.Property? property)
            {
                var enquiryType = EnquiryType.None;
                if (property?.EnquiredFor == EnquiryType.Sale)
                {
                    enquiryType = EnquiryType.Buy;
                }
                else
                {
                    enquiryType = property?.EnquiredFor ?? EnquiryType.None;
                }
                var enquiry = new LeadEnquiry()
                {
                    IsPrimary = true,
                    //EnquiredFor = enquiryType,
                    EnquiryTypes = new List<EnquiryType> { enquiryType },
                    SaleType = property?.SaleType,
                    //BHKType = property?.BHKType ?? BHKType.None,
                    BHKTypes = new List<BHKType> { property?.BHKType ?? BHKType.None },
                    NoOfBHK = property?.NoOfBHK ?? default,
                    // NoOfBHKs = property?.NoOfBHKs ?? default,
                    BHKs = new List<double> { property?.NoOfBHKs ?? default },
                    PossessionDate = property?.PossessionDate,
                    LeadSource = LeadSource.PropertyMicrosite,
                    CarpetArea = property?.Dimension?.CarpetArea,
                    CarpetAreaUnitId = property?.Dimension?.CarpetAreaId ?? Guid.Empty,
                    Area = property?.Dimension?.Area ?? default,
                    AreaUnitId = property?.Dimension?.AreaUnitId ?? Guid.Empty,
                    //Address = property?.Address,
                    PropertyType = property?.PropertyType,
                    PropertyTypes = new List<MasterPropertyType> { property?.PropertyType },
                    UpperBudget = property?.MonetaryInfo?.ExpectedPrice,
                    LowerBudget = property?.MonetaryInfo?.ExpectedPrice,
                    NetArea = property?.Dimension?.NetArea,
                    NetAreaUnitId = property?.Dimension?.NetAreaUnitId ?? Guid.Empty,
                    PropertyArea = property?.Dimension?.Area,
                    PropertyAreaUnitId = property?.Dimension?.AreaUnitId ?? Guid.Empty,
                    SaleableArea = property?.Dimension?.SaleableArea,
                    SaleableAreaUnitId = property?.Dimension?.SaleableAreaId ?? Guid.Empty,
                    BuiltUpArea = property?.Dimension?.BuildUpArea,
                    BuiltUpAreaUnitId = property?.Dimension?.BuildUpAreaId ?? Guid.Empty,
                };
                if (property?.Address != null)
                {
                    enquiry.Addresses = new List<Address> { property.Address };
                }
                try
                {
                    if(property?.NoOfBHKs != null  && property.NoOfBHKs > 0)
                    {
                        enquiry.Beds = new List<int> { Convert.ToInt32(property.NoOfBHKs) };
                    }
                    if (property?.OfferingType != null)
                    {
                        enquiry.OfferType = (OfferType?)property.OfferingType;
                    }
                    if (property?.FurnishStatus != null)
                    {
                        enquiry.Furnished = (FurnishStatus?)property.FurnishStatus;
                    }
                }
                catch (Exception ex)
                {
                
                }
                return enquiry;
            }

            public static LeadEnquiry MicrositeEnquiryMapping(Lrb.Domain.Entities.UnitType? unit)
        {
            return new LeadEnquiry()
            {
                IsPrimary = true,
                NoOfBHKs = unit?.NoOfBHK ?? default,
                BHKType = unit?.BHKType ?? BHKType.None,
                LeadSource = LeadSource.ProjectMicrosite,
                CarpetArea = unit?.CarpetArea,
                CarpetAreaUnitId = unit?.CarpetAreaUnitId ?? Guid.Empty,
                Area = unit?.Area ?? default,
                AreaUnitId = unit?.AreaUnitId ?? Guid.Empty,
                BuiltUpArea = unit?.BuildUpArea,
                BuiltUpAreaUnitId = unit?.BuildUpAreaId?? Guid.Empty,
                Furnished = (FurnishStatus?)unit?.FurnishingStatus,
                BHKs = new List<double> { unit?.NoOfBHK ?? default },
            };
        }
    }
}
