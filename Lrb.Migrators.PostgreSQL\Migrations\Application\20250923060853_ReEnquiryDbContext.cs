﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class ReEnquiryDbContext : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsReEnquired",
                schema: "LeadratBlack",
                table: "Leads",
                type: "boolean",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "LeadReEnquiry",
                schema: "LeadratBlack",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Notes = table.Column<string>(type: "text", nullable: true),
                    LeadId = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LeadReEnquiry", x => x.Id);
                    table.ForeignKey(
                        name: "FK_LeadReEnquiry_Leads_LeadId",
                        column: x => x.LeadId,
                        principalSchema: "LeadratBlack",
                        principalTable: "Leads",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_LeadReEnquiry_LeadId",
                schema: "LeadratBlack",
                table: "LeadReEnquiry",
                column: "LeadId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "LeadReEnquiry",
                schema: "LeadratBlack");

            migrationBuilder.DropColumn(
                name: "IsReEnquired",
                schema: "LeadratBlack",
                table: "Leads");
        }
    }
}
