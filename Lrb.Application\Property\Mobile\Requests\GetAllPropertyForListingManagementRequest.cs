﻿using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.Property.Mobile.Specs;
using Lrb.Application.Property.Web;
using Lrb.Application.Property.Web.Requests;
using Lrb.Domain.Entities.AmenitiesAttributes;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;

namespace Lrb.Application.Property.Mobile
{
    public class GetAllListingManagementParameter : PaginationFilter
    {
        public PropertyVisiblity PropertyVisiblity { get; set; }
        public FirstLevelFilter FirstLevelFilter { get; set; }
        public SecondLevelFilter? SecondLevelFilter { get; set; }
        public PropertyDimensionDto? PropertySize { get; set; }
        public EnquiryType? EnquiredFor { get; set; }
        public double? NoOfBHK { get; set; }
        public List<double>? BHKs { get; set; }
        public string? Ratings { get; set; }
        public PropertyStatus? PropertyStatus { get; set; }
        public List<Guid>? PropertyTypes { get; set; }
        public List<Guid>? PropertySubTypes { get; set; }
        public Guid? BasePropertyTypeId { get; set; }
        public Guid? SubPropertyTypeId { get; set; }
        //public List<PropertyPriceFilter>? PriceRange { get; set; }
        public string? PropertySearch { get; set; }
        public long? MinPrice { get; set; }
        public long? MaxPrice { get; set; }
        public long? MinBudget { get; set; }
        public long? MaxBudget { get; set; }
        public PropertyDateType? DateType { get; set; }
        public List<string>? Locations { get; set; }
        public List<string>? Cities { get; set; }
        public List<string>? States { get; set; }
        public List<Guid>? SubPropertyTypeIds { get; set; }
        public List<Guid>? Amenities { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<BHKType>? BHKTypes { get; set; }
        public List<string>? Projects { get; set; }
        public List<FurnishStatus>? FurnishStatuses { get; set; }
        public List<SaleType>? SaleTypes { get; set; }
        public string? OwnerName { get; set; }
        public string? PropertyTitle { get; set; }
        public DateTime? FromPossessionDate { get; set; }
        public DateTime? ToPossessionDate { get; set; }
        public Facing? Facing { get; set; }
        public List<int>? NoOfBathrooms { get; set; }
        public List<int>? NoOfLivingrooms { get; set; }
        public List<int>? NoOfBedrooms { get; set; }
        public List<string>? NoOfFloors { get; set; }
        public List<int>? NoOfUtilites { get; set; }
        public List<int>? NoOfKitchens { get; set; }
        public List<int>? NoOfBalconies { get; set; }
        public List<int>? NoOfFloor { get; set; }
        public int? FloorNumber { get; set; }
        public List<string>? OwnerNames { get; set; }
        public string? Currency { get; set; }
        public bool? IsWithTeam { get; set; }
        public List<Guid>? UserIds { get; set; }
        public ViewAssignmentsPermission? Permission { get; set; }
        public List<Guid>? ListingSourceIds { get; set; }
        public List<string>? Communities { get; set; }
        public List<string>? SubCommunities { get; set; }
        public CompletionStatus? CompletionStatus { get; set; }
        public ListingLevel? ListingLevel { get; set; }

        public double? MinPropertySize { get; set; }
        public double? MaxPropertySize { get; set; }
        public Guid? PropertySizeUnit { get; set; }

        public double? MinCarpetArea { get; set; }
        public double? MaxCarpetArea { get; set; }
        public Guid? CarpetAreaUnit { get; set; }

        public double? MinBuitUpArea { get; set; }
        public double? MaxBuitUpArea { get; set; }
        public Guid? BuitUpAreaUnit { get; set; }

        public double? MinSaleableArea { get; set; }
        public double? MaxSaleableArea { get; set; }
        public Guid? SaleableAreaUnit { get; set; }
        public string? SerialNo { get; set; }
        public double? MinNetArea { get; set; }
        public double? MaxNetArea { get; set; }
        public Guid? NetAreaUnit { get; set; }
        public List<Guid>? ListingOnBehalf { get; set; }
        public bool? IsListingOnBehalf { get; set; }
        public int? MinLeadCount { get; set; } 
        public int? MaxLeadCount { get; set; } 
        public int? MaxProspectCount { get; set; } 
        public int? MinProspectCount { get; set; } 

        public PossesionType? PossesionType { get; set; }
        public DateTime? FromPossesionDate { get; set; }
        public DateTime? ToPossesionDate { get; set; }
        public List<int>? Parking { get; set; }
        public List<string>? Countries { get; set; }
        public string? ReferenceNo { get; set; }

    }

    public class GetAllPropertyForListingManagementRequest : GetAllListingManagementParameter, IRequest<PagedResponse<GetAllPropertyForListingManagementDTO, string>>
    {
    }

    public class GetAllPropertyForListingManagementRequestHandler : IRequestHandler<GetAllPropertyForListingManagementRequest, PagedResponse<GetAllPropertyForListingManagementDTO, string>>
    {
        private readonly IReadRepository<Domain.Entities.Property> _propertyRepo;
        private readonly IReadRepository<CustomMasterAttribute> _masterPropertyAttributeRepo;
        private readonly IReadRepository<CustomMasterAmenity> _masterPropertyAmenityRepo;
        private readonly IReadRepository<MasterPropertyType> _masterPropertyTypeRepo;
        private readonly IReadRepository<MasterAreaUnit> _masterAreaUnitRepo;
        private readonly IReadRepository<PropertyDimension> _propertyDimensionRepo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        private readonly IPropertyRepository _propertyRepository;

        public GetAllPropertyForListingManagementRequestHandler(
            IReadRepository<Domain.Entities.Property> propertyRepo,
            IReadRepository<CustomMasterAttribute> masterPropertyAttributeRepo,
            IReadRepository<CustomMasterAmenity> masterPropertyAmenityRepo,
            IReadRepository<MasterPropertyType> masterPropertyTypeRepo,
            IReadRepository<MasterAreaUnit> masterAreaUnitRepo,
            IReadRepository<PropertyDimension> propertyDimensionRepo,
            ILeadRepositoryAsync leadRepositoryAsync,
            IDapperRepository dapperRepository,
            ICurrentUser currentUser,
            IPropertyRepository propertyRepository
            )
        {
            _propertyRepo = propertyRepo;
            _masterPropertyAttributeRepo = masterPropertyAttributeRepo;
            _masterPropertyAmenityRepo = masterPropertyAmenityRepo;
            _masterPropertyTypeRepo = masterPropertyTypeRepo;
            _masterAreaUnitRepo = masterAreaUnitRepo;
            _propertyDimensionRepo = propertyDimensionRepo;
            _leadRepositoryAsync = leadRepositoryAsync;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
            _propertyRepository = propertyRepository;
        }
        public async Task<PagedResponse<GetAllPropertyForListingManagementDTO, string>> Handle(GetAllPropertyForListingManagementRequest request, CancellationToken cancellationToken)
        {
            if (request != null && request.Permission == ViewAssignmentsPermission.None)
            {
                return new PagedResponse<GetAllPropertyForListingManagementDTO, string>(null, 0);
            }
            CustomMasterAttribute? masterPropertyAttribute = new();
            CustomMasterAmenity? masterPropertyAmenity = new();
            MasterPropertyType? masterPropertyType = new();
            if (!string.IsNullOrWhiteSpace(request.PropertySearch))
            {
                masterPropertyAttribute = (await _masterPropertyAttributeRepo.ListAsync(new GetMasterPropertyAttributeSpec(request?.PropertySearch?.Trim().ToLower() ?? string.Empty), cancellationToken)).FirstOrDefault();
                masterPropertyAmenity = (await _masterPropertyAmenityRepo.ListAsync(new GetMasterPropertyAmenitySpec(request?.PropertySearch?.Trim().ToLower() ?? string.Empty), cancellationToken)).FirstOrDefault();
                masterPropertyType = (await _masterPropertyTypeRepo.ListAsync(new GetMasterPropertyTypeSpec(request?.PropertySearch?.Trim().ToLower() ?? string.Empty), cancellationToken)).FirstOrDefault();
            }

            List<Guid>? propertyDimensionIds = new();
            //if (request != null && request.PropertySize != null && request.PropertySize.AreaUnitId != default)
            //{
            //    var masterAreaUnit = await _masterAreaUnitRepo.GetByIdAsync(request.PropertySize.AreaUnitId);
            //    request.PropertySize.ConversionFactor = masterAreaUnit?.ConversionFactor ?? default;
            //    var area = request.PropertySize.Area * request.PropertySize.ConversionFactor;
            //    propertyDimensionIds = (await _propertyDimensionRepo.ListAsync()).Where(i => (i.Area * PropertySearchHelper.GetConversionFactor(i.AreaUnitId, _masterAreaUnitRepo).Result) == area).Select(i => i.Id).ToList();
            //}
            //if (request != null && request.PropertySize != null && request.PropertySize.CarpetAreaId != default)
            //{
            //    var masterAreaUnit = await _masterAreaUnitRepo.GetByIdAsync(request.PropertySize.CarpetAreaId);
            //    request.PropertySize.ConversionFactor = masterAreaUnit?.ConversionFactor ?? default;
            //    var Carpetarea = request.PropertySize.CarpetArea * request.PropertySize.ConversionFactor;
            //    propertyDimensionIds = (await _propertyDimensionRepo.ListAsync()).Where(i => (i.CarpetArea * PropertySearchHelper.GetConversionFactor(i.CarpetAreaId ?? Guid.Empty, _masterAreaUnitRepo).Result) == Carpetarea).Select(i => i.Id).ToList();
            //}

            //if (request != null && request.PropertySize != null && request.PropertySize.BuildUpAreaId != default)
            //{
            //    var masterAreaUnit = await _masterAreaUnitRepo.GetByIdAsync(request.PropertySize.BuildUpAreaId);
            //    request.PropertySize.ConversionFactor = masterAreaUnit?.ConversionFactor ?? default;
            //    var builduparea = request.PropertySize.BuildUpArea * request.PropertySize.ConversionFactor;
            //    propertyDimensionIds = (await _propertyDimensionRepo.ListAsync()).Where(i => (i.BuildUpArea * PropertySearchHelper.GetConversionFactor(i.BuildUpAreaId ?? Guid.Empty, _masterAreaUnitRepo).Result) == builduparea).Select(i => i.Id).ToList();
            //}
            //if (request != null && request.PropertySize != null && request.PropertySize.SaleableAreaId != default)
            //{
            //    var masterAreaUnit = await _masterAreaUnitRepo.GetByIdAsync(request.PropertySize.SaleableAreaId);
            //    request.PropertySize.ConversionFactor = masterAreaUnit?.ConversionFactor ?? default;
            //    var saleblearea = request.PropertySize.SaleableArea * request.PropertySize.ConversionFactor;
            //    propertyDimensionIds = (await _propertyDimensionRepo.ListAsync()).Where(i => (i.SaleableArea * PropertySearchHelper.GetConversionFactor(i.SaleableAreaId ?? Guid.Empty, _masterAreaUnitRepo).Result) == saleblearea).Select(i => i.Id).ToList();
            //}
            //if (request != null && request.PropertySize != null && request.PropertySize.NetAreaUnitId != default)
            //{
            //    var masterAreaUnit = await _masterAreaUnitRepo.GetByIdAsync(request.PropertySize.NetAreaUnitId);
            //    request.PropertySize.ConversionFactor = masterAreaUnit?.ConversionFactor ?? default;
            //    var netAreaId = request.PropertySize.NetArea * request.PropertySize.ConversionFactor;
            //    propertyDimensionIds = (await _propertyDimensionRepo.ListAsync()).Where(i => (i.NetArea * PropertySearchHelper.GetConversionFactor(i.NetAreaUnitId ?? Guid.Empty, _masterAreaUnitRepo).Result) == netAreaId).Select(i => i.Id).ToList();
            //}
            var tenantId = _currentUser.GetTenant();
            var currentUserId = _currentUser.GetUserId();
            List<Guid>? userIds = new();
            List<Guid>? filterIds = new();
            List<Guid>? teamUserIds = new();
            bool showAllProperties = false;
            try
            {
                switch (request.Permission)
                {
                    case ViewAssignmentsPermission.View:
                        if (request.UserIds?.Any() ?? false)
                        {
                            filterIds.AddRange(request.UserIds);
                            if (request.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty))?.ToList() ?? new();
                                filterIds.AddRange(teamUserIds);
                            }
                            userIds.AddRange(filterIds);
                        }
                        else
                        {
                            userIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                            showAllProperties = true;
                        }
                        break;
                    case ViewAssignmentsPermission.ViewAssigned:
                        userIds.Add(currentUserId);
                        break;
                    default:
                        if (request.UserIds?.Any() ?? false)
                        {
                            filterIds.AddRange(request.UserIds);
                            if (request.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty))?.ToList() ?? new();
                                filterIds.AddRange(teamUserIds);
                            }
                            userIds.AddRange(filterIds);
                        }
                        else
                        {
                            userIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                            showAllProperties = true;
                        }
                        break;
                }

            }
            catch (Exception ex)
            {
            }
            List<int> noOfAttributes = Enumerable.Range(1, 5).ToList();
            NumericAttributesDto numericAttributeDto = InitializationOfNumericAttributes(noOfAttributes, request);
            //var properties = await _propertyRepo.ListAsync(new PropertyForListingManagementSpecs(request, masterPropertyAttribute?.Id ?? null, masterPropertyAmenity?.Id ?? null, masterPropertyType?.Id ?? null, propertyDimensionIds, numericAttributeDto, userIds, showAllProperties));
            //var totalCount = await _propertyRepo.CountAsync(new PropertyForListingManagementSpecs(request, masterPropertyAttribute?.Id ?? null, masterPropertyAmenity?.Id ?? null, masterPropertyType?.Id ?? null, propertyDimensionIds, numericAttributeDto, userIds, showAllProperties));
            List<Guid> propertyIds = new();
            if (request.MinLeadCount != null || request.MaxLeadCount != null ||
                request.MinProspectCount != null || request.MaxProspectCount != null)
            {
                var property = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<PropertyIdsDto>("LeadratBlack", "Lead&Prospects_PropertiesAssociatedCountFilter",
                    new
                    {
                        tenantid = tenantId,
                        minprospectcount = request.MinProspectCount,
                        maxprospectcount = request.MaxProspectCount,
                        minleadcount = request.MinLeadCount,
                        maxleadcount = request.MaxLeadCount
                    })).FirstOrDefault()?.PropertyIds ?? new List<Guid>();
                propertyIds = property.ToList();
            }
            List<CustomPropertyAttributeDto> attributes = new();
            if (request?.NoOfFloors != null ||  request?.NoOfKitchens != null || request?.NoOfUtilites != null || request?.NoOfBedrooms != null || request?.NoOfLivingrooms != null || request?.NoOfBalconies != null || request?.NoOfBathrooms != null || request?.Parking != null)
            {
                attributes = await _dapperRepository.GetAttributeDetails(tenantId ?? string.Empty);
            }
            Guid? propertyId = null;
            if (request?.ReferenceNo != null)
            {
                propertyId = await _dapperRepository.GetPropertyListingIdBySerialNoOrCustomRefNo(request?.ReferenceNo ?? string.Empty, tenantId);
            }
            var properties = await _propertyRepository.GetAllPropertiesForMobileListingManagementAsync(request, masterPropertyAttribute?.Id ?? null, masterPropertyAmenity?.Id ?? null, masterPropertyType?.Id ?? null, propertyDimensionIds, numericAttributeDto, userIds, showAllProperties, tenantId, propertyIds, attributes, propertyId);
            var totalCount = await _propertyRepository.GetAllPropertiesForMobileListingManagementCountAsync(request, masterPropertyAttribute?.Id ?? null, masterPropertyAmenity?.Id ?? null, masterPropertyType?.Id ?? null, propertyDimensionIds, numericAttributeDto, userIds, showAllProperties, tenantId, propertyIds, attributes, propertyId);
            List<GetAllPropertyForListingManagementDTO> getAllPropertyDTOs = new List<GetAllPropertyForListingManagementDTO>();
            getAllPropertyDTOs = properties.Adapt<List<GetAllPropertyForListingManagementDTO>>();
            List<GetAllPropertyForListingManagementDTO> resultDtos = getAllPropertyDTOs.Where(i => i.Status != PropertyStatus.Sold).ToList();
            resultDtos.AddRange(getAllPropertyDTOs.Where(i => i.Status == PropertyStatus.Sold).ToList());
            return new PagedResponse<GetAllPropertyForListingManagementDTO, string>(getAllPropertyDTOs, totalCount);
        }

        private NumericAttributesDto InitializationOfNumericAttributes(List<int> noOfAttributes, GetAllPropertyForListingManagementRequest request)
        {
            return new NumericAttributesDto
            {
                NoOfFloor = FilterNumericAttributes(request.NoOfFloor, noOfAttributes),
                NoOfBathrooms = FilterNumericAttributes(request.NoOfBathrooms, noOfAttributes),
                NoOfBedrooms = FilterNumericAttributes(request.NoOfBedrooms, noOfAttributes),
                NoOfKitchens = FilterNumericAttributes(request.NoOfKitchens, noOfAttributes),
                NoOfUtilites = FilterNumericAttributes(request.NoOfUtilites, noOfAttributes),
                NoOfLivingrooms = FilterNumericAttributes(request.NoOfLivingrooms, noOfAttributes),
                NoOfBalconies = FilterNumericAttributes(request.NoOfBalconies, noOfAttributes),
                NoOfFloors = FilterNumericAttributesV1(request.NoOfFloors, noOfAttributes),
                Parking  =FilterNumericAttributes(request.Parking, noOfAttributes),

            };
        }

        private NoOfAttributeFilterDto FilterNumericAttributesV1(List<string>? requestValues, List<int> noOfAttributes)
        {
            NoOfAttributeFilterDto noOfAttributesDto = new NoOfAttributeFilterDto();
            var noOfFloorFilterList = new List<int>();
            var selectedAttributes = new List<string>();
            if (requestValues != null)
            {
                if (requestValues.Contains("Ground Floor"))
                {
                    selectedAttributes.Add("Ground Floor");
                }
                if (requestValues.Contains("5"))
                {
                    noOfFloorFilterList = noOfAttributes.Where(i => !requestValues.Contains(i.ToString())).ToList();
                    selectedAttributes.AddRange(noOfFloorFilterList.Select(i => i.ToString()));
                    noOfAttributesDto.IsMaxValueIncluded = true;
                }
                else
                {
                    noOfFloorFilterList = noOfAttributes.Where(i => requestValues.Contains(i.ToString())).ToList();
                    selectedAttributes.AddRange(noOfFloorFilterList.Select(i => i.ToString()));
                    noOfAttributesDto.IsMaxValueIncluded = false;
                }
                noOfAttributesDto.NoOfAttributes = selectedAttributes;
            }

            return noOfAttributesDto;
        }

        private NoOfAttributeFilterDto FilterNumericAttributes(List<int>? requestValues, List<int> noOfAttributes)
        {
            NoOfAttributeFilterDto noOfAttributesDto = new NoOfAttributeFilterDto();
            var noOfFloorFilterList = new List<int>();

            if (requestValues != null)
            {
                if (requestValues.Contains(5))
                {
                    noOfFloorFilterList = noOfAttributes.Where(i => !requestValues.Contains(i)).ToList();
                    noOfAttributesDto.NoOfAttributes = noOfFloorFilterList.Select(i => i.ToString()).ToList();
                    noOfAttributesDto.IsMaxValueIncluded = true;
                }
                else
                {
                    noOfFloorFilterList = noOfAttributes.Where(i => requestValues.Contains(i)).ToList();
                    noOfAttributesDto.NoOfAttributes = noOfFloorFilterList.Select(i => i.ToString()).ToList();
                    noOfAttributesDto.IsMaxValueIncluded = false;
                }
            }

            return noOfAttributesDto;
        }
    }

    public enum PropertyVisiblity
    {
        All,
        Draft,
        Approved,
        Refused,
        Archived,
        Sold
    }
    public enum FirstLevelFilter
    {
        All,
        Ready,
        OffPlan,
        Secondary
    }
    public enum SecondLevelFilter
    {
        All,
        Residential,
        Commercial,
        Agricultural
    }

}
