﻿using Lrb.Application.Identity.Users;
using Lrb.Application.Project.Web;
using Lrb.Application.Property.Web;
using Lrb.Application.Team.Web;

namespace Lrb.Application.Lead.Web
{
    public class GetLeadByIdRequest : IRequest<Response<ViewLeadDto>>
    {
        public Guid Id { get; set; }
        public GetLeadByIdRequest(Guid id) => Id = id;
    }
    public class GetByIdRequestHandler : IRequestHandler<GetLeadByIdRequest, Response<ViewLeadDto>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        private readonly IUserService _userService;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;

        public GetByIdRequestHandler(
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepo,
            IUserService userService,
            IDapperRepository dapperRepository,
            ICurrentUser currentUser)
        {
            _leadRepo = leadRepo;
            _userService = userService;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        public async Task<Response<ViewLeadDto>> Handle(GetLeadByIdRequest request, CancellationToken cancellationToken)
        {
            var lead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(request.Id), cancellationToken);
            if (lead == null)
            {
                throw new NotFoundException("No lead found by this Id");
            }

            //leadDto = AddAppointmentDocuments(leadDto);
            string? tenantId = _currentUser.GetTenant();
            Guid userId = _currentUser.GetUserId();
            if (!(await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty)))
            {
                // lead.Appointments = lead.Appointments != null && lead.Appointments.Any() ? lead.Appointments.Where(appointment => appointment.UserId == userId).DistinctBy(i => i.UniqueKey).ToList() : new();
                if (lead?.Appointments?.Any() ?? false)
                {
                    var appointmentsWithoutUniqueKey = lead.Appointments?.Where(appointment => appointment.UserId == userId)?.Where(i => i.UniqueKey == null && i.UniqueKey == default).ToList() ?? new();
                    var appointmentsWithUniqueKey = lead.Appointments?.Where(appointment => appointment.UserId == userId)?.Where(i => i.UniqueKey != null && i.UniqueKey != default)?.DistinctBy(i => i.UniqueKey).ToList() ?? new();

                    appointmentsWithoutUniqueKey.AddRange(appointmentsWithUniqueKey);
                    lead.Appointments = appointmentsWithoutUniqueKey;
                }
            }
            else
            {
                if (lead?.Appointments?.Any(i => i.UniqueKey == Guid.Empty || i.UniqueKey == null) ?? false)
                {
                    var appointmentsWithoutUniqueKey = lead.Appointments?.Where(i => i.UniqueKey == null && i.UniqueKey == default).ToList() ?? new();
                    var appointmentsWithUniqueKey = lead.Appointments?.Where(i => i.UniqueKey != null && i.UniqueKey != default)?.DistinctBy(i => i.UniqueKey).ToList() ?? new();

                    appointmentsWithoutUniqueKey.AddRange(appointmentsWithUniqueKey);
                    lead.Appointments = appointmentsWithoutUniqueKey;
                }
            }
            if (lead?.BookedDetails?.Any() ?? false)
            {
                lead.BookedDetails = lead.BookedDetails.OrderByDescending(i => i.LastModifiedOn).ToList();
                List<LeadBookedDetail> details = new();
                var book = lead.BookedDetails.FirstOrDefault();
                details.Add(book);
                lead.BookedDetails = details;

            }
            var leadDto = lead.Adapt<ViewLeadDto>();

            if (lead.BookedDetails?.FirstOrDefault()?.Properties?.Any() ?? false)
            {
                leadDto.BookedDetails.FirstOrDefault().Property = lead.BookedDetails.FirstOrDefault().Properties.FirstOrDefault().Adapt<BasicPropertyInfoDto>();
            }
            if (lead.BookedDetails?.FirstOrDefault()?.Projects?.Any() ?? false)
            {
                leadDto.BookedDetails.FirstOrDefault().Projects = lead.BookedDetails.FirstOrDefault().Projects.FirstOrDefault().Adapt<BasicProjectDto>();
            }
            //leadDto.Appointments = leadDto.Appointments?.Where(i => i.Type != AppointmentType.None).ToList();
            //leadDto = AddAppointmentDocuments(leadDto);
            var userIds = new List<string>() { (leadDto?.AssignTo ?? Guid.Empty).ToString(), (leadDto?.SecondaryUserId ?? Guid.Empty).ToString() };
            var users = await _userService.GetListOfUsersByIdsAsync(userIds, cancellationToken);
            leadDto.ContactRecords = lead.ContactRecords;
            var assignedUser = users?.FirstOrDefault(i => i.Id == (leadDto?.AssignTo ?? Guid.Empty));
            var secondaryUser = users?.FirstOrDefault(i => i.Id == (leadDto?.SecondaryUserId ?? Guid.Empty));
            if (assignedUser != null)
            {
                leadDto.AssignedUser = assignedUser.Adapt<UserDto>();
            }
            if (secondaryUser != null)
            {
                leadDto.SecondaryUser = secondaryUser.Adapt<UserDto>();
            }
            //if (leadDto.AssignTo != Guid.Empty)
            //{
            //    var user = await _userService.GetAsync(leadDto.AssignTo.ToString(), cancellationToken);
            //    leadDto.AssignedUser = user.Adapt<UserDto>();
            //}
            var res = (await _dapperRepository.GetLeadSourceUpdateStatusAsync(lead.Id, tenantId ?? string.Empty));
            leadDto.IsSourceUpdated = res > 1 ? true : false;
            //LeadHistory? leadHistory = await _leadHistoryRepo.GetByIdAsync(lead.Id, cancellationToken);
            //Dictionary<int, MeetingOrVisitDto> meetings = new();
            //Dictionary<int, MeetingOrVisitDto> siteVisits = new();
            //if (leadHistory != null)
            //{
            //    var isMeetingDone = leadHistory.IsMeetingDone;
            //    if (isMeetingDone?.Any() ?? false)
            //    {
            //        foreach (var meeting in isMeetingDone)
            //        {
            //            var lastModifiedBy = leadHistory.LastModifiedBy?.Where(i => i.Key == meeting.Key).FirstOrDefault().Value ?? default;
            //            var lastModifiedDate = leadHistory.ModifiedDate?.Where(i => i.Key == meeting.Key).FirstOrDefault().Value ?? default;
            //            var addressId = leadHistory.MeetingLocation?.Where(i => i.Key == meeting.Key).FirstOrDefault().Value ?? default;
            //            var address = await _addressRepo.GetByIdAsync(addressId, cancellationToken);
            //            meetings.Add(meetings.Count + 1, new MeetingOrVisitDto()
            //            {
            //                IsDone = meeting.Value,
            //                Address = address,
            //                Date = lastModifiedDate,
            //                DoneByUser = lastModifiedBy
            //            });
            //        }
            //    }
            //    var isSiteVisitDone = leadHistory.IsSiteVisitDone;
            //    if (isSiteVisitDone?.Any() ?? false)
            //    {
            //        foreach (var siteVisit in isSiteVisitDone)
            //        {
            //            var lastModifiedBy = leadHistory.LastModifiedBy?.Where(i => i.Key == siteVisit.Key).FirstOrDefault().Value ?? default;
            //            var lastModifiedDate = leadHistory.ModifiedDate?.Where(i => i.Key == siteVisit.Key).FirstOrDefault().Value ?? default;
            //            var addressId = leadHistory.SiteLocation?.Where(i => i.Key == siteVisit.Key).FirstOrDefault().Value ?? default;
            //            var address = await _addressRepo.GetByIdAsync(addressId, cancellationToken);
            //            siteVisits.Add(siteVisits.Count + 1, new MeetingOrVisitDto()
            //            {
            //                IsDone = siteVisit.Value,
            //                Address = address,
            //                Date = lastModifiedDate,
            //                DoneByUser = lastModifiedBy
            //            });
            //        }
            //    }
            //    leadDto.MeetingsDone = meetings.Select(i => i.Value).ToList().Where(i => i.IsDone ?? false).ToList();
            //    leadDto.MeetingsNotDone = meetings.Select(i => i.Value).ToList().Where(i => !i.IsDone ?? false).ToList();
            //    leadDto.SiteVisitsDone = siteVisits.Select(i => i.Value).ToList().Where(i => i.IsDone ?? false).ToList();
            //    leadDto.SiteVisitsNotDone = siteVisits.Select(i => i.Value).ToList().Where(i => !i.IsDone ?? false).ToList();
            //}
            return new Response<ViewLeadDto>(leadDto);
        }
    }
}
