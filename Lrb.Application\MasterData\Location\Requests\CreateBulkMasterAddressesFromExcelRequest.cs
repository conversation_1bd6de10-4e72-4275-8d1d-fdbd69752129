﻿using Amazon.DynamoDBv2.Model;
using Lrb.Application.Common.AWS_Batch;
using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Common.ServiceBus;
using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.ListingManagement.Web.Dtos;
using Lrb.Application.ListingManagement.Web.Specs;
using Lrb.Application.Utils;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities;
using Newtonsoft.Json;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Domain.Entities.MasterData.Location;

namespace Lrb.Application.MasterData.Location.Requests
{
    public class CreateBulkMasterAddressesFromExcelRequest : IRequest<Response<bool>>
    {
        public string? S3BucketKey { get; set; }
        public string? SheetName { get; set; }
        public Dictionary<ListingSourceAddressDataColumn, string>? MappedColumnsData { get; set; }
        public Guid ListingSourceId { get; set; }
        public string? FileName { get; set; }

    }

    public class CreateBulkMasterAddressesFromExcelRequestHandler : IRequestHandler<CreateBulkMasterAddressesFromExcelRequest, Response<bool>>
    {
        public const string type = "masterAddress";
        private readonly IBlobStorageService _blobStorageService;
        private readonly IRepositoryWithEvents<MasterAddressDirectory> _masterAddressRepo;

        public CreateBulkMasterAddressesFromExcelRequestHandler(
            IBlobStorageService blobStorageService,
            IRepositoryWithEvents<MasterAddressDirectory> masterAddressRepo
            )
        {
            _blobStorageService = blobStorageService;
            _masterAddressRepo = masterAddressRepo;
        }

        public async Task<Response<bool>> Handle(CreateBulkMasterAddressesFromExcelRequest request, CancellationToken cancellationToken)
        {
            
            try
            {
                #region Convert to Datatable

                Stream fileStream = await _blobStorageService.GetObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", request.S3BucketKey);
                DataTable dataTable = new();
                if (request.S3BucketKey.Split('.').LastOrDefault() == "csv")
                {
                    using MemoryStream memoryStream = new();
                    fileStream.CopyTo(memoryStream);
                    dataTable = CSVHelper.CSVToDataTable(memoryStream);
                }
                else
                {
                    dataTable = EPPlusExcelHelper.ConvertExcelToDataTable(fileStream, request.SheetName);
                }

                List<InvalidProspect> invalids = new();
                int totalRows = dataTable.Rows.Count;
                for (int i = totalRows - 1; i >= 0; i--)
                {
                    var row = dataTable.Rows[i];
                    if (row.ItemArray.All(i => string.IsNullOrEmpty(i.ToString())))
                    {
                        row.Delete();
                    }
                }
                if (dataTable.Rows.Count <= 0)
                {
                    throw new Exception("Excel sheet is empty. Please fill some data in the excel sheet template.");
                }
                totalRows = dataTable.Rows.Count;
                Console.WriteLine($"handler() -> Total Rows in the Excel: {dataTable.Rows.Count}");

                #endregion


                var masterAddresses = await ConvertToListingSourceAddress(dataTable,request.MappedColumnsData);
             
                if (masterAddresses.Count > 0)
                {
                    await _masterAddressRepo.AddRangeAsync(masterAddresses);
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CreateBulkMasterAddressesFromExcelRequest -> ListingSourceHandler()"
                };
                Console.WriteLine($"handler() -> Exception:  {JsonConvert.SerializeObject(ex, settings: new() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                throw;
            }
            return new(true);


        }
        public async Task<List<MasterAddressDirectory>> ConvertToListingSourceAddress(DataTable dataTable, Dictionary<ListingSourceAddressDataColumn, string> dataColumn)
        {
            List<MasterAddressDirectory> sourceAddresses = new List<MasterAddressDirectory>();
            foreach (DataRow row in dataTable.Rows)
            {
                #region GetData
                string? towerName = !dataColumn.ContainsKey(ListingSourceAddressDataColumn.TowerName) || string.IsNullOrEmpty(dataColumn[ListingSourceAddressDataColumn.TowerName]) ? string.Empty : row[dataColumn[ListingSourceAddressDataColumn.TowerName]].ToString();
                string? subCommunity = !dataColumn.ContainsKey(ListingSourceAddressDataColumn.SubCommunity) || string.IsNullOrEmpty(dataColumn[ListingSourceAddressDataColumn.SubCommunity]) ? string.Empty : row[dataColumn[ListingSourceAddressDataColumn.SubCommunity]].ToString();
                string? community = !dataColumn.ContainsKey(ListingSourceAddressDataColumn.Community) || string.IsNullOrEmpty(dataColumn[ListingSourceAddressDataColumn.Community]) ? string.Empty : row[dataColumn[ListingSourceAddressDataColumn.Community]].ToString();
                string? city = !dataColumn.ContainsKey(ListingSourceAddressDataColumn.City) || string.IsNullOrEmpty(dataColumn[ListingSourceAddressDataColumn.City]) ? string.Empty : row[dataColumn[ListingSourceAddressDataColumn.City]].ToString();
                #endregion

                MasterAddressDirectory address = new()
                {
                    TowerName = towerName,
                    SubCommunity = subCommunity,
                    Community = community,
                    City = city,
                };
                sourceAddresses.Add(address);
            }
            return sourceAddresses;
        }
        
    }
}
