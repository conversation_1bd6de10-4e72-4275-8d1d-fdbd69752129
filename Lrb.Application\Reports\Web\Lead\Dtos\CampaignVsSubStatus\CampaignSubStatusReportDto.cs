﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Reports.Web.Lead.Dtos.CampaignVsSubStatus
{
    public class CampaignvsSubStatusReportDto : IDto
    {
        public string? CampaignName { get; set; }
        public Dictionary<string, object>? BaseStatusWithSubStatusCount { get; set; }
    }
    public class LeadCampaignBySubStatusDto : IDto
    {
        public string? CampaignName { get; set; }
        public string? Status { get; set; }
        public List<StatusDto>? StatusDtos { get; set; }
    }
}
