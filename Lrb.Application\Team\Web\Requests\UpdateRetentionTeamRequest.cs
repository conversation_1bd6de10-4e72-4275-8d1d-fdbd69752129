﻿using Lrb.Application.Team.Web.Dtos;
using Lrb.Application.CustomStatus.Web;
using Lrb.Domain.Entities.MasterData;

namespace Lrb.Application.Team.Web.Requests
{
    public class UpdateRetentionTeamRequest : UpdateRetentionDto, IRequest<Response<bool>>
    {
    }

    public class UpdateRetentionTeamRequestHandler : IRequestHandler<UpdateRetentionTeamRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Team> _teamRepo;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _custLeadStatusRepo;
        private readonly IRepositoryWithEvents<TeamConfiguration> _teamConfigRepo;
        public UpdateRetentionTeamRequestHandler(
            IRepositoryWithEvents<Domain.Entities.Team> teamRepo,
            IRepositoryWithEvents<CustomMasterLeadStatus> custLeadStatusRepo,
            IRepositoryWithEvents<TeamConfiguration> teamConfigRepo
            )
        {
            _teamRepo = teamRepo;
            _custLeadStatusRepo = custLeadStatusRepo;
            _teamConfigRepo = teamConfigRepo;
        }

        public async Task<Response<bool>> Handle(UpdateRetentionTeamRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var team = await _teamRepo.FirstOrDefaultAsync(new TeamByIdSpec(request.Id ?? Guid.Empty));
                if (team == null)
                {
                    throw new InvalidOperationException("Team not found by this Id");
                }
                List<CustomMasterLeadStatus> customMasterLeadStatuses = new();
                List<CustomMasterLeadStatus> masterLeadStatuses = new();
                if (request.StatusesIds?.Any() ?? false)
                {
                    List<Guid> allStatusIds = new List<Guid>();
                    allStatusIds.AddRange(request.StatusesIds);
                    if(request.Configuration?.StatusesIds?.Any() ?? false)
                    {
                        allStatusIds.AddRange(request.Configuration.StatusesIds);
                    }
                    customMasterLeadStatuses = await _custLeadStatusRepo.ListAsync(new GetAllStatusByIdSpec(allStatusIds ?? new List<Guid>()), cancellationToken);
                    if (customMasterLeadStatuses?.Any() ?? false)
                    {
                        // customMasterLeadStatuses.AddRange(statuses);
                        masterLeadStatuses = customMasterLeadStatuses.Where(i => request.StatusesIds.Contains(i.Id)).ToList();
                    }
                }
                //if (request.SubStatusesIds?.Any() ?? false)
                //{
                //    var subStatuses = await _custLeadStatusRepo.ListAsync(new GetAllStatusByIdSpec(request.SubStatusesIds ?? new List<Guid>()), cancellationToken);
                //    if (subStatuses?.Any() ?? false)
                //    {
                //        customMasterLeadStatuses.AddRange(subStatuses);
                //    }
                //}
                team.IsRotationEnabled = request.IsRotationEnabled ?? team.IsRotationEnabled;
                team.Statuses = masterLeadStatuses;
                team.LeadAssignmentType = request.LeadAssignmentType;
                await _teamRepo.UpdateAsync(team);
                if (team.Configurations!= null && request.Configuration != null )
                {
                    var teamConfig = team.Configurations.FirstOrDefault(i => i.IsForRetention == true);
                    teamConfig = request.Configuration.Adapt(teamConfig);
                    if (request.Configuration?.StatusesIds?.Any() ?? false)
                    {
                       var postRetentionStatuses = customMasterLeadStatuses?.Where(i => request.Configuration.StatusesIds.Contains(i.Id))?.ToList();
                        teamConfig.Statuses = postRetentionStatuses;
                    }
                    teamConfig.IsForRetention = true;
                    await _teamConfigRepo.UpdateAsync(teamConfig);
                }
                else if(request.Configuration != null) 
                {
                    var newTeamConfig = request.Configuration.Adapt<TeamConfiguration>();
                        newTeamConfig.TeamId = team.Id;
                    if (request.Configuration?.StatusesIds?.Any() ?? false)
                    {
                        var postRetentionStatuses = customMasterLeadStatuses?.Where(i => request.Configuration.StatusesIds.Contains(i.Id))?.ToList();
                        newTeamConfig.Statuses = postRetentionStatuses;
                    }
                    newTeamConfig.IsForRetention = true;
                    await _teamConfigRepo.AddAsync(newTeamConfig);
                }
                return new(true);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
    }
}
