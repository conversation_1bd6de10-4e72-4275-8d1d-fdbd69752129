﻿using Lrb.Application.Lead.Mobile;
using Lrb.Application.Lead.Mobile.Dtos.v3;
using Lrb.Application.Lead.Mobile.Dtos.v4;
using Lrb.Application.Lead.Mobile.Requests.v2;
using Lrb.Application.Lead.Mobile.v2;
using Lrb.Application.Lead.Mobile.v3;

namespace Lrb.MobileApi.Host.Controllers.v4
{

    [Authorize]
    [Route("api/v4/[controller]")]
    [ApiVersionNeutral]
    public class LeadController : BaseApiController
    {
        [HttpPost]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Search leads using available filters.", "")]
        public async Task<Response<V4GetAllLeadsWrapperDto>> SearchAsync([FromBody] GetAllLeadsRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("getlLeadCategory")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all leadCategory details.", "")]
        public async Task<Response<V4LeadCategoryDto>> GetAsync([FromBody] GetLeadCategoryRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("SearchLeads")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Search lead details.", "")]
        public Task<Response<Lrb.Application.Lead.Mobile.LeadCategoryDto>> GetAsync([FromBody] SearchLeadRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpPost("getUnAssignLeads")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.ViewUnAssignedLead, LrbResource.Leads)]
        [OpenApiOperation("Get all unassign leads details.", "")]
        public async Task<PagedResponse<ViewLeadDto, string>> GetAsync([FromBody] GetUnAssignLeadsRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("archiveleads")]
        [TenantIdHeader]
        [OpenApiOperation("get archive leads")]
        public async Task<ActionResult<PagedResponse<ViewLeadDto, string>>> GetAsync([FromBody] GetArchivedLeadsRequest request)
        {
            var response = await Mediator.Send(request);
            return response;
        }
        [HttpPost("leadSearch")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Search lead details.", "")]
        public async Task<PagedResponse<ViewLeadDto, string>> GetAsync([FromBody] LeadSearchRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("getAllLeadCategory")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all leadCategory details.", "")]
        public Task<Response<Lrb.Application.Lead.Mobile.v2.LeadCategoryDto>> GetAsync([FromBody] V2GetLeadCategoryRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpPost("SearchLeads/v2")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Search lead details.", "")]
        public Task<Response<Lrb.Application.Lead.Mobile.v2.LeadCategoryDto>> GetAsync([FromBody] V2SearchLeadRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpPost("leadSearch/v2")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("lead global search.", "")]
        public async Task<PagedResponse<SearchLeadDto, string>> GetAsync([FromBody] V2LeadGlobalSearchRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("basicInfo/anonymous")]
        [AllowAnonymous]
        [OpenApiOperation("Get lead basic info by lead id anonymously - Updated", "")]
        public async Task<Response<V2BasicLeadInfoDto>> GetLeadBasicInfoAnonymousAsync([FromBody] GetLeadByIdAnonymouslyRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("counts")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get leads count using available filters.", "")]
        public async Task<Response<LeadCountDto>> GetCountAsync([FromBody] GetAllLeadCountsRequest request)
        {
            var response = await Mediator.Send(request);
            return response;
        }

        [HttpPost("v3")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Search leads using available filters.", "")]
        public async Task<Response<V3CategoryDto>> SearchAsync([FromBody] V3GetAllLeadsRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("getAllLeadCategory/v2")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all leadCategory details.", "")]
        public Task<Response<V3LeadCategoryDto>> GetAsync([FromBody] V3GetLeadCategoryRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpPost("custom-filters")]
        [TenantIdHeader]
        [OpenApiOperation("Get leads by custom filters.", "")]
        public async Task<Response<V5GetAllLeadsWrapperDto>> GetAsync([FromBody] GetAllLeadsByCustomFiltersRequest request)
        {
            return await Mediator.Send(request);
        }
    }
}
