﻿using Lrb.Application.Common.Gmail;
using Lrb.Application.Common.GoogleAd;
using Lrb.Application.Common.GoogleAds;
using Lrb.Application.Common.IVR;
using Lrb.Application.Common.IVR.Common.Dtos;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Common.Servetel.ResponseDtos;
using Lrb.Application.DataManagement.Web.Request;
using Lrb.Application.Integration.Web;
using Lrb.Application.Integration.Web.Automation;
using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.Integration.Web.Requests;
using Lrb.Application.Integration.Web.Requests.Bayut;
using Lrb.Application.Integration.Web.Requests.Common;
using Lrb.Application.Integration.Web.Requests.CommonFloor;
using Lrb.Application.Integration.Web.Requests.Facebook;
using Lrb.Application.Integration.Web.Requests.Gmail;
using Lrb.Application.Integration.Web.Requests.GoogleAds;
using Lrb.Application.Integration.Web.Requests.GoogleSheet;
using Lrb.Application.Integration.Web.Requests.GoogleSheets;
using Lrb.Application.Integration.Web.Requests.IVR;
using Lrb.Application.Integration.Web.Requests.JustLead;
using Lrb.Application.Integration.Web.Requests.ListingSites;
using Lrb.Application.Integration.Web.Requests.Pabbly;
using Lrb.Application.Integration.Web.Requests.Webhook;
using Lrb.Application.Integration.Web.Requests.WhatsApp;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Requests;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.LeadGenRequests;
using Lrb.Application.Source.Web;
using Lrb.Application.UserDetails.Web;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.Integration.GoogleSheet;
using Lrb.Domain.Enums;
using Lrb.Shared.Extensions;
using Lrb.Shared.Multitenancy;
using Mapster;
using Microsoft.Extensions.Primitives;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using PhoneNumbers;
using RestSharp;

namespace Lrb.WebApi.Host.Controllers
{
    [Authorize]
    public class IntegrationController : VersionedApiController
    {
        private readonly Serilog.ILogger _logger;
        private readonly IServetelService _servetelService;

        //  private readonly IWebhookService webhookService;
        private readonly IReadRepository<Domain.Entities.Source> _sourceRepo;
        public IntegrationController(Serilog.ILogger logger, IServetelService servetelService, IReadRepository<Domain.Entities.Source> sourceRepo)
        {
            _logger = logger;
            _servetelService = servetelService;
            _sourceRepo = sourceRepo;
        }
        [HttpPost]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        [OpenApiOperation("Create a new Integration.", "")]
        public Task<Response<Guid>> Create([FromBody] CreateIntegrationRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpGet("checkaccountname")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Integration)]
        [OpenApiOperation("Check Integeration Account Name")]
        public async Task<Response<bool>> CheckIntegerationAccountname([FromQuery] CheckIntegerationAccountNameRequest request)
        {
            try
            {
                return await Mediator.Send(request);
            }
            catch (Exception)
            {
                return new(false);
            }
        }

        [HttpPost("Servetel")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        [OpenApiOperation("Create a new Servetel IVR Integration.", "")]
        public Task<Response<string>> CreateServetelIntegration([FromBody] CreateServetelIntegrationRequest request)
        {
            try
            {
                return Mediator.Send(request);
            }
            catch (Exception ex)
            {
                return Task.FromResult(new Response<string>
                {
                    Succeeded = false,
                    Message = ex.Message
                });
            }
        }

        [HttpPost("Servetel/ClickToCall")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.IVRCall, LrbResource.Integration)]
        [OpenApiOperation("Click to Call Servetel IVR", "")]
        public async Task<Response<ClickToCallResponseDto>> ClickToCall(ServetelClickToCallRequest request)
        {
            try
            {
                return await Mediator.Send(request);
            }
            catch (Exception ex)
            {
                return new() { Message = ex.Message };
            }
        }

        [HttpPut("MakePrimary/{accountId:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Integration)]
        [OpenApiOperation("Make an Integration account a primary account", "")]
        public async Task<Response<bool>> MakeIVRPrimary(Guid accountId)
        {
            MakeIntegrationAccountPrimaryRequest request = new(accountId);
            return await Mediator.Send(request);
        }

        [HttpPut("agencyname")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Integration)]
        [OpenApiOperation("Set agency name for integration Accounts", "")]
        public async Task<Response<bool>> SetAgencyName([FromBody] AddAgencyNameToIntegrationAccountRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("agencynames")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Integration)]
        [OpenApiOperation("Get all agency names")]
        public async Task<Response<List<string>>> GetAsync()
        {
            return await Mediator.Send(new GetIntegrationAgencyNamesRequest());
        }

        [HttpPut("projects")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Integration)]
        [OpenApiOperation("Set agency name for integration Accounts", "")]
        public async Task<Response<bool>> SetProjects([FromBody] AddProjectListToIntegrationAccountRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("projects/{accountId:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Integration)]
        [OpenApiOperation("Set agency name for integration Accounts", "")]
        public async Task<PagedResponse<Lrb.Domain.Entities.Project, string>> GetProjects(Guid accountId, [FromQuery] LeadSource source)
        {
            var request = new GetProjectListOfAnIntegrationAccountRequest() { AccountId = accountId, Source = source };
            return await Mediator.Send(request);
        }

        [HttpGet("Servetel/agents")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.IVRCall, LrbResource.Integration)]
        [OpenApiOperation("Get a list of all agents")]
        public async Task<PagedResponse<ServetelIVRAgentDto, string>> GetAllAgents([FromQuery] GetAllAgentsRequest request)
        {
            try
            {
                return await Mediator.Send(request);
            }
            catch (Exception ex)
            {
                return new() { Message = ex.Message };
            }
        }

        [HttpPost("Servetel/agent")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.IVRCall, LrbResource.Integration)]
        [OpenApiOperation("Create a new agent.")]
        public async Task<Response<AgentCreationResponseDto>> CreateAgent(CreateAgentRequest request)
        {
            try
            {
                return await Mediator.Send(request);
            }
            catch (Exception ex)
            {
                return new() { Message = ex.Message };
            }
        }

        [HttpGet]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Integration)]
        [OpenApiOperation("Get all Integrations.", "")]
        public Task<PagedResponse<IntegrationDto, string>> GetAllAsync([FromQuery] GetAllIntegrationAccountRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpGet("totalCount")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Integration)]
        [OpenApiOperation("Get totalCount of Integrations.", "")]
        public Task<Response<Dictionary<LeadSource, IntegrationCountDto>>> GetAllAsync()
        {
            return Mediator.Send(new GetIntegrationTotalCountRequest());
        }
        [HttpPost("Gmail")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        [OpenApiOperation("Integrate a new Gmail Account.", "")]
        public Task<Response<Guid>> IntegrateGmail([FromBody] CreateWebClientGmailDataRequest request)
        {
            _logger.Information("IntegrationController -> POST(Gmail), request: {0}", JsonConvert.SerializeObject(request));
            return Mediator.Send(request);
        }
        [HttpPost("Gmail/messages")]
        [TenantIdHeader]
        [AllowAnonymous]
        public async Task<Response<bool>> ReadMessages([FromBody] Root value)
        {
            if (!await IsSourceEnabled(LeadSource.Gmail))
                return new(false, "Gmail integration is currently disabled");
            CreateLeadGenRequest leadGenRequest = new(LeadSource.Gmail, value);
            await Mediator.Send(leadGenRequest);
            _logger.Information("IntegrationController -> POST(Gmail/messages) -> called, Gmail pubSubMessage: " + JsonConvert.SerializeObject(value));
            Console.WriteLine("IntegrationController -> POST(Gmail/messages) -> called, Gmail pubSubMessage: " + JsonConvert.SerializeObject(value));
            //this.HttpContext.Request.Headers.Add("tenant", "development");
            try
            {

                CreateLeadFromPubSubMessageRequest request = new()
                {
                    PubSubMessage = value
                };
                return await Mediator.Send(request);
            }
            catch (Exception e)
            {
                _logger.Error("IntegrationController -> POST(Gmail/messages) -> Error: " + JsonConvert.SerializeObject(e));
                return new(false);
            }

        }

        //New Implementation
        //Get all associated accounts
        [HttpGet("facebook/account")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Integration)]
        public async Task<PagedResponse<ViewFacebookAccountDto, string>> GetAllFacebookIntegrationsAsync([FromQuery] GetAllFacebookIntegrationAccountsRequest request)
        {
            var response = await Mediator.Send(request);
            return response;
        }
        [HttpGet("facebook/account-forms")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Integration)]
        public async Task<PagedResponse<ViewFacebookAccountWithFormDto, string>> GetAllFacebookIntegrationsAsync([FromQuery] GetAllFbIntegrationAccountsWithLeadGenFormsRequest request)
        {
            try
            {
                var response = await Mediator.Send(request);
                return response;
            }
            catch (Exception ex)
            {
                return new() { Message = ex.Message };
            }
        }

        [HttpPost("facebook/marketing")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Integration)]
        public async Task<Response<List<CampaignAdMetricsDto>>> GetAllFacebookMarketingAsync([FromBody] GetCampaignAdMetricsRequest request)
        {
            try
            {
                var response = await Mediator.Send(request);
                return response;
            }
            catch (Exception ex)
            {
                return new() { Message = ex.Message };
            }
        }
        [HttpPost("facebook/campaign-marketing")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Integration)]
        public async Task<Response<List<CampaignMetricsDto>>> GetAllFacebookCampaignMarketingAsync([FromBody] GetCampaignMetricsRequest request)
        {
            try
            {
                var response = await Mediator.Send(request);
                return response;
            }
            catch (Exception ex)
            {
                return new() { Message = ex.Message };
            }
        }

        [HttpGet("facebook/account-forms/separated")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Integration)]
        public async Task<PagedResponse<ViewFacebookAccountWithFormDto, string>> GetAllFacebookIntegrationsAsync([FromQuery] GetAllFbIntegrationAccountsWithSegregatedLeadGenFormsRequest request)
        {
            try
            {
                var response = await Mediator.Send(request);
                return response;
            }
            catch (Exception ex)
            {
                return new() { Message = ex.Message };
            }
        }
        //With SP
        [HttpGet("facebook/account-forms/SP")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Integration)]
        public async Task<PagedResponse<ViewFacebookAccountWithFormDto, string>> GetAllFacebookIntegrationsWithSPAsync([FromQuery] GetAllFbIntegrationAccountsWithLeadGenFormsWithSPRequest request)
        {
            try
            {
                var response = await Mediator.Send(request);
                return response;
            }
            catch (Exception ex)
            {
                return new() { Message = ex.Message };
            }
        }

        //Add Account
        [HttpPost("facebook/account")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        public async Task<Response<bool>> CreateFacebookIntegrationAsync([FromBody] CreateFacebookIntegrationRequest request)
        {
            try
            {
                var response = await Mediator.Send(request);
                return response;
            }
            catch (Exception ex)
            {
                return new() { Message = ex.Message };
            }
        }

        //Add Account
        [HttpPost("facebook/account/console")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        public async Task<Response<bool>> CreateFacebookIntegrationAsync([FromBody] CreateFacebookIntegrationUsingconsoleRequest request)
        {
            try
            {
                var response = await Mediator.Send(request);
                return response;
            }
            catch (Exception ex)
            {
                return new() { Message = ex.Message };
            }
        }

        [HttpDelete("facebook/account/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Delete, LrbResource.Integration)]
        public async Task<Response<bool>> DeleteFacebookIntegrationAsync(Guid id)
        {
            DeleteFacebookIntegrationAccountRequest request = new(id);
            var response = await Mediator.Send(request);
            return response;
        }

        //End of new implementation


        [HttpPost("facebook/accounts")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        public async Task<Response<bool>> StoreFacebookAccounts(CreateFacebookAccountsDataRequest request)
        {
            var response = await Mediator.Send(request);
            return response;
        }
        [HttpPost("facebook/forms")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        public async Task<Response<bool>> StoreFacebookAccounts(CreateFacebookFormDataRequest request)
        {
            var response = await Mediator.Send(request);
            return response;
        }

        [HttpGet("facebook/forms")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Integration)]
        [OpenApiOperation("Get all FacebookForms.", "")]
        public Task<PagedResponse<ViewFacebookFormDataDto, string>> GetAllAsync([FromQuery] GetAllFacebookFormRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpGet("facebook/webhook")]
        [AllowAnonymous]
        [OpenApiOperation("Verification endpoint for facebook webhook.", "")]
        public async Task<string> GetChallange([FromQuery(Name = "hub.mode")] string hub_mode,
            [FromQuery(Name = "hub.challenge")] string hub_challenge,
            [FromQuery(Name = "hub.verify_token")] string hub_verify_token)
        {
            _logger.Information("IntegrationController -> GET(facebook/webhook) -> Verify webhook challenge: " + hub_challenge);
            return hub_challenge;
        }

        [HttpPost("facebook/webhook")]
        [AllowAnonymous]
        [OpenApiOperation("Push endpoint for facebook webhook.", "")]
        public async Task<bool> SendRequestToApiGateway(FacebookPageWebhookDto dto)
        {
            _logger.Information("IntegrationController -> POST(facebook/webhook) -> called, Dto: " + JsonConvert.SerializeObject(dto));
            try
            {
                var env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
                //TODO : need to change the function access key
                string apiGatewayUrl = $"https://lrb-prd-integration-jobs.azurewebsites.net/api/{env}?code=m6lphsoTgH-hXgWX-wlpANbRKlJUocDh5yGnT49fVOuZAzFucHGl9Q%3D%3D";
                RestClient client = new(apiGatewayUrl);
                RestRequest request = new();
                request.AddBody(dto);
                _logger.Information("IntegrationController -> POST(facebook/webhook) -> RestRequest to Lambda, Dto: " + JsonConvert.SerializeObject(request));
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await client.PostAsync(request);

                    }
                    catch (Exception ex)
                    {
                        _logger.Error(ex, "Error sending fire-and-forget request to Lambda.");
                    }
                });
                return true;
            }
            catch (Exception e)
            {
                _logger.Error("IntegrationController -> POST(facebook/webhook) -> Error: " + JsonConvert.SerializeObject(e));
                return true;
            }
        }
        [HttpPost("facebook/webhook/process")]
        [AllowAnonymous]
        [TenantIdHeader]
        [OpenApiOperation("Process webhook from facebook.", "")]
        public async Task<Response<bool>> ProcessWebhookData(FacebookPageWebhookDto dto)
        {
            if (!await IsSourceEnabled(LeadSource.Facebook))
                return new(false, "Facebook integration is currently disabled");
            CreateLeadGenRequest leadGenRequest = new(LeadSource.Facebook, dto);
            await Mediator.Send(leadGenRequest);
            StringValues tenantIds;
            this.HttpContext.Request.Headers.TryGetValue(MultitenancyConstants.TenantIdName, out tenantIds);

            if (tenantIds.Count > 0)
            {
                var tenantId = tenantIds[0];
                _logger.Information($"IntegrationController -> POST(facebook/webhook/process) -> Called for Tenant: {tenantId} Dto: " + JsonConvert.SerializeObject(dto));
            }
            else
            {
                _logger.Information("IntegrationController -> POST(facebook/webhook/process) -> Called, Dto: " + JsonConvert.SerializeObject(dto));
            }
            try
            {
                ProcessFacebookWebhookRequest request = new()
                {
                    Dto = dto,
                    TenantId = tenantIds[0]
                };
                var response = await Mediator.Send(request);
                return response;
            }
            catch (Exception e)
            {
                _logger.Error("IntegrationController -> POST(facebook/webhook/process) -> Error: " + JsonConvert.SerializeObject(e));
                return new(false, $"IntegrationController -> POST(facebook/webhook/process) -> Error: {JsonConvert.SerializeObject(e)}");
            }

        }


        [HttpGet("IntegrationAccountInfo/{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Get by IntegrationAccountId.", "")]
        public Task<Response<string>> GetById(Guid id)
        {
            return Mediator.Send(new GetIntegrationAccountInfoRequest(id));
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("Any")]
        [OpenApiOperation("Push endpoint for common webhook integration", "")]
        public Task<Response<bool>> PostWebhook([FromBody] CommonWebhookRequest lead)
        {
            try
            {
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                lead.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(lead.LeadSource, lead);
                var req = Mediator.Send(leadGenRequest).Result;
                _logger.Information("IntegrationController -> POST(Webhook) -> called, Dto: " + JsonConvert.SerializeObject(lead));
                var res = Mediator.Send(lead);
                return res;
            }
            catch (Exception ex)
            {
                return Task.FromResult(new Response<bool>
                {
                    Succeeded = false,
                    Message = ex.Message
                });
            }
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("Facebook")]
        [OpenApiOperation("Push endpoint for Facebook webhook integration", "")]
        public async Task<Response<bool>> PostFacebook([FromBody] FacebookLrbWebhookRequest lead)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.Facebook))
                    return new(false, "Facebook integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                lead.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.Facebook, lead);
                var req = await Mediator.Send(leadGenRequest);
                _logger.Information("IntegrationController -> POSTfacebook/custom-webhook) -> called, Dto: " + JsonConvert.SerializeObject(lead));
                lead.LeadSource = Domain.Enums.LeadSource.Facebook;
                CheckDuplicateLeadRequest checkDuplicateLeadRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                checkDuplicateLeadRequest.SerializedData = lead.Serialize();
                var shouldCreateNewLead = await Mediator.Send(checkDuplicateLeadRequest);
                if (shouldCreateNewLead.Data)
                {
                    return await Mediator.Send(lead);
                }
                return shouldCreateNewLead;
            }
            catch (Exception ex)
            {
                return new(false);
            }
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("Housing")]
        [OpenApiOperation("Push endpoint for Housing.com integration", "")]
        public async Task<Response<bool>> PostHousing([FromBody] ListingSitesIntegrationRequest lead)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.Housing))
                    return new(false, "Housing integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                lead.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.Housing, lead);
                var req = await Mediator.Send(leadGenRequest);
                _logger.Information("IntegrationController -> POST(Housing) -> called, Dto: " + JsonConvert.SerializeObject(lead));
                lead.LeadSource = Domain.Enums.LeadSource.Housing;
                CheckDuplicateLeadRequest checkDuplicateLeadRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                checkDuplicateLeadRequest.SerializedData = lead.Serialize();
                var shouldCreateNewLead = await Mediator.Send(checkDuplicateLeadRequest);
                if (shouldCreateNewLead.Data)
                {
                    return await Mediator.Send(lead);
                }
                return shouldCreateNewLead;
            }
            catch (Exception)
            {
                return new(false);
            }
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("WhatsApp")]
        [OpenApiOperation("Push endpoint for WhatsApp.com integration", "")]
        public async Task<Response<bool>> PostWhatsApp([FromBody] WhatsAppListingSitesIntegrationRequest lead)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.WhatsApp))
                    return new(false, "WhatsApp integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                lead.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.WhatsApp, lead);
                var req = await Mediator.Send(leadGenRequest);
                _logger.Information("IntegrationController -> POST(WhatsApp) -> called, Dto: " + JsonConvert.SerializeObject(lead));
                lead.LeadSource = Domain.Enums.LeadSource.WhatsApp;
                //CheckDuplicateLeadRequest checkDuplicateLeadRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                //checkDuplicateLeadRequest.SerializedData = lead.Serialize();
                //var shouldCreateNewLead = await Mediator.Send(checkDuplicateLeadRequest);
                //if (shouldCreateNewLead.Data)
                //{
                return await Mediator.Send(lead);
            }
            catch (Exception)
            {
                return new(false);
            }
        }


        [AllowAnonymous]
        [HttpGet("WhatsApp/{tenant}/{base64}")]
        [OpenApiOperation("Push endpoint for WhatsApp.com integration", "")]
        public async Task<object> PostWhatsAppFromBody([FromRoute] string tenant, [FromRoute] string base64, [FromQuery] WhatsAppListingSitesIntegrationRequest lead)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.WhatsApp))
                    return new { data = new { success = false, message = "WhatsApp integration is currently disabled" } };
                lead.ApiKey = base64;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.WhatsApp, lead);
                var req = await Mediator.Send(leadGenRequest);
                _logger.Information("IntegrationController -> POST(WhatsApp) -> called, Dto: " + JsonConvert.SerializeObject(lead));
                lead.LeadSource = Domain.Enums.LeadSource.WhatsApp;
                //CheckDuplicateLeadRequest checkDuplicateLeadRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                //checkDuplicateLeadRequest.SerializedData = lead.Serialize();
                //var shouldCreateNewLead = await Mediator.Send(checkDuplicateLeadRequest);
                //if (shouldCreateNewLead.Data)
                //{\
                var data = await Mediator.Send(lead);
                return new { data = new { success = data?.Succeeded ?? false } };
            }
            catch (Exception ex)
            {
                return new { data = new { success = false } };
            }
            //}
            //return shouldCreateNewLead;
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("NinetyNineAcres")]
        [OpenApiOperation("Push endpoint for NinetyNineAcres integration", "")]
        public async Task<Response<bool>> PostNinetynineAcres([FromBody] ListingSitesIntegrationRequest lead)
        {
            try
            {

                if (!(await IsSourceEnabled(LeadSource.NinetyNineAcres)))
                    return new(false, "NinetyNineAcres integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                lead.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.NinetyNineAcres, lead);
                var req = await Mediator.Send(leadGenRequest);
                _logger.Information("IntegrationController -> POST(NinetyNineAcres) -> called, Dto: " + JsonConvert.SerializeObject(lead));
                lead.LeadSource = Domain.Enums.LeadSource.NinetyNineAcres;
                CheckDuplicateLeadRequest checkDuplicateLeadRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                checkDuplicateLeadRequest.SerializedData = lead.Serialize();
                var shouldCreateNewLead = await Mediator.Send(checkDuplicateLeadRequest);
                if (shouldCreateNewLead.Data)
                {
                    return await Mediator.Send(lead);
                }
                return shouldCreateNewLead;
            }
            catch (Exception ex)
            {
                _logger.Information("IntegrationController -> POST(NinetyNineAcres) -> called, Dto: " + ex.Message);
                return new(false, ex.Message);
            }
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("Youtube")]
        [OpenApiOperation("Push endpoint for Youtube integration", "")]
        public async Task<Response<bool>> PostYoutube([FromBody] ListingSitesIntegrationRequest lead)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.YouTube))
                    return new(false, "YouTube integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                lead.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.YouTube, lead);
                var req = await Mediator.Send(leadGenRequest);
                _logger.Information("IntegrationController -> POST(Youtube) -> called, Dto: " + JsonConvert.SerializeObject(lead));
                lead.LeadSource = Domain.Enums.LeadSource.YouTube;
                CheckDuplicateLeadRequest checkDuplicateLeadRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                checkDuplicateLeadRequest.SerializedData = lead.Serialize();
                var shouldCreateNewLead = await Mediator.Send(checkDuplicateLeadRequest);
                if (shouldCreateNewLead.Data)
                {
                    return await Mediator.Send(lead);
                }
                return shouldCreateNewLead;
            }
            catch (Exception)
            {
                return new(false);
            }
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("LinkedIn")]
        [OpenApiOperation("Push endpoint for LinkedIn integration", "")]
        public async Task<Response<bool>> PostLinkedIn([FromBody] ListingSitesIntegrationRequest lead)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.LinkedIn))
                    return new(false, "LinkedIn integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                lead.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.LinkedIn, lead);
                var req = await Mediator.Send(leadGenRequest);
                _logger.Information("IntegrationController -> POST(LinkedIn) -> called, Dto: " + JsonConvert.SerializeObject(lead));
                lead.LeadSource = Domain.Enums.LeadSource.LinkedIn;
                CheckDuplicateLeadRequest checkDuplicateLeadRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                checkDuplicateLeadRequest.SerializedData = lead.Serialize();
                var shouldCreateNewLead = await Mediator.Send(checkDuplicateLeadRequest);
                if (shouldCreateNewLead.Data)
                {
                    return await Mediator.Send(lead);
                }
                return shouldCreateNewLead;
            }
            catch (Exception)
            {
                return new(false);
            }
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("MagicBricks")]
        [OpenApiOperation("Push endpoint for MagicBricks integration", "")]
        public async Task<Response<bool>> PostMagicBricks([FromBody] ListingSitesIntegrationRequest lead)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.MagicBricks))
                    return new(false, "MagicBricks integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                lead.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.MagicBricks, lead);
                var req = await Mediator.Send(leadGenRequest);
                _logger.Information("IntegrationController -> POST(MagicBricks) -> called, Dto: " + JsonConvert.SerializeObject(lead));
                lead.LeadSource = Domain.Enums.LeadSource.MagicBricks;
                CheckDuplicateLeadRequest checkDuplicateLeadRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                checkDuplicateLeadRequest.SerializedData = lead.Serialize();
                var shouldCreateNewLead = await Mediator.Send(checkDuplicateLeadRequest);
                if (shouldCreateNewLead.Data)
                {
                    return await Mediator.Send(lead);
                }
                return shouldCreateNewLead;
            }
            catch (Exception ex)
            {
                _logger.Information("IntegrationController -> POST(MagicBricks) -> called, Dto: " + JsonConvert.SerializeObject(lead));
                return new(false, ex.Message);
            }
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("SquareYards")]
        [OpenApiOperation("Push endpoint for SquareYards integration", "")]
        public async Task<Response<bool>> PostSquareYards([FromBody] ListingSitesIntegrationRequest lead)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.SquareYards))
                    return new(false, "SquareYards integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                lead.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.SquareYards, lead);
                var req = await Mediator.Send(leadGenRequest);
                _logger.Information("IntegrationController -> POST(SquareYards) -> called, Dto: " + JsonConvert.SerializeObject(lead));
                lead.LeadSource = Domain.Enums.LeadSource.SquareYards;
                CheckDuplicateLeadRequest checkDuplicateLeadRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                checkDuplicateLeadRequest.SerializedData = lead.Serialize();
                var shouldCreateNewLead = await Mediator.Send(checkDuplicateLeadRequest);
                if (shouldCreateNewLead.Data)
                {
                    return await Mediator.Send(lead);
                }
                return shouldCreateNewLead;
            }
            catch (Exception ex)
            {
                return new(false);
            }
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("EstateDekho")]
        [OpenApiOperation("Push endpoint for EstateDekho integration", "")]
        public async Task<Response<bool>> PostEstateDekho([FromBody] ListingSitesIntegrationRequest lead)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.EstateDekho))
                    return new(false, "Estate Dekho integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                lead.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.EstateDekho, lead);
                var req = await Mediator.Send(leadGenRequest);
                _logger.Information("IntegrationController -> POST(EstateDekho) -> called, Dto: " + JsonConvert.SerializeObject(lead));
                lead.LeadSource = Domain.Enums.LeadSource.EstateDekho;
                CheckDuplicateLeadRequest checkDuplicateLeadRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                checkDuplicateLeadRequest.SerializedData = lead.Serialize();
                var shouldCreateNewLead = await Mediator.Send(checkDuplicateLeadRequest);
                if (shouldCreateNewLead.Data)
                {
                    return await Mediator.Send(lead);
                }
                return shouldCreateNewLead;
            }
            catch (Exception ex)
            {
                return new(false);
            }
        }
        [ApiKey]
        [AllowAnonymous]
        [HttpPost("OLX")]
        [OpenApiOperation("Push endpoint for OLX integration", "")]
        public async Task<Response<bool>> PostOLX([FromBody] ListingSitesIntegrationRequest lead)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.OLX))
                    return new(false, "OLX integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                lead.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.OLX, lead);
                var req = Mediator.Send(leadGenRequest).Result;
                _logger.Information("IntegrationController -> POST(OLX) -> called, Dto: " + JsonConvert.SerializeObject(lead));
                lead.LeadSource = Domain.Enums.LeadSource.OLX;
                CheckDuplicateLeadRequest checkDuplicateLeadRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                checkDuplicateLeadRequest.SerializedData = lead.Serialize();
                var shouldCreateNewLead = await Mediator.Send(checkDuplicateLeadRequest);
                if (shouldCreateNewLead.Data)
                {
                    return await Mediator.Send(lead);
                }
                return shouldCreateNewLead;
            }
            catch (Exception ex)
            {
                return new(false);
            }
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("Website")]
        [OpenApiOperation("Push endpoint for any Website integration", "")]
        public async Task<Response<bool>> PostWebsite([FromBody] List<WebsiteIntegrationDto>? Leads)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.Website))
                    return new(false, "Website integration is currently disabled");

                LeadSource? leadSource = null;
                if (Leads?.Any() ?? false)
                {
                    foreach (var lead in Leads)
                    {
                        if (!string.IsNullOrWhiteSpace(lead.Source))
                        {
                            lead.Source = lead.Source.Replace(" ", "").Trim();
                            leadSource = EnumFromDescription.GetValueFromDescription<LeadSource>(lead.Source);
                            if (leadSource == LeadSource.Direct)
                            {
                                leadSource = null;
                            }
                        }
                        CreateLeadGenRequest leadGenRequest = new(leadSource ?? LeadSource.Website, lead);
                        var req = await Mediator.Send(leadGenRequest);
                    }
                }
                _logger.Information("IntegrationController -> POST(Website) -> called, Dto: " + JsonConvert.SerializeObject(Leads));
                WebsiteIntegrationRequest command = new()
                {
                    Leads = Leads
                };
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                command?.Leads?.ForEach(i => i.LeadSource = leadSource ?? Domain.Enums.LeadSource.Website);
                command.ApiKey = apiKey;
                List<string> leadMobileNos = new();
                Response<bool>? response = null;
                if (command.Leads?.Any() ?? false)
                {
                    foreach (var lead in command.Leads)
                    {
                        CheckDuplicateLeadRequest checkDuplicateLeadRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                        checkDuplicateLeadRequest.SerializedData = lead.Serialize();
                        checkDuplicateLeadRequest.ApiKey = command.ApiKey;
                        var shouldCreateNewLead = await Mediator.Send(checkDuplicateLeadRequest);
                        if (!shouldCreateNewLead.Data)
                        {
                            leadMobileNos.Add(lead.Mobile ?? string.Empty);
                            if (response == null)
                            {
                                response = shouldCreateNewLead;
                            }
                            else
                            {
                                response.Message += shouldCreateNewLead.Message;
                            }
                        }
                    }
                }
                leadMobileNos.RemoveAll(i => string.IsNullOrWhiteSpace(i));
                command.Leads = command.Leads?.Where(i => !leadMobileNos.Contains(i.Mobile ?? string.Empty)).ToList();
                if (command.Leads?.Any() ?? false)
                {
                    return await Mediator.Send(command);
                }
                return response ?? new();
            }
            catch (Exception e) { return new(false, e.Message); }
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("IVR")]
        [OpenApiOperation("Push endpoint for IVR integration", "")]
        public async Task<Response<bool>> PostIVR([FromBody] IVRIntegrationDto dto)
        {
            try
            {
                if (!(await IsSourceEnabled(LeadSource.IVR)))
                    return new Response<bool>(false, "IVR integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                IVRIntegrationRequest command = dto.Adapt<IVRIntegrationRequest>();
                command.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.IVR, dto);
                var req = Mediator.Send(leadGenRequest).Result;
                //command.AccountId = accountId;
                return await Mediator.Send(command);
            }
            catch (Exception ex)
            {
                return new Response<bool>
                {
                    Succeeded = false,
                    Message = ex.Message
                };
            }
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("IVR/knowlarity")]
        [OpenApiOperation("Push endpoint for Knowlarity IVR integration", "")]
        public async Task<Response<bool>> PostKnowlarityIVR([FromBody] IVRIntegrationDto dto)
        {
            try
            {
                if (!(await IsSourceEnabled(LeadSource.IVR)))
                    return new Response<bool>(false, "IVR integration is currently disabled");
                _logger.Information("IntegrationController -> POST(IVR/knowlarity) -> called, Dto: " + dto.Serialize());
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                IVRIntegrationRequest command = dto.Adapt<IVRIntegrationRequest>();
                command.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.IVR, dto);
                var req = Mediator.Send(leadGenRequest).Result;
                //command.AccountId = accountId;
                return await Mediator.Send(command);
            }
            catch (Exception ex)
            {
                return new Response<bool>  // Removed Task.FromResult
                {
                    Succeeded = false,
                    Message = ex.Message
                };
            }
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("IVR/Servetel/Outbound")]
        [OpenApiOperation("Push endpoint for Outbound Servetel IVR integration", "")]
        public async Task<Response<bool>> PostServetelIVROutbound([FromBody] ServetelIntegrationOutboundDto dto)
        {
            try
            {
                if (!(await IsSourceEnabled(LeadSource.IVR)))
                    return new Response<bool>(false, "IVR integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                ServetelOutboundIntegrationRequest command = dto.Adapt<ServetelOutboundIntegrationRequest>();
                command.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.IVR, dto);
                var req = Mediator.Send(leadGenRequest).Result;
                _logger.Information("IntegrationController -> POST(IVR/Servetel/Outbound) -> called, Dto: " + JsonConvert.SerializeObject(dto));
                //command.AccountId = accountId;
                return await Mediator.Send(command);
            }
            catch (Exception ex)
            {
                return new Response<bool>
                {
                    Succeeded = false,
                    Message = ex.Message
                };
            }
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("IVR/Servetel/Inbound")]
        [OpenApiOperation("Push endpoint for Inbound Servetel IVR integration", "")]
        public async Task<Response<bool>> PostServetelIVRInbound([FromBody] ServetelIntegrationInboundDto dto)
        {
            try
            {
                if (!(await IsSourceEnabled(LeadSource.IVR)))
                    return new Response<bool>(false, "IVR integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                ServetelInboundIntegrationRequest command = dto.Adapt<ServetelInboundIntegrationRequest>();
                command.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.IVR, dto);
                var req = Mediator.Send(leadGenRequest).Result;
                _logger.Information("IntegrationController -> POST(IVR/Servetel/Inbound) -> called, Dto: " + JsonConvert.SerializeObject(dto));
                // command.AccountId = accountId;
                return await Mediator.Send(command);
            }
            catch (Exception ex)
            {
                return new Response<bool>
                {
                    Succeeded = false,
                    Message = ex.Message
                };
            }
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("IVR/Tatatelebusiness/Outbound")]
        [OpenApiOperation("Push endpoint for Outbound Tatatelebusiness IVR integration", "")]
        public async Task<Response<bool>> PostTataTeleBusinessIVROutbound([FromBody] TataTeleBusinessIntegrationDto dto)
        {
            try
            {
                if (!(await IsSourceEnabled(LeadSource.IVR)))
                    return new Response<bool>(false, "IVR integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                TataTeleBusinessOutboundIntegrationRequest command = dto.Adapt<TataTeleBusinessOutboundIntegrationRequest>();
                command.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.IVR, dto);
                var req = Mediator.Send(leadGenRequest).Result;
                _logger.Information("IntegrationController -> POST(IVR/Tatatelebusiness/Outbound) -> called, Dto: " + JsonConvert.SerializeObject(dto));
                return await Mediator.Send(command);
            }
            catch (Exception ex)
            {
                return new Response<bool>
                {
                    Succeeded = false,
                    Message = ex.Message
                };
            }
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("IVR/Tatatelebusiness/Inbound")]
        [OpenApiOperation("Push endpoint for Inbound Tatatelebusiness IVR integration", "")]
        public async Task<Response<bool>> PostTataTeleBusinessIVRInbound([FromBody] TataTeleBusinessIntegrationDto dto)
        {
            try
            {
                if (!(await IsSourceEnabled(LeadSource.IVR)))
                    return new Response<bool>(false, "IVR integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                TataTeleBusinessInboundIntegrationRequest command = dto.Adapt<TataTeleBusinessInboundIntegrationRequest>();
                command.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.IVR, dto);
                var req = Mediator.Send(leadGenRequest).Result;
                _logger.Information("IntegrationController -> POST(IVR/Tatatelebusiness/Inbound) -> called, Dto: " + JsonConvert.SerializeObject(dto));
                return await Mediator.Send(command);
            }
            catch (Exception ex)
            {
                return new Response<bool>
                {
                    Succeeded = false,
                    Message = ex.Message
                };
            }
        }

        [HttpDelete("{id:guid}")]
        [MustHavePermission(LrbAction.Delete, LrbResource.Integration)]
        [TenantIdHeader]
        [OpenApiOperation("Delete an integration account.", "")]
        public Task<Response<Guid>> DeleteAsync(Guid id)
        {
            return Mediator.Send(new DeleteIntegrationAccountRequest(id));
        }

        [HttpGet("Servetel/History")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Integration)]
        [OpenApiOperation("Get ServetelCallLogHistory by Customer Number", "")]
        public async Task<Response<Dictionary<DateTime, Dictionary<DateTime, List<ServetelCallLogHistoryDto>>>>> GetServetelCallLogHistory([FromQuery] string customerNo)
        {
            GetServetelCallLogHistoryByCustomerNoRequest request = new() { CustomerNo = customerNo };
            var res = await Mediator.Send(request);
            return res;
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("GoogleAds")]
        [OpenApiOperation("Push endpoint for GoogleAds LandingPage.", "")]
        public async Task<Response<bool>> PostGoogleAdLandingPage([FromBody] ListingSitesIntegrationRequest lead)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.GoogleAds))
                    return new(false, "Google Ads integration is currently disabled");
                lead.ApiKey = this.HttpContext.Request.Headers["API-Key"];
                CreateLeadGenRequest leadGenRequest = new(LeadSource.GoogleAds, lead);
                var req = await Mediator.Send(leadGenRequest);
                _logger.Information("IntegrationController -> POST(GoogleAds/LandingPage) -> called, Dto: " + JsonConvert.SerializeObject(lead));
                lead.LeadSource = Domain.Enums.LeadSource.GoogleAds;
                CheckDuplicateLeadRequest checkDuplicateLeadRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                checkDuplicateLeadRequest.SerializedData = lead.Serialize();
                var shouldCreateNewLead = await Mediator.Send(checkDuplicateLeadRequest);
                if (shouldCreateNewLead.Data)
                {
                    return await Mediator.Send(lead);
                }
                return shouldCreateNewLead;
            }
            catch (Exception ex)
            {
                return new(false);
            }
        }
        [HttpPost("GoogleAds/LeadFormExcel")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        [OpenApiOperation("Create GoogleAd leadForm Account.", "")]
        public Task<Response<Guid>> Create(CreateGoogleAdLeadFormAccountRequest request)
        {
            return Mediator.Send(request);
        }

        [AllowAnonymous]
        [HttpPost("GoogleAds/LeadForm/{tenant}")]
        [OpenApiOperation("Push end point for google ad LeadForm.", "")]
        public async Task<Response<bool>> ProcessGoogleAdWebHookData(string tenant, [FromBody] GoogleAdWebhookDto dto)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.GoogleAds))
                    return new(false, "Google Ads integration is currently disabled");
                CreateLeadGenRequest leadGenRequest = new(LeadSource.GoogleAds, dto);
                var req = Mediator.Send(leadGenRequest).Result;
                _logger.Information("IntegrationController -> POST(GoogleAd/webhook) -> called, Dto: " + JsonConvert.SerializeObject(dto));
                ProcessGoogleAdWebhookRequest request = new()
                {
                    Dto = dto
                };
                var response = await Mediator.Send(request);
                return response;
            }
            catch (Exception ex)
            {
                return new(false);
            }
        }
        [HttpGet("GoogleAds/Accounts")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Integration)]
        [OpenApiOperation("Get all Integrations.", "")]
        public Task<PagedResponse<GoogleAdIntegrationDto, string>> GetAllGoogleAdAccountAsync([FromQuery] GetGoogleAdIntegrationAccountRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpDelete("GoogleAds/{id:guid}")]
        [MustHavePermission(LrbAction.Delete, LrbResource.Integration)]
        [TenantIdHeader]
        [OpenApiOperation("Delete an GoogleAd integration account.", "")]
        public Task<Response<Guid>> DeleteAccountAsync(Guid id)
        {
            return Mediator.Send(new DeleteGoogleAdIntegrationAccountRequest(id));
        }

        [HttpGet("GoogleadsIntegrationAccountInfo/{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Get by GoogleadsIntegrationAccountId.", "")]
        public Task<Response<string>> GetGoogleAdsAccountById(Guid id)
        {
            return Mediator.Send(new GetGoogleAdsIntegrationAccountInfoRequest(id));
        }
        [HttpPost("automate")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Assign, LrbResource.Integration)]
        [OpenApiOperation("Automate any integration account.", "")]
        public async Task<Response<bool>> AutomateAccountAsync(AutomateIntegrationLeadRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("automate/fb-ad")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Assign, LrbResource.Integration)]
        [OpenApiOperation("Automate any integration account.", "")]
        public async Task<Response<bool>> AutomateAccountAsync(AutomateFBAdRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("automate/fb-form")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Assign, LrbResource.Integration)]
        [OpenApiOperation("Automate any integration account.", "")]
        public async Task<Response<bool>> AutomateAccountAsync(AutomateFbLeadgenFormRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("assignment/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Integration)]
        [OpenApiOperation("View assigned users of any integration account.", "")]
        public async Task<Response<IntegrationAssignmentInfoDto?>> GetAssignmentInfoAsync(Guid id, [FromQuery] LeadSource source)
        {
            return await Mediator.Send(new GetAssignmentInfoOfAnAccountRequest(id, source));
        }

        [HttpGet("fb-ad/sync/{accountId:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Integration)]
        [OpenApiOperation("Sync ads of an Facebook account", "")]
        public async Task<PagedResponse<FacebookAdsInfo, string>> SyncFacebookAds(Guid accountId)
        {
            SyncFacebookAdsRequest request = new() { AccountId = accountId };
            return await Mediator.Send(request);
        }
        //[HttpGet("fb/sync/{accountId:guid}")]
        //[TenantIdHeader]
        //[MustHavePermission(LrbAction.Update, LrbResource.Integration)]
        //[OpenApiOperation("Sync an Facebook account", "")]
        //public async Task<Response<bool>> SyncFacebook(Guid accountId)
        //{
        //    SyncFacebookRequest request = new() { AccountId = accountId };
        //    return await Mediator.Send(request);
        //}
        [HttpGet("fb/sync/{accountId:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Integration)]
        [OpenApiOperation("Sync an Facebook account through a Console project.", "")]
        public async Task<Response<bool>> SyncFacebookConsole(Guid accountId)
        {
            SyncFacebookInConsoleRequest request = new() { AccountId = accountId };
            return await Mediator.Send(request);
        }

        [HttpPost("fb-ad/subscribe")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Integration)]
        [OpenApiOperation("Subscribe to Facebook ads", "")]
        public async Task<Response<bool>> SubscribeFacebookAds([FromBody] List<Guid> ids)
        {
            ToggleFacebookAdsSubscriptionRequest request = new() { AdIds = ids };
            return await Mediator.Send(request);
        }

        [HttpPost("fb/bulk-fetch")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Integration)]
        [OpenApiOperation("Fetch facebook leads between a date range", "")]
        public async Task<Response<bool>> FetchBulkLeadsAsync([FromQuery] GetFacebookBulkLeadsRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("fb/bulk-export")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Integration)]
        [OpenApiOperation("Export facebook leads between a date range", "")]
        public async Task<Response<bool>> ExportBulkLeadsAsync([FromQuery] ExportFacebookBulkLeadsRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("fb/toggle-subscription")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Integration)]
        [OpenApiOperation("Toggle ads and forms subscription of Facebook", "")]
        public async Task<Response<bool>> SubscribeFacebook([FromBody] ToggleFacebookAdsAndFormsSubscriptionRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("MyOperator")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        [OpenApiOperation("Create a new MyOperator IVR Integration.", "")]
        public Task<Response<string>> CreateMyOperatorIntegration([FromBody] CreateMyOperatorIntegrationRequest request)
        {
            return Mediator.Send(request);
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("IVR/MyOperator/Inbound")]
        [OpenApiOperation("Push endpoint for Inbound MyOperator IVR integration", "")]
        public async Task<Response<bool>> PostMyOperatorIVRInbound([FromBody] Object dto) //MyOperatorIVRInboundDto
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.IVR))
                    return new(false, "IVR integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                if (dto != null)
                {
                    try
                    {
                        MyOperatorIVRInboundDto? inboundDto = System.Text.Json.JsonSerializer.Deserialize<MyOperatorIVRInboundDto>(dto.ToString());
                        if (inboundDto != null)
                        {
                            MyOperatorIVRInboundRequest command = inboundDto.Adapt<MyOperatorIVRInboundRequest>();
                            command.ApiKey = apiKey;
                            return await Mediator.Send(command);
                        }
                    }
                    catch (Exception ex) { }

                }
                CreateLeadGenRequest leadGenRequest = new(LeadSource.IVR, dto);
                var req = Mediator.Send(leadGenRequest).Result;
                _logger.Information("IntegrationController -> POST(IVR/MyOperator/Inbound) -> called, Dto: " + JsonConvert.SerializeObject(dto));
                return new(false);
            }
            catch (Exception ex)
            {
                return new(false);
            }
        }
        [ApiKey]
        [AllowAnonymous]
        [HttpPost("IVR/MyOperator/Outbound")]
        [OpenApiOperation("Push endpoint for Outbound MyOperator IVR integration", "")]
        public async Task<Response<bool>> PostMyOperatorIVROutbound([FromBody] Object dto) //MyOperatorIVROutboundDto
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.IVR))
                    return new(false, "IVR integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                if (dto != null)
                {
                    try
                    {
                        MyOperatorIVRInboundDto? inboundDto = System.Text.Json.JsonSerializer.Deserialize<MyOperatorIVRInboundDto>(dto.ToString());
                        if (inboundDto != null)
                        {
                            MyOperatorIVRInboundRequest command = inboundDto.Adapt<MyOperatorIVRInboundRequest>();
                            command.ApiKey = apiKey;
                            return await Mediator.Send(command);
                        }
                    }
                    catch (Exception ex) { }

                }
                CreateLeadGenRequest leadGenRequest = new(LeadSource.IVR, dto);
                var req = Mediator.Send(leadGenRequest).Result;
                _logger.Information("IntegrationController -> POST(IVR/MyOperator/Outbound) -> called, Dto: " + JsonConvert.SerializeObject(dto));
                return new(false);
            }
            catch (Exception ex)
            {
                return new(false);
            }
        }
        [ApiKey]
        [AllowAnonymous]
        [HttpPost("JustLead")]
        [OpenApiOperation("Push endpoint for JustLead integration", "")]
        public async Task<Response<bool>> PostJustLead([FromBody] ListingSitesIntegrationRequest lead)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.JustLead))
                    return new(false, "JustLead integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                lead.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.JustLead, lead);
                var req = await Mediator.Send(leadGenRequest);
                _logger.Information("IntegrationController -> POST(JustLead) -> called, Dto: " + JsonConvert.SerializeObject(lead));
                lead.LeadSource = Domain.Enums.LeadSource.JustLead;

                CheckDuplicateLeadRequest checkDuplicateLeadRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                var shouldCreateNewLead = await Mediator.Send(checkDuplicateLeadRequest);
                if (shouldCreateNewLead.Data)
                {
                    return await Mediator.Send(lead);
                }
                return shouldCreateNewLead;
            }
            catch (Exception ex)
            {
                return new(false);
            }
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("QuikrHomes")]
        [OpenApiOperation("Push endpoint for QuikrHomes integration", "")]
        public async Task<Response<bool>> PostQuikrHomes([FromBody] ListingSitesIntegrationRequest lead)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.QuikrHomes))
                    return new(false, "QuikrHomes integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                lead.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.QuikrHomes, lead);
                var req = await Mediator.Send(leadGenRequest);
                _logger.Information("IntegrationController -> POST(QuikrHomes) -> called, Dto: " + JsonConvert.SerializeObject(lead));

                lead.LeadSource = Domain.Enums.LeadSource.QuikrHomes;
                CheckDuplicateLeadRequest checkDuplicateLeadRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                var shouldCreateNewLead = await Mediator.Send(checkDuplicateLeadRequest);
                if (shouldCreateNewLead.Data)
                {
                    return await Mediator.Send(lead);
                }
                return shouldCreateNewLead;
            }
            catch (Exception ex)
            {
                return new(false);
            }
        }

        [HttpGet("subsource")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Integration)]
        [OpenApiOperation("Get all integration subsources.", "")]
        public async Task<Response<Dictionary<LeadSource, List<string?>?>>> GetAllSubSourceAsync()
        {
            return await Mediator.Send(new GetAllIntegrationSubSourceRequest());
        }

        [HttpPost("JustLead/Account")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        [OpenApiOperation("Create a JustLead Integration Account.", "")]
        public Task<Response<Guid>> Create([FromBody] CreateJustLeadIntegrationRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpPut("JustLead/Account")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Integration)]
        [OpenApiOperation("Update a JustLead Integration Account.", "")]
        public Task<Response<bool>> Update([FromBody] UpdateJustLeadIntegrationRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpPost("CommonFloor/Account")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        [OpenApiOperation("Create a CommonFloor Integration Account.", "")]
        public Task<Response<Guid>> Create([FromBody] CreateCommonFloorIntegrationRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpPut("CommonFloor/Account")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Integration)]
        [OpenApiOperation("Update a CommonFloor Integration Account.", "")]
        public Task<Response<bool>> Update([FromBody] UpdateCommonFloorIntegrationRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpPost("Cronberry")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        [OpenApiOperation("Create a new Cronberry IVR Integration.", "")]
        public async Task<Response<string>> CreateCronberryIntegration([FromBody] CreateCronberryIntegrationRequest request)
        {
            return await Mediator.Send(request);
        }
        //[ApiKey]
        //[AllowAnonymous]
        //[HttpPost("IVR/Cronberry/Inbound")]
        //[OpenApiOperation("Push endpoint for Inbound Cronberry IVR integration", "")]
        //public async Task<Response<bool>> PostCronberryInboundAsync([FromBody] Object dto)
        //{
        //    CreateLeadGenRequest leadGenRequest = new(LeadSource.IVR, dto);
        //    var req = Mediator.Send(leadGenRequest).Result;
        //    _logger.Information("IntegrationController -> POST(IVR/Cronberry/Inbound) -> called, Dto: " + JsonConvert.SerializeObject(dto));
        //    if (dto != null)
        //    {
        //        try
        //        {
        //            CronberryIVRInboundDto? inboundDto = System.Text.Json.JsonSerializer.Deserialize<CronberryIVRInboundDto>(dto.ToString());
        //            if (inboundDto != null)
        //            {
        //                CronberryIVRInboundRequest command = inboundDto.Adapt<CronberryIVRInboundRequest>();
        //                var apiKey = this.HttpContext.Request.Headers["API-Key"];
        //                command.ApiKey = apiKey;
        //                command.AccountId = AccountIdHelper.GetAccountId(command.ApiKey);
        //                return await Mediator.Send(command);
        //            }
        //        }
        //        catch (Exception ex) 
        //        {
        //            throw;
        //        }

        //    }
        //    return new(false);
        //}
        [AllowAnonymous]
        [HttpGet("IVR/Cronberry/Inbound/{tenant}/{base64}")]
        [OpenApiOperation("Get endpoint for Inbound Cronberry IVR integration", "")]
        public async Task<Response<bool>> GetCronberryInboundAsync(string tenant, string base64, [FromQuery] CronberryIVRInboundDto dto)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.IVR))
                    return new(false, "IVR integration is currently disabled");
                _logger.Information("IntegrationController -> Get(IVR/Cronberry/Inbound) -> called, Dto: " + JsonConvert.SerializeObject(dto));
                CreateLeadGenRequest leadGenRequest = new(LeadSource.IVR, dto);
                var req = Mediator.Send(leadGenRequest).Result;
                if (dto != null)
                {
                    CronberryIVRInboundRequest command = dto.Adapt<CronberryIVRInboundRequest>();
                    command.AccountId = base64.GetAccountId();
                    return await Mediator.Send(command);
                }
            }
            catch (Exception ex)
            {
                return new(false);
            }
            return new(false);
        }
        [HttpPost("Exotel")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        [OpenApiOperation("Create a new Exotel IVR Integration.", "")]
        public async Task<Response<string>> CreateExotelIntegration([FromBody] CreateExotelIntegrationRequest request)
        {
            return await Mediator.Send(request);
        }
        [AllowAnonymous]
        [HttpGet("IVR/Exotel/Inbound/{tenant}/{base64}")]
        [OpenApiOperation("Get endpoint for Inbound Exotel IVR integration", "")]
        public async Task<Response<bool>> GetExotelInboundAsync(string tenant, string base64, [FromQuery] ExotelAppletIncomingDto dto)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.IVR))
                    return new(false, "IVR integration is currently disabled");
                _logger.Information("IntegrationController -> Get(IVR/Exotel/Inbound) -> called, Dto: " + JsonConvert.SerializeObject(dto, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }));
                CreateLeadGenRequest leadGenRequest = new(LeadSource.IVR, dto);
                var req = Mediator.Send(leadGenRequest).Result;
                if (dto != null)
                {
                    ExotelIVRInboundRequest command = dto.Adapt<ExotelIVRInboundRequest>();
                    command.AccountId = base64.GetAccountId();
                    return await Mediator.Send(command);
                }
            }
            catch (Exception ex)
            {
                _logger.Information("IntegrationController -> Get(IVR/Exotel/Inbound) -> Exception : " + JsonConvert.SerializeObject(ex, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }));
                return new(false);
            }
            return new(false);
        }
        [AllowAnonymous]
        [HttpPost("IVR/Exotel/Outbound/{tenant}/{base64}")]
        [OpenApiOperation("Get endpoint for Outbound Exotel IVR integration", "")]
        public async Task<Response<bool>> GetExotelOutboundAsync(string tenant, string base64, [FromBody] ExotelAppletOutgoingDto dto)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.IVR))
                    return new(false, "IVR integration is currently disabled");
                _logger.Information("IntegrationController -> Get(IVR/Exotel/Outbound) -> called, Dto: " + dto.Serialize());
                CreateLeadGenRequest leadGenRequest = new(LeadSource.IVR, dto);
                var req = Mediator.Send(leadGenRequest).Result;
                if (dto != null)
                {
                    ExotelIVROutboundRequest command = dto.Adapt<ExotelIVROutboundRequest>();
                    command.AccountId = base64.GetAccountId();
                    return await Mediator.Send(command);
                }
            }
            catch (Exception ex)
            {
                _logger.Information("IntegrationController -> Get(IVR/Exotel/Outbound) -> Exception : " + ex.Serialize());
                return new(false);
            }
            return new(false);
        }
        #region Google Sheets
        [HttpPost("googlesheet/create/account")]
        [TenantIdHeader]
        public async Task<Response<bool>> CreateGoogleSheetIntegrationAsync([FromBody] CreateGoogleSheetIntegrationRequest request)
        {
            var response = await Mediator.Send(request);
            return response;
        }

        [HttpPost("googlesheet/columns")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        [OpenApiOperation("Get google sheet columns")]
        public async Task<ActionResult<Response<GoogleSheetColumnDto>>> GetGoogleSheetColumnsAsync(Guid SheetId)
        {
            try
            {
                return await Mediator.Send(new GetGoogleSheetColumnRequest(SheetId));
            }
            catch (Exception e)
            {
                _logger.Error("IntegrationController -> GetGoogleSheetHeadersAsync, Error: " + JsonConvert.SerializeObject(e));
                throw;
            }
        }
        [AllowAnonymous]
        [HttpPost("googlesheet/webhook/{tenant}")]
        [OpenApiOperation("push end point for google sheets trigger notification ", "")]
        public async Task<IActionResult> ProcessGoogleSheetsWebhook(string tenant)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.GoogleSheet))
                    return BadRequest(new Response<bool>(false, "GoogleSheet integration is currently disabled"));
                GoogleNotificationChannelData webhookData = new()
                {
                    ChannelId = this.HttpContext.Request.Headers["X-Goog-Channel-ID"],
                    ChannelToken = this.HttpContext.Request.Headers["X-Goog-Channel-Token"],
                    ChannelExpiration = this.HttpContext.Request.Headers["X-Goog-Channel-Expiration"],
                    ResourceId = this.HttpContext.Request.Headers["X-Goog-Resource-ID"],
                    ResourceUri = this.HttpContext.Request.Headers["X-Goog-Resource-URI"],
                    GoogleMessageNumber = this.HttpContext.Request.Headers["X-Goog-Message-Number"],
                    TenantId = tenant
                };
                CreateLeadGenRequest leadGenRequest = new(LeadSource.GoogleSheet, webhookData);
                var req = Mediator.Send(leadGenRequest).Result;
                _logger.Information("IntegrationController -> POST(googleSheets) -> called, GoogleSheets NotificationDto: : " + JsonConvert.SerializeObject(webhookData));
                await Mediator.Send(new ProcessGoogleSheetsWebhookRequest(webhookData));
                return Ok();
            }
            catch (Exception e)
            {
                _logger.Information("ProcessGoogleSheetsWebhook() called: " + JsonConvert.SerializeObject(e));
                return BadRequest(new
                {
                    Succeeded = false,
                    Message = $"An error occurred"
                });
            }
        }
        [HttpPost("googlesheet/automate")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        [OpenApiOperation("Automate google sheet")]
        public async Task<Response<bool>> AutomateGoogleSheetAsync(AutomateGoogleSheetRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("googleSheet/accounts")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        [OpenApiOperation("Get GoogleSheet integration accounts")]
        public async Task<PagedResponse<ViewGoogleSheetAccountDto, string>> GetGoogleSheetAccountsAsync([FromQuery] GetGoogleSheetAccountsRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("googleSheet/sheets")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        [OpenApiOperation("Get GoogleSheet integration accounts")]
        public async Task<PagedResponse<SheetDto, string>> GetGoogleSheetsAsync([FromQuery] GetSheetsBySpreadSheetIdRequest request)
        {
            return await Mediator.Send(request);
        }

        #endregion
        [HttpPost("voicePanel")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        [OpenApiOperation("Create a new Voice Panel IVR Integration.", "")]
        public Task<Response<string>> CreateVoicePanelIntegration([FromBody] CreateVoicePanelIntegrationRequest request)
        {
            return Mediator.Send(request);
        }
        [ApiKey]
        [AllowAnonymous]
        [HttpPost("IVR/VoicePanel/Inbound")]
        [OpenApiOperation("Push endpoint for Inbound VoicePanel IVR integration", "")]
        public async Task<Response<bool>> PostVoicePanelIVRInbound([FromBody] VoicePanelIntegrationInboundDto dto)
        {
            try
            {
                if (!(await IsSourceEnabled(LeadSource.IVR)))
                    return new Response<bool>(false, "IVR integration is currently disabled");
                _logger.Information("IntegrationController -> POST(IVR/VoicePanel/Inbound) -> called, Dto: " + JsonConvert.SerializeObject(dto));
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                CreateLeadGenRequest leadGenRequest = new(LeadSource.IVR, dto);
                var req = Mediator.Send(leadGenRequest).Result;
                VoicePanelInboundIntegrationRequest command = dto.Adapt<VoicePanelInboundIntegrationRequest>();
                command.ApiKey = apiKey;
                return await Mediator.Send(command);
            }
            catch (Exception ex)
            {
                return new Response<bool>
                {
                    Succeeded = false,
                    Message = ex.Message
                };
            }
        }
        //[ApiKey]
        //[AllowAnonymous]
        //[HttpPost("RealEstateIndia")]
        //[OpenApiOperation("Push endpoint for RealEstateIndia integration", "")]
        //public Task<Response<bool>> PostRealEstateIndia([FromBody] ListingSitesIntegrationRequest lead)
        //{
        //    CreateLeadGenRequest leadGenRequest = new(LeadSource.RealEstateIndia, lead);
        //    var req = Mediator.Send(leadGenRequest).Result;
        //    _logger.Information("IntegrationController -> POST(RealEstateIndia) -> called, Dto: " + JsonConvert.SerializeObject(lead));
        //    var apiKey = this.HttpContext.Request.Headers["API-Key"];
        //    lead.LeadSource = Domain.Enums.LeadSource.RealEstateIndia;
        //    lead.ApiKey = apiKey;
        //    var res = Mediator.Send(lead);
        //    return res;
        //}
        [ApiKey]
        [AllowAnonymous]
        [HttpPost("CommonFloor")]
        [OpenApiOperation("Push endpoint for CommonFloor integration", "")]
        public async Task<Response<bool>> PostCommonFloor([FromBody] ListingSitesIntegrationRequest lead)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.CommonFloor))
                    return new(false, "CommonFloor integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                lead.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.CommonFloor, lead);
                var req = await Mediator.Send(leadGenRequest);
                _logger.Information("IntegrationController -> POST(CommonFloor) -> called, Dto: " + JsonConvert.SerializeObject(lead));
                lead.LeadSource = Domain.Enums.LeadSource.CommonFloor;
                CheckDuplicateLeadRequest checkDuplicateLeadRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                var shouldCreateNewLead = await Mediator.Send(checkDuplicateLeadRequest);
                if (shouldCreateNewLead.Data)
                {
                    return await Mediator.Send(lead);
                }
                return shouldCreateNewLead;
            }
            catch (Exception)
            {
                return new(false);
            }
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("RealEstateIndia")]
        [OpenApiOperation("Push endpoint for RealEstateIndia integration", "")]
        public async Task<Response<bool>> PostNewRealEstateIndia([FromForm] RealStateIndiaFormDto request)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.RealEstateIndia))
                    return new(false, "Real Estate India integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["Api-Key"];
                CreateLeadGenRequest leadGenRequest = new(LeadSource.RealEstateIndia, request);
                var req = await Mediator.Send(leadGenRequest);
                _logger.Information("IntegrationController -> POST(RealEstateIndia) -> called, Dto: " + JsonConvert.SerializeObject(request));
                var lead = new RealStateIndiaListingSiteIntegrationRequest()
                {
                    LeadSource = Domain.Enums.LeadSource.RealEstateIndia,
                    ApiKey = apiKey,
                    PropertyId = request.Property_Id,
                    Name = request.Name,
                    Mobile = request.Mobile,
                    Email = request.Email,
                    EnquiryId = request.Inquiry_Id,
                    Subject = request.Subject,
                    Details = request.Details,
                    RecvDate = request.Recv_Date,
                    LookingFor = request.Lookinf_For,
                    Currency = request.Currency,
                    Address = request.Address,
                    PrimaryUser = request.PrimaryUser ?? null,
                    SecondaryUser = request.SecondaryUser ?? null,

                };
                CheckDuplicateLeadRequest checkDuplicateLeadRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                checkDuplicateLeadRequest.SerializedData = lead.Serialize();
                var shouldCreateNewLead = await Mediator.Send(checkDuplicateLeadRequest);
                if (shouldCreateNewLead.Data)
                {
                    return await Mediator.Send(lead);
                }
                return shouldCreateNewLead;
            }
            catch (Exception)
            {
                return new(false);
            }
        }

        [AllowAnonymous]
        [TenantIdHeader]
        [HttpPost("microsite")]
        [OpenApiOperation("Push End Point for Property Microsite")]
        public async Task<Response<bool>> CreateLeadFromMicrosite([FromBody] MicrositeIntegrationDto request)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.PropertyMicrosite))
                    return new(false, "PropertyMicrosite integration is currently disabled");

                CreateLeadGenRequest leadGenRequest = new(LeadSource.PropertyMicrosite, request);
                var req = await Mediator.Send(leadGenRequest);
                _logger.Information("IntegrationController -> POST(PropertyMicrosite) -> called, Dto: " + JsonConvert.SerializeObject(request));
                string countryCode = null;
                try
                {
                    PhoneNumberUtil numberUtil = PhoneNumberUtil.GetInstance();
                    PhoneNumber number = numberUtil.Parse(request.ContactNo, null);
                    countryCode = number.CountryCode.ToString();
                }
                catch
                {

                }
                var lead = new ListingSitesIntegrationRequest()
                {
                    Name = request.Name,
                    Mobile = request.ContactNo,
                    UserName = request.UserName,
                    SerialNo = request.SerialNo,
                    LeadSource = LeadSource.PropertyMicrosite,
                    CountryCode = countryCode
                };
                CheckDuplicateLeadRequest duplicateLeadRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                duplicateLeadRequest.SerializedData = lead.Serialize();
                var shouldCreateNewLead = await Mediator.Send(duplicateLeadRequest);
                if (shouldCreateNewLead.Data)
                {
                    return await Mediator.Send(lead);
                }
                return shouldCreateNewLead;
            }
            catch (Exception)
            {
                return new(false);
            }
        }

        [AllowAnonymous]
        [TenantIdHeader]
        [HttpGet("IVR/count")]
        [OpenApiOperation("Get IVR Primary Account Count", "")]
        public async Task<Response<int>> GetIVRPrimaryCount()
        {
            return await Mediator.Send(new GetIVRPrimaryAccountRequest());
        }

        [AllowAnonymous]
        [HttpPost("WhatsApp/{tenant}")]
        [OpenApiOperation("Push end point for WhatsApp inbound messages.", "")]
        public async Task<Response<bool>> ProcessWhatsAppWebHookData(string tenant, [FromBody] object dto)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.WhatsApp))
                    return new(false, "WhatsApp integration is currently disabled");

                ProcessWhatsAppWebhookRequest? whatsappWebhookRequest = JsonConvert.DeserializeObject<ProcessWhatsAppWebhookRequest>(dto.ToString() ?? string.Empty);
                if (whatsappWebhookRequest == null)
                {
                    return new(false);
                }
                CreateLeadGenRequest leadGenRequest = new(LeadSource.WhatsApp, whatsappWebhookRequest, true);
                var req = Mediator.Send(leadGenRequest).Result;
                _logger.Information("IntegrationController -> POST(WhatsApp/webhook) -> called, Dto: " + JsonConvert.SerializeObject(whatsappWebhookRequest));
                var response = await Mediator.Send(whatsappWebhookRequest);
                return response;
            }
            catch (Exception)
            {
                return new(false);
            }
        }

        [AllowAnonymous]
        [HttpPost("Interakt/{tenant}")]
        [OpenApiOperation("Push end point for Interakt WhatsApp inbound messages.", "")]
        public async Task<Response<bool>> InteraktWhatsAppWebHookData(string tenant, [FromBody] object dto)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.WhatsApp))
                    return new(false, "WhatsApp integration is currently disabled");
                InteraktWhatsAppWebhookRequest? whatsappWebhookRequest = JsonConvert.DeserializeObject<InteraktWhatsAppWebhookRequest>(dto.ToString() ?? string.Empty);
                if (whatsappWebhookRequest == null)
                {
                    return new(false);
                }
                CreateLeadGenRequest leadGenRequest = new(LeadSource.WhatsApp, whatsappWebhookRequest, true);
                var req = Mediator.Send(leadGenRequest).Result;
                _logger.Information("IntegrationController -> POST(Interakt/webhook) -> called, Dto: " + JsonConvert.SerializeObject(whatsappWebhookRequest));
                var response = await Mediator.Send(whatsappWebhookRequest);
                return response;
            }
            catch (Exception)
            {
                return new(false);
            }
        }

        [TenantIdHeader]
        [HttpPost("gmail/watch")]
        [OpenApiOperation("Set Watch for a gmail account", "")]
        public async Task<Response<bool>> SetWatch([FromBody] SetWatchForGmailAccountRequest request)
        {
            return await Mediator.Send(request);
        }

        [AllowAnonymous]
        [HttpGet("gmail/watch/all")]
        [OpenApiOperation("Set Watch for all gmail accounts", "")]
        public async Task<Response<bool>> SetWatch()
        {
            return await Mediator.Send(new SetWatchForAllGmailAccountsRequest());
        }


        //Newly Added
        [HttpPost("IVR/create/common")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        [OpenApiOperation("Common End Point for creating IVR Integration Account.", "")]
        public Task<Response<string>> CreateIVRIntegration([FromBody] CreateIVRIntegrationAccountRequest request)
        {
            return Mediator.Send(request);
        }
        //Newly Added
        [HttpPost("common/ClickToCall")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.IVRCall, LrbResource.Integration)]
        [OpenApiOperation("Click to Call Common IVR", "")]
        public async Task<Response<ClickToCallCommonResponseDto>> ClickToCall(CommonClickToCallRequest request)
        {
            return await Mediator.Send(request);
        }
        //Newly Added
        [HttpGet("ivr/service-providers")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Integration)]
        [OpenApiOperation("Get all IVR Service Providers")]
        public async Task<Response<Dictionary<IVRServiceProvider, string>>> GetAllIVRServiceProvidersAsync()
        {
            return await Mediator.Send(new GetAllIVRServiceProvidersRequest());
        }
        //Newly Added
        [HttpPut("ivr/update")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Integration)]
        [OpenApiOperation("Update an IVR Integration account", "")]
        public async Task<Response<bool>> UpdateIVRIntegrationAccount(UpdateIVRIntegrationAccountInfoRequest request)
        {
            return await Mediator.Send(request);
        }
        //Newly Added
        [HttpGet("ivr")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Integration)]
        [OpenApiOperation("Get an IVR Integration account", "")]
        public async Task<Response<ViewIntegrationAccountDto>> GetIVRIntegrationAccount([FromQuery] GetIntegrationAccountInfoByIdRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("ivr/acc/with-mapping")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Integration)]
        [OpenApiOperation("Get all IVR Integration accounts with Payload Mapping", "")]
        public async Task<Response<List<ViewIntegrationAccountDto>>> GetIVRIntegrationAccountWithMapping()
        {
            return await Mediator.Send(new IVRIntegrationAccsWithPayloadMappingRequest());
        }
        //Newly Added
        [ApiKey]
        [AllowAnonymous]
        [HttpPost("IVR/QKonnect/outbound")]
        [OpenApiOperation("Push endpoint(Outbound) for QKonnect", "")]
        public async Task<Response<bool>> PostQKonnectIVROutbound([FromForm] QKonnectCommonDto dto)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.IVR))
                    return new(false, "IVR integration is currently disabled");
                _logger.Information("IntegrationController -> POST(IVR/QKonnect/Outbound) -> called, Dto: " + JsonConvert.SerializeObject(dto));
                _logger.Information("IntegrationController -> POST(IVR/QKonnect/Outbound) -> called, Dto: " + dto);
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                CreateLeadGenRequest leadGenRequest = new(LeadSource.IVR, dto);
                var req = Mediator.Send(leadGenRequest).Result;
                QKonnectOutboundRequest qKonnectOutboundRequest = dto.Adapt<QKonnectOutboundRequest>();
                qKonnectOutboundRequest.ApiKey = apiKey;
                return await Mediator.Send(qKonnectOutboundRequest);
            }
            catch (Exception)
            {
                return new(false);
            }
        }
        //Newly Added
        [ApiKey]
        [AllowAnonymous]
        [HttpPost("IVR/QKonnect/inbound")]
        [OpenApiOperation("Push endpoint(Inbound) for QKonnect", "")]
        public async Task<Response<bool>> PostQKonnectIVRInbound([FromForm] QKonnectCommonDto dto)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.IVR))
                    return new(false, "IVR integration is currently disabled");
                _logger.Information("IntegrationController -> POST(IVR/QKonnect/inbound) -> called, Dto: " + JsonConvert.SerializeObject(dto));
                _logger.Information("IntegrationController -> POST(IVR/QKonnect/inbound) -> called, Dto: " + dto);
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                CreateLeadGenRequest leadGenRequest = new(LeadSource.IVR, dto);
                var req = Mediator.Send(leadGenRequest).Result;
                QKonnectInboundRequest qKonnectInboundRequest = dto.Adapt<QKonnectInboundRequest>();
                qKonnectInboundRequest.ApiKey = apiKey;
                return await Mediator.Send(qKonnectInboundRequest);
            }
            catch (Exception)
            {
                return new(false);
            }
        }
        //Newly Added
        [HttpGet("IVR/virtual-numbers")]
        [OpenApiOperation("Get All Virtual Numbers", " ")]
        [TenantIdHeader]
        public async Task<Response<List<string>>> GetAllIVRVirtualNumbersAsync()
        {
            return await Mediator.Send(new GetAllVirtualNumbersRequest());
        }
        //Newly Added
        [HttpGet("IVR/virtual-number/assignment-check")]
        [OpenApiOperation("To check if any Virtual Number has been assigned to a user", " ")]
        [TenantIdHeader]
        public async Task<Response<VirtualNumberAssignmentResponseDto>> CheckVirtualNumberAssignmentAsync([FromQuery] CheckVirtualNumberAssignmentRequest request)
        {
            return await Mediator.Send(request);
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("RoofandFloor")]
        [OpenApiOperation("Push endpoint for RoofandFloor integration", "")]
        public async Task<Response<bool>> RoofandFloor([FromBody] ListingSitesIntegrationRequest lead)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.RoofandFloor))
                    return new(false, "Roof & Floor integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                lead.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.RoofandFloor, lead);
                var req = Mediator.Send(leadGenRequest).Result;
                _logger.Information("IntegrationController -> POST(RoofandFloor) -> called, Dto: " + JsonConvert.SerializeObject(lead));
                lead.LeadSource = Domain.Enums.LeadSource.RoofandFloor;
                CheckDuplicateLeadRequest checkDuplicateLeadRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                checkDuplicateLeadRequest.SerializedData = lead.Serialize();
                var shouldCreateNewLead = await Mediator.Send(checkDuplicateLeadRequest);
                if (shouldCreateNewLead.Data)
                {
                    return await Mediator.Send(lead);
                }
                return shouldCreateNewLead;
            }
            catch (Exception)
            {
                return new(false);
            }
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("MicrosoftAds")]
        [OpenApiOperation("Push endpoint for Microsoft LandingPage.", "")]
        public async Task<Response<bool>> PostMicrosoftAdLandingPage([FromBody] ListingSitesIntegrationRequest lead)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.MicrosoftAds))
                    return new(false, "Microsoft Ads integration is currently disabled");
                lead.ApiKey = this.HttpContext.Request.Headers["API-Key"];
                CreateLeadGenRequest leadGenRequest = new(LeadSource.MicrosoftAds, lead);
                var req = await Mediator.Send(leadGenRequest);
                _logger.Information("IntegrationController -> POST(MicrosoftAds/LandingPage) -> called, Dto: " + JsonConvert.SerializeObject(lead));
                lead.LeadSource = Domain.Enums.LeadSource.MicrosoftAds;
                CheckDuplicateLeadRequest checkDuplicateLeadRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                checkDuplicateLeadRequest.SerializedData = lead.Serialize();
                var shouldCreateNewLead = await Mediator.Send(checkDuplicateLeadRequest);
                if (shouldCreateNewLead.Data)
                {
                    return await Mediator.Send(lead);
                }
                return shouldCreateNewLead;
            }
            catch (Exception)
            {
                return new(false);
            }
        }

        [HttpPut("filter")]
        [TenantIdHeader]
        [OpenApiOperation("Set Integration Lead Filter")]
        public async Task<Response<bool>> SetIntegrationFilter([FromBody] AddIntegrationFilterInfoRequest request)
        {
            return await Mediator.Send(request);
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPut("Lead/Update")]
        [OpenApiOperation("Update lead by contact number")]
        public async Task<Response<bool>> UpdateLeadAsync(UpdateLeadByContactNoRequest request)
        {
            if (!await IsSourceEnabled(LeadSource.WhatsApp))
                return new(false, "WhatsApp integration is currently disabled");
            request.ApiKey = this.HttpContext.Request.Headers["API-Key"];
            CreateLeadGenRequest leadGenRequest = new(request.LeadSource ?? LeadSource.WhatsApp, request);
            var req = await Mediator.Send(leadGenRequest);
            _logger.Information("IntegrationController -> Put(LeadUpdate) -> called, Dto: " + JsonConvert.SerializeObject(request));
            request.LeadSource = request.LeadSource ?? Domain.Enums.LeadSource.WhatsApp;
            return await Mediator.Send(request);
        }

        [AllowAnonymous]
        [TenantIdHeader]
        [HttpPost("project/microsite")]
        [OpenApiOperation("Push End Point for Project Microsite")]
        public async Task<Response<bool>> CreateLeadFromProjectMicrosite([FromBody] ProjectMicrositeIntegrationDto request)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.ProjectMicrosite))
                    return new(false, "ProjectMicrosite integration is currently disabled");

                CreateLeadGenRequest leadGenRequest = new(LeadSource.ProjectMicrosite, request);
                var req = await Mediator.Send(leadGenRequest);
                _logger.Information("IntegrationController -> POST(ProjectMicrosite) -> called, Dto: " + JsonConvert.SerializeObject(request));
                string countryCode = null;
                try
                {
                    PhoneNumberUtil numberUtil = PhoneNumberUtil.GetInstance();
                    PhoneNumber number = numberUtil.Parse(request.ContactNo, null);
                    countryCode = number.CountryCode.ToString();
                }
                catch
                {

                }

                var lead = new ListingSitesIntegrationRequest()
                {
                    Name = request.Name,
                    Mobile = request.ContactNo,
                    Email = request.Email,
                    Budget = request.Budget,
                    UserName = request.UserName,
                    SerialNo = request.SerialNo,
                    LeadSource = LeadSource.ProjectMicrosite,
                    UnitTypeId = request.UnitId,
                    Currency = request.Currency,
                    CountryCode = countryCode
                };
                CheckDuplicateLeadRequest duplicateLeadRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                var shouldCreateNewLead = await Mediator.Send(duplicateLeadRequest);
                if (shouldCreateNewLead.Data)
                {
                    return await Mediator.Send(lead);
                }
                return shouldCreateNewLead;
            }
            catch (Exception ex)
            {
                return new(false);
            }

        }

        //Newly Added
        [AllowAnonymous]
        [HttpPost("IVR/frejun/{tenant}")]
        [OpenApiOperation("Push endpoint(Inbound/Outbound) for FreJun", "")]
        public async Task<Response<bool>> PostFreJunIVROutbound(string tenant, FreJunCommonDto dto)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.IVR))
                    return new(false, "IVR integration is currently disabled");
                _logger.Information("IntegrationController -> POST(IVR/frejun/Outbound) -> called, Dto: " + JsonConvert.SerializeObject(dto));
                CreateLeadGenRequest leadGenRequest = new(LeadSource.IVR, dto);
                var req = Mediator.Send(leadGenRequest).Result;
                //var freJunSignature = this.HttpContext.Request.Headers["frejun-signature"];
                //string encodedString = this.HttpContext.Request.Method + this.HttpContext.Request.GetDisplayUrl() + JsonConvert.SerializeObject(dto);
                //var isSignatureEqualToEncodedString = freJunSignature.FirstOrDefault()?.Equals(encodedString);
                //_logger.Information("IntegrationController -> POST(IVR/frejun/Outbound) -> called, frejun-signature : " + freJunSignature.FirstOrDefault());
                //_logger.Information("IntegrationController -> POST(IVR/frejun/Outbound) -> called, encodedString : " + encodedString);
                //_logger.Information("IntegrationController -> POST(IVR/frejun/Outbound) -> called, isSignatureEqualToEncodedString : " + isSignatureEqualToEncodedString);
                FreJunWebhookRequest freJunOutboundRequest = dto.Adapt<FreJunWebhookRequest>();
                //freJunOutboundRequest.ApiKey = apiKey;
                return await Mediator.Send(freJunOutboundRequest);
            }
            catch (Exception ex)
            {
                return new(false);
            }
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("IVR/mcube")]
        [OpenApiOperation("Push endpoint MCube IVR integration", "")]
        public async Task<Response<bool>> PostMCubeIVRWebhook([FromBody] MCubeWebhookDto dto)
        {
            try
            {
                if (!(await IsSourceEnabled(LeadSource.IVR)))
                    return new Response<bool>(false, "IVR integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                MCubeWebhookRequest command = dto.Adapt<MCubeWebhookRequest>();
                command.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.IVR, dto);
                var req = Mediator.Send(leadGenRequest).Result;
                _logger.Information("IntegrationController -> POST(IVR/mcube) -> called, Dto: " + JsonConvert.SerializeObject(dto));
                return await Mediator.Send(command);
            }
            catch (Exception ex)
            {
                return new Response<bool>
                {
                    Succeeded = false,
                    Message = ex.Message
                };
            }
        }

        [AllowAnonymous]
        [HttpPost("PropertyWala/{tenant}")]
        [OpenApiOperation("Push endpoint for PropertyWala integration", "")]
        public async Task<Response<bool>> PostNewPropertyWala([FromRoute] string tenant, [FromForm] PropertyWalaPayloadDto payloadDto)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.PropertyWala))
                    return new(false, "PropertyWala integration is currently disabled");
                ListingSitesIntegrationRequest lead = payloadDto.Adapt<ListingSitesIntegrationRequest>();
                if (!string.IsNullOrWhiteSpace(payloadDto.AdditionalProperties))
                {
                    try
                    {
                        lead.AdditionalProperties = JsonConvert.DeserializeObject<Dictionary<string, string>>(payloadDto.AdditionalProperties);
                    }
                    catch (Exception ex)
                    {
                        _logger.Information($"PropertyWala - Push endpoint -> Exception : {ex.Serialize()}");
                    }
                }
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                if (string.IsNullOrWhiteSpace(lead.ApiKey) && string.IsNullOrWhiteSpace(apiKey))
                {
                    throw new ArgumentNullException("ApiKey is required!");
                }
                else if (string.IsNullOrWhiteSpace(lead.ApiKey) && !string.IsNullOrWhiteSpace(apiKey))
                {
                    lead.ApiKey = apiKey;
                }
                CreateLeadGenRequest leadGenRequest = new(LeadSource.PropertyWala, lead);
                var req = await Mediator.Send(leadGenRequest);
                _logger.Information("IntegrationController -> POST(PropertyWala) -> called, Dto: " + JsonConvert.SerializeObject(lead));
                lead.LeadSource = Domain.Enums.LeadSource.PropertyWala;
                CheckDuplicateLeadRequest checkDuplicateLeadRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                checkDuplicateLeadRequest.SerializedData = lead.Serialize();
                var shouldCreateNewLead = await Mediator.Send(checkDuplicateLeadRequest);
                if (shouldCreateNewLead.Data)
                {
                    return await Mediator.Send(lead);
                }
                return shouldCreateNewLead;
            }
            catch (Exception)
            {
                return new(false);
            }
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("IVR/Kommuno/Outbound")]
        [OpenApiOperation("Push endpoint(Outbound) for Kommuno", "")]
        public async Task<Response<bool>> PostKommunoIVROutbound([FromBody] KommunoIntegrationDto dto)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.IVR))
                    return new(false, "IVR integration is currently disabled");
                _logger.Information("IntegrationController -> POST(IVR/Kommuno/Outbound) -> called, Dto: " + JsonConvert.SerializeObject(dto));
                _logger.Information("IntegrationController -> POST(IVR/Kommuno/Outbound) -> called, Dto: " + dto);
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                CreateLeadGenRequest leadGenRequest = new(LeadSource.IVR, dto);
                var req = Mediator.Send(leadGenRequest).Result;
                KommunoOutboundRequest kommunoOutboundRequest = dto.Adapt<KommunoOutboundRequest>();
                kommunoOutboundRequest.ApiKey = apiKey;
                return await Mediator.Send(kommunoOutboundRequest);
            }
            catch (Exception)
            {
                return new(false);
            }
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("IVR/Kommuno/Inbound")]
        [OpenApiOperation("Push endpoint(Inbound) for Kommuno", "")]
        public async Task<Response<bool>> PostKommunoIVRInbound([FromBody] KommunoIntegrationDto dto)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.IVR))
                    return new(false, "IVR integration is currently disabled");
                _logger.Information("IntegrationController -> POST(IVR/Kommuno/Inbound) -> called, Dto: " + JsonConvert.SerializeObject(dto));
                _logger.Information("IntegrationController -> POST(IVR/Kommuno/Inbound) -> called, Dto: " + dto);
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                KommunoInboundRequest kommunoOutboundRequest = dto.Adapt<KommunoInboundRequest>();
                kommunoOutboundRequest.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.IVR, dto);
                var req = Mediator.Send(leadGenRequest).Result;
                return await Mediator.Send(kommunoOutboundRequest);
            }
            catch (Exception)
            {
                return new(false);
            }
        }

        [HttpPost("ivr/create/with-params")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        [OpenApiOperation("Create a new IVR Integration with parameters.", "")]
        public Task<Response<string>> Create([FromBody] CreateIVRIntegrationWithParamsRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpPost("common/ClickToCall/config")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.IVRCall, LrbResource.Integration)]
        [OpenApiOperation("Click to Call Common IVR using stored config", "")]
        public async Task<Response<ClickToCallCommonResponseDto>> ClickToCallFromConfig(ClickToCallWithConfigRequest request)
        {
            return await Mediator.Send(request);
        }

        #region IVR Common Push End Points
        //Post - Body
        [ApiKey]
        [AllowAnonymous]
        [HttpPost("IVR/common/body/{serviceprovider}")]
        [OpenApiOperation("Common Push endpoint (FromBody) for IVR integration from body", "")]
        public async Task<Response<bool>> PostIVRWebhookFromBody(string serviceprovider, [FromBody] object sourceData)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.IVR))
                    return new(false, "IVR integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                CommonIVRWebhookRequest command = new() { RequestBody = sourceData };
                command.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.IVR, sourceData);
                var req = Mediator.Send(leadGenRequest).Result;
                _logger.Information("IntegrationController -> POST(IVRCommonFromBody) -> called, Dto: " + sourceData);
                command.ServiceProvider = serviceprovider;
                return await Mediator.Send(command);
            }
            catch (Exception)
            {
                return new(false);
            }
        }
        //Post - Body - with TenantId and ApiKey in the URL
        [AllowAnonymous]
        [HttpPost("IVR/common/body/{serviceprovider}/{tenant}/{base64}")]
        [OpenApiOperation("Common Post endpoint (FromBody) for IVR integration from Body", "")]
        public async Task<Response<bool>> PostIVRWebhookFromBody(string serviceprovider, string tenant, string base64, [FromBody] object sourceData)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.IVR))
                    return new(false, "IVR integration is currently disabled");
                _logger.Information("IntegrationController -> Post(IVR common body) -> called, Dto: " + sourceData);
                CreateLeadGenRequest leadGenRequest = new(LeadSource.IVR, sourceData);
                var req = Mediator.Send(leadGenRequest).Result;
                if (sourceData != null)
                {
                    IVRIntermediateRequest command = new() { RequestBody = sourceData };
                    command.AccountId = base64.GetAccountId();
                    command.ServiceProvider = serviceprovider;
                    return await Mediator.Send(command);
                }
            }
            catch (Exception ex)
            {
                _logger.Information("IntegrationController -> Post(IVR common body) -> Exception : " + JsonConvert.SerializeObject(ex, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }));
                return new(false);

            }
            return new(false);
        }

        //Post - Form
        [ApiKey]
        [AllowAnonymous]
        [HttpPost("IVR/common/form/{serviceprovider}")]
        [OpenApiOperation("Common Push endpoint (FromForm) for IVR integration from form", "")]
        public async Task<Response<bool>> PostIVRWebhookFromForm(string serviceprovider, [FromForm] Dictionary<string, string> sourceData)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.IVR))
                    return new(false, "IVR integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                CommonIVRWebhookRequest command = new() { RequestBody = sourceData };
                command.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.IVR, sourceData);
                var req = Mediator.Send(leadGenRequest).Result;
                _logger.Information("IntegrationController -> POST(IVRCommonFromForm) -> called, Dto: " + sourceData.Serialize());
                command.ServiceProvider = serviceprovider;
                command.IsPayloadInFormData = true;
                return await Mediator.Send(command);
            }
            catch (Exception ex)
            {
                return new(false);
            }
        }
        //Post - Form - with TenantId and ApiKey in the URL
        [AllowAnonymous]
        [HttpPost("IVR/common/form/{serviceprovider}/{tenant}/{base64}")]
        [OpenApiOperation("Common Post endpoint (FromForm) for IVR integration from Form", "")]
        public async Task<Response<bool>> PostIVRWebhookFromForm(string serviceprovider, string tenant, string base64, [FromForm] Dictionary<string, string> sourceData)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.IVR))
                    return new(false, "IVR integration is currently disabled");
                _logger.Information("IntegrationController -> Get(IVR common form) -> called, Dto: " + sourceData.Serialize());
                CreateLeadGenRequest leadGenRequest = new(LeadSource.IVR, sourceData);
                var req = Mediator.Send(leadGenRequest).Result;
                if (sourceData != null)
                {
                    IVRIntermediateRequest command = new() { RequestBody = sourceData };
                    command.AccountId = base64.GetAccountId();
                    command.ServiceProvider = serviceprovider;
                    command.IsPayloadInFormData = true;
                    return await Mediator.Send(command);
                }
            }
            catch (Exception ex)
            {
                _logger.Information("IntegrationController -> Get(IVR common form) -> Exception : " + JsonConvert.SerializeObject(ex, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }));
                return new(false);
            }
            return new(false);
        }

        //Get - Query
        [ApiKey]
        [AllowAnonymous]
        [HttpGet("IVR/common/query/{serviceprovider}")]
        [OpenApiOperation("Common Push endpoint (FromQuery) for IVR integration from query", "")]
        public async Task<Response<bool>> GetIVRWebhookFromQuery(string serviceprovider, [FromQuery] Dictionary<string, string> sourceData)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.IVR))
                    return new(false, "IVR integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                CommonIVRWebhookRequest command = new() { RequestBody = sourceData };
                command.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.IVR, sourceData);
                var req = Mediator.Send(leadGenRequest).Result;
                _logger.Information("IntegrationController -> POST(IVRCommonFromQuery) -> called, Dto: " + sourceData.Serialize());
                command.ServiceProvider = serviceprovider;
                command.IsPayloadInFormData = true;
                return await Mediator.Send(command);
            }
            catch (Exception ex)
            {
                return new(false);
            }
        }
        //Get - Query - with TenantId and ApiKey in the URL
        [AllowAnonymous]
        [HttpGet("IVR/common/query/{serviceprovider}/{tenant}/{base64}")]
        [OpenApiOperation("Common Get endpoint (FromQuery) for IVR integration from Query", "")]
        public async Task<Response<bool>> PostIVRWebhookFromQuery(string serviceprovider, string tenant, string base64, [FromQuery] Dictionary<string, string> sourceData)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.IVR))
                    return new(false, "IVR integration is currently disabled");
                _logger.Information("IntegrationController -> Get(IVR common query) -> called, Dto: " + sourceData.Serialize());
                CreateLeadGenRequest leadGenRequest = new(LeadSource.IVR, sourceData);
                var req = Mediator.Send(leadGenRequest).Result;
                if (sourceData != null)
                {
                    IVRIntermediateRequest command = new() { RequestBody = sourceData };
                    command.AccountId = base64.GetAccountId();
                    command.ServiceProvider = serviceprovider;
                    command.IsPayloadInFormData = true;
                    return await Mediator.Send(command);
                }
            }
            catch (Exception ex)
            {
                _logger.Information("IntegrationController -> Get(IVR common query) -> Exception : " + JsonConvert.SerializeObject(ex, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }));
                return new(false);
            }
            return new(false);
        }
        #endregion

        [AllowAnonymous]
        [HttpPost("Whatsapp/Asputility/{tenant}")]
        [OpenApiOperation("Asputility WhatsApp Test End Point", "")]
        public async Task<Response<bool>> AsputilityWhatsappAsync(string tenant, AsputilityWhatsappRequest request)
        {
            request.Tenant = tenant;
            return await Mediator.Send(request);
        }
        [ApiKey]
        [AllowAnonymous]
        [HttpPost("MyGate")]
        [OpenApiOperation("Push endpoint for MyGate integration", "")]
        public async Task<Response<bool>> PostMyGateAsync([FromBody] ListingSitesIntegrationRequest lead)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.MyGate))
                    return new(false, "MyGate integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                lead.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.MyGate, lead);
                var req = await Mediator.Send(leadGenRequest);
                _logger.Information("IntegrationController -> POST(MyGate) -> called, Dto: " + JsonConvert.SerializeObject(lead));
                lead.LeadSource = Domain.Enums.LeadSource.MyGate;
                CheckDuplicateLeadRequest checkDuplicateLeadRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                checkDuplicateLeadRequest.SerializedData = lead.Serialize();
                var shouldCreateNewLead = await Mediator.Send(checkDuplicateLeadRequest);
                if (shouldCreateNewLead.Data)
                {
                    return await Mediator.Send(lead);
                }
                return shouldCreateNewLead;
            }
            catch (Exception ex)
            {
                return new(false);
            }
        }

        [AllowAnonymous]
        [HttpPost("test-whatsapp-payload")]
        [HttpGet("test-whatsapp-payload")]
        [OpenApiOperation("Testing end point for taking sample payload", "")]
        public void TestSamplePayload()
        {
            var contentType = this.HttpContext.Request.ContentType;
            var body = this.HttpContext.Request.Body;
            var query = this.HttpContext.Request.Query;
            var queryKeys = query.Keys;
            var queryDic = query.TryGetValue(queryKeys.FirstOrDefault(), out var outValue);

            //byte[] bytes = new byte[10000];
            //var readStream = body.ReadAsync(bytes);
            //string bodyInString = System.Text.Encoding.UTF8.GetString(bytes);
        }
        [HttpGet("fb/bulk/fetch/Status")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Integration)]
        [OpenApiOperation("Get facebook bulk fetch Status", "")]
        public async Task<PagedResponse<FaceBookFetchedLeadDto, int>> GetFbBulkFetchStatusAsync([FromQuery] GetFbBulkFetchStatusRequest request)
        {
            return await Mediator.Send(request);
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("Flipkart")]
        [OpenApiOperation("Push endpoint for Flipkart integration", "")]
        public async Task<object> PostFlipkart([FromBody] ListingSitesIntegrationRequest lead)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.Flipkart))
                {
                    var dynamicResponse = new
                    {
                        RESPONSE = new
                        {
                            SUCCEEDED = false,
                            MESSAGE = "Flipkart integration is currently disabled",
                            ERRORS = new List<string>(),
                            DATA = false
                        },
                        STATUS_CODE = 200
                    };
                    return dynamicResponse;
                }
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                lead.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.Flipkart, lead);
                var req = await Mediator.Send(leadGenRequest);
                _logger.Information("IntegrationController -> POST(Flipkart) -> called, Dto: " + JsonConvert.SerializeObject(lead));
                lead.LeadSource = Domain.Enums.LeadSource.Flipkart;
                CheckDuplicateLeadRequest checkDuplicateLeadRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                checkDuplicateLeadRequest.SerializedData = lead.Serialize();
                var shouldCreateNewLead = await Mediator.Send(checkDuplicateLeadRequest);
                if (shouldCreateNewLead.Data)
                {
                    var result = await Mediator.Send(lead);
                    if (result.Succeeded)
                    {
                        var dynamicResponse = new
                        {
                            RESPONSE = new
                            {
                                SUCCEEDED = result.Succeeded,
                                MESSAGE = result.Message,
                                ERRORS = result.Errors,
                                DATA = result.Data
                            },
                            STATUS_CODE = 200
                        };

                        // Configure JsonSerializerSettings to use default naming strategy
                        var jsonSettings = new JsonSerializerSettings
                        {
                            ContractResolver = new DefaultContractResolver
                            {
                                NamingStrategy = new DefaultNamingStrategy()
                            }
                        };

                        // Serialize the dynamic object with specified settings
                        string jsonResponse = JsonConvert.SerializeObject(dynamicResponse, jsonSettings);
                        return (object)jsonResponse;
                    }
                    else
                    {
                        var dynamicResponse = new
                        {
                            RESPONSE = new
                            {
                                SUCCEEDED = result.Succeeded,
                                MESSAGE = result.Message,
                                ERRORS = result.Errors,
                                DATA = result.Data
                            },
                            STATUS_CODE = 500
                        };

                        // Configure JsonSerializerSettings to use default naming strategy
                        var jsonSettings = new JsonSerializerSettings
                        {
                            ContractResolver = new DefaultContractResolver
                            {
                                NamingStrategy = new DefaultNamingStrategy()
                            }
                        };

                        // Serialize the dynamic object with specified settings
                        string jsonResponse = JsonConvert.SerializeObject(dynamicResponse, jsonSettings);
                        return (object)jsonResponse;
                    }

                }
                if (shouldCreateNewLead.Succeeded)
                {
                    var dynamicResponse = new
                    {
                        RESPONSE = new
                        {
                            SUCCEEDED = shouldCreateNewLead.Succeeded,
                            MESSAGE = shouldCreateNewLead.Message,
                            ERRORS = shouldCreateNewLead.Errors,
                            DATA = shouldCreateNewLead.Data
                        },
                        STATUS_CODE = 200
                    };

                    // Configure JsonSerializerSettings to use default naming strategy
                    var jsonSettings = new JsonSerializerSettings
                    {
                        ContractResolver = new DefaultContractResolver
                        {
                            NamingStrategy = new DefaultNamingStrategy()
                        }
                    };

                    // Serialize the dynamic object with specified settings
                    string jsonResponse = JsonConvert.SerializeObject(dynamicResponse, jsonSettings);
                    return (object)jsonResponse;
                }
                else
                {
                    var dynamicResponse = new
                    {
                        RESPONSE = new
                        {
                            SUCCEEDED = shouldCreateNewLead.Succeeded,
                            MESSAGE = shouldCreateNewLead.Message,
                            ERRORS = shouldCreateNewLead.Errors,
                            DATA = shouldCreateNewLead.Data
                        },
                        STATUS_CODE = 500
                    };

                    // Configure JsonSerializerSettings to use default naming strategy
                    var jsonSettings = new JsonSerializerSettings
                    {
                        ContractResolver = new DefaultContractResolver
                        {
                            NamingStrategy = new DefaultNamingStrategy()
                        }
                    };

                    // Serialize the dynamic object with specified settings
                    string jsonResponse = JsonConvert.SerializeObject(dynamicResponse, jsonSettings);
                    return (object)jsonResponse;
                }
            }
            catch (Exception ex)
            {
                return (object)"error";
            }
        }

        [AllowAnonymous]
        [HttpPost("webhook/{tenant}/{base64}")]
        [HttpGet("webhook/{tenant}/{base64}")]
        public async Task<Response<bool>> HandleWebhookPayload(string tenant, string base64)
        {
            if (!await IsSourceEnabled(LeadSource.Webhook))
                return new(false, "Webhook integration is currently disabled");
            ListingSitesWebhookIntegrationRequest lead = new(this.HttpContext.Request, tenant, base64);
            _logger.Information("IntegrationController -> POST(Webhook) -> called, Dto: " + tenant + base64);
            return await Mediator.Send(lead);

        }

        [AllowAnonymous]
        [HttpPost("Webhook/push/properties/{tenant}")]
        [HttpGet("Webhook/push/properties/{tenant}")]
        [OpenApiOperation("Create Parameters for Webhook integration", "")]
        [MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        public async Task<Response<TempVariable>> CreateAsync(string tenant)
        {
            try
            {
                CreateWebhookPayloadParamRequest request = new();
                request.HttpContext = this.HttpContext;
                return await Mediator.Send(request);
            }
            catch (Exception ex) { return new() { Message = ex.Message }; }
        }


        [TenantIdHeader]
        [HttpGet("Webhook/payload/parameters")]
        [OpenApiOperation("Get Webhook payload parameters", "")]
        [MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        public async Task<Response<WebhookVariablesDto>> GetWebhookAsync()
        {
            GetWebhookPayloadParamRequest request = new();
            return await Mediator.Send(request);
        }

        [HttpPut("Update/WebHook")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Integration)]
        [OpenApiOperation("Update the webhook payload by Id", "")]
        public async Task<Response<bool>> UpdateWebhookAsync(UpdateWebhookIntegrationPayloadRequest request)
        {
            //UpdateWebhookIntegrationPayloadRequest request = new(accountId);
            return await Mediator.Send(request);
        }
        [TenantIdHeader]
        [HttpGet("Webhook/AccountInfo")]
        [OpenApiOperation("Get Webhook AccountInfo By Id", "")]
        [MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        public Task<Response<ViewIntegrationAccountDto>> GetAsync(Guid id)
        {
            return Mediator.Send(new GetWebhookAccountInfoRequest(id));
        }

        [HttpPost("Extract/lead")]
        [AllowAnonymous]
        [TenantIdHeader]
        [OpenApiOperation("Extarct Lead from facebook.", "")]
        public async Task<Response<LeadInfoDto>> ExtractData(FacebookPageWebhookDto dto)
        {
            if (!await IsSourceEnabled(LeadSource.Facebook))
                return new() { Succeeded = false, Message = "Facebook integration is currently disabled" };
            CreateLeadGenRequest leadGenRequest = new(LeadSource.Facebook, dto);
            await Mediator.Send(leadGenRequest);
            StringValues tenantIds;
            this.HttpContext.Request.Headers.TryGetValue(MultitenancyConstants.TenantIdName, out tenantIds);

            if (tenantIds.Count > 0)
            {
                var tenantId = tenantIds[0];
            }
            else
            {
            }
            try
            {
                ExtractFacebookDataRequest request = new()
                {
                    Dto = dto,
                    TenantId = tenantIds[0]
                };
                var response = await Mediator.Send(request);
                return response;
            }
            catch (Exception ex)
            {
                return new() { Message = ex.Message };
            }

        }

        #region Bayut
        [HttpPost("bayut/account")]
        [TenantIdHeader]
        [OpenApiOperation("Create Bayut Integration Account", "")]
        [MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        public async Task<Response<Guid>> PostAsync(CreateBayutIntegrationRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPut("bayut/account")]
        [TenantIdHeader]
        [OpenApiOperation("Update Bayut Integration Account", "")]
        [MustHavePermission(LrbAction.Update, LrbResource.Integration)]
        public async Task<Response<bool>> PutAsync(UpdateBayutIntegrationRequest request)
        {
            return await Mediator.Send(request);
        }

        [AllowAnonymous]
        [HttpGet("bayut/{tenant}/{base64}")]
        [HttpPost("bayut/{tenant}/{base64}")]
        [OpenApiOperation("Get end point for bayut whatsapp integration", "")]
        public async Task<Response<bool>> PostWebhookBayut(string tenant, string base64)
        {
            if (!await IsSourceEnabled(LeadSource.Bayut))
                return new(false, "Bayut integration is currently disabled");
            BayutWAPushIntegrationRequest lead = new(this.HttpContext.Request, tenant, base64);
            _logger.Information("IntegrationController -> POST(Bayut) -> called, Dto: " + tenant + base64);
            return await Mediator.Send(lead);
        }

        [AllowAnonymous]
        [HttpGet("bayut/Stories/{tenant}/{base64}")]
        [HttpPost("bayut/Stories/{tenant}/{base64}")]
        [OpenApiOperation("Get end point for bayut whatsapp stories integration", "")]
        public async Task<Response<bool>> PostNewWebhookBayut(string tenant, string base64)
        {
            if (!await IsSourceEnabled(LeadSource.Bayut))
                return new(false, "Bayut integration is currently disabled");
            BayutWAStoriesPushIntegrationRequest lead = new(this.HttpContext.Request, tenant, base64);
            _logger.Information("IntegrationController -> POST(Bayut) -> called, Dto: " + tenant + base64);
            return await Mediator.Send(lead);
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("Bayut")]
        [OpenApiOperation("Push endpoint for Bayut integration", "")]
        public async Task<Response<bool>> PostBayutAsync([FromBody] ListingSitesIntegrationRequest lead)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.Bayut))
                    return new(false, "Bayut integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                lead.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.Bayut, lead);
                var req = await Mediator.Send(leadGenRequest);
                _logger.Information("IntegrationController -> POST(Bayut) -> called, Dto: " + JsonConvert.SerializeObject(lead));
                lead.LeadSource = Domain.Enums.LeadSource.Bayut;
                CheckDuplicateLeadRequest checkDuplicateLeadRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                checkDuplicateLeadRequest.SerializedData = lead.Serialize();
                var shouldCreateNewLead = await Mediator.Send(checkDuplicateLeadRequest);
                if (shouldCreateNewLead.Data)
                {
                    return await Mediator.Send(lead);
                }
                return shouldCreateNewLead;
            }
            catch (Exception ex)
            {
                return new(false);
            }
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("Bayut/Email")]
        [OpenApiOperation("Push endpoint for Bayut Email integration", "")]
        public async Task<Response<bool>> PostBayutEmailAsync([FromBody] ListingSitesIntegrationRequest lead)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.Bayut))
                    return new(false, "Bayut integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                lead.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.Bayut, lead);
                var req = await Mediator.Send(leadGenRequest);
                _logger.Information("IntegrationController -> POST(Bayut) -> called, Dto: " + JsonConvert.SerializeObject(lead));
                lead.LeadSource = Domain.Enums.LeadSource.Bayut;
                CheckDuplicateLeadRequest checkDuplicateLeadRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                checkDuplicateLeadRequest.SerializedData = lead.Serialize();
                var shouldCreateNewLead = await Mediator.Send(checkDuplicateLeadRequest);
                if (shouldCreateNewLead.Data)
                {
                    return await Mediator.Send(lead);
                }
                return shouldCreateNewLead;
            }
            catch (Exception ex)
            {
                return new(false);
            }
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("Bayut/Email-Lead")]
        [OpenApiOperation("Push endpoint for Bayut Email integration", "")]
        public async Task<Response<bool>> V2PostBayutEmailAsync([FromBody] ListingSitesIntegrationRequest lead)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.Bayut))
                    return new(false, "Bayut integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                lead.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.Bayut, lead);
                var req = await Mediator.Send(leadGenRequest);
                _logger.Information("IntegrationController -> POST(Bayut) -> called, Dto: " + JsonConvert.SerializeObject(lead));
                lead.LeadSource = Domain.Enums.LeadSource.Bayut;
                CheckDuplicateLeadRequest checkDuplicateLeadRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                checkDuplicateLeadRequest.SerializedData = lead.Serialize();
                var shouldCreateNewLead = await Mediator.Send(checkDuplicateLeadRequest);
                if (shouldCreateNewLead.Data)
                {
                    return await Mediator.Send(lead);
                }
                return shouldCreateNewLead;
            }
            catch (Exception ex)
            {
                return new(false);
            }
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("Bayut/IVR")]
        [OpenApiOperation("Push endpoint for Inbound Servetel IVR integration", "")]
        public async Task<Response<bool>> PostBayutIVRInbound([FromBody] ServetelIntegrationInboundDto dto)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.Bayut))
                    return new(false, "Bayut integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                ServetelInboundIntegrationRequest command = dto.Adapt<ServetelInboundIntegrationRequest>();
                command.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.Bayut, dto);
                var req = Mediator.Send(leadGenRequest).Result;
                _logger.Information("IntegrationController -> POST(Bayut/IVR) -> called, Dto: " + JsonConvert.SerializeObject(dto));
                // command.AccountId = accountId;
                return await Mediator.Send(command);
            }
            catch (Exception ex)
            {
                return new Response<bool>
                {
                    Succeeded = false,
                    Message = ex.Message
                };
            }
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("Bayut/Wa")]
        [OpenApiOperation("Push end point for Bayut integration", "")]
        public async Task<Response<bool>> PostBayut([FromBody] BayutWhatsappDto request)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.Bayut))
                    return new(false, "Bayut integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                CreateLeadGenRequest leadGenRequest = new(LeadSource.Bayut, request);
                var req = await Mediator.Send(leadGenRequest);
                _logger.Information("IntegrationController -> POST(Bayut) -> called, Dto: " + JsonConvert.SerializeObject(request));

                var lead = new WhatsAppListingSitesIntegrationRequest()
                {
                    LeadSource = Domain.Enums.LeadSource.Bayut,
                    ApiKey = apiKey,
                    Name = request?.enquirer?.name ?? "Unknown",
                    Mobile = request?.enquirer?.phone_number ?? string.Empty,
                    Notes = request?.message ?? string.Empty,
                    Link = request?.enquirer?.contact_link ?? string.Empty,
                    ReferenceId = request?.listing?.reference ?? string.Empty
                };
                return await Mediator.Send(lead);
            }
            catch (Exception ex)
            {
                return new(false);
            }
        }
        #endregion

        #region Dubizzle
        [HttpPost("dubizzle/account")]
        [TenantIdHeader]
        [OpenApiOperation("Create Dubizzle Integration Account", "")]
        [MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        public async Task<Response<Guid>> PostAsync(CreateDubizzleIntegrationRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPut("dubizzle/account")]
        [TenantIdHeader]
        [OpenApiOperation("Update Dubizzle Integration Account", "")]
        [MustHavePermission(LrbAction.Update, LrbResource.Integration)]
        public async Task<Response<bool>> PostAsync(UpdateDubizzleIntegrationRequest request)
        {
            return await Mediator.Send(request);
        }

        [AllowAnonymous]
        [HttpGet("dubizzle/{tenant}/{base64}")]
        [HttpPost("dubizzle/{tenant}/{base64}")]
        [OpenApiOperation("Get end point for dubizzle whatsapp integration", "")]
        public async Task<Response<bool>> PostWebhookDubizzle(string tenant, string base64)
        {
            if (!await IsSourceEnabled(LeadSource.Dubizzle))
                return new(false, "Dubizzle integration is currently disabled");
            DubizzleWAPushIntegrationRequest lead = new(this.HttpContext.Request, tenant, base64);
            _logger.Information("IntegrationController -> POST(Bayut) -> called, Dto: " + tenant + base64);
            return await Mediator.Send(lead);
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("Dubizzle")]
        [OpenApiOperation("Push endpoint for Dubizzle integration", "")]
        public async Task<Response<bool>> PostDubizzle([FromBody] ListingSitesIntegrationRequest lead)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.Dubizzle))
                    return new(false, "Dubizzle integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                lead.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.Dubizzle, lead);
                var req = await Mediator.Send(leadGenRequest);
                _logger.Information("IntegrationController -> POST(Dubizzle) -> called, Dto: " + JsonConvert.SerializeObject(lead));
                lead.LeadSource = Domain.Enums.LeadSource.Dubizzle;
                CheckDuplicateLeadRequest checkDuplicateLeadRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                checkDuplicateLeadRequest.SerializedData = lead.Serialize();
                var shouldCreateNewLead = await Mediator.Send(checkDuplicateLeadRequest);
                if (shouldCreateNewLead.Data)
                {
                    return await Mediator.Send(lead);
                }
                return shouldCreateNewLead;
            }
            catch (Exception ex)
            {
                return new(false);
            }
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("Dubizzle/WA")]
        [OpenApiOperation("Push end point for Dubizzle integration", "")]
        public async Task<Response<bool>> PostDubizzle([FromBody] BayutWhatsappDto request)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.Dubizzle))
                    return new(false, "Dubizzle integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                CreateLeadGenRequest leadGenRequest = new(LeadSource.Dubizzle, request);
                var req = await Mediator.Send(leadGenRequest);
                _logger.Information("IntegrationController -> POST(Dubizzle) -> called, Dto: " + JsonConvert.SerializeObject(request));
                var lead = new WhatsAppListingSitesIntegrationRequest()
                {
                    LeadSource = Domain.Enums.LeadSource.Dubizzle,
                    ApiKey = apiKey,
                    Name = request?.enquirer?.name ?? "Unknown",
                    Mobile = request?.enquirer?.phone_number ?? string.Empty,
                    Notes = request?.message ?? string.Empty,
                    Link = request?.enquirer?.contact_link ?? string.Empty,
                    ReferenceId = request?.listing?.reference ?? string.Empty
                };
                return await Mediator.Send(lead);
            }
            catch (Exception ex)
            {
                return new(false);
            }
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("Dubizzle/IVR")]
        [OpenApiOperation("Push endpoint for Inbound Servetel IVR integration", "")]
        public async Task<Response<bool>> PostDubizzleIVRInbound([FromBody] ServetelIntegrationInboundDto dto)
        {
            try
            {
                if (!(await IsSourceEnabled(LeadSource.Dubizzle)))
                    return new Response<bool>(false, "Dubizzle integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                ServetelInboundIntegrationRequest command = dto.Adapt<ServetelInboundIntegrationRequest>();
                command.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.Dubizzle, dto);
                var req = Mediator.Send(leadGenRequest).Result;
                _logger.Information("IntegrationController -> POST(Dubizzle/IVR) -> called, Dto: " + JsonConvert.SerializeObject(dto));
                return await Mediator.Send(command);
            }
            catch (Exception ex)
            {
                return new Response<bool>
                {
                    Succeeded = false,
                    Message = ex.Message
                };
            }
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("Dubizzle/Email")]
        [OpenApiOperation("Push endpoint for Dubizzle Email integration", "")]
        public async Task<Response<bool>> PostDubizzleEmailAsync([FromBody] ListingSitesIntegrationRequest lead)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.Dubizzle))
                    return new(false, "Dubizzle integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                lead.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.Dubizzle, lead);
                var req = await Mediator.Send(leadGenRequest);
                _logger.Information("IntegrationController -> POST(Bayut) -> called, Dto: " + JsonConvert.SerializeObject(lead));
                lead.LeadSource = Domain.Enums.LeadSource.Dubizzle;
                CheckDuplicateLeadRequest checkDuplicateLeadRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                checkDuplicateLeadRequest.SerializedData = lead.Serialize();
                var shouldCreateNewLead = await Mediator.Send(checkDuplicateLeadRequest);
                if (shouldCreateNewLead.Data)
                {
                    return await Mediator.Send(lead);
                }
                return shouldCreateNewLead;
            }
            catch (Exception ex)
            {
                return new(false);
            }
        }
        #endregion

        #region Property Finder
        [HttpPost("property-finder/account")]
        [TenantIdHeader]
        [OpenApiOperation("Create property finder Integration Account", "")]
        [MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        public async Task<Response<Guid>> PostAsync(CreatePropertyFinderIntegrationRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPut("property-finder/account")]
        [TenantIdHeader]
        [OpenApiOperation("Update property finder Integration Account", "")]
        [MustHavePermission(LrbAction.Update, LrbResource.Integration)]
        public async Task<Response<bool>> PutAsync(UpdatePropertyFinderIntegrationRequest request)
        {
            return await Mediator.Send(request);
        }

        [AllowAnonymous]
        [HttpGet("propertyfinder/{tenant}/{base64}")]
        [HttpPost("propertyfinder/{tenant}/{base64}")]
        [OpenApiOperation("Get end point for Property Finder whatsapp integration", "")]
        public async Task<Response<bool>> PostWebhookPropertyFinderAsync(string tenant, string base64)
        {
            if (!await IsSourceEnabled(LeadSource.PropertyFinder))
                return new(false, "PropertyFinder integration is currently disabled");
            PropertyFinderWAPushIntegrationRequest lead = new(this.HttpContext.Request, tenant, base64);
            _logger.Information("IntegrationController -> POST(PropertyFinder) -> called, Dto: " + tenant + base64);
            return await Mediator.Send(lead);
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("PropertyFinder")]
        [OpenApiOperation("Push endpoint for PropertyFinder integration", "")]
        public async Task<Response<bool>> PostPropertyFinder([FromBody] ListingSitesIntegrationRequest lead)
        {
            try
            {
                if (!await IsSourceEnabled(LeadSource.PropertyFinder))
                    return new(false, "PropertyFinder integration is currently disabled");
                var apiKey = this.HttpContext.Request.Headers["API-Key"];
                lead.ApiKey = apiKey;
                CreateLeadGenRequest leadGenRequest = new(LeadSource.PropertyFinder, lead);
                var req = await Mediator.Send(leadGenRequest);
                _logger.Information("IntegrationController -> POST(PropertyFinder) -> called, Dto: " + JsonConvert.SerializeObject(lead));
                lead.LeadSource = Domain.Enums.LeadSource.PropertyFinder;
                CheckDuplicateLeadRequest checkDuplicateLeadRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                checkDuplicateLeadRequest.SerializedData = lead.Serialize();
                var shouldCreateNewLead = await Mediator.Send(checkDuplicateLeadRequest);
                if (shouldCreateNewLead.Data)
                {
                    return await Mediator.Send(lead);
                }
                return shouldCreateNewLead;
            }
            catch (Exception ex)
            {
                return new(false);
            }
        }

        [AllowAnonymous]
        [HttpGet("pf/{tenant}/{base64}")]
        [HttpPost("pf/{tenant}/{base64}")]
        [OpenApiOperation("Get end point for Property Finder whatsapp integration", "")]
        public async Task<Response<bool>> PostWebhookPFAsync(string tenant, string base64)
        {
            if (!await IsSourceEnabled(LeadSource.PropertyFinder))
                return new(false, "PropertyFinder integration is currently disabled");
            PropertyFinderPushIntegrationRequestV2 lead = new(this.HttpContext.Request, tenant, base64);
            _logger.Information("IntegrationController -> POST(PropertyFinder) -> called, Dto: " + tenant + base64);
            return await Mediator.Send(lead);
        }
        #endregion

        [HttpPut("pixel-confguration")]
        [TenantIdHeader]
        [OpenApiOperation("Update Facebook account pixel configuration.", "")]
        [MustHavePermission(LrbAction.Update, LrbResource.Integration)]
        public async Task<Response<bool>> AddAsync(UpdateFacebookConfigurationRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("Email")]
        [TenantIdHeader]
        [OpenApiOperation("Send email", "")]
        public Task<Response<bool>> SendEmailAsync(SendIntegrationEmailRequest request)
        {
            return Mediator.Send(request);
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPatch("WhatsApp")]
        [OpenApiOperation("Patch endpoint for WhatsApp.com integration", "")]
        public async Task<Response<bool>> PatchWhatsAppAsync([FromBody] WhatsAppPatchLeadRequest lead)
        {
            var apiKey = this.HttpContext.Request.Headers["API-Key"];
            lead.ApiKey = apiKey;
            CreateLeadGenRequest leadGenRequest = new(LeadSource.WhatsApp, lead);
            var req = await Mediator.Send(leadGenRequest);
            _logger.Information("IntegrationController -> POST(WhatsApp) -> called, Dto: " + JsonConvert.SerializeObject(lead));
            lead.LeadSource = Domain.Enums.LeadSource.WhatsApp;
            return await Mediator.Send(lead);
        }
        private async Task<bool> IsSourceEnabled(LeadSource leadSource, CancellationToken cancellationToken = default)
        {
            try
            {
                var source = await _sourceRepo.FirstOrDefaultAsync(new GetSourceByValueSpecs((int)leadSource), cancellationToken);

                if (source == null)
                {
                    _logger.Warning($"Source {leadSource} not found");
                    return false;
                }
                if (source.IsEnabled == false) return false;
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Error checking source status for {leadSource}");
                return false;
            }
        }


        [HttpGet("facebook/account/new")]
        [TenantIdHeader]
        public async Task<PagedResponse<ViewFacebookAccountDtoV1, string>> GetAllFacebookAccountsAsync([FromQuery] GetAllFacebookAccountsRequest request)
        {
            var response = await Mediator.Send(request);
            return response;
        }
        [HttpGet("facebook/account-Ads")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Integration)]
        public async Task<PagedResponse<FacebookAdsInfoDto, string>> GetAllFacebookAdsIntegrationsAsync([FromQuery] GetAllFbAdsRequest request)
        {
            try
            {
                var response = await Mediator.Send(request);
                return response;
            }
            catch (Exception ex)
            {
                return new() { Message = ex.Message };
            }
        }
        [HttpGet("facebook/account-campaigns")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Integration)]
        public async Task<PagedResponse<FacebookCampaignInfoDto, string>> GetAllFacebookCampaignsIntegrationsAsync([FromQuery] GetAllFbCampaignsRequest request)
        {
            try
            {
                var response = await Mediator.Send(request);
                return response;
            }
            catch (Exception ex)
            {
                return new() { Message = ex.Message };
            }
        }
        [HttpGet("facebook/account-externalforms")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Integration)]
        public async Task<PagedResponse<FacebookLeadGenFormDto, string>> GetAllFacebookFormsIntegrationsAsync([FromQuery] GetAllFbFormsRequest request)
        {
            try
            {
                var response = await Mediator.Send(request);
                return response;
            }
            catch (Exception ex)
            {
                return new() { Message = ex.Message };
            }
        }
        [HttpPost("fb/toggle-subscription/new")]
        [TenantIdHeader]
        [OpenApiOperation("Toggle ads and forms subscription of Facebook", "")]
        public async Task<Response<bool>> SubscribeFacebook([FromBody] ToggleFacebookAdsAndFormsSubscriptionRequestV1 request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("fb/subscription")]
        [TenantIdHeader]
        [OpenApiOperation("Toggle Status Facebook Account", "")]
        public async Task<Response<bool>> SubscribeFacebook([FromQuery] GetAccountSubscriptionStatusRequest request)
        {
            return await Mediator.Send(request);
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpGet("GetLeadAssignment/{contactNo}")]
        [TenantIdHeader]
        [OpenApiOperation("Get Lead assignment by ContactNo", "")]
        public async Task<Response<AssignedUserDetailsDto>> GetAssignmentasync(string contactNo, string? countryCode)
        {
            GetAssignedUserDetailsByLeadContactRequest request =
                new(contactNo, countryCode, this.HttpContext.Request.Headers["tenant"], this.HttpContext.Request.Headers["API-Key"]);
            return await Mediator.Send(request);
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("Pabbly")]
        [OpenApiOperation("Pabbly api for dynamic integration", "")]
        public async Task<Response<bool>> CreatePabblyLeadAsync()
        {
            var apiKey = this.HttpContext.Request.Headers["API-Key"];
            PabblyListingSitesIntegrationRequest lead = new(apiKey, this.HttpContext.Request);
            _logger.Information("IntegrationController -> POST(Pabbly) -> called, Dto: " + apiKey);
            return await Mediator.Send(lead);
        }
        /*[ApiKey]
        [AllowAnonymous]
        [HttpPost("pabbly/prospect")]
        [OpenApiOperation("Pabbly Prospect Creation", "")]
        public async Task<Response<bool>> CreatePabblyProspectAsync()
        {
            //try
            //{
            //    var apiKey = this.HttpContext.Request.Headers["API-Key"];
            //    _logger.Information("IntegrationController -> POST(Pabbly/Prospect) -> called, API Key: " + apiKey);

            //    PabblyCreateProspectRequest request = new(apiKey, this.HttpContext.Request);
            //    var result = await Mediator.Send(request);

            //    _logger.Information("IntegrationController -> POST(Pabbly/Prospect) -> Result: " + JsonConvert.SerializeObject(result));
            //    return result;
            //}
            //catch (Exception ex)
            //{
            //    _logger.Error(ex, "IntegrationController -> POST(Pabbly/Prospect) -> Exception: {Message}", ex.Message);
            //    //return new Response<Guid>(Guid.Empty, false, "An error occurred while processing the prospect request");
            //}
            var apiKey = this.HttpContext.Request.Headers["API-Key"];
            PabblyCreateProspectRequest prospect = new(apiKey, this.HttpContext.Request);
            _logger.Information("IntegrationController -> POST(Pabbly) -> called, Dto: " + apiKey);
            return await Mediator.Send(prospect);
        }*/
        [ApiKey]
        [AllowAnonymous]
        [HttpPost("pabbly/lead")]
        [OpenApiOperation("Pabbly Lead Creation", "")]
        public async Task<Response<bool>> CreatePabblyLeadAsync([FromBody] ListingSitesIntegrationRequest request)
        {
            var apiKey = this.HttpContext.Request.Headers["API-Key"];
            request.ApiKey = apiKey;
            LeadSource? leadSource = null;
            if (!string.IsNullOrEmpty(request.Source))
            {
                request.Source = request.Source.Replace(" ", "").Trim();
                leadSource = EnumFromDescription.GetValueFromDescription<LeadSource>(request.Source);
                if (leadSource == LeadSource.Direct)
                {
                    leadSource = null;
                }
                CreateLeadGenRequest leadGenRequest = new(leadSource ?? LeadSource.Pabbly, request);
                var req = await Mediator.Send(leadGenRequest);
            }
            request.LeadSource = leadSource ?? LeadSource.Pabbly;
            return await Mediator.Send(request);
        }
        [ApiKey]
        [AllowAnonymous]
        [HttpPatch("pabbly/customfieldupdate")]
        [OpenApiOperation("update lead with custom fields", "")]
        public async Task<Response<bool>> CustomFieldUpdate([FromBody] CustomFieldUpdateRequest lead)
        {
            var apiKey = this.HttpContext.Request.Headers["API-Key"];
            lead.ApiKey = apiKey;
            CreateLeadGenRequest leadGenRequest = new(LeadSource.WhatsApp, lead);
            var req = await Mediator.Send(leadGenRequest);
            _logger.Information("IntegrationController -> POST(WhatsApp) -> called, Dto: " + JsonConvert.SerializeObject(lead));
            lead.LeadSource = Domain.Enums.LeadSource.Pabbly;
            return await Mediator.Send(lead);
        }
        [ApiKey]
        [AllowAnonymous]
        [HttpPost("pabbly/data")]
        [OpenApiOperation("Pabbly Lead Creation", "")]
        public async Task<Response<bool>> CreatePabblyProspectAsync([FromBody] PabblyCreateProspectRequest request)
        {
            var apiKey = this.HttpContext.Request.Headers["API-Key"];
            request.ApiKey = apiKey;
            LeadSource? leadSource = null;
            if (!string.IsNullOrEmpty(request.Source))
            {
                request.Source = request.Source.Replace(" ", "").Trim();
                leadSource = EnumFromDescription.GetValueFromDescription<LeadSource>(request.Source);
                if (leadSource == LeadSource.Direct)
                {
                    leadSource = null;
                }
                CreateLeadGenRequest leadGenRequest = new(leadSource ?? LeadSource.Pabbly, request);
                var req = await Mediator.Send(leadGenRequest);
            }
            request.LeadSource = leadSource ?? LeadSource.Pabbly;
            return await Mediator.Send(request);
        }
        [ApiKey]
        [AllowAnonymous]
        [HttpPost("pabbly/updatelead")]
        [OpenApiOperation("Pabbly Lead Creation", "")]
        public async Task<Response<bool>> UpdateLeadViaPabblyAsync([FromBody] UpdateLeadFromPabblyRequest request)
        {
            var apiKey = this.HttpContext.Request.Headers["API-Key"];
            request.ApiKey = apiKey;
            LeadSource? leadSource = null;
            if (!string.IsNullOrEmpty(request.Source))
            {
                request.Source = request.Source.Replace(" ", "").Trim();
                leadSource = EnumFromDescription.GetValueFromDescription<LeadSource>(request.Source);
                if (leadSource == LeadSource.Direct)
                {
                    leadSource = null;
                }
                CreateLeadGenRequest leadGenRequest = new(leadSource ?? LeadSource.Pabbly, request);
                var req = await Mediator.Send(leadGenRequest);
            }
            return await Mediator.Send(request);
        }
        [ApiKey]
        [AllowAnonymous]
        [HttpPost("pabbly/leadstatus")]
        [OpenApiOperation("Pabbly Lead Status Change", "")]
        public async Task<Response<bool>> UpdateLeadStatusViaPabblyAsync([FromBody] UpdateLeadStatusFromPabblyRequest request)
        {
            var apiKey = this.HttpContext.Request.Headers["API-Key"];
            request.ApiKey = apiKey;
            LeadSource? leadSource = null;
            if (!string.IsNullOrEmpty(request.Source))
            {
                request.Source = request.Source.Replace(" ", "").Trim();
                leadSource = EnumFromDescription.GetValueFromDescription<LeadSource>(request.Source);
                if (leadSource == LeadSource.Direct)
                {
                    leadSource = null;
                }
                CreateLeadGenRequest leadGenRequest = new(leadSource ?? LeadSource.Pabbly, request);
                var req = await Mediator.Send(leadGenRequest);
            }
            return await Mediator.Send(request);
        }
        [ApiKey]
        [AllowAnonymous]
        [HttpPost("pabbly/leadproject")]
        [OpenApiOperation("Pabbly Project Creation", "")]
        public async Task<Response<bool>> CreatePabblyProjectAsync([FromBody] CreateProjectFromPabblyRequest request)
        {
            var apiKey = this.HttpContext.Request.Headers["API-Key"];
            //LeadSource? leadSource = null;
            //request.Source = request.Source.Replace(" ", "").Trim();
            //leadSource = EnumFromDescription.GetValueFromDescription<LeadSource>(request.Source);
            //CreateLeadGenRequest leadGenRequest = new(leadSource ?? LeadSource.Pabbly, request);
            request.ApiKey = apiKey;
            return await Mediator.Send(request);
        }
        [ApiKey]
        [AllowAnonymous]
        [HttpPost("pabbly/leadproperty")]
        [OpenApiOperation("Pabbly Property Creation", "")]
        public async Task<Response<bool>> CreatePabblyProjectAsync([FromBody] CreatePropertyFromPabblyRequest request)
        {
            var apiKey = this.HttpContext.Request.Headers["API-Key"];
            //LeadSource? leadSource = null;
            //request.Source = request.Source.Replace(" ", "").Trim();
            //leadSource = EnumFromDescription.GetValueFromDescription<LeadSource>(request.Source);
            //CreateLeadGenRequest leadGenRequest = new(leadSource ?? LeadSource.Pabbly, request);
            request.ApiKey = apiKey;
            return await Mediator.Send(request);
        }
        //google ads account create
        [HttpPost("googleads/account/console")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        public async Task<Response<bool>> CreateGoogleAdsAccountIntegrationAsync([FromBody] CreateGoogleAdsIntegrationUsingConsoleRequest request)
        {
            try
            {
                var response = await Mediator.Send(request);
                return response;
            }
            catch (Exception ex)
            {
                return new() { Message = ex.Message };
            }
        }
        [HttpPost("googleads/bulk-fetch")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Integration)]
        [OpenApiOperation("Fetch google ads leads between a date range", "")]
        public async Task<Response<bool>> FetchGoogleAdsBulkLeadsAsync([FromQuery] GetGoogleAdsBulkLeadsRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("google-ads/account-Ads")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Integration)]
        public async Task<PagedResponse<GoogleAdsInfoDto, string>> GetAllGoogleAdAdsIntegrationsAsync([FromQuery] GetAllGoogleAdAdsRequest request)
        {
            try
            {
                var response = await Mediator.Send(request);
                return response;
            }
            catch (Exception ex)
            {
                return new() { Message = ex.Message };
            }
        }
        [HttpGet("google-ads/accounts")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Integration)]
        public async Task<PagedResponse<GoogleAdsAuthResponseDto, string>> GetAllGoogleAdsAccountsAsync([FromQuery] GetAllGoogleAdsIntegrationAccountRequest request)
        {
            var response = await Mediator.Send(request);
            return response;
        }
        [HttpDelete("googleads/account/{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Delete, LrbResource.Integration)]
        public async Task<Response<bool>> DeletegooglreadsIntegrationAsync(Guid id)
        {
            DeleteGoogleAdsAccountRequest request = new(id);
            var response = await Mediator.Send(request);
            return response;
        }
        [HttpGet("googleads/account-campaigns")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Integration)]
        public async Task<PagedResponse<GoogleAdsCampaignInfoDto, string>> GetAllFacebookCampaignsIntegrationsAsync([FromQuery] GetAllGoogleAdsCampaignsRequest request)
        {
            try
            {
                var response = await Mediator.Send(request);
                return response;
            }
            catch (Exception ex)
            {
                return new() { Message = ex.Message };
            }
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("UpdateLeadAssignment")]
        [TenantIdHeader]
        [OpenApiOperation("Lead assignment by Lead ContactNo", "")]
        public async Task<Response<bool>> AssignLeadByLeadContactNo(UpdateLeadAssignmentByContactNoRequest request)
        {
            var apiKey = this.HttpContext.Request.Headers["API-Key"];
            request.ApiKey ??= apiKey;
            var response = await Mediator.Send(request);
            return response;
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpGet("GetUsersBasicInfo")]
        [TenantIdHeader]
        [OpenApiOperation("Lead assignment by Lead ContactNo", "")]
        public async Task<Response<List<ViewUserBasicDetailsDto>>> GetUsersBasicInfo([FromQuery] GetUsersBasicInfoRequest request)
        {
            StringValues tenantIds;
            this.HttpContext.Request.Headers.TryGetValue(MultitenancyConstants.TenantIdName, out tenantIds);

            if (tenantIds.Count > 0)
            {
                request.TenantId ??= tenantIds[0];
            }
            var response = await Mediator.Send(request);
            return response;
        }

        [HttpGet("google-ads/account/names")]
        [TenantIdHeader]
        [OpenApiOperation("Get all google ads account names", "")]
        public async Task<PagedResponse<GoogleAdsNamesAuthResponseDto, string>> GetAllGoogleAdsAccountNamesAsync([FromQuery] GetAllGoogleAdsIntegrationAccountNamesRequest request)
        {
            var response = await Mediator.Send(request);
            return response;
        }
        [AllowAnonymous]
        [HttpGet("voice2/IVR/{tenant}/{base64}")]
        [HttpPost("voice2/IVR/{tenant}/{base64}")]
        [OpenApiOperation("Ivr Integration By HttpRequest", "")]
        public async Task<Response<bool>> PostWebhookVoice2IVR(string tenant, string base64)
        {
            if (!await IsSourceEnabled(LeadSource.IVR))
                return new(false, "Ivr integration is currently disabled");
            Voice2IVRIntegrationRequest lead = new(this.HttpContext.Request, tenant, base64);
            _logger.Information("IntegrationController -> POST(voice2) and GET(voice2) -> called, Dto: " + tenant + base64);
            return await Mediator.Send(lead);
        }
        [HttpGet("all/subsources")]
        [TenantIdHeader]
        [OpenApiOperation("Get all subsources ", "")]
        public async Task<Response<List<IntegrationInformationDto>>> GetAllSourcesAsync([FromQuery] GetIntegrationsBySourcesRequest request)
        {
            return await Mediator.Send(request);
        }
    }
}
