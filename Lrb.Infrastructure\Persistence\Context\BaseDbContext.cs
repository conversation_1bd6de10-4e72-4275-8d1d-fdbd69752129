using System.Data;
using Finbuckle.MultiTenant;
using Lrb.Application.Common.Events;
using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.Multitenancy;
using Lrb.Domain.Common.Contracts;
using Lrb.Domain.Entities;
using Lrb.Infrastructure.Auditing;
using Lrb.Infrastructure.Identity;
using Lrb.Infrastructure.Multitenancy;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;

namespace Lrb.Infrastructure.Persistence.Context;

public abstract class BaseDbContext : MultiTenantIdentityDbContext<ApplicationUser, ApplicationRole, string, IdentityUserClaim<string>, IdentityUserRole<string>, ApplicationUserLogin, ApplicationRoleClaim, ApplicationUserToken>
{
    protected readonly ICurrentUser _currentUser;
    protected readonly ISerializerService _serializer;
    protected readonly DatabaseSettings _dbSettings;
    protected readonly IEventPublisher _events;
    protected readonly IServiceProvider _provider;

    protected BaseDbContext(ITenantInfo currentTenant, DbContextOptions options, ICurrentUser currentUser, ISerializerService serializer, IOptions<DatabaseSettings> dbSettings, IEventPublisher events, IServiceProvider provider)
        : base(currentTenant, options)
    {
        _currentUser = currentUser;
        _serializer = serializer;
        _dbSettings = dbSettings.Value;
        _events = events;
        _provider = provider;
    }

    // Used by Dapper
    public IDbConnection Connection => Database.GetDbConnection();

    public DbSet<Trail> AuditTrails => Set<Trail>();

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // QueryFilters need to be applied before base.OnModelCreating
        modelBuilder.AppendGlobalQueryFilter<ISoftDelete>(s => s.DeletedOn == null);

        base.OnModelCreating(modelBuilder);

        modelBuilder.ApplyConfigurationsFromAssembly(GetType().Assembly);
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        // TODO: We want this only for development probably... maybe better make it configurable in logger.json config?
        optionsBuilder.EnableSensitiveDataLogging();

        // If you want to see the sql queries that efcore executes:

        // Uncomment the next line to see them in the output window of visual studio
        // optionsBuilder.LogTo(m => System.Diagnostics.Debug.WriteLine(m), Microsoft.Extensions.Logging.LogLevel.Information);

        // Or uncomment the next line if you want to see them in the console
        // optionsBuilder.LogTo(Console.WriteLine, Microsoft.Extensions.Logging.LogLevel.Information);

        if (!string.IsNullOrWhiteSpace(TenantInfo?.ConnectionString))
        {
            optionsBuilder.UseDatabase(_dbSettings.DBProvider, TenantInfo.ConnectionString);
        }
        optionsBuilder.EnableSensitiveDataLogging(true);
    }

  
}