﻿using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Lead.Web;

namespace Lrb.Application.DataManagement.Web.Request
{
    public class Convert2LeadContactNoCheckRequest : IRequest<Response<List<Guid>>>
    {
        public List<BulkContactCheck> ContactDetails { get; set; }
        public Convert2LeadContactNoCheckRequest(List<BulkContactCheck> contactDetails)
        {
            ContactDetails = contactDetails;
        }
    }

    public class Convert2LeadContactNoCheckRequestHandler : IRequestHandler<Convert2LeadContactNoCheckRequest, Response<List<Guid>>>
    {
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Lead> _leadRepo;
        private readonly IRepositoryWithEvents<Prospect> _prospectRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.GlobalSettings> _globalSettings;
        private readonly IRepositoryWithEvents<DuplicateLeadFeatureInfo> _duplicateInfoRepo;

        public Convert2LeadContactNoCheckRequestHandler(IRepositoryWithEvents<Lrb.Domain.Entities.Lead> leadRepo,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettings,
            IRepositoryWithEvents<Prospect> prospectRepo,
            IRepositoryWithEvents<DuplicateLeadFeatureInfo> duplicateInfoRepo)
        {
            _leadRepo = leadRepo;
            _globalSettings = globalSettings;
            _prospectRepo = prospectRepo;
            _duplicateInfoRepo = duplicateInfoRepo;
        }

        public async Task<Response<List<Guid>>> Handle(Convert2LeadContactNoCheckRequest request, CancellationToken cancellationToken)
        {
            try
            {
                if (request.ContactDetails == null) { throw new Exception("Contact No field can not be empty."); }
                Domain.Entities.GlobalSettings? globalSettings = await _globalSettings.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                var countryCode = globalSettings?.Countries?.FirstOrDefault()?.DefaultCallingCode;
                var primaryContacts = request.ContactDetails
                    .Select(i => i.PrimaryContactNo)
                    .Where(contact => !string.IsNullOrEmpty(contact))
                    .Select(contact => $"{contact}")
                    .ToList();
                var alternativeContacts = request.ContactDetails
                    .Select(i => i.AlternateContactNo)
                    .Where(contact => !string.IsNullOrEmpty(contact))
                    .Select(contact => $"{contact}")
                    .ToList();
                //List<string> contactNumbers = primaryContacts.Concat(alternativeContacts).ToList() ?? new List<string?>();
                var leads = (await _leadRepo.ListAsync(new CheckLeadByContactNoSpec(primaryContacts, alternativeContacts))).ToList();
                List<Guid> result = new List<Guid>();
                var duplicateFeatureInfo = (await _duplicateInfoRepo.ListAsync(cancellationToken)).FirstOrDefault();
                foreach (var details in request.ContactDetails)
                {
                    if (leads.Any(lead => ((lead.ContactNo != null && details.PrimaryContactNo != null && lead.ContactNo == (details.PrimaryContactNo)) ||
                      (lead.ContactNo != null && details.AlternateContactNo != null && lead.ContactNo == (details.AlternateContactNo)) ||
                      (lead.AlternateContactNo != null && details.AlternateContactNo != null && lead.AlternateContactNo == (details.AlternateContactNo)) ||
                      (lead.AlternateContactNo != null && details.PrimaryContactNo != null && lead.AlternateContactNo == (details.PrimaryContactNo))) && (duplicateFeatureInfo != null && duplicateFeatureInfo.IsSourceBased) && !(duplicateFeatureInfo.Sources?.Any(i => i == (int)(lead.Enquiries?.FirstOrDefault()?.LeadSource ?? LeadSource.Any)) ?? false)
                      ))
                    {
                        result.Add(details.ProspectId ?? Guid.Empty);
                    }

                }
                return new(result);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
    }
    public class BulkContactCheck
    {
        public Guid? ProspectId { get; set; }
        public string? PrimaryContactNo { get; set; }
        public string? AlternateContactNo { get; set; }
    }
}
