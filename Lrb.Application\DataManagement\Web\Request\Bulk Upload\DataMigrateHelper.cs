﻿using Lrb.Application.Common.TimeZone;
using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Mappings;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.Source.Web;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.DataManagement;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using Newtonsoft.Json;
using PhoneNumbers;
using System.Data;
using System.Globalization;
using System.Text.RegularExpressions;
using Lrb.Application.Utils;
using ThirdParty.Json.LitJson;
using System.Threading;

namespace Lrb.Application.DataManagement.Web.Request.Bulk_Upload
{
    public static class DataMigrateHelper
    {
        public static async Task<List<Domain.Entities.Prospect>> DataMigrateAsync(
               this DataTable table,
               Dictionary<DataMigrateColumns, string>? mappedColumnsData,
               List<string> unMappedColumns,
               List<MasterPropertyType> propertyTypes,
               List<CustomProspectStatus> leadStatuses,
               List<UserDetailsDto> users,
               List<MasterProspectSource> prospectSources,
               IRepositoryWithEvents<Domain.Entities.Property> propertyRepo,
               IRepositoryWithEvents<Lrb.Domain.Entities.Project> projectRepo,
                List<Domain.Entities.Agency> agencies,
                 DataMigrateTracker? dataMigrateTracker,
                 Domain.Entities.GlobalSettings globalSettings,
                 List<Domain.Entities.ChannelPartner> channelPartners,
                 List<Domain.Entities.Campaign> campaigns,
                 string jsonData=null
                 )
        {
            List<Domain.Entities.Prospect> datas = new();
            if (mappedColumnsData == null)
            {
                return datas;
            }
            else
            {
                foreach (DataRow row in table.Rows)
                {
                    Domain.Entities.Prospect data = await ConstructLeadAsync(mappedColumnsData, unMappedColumns, propertyTypes, leadStatuses, users, propertyRepo,
                        projectRepo, row, prospectSources, agencies, dataMigrateTracker, globalSettings, channelPartners,campaigns,jsonData);
                    datas.Add(data);
                }
            }

            return datas;
        }

        private static async Task<Domain.Entities.Prospect> ConstructLeadAsync(Dictionary<DataMigrateColumns, string> mappedColumnsData, List<string> unMappedColumns, List<MasterPropertyType> propertyTypes,
            List<CustomProspectStatus> leadStatuses, List<UserDetailsDto> users, IRepositoryWithEvents<Domain.Entities.Property> propertyRepo, IRepositoryWithEvents<Lrb.Domain.Entities.Project> projectRepo,
            DataRow row, List<MasterProspectSource> prospectSources, List<Domain.Entities.Agency> agencies, DataMigrateTracker? dataMigrateTracker, Domain.Entities.GlobalSettings globalSettings,
            List<Domain.Entities.ChannelPartner> channelPartners, List<Domain.Entities.Campaign> campaigns, string jsonData =null)
        {
            var name = mappedColumnsData.GetStringValue(DataMigrateColumns.Name, row);
            name = string.IsNullOrWhiteSpace(name?.Trim()) ? "Unknown" : name.Trim();
            var contactNo = mappedColumnsData.GetStringValue(DataMigrateColumns.ContactNo, row);
            var altContactNo = mappedColumnsData.GetStringValue(DataMigrateColumns.AlternateContactNo, row);
            var scheduledDate = mappedColumnsData.GetScheduledDate(DataMigrateColumns.ScheduledDate, row,jsonData);
            var createdon = mappedColumnsData.GetCreatedDate(DataMigrateColumns.CreatedDate, row,jsonData);
            var email = mappedColumnsData.GetStringValue(DataMigrateColumns.Email, row);
            string? lowerBudget = string.Empty;
            string? upperBudget = string.Empty;
            var city = mappedColumnsData.GetStringValue(DataMigrateColumns.City, row);
            var state = mappedColumnsData.GetStringValue(DataMigrateColumns.State, row);
            var location = mappedColumnsData.GetStringValue(DataMigrateColumns.Location, row);
            string? enquiryFor = !mappedColumnsData.ContainsKey(DataMigrateColumns.EnquiryTypes) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.EnquiryTypes]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.EnquiryTypes]].ToString();
            string? bhkType = !mappedColumnsData.ContainsKey(DataMigrateColumns.BHKTypes) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.BHKTypes]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.BHKTypes]].ToString();
            string? noOfBhks = !mappedColumnsData.ContainsKey(DataMigrateColumns.BHKs) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.BHKs]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.BHKs]].ToString();
            string? baseProperty = !mappedColumnsData.ContainsKey(DataMigrateColumns.BasePropertyType) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.BasePropertyType]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.BasePropertyType]].ToString();
            string? subProperty = !mappedColumnsData.ContainsKey(DataMigrateColumns.SubPropertyType) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.SubPropertyType]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.SubPropertyType]].ToString();
            var properties = await mappedColumnsData.GetPropertiesAsync(DataMigrateColumns.Property, row, propertyRepo);
            var enquiryInfo = BulkUploadHelper.GetEnquiryForInfo(enquiryFor ?? string.Empty);
            var projects = await mappedColumnsData.GetProjectsAsync(DataMigrateColumns.Project, row, projectRepo);
            //(MasterPropertyType? PropertyType, BHKType? BHKType, double? NoOfBHK) = GetPropertyTypeInfo(mappedColumnsData, row, propertyTypes);
           // var propertyInfo = BulkUploadHelper.GetPropertyType(baseProperty, subProperty, propertyTypes, bhkType, noOfBhks);
            var leadSource = mappedColumnsData.GetEnumValue<LeadSource>(DataMigrateColumns.Source, row);
            leadSource ??= mappedColumnsData.GetStringValue(DataMigrateColumns.Source, row)?.GetValueFromDescription<LeadSource>();
            var subSource = mappedColumnsData.GetStringValue(DataMigrateColumns.SubSource, row);
            var agencyName = mappedColumnsData.GetStringValue(DataMigrateColumns.AgencyName, row);
            var agencyToAdd = agencies.FirstOrDefault(i => i.Name.Trim().ToLower().Equals(agencyName?.Trim().ToLower(), StringComparison.InvariantCultureIgnoreCase));
            var createdDate = mappedColumnsData.GetDataTimeValue(DataMigrateColumns.CreatedDate, row);
            string? dataSource = !mappedColumnsData.ContainsKey(DataMigrateColumns.Source) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.Source]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.Source]].ToString();
            //var validSource = GetValidDataSource(dataSource ?? string.Empty, prospectSources);
            var source = prospectSources.Where(i => i.IsDefault).FirstOrDefault();
            var user = mappedColumnsData.GetStringValue(DataMigrateColumns.AssignToUser, row);
            var assignToUser = users.FirstOrDefault(u => u.UserName.ToLower() == user.ToLower().Trim().Replace(" ", ""));
            string currecncy = !mappedColumnsData.ContainsKey(DataMigrateColumns.Currency) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.Currency]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.Currency]].ToString();
            //var currencycode = mappedColumnsData.GetCurrencySymbol1(row, currecncy);
            var currencycode = mappedColumnsData.GetCurrencySymbol1DataMigrate(row, currecncy, globalSettings?.Countries?.FirstOrDefault()?.DefaultCurrency);
            string budget = !mappedColumnsData.ContainsKey(DataMigrateColumns.Budget) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.Budget]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.Budget]].ToString();
            var cpName = !mappedColumnsData.ContainsKey(DataMigrateColumns.ChannelPartnerName) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.ChannelPartnerName]) ? null : row[mappedColumnsData[DataMigrateColumns.ChannelPartnerName]].ToString();
            var channelPartenr = channelPartners.FirstOrDefault(i => i.FirmName.Trim().ToLower().Equals(cpName?.Trim().ToLower(), StringComparison.InvariantCultureIgnoreCase));
            var prospectStatus = mappedColumnsData.GetLeadStatus(row, leadStatuses);
            string countryCode = !mappedColumnsData.ContainsKey(DataMigrateColumns.CountryCode) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.CountryCode]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.CountryCode]].ToString();
            string altCountryCode = !mappedColumnsData.ContainsKey(DataMigrateColumns.AlternativeNoCountryCode) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.AlternativeNoCountryCode]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.AlternativeNoCountryCode]].ToString();
            var beds = !mappedColumnsData.ContainsKey(DataMigrateColumns.Beds) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.Beds]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.Beds]].ToString();
            var baths = !mappedColumnsData.ContainsKey(DataMigrateColumns.Baths) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.Baths]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.Baths]].ToString();
            var furnishStatus = !mappedColumnsData.ContainsKey(DataMigrateColumns.FurnishStatus) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.FurnishStatus]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.FurnishStatus]].ToString();
            string floor = !mappedColumnsData.ContainsKey(DataMigrateColumns.PreferredFloor) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.PreferredFloor]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.PreferredFloor]].ToString();
            string bHKTypes = !mappedColumnsData.ContainsKey(DataMigrateColumns.BHKTypes) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.BHKTypes]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.BHKTypes]].ToString();
            var offeringType = !mappedColumnsData.ContainsKey(DataMigrateColumns.OfferingType) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.OfferingType]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.OfferingType]].ToString();
            var offeringTypeInfo = BulkUploadHelper.GetOfferingTypesInfo(offeringType ?? string.Empty);
            var propertyInfo = GetPropertyType(baseProperty, subProperty, propertyTypes, bhkType, noOfBhks, beds, baths, furnishStatus, floor);
            var purpose = !mappedColumnsData.ContainsKey(DataMigrateColumns.Purpose) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.Purpose]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.Purpose]].ToString();
            var purposees = BulkUploadHelper.GetPurposeInfo(purpose ?? string.Empty);


            var campaignName = mappedColumnsData.GetStringValue(DataMigrateColumns.CampaignName, row);
            var campaignToAdd = campaigns.FirstOrDefault(i => i.Name.Trim().ToLower().Equals(campaignName?.Trim().ToLower(), StringComparison.InvariantCultureIgnoreCase));
            if (budget?.Contains('-') ?? false)
            {
                string[] budgets = budget.Split('-');
                lowerBudget = budgets.FirstOrDefault();
                upperBudget = budgets.LastOrDefault();
            }
            else
            {
                lowerBudget = budget;
                upperBudget = budget;
            }
            var lowerBudgetInfo = BudgetHelper.ConvertBuget(lowerBudget ?? string.Empty);
            var upperBudgetinfo = BudgetHelper.ConvertBuget(upperBudget ?? string.Empty);
            Domain.Entities.Prospect prospect = new()
            {
                Name = name,
                ContactNo = contactNo,
                AlternateContactNo = altContactNo,
                Email = email,
                ScheduleDate = scheduledDate,
                Notes = unMappedColumns.Any() ? string.Join(", \n", unMappedColumns.Select(column => !string.IsNullOrEmpty(row[column].ToString()) ? column + " - " + row[column] : null).Where(i => i != null)) : string.Empty,
                Properties = properties,
                Projects = projects,
                //AgencyName = agencyName,
                Agencies = agencyToAdd != null
                            ? new List<Domain.Entities.Agency>() { agencyToAdd }
                             : !string.IsNullOrEmpty(agencyName)
                             ? new List<Domain.Entities.Agency>()
                                  {
                                        new Domain.Entities.Agency()
                                        {
                                            Name = agencyName,
                                            CreatedBy = dataMigrateTracker.CreatedBy,
                                            LastModifiedBy = dataMigrateTracker.LastModifiedBy
                                        }
                                  } : null,
                AssignTo = assignToUser?.Id ?? dataMigrateTracker.CreatedBy,
                CreatedOn = (DateTime)createdon,
                LastModifiedBy = dataMigrateTracker.CreatedBy,

                Status = prospectStatus ?? leadStatuses.FirstOrDefault(i => i.Status == "new"),
                Enquiries = new List<ProspectEnquiry>
                    {
                        new ProspectEnquiry
                        {
                            IsPrimary = true,
                           Addresses = new List<Address>
                            {
                                new Address
                                {
                                City=city,
                                State=state,
                                SubLocality=location,
                                Locality = !mappedColumnsData.ContainsKey(DataMigrateColumns.Location) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.Location]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.Location]].ToString(),
                                Community = !mappedColumnsData.ContainsKey(DataMigrateColumns.Community) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.Community]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.Community]].ToString(),
                                SubCommunity = !mappedColumnsData.ContainsKey(DataMigrateColumns.SubCommunity) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.SubCommunity]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.SubCommunity]].ToString(),
                                TowerName = !mappedColumnsData.ContainsKey(DataMigrateColumns.TowerName) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.TowerName]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.TowerName]].ToString(),

                                }

                            },
                            Currency = currencycode ?? "INR",
                            EnquiryTypes=propertyInfo.IsValidInfo?enquiryInfo.EnquiryTypes : default,
                            BHKTypes = propertyInfo.IsValidInfo ? propertyInfo.BHKTypes : default,
                            BHKs = propertyInfo.IsValidInfo?propertyInfo.BHKs : default,
                            //Source = validSource ?? source,
                            PropertyType = propertyInfo.IsValidInfo ? propertyInfo.PropertyType : default,
                            SubSource = subSource,
                            UpperBudget = upperBudgetinfo.IsValidInfo ? upperBudgetinfo.Budget : default,
                            LowerBudget = lowerBudgetInfo.IsValidInfo ? lowerBudgetInfo.Budget : default,
                            Beds=propertyInfo.IsValidInfo ? propertyInfo.Beds : default,
                            Baths=propertyInfo.IsValidInfo ? propertyInfo.Baths : default,
                            Furnished=propertyInfo.IsValidInfo ? propertyInfo.Furnished : default,
                            Floors =propertyInfo.IsValidInfo ? propertyInfo.Floors :default,
                            OfferType = offeringTypeInfo.IsValidInfo ? offeringTypeInfo.OfferingType :OfferType.None,
                            Purpose = purposees.IsValidInfo ? purposees.Purpose :Purpose.None,


                        }
                    },
                ChannelPartners = channelPartenr != null
                            ? new List<Domain.Entities.ChannelPartner>() { channelPartenr }
                             : !string.IsNullOrEmpty(cpName)
                             ? new List<Domain.Entities.ChannelPartner>()
                                  {
                                        new Domain.Entities.ChannelPartner()
                                        {
                                            FirmName = cpName,
                                            CreatedBy = dataMigrateTracker.CreatedBy,
                                            LastModifiedBy = dataMigrateTracker.LastModifiedBy
                                        }
                                  } : null,
                Campaigns = campaignToAdd != null
                            ? new List<Domain.Entities.Campaign>() { campaignToAdd }
                             : !string.IsNullOrEmpty(campaignName)
                             ? new List<Domain.Entities.Campaign>()
                                  {
                                        new Domain.Entities.Campaign()
                                        {
                                            Name = campaignName,
                                            CreatedBy = dataMigrateTracker.CreatedBy,
                                            LastModifiedBy = dataMigrateTracker.LastModifiedBy
                                        }
                                  } : null,

                CountryCode = countryCode ?? globalSettings?.Countries?.FirstOrDefault()?.DefaultCallingCode ?? "+91",
                AltCountryCode = altCountryCode ?? globalSettings?.Countries?.FirstOrDefault()?.DefaultCallingCode ?? "+91",
                ReferralName = !mappedColumnsData.ContainsKey(DataMigrateColumns.ReferralName) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.ReferralName]) ? null : row[mappedColumnsData[DataMigrateColumns.ReferralName]].ToString(),
                ReferralContactNo = !mappedColumnsData.ContainsKey(DataMigrateColumns.ReferralContactNo) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.ReferralContactNo]) ? null : row[mappedColumnsData[DataMigrateColumns.ReferralContactNo]].ToString(),
                ReferralEmail = !mappedColumnsData.ContainsKey(DataMigrateColumns.ReferralEmail) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.ReferralEmail]) ? null : row[mappedColumnsData[DataMigrateColumns.ReferralEmail]].ToString(),

            };
            if (!propertyInfo.IsValidInfo)
            {
                prospect.Notes += !string.IsNullOrEmpty(propertyInfo.BasePropertyType) ? " \n" + "BaseProperty" + " - " + propertyInfo.BasePropertyType : string.Empty;
                prospect.Notes += !string.IsNullOrEmpty(propertyInfo.SubPropertyType) ? ", \n" + "SubProperty" + " - " + propertyInfo.SubPropertyType : string.Empty;
                prospect.Notes += !string.IsNullOrEmpty(propertyInfo.InvalidNoOfBHK) ? ", \n" + "NoOfBHK" + " - " + propertyInfo.InvalidNoOfBHK : string.Empty;
                prospect.Notes += !string.IsNullOrEmpty(propertyInfo.InvalidBHKType) ? ", \n" + "BHKType" + " - " + propertyInfo.InvalidBHKType : string.Empty;
            }
            if (!upperBudgetinfo.IsValidInfo)
            {
                prospect.Notes += !string.IsNullOrEmpty(upperBudgetinfo.Invalidbudget) ? ", \n" + "UpperBudget" + " - " + upperBudgetinfo.Invalidbudget : string.Empty;
            }
            if (!lowerBudgetInfo.IsValidInfo)
            {
                prospect.Notes += !string.IsNullOrEmpty(lowerBudgetInfo.Invalidbudget) ? ", \n" + "LowerBudget" + " - " + lowerBudgetInfo.Invalidbudget : string.Empty;
            }

            return prospect;
        }

        public static string GetCurrencySymbol1(this Dictionary<DataMigrateColumns, string> mappedColumnsData, DataRow row, string? currency)
        {
            if (string.IsNullOrEmpty(currency))
            {
                return currency;
            }

            bool isCurrencyCode = currency.Length == 3 && currency.All(char.IsLetter);

            if (isCurrencyCode)
            {
                string symbol;
                if (TryGetCurrencySymbol(currency, out symbol))
                {
                    return symbol;
                }
                else
                {
                    return currency;
                }
            }
            else
            {
                return null;
            }
        }
        public static List<string> GetAgencyNamesFromDataTable(
DataTable table,
Dictionary<DataMigrateColumns, string> mappedColumnsData)
        {
            var agencyNames = new HashSet<string>();

            foreach (DataRow row in table.Rows)
            {

                if (mappedColumnsData.TryGetValue(DataMigrateColumns.AgencyName, out var agencyColumn) &&
                    !string.IsNullOrEmpty(agencyColumn))
                {
                    string agencyName = row[agencyColumn].ToString();
                    if (!string.IsNullOrEmpty(agencyName))
                    {
                        agencyNames.Add(agencyName);
                    }
                }
            }

            return agencyNames.Distinct().ToList();
        }

        public static List<string> GetCpNamesFromDataTable(
  DataTable table,
  Dictionary<DataMigrateColumns, string> mappedColumnsData)
        {
            var cpNames = new HashSet<string>();

            foreach (DataRow row in table.Rows)
            {

                if (mappedColumnsData.TryGetValue(DataMigrateColumns.ChannelPartnerName, out var Cpname) &&
                    !string.IsNullOrEmpty(Cpname))
                {
                    string agencyName = row[Cpname].ToString();
                    if (!string.IsNullOrEmpty(agencyName))
                    {
                        cpNames.Add(agencyName);
                    }
                }
            }

            return cpNames.Distinct().ToList();
        }

        public static PropertyTypeInfo GetPropertyType(string basePropertyType, string subPropertyType, List<MasterPropertyType> propertyTypes, string bhkType, string? noOfBHK, string? beds = null, string? baths = null, string? furnishedstatus = null, string? floors = null)
        {
            if (basePropertyType.ToLower().Contains("residential") && !string.IsNullOrWhiteSpace(noOfBHK) && string.IsNullOrWhiteSpace(subPropertyType))
            {
                subPropertyType = "flat";
            }
            if (basePropertyType.ToLower().Contains("commercial") && string.IsNullOrWhiteSpace(subPropertyType))
            {
                subPropertyType = "Plot";
            }
            if (basePropertyType.ToLower().Contains("agricultural") && string.IsNullOrWhiteSpace(subPropertyType))
            {
                subPropertyType = "land";
            }

            MasterPropertyType propertyType = null;
            List<string> proprtyTypes = new() { "flat", "independent house", "villa", "residential", "shop", "row villa", "land", "office space", "hostel guest house", "plot", "showroom", "godown", "chambers", "farm house", "basement", "guest house", "kiosk", "complete building", "studio", "farmhouse land", "hotel space", "agricultural land", "industrial space" };
            List<BHKType> bhkTypes = new();
            List<double> listOfBHKs = new();
            List<int> listOfBeds = new();
            List<int> listOfBaths = new();
            FurnishStatus FurnishStatus = new();
            List<string> listOfFloors = new();
            //List<string> proprtyTypes = new() { "flat", "independent house", "villa", "residential" ,"commercial","agricultural","plot","shop","agricultural land"};

            if (!string.IsNullOrEmpty(bhkType))
            {
                foreach (string bhk in bhkType.Split(','))
                {
                    if (Enum.TryParse<BHKType>(bhk, true, out BHKType type))
                    {
                        bhkTypes.Add(type);
                    }
                }
            }
            if (!string.IsNullOrEmpty(noOfBHK))
            {
                foreach (string bhk in noOfBHK.Split(','))
                {
                    double bHK = CreateLeadHelper.GetNoOfBHK(bhk);
                    if (bHK != 0)
                    {
                        listOfBHKs.Add(bHK);
                    }
                }
            }
            if (!string.IsNullOrEmpty(floors))
            {
                foreach (string floor in floors.Split(','))
                {
                    if (!string.IsNullOrWhiteSpace(floor))
                    {
                        listOfFloors.Add(floor);
                    }
                }


            }
            if (!string.IsNullOrWhiteSpace(furnishedstatus))
            {
                foreach (string furnished in furnishedstatus.Split(','))
                {
                    if (Enum.TryParse<FurnishStatus>(furnished, true, out FurnishStatus type))
                    {
                        FurnishStatus = type;
                    }
                }
            }
            if (!string.IsNullOrWhiteSpace(beds))
            {
                foreach (string bed in beds.Split(','))
                {
                    int noBed = CreateLeadHelper.Get(bed);
                    listOfBeds.Add(noBed);
                }
            }
            if (!string.IsNullOrWhiteSpace(baths))
            {
                foreach (string bath in baths.Split(','))
                {
                    int noBaths = CreateLeadHelper.Get(bath);
                    if (noBaths != 0)
                    {
                        listOfBaths.Add(noBaths);
                    }
                }
            }

            if (bhkTypes.Count > 0 || listOfBHKs.Count > 0)
            {
                if (!proprtyTypes.Contains(subPropertyType.ToLower()))
                {
                    return new PropertyTypeInfo()
                    {
                        InvalidBHKType = bhkType,
                        InvalidNoOfBHK = noOfBHK,
                        IsValidInfo = false,
                        BasePropertyType = basePropertyType.ToLower(),
                        SubPropertyType = subPropertyType
                    };
                }
            }
            else
            {
                if (string.IsNullOrEmpty(basePropertyType) && string.IsNullOrEmpty(subPropertyType))
                {
                    return new PropertyTypeInfo() { IsValidInfo = false };

                }
            }

            if (!string.IsNullOrEmpty(basePropertyType) && basePropertyType?.ToLower() == "commercial")
            {
                propertyType = propertyTypes.FirstOrDefault(i => i.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573") && i.DisplayName.ToLower() == subPropertyType.ToLower() && !i.IsDeleted);
            }
            if (string.IsNullOrEmpty(basePropertyType) || basePropertyType?.ToLower() == "residential")
            {
                propertyType = propertyTypes.FirstOrDefault(i => i.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") && i.DisplayName.ToLower() == subPropertyType.ToLower() && !i.IsDeleted);
            }
            else
            {
                propertyType = propertyTypes.FirstOrDefault(i => i.DisplayName.ToLower() == subPropertyType.ToLower());
            }
            return new PropertyTypeInfo()
            {
                PropertyType = propertyType,
                BHKTypes = bhkTypes,
                BHKs = listOfBHKs,
                Beds = listOfBeds,
                Baths = listOfBaths,
                Furnished = FurnishStatus,
                Floors = listOfFloors,
                IsValidInfo = true

            };
        }
        public static double GetNumber(string number)
        {
            try
            {
                Regex regex = new Regex(@"^\d+");
                Match match = regex.Match(number);
                double integer = 0;

                if (match.Success)
                {
                    integer = int.Parse(match.Value);
                }
                return integer;
            }
            catch (Exception e)
            {
                throw;
            }

        }

        /*   public static DateTime? GetCtearedDate(this Dictionary<DataMigrateColumns, string> mappedColumnsData, DataMigrateColumns column, DataRow row)
           {
               if (column == DataMigrateColumns.CreatedDate)
               {
                   string dateString = row[mappedColumnsData[column]].ToString();
                   DateTime parsedDate;
                   string[] formats = { "MM/dd/yyyy hh:mm tt", "dd/MM/yyyy hh:mm tt", "yyyy-MM-dd hh:mm tt", "MM-dd-yyyy hh:mm tt", "dd-MM-yyyy hh:mm tt" };
                   if (DateTime.TryParseExact(dateString, formats, CultureInfo.InvariantCulture, DateTimeStyles.None, out parsedDate))
                   {
                       return parsedDate;
                   }
                   else
                   {
                       return DateTime.UtcNow;
                   }
               }
               return null;
           }

   */



        public static string GetCurrencySymbol1DataMigrate(this Dictionary<DataMigrateColumns, string> mappedColumnsData, DataRow row, string? currency, string? defaultcurrency)
        {
            if (string.IsNullOrWhiteSpace(currency))
            {
                return defaultcurrency;
            }
            currency = currency.Replace(" ", "").ToUpper();
            var currencies = ISO3166.Country.List
           .SelectMany(country => new Nager.Country.CountryProvider().GetCountries()
           .Where(c => c.CommonName.Equals(country.Name, StringComparison.OrdinalIgnoreCase))
           .SelectMany(c => c.Currencies)
           .Select(currency => currency.IsoCode))
           .ToList();
            bool isCurrencyCode = !currencies.Contains(currency);

            if (isCurrencyCode)
            {
                string symbol;
                if (TryGetCurrencySymbolDataMigrate(currency, out symbol))
                {
                    return symbol;
                }
                else
                {
                    return defaultcurrency;
                }
            }
            if (GetValidCurrencySymbols().Contains(currency))
            {
                return currency;
            }

            else
            {
                return defaultcurrency;
            }
        }
        public static bool TryGetCurrencySymbolDataMigrate(string currencySymbol, out string isoCurrencyCode)
        {
            isoCurrencyCode = CultureInfo
        .GetCultures(CultureTypes.AllCultures)
        .Where(c => !c.IsNeutralCulture)
        .Select(culture =>
        {
            try
            {
                return new RegionInfo(culture.Name);
            }
            catch
            {
                return null;
            }
        })
        .Where(ri => ri != null && ri.CurrencySymbol.Equals(currencySymbol, StringComparison.OrdinalIgnoreCase))
        .Select(ri => ri.ISOCurrencySymbol)
        .FirstOrDefault();

            return isoCurrencyCode != null;
        }




        public static List<string> GetValidCurrencySymbols()
        {
            var symbols = new List<string>();
            var countries = ISO3166.Country.List;

            var currencyProvider = new Nager.Country.CountryProvider();

            foreach (var country in countries)
            {
                var matchingCountry = currencyProvider.GetCountries()
                    .FirstOrDefault(c => c.CommonName.Equals(country.Name, StringComparison.OrdinalIgnoreCase));

                if (matchingCountry != null)
                {
                    foreach (var currency in matchingCountry.Currencies)
                    {
                        var symbol = currency.IsoCode;
                        symbols.Add(symbol);
                    }
                }
            }

            return symbols;

        }

        public static DateTime? GetScheduledDate(this Dictionary<DataMigrateColumns, string> mappedColumnsData, DataMigrateColumns column, DataRow row,string jsonData=null)
        {
            try
            {
                // Get the date string from the mapped columns or row
                var dateString = GetStringValue(mappedColumnsData, column, row);

                // If the date string is empty or null, handle default cases
                if (string.IsNullOrEmpty(dateString))
                {
                    return column == DataMigrateColumns.ScheduledDate ? (DateTime?)null : DateTime.UtcNow;
                }

                // Try to parse the date string
                DateTime? date = null;
                CommonTimeZoneDto? commonTimeZoneDto = null;

                // Deserialize JSON data if provided
                if (!string.IsNullOrWhiteSpace(jsonData))
                {
                    commonTimeZoneDto = JsonConvert.DeserializeObject<CommonTimeZoneDto>(jsonData);
                }

                // Handle Excel date format (OLE Automation date)
                if (double.TryParse(dateString, out var excelDateValue))
                {
                    date = DateTime.FromOADate(excelDateValue);
                }
                // Handle regular date strings
                else
                {
                    // Try parsing with the default format
                    if (!DateTime.TryParse(dateString, out var parsedDate))
                    {
                        // Fallback to a specific format if the default parsing fails
                        date = DateTime.ParseExact(dateString, formats, CultureInfo.InvariantCulture);
                    }
                    else
                    {
                        string formattedDate = parsedDate.ToString(CultureInfo.CurrentCulture);
                        date = DateTime.Parse(formattedDate, CultureInfo.CurrentCulture);
                    }
                }

                // Convert the date to the specified time zone if JSON data is provided
                if (commonTimeZoneDto != null)
                {
                    date = date?.ConvertDateTime(commonTimeZoneDto.TimeZoneId ?? string.Empty, commonTimeZoneDto.BaseUTcOffset);
                }

                return date;
            }
            catch (Exception ex)
            {
                // Return UTC time as a fallback in case of any errors
                return null;
            }

            }
        public static DateTime? GetCreatedDate(this Dictionary<DataMigrateColumns, string> mappedColumnsData, DataMigrateColumns column, DataRow row, string jsonData = null)
        {
            try
            {
                // Get the date string from the mapped columns or row
                var dateString = GetStringValue(mappedColumnsData, column, row);

                // If the date string is empty or null, handle default cases
                if (string.IsNullOrEmpty(dateString))
                {
                    return column == DataMigrateColumns.CreatedDate ? DateTime.UtcNow : (DateTime?)null;
                }

                // Try to parse the date string
                DateTime? date = null;
                CommonTimeZoneDto? commonTimeZoneDto = null;

                // Deserialize JSON data if provided
                if (!string.IsNullOrWhiteSpace(jsonData))
                {
                    commonTimeZoneDto = JsonConvert.DeserializeObject<CommonTimeZoneDto>(jsonData);
                }

                // Handle Excel date format (OLE Automation date)
                if (double.TryParse(dateString, out var excelDateValue))
                {
                    date = DateTime.FromOADate(excelDateValue);
                }
                // Handle regular date strings
                else
                {
                    // Try parsing with the default format
                    if (!DateTime.TryParse(dateString, out var parsedDate))
                    {
                        // Fallback to a specific format if the default parsing fails
                        date = DateTime.ParseExact(dateString, formats, CultureInfo.InvariantCulture);
                    }
                    else
                    {
                        string formattedDate = parsedDate.ToString(CultureInfo.CurrentCulture);
                        date = DateTime.Parse(formattedDate, CultureInfo.CurrentCulture);
                    }
                }

                // Convert the date to the specified time zone if JSON data is provided
                if (commonTimeZoneDto != null)
                {
                    date = date?.ConvertDateTime(commonTimeZoneDto.TimeZoneId ?? string.Empty, commonTimeZoneDto.BaseUTcOffset);
                }

                return date;
            }
            catch (Exception ex)
            {
                // Return UTC time as a fallback in case of any errors
                return DateTime.UtcNow;
            }
        }
        public static bool TryGetCurrencySymbol(string ISOCurrencySymbol, out string symbol)
        {
            symbol = CultureInfo
                .GetCultures(CultureTypes.AllCultures)
                .Where(c => !c.IsNeutralCulture)
                .Select(culture =>
                {
                    try
                    {
                        return new RegionInfo(culture.Name);
                    }
                    catch
                    {
                        return null;
                    }
                })
                .Where(ri => ri != null && ri.ISOCurrencySymbol == ISOCurrencySymbol)
                .Select(ri => ri.CurrencySymbol)
                .FirstOrDefault();
            return symbol != null;
        }


        /* private static MasterProspectSource? GetValidDataSource(string source, List<MasterProspectSource> sources)
         {

             var validSource = source.ToLower().Trim();
             MasterProspectSource masterSource = null;
             if (string.IsNullOrEmpty(validSource))
             {
                 return null;
             }
             return new MasterProspectSource
             {
                 DisplayName = "99 Acres"
             };
             if (sources?.Any() ?? false)
             {
                 masterSource = (sources?.Where(i => i.DisplayName.ToLower().Trim() == validSource))?.FirstOrDefault() ?? null;
             }
             return masterSource;
         }*/
        public async static Task<MasterProspectSource>? GetValidDataSource(string source, List<MasterProspectSource> sources, IRepositoryWithEvents<Lrb.Domain.Entities.Source> _sourceRepo)
        {
            if (string.IsNullOrEmpty(source))
            {
                return sources.Where(i => i.IsDefault).FirstOrDefault(); ;
            }
             var source1 = source.Replace(" ", "").ToLower().Trim();
            var validSource = source1.Replace(" ", "");
            if (validSource == "99 acres" || validSource == "99acres" || validSource == "99" || validSource == "ninetynineacre")
            {
                validSource = "ninetynineacres";
            }

            MasterProspectSource? masterSource = null;

            if (sources?.Any() ?? false)
            {
                masterSource = sources
                    .FirstOrDefault(i => i.DisplayName.Replace(" ", "").ToLower().Trim() == validSource);

                if (masterSource != null)
                {
                    return await IsSourceEnabled(masterSource.Value, _sourceRepo) ? masterSource : null;
                }
            }

            return masterSource;
        }
        private async static Task<bool> IsSourceEnabled(int value, IRepositoryWithEvents<Lrb.Domain.Entities.Source> _sourceRepo, CancellationToken cancellationToken = default)
        {
            try
            {
                var source = await _sourceRepo.FirstOrDefaultAsync(new GetSourceByValueSpecs(value), cancellationToken);

                if (source == null)
                {
                    return false;
                }
                if (source.IsEnabled == false) return false;
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        private static string? GetStringValue(this Dictionary<DataMigrateColumns, string> mappedColumnsData, DataMigrateColumns column, DataRow row)
        {
            var data = !mappedColumnsData.ContainsKey(column)
                    || string.IsNullOrEmpty(mappedColumnsData[column])
                    ? string.Empty
                    : row[mappedColumnsData[column]]?.ToString();
            return data?.Trim() ?? null;
        }

        private static DateTime? GetDataTimeValue(this Dictionary<DataMigrateColumns, string> mappedColumnsData, DataMigrateColumns column, DataRow row)
        {
            try
            {
                var dateString = GetStringValue(mappedColumnsData, column, row);
                if (string.IsNullOrEmpty(dateString))
                {
                    if (column == DataMigrateColumns.CreatedDate)
                    {
                        return DateTime.UtcNow;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    DateTime? date = null;
                    try
                    {
                        if (double.TryParse(dateString, out var value))
                        {
                            date = DateTime.FromOADate(value);
                        }
                        else
                        {
                            date = Convert.ToDateTime(dateString, CultureInfo.GetCultureInfo("hi-IN"));
                        }
                        if (date.HasValue)
                        {
                            return date.Value.ToUniversalTime();
                        }
                    }
                    catch
                    {
                        return date;
                    }
                    return date;
                }
            }
            catch
            {
                return DateTime.UtcNow;
            }

        }
        private static (long Min, long Max) GetBudgetValues(this Dictionary<DataMigrateColumns, string> mappedDataColumns, DataRow row)
        {
            var minBudgetString = mappedDataColumns.GetStringValue(DataMigrateColumns.LowerBudget, row);
            var maxBudgetString = mappedDataColumns.GetStringValue(DataMigrateColumns.UpperBudget, row);
            var budgetStrings = mappedDataColumns.GetStringValue(DataMigrateColumns.Budget, row)?.Split("-")?.ToList() ?? new();
            long minBudget = (long)ConvertBuget(minBudgetString ?? budgetStrings?.FirstOrDefault() ?? string.Empty);
            long maxBudget = (long)ConvertBuget(maxBudgetString ?? budgetStrings?.LastOrDefault() ?? string.Empty);
            return (minBudget, maxBudget);
        }
        private static bool TryGetPriceValue(this Dictionary<DataMigrateColumns, string> mappedDataColumns, DataMigrateColumns column, DataRow row, out double value)
        {
            var budgetString = mappedDataColumns.GetStringValue(column, row);
            if (string.IsNullOrEmpty(budgetString))
            {
                value = 0;
                return default;
            }
            else
            {
                value = double.TryParse(budgetString, out value) ? value : ConvertBuget(budgetString);
                if (value != default)
                {
                    return true;
                }
            }
            return false;
        }
        private static T? GetEnumValue<T>(this Dictionary<DataMigrateColumns, string> mappedDataColumns, DataMigrateColumns column, DataRow row) where T : struct
        {
            var enumString = mappedDataColumns.GetStringValue(column, row);
            if (Enum.TryParse(enumString, true, out T value))
            {
                return value;
            }
            else
            {
                return null;
            }
        }
        private static double ConvertBuget(string budget)
        {
            if (double.TryParse(budget, out double result))
            {
                return result;
            }
            var lakhRegex = new Regex(@"\d+\s*(lak|lac|lack|lacks|lakh|l)s?", RegexOptions.IgnoreCase);
            var lakhMatch = lakhRegex.Match(budget);

            if (lakhMatch.Success)
            {
                var lakhString = lakhMatch.Value.ToLower();
                var croreRegex = new Regex(@"\d+\s*(crore|crores|cr|crs|c)", RegexOptions.IgnoreCase);
                var croreMatch = croreRegex.Match(budget);
                var crore = 0D;

                if (croreMatch.Success)
                {
                    crore = double.Parse(croreMatch.Value.ToLower().Replace("crore", "").Replace("crores", "").Replace("cr", "").Replace("crs", "").Replace("c", "").Trim());
                }
                var thousandRegex = new Regex(@"\d+\s*(thousand|thousands|tho|thosand|k)s?", RegexOptions.IgnoreCase);
                var thousandMatch = thousandRegex.Match(budget);
                var thousand = 0D;
                if (thousandMatch.Success)
                {
                    thousand = double.Parse(thousandMatch.Value.ToLower().Replace("thousand", "").Replace("thousands", "").Replace("thousands", "").Replace("tho", "").Replace("k", "").Trim());
                }
                var lakh = double.Parse(Regex.Replace(lakhString, @"[^\d]", "")) * 100000;
                result = crore * 10000000 + lakh + thousand * 1000;
            }
            else
            {
                var thousandRegex = new Regex(@"\d+\s*(thousand|thousands|tho|thosand|k)s?", RegexOptions.IgnoreCase);
                var thousandMatch = thousandRegex.Match(budget);

                if (thousandMatch.Success)
                {
                    var thousandString = thousandMatch.Value.ToLower();
                    var croreRegex = new Regex(@"\d+\s*(crore|crores|cr|crs)", RegexOptions.IgnoreCase);
                    var croreMatch = croreRegex.Match(budget);
                    var crore = 0D;

                    if (croreMatch.Success)
                    {
                        crore = double.Parse(croreMatch.Value.ToLower().Replace("crore", "").Replace("crores", "").Replace("cr", "").Replace("crs", "").Trim());
                    }

                    var thousand = double.Parse(Regex.Replace(thousandString, @"[^\d]", "")) * 1000;
                    result = crore * 10000000 + thousand;
                }
                else
                {
                    var croreRegex = new Regex(@"\d+\s*(crore|crores|cr|crs)", RegexOptions.IgnoreCase);
                    var croreMatch = croreRegex.Match(budget);

                    if (croreMatch.Success)
                    {
                        var croreString = croreMatch.Value.ToLower();
                        var millionRegex = new Regex(@"\d+\s*(million|millions)", RegexOptions.IgnoreCase);
                        var millionMatch = millionRegex.Match(budget);
                        var million = 0D;
                        if (millionMatch.Success)
                        {
                            million = double.Parse(millionMatch.Value.ToLower().Replace("million", "").Replace("millions", "").Trim());
                        }

                        var crore = double.Parse(Regex.Replace(croreString, @"[^\d]", "")) * 10000000;
                        result = crore + million * 1000000;
                    }
                }
            }
            return result;
        }
        private static async Task<List<Domain.Entities.Property>> GetPropertiesAsync(this Dictionary<DataMigrateColumns, string> mappedDataColumns,
            DataMigrateColumns column,
            DataRow row,
            IRepositoryWithEvents<Domain.Entities.Property> propertyRepo)
        {
            var propertyString = mappedDataColumns.GetStringValue(column, row);
            var propertyNames = propertyString.Split(",")?.ToList();
            propertyNames = (propertyNames?.Any() ?? false) ? propertyNames.Where(i => !string.IsNullOrWhiteSpace(i))?.ToList() : null;
            var leadProperties = new List<Domain.Entities.Property>();
            if (propertyNames != null && propertyNames.Any())
            {
                foreach (var propertyName in propertyNames)
                {
                    var existingProperty = await propertyRepo.FirstOrDefaultAsync(new GetPropertyByTitleSpec(propertyName.Trim().ToLower()), default);
                    if (existingProperty != null)
                    {
                        leadProperties.Add(existingProperty);
                    }
                    else
                    {
                        var newProperty = new Domain.Entities.Property() { Title = propertyName };
                        newProperty = await propertyRepo.AddAsync(newProperty, default);
                        leadProperties.Add(newProperty);
                    }
                }
            }
            return leadProperties;
        }
        private static async Task<List<Lrb.Domain.Entities.Project>> GetProjectsAsync(this Dictionary<DataMigrateColumns, string> mappedDataColumns,
            DataMigrateColumns column,
            DataRow row,
            IRepositoryWithEvents<Lrb.Domain.Entities.Project> projectRepo)
        {
            var projectString = mappedDataColumns.GetStringValue(column, row);
            var projectNames = projectString.Split(",")?.ToList();
            projectNames = (projectNames?.Any() ?? false) ? projectNames.Where(i => !string.IsNullOrWhiteSpace(i))?.ToList() : null;
            var leadProjects = new List<Lrb.Domain.Entities.Project>();
            if (projectNames != null && projectNames.Any())
            {
                foreach (var projectName in projectNames)
                {
                    var existingProject = await projectRepo.FirstOrDefaultAsync(new GetNewProjectsByIdV2Spec(projectName.Trim().ToLower()), default);
                    if (existingProject != null)
                    {
                        leadProjects.Add(existingProject);
                    }
                    else
                    {
                        var newProjects = new Lrb.Domain.Entities.Project() { Name = projectName };
                        newProjects = await projectRepo.AddAsync(newProjects, default);
                        leadProjects.Add(newProjects);
                    }
                }
            }
            return leadProjects;
        }
        private static (MasterPropertyType? PropertyType, BHKType? BHKType, double? NoOfBHK) GetPropertyTypeInfo(this Dictionary<DataMigrateColumns, string> mappedDataColumns,
            DataRow row, List<MasterPropertyType> propertyTypes)
        {
            string basePropertyType = mappedDataColumns.GetStringValue(DataMigrateColumns.BasePropertyType, row);
            string subPropertyType = mappedDataColumns.GetStringValue(DataMigrateColumns.SubPropertyType, row);
            string bhkType = mappedDataColumns.GetStringValue(DataMigrateColumns.BHKTypes, row);
            string noOfBHK = mappedDataColumns.GetStringValue(DataMigrateColumns.BHKs, row);

            MasterPropertyType? propertyType = null;
            BHKType? bHKType = Enum.TryParse(bhkType, true, out BHKType value) ? value : null;
            double bHK = CreateLeadHelper.GetNoOfBHK(noOfBHK);

            if (!string.IsNullOrEmpty(subPropertyType))
            {
                if (!string.IsNullOrEmpty(basePropertyType))
                {
                    if (basePropertyType.ToLower().Contains("residential"))
                    {
                        subPropertyType = "flat";
                    }
                    if (basePropertyType.ToLower().Contains("commercial"))
                    {
                        subPropertyType = "Plot";
                    }
                    if (basePropertyType.ToLower().Contains("agricultural"))
                    {
                        subPropertyType = "land";
                    }
                }
                else
                {
                    if (bHKType != null && bHK > 0)
                    {
                        subPropertyType = "flat";
                    }
                }
                propertyType = propertyTypes.FirstOrDefault(i =>
                            i.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c")
                            && i.DisplayName != null && !string.IsNullOrEmpty(subPropertyType)
                            && i.DisplayName.ToLower() == subPropertyType.ToLower() && !i.IsDeleted);
                return (propertyType, bHKType, bHK);
            }
            else if (basePropertyType != null)
            {
                if (basePropertyType.ToLower().Contains("residential"))
                {
                    propertyType = propertyTypes.FirstOrDefault(i =>
                            i.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c")
                            && i.DisplayName != null && !string.IsNullOrEmpty(subPropertyType)
                            && i.DisplayName.ToLower() == subPropertyType.ToLower() && !i.IsDeleted);
                }
                if (basePropertyType.ToLower().Contains("commercial"))
                {
                    propertyType = propertyTypes.FirstOrDefault(i =>
                            i.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573")
                            && i.DisplayName != null && !string.IsNullOrEmpty(subPropertyType)
                            && i.DisplayName.ToLower() == subPropertyType.ToLower() && !i.IsDeleted);
                }
                if (basePropertyType.ToLower().Contains("agricultural"))
                {
                    propertyType = propertyTypes.FirstOrDefault(i => i.DisplayName != null && i.DisplayName.ToLower() == subPropertyType.ToLower() && !i.IsDeleted);
                }
            }
            propertyType ??= propertyTypes.FirstOrDefault(i => i.DisplayName != null && i.DisplayName.ToLower() == subPropertyType.ToLower() && !i.IsDeleted);
            return (propertyType, bHKType, bHK);
        }

        private static CustomProspectStatus? GetLeadStatus(this Dictionary<DataMigrateColumns, string> mappedDataColumns,
            DataRow row, List<CustomProspectStatus> leadStatuses)
        {
            CustomProspectStatus? leadStatus = null;
            var baseStatusString = mappedDataColumns.GetStringValue(DataMigrateColumns.BaseStatus, row);
            var subStatusString = mappedDataColumns.GetStringValue(DataMigrateColumns.SubStatus, row);
            if (!string.IsNullOrWhiteSpace(subStatusString))
            {
                if (!string.IsNullOrWhiteSpace(baseStatusString))
                {
                    var subStatuses = leadStatuses.Where(i => !i.IsDeleted && ((i.DisplayName != null && i.DisplayName.ToLower().Replace(" ", "") == subStatusString.ToLower().Replace(" ", "")) || i.Status == subStatusString.Replace(" ", "_").ToLower())).ToList();
                    var baseStatus = leadStatuses.FirstOrDefault(i => !i.IsDeleted && ((i.DisplayName != null && i.DisplayName.ToLower().Replace(" ", "") == baseStatusString.ToLower().Replace(" ", "")) || i.Status == baseStatusString.Replace(" ", "_").ToLower()));
                    if (baseStatus != null)
                    {
                        leadStatus = subStatuses.FirstOrDefault(i => i.BaseId == baseStatus.Id);
                    }
                }
                leadStatus ??= leadStatuses.FirstOrDefault(i => !i.IsDeleted && ((i.DisplayName != null && i.DisplayName.ToLower().Replace(" ", "") == subStatusString.ToLower().Replace(" ", "")) || i.Status == subStatusString.Replace(" ", "_").ToLower()));
            }
            else
            {
                if (!string.IsNullOrWhiteSpace(baseStatusString))
                {
                    leadStatus = leadStatuses.FirstOrDefault(i => !i.IsDeleted && ((i.DisplayName != null && i.DisplayName.ToLower().Replace(" ", "") == baseStatusString.ToLower().Replace(" ", "")) || i.Status == baseStatusString.Replace(" ", "_").ToLower()));
                }
            }
            return leadStatus != null && leadStatus.IsValidStatus(leadStatuses) ? leadStatus : null;
        }
        private static bool IsValidStatus(this CustomProspectStatus leadStatus, List<CustomProspectStatus> leadStatuses)
        {
            if (leadStatus == null)
            {
                return false;
            }
            else
            {
                if (leadStatus.BaseId == default || leadStatus.BaseId == null)
                {
                    return !leadStatuses.Any(i => i.BaseId == leadStatus.Id);
                }
                else
                {
                    return leadStatuses.Any(i => i.Id == leadStatus.BaseId);
                }
            }
        }

        private static string GetAdditionalDataAsNote(this Dictionary<DataMigrateColumns, string> mappedDataColumns,
            DataRow row,
            List<string> unMappedColumns,

            BHKType? bHKType,
            double? noOfBHK,
            LeadSource? source,
            EnquiryType? enquiredFor,
            long minBudget,
            long maxBudget,
            CustomProspectStatus? leadStatus)
        {
            var notes = unMappedColumns.Any()
                ? string.Join(", \n", unMappedColumns.Select(column => !string.IsNullOrEmpty(row[column].ToString())
                                                                       ? column + " - " + row[column]
                                                                       : null).Where(i => i != null))
                : string.Empty;

            /*  if (propertyType == null)
              {
                  string basePropertyType = mappedDataColumns.GetStringValue(DataMigrateColumns.BasePropertyType, row);
                  string subPropertyType = mappedDataColumns.GetStringValue(DataMigrateColumns.SubPropertyType, row);
                  notes += !string.IsNullOrWhiteSpace(basePropertyType) ? $"\n{mappedDataColumns[DataMigrateColumns.BasePropertyType]} - {basePropertyType}" : string.Empty;
                  notes += !string.IsNullOrWhiteSpace(subPropertyType) ? $"\n{mappedDataColumns[DataMigrateColumns.SubPropertyType]} - {subPropertyType}" : string.Empty;
              }*/
            if (bHKType == null || bHKType == (BHKType)default)
            {
                string bhkTypeString = mappedDataColumns.GetStringValue(DataMigrateColumns.BHKTypes, row);
                notes += !string.IsNullOrWhiteSpace(bhkTypeString) ? $"\n{mappedDataColumns[DataMigrateColumns.BHKTypes]} - {bhkTypeString}" : string.Empty;
            }
            if (noOfBHK == null || noOfBHK <= 0)
            {
                string noOfBHKString = mappedDataColumns.GetStringValue(DataMigrateColumns.BHKs, row);
                notes += !string.IsNullOrWhiteSpace(noOfBHKString) ? $"\n{mappedDataColumns[DataMigrateColumns.BHKs]} - {noOfBHKString}" : string.Empty;
            }
            if ((minBudget < 0 && maxBudget < 0) || (minBudget > maxBudget))
            {
                var budgetString = mappedDataColumns.GetStringValue(DataMigrateColumns.Budget, row);
                string? minBudgetString = mappedDataColumns.GetStringValue(DataMigrateColumns.LowerBudget, row);
                string? maxBudgetString = mappedDataColumns.GetStringValue(DataMigrateColumns.LowerBudget, row);
                notes += !string.IsNullOrWhiteSpace(budgetString) ? $"\n{mappedDataColumns[DataMigrateColumns.Budget]} - {budgetString}" : string.Empty;
                notes += !string.IsNullOrWhiteSpace(minBudgetString) ? $"\n{mappedDataColumns[DataMigrateColumns.LowerBudget]} - {minBudgetString}" : string.Empty;
                notes += !string.IsNullOrWhiteSpace(maxBudgetString) ? $"\n{mappedDataColumns[DataMigrateColumns.UpperBudget]} - {maxBudgetString}" : string.Empty;
            }
            if (source == null)
            {
                var sourceString = mappedDataColumns.GetStringValue(DataMigrateColumns.Source, row);
                notes += !string.IsNullOrWhiteSpace(sourceString) ? $"\n{mappedDataColumns[DataMigrateColumns.Source]} - {sourceString}" : string.Empty;
            }
            if (enquiredFor == null)
            {
                var enquiredForString = mappedDataColumns.GetStringValue(DataMigrateColumns.EnquiryTypes, row);
                notes += !string.IsNullOrWhiteSpace(enquiredForString) ? $"\n{mappedDataColumns[DataMigrateColumns.EnquiryTypes]} - {enquiredForString}" : string.Empty;
            }
            if (leadStatus == null)
            {
                var baseStatusString = mappedDataColumns.GetStringValue(DataMigrateColumns.BaseStatus, row);
                var subStatusString = mappedDataColumns.GetStringValue(DataMigrateColumns.SubStatus, row);
                notes += !string.IsNullOrWhiteSpace(baseStatusString) ? $"\n{mappedDataColumns[DataMigrateColumns.BaseStatus]} - {baseStatusString}" : string.Empty;
                notes += !string.IsNullOrWhiteSpace(subStatusString) ? $"\n{mappedDataColumns[DataMigrateColumns.SubStatus]} - {subStatusString}" : string.Empty;
            }
            notes += $"\nAdded to leadrat on GMT - {DateTime.UtcNow}";
            return notes;
        }

        public static (List<Lrb.Domain.Entities.Prospect>, List<InvalidProspect>) DataMigrateAsyncV2(
               this DataTable table,
               Dictionary<DataMigrateColumns, string>? mappedColumnsData,
               List<string> unMappedColumns,
               MasterItems masterItems,
               DataMigrateTracker? dataMigrateTracker,
               IRepositoryWithEvents<Lrb.Domain.Entities.Source> _sourceRep,
               string jsonData = null)
        {
            List<Prospect> prospects = new();
            List<InvalidProspect> invalidProspects = new();
            PhoneNumberUtil phoneUtil = PhoneNumberUtil.GetInstance();
            if (mappedColumnsData == null)
            {
                return (prospects, invalidProspects);
            }
            else
            {
                Guid? defaultUnitId = Guid.TryParse(masterItems?.GlobalSettings?.DefaultValues?.FirstOrDefault().Value, out Guid parsedGuid) ? parsedGuid : (Guid?)null;

                foreach (DataRow row in table.Rows)
                {
                    var name = mappedColumnsData.GetStringValue(DataMigrateColumns.Name, row);
                    name = string.IsNullOrWhiteSpace(name?.Trim()) ? "Unknown" : name.Trim();
                    var contactNo = mappedColumnsData.GetStringValue(DataMigrateColumns.ContactNo, row);
                    var altContactNo = mappedColumnsData.GetStringValue(DataMigrateColumns.AlternateContactNo, row);
                    var scheduledDate = mappedColumnsData.GetScheduledDate(DataMigrateColumns.ScheduledDate, row, jsonData);
                    var createdon = mappedColumnsData.GetCreatedDate(DataMigrateColumns.CreatedDate, row, jsonData);
                    var email = mappedColumnsData.GetStringValue(DataMigrateColumns.Email, row);
                    string lowerBudget = !mappedColumnsData.ContainsKey(DataMigrateColumns.LowerBudget) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.LowerBudget]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.LowerBudget]].ToString();
                    string upperBudget = !mappedColumnsData.ContainsKey(DataMigrateColumns.UpperBudget) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.UpperBudget]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.UpperBudget]].ToString();
                    var city = mappedColumnsData.GetStringValue(DataMigrateColumns.City, row);
                    var state = mappedColumnsData.GetStringValue(DataMigrateColumns.State, row);
                    var location = mappedColumnsData.GetStringValue(DataMigrateColumns.Location, row);
                    var country = mappedColumnsData.GetStringValue(DataMigrateColumns.Country, row);
                    var subCommunity = mappedColumnsData.GetStringValue(DataMigrateColumns.SubCommunity, row);
                    var towerName = mappedColumnsData.GetStringValue(DataMigrateColumns.TowerName, row);
                    var community = mappedColumnsData.GetStringValue(DataMigrateColumns.Community, row);
                    var postalCode = mappedColumnsData.GetStringValue(DataMigrateColumns.PostalCode, row);
                    var customercity = mappedColumnsData.GetStringValue(DataMigrateColumns.CustomerCity, row);
                    var customerstate = mappedColumnsData.GetStringValue(DataMigrateColumns.CustomerState, row);
                    var customerlocation = mappedColumnsData.GetStringValue(DataMigrateColumns.CustomerLocation, row);
                    var customercountry = mappedColumnsData.GetStringValue(DataMigrateColumns.CustomerCountry, row);
                    var customersubCommunity = mappedColumnsData.GetStringValue(DataMigrateColumns.CustomerSubCommunity, row);
                    var customertowerName = mappedColumnsData.GetStringValue(DataMigrateColumns.CustomerTowerName, row);
                    var customercommunity = mappedColumnsData.GetStringValue(DataMigrateColumns.CustomerCommunity, row);
                    var customerpostalCode = mappedColumnsData.GetStringValue(DataMigrateColumns.CustomerPostalCode, row);

                    string? enquiryFor = !mappedColumnsData.ContainsKey(DataMigrateColumns.EnquiryTypes) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.EnquiryTypes]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.EnquiryTypes]].ToString();
                    string? bhkType = !mappedColumnsData.ContainsKey(DataMigrateColumns.BHKTypes) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.BHKTypes]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.BHKTypes]].ToString();
                    string? noOfBhks = !mappedColumnsData.ContainsKey(DataMigrateColumns.BHKs) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.BHKs]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.BHKs]].ToString();
                    string? baseProperty = !mappedColumnsData.ContainsKey(DataMigrateColumns.BasePropertyType) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.BasePropertyType]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.BasePropertyType]].ToString();
                    string? subProperty = !mappedColumnsData.ContainsKey(DataMigrateColumns.SubPropertyType) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.SubPropertyType]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.SubPropertyType]].ToString();
                    var enquiryInfo = BulkUploadHelper.GetEnquiryForInfo(enquiryFor ?? string.Empty);
                    var projectName = !mappedColumnsData.ContainsKey(DataMigrateColumns.Project) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.Project]) ? null : row[mappedColumnsData[DataMigrateColumns.Project]].ToString();
                    var projectNames = projectName?.Split(',').Select(p => p.Trim()).Where(p => !string.IsNullOrWhiteSpace(p)).ToList();
                    var projects = masterItems.Projects?
                                    .Where(i => projectNames != null && projectNames.Any(name =>
                                        i.Name.Trim().ToLower().Equals(name.Trim().ToLower(), StringComparison.InvariantCultureIgnoreCase)))
                                    .ToList();
                    var propertyName = !mappedColumnsData.ContainsKey(DataMigrateColumns.Property) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.Property]) ? null : row[mappedColumnsData[DataMigrateColumns.Property]].ToString();
                    var propertyNames = propertyName?.Split(',').Select(p => p.Trim()).Where(p => !string.IsNullOrWhiteSpace(p)).ToList();
                    var properties = masterItems.Properties?
                                    .Where(i => propertyNames != null && propertyNames.Any(title =>
                                        i.Title.Trim().ToLower().Equals(title.Trim().ToLower(), StringComparison.InvariantCultureIgnoreCase)))
                                    .ToList();
                    var beds = !mappedColumnsData.ContainsKey(DataMigrateColumns.Beds) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.Beds]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.Beds]].ToString();
                    var baths = !mappedColumnsData.ContainsKey(DataMigrateColumns.Baths) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.Baths]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.Baths]].ToString();
                    var furnishStatus = !mappedColumnsData.ContainsKey(DataMigrateColumns.FurnishStatus) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.FurnishStatus]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.FurnishStatus]].ToString();
                    string floor = !mappedColumnsData.ContainsKey(DataMigrateColumns.PreferredFloor) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.PreferredFloor]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.PreferredFloor]].ToString();
                    var propertyInfo = BulkUploadHelper.GetPropertyType(baseProperty, subProperty, masterItems.PropetyTypes ?? new(), bhkType, noOfBhks,beds, baths, furnishStatus, floor);
                    var subSource = mappedColumnsData.GetStringValue(DataMigrateColumns.SubSource, row);
                    var agencyName = !mappedColumnsData.ContainsKey(DataMigrateColumns.AgencyName) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.AgencyName]) ? null : row[mappedColumnsData[DataMigrateColumns.AgencyName]].ToString();
                    var agencyToAdd = masterItems.Agencies?.FirstOrDefault(i => i.Name.Replace(" ", "").Trim().Equals(agencyName?.Replace(" ", "").Trim(), StringComparison.InvariantCultureIgnoreCase));
                    var cpName = !mappedColumnsData.ContainsKey(DataMigrateColumns.ChannelPartnerName) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.ChannelPartnerName]) ? null : row[mappedColumnsData[DataMigrateColumns.ChannelPartnerName]].ToString();
                    var channelPartenr = masterItems.ChannelPartners?.FirstOrDefault(i => i.FirmName.Trim().ToLower().Equals(cpName?.Trim().ToLower(), StringComparison.InvariantCultureIgnoreCase));
                    var createdDate = mappedColumnsData.GetDataTimeValue(DataMigrateColumns.CreatedDate, row);
                    string? dataSource = !mappedColumnsData.ContainsKey(DataMigrateColumns.Source) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.Source]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.Source]].ToString();
                    var validSource = GetValidDataSource(dataSource ?? string.Empty, masterItems.ProspectSources ?? new(), _sourceRep);
                    var user = mappedColumnsData.GetStringValue(DataMigrateColumns.AssignToUser, row);
                    var assignToUser = masterItems.Users?.FirstOrDefault(u => u.UserName.ToLower() == user.ToLower().Trim().Replace(" ", ""));
                    string currecncy = !mappedColumnsData.ContainsKey(DataMigrateColumns.Currency) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.Currency]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.Currency]].ToString();
                    var currencycode = mappedColumnsData.GetCurrencySymbol1DataMigrate(row, currecncy, masterItems.GlobalSettings?.Countries?.FirstOrDefault()?.DefaultCurrency);
                    string budget = !mappedColumnsData.ContainsKey(DataMigrateColumns.Budget) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.Budget]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.Budget]].ToString();
                    var prospectStatus = mappedColumnsData.GetLeadStatus(row, masterItems.ProspectStatuses ?? new()) ?? masterItems.ProspectStatuses?.FirstOrDefault(i => i.Status == "new");
                    string countryCode = !mappedColumnsData.ContainsKey(DataMigrateColumns.CountryCode) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.CountryCode]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.CountryCode]].ToString();
                    string altCountryCode = !mappedColumnsData.ContainsKey(DataMigrateColumns.AlternativeNoCountryCode) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.AlternativeNoCountryCode]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.AlternativeNoCountryCode]].ToString();
                    var offeringType = !mappedColumnsData.ContainsKey(DataMigrateColumns.OfferingType) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.OfferingType]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.OfferingType]].ToString();
                    var offeringTypeInfo = BulkUploadHelper.GetOfferingTypesInfo(offeringType ?? string.Empty);
                    string? carpetArea = !mappedColumnsData.ContainsKey(DataMigrateColumns.CarpetArea) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.CarpetArea]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.CarpetArea]].ToString();
                    string? carpetAreaUnit = !mappedColumnsData.ContainsKey(DataMigrateColumns.CarpetAreaUnit) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.CarpetAreaUnit]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.CarpetAreaUnit]].ToString();
                    var carpetarea = GetUnitDetails(carpetArea ?? string.Empty, masterItems.AreaUnits, carpetAreaUnit ?? string.Empty, defaultUnitId);
                    string? propertyArea = !mappedColumnsData.ContainsKey(DataMigrateColumns.PropertyArea) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.PropertyArea]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.PropertyArea]].ToString();
                    string? propertyAreaUnit = !mappedColumnsData.ContainsKey(DataMigrateColumns.PropertyAreaUnit) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.PropertyAreaUnit]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.PropertyAreaUnit]].ToString();
                    var propertyArea1 = GetUnitDetails(propertyArea ?? string.Empty, masterItems.AreaUnits, propertyAreaUnit ?? string.Empty, defaultUnitId);
                    string? netArea = !mappedColumnsData.ContainsKey(DataMigrateColumns.NetArea) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.NetArea]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.NetArea]].ToString();
                    string? netAreaUnit = !mappedColumnsData.ContainsKey(DataMigrateColumns.NetAreaUnit) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.NetAreaUnit]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.NetAreaUnit]].ToString();
                    var netArea1 = GetUnitDetails(netArea ?? string.Empty, masterItems.AreaUnits, netAreaUnit ?? string.Empty, defaultUnitId);
                    string? builtUpArea = !mappedColumnsData.ContainsKey(DataMigrateColumns.BuiltUpArea) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.BuiltUpArea]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.BuiltUpArea]].ToString();
                    string? builtUpAreaUnit = !mappedColumnsData.ContainsKey(DataMigrateColumns.BuiltUpAreaUnit) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.BuiltUpAreaUnit]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.BuiltUpAreaUnit]].ToString();
                    var builtUpArea1 = GetUnitDetails(builtUpArea ?? string.Empty, masterItems.AreaUnits, builtUpAreaUnit ?? string.Empty, defaultUnitId);
                    string? SaleableArea = !mappedColumnsData.ContainsKey(DataMigrateColumns.SaleableArea) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.SaleableArea]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.SaleableArea]].ToString();
                    string? SaleableAreaUnit = !mappedColumnsData.ContainsKey(DataMigrateColumns.SaleableAreaUnit) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.SaleableAreaUnit]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.SaleableAreaUnit]].ToString();
                    var SaleableArea1 = GetUnitDetails(SaleableArea ?? string.Empty, masterItems.AreaUnits, SaleableAreaUnit ?? string.Empty);
                    var purpose = !mappedColumnsData.ContainsKey(DataMigrateColumns.Purpose) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.Purpose]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.Purpose]].ToString();
                    var purposees = BulkUploadHelper.GetPurposeInfo(purpose ?? string.Empty);
                    var possesionType = !mappedColumnsData.ContainsKey(DataMigrateColumns.PossesionType) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.PossesionType]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.PossesionType]].ToString();
                    
                    if (lowerBudget?.Contains('-') ?? false)
                    {
                        string[] lowerbudgets = lowerBudget.Split('-');
                        lowerBudget = lowerbudgets.FirstOrDefault();
                    }
                    if (upperBudget?.Contains('-') ?? false)
                    {
                        string[] upperbudgets = upperBudget.Split('-');
                        upperBudget = upperbudgets.LastOrDefault();
                    }
                    var lowerBudgetInfo = BudgetHelper.ConvertBuget(lowerBudget ?? string.Empty);
                    var upperBudgetinfo = BudgetHelper.ConvertBuget(upperBudget ?? string.Empty);
                    var campaignName = !mappedColumnsData.ContainsKey(DataMigrateColumns.CampaignName) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.CampaignName]) ? null : row[mappedColumnsData[DataMigrateColumns.CampaignName]].ToString();
                    var campaign = masterItems.Campaigns?.FirstOrDefault(i => i.Name.Trim().ToLower().Equals(campaignName?.Trim().ToLower(), StringComparison.InvariantCultureIgnoreCase));
                    var sourcinguser = mappedColumnsData.GetStringValue(DataMigrateColumns.SourcingManager, row);
                    var sourcinguserName = masterItems.Users?.FirstOrDefault(u => u.UserName.ToLower() == sourcinguser.ToLower().Trim().Replace(" ", "") && !u.IsDeleted);
                    var ClosingUser = mappedColumnsData.GetStringValue(DataMigrateColumns.ClosingManager, row);
                    var ClosingUserName = masterItems.Users?.FirstOrDefault(u => u.UserName.ToLower() == ClosingUser.ToLower().Trim().Replace(" ", "") && !u.IsDeleted);
                    var profession = !mappedColumnsData.ContainsKey(DataMigrateColumns.Profession) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.Profession]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.Profession]].ToString();
                    var validProfession = BulkUploadHelper.GetProfessionInfo(profession ?? string.Empty);
                    var possessionDate = mappedColumnsData.GetPossessionDate(DataMigrateColumns.PossessionDate, row, jsonData);
                    bool isValidPossessionDate = possessionDate.HasValue;
                    DateTime? dateTime = possessionDate;
                    if (possesionType != "None" && possesionType != "CustomDate")
                    {
                        DateTime currentUtcTime = DateTime.UtcNow;
                        possessionDate = currentUtcTime;
                        isValidPossessionDate = true;

                        switch (possesionType)
                        {
                            case "UnderConstruction":
                                dateTime = null;
                                possesionType = PossesionType.UnderConstruction.ToString(); ;

                                break;
                            case "Next 3 Months":
                                dateTime = currentUtcTime.AddMonths(3);
                                possesionType = PossesionType.ThreeMonth.ToString(); ;

                                break;

                            case "3–6 Months":
                                dateTime = currentUtcTime.AddMonths(6);
                                possesionType = PossesionType.SixMonth.ToString(); ;

                                break;

                            case "6–12 Months":
                                dateTime = currentUtcTime.AddYears(1);
                                possesionType = PossesionType.Year.ToString(); ;

                                break;

                            case "1-2 year":
                                dateTime = currentUtcTime.AddYears(2);
                                possesionType = PossesionType.TwoYears.ToString(); ;

                                break;

                            case "Immediate Possession (within 30 days)":
                                dateTime = currentUtcTime.AddMonths(1);
                                possesionType = PossesionType.Immediate.ToString(); ;
                                break;
                        }
                    }
                    if (dateTime.HasValue && (possesionType == null || possesionType == "None" || string.IsNullOrWhiteSpace(possesionType)))
                    {
                        possesionType = PossesionType.CustomDate.ToString();
                    }
                    string? landline = !mappedColumnsData.ContainsKey(DataMigrateColumns.LandLine) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.LandLine]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.LandLine]].ToString();

                    var gender = !mappedColumnsData.ContainsKey(DataMigrateColumns.Gender) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.Gender]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.Gender]].ToString();
                    var validGenderType = BulkUploadHelper.GetGenderInfo(gender ?? string.Empty);
                    var maritalStatus = !mappedColumnsData.ContainsKey(DataMigrateColumns.MaritalStatus) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.MaritalStatus]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.MaritalStatus]].ToString();
                    var validMaritalStatusType = BulkUploadHelper.GetMaritalStatusInfo(maritalStatus ?? string.Empty);
                    var dateOdBirth = !mappedColumnsData.ContainsKey(DataMigrateColumns.DateOfBirth) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.DateOfBirth]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.DateOfBirth]].ToString();
                    var validDateOfBirth = mappedColumnsData.GetDate(DataMigrateColumns.DateOfBirth, row, jsonData);
                    var anniversaryDate = !mappedColumnsData.ContainsKey(DataMigrateColumns.AnniversaryDate) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.AnniversaryDate]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.AnniversaryDate]].ToString();
                    var validanniversaryDate = mappedColumnsData.GetDate(DataMigrateColumns.AnniversaryDate, row, jsonData);
                    Domain.Entities.Prospect prospect = new()
                    {
                        Id = Guid.NewGuid(),
                        Name = name,
                        ContactNo = contactNo,
                        AlternateContactNo = altContactNo,
                        Email = email,
                        ScheduleDate = prospectStatus.IsScheduled ? scheduledDate : null,
                        Notes = unMappedColumns.Any() ? string.Join(", \n", unMappedColumns.Select(column => !string.IsNullOrEmpty(row[column].ToString()) ? column + " - " + row[column] : null).Where(i => i != null)) : string.Empty,
                        Properties = properties,
                        Projects = projects,
                        AssignTo = assignToUser?.Id ?? Guid.Empty,
                        CreatedOn = (DateTime)createdon,
                        CreatedBy = dataMigrateTracker.CreatedBy,
                        LastModifiedBy = dataMigrateTracker.CreatedBy,
                        Status = prospectStatus,
                        Enquiries = new List<ProspectEnquiry>
                        {
                            new ProspectEnquiry
                            {
                                Id= Guid.NewGuid(),
                                IsPrimary = true,
                                Addresses = (!string.IsNullOrEmpty(city) || !string.IsNullOrEmpty(state) || !string.IsNullOrEmpty(location)||!string.IsNullOrEmpty(country)||!string.IsNullOrEmpty(subCommunity)
                                ||!string.IsNullOrEmpty(towerName)||!string.IsNullOrEmpty(community) ||!string.IsNullOrEmpty(postalCode))
                                ? new()
                                {
                                    new()
                                    {
                                        Id = Guid.NewGuid(),
                                        City = city,
                                        State = state,
                                        SubLocality = location,
                                        Country=country,
                                        SubCommunity=subCommunity,
                                        TowerName=towerName,
                                        Community=community,
                                        PostalCode=postalCode,

                                    }
                                }
                                : null,
                                Currency = currencycode ?? "INR",
                                EnquiryTypes=enquiryInfo.IsValidInfo ? enquiryInfo.EnquiryTypes : default,
                                BHKTypes = propertyInfo.IsValidInfo ? propertyInfo.BHKTypes : default,
                                BHKs = propertyInfo.IsValidInfo?propertyInfo.BHKs : default,
                                PropertyType = propertyInfo.IsValidInfo ? propertyInfo.PropertyType : default,
                                UpperBudget = upperBudgetinfo.IsValidInfo ? upperBudgetinfo.Budget : default,
                                LowerBudget = lowerBudgetInfo.IsValidInfo ? lowerBudgetInfo.Budget : default,
                                Beds=propertyInfo.IsValidInfo ? propertyInfo.Beds : default,
                                Baths=propertyInfo.IsValidInfo ? propertyInfo.Baths : default,
                                Furnished=propertyInfo.IsValidInfo ? propertyInfo.Furnished : null,
                                Floors =propertyInfo.IsValidInfo ? propertyInfo.Floors :default,
                                OfferType = offeringTypeInfo.IsValidInfo ? offeringTypeInfo.OfferingType :OfferType.None,
                                CarpetArea =  carpetarea.Item1,
                                CarpetAreaUnitId = carpetarea.Item2,
                                PropertyArea =  propertyArea1.Item1,
                                PropertyAreaUnitId = propertyArea1.Item2,
                                NetArea =  netArea1.Item1,
                                NetAreaUnitId = netArea1.Item2,
                                BuiltUpArea =  builtUpArea1.Item1,
                                BuiltUpAreaUnitId = builtUpArea1.Item2,
                                SaleableArea =  SaleableArea1.Item1,
                                SaleableAreaUnitId = SaleableArea1.Item2,
                                UnitName = !mappedColumnsData.ContainsKey(DataMigrateColumns.UnitName) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.UnitName]) ? null : row[mappedColumnsData[DataMigrateColumns.UnitName]].ToString(),
                                ClusterName = !mappedColumnsData.ContainsKey(DataMigrateColumns.ClusterName) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.ClusterName]) ? null : row[mappedColumnsData[DataMigrateColumns.ClusterName]].ToString(),
                                PropertyTypes = propertyInfo.IsValidInfo ? propertyInfo.PropertyTypes : default,
                                Purpose = purposees.IsValidInfo ? purposees.Purpose :Purpose.None,
                                PossesionType = Enum.TryParse(possesionType, true, out PossesionType parsedType) && (parsedType != PossesionType.CustomDate || isValidPossessionDate) ? parsedType : default,
                            }
                        },
                        Agencies = agencyToAdd != null ? new List<Domain.Entities.Agency>() { agencyToAdd } : null,
                        ChannelPartners = channelPartenr != null ? new List<Domain.Entities.ChannelPartner>() { channelPartenr } : null,
                        CountryCode = countryCode ?? masterItems.GlobalSettings?.Countries?.FirstOrDefault()?.DefaultCallingCode ?? "+91",
                        AltCountryCode = altCountryCode ?? masterItems.GlobalSettings?.Countries?.FirstOrDefault()?.DefaultCallingCode ?? "+91",
                        Designation = !mappedColumnsData.ContainsKey(DataMigrateColumns.Designation) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.Designation]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.Designation]].ToString(),
                        ExecutiveName = !mappedColumnsData.ContainsKey(DataMigrateColumns.ChannelPartnerExecutiveName) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.ChannelPartnerExecutiveName]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.ChannelPartnerExecutiveName]].ToString(),
                        ChannelPartnerName = !mappedColumnsData.ContainsKey(DataMigrateColumns.ChannelPartnerName) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.ChannelPartnerName]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.ChannelPartnerName]].ToString(),
                        ExecutiveContactNo = !mappedColumnsData.ContainsKey(DataMigrateColumns.ChannelPartnerContactNo) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.ChannelPartnerContactNo]) ? null : row[mappedColumnsData[DataMigrateColumns.ChannelPartnerContactNo]].ToString().Trim(),
                        ReferralName = !mappedColumnsData.ContainsKey(DataMigrateColumns.ReferralName) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.ReferralName]) ? null : row[mappedColumnsData[DataMigrateColumns.ReferralName]].ToString(),
                        ReferralContactNo = !mappedColumnsData.ContainsKey(DataMigrateColumns.ReferralContactNo) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.ReferralContactNo]) ? null : row[mappedColumnsData[DataMigrateColumns.ReferralContactNo]].ToString(),
                        ReferralEmail = !mappedColumnsData.ContainsKey(DataMigrateColumns.ReferralEmail) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.ReferralEmail]) ? null : row[mappedColumnsData[DataMigrateColumns.ReferralEmail]].ToString(),
                        Nationality = !mappedColumnsData.ContainsKey(DataMigrateColumns.Nationality) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.Nationality]) ? null : row[mappedColumnsData[DataMigrateColumns.Nationality]].ToString(),
                        Campaigns = campaign != null ? new List<Domain.Entities.Campaign>() { campaign } : null,
                        CompanyName = !mappedColumnsData.ContainsKey(DataMigrateColumns.CompanyName) || string.IsNullOrEmpty(mappedColumnsData[DataMigrateColumns.CompanyName]) ? string.Empty : row[mappedColumnsData[DataMigrateColumns.CompanyName]].ToString(),
                        Address = (!string.IsNullOrEmpty(customercity) || !string.IsNullOrEmpty(customerstate) || !string.IsNullOrEmpty(customerlocation) || !string.IsNullOrEmpty(customercountry) || !string.IsNullOrEmpty(customersubCommunity)
                                || !string.IsNullOrEmpty(customertowerName) || !string.IsNullOrEmpty(customercommunity) || !string.IsNullOrEmpty(customerpostalCode)) ? new Address
                                {
                                    Id = Guid.NewGuid(),
                                    City = customercity,
                                    State = customerstate,
                                    SubLocality = customerlocation,
                                    Community = customercountry,
                                    SubCommunity = customersubCommunity,
                                    TowerName = customertowerName,
                                    Country = customercommunity,
                                    PostalCode = customerpostalCode,
                                } : null,
                        SourcingManager = sourcinguserName?.Id ?? Guid.Empty,
                        ClosingManager = ClosingUserName?.Id ?? Guid.Empty,
                        Profession = validProfession.IsValidInfo ? validProfession.Profession : Profession.None,
                        PossesionDate = isValidPossessionDate ? dateTime?.ToUniversalTime() : null,
                        LandLine = (!string.IsNullOrWhiteSpace(landline) && Regex.IsMatch(Regex.Replace(landline, @"[^0-9\-]", ""), @"^[0-9\-]{6,16}$")) ? Regex.Replace(landline, @"[^0-9\-]", "") : string.Empty,

                        UploadTypeName = dataMigrateTracker?.S3BucketKey,
                        Gender = validGenderType.IsValidInfo ? validGenderType.Gender : Gender.NotMentioned,
                        MaritalStatus = validMaritalStatusType.IsValidInfo ? validMaritalStatusType.MaritalStatusType : MaritalStatusType.NotMentioned,
                        DateOfBirth = validDateOfBirth ?? null,
                        AnniversaryDate = validMaritalStatusType.IsValidInfo && validMaritalStatusType.MaritalStatusType == MaritalStatusType.Married ? validanniversaryDate : null,
                    };

                    InvalidProspect invalidProspect = null;
                    bool isInvalidContact = false;
                    if (!string.IsNullOrEmpty(prospect.ContactNo))
                    {
                        var contactNum = phoneUtil.V2ConcatenatePhoneNumber(prospect.CountryCode, prospect.ContactNo, masterItems.GlobalSettings ?? new());
                        if (string.IsNullOrEmpty(contactNum))
                        {
                            invalidProspect = prospect.Adapt<InvalidProspect>();
                            invalidProspect.Errors = "Invalid ContactNo";
                            invalidProspect.AssignTo = assignToUser != null ? assignToUser.FirstName + " " + assignToUser.LastName : string.Empty;
                            invalidProspect.Source = prospect.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.Source?.DisplayName?.ToString() ?? prospect.Enquiries?.FirstOrDefault()?.Source?.DisplayName?.ToString();
                            invalidProspect.SubSource = prospect.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.SubSource ?? prospect.Enquiries?.FirstOrDefault()?.Source?.DisplayName?.ToString();
                            invalidProspect.Status = prospect.Status?.DisplayName ?? string.Empty;
                            invalidProspect.Created = prospect.CreatedOn.Date;
                            invalidProspects.Add(invalidProspect);
                            isInvalidContact = true;
                        }
                        prospect.ContactNo = contactNum;
                    }
                    else
                    {
                        invalidProspect = prospect.Adapt<InvalidProspect>();
                        invalidProspect.Errors = "Invalid ContactNo";
                        invalidProspect.AssignTo = assignToUser != null ? assignToUser.FirstName + " " + assignToUser.LastName : string.Empty;
                        invalidProspect.Source = prospect.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.Source?.DisplayName?.ToString() ?? prospect.Enquiries?.FirstOrDefault()?.Source?.DisplayName?.ToString();
                        invalidProspect.SubSource = prospect.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.SubSource ?? prospect.Enquiries?.FirstOrDefault()?.Source?.DisplayName?.ToString();
                        invalidProspect.Status = prospect.Status?.DisplayName ?? string.Empty;
                        invalidProspect.Created = prospect.CreatedOn.Date;
                        invalidProspects.Add(invalidProspect);
                        isInvalidContact = true;
                    }
                    prospect.AlternateContactNo = !string.IsNullOrEmpty(prospect.AlternateContactNo) ? phoneUtil.V2ConcatenatePhoneNumber(prospect.AltCountryCode, prospect.AlternateContactNo, masterItems.GlobalSettings ?? new()) : null;
                    prospect.ReferralContactNo = !string.IsNullOrEmpty(prospect.AlternateContactNo) ? phoneUtil.V2ConcatenatePhoneNumber(null, prospect.ReferralContactNo, masterItems.GlobalSettings ?? new()) : null;
                    prospect.ChannelPartnerContactNo = !string.IsNullOrEmpty(prospect.ChannelPartnerContactNo) ? phoneUtil.V2ConcatenatePhoneNumber(null, prospect.ChannelPartnerContactNo, masterItems.GlobalSettings ?? new()) : null;
                    if (dataMigrateTracker.MigrationType == DataMigrationType.None || dataMigrateTracker.MigrationType == DataMigrationType.CreateDuplicateData)
                    {
                        prospect.LastModifiedOn = (DateTime)createdon;
                    }
                    else
                    {
                        prospect.LastModifiedOn = DateTime.UtcNow;
                    }
                    if (validSource.Result == null && !isInvalidContact)
                    {
                        invalidProspect = prospect.Adapt<InvalidProspect>();
                        invalidProspect.Errors = "Invalid Source";
                        invalidProspect.Source = dataSource;
                        invalidProspect.SubSource = subSource;
                        invalidProspect.Status = prospect.Status?.DisplayName ?? string.Empty;
                        invalidProspect.Created = prospect.CreatedOn.Date;
                        invalidProspects.Add(invalidProspect);
                    }
                    else if (validSource?.Result != null)
                    {
                        prospect.Enquiries.FirstOrDefault().Source = validSource.Result;
                        prospect.Enquiries.FirstOrDefault().SubSource = subSource;
                    }

                    if (!propertyInfo.IsValidInfo)
                    {
                        prospect.Notes += !string.IsNullOrEmpty(propertyInfo.BasePropertyType) ? " \n" + "BaseProperty" + " - " + propertyInfo.BasePropertyType : string.Empty;
                        prospect.Notes += !string.IsNullOrEmpty(propertyInfo.SubPropertyType) ? ", \n" + "SubProperty" + " - " + propertyInfo.SubPropertyType : string.Empty;
                        prospect.Notes += !string.IsNullOrEmpty(propertyInfo.InvalidNoOfBHK) ? ", \n" + "NoOfBHK" + " - " + propertyInfo.InvalidNoOfBHK : string.Empty;
                        prospect.Notes += !string.IsNullOrEmpty(propertyInfo.InvalidBHKType) ? ", \n" + "BHKType" + " - " + propertyInfo.InvalidBHKType : string.Empty;
                    }
                    if (!upperBudgetinfo.IsValidInfo)
                    {
                        prospect.Notes += !string.IsNullOrEmpty(upperBudgetinfo.Invalidbudget) ? ", \n" + "UpperBudget" + " - " + upperBudgetinfo.Invalidbudget : string.Empty;
                    }
                    if (!lowerBudgetInfo.IsValidInfo)
                    {
                        prospect.Notes += !string.IsNullOrEmpty(lowerBudgetInfo.Invalidbudget) ? ", \n" + "LowerBudget" + " - " + lowerBudgetInfo.Invalidbudget : string.Empty;
                    }
                    if(dataMigrateTracker.MigrationType != DataMigrationType.CreateDuplicateData)
                    {
                        var existingContacts = prospects.SelectMany(i => new[] { i.ContactNo, i.AlternateContactNo })
                        .Where(i => !string.IsNullOrWhiteSpace(i)).ToList();
                        if (invalidProspect == null && (!existingContacts.Contains(prospect.ContactNo) && !existingContacts.Contains(prospect.AlternateContactNo)))
                        {
                            prospects.Add(prospect);
                        }
                        else if ((existingContacts.Contains(prospect.ContactNo) || existingContacts.Contains(prospect.AlternateContactNo)))
                        {
                            invalidProspect = prospect.Adapt<InvalidProspect>();
                            invalidProspect.Errors = "Duplicate ContactNo";
                            invalidProspect.AssignTo = assignToUser != null ? assignToUser.FirstName + " " + assignToUser.LastName : string.Empty;
                            invalidProspect.Source = prospect.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.Source?.DisplayName?.ToString() ?? prospect.Enquiries?.FirstOrDefault()?.Source?.DisplayName?.ToString();
                            invalidProspect.SubSource = prospect.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.SubSource ?? prospect.Enquiries?.FirstOrDefault()?.Source?.DisplayName?.ToString();
                            invalidProspect.Status = prospect.Status?.DisplayName ?? string.Empty;
                            invalidProspect.Created = prospect.CreatedOn.Date;
                            invalidProspects.Add(invalidProspect);
                        }
                    }
                    else
                    {
                        if (invalidProspect == null)
                        {
                            prospects.Add(prospect);
                        }
                    }
                }
            }
            return (prospects, invalidProspects);
        }
        public static double GetArea(string number)
        {
            try
            {
                Regex regex = new Regex(@"^\d+(\.\d+)?$");
                Match match = regex.Match(number);
                double res = 0;

                if (match.Success)
                {
                    res = double.Parse(match.Value);
                }
                return res;
            }
            catch (Exception e)
            {
                throw;
            }

        }
        public static (double, Guid) GetUnitDetails(string unitAreaSize, List<MasterAreaUnit> areaUnits, string unit, Guid? areaUnitId = null)
        {
            var unitArea = GetArea(unitAreaSize);
            if (areaUnits.Count > 0)
            {
                var masterUnits = areaUnits?.ConvertAll(i => i?.Unit?.Replace(" ", "")?.Replace(".", "")?.ToLower()?.Trim() ?? string.Empty)?.ToList();
                var unitOfArea = Regex.Replace(unitAreaSize, "[a-zA-Z]", string.Empty).Trim();
                if (!string.IsNullOrWhiteSpace(unitOfArea) && !string.IsNullOrWhiteSpace(unit) && (masterUnits?.Any(i => i.Contains(unit?.Replace(" ", "")?.Replace(".", "")?.ToLower()?.Trim() ?? string.Empty)) ?? false))
                {
                    var normalizedUnit = unit?.Replace(" ", "")?.Replace(".", "")?.ToLower()?.Trim() ?? string.Empty;
                    var unitId = areaUnits?.FirstOrDefault(i => (i?.Unit?.Replace(" ", "")?.Replace(".", "")?.ToLower()?.Trim() ?? string.Empty).Contains(normalizedUnit))?.Id ?? Guid.Empty;
                    return (unitArea, unitId);
                }
                else if (areaUnitId != null)
                {
                    return (unitArea, areaUnitId ?? Guid.Empty);
                }
                else if (string.IsNullOrWhiteSpace(unit))
                {
                    var unitId = areaUnits?.FirstOrDefault(i => i.Unit == "Sq. Feet")?.Id ?? Guid.Empty;
                    return (unitArea, unitId);
                }
            }
            return (unitArea, Guid.Empty);
        }
        public static string[] formats = {
            // Month-Day-Year formats
            "MM/dd/yyyy HH:mm:ss",       // 10/24/2024 14:30:00
            "MM/dd/yyyy hh:mm:ss tt",    // 10/24/2024 02:30:00 PM
            "MM-dd-yyyy HH:mm:ss",       // 10-24-2024 14:30:00
            "MM-dd-yyyy hh:mm:ss tt",    // 10-24-2024 02:30:00 PM

            // Day-Month-Year formats
            "dd-MM-yyyy HH:mm:ss",       // 24-10-2024 14:30:00
            "dd-MM-yyyy hh:mm:ss tt",    // 24-10-2024 02:30:00 PM
            "dd/MM/yyyy HH:mm:ss",       // 24/10/2024 14:30:00
            "dd/MM/yyyy hh:mm:ss tt",    // 24/10/2024 02:30:00 PM
            "dd.MM.yyyy HH:mm:ss",       // 24.10.2024 14:30:00
            "dd.MM.yyyy hh:mm:ss tt",    // 24.10.2024 02:30:00 PM

            // Year-Month-Day formats
            "yyyy/MM/dd HH:mm:ss",       // 2024/10/24 14:30:00
            "yyyy-MM-dd HH:mm:ss",       // 2024-10-24 14:30:00
            "yyyy.MM.dd HH:mm:ss",       // 2024.10.24 14:30:00
            "yyyy/MM/dd hh:mm:ss tt",    // 2024/10/24 02:30:00 PM
            "yyyy-MM-dd hh:mm:ss tt",    // 2024-10-24 02:30:00 PM
            "yyyy.MM.dd hh:mm:ss tt",    // 2024.10.24 02:30:00 PM

            // ISO 8601 formats
            "yyyy-MM-ddTHH:mm:ss.fffffffZ",  // 2024-10-24T14:30:00.0000000Z
            "yyyy-MM-ddTHH:mm:ssZ",          // 2024-10-24T14:30:00Z
            "yyyy-MM-ddTHH:mm:ss.fffZ",      // 2024-10-24T14:30:00.000Z
            "yyyy-MM-ddTHH:mm:ss",           // 2024-10-24T14:30:00
            "yyyy-MM-ddTHH:mm:ssK",          // 2024-10-24T14:30:00+00:00

            // Custom formats
            "yyyyMMddHHmmss",             // 20241024143000
            "ddMMyyyyHHmmss",             // 24102024143000
            "yyyyMMdd",                   // 20241024

            // Optional seconds
            "MM/dd/yyyy HH:mm",           // 10/24/2024 14:30
            "dd/MM/yyyy HH:mm",           // 24/10/2024 14:30
            "yyyy-MM-ddTHH:mm",           // 2024-10-24T14:30
            "yyyyMMddHHmm",               // 202410241430

            // Optional milliseconds
            "MM/dd/yyyy HH:mm:ss.fff",    // 10/24/2024 14:30:00.000
            "dd/MM/yyyy HH:mm:ss.fff",    // 24/10/2024 14:30:00.000
            "yyyy-MM-dd HH:mm:ss.fff",    // 2024-10-24 14:30:00.000
            "yyyy-MM-ddTHH:mm:ss.fff",    // 2024-10-24T14:30:00.000

            "dd/MM/yyyy hh:mm tt",   // 29/01/2025 07:00 PM
            "MM/dd/yyyy hh:mm tt",   // 01/29/2025 07:00 PM (US format)
            "dd/MM/yyyy"
        };
        
        public static DateTime? GetPossessionDate(this Dictionary<DataMigrateColumns, string> mappedColumnsData, DataMigrateColumns column, DataRow row, string jsonData = null)
        {
            try
            {
                // Get the date string from the mapped columns or row
                var dateString = GetStringValue(mappedColumnsData, column, row);

                // If the date string is empty or null, handle default cases
                if (string.IsNullOrEmpty(dateString))
                {
                    return null;
                }

                // Try to parse the date string
                DateTime? date = null;
                CommonTimeZoneDto? commonTimeZoneDto = null;

                // Deserialize JSON data if provided
                if (!string.IsNullOrWhiteSpace(jsonData))
                {
                    commonTimeZoneDto = JsonConvert.DeserializeObject<CommonTimeZoneDto>(jsonData);
                }

                // Handle Excel date format (OLE Automation date)
                if (double.TryParse(dateString, out var excelDateValue))
                {
                    date = DateTime.FromOADate(excelDateValue);
                }
                // Handle regular date strings
                else
                {
                    // Try parsing with the default format
                    if (!DateTime.TryParse(dateString, out var parsedDate))
                    {
                        // Fallback to a specific format if the default parsing fails
                        date = DateTime.ParseExact(dateString, formats, CultureInfo.InvariantCulture);
                    }
                    else
                    {
                        string formattedDate = parsedDate.ToString(CultureInfo.CurrentCulture);
                        date = DateTime.Parse(formattedDate, CultureInfo.CurrentCulture);
                    }
                }

                // Convert the date to the specified time zone if JSON data is provided
                if (commonTimeZoneDto != null)
                {
                    date = date?.ConvertDateTime(commonTimeZoneDto.TimeZoneId ?? string.Empty, commonTimeZoneDto.BaseUTcOffset);
                }

                return date;
            }
            catch (Exception ex)
            {
                // Return UTC time as a fallback in case of any errors
                return null;
            }
        }
        public static DateTime? GetDate(this Dictionary<DataMigrateColumns, string> mappedColumnsData, DataMigrateColumns column, DataRow row, string jsonData = null)
        {
            try
            {
                var dateString = GetStringValue(mappedColumnsData, column, row);
                if (string.IsNullOrEmpty(dateString))
                {
                    return null;
                }
                DateTime? date = null;
                if (double.TryParse(dateString, out var excelDateValue))
                {
                    date = DateTime.FromOADate(excelDateValue).Date;
                }
                else
                {
                    if (!DateTime.TryParseExact(dateString, LeadMigrateHelper.formats, CultureInfo.InvariantCulture, DateTimeStyles.None, out var parsedDate))
                    {
                        date = DateTime.ParseExact(dateString, LeadMigrateHelper.formats, CultureInfo.InvariantCulture).Date;
                    }
                    else
                    {
                        string formattedDate = parsedDate.ToString(CultureInfo.CurrentCulture);
                        date = DateTime.Parse(formattedDate, CultureInfo.CurrentCulture).Date;
                    }
                }

                if (date.HasValue && date.Value > DateTime.UtcNow.Date)
                {
                    return null;
                }

                return date;
            }
            catch
            {
                return null;
            }
        }
    }
}
