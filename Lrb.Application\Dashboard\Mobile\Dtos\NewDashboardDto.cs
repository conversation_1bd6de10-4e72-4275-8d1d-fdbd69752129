﻿namespace Lrb.Application.Dashboard.Mobile
{
    public class NewDashboardDto : IDto
    {
        public LeadCountByStatusDto? LeadsCountByStatus { get; set; }
        //public List<DashboardBySourceDto>? DashboardBySourceDtos { get; set; }
        public List<DashboardBySourceWithTotalCountDto>? GroupedSourceDtos { get; set; }
        public LeadsInContactWithDto? LeadsInContactWith { get; set; }
        public SiteVisitAndMeetingScheduledDto? SiteVisitAndMeetingScheduled { get; set; }
        public LeadtrackerDto? LeadTracker { get; set; }
        public List<UpcomingEventDto>? UpcomingEvents { get; set; }
        public List<LeadReportDto>? LeadReports { get; set; }
    }
    public class LeadCountByStatusDto : IDto
    {
        public int TotalLeads { get; set; }
        public int WorkableLeads { get; set; }
        public int UnassignedLeads { get; set; }
        public int DeletedLeads { get; set; }
        public int New { get; set; }
        public int Dropped { get; set; }
        public int Pending { get; set; }
        public int Callback { get; set; }
        public int Booked { get; set; }
        public int MeetingScheduled { get; set; }
        public int SiteVisitScheduled { get; set; }
        public int Overdue { get; set; }
        public int NotInterested { get; set; }
    }
    public class LeadCountByStatusForMyDashboardDto : IDto
    {
        public int All { get; set; }
        public int New { get; set; }
        public int Dropped { get; set; }
        public int Pending { get; set; }
        public int Callback { get; set; }
        public int Booked { get; set; }
        public int MeetingScheduled { get; set; }
        public int SiteVisitScheduled { get; set; }
        public int Overdue { get; set; }
        public int NotInterested { get; set; }
    }
    public class DashboardBySourceDto : IDto
    {
        public LeadSource Source { get; set; }
        public string? Status { get; set; }
        public long Count { get; set; }
    }
    public class DashboardBySourceWithTotalCountDto : IDto
    {
        public LeadSource LeadSource { get; set; }
        public long TotalCount { get; set; }
        public List<DashboardBySourceDto>? CountBySources { get; set; }
    }
    public class LeadsInContactWithDto : IDto
    {
        public int Callback { get; set; }
        public int MeetingDone { get; set; }
        public int SiteVisitDone { get; set; }
        public int SiteVisitScheduled { get; set; }
        public int MeetingScheduled { get; set; }
    }
    public class SiteVisitAndMeetingScheduledDto : IDto
    {
        public int SiteVisitDoneUniqueCount { get; set; }
        public int SiteVisitDoneEventCount { get; set; }
        public int SiteVisitNotDoneUniqueCount { get; set; }
        public int SiteVisitNotDoneEventCount { get; set; }
        public int SiteVisitOverdue { get; set; }

        public int MeetingDoneUniqueCount { get; set; }
        public int MeetingDoneEventCount { get; set; }
        public int MeetingNotDoneUniqueCount { get; set; }
        public int MeetingNotDoneEventCount { get; set; }
       
        public int MeetingOverdue { get; set; }


        public int UpcomingMeetings{ get; set; }
        public int UpcomingSiteVisits{ get; set; }

    }
    public class LeadtrackerDto : IDto
    {
        public int SMS { get; set; }
        public int Call { get; set; }
        public int Email { get; set; }
        public int WhatsApp { get; set; }
        public int Total { get; set; }
    }

    public class NewLeadtrackerDto : IDto
    {
        public int WhatsAppLead { get; set; }
        public int CallLead { get; set; }
        public int EmailLead { get; set; }
        public int SMSLead { get; set; }
        public int TotalEvent { get; set; }
        public int WhatsAppEvent { get; set; }
        public int CallEvent { get; set; }
        public int EmailEvent { get; set; }
        public int SMSEvent { get; set; }
    }

    public class UpcomingEventDto : IDto
    {



        public Guid Id { get; set; }
        public string Name { get; set; }
        public Guid? StatusId { get; set; }
        public DateTime? ScheduledDate { get; set; }
        public Guid? AssignTo { get; set; }
        public Guid? AssignedFrom { get; set; }
        public string ContactNo { get; set; }
        public string? AlternateContactNo { get; set; }
        public string? Email { get; set; }

        /*public Guid UserId { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? PhoneNumber { get; set; }
        public Guid LeadId { get; set; }
        public string? LeadName { get; set; }
        public string? LeadContactNo { get; set; }
        public string? Status { get; set; }
        public string? SubStatus { get; set; }
        public DateTime ScheduledDate { get; set; }*/

    }

    public class LeadReportDto : IDto
    {
        public Guid UserId { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public int DroppedCount { get; set; }
        public int NotInterestedCount { get; set; }
        public int BookedCount { get; set; }
        public int PendingCount { get; set; }
        public int SiteVisitScheduledCount { get; set; }
        public int CallbackCount { get; set; }
        public int MeetingScheduledCount { get; set; }
        public int NewCount { get; set; }
        public int TotalCount { get; set; }
        public int OverdueCount { get; set; }
    }
    public class LeadReportFromDBDto : IDto
    {
        public Guid UserId { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public int All { get; set; }
        public int New { get; set; }
        public int Dropped { get; set; }
        public int Pending { get; set; }
        public int Callback { get; set; }
        public int Booked { get; set; }
        public int MeetingScheduled { get; set; }
        public int SiteVisitScheduled { get; set; }
        public int Overdue { get; set; }
        public int NotInterested { get; set; }

    }
    public class LicwLwsvmsLtDto : IDto
    {
        public int SiteVisitDoneLeadCount { get; set; }
        public int MeetingDoneLeadCount { get; set; }
        public int CallbackLeadCount { get; set; }
        public int SiteVisitDoneEventCount { get; set; }
        public int SiteVisitNotDoneEventCount { get; set; }
        public int MeetingDoneEventCount { get; set; }
        public int MeetingNotDoneEventCount { get; set; }
        public int SMS { get; set; }
        public int Call { get; set; }
        public int Email { get; set; }
        public int WhatsApp { get; set; }
        public int Total { get; set; }
    }
    public class LeadsCountByStatusDto : IDto
    {
        public Guid AssignTo { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? Status { get; set; }
        public int Count { get; set; }
    }

    public class LeadReceivedCountDto : IDto
    {

        public DateTime CreatedOn { get; set; }
        public int? Source { get; set; }
        public int LeadCount { get; set; }

    }

    public class LeadSourcesDto:IDto
    {
        public LeadSource LeadSource { get; set; }
        public LeadSourceType Type { get; set; }
        public string BackgroundColor { get; set; }
        public string ImageURL { get; set; }
        public string ProgressColor { get; set; }
        public int Count { get; set; }


    }



    public class SiteVisitDto : IDto
    {
        public int SiteVisitDoneUniqueCount { get; set; }
        public int SiteVisitDoneEventCount { get; set; }
        public int SiteVisitNotDoneUniqueCount { get; set; }
        public int SiteVisitNotDoneEventCount { get; set; }
        public int SiteVisitOverdue { get; set; }

       
        public int UpcomingSiteVisits { get; set; }
    }
    public class MeetingDto : IDto
    {
      

        public int MeetingDoneUniqueCount { get; set; }
        public int MeetingDoneEventCount { get; set; }
        public int MeetingNotDoneUniqueCount { get; set; }
        public int MeetingNotDoneEventCount { get; set; }

        public int MeetingOverdue { get; set; }


        public int UpcomingMeetings { get; set; }
     

    }
   
}
