﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class UserDetailsLogoutChanges : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "LastLogoutStamp",
                schema: "LeadratBlack",
                table: "UserDetails",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "LastLogoutType",
                schema: "LeadratBlack",
                table: "UserDetails",
                type: "integer",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "LastLogoutStamp",
                schema: "LeadratBlack",
                table: "UserDetails");

            migrationBuilder.DropColumn(
                name: "LastLogoutType",
                schema: "LeadratBlack",
                table: "UserDetails");
        }
    }
}
