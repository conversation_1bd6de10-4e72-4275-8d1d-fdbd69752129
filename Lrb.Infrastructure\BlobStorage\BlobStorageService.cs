﻿using Amazon;
using Amazon.S3;
using Amazon.S3.Model;
using Lrb.Application.Common.BlobStorage;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;

namespace Lrb.Infrastructure.BlobStorage
{
    public class BlobStorageService : IBlobStorageService
    {
        private readonly AWSSettings _settings;
        private AmazonS3Client _s3Client;
        public string? BucketName { get; set; }
        public string? AWSS3BucketUrl { get; set; }
        public BlobStorageService(IOptions<AWSSettings> options)
        {
            _settings = options.Value;
            BucketName = _settings?.AWSS3BucketName;
            AWSS3BucketUrl = _settings?.AWSS3BucketUrl;
        }
        public async Task<bool> DeleteObjectAsync(string bucketName, string key)
        {
            _s3Client = new AmazonS3Client(_settings.AWSAccessToken, _settings.AWSSecret, RegionEndpoint.GetBySystemName(_settings.Region));

            using (_s3Client)
            {
                var request = new DeleteObjectRequest
                {
                    BucketName = bucketName,
                    Key = key
                };
                DeleteObjectResponse response = await _s3Client.DeleteObjectAsync(request);
                return response.HttpStatusCode == System.Net.HttpStatusCode.NoContent;
            }
        }

        public async Task<Stream> GetObjectAsync(string bucketName, string key)
        {
            _s3Client = new AmazonS3Client(_settings.AWSAccessToken, _settings.AWSSecret, RegionEndpoint.GetBySystemName(_settings.Region));

            using (_s3Client)
            {
                GetObjectRequest request = new()
                {
                    BucketName = bucketName,
                    Key = key
                };
                GetObjectResponse response = await _s3Client.GetObjectAsync(request);
                return response.ResponseStream;
            }
        }

        public async Task<string> GetPreSignedURL(string bucketName, string key, int validityInSeconds = 3600)
        {
            _s3Client = new AmazonS3Client(_settings.AWSAccessToken, _settings.AWSSecret, RegionEndpoint.GetBySystemName(_settings.Region));

            string presignedUrl = string.Empty;
            using (_s3Client)
            {
                presignedUrl = _s3Client.GetPreSignedURL(new GetPreSignedUrlRequest()
                {
                    BucketName = bucketName,
                    Key = key,
                    Expires = DateTime.UtcNow.AddSeconds(validityInSeconds)
                });
            }

            return presignedUrl;
        }
        public async Task<string> UploadObjectAsync(string bucketName, string folderName, string bytebase64encodedFile)
        {
            _s3Client = new AmazonS3Client(_settings.AWSAccessToken, _settings.AWSSecret, RegionEndpoint.GetBySystemName(_settings.Region));

            using (_s3Client)
            {
                string fileName = DateTime.Now.ToString("MMddyyyyfffffff") + "." + bytebase64encodedFile.Split(';')[0].Split('/')[1];
                bytebase64encodedFile = bytebase64encodedFile.Split(',')[1];
                byte[] bytes = Convert.FromBase64String(bytebase64encodedFile);
                using (Stream stream = new MemoryStream(bytes))
                {
                    string key = string.Format(folderName + "/" + DateTime.Now.ToString("MMddyyyyfffffff") + fileName);
                    var request = new PutObjectRequest
                    {
                        BucketName = bucketName,
                        CannedACL = S3CannedACL.Private,
                        Key = key
                    };
                    PutObjectResponse response;
                    using (stream)
                    {
                        request.InputStream = stream;
                        response = await _s3Client.PutObjectAsync(request);
                    }
                    if (response.HttpStatusCode == System.Net.HttpStatusCode.OK)
                        return key;
                    else
                        return "Failure";
                }
            }
        }
        public async Task<string> UploadWebObjectAsync(string bucketName, string folderName, string bytebase64encodedFile, string fileName)
        {
            _s3Client = new AmazonS3Client(_settings.AWSAccessToken, _settings.AWSSecret, RegionEndpoint.GetBySystemName(_settings.Region));

            using (_s3Client)
            {
                fileName = DateTime.Now.ToString("MMddyyyyfffffff") + "." + fileName;
                bytebase64encodedFile = bytebase64encodedFile.Split(',')[0];
                byte[] bytes = Convert.FromBase64String(bytebase64encodedFile);
                using (Stream stream = new MemoryStream(bytes))
                {
                    string key = string.Format(folderName + "/" + fileName);
                    var request = new PutObjectRequest
                    {
                        BucketName = bucketName,
                        CannedACL = S3CannedACL.Private,
                        Key = key
                    };
                    PutObjectResponse response;
                    using (stream)
                    {
                        request.InputStream = stream;
                        response = await _s3Client.PutObjectAsync(request);
                    }
                    if (response.HttpStatusCode == System.Net.HttpStatusCode.OK)
                        return key;
                    else
                        return "Failure";
                }
            }
        }

        public async Task<string> UploadWebObjectV2Async(string bucketName, string folderName, string bytebase64encodedFile, string fileName)
        {
            _s3Client = new AmazonS3Client(_settings.AWSAccessToken, _settings.AWSSecret, RegionEndpoint.GetBySystemName(_settings.Region));

            using (_s3Client)
            {
                bytebase64encodedFile = bytebase64encodedFile.Split(',')[0];
                byte[] bytes = Convert.FromBase64String(bytebase64encodedFile);
                using (Stream stream = new MemoryStream(bytes))
                {
                    string key = string.Format(folderName + "/" + fileName);
                    var request = new PutObjectRequest
                    {
                        BucketName = bucketName,
                        CannedACL = S3CannedACL.Private,
                        Key = key
                    };
                    PutObjectResponse response;
                    using (stream)
                    {
                        request.InputStream = stream;
                        response = await _s3Client.PutObjectAsync(request);
                    }
                    if (response.HttpStatusCode == System.Net.HttpStatusCode.OK)
                        return key;
                    else
                        return "Failure";
                }
            }
        }

        public async Task<string> UploadMobileObjectAsync(string bucketName, string folderName, string bytebase64encodedFile, string fileName)
        {
            _s3Client = new AmazonS3Client(_settings.AWSAccessToken, _settings.AWSSecret, RegionEndpoint.GetBySystemName(_settings.Region));

            using (_s3Client)
            {
                fileName = DateTime.Now.ToString("MMddyyyyfffffff") + "." + fileName;
                bytebase64encodedFile = bytebase64encodedFile.Split(',')[0];
                byte[] bytes = Convert.FromBase64String(bytebase64encodedFile);
                using (Stream stream = new MemoryStream(bytes))
                {
                    string key = string.Format(folderName + "/" + fileName);
                    var request = new PutObjectRequest
                    {
                        BucketName = bucketName,
                        CannedACL = S3CannedACL.Private,
                        Key = key
                    };
                    PutObjectResponse response;
                    using (stream)
                    {
                        request.InputStream = stream;
                        response = await _s3Client.PutObjectAsync(request);
                    }
                    if (response.HttpStatusCode == System.Net.HttpStatusCode.OK)
                        return key;
                    else
                        return "Failure";
                }
            }
        }
        public async Task<string> UploadObjectAsync(string bucketName, string folderName, IFormFile formFile)
        {
            _s3Client = new AmazonS3Client(_settings.AWSAccessToken, _settings.AWSSecret, RegionEndpoint.GetBySystemName(_settings.Region));
            MemoryStream imageStream = new MemoryStream();
            if (formFile != null)
            {
                await formFile.CopyToAsync(imageStream);
                string fileName = formFile.FileName;
                fileName = DateTime.Now.ToString("MMddyyyyfffffff") + "." + fileName.Split('.')[1];
                using (_s3Client)
                {
                    string key = string.Format(folderName + "/" + fileName);
                    var request = new PutObjectRequest
                    {
                        BucketName = bucketName,
                        CannedACL = S3CannedACL.Private,
                        Key = key
                    };
                    PutObjectResponse response;
                    using (imageStream)
                    {
                        request.InputStream = imageStream;
                        response = await _s3Client.PutObjectAsync(request);
                    }
                    if (response.HttpStatusCode == System.Net.HttpStatusCode.OK)
                        return key;
                    else
                        return "Failure";
                }
            }
            else
            {
                return "Failure";
            }
        }
        public async Task<string> UploadObjectAsync(string bucketName, string folderName, string fileName, byte[] bytes)
        {
            _s3Client = new AmazonS3Client(_settings.AWSAccessToken, _settings.AWSSecret, RegionEndpoint.GetBySystemName(_settings.Region));

            using (_s3Client)
            {
                using (Stream stream = new MemoryStream(bytes))
                {
                    string key = string.Format(folderName + "/"+ fileName);
                    var request = new PutObjectRequest
                    {
                        BucketName = bucketName,
                        CannedACL = S3CannedACL.Private,
                        Key = key
                    };
                    PutObjectResponse response;
                    using (stream)
                    {
                        request.InputStream = stream;
                        response = await _s3Client.PutObjectAsync(request);
                    }
                    if (response.HttpStatusCode == System.Net.HttpStatusCode.OK)
                        return key;
                    else
                        return "Failure";
                }
            }
        }
        public async Task<string> UploadObjectAsync(string bucketName, string folderName, string fileName, byte[] bytes, params int[] optional)
        {
            _s3Client = new AmazonS3Client(_settings.AWSAccessToken, _settings.AWSSecret, RegionEndpoint.GetBySystemName(_settings.Region));

            using (_s3Client)
            {
                using (Stream stream = new MemoryStream(bytes))
                {
                    string key = string.Format(folderName + "/" + fileName);
                    var request = new PutObjectRequest
                    {
                        BucketName = bucketName,
                        CannedACL = S3CannedACL.Private,
                        Key = key
                    };
                    PutObjectResponse response;
                    using (stream)
                    {
                        request.InputStream = stream;
                        response = await _s3Client.PutObjectAsync(request);
                    }
                    if (response.HttpStatusCode == System.Net.HttpStatusCode.OK)
                        return key;
                    else
                        return "Failure";
                }
            }
            }
        public async Task<string> UploadObjectAsync(string bucketName, string folderName, string fileName, Stream stream)
        {
            _s3Client = new AmazonS3Client(_settings.AWSAccessToken, _settings.AWSSecret, RegionEndpoint.GetBySystemName(_settings.Region));

            using (_s3Client)
            {
                using (stream)
                {
                    string key = string.Format(folderName + "/" + DateTime.Now.ToString("MMddyyyyfffffff") + fileName);
                    var request = new PutObjectRequest
                    {
                        BucketName = bucketName,
                        CannedACL = S3CannedACL.Private,
                        Key = key
                    };
                    PutObjectResponse response;
                    using (stream)
                    {
                        request.InputStream = stream;
                        response = await _s3Client.PutObjectAsync(request);
                    }
                    if (response.HttpStatusCode == System.Net.HttpStatusCode.OK)
                        return key;
                    else
                        return "Failure";
                }
            }
        }
        public async Task<string> UploadFileAsync(string fileName, byte[] imageData)
        {
            try
            {
                using (var s3Client = new AmazonS3Client(_settings.AWSAccessToken, _settings.AWSSecret, RegionEndpoint.GetBySystemName(_settings.Region)))
                {
                    var request = new PutObjectRequest
                    {
                        BucketName = BucketName,
                        InputStream = new MemoryStream(imageData),
                        Key = fileName,
                        CannedACL = S3CannedACL.Private

                    };

                    var response = await s3Client.PutObjectAsync(request);

                    var url = $"{AWSS3BucketUrl}{fileName}";

                    return url;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

    }
}
    