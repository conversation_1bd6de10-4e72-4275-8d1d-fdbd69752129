﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Domain.Entities.Integration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Integration.Web.Requests
{
    public class GetGoogleAdsIntegrationAccountInfoRequest : IRequest<Response<string>>
    {
        public Guid Id { get; set; }
        public GetGoogleAdsIntegrationAccountInfoRequest(Guid id)
        {
            Id = id;
        }
    }
    public class GetGoogleAdsnItegrationAccountInfoRequestHandler : IRequestHandler<GetGoogleAdsIntegrationAccountInfoRequest, Response<string>>
    {
        private readonly IReadRepository<GoogleAdLeadFormIntegrationInfo> _integrationRepo;
        private readonly IBlobStorageService _blobStorageService;
        public GetGoogleAdsnItegrationAccountInfoRequestHandler(IReadRepository<GoogleAdLeadFormIntegrationInfo> integrationRepo,
            IBlobStorageService blobStorageService)
        {
            _integrationRepo = integrationRepo;
            _blobStorageService = blobStorageService;
        }
        public async Task<Response<string>> Handle(GetGoogleAdsIntegrationAccountInfoRequest request, CancellationToken cancellationToken)
        {
            var account = await _integrationRepo.GetByIdAsync(request.Id);
            string fileUrl = await _blobStorageService.GetPreSignedURL(_blobStorageService?.BucketName ?? "prd-leadratblack", account.FileUrl);
            return new(fileUrl, default);
        }
    }
}
