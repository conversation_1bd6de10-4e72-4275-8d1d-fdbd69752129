﻿using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Common.ServiceBus;
using Lrb.Application.DataManagement.Mobile.Dtos;
using Lrb.Application.DataManagement.Mobile.Specs;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Mobile.Dtos.v1;
using static Lrb.Application.Lead.Mobile.Requests.v1.GetAllLeadsOfflineRequestHandler;

namespace Lrb.Application.DataManagement.Mobile.Requests
{
    public class GetAllProspectsByLastModifiedRequest : PaginationFilter, IRequest<Response<List<GetAllOfflineProspectsDto>>>
    {
        public DateTime? DateRangeFrom { get; set; }
        public DateTime? DateRangeTo { get; set; }
        public Guid? UserId { get; set; }
        public bool? SendOnlyAssignedLeads { get; set; }
        public string? TenantId { get; set; }
    }

    public class GetAllProspectsByLastModifiedRequestHandler : IRequestHandler<GetAllProspectsByLastModifiedRequest, Response<List<GetAllOfflineProspectsDto>>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.Prospect> _prospectsRepo;
        private readonly IUserService _userService;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        private readonly IServiceBus _serviceBus;
        public GetAllProspectsByLastModifiedRequestHandler(IRepositoryWithEvents<Domain.Entities.Prospect> prospectsRepo,
        IUserService userService,
        IDapperRepository dapperRepository,
        ICurrentUser currentUser,
        IServiceBus serviceBus)
        {
            _prospectsRepo = prospectsRepo;
            _userService = userService;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
            _serviceBus = serviceBus;
        }

        public async Task<Response<List<GetAllOfflineProspectsDto>>> Handle(GetAllProspectsByLastModifiedRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var tenantId = request.TenantId ?? _currentUser.GetTenant();
                if (request.PageNumber < 0 && request.PageSize < 0)
                {
                    throw new ArgumentException("Pagination is required: PageNumber and PageSize must be greater than 0.");
                }
                if (request.UserId != null)
                {
                    var user = await _userService.GetAsync(request.UserId.ToString() ?? "", cancellationToken);
                    if (user.IsActive)
                    {
                        return new(await _dapperRepository.GetAllOfflineProspectsAsync(tenantId ?? string.Empty, request.UserId, request.SendOnlyAssignedLeads ?? false, request.DateRangeFrom, request.DateRangeTo, pageNumber: request.PageNumber, pageSize: request.PageSize));
                    }
                    return new("UnAuthorized");
                }
                return null;

            }
            catch (Exception ex)
            {
                return null;
            }
        }
    }
}
