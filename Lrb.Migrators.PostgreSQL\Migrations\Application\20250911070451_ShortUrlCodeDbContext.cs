﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class ShortUrlCodeDbContext : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Code",
                schema: "LeadratBlack",
                table: "Properties",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LongUrl",
                schema: "LeadratBlack",
                table: "Properties",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ShortUrl",
                schema: "LeadratBlack",
                table: "Properties",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Code",
                schema: "LeadratBlack",
                table: "Projects",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LongUrl",
                schema: "LeadratBlack",
                table: "Projects",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ShortUrl",
                schema: "LeadratBlack",
                table: "Projects",
                type: "text",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Code",
                schema: "LeadratBlack",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "LongUrl",
                schema: "LeadratBlack",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "ShortUrl",
                schema: "LeadratBlack",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "Code",
                schema: "LeadratBlack",
                table: "Projects");

            migrationBuilder.DropColumn(
                name: "LongUrl",
                schema: "LeadratBlack",
                table: "Projects");

            migrationBuilder.DropColumn(
                name: "ShortUrl",
                schema: "LeadratBlack",
                table: "Projects");
        }
    }
}
