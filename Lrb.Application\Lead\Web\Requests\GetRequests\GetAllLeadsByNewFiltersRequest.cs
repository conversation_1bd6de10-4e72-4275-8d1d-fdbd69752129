﻿using Azure.Core;
using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Team.Web;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Microsoft.Graph;
using Newtonsoft.Json;
using System.Collections.Concurrent;

namespace Lrb.Application.Lead.Web
{
    public class GetAllLeadsByNewFiltersRequest : GetAllLeadsParametersNewFilters, IRequest<PagedResponse<ViewLeadDto, LeadCountsByNewFilterDto>>
    {

       
    }
    public class GetAllLeadsByNewFiltersRequestHandler : IRequestHandler<GetAllLeadsByNewFiltersRequest, PagedResponse<ViewLeadDto, LeadCountsByNewFilterDto>>
    {
        public string? manageLeadsCount = (typeof(LeadCountsByNewFilterDto).GetProperty("ManageLeadsCount"))?.ToString() ?? null;
        private readonly ICurrentUser _currentUser;
        private readonly IDapperRepository _dapperRepository;
        private readonly ILeadRepository _efLeadRepository;
        private readonly IUserService _userService;
        private readonly ILeadHistoryRepository _leadHistoryRepository;
        private readonly IRepositoryWithEvents<Address> _addressRepo;
        //private readonly IRepositoryWithEvents<MasterLeadStatus> _leadStatusRepo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customLeadStatusRepo;
        public GetAllLeadsByNewFiltersRequestHandler(
            ICurrentUser currentUser,
            IDapperRepository dapperRepository,
            ILeadRepository efLeadRepository,
            IUserService userService,
            ILeadHistoryRepository leadHistoryRepository,
            IRepositoryWithEvents<Address> addressRepo,
            //IRepositoryWithEvents<MasterLeadStatus> leadStatusRepo,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<CustomMasterLeadStatus> customLeadStatusRepo)
        {
            _currentUser = currentUser;
            _dapperRepository = dapperRepository;
            _efLeadRepository = efLeadRepository;
            _userService = userService;
            _leadHistoryRepository = leadHistoryRepository;
            _addressRepo = addressRepo;
            //_leadStatusRepo = leadStatusRepo;
            _leadRepositoryAsync = leadRepositoryAsync;
            _customLeadStatusRepo = customLeadStatusRepo;
        }
        public async Task<PagedResponse<ViewLeadDto, LeadCountsByNewFilterDto>> Handle(GetAllLeadsByNewFiltersRequest request, CancellationToken cancellationToken)
        {
            if (request?.LeadTags?.Any() ?? false)
            {
                request.TagFilterDto = GetLeadTagFilter(request);
                request.LeadTags = null;
            }
            var userId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var isAdmin = _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty).Result;
            List<Guid> leadHistoryIds = new();
            List<Guid> subIds = new();
            try
            {
                if (request?.AssignTo?.Any() ?? false)
                {
                    if (request?.IsWithTeam ?? false)
                    {
                        subIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.AssignTo, tenantId ?? string.Empty)).ToList();
                    }
                    else
                    {
                        subIds = request?.AssignTo ?? new List<Guid>();
                    }
                }
                else
                {
                    subIds = (await _dapperRepository.GetSubordinateIdsAsync(userId, tenantId ?? string.Empty, request?.CanAccessAllLeads))?.ToList() ?? new();
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "GetAllLeadsByNewFiltersRequestHandler -> Handle()->GetSubordinateIdsAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
            if (request.IsDualOwnershipEnabled == null)
            {
                request.IsDualOwnershipEnabled = await _dapperRepository.GetDualOwnershipDetails(tenantId ?? string.Empty);
            }
            var leads = _efLeadRepository.GetAllLeadsByNewFiltersForWebAsync(request, subIds, userId, leadHistoryIds, isAdmin: isAdmin).Result;
            var totalCount = await _efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, leadHistoryIds,isAdmin : isAdmin);
            List<ViewLeadDto> leadDtos = new();
            leadDtos = leads.Adapt<List<ViewLeadDto>>();
            #region Count
            LeadCountsByNewFilterDto leadsCount = new();
            var types = typeof(LeadCountsByNewFilterDto).GetProperties();
            for (int i = 0; i < types.Length; i++)
            {
                var requestCopy = request.Adapt<GetAllLeadsByNewFiltersRequest>();
                AddLeadsCount(leadsCount, types[i], requestCopy, subIds, userId, leadHistoryIds,isAdmin).GetAwaiter();
            }
            //leadsCount.ManageLeadsCount = leadsCount.AllLeadsCount + leadsCount.DeletedLeadsCount + leadsCount.UnassignLeadsCount;
            #endregion
            return new PagedResponse<ViewLeadDto, LeadCountsByNewFilterDto>(leadDtos, totalCount, leadsCount);
        }
        private async Task AddLeadsCount(LeadCountsByNewFilterDto leadsCount, System.Reflection.PropertyInfo property, GetAllLeadsByNewFiltersRequest request, IEnumerable<Guid> subIds, Guid userId, List<Guid> leadHistoryIds,bool? isAdmin)
        {
            var propertyName = property.Name;
            switch (propertyName)
            {
                case nameof(LeadCountsByNewFilterDto.ManageLeadsCount):
                    request.LeadVisibility = (BaseLeadVisibility)(-1);
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    leadsCount.ManageLeadsCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.MyLeadsCount):
                    request.LeadVisibility = BaseLeadVisibility.Self;
                    leadsCount.MyLeadsCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.TeamLeadsCount):
                    request.LeadVisibility = BaseLeadVisibility.Reportee;
                    leadsCount.TeamLeadsCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.AllLeadsCount):
                    request.LeadVisibility = BaseLeadVisibility.SelfWithReportee;
                    leadsCount.AllLeadsCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.UnassignLeadsCount):
                    request.LeadVisibility = BaseLeadVisibility.UnassignLead;
                    leadsCount.UnassignLeadsCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.DeletedLeadsCount):
                    request.LeadVisibility = BaseLeadVisibility.DeletedLeads;
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.All;
                    leadsCount.DeletedLeadsCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, new(), isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.DuplicateLeadsCount):
                    request.LeadVisibility = BaseLeadVisibility.DuplicateLeads;
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.All;
                    leadsCount.DuplicateLeadsCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, new(), isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.NewLeadsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.New;
                    leadsCount.NewLeadsCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.ActiveLeadsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.All;
                    leadsCount.ActiveLeadsCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.PendingLeadsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.Pending;
                    leadsCount.PendingLeadsCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.ScheduledLeadsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.Scheduled;
                    leadsCount.ScheduledLeadsCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.OverdueLeadsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.Overdue;
                    leadsCount.OverdueLeadsCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.BookedLeadsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.Booked;
                    leadsCount.BookedLeadsCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.ScheduledTodayLeadsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.Scheduled;
                    request.ScheduledDateTypeFilter = ScheduledDateTypeFilter.Today;
                    request.ScheduledType = ScheduledType.None;
                    leadsCount.ScheduledTodayLeadsCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.ScheduledTomorrowLeadsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.Scheduled;
                    request.ScheduledDateTypeFilter = ScheduledDateTypeFilter.Tomorrow;
                    request.ScheduledType = ScheduledType.None;
                    leadsCount.ScheduledTomorrowLeadsCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.ScheduledNextTwoDaysLeadsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.Scheduled;
                    request.ScheduledDateTypeFilter = ScheduledDateTypeFilter.NextTwoDays;
                    request.ScheduledType = ScheduledType.None;
                    leadsCount.ScheduledNextTwoDaysLeadsCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.UpcomingScheduledLeadsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.Scheduled;
                    request.ScheduledDateTypeFilter = ScheduledDateTypeFilter.Upcoming;
                    request.ScheduledType = ScheduledType.None;
                    leadsCount.UpcomingScheduledLeadsCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.MeetingsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.Scheduled;
                    request.ScheduledType = ScheduledType.Meeting;
                    leadsCount.MeetingsCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.SiteVisitsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.Scheduled;
                    request.ScheduledType = ScheduledType.SiteVisit;
                    leadsCount.SiteVisitsCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.CallbackCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.Scheduled;
                    request.ScheduledType = ScheduledType.Callback;
                    leadsCount.CallbackCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.NotInterestedLeadsCount):
                    request.FirstLevelFilter = FirstLevelFilter.NotInterested;
                    request.SecondLevelFilter = SecondLevelFilter.All;
                    leadsCount.NotInterestedLeadsCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.DifferentRequirementsCount):
                    request.FirstLevelFilter = FirstLevelFilter.NotInterested;
                    request.SecondLevelFilter = SecondLevelFilter.DifferentRequirements;
                    leadsCount.DifferentRequirementsCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.DifferentLocationCount):
                    request.FirstLevelFilter = FirstLevelFilter.NotInterested;
                    request.SecondLevelFilter = SecondLevelFilter.DifferentLocation;
                    leadsCount.DifferentLocationCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.UnmatchedBudgetCount):
                    request.FirstLevelFilter = FirstLevelFilter.NotInterested;
                    request.SecondLevelFilter = SecondLevelFilter.UnmatchedBudget;
                    leadsCount.UnmatchedBudgetCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.DroppedLeadsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Dropped;
                    request.SecondLevelFilter = SecondLevelFilter.All;
                    leadsCount.DroppedLeadsCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.NotLookingCount):
                    request.FirstLevelFilter = FirstLevelFilter.Dropped;
                    request.SecondLevelFilter = SecondLevelFilter.NotLooking;
                    leadsCount.NotLookingCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.WrongOrInvalidNoCount):
                    request.FirstLevelFilter = FirstLevelFilter.Dropped;
                    request.SecondLevelFilter = SecondLevelFilter.WrongOrInvalidNo;
                    leadsCount.WrongOrInvalidNoCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.PurchasedFromOthersCount):
                    request.FirstLevelFilter = FirstLevelFilter.Dropped;
                    request.SecondLevelFilter = SecondLevelFilter.PurchasedFromOthers;
                    leadsCount.PurchasedFromOthersCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.EscalatedLeadsFlagCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.All;
                    request.LeadTags = new List<LeadTagEnum> { LeadTagEnum.IsEscalated };
                    leadsCount.EscalatedLeadsFlagCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.HotLeadsFlagCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.All;
                    request.LeadTags = new List<LeadTagEnum> { LeadTagEnum.IsHot };
                    leadsCount.HotLeadsFlagCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.WarmLeadsFlagCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.All;
                    request.LeadTags = new List<LeadTagEnum> { LeadTagEnum.IsWarmLead };
                    leadsCount.WarmLeadsFlagCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.ColdLeadsFlagCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.All;
                    request.LeadTags = new List<LeadTagEnum> { LeadTagEnum.IsColdLead };
                    leadsCount.ColdLeadsFlagCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.AboutToConvertLeadsFlagCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.All;
                    request.LeadTags = new List<LeadTagEnum> { LeadTagEnum.IsAboutToConvert };
                    leadsCount.AboutToConvertLeadsFlagCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.HighlightedLeadsFlagCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.All;
                    request.LeadTags = new List<LeadTagEnum> { LeadTagEnum.IsHighlighted };
                    leadsCount.HighlightedLeadsFlagCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.AllFirstLevelLeadsCount):
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    leadsCount.AllFirstLevelLeadsCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.BookingCancelLeadCount):
                    request.FirstLevelFilter = FirstLevelFilter.BookingCancel;
                    leadsCount.BookingCancelLeadCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, isAdmin: isAdmin)).Result;
                    break;
                case nameof(LeadCountsByNewFilterDto.ReEnquiredLeadsCount):
                    request.LeadVisibility = BaseLeadVisibility.DeletedLeads;
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.All;
                    leadsCount.ReEnquiredLeadsCount = (_efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(request, subIds, userId, new(), isAdmin: isAdmin)).Result;
                    break;
            }
        }
        public LeadTagFilterDto GetLeadTagFilter(GetAllLeadsByNewFiltersRequest request)
        {
            LeadTagFilterDto tagFilterDto = new LeadTagFilterDto();
            foreach (var tag in request.LeadTags)
                switch (tag)
                {
                    case LeadTagEnum.IsHot:
                        tagFilterDto.IsHotLead = true;
                        break;
                    case LeadTagEnum.IsAboutToConvert:
                        tagFilterDto.IsAboutToConvert = true;
                        break;
                    case LeadTagEnum.IsEscalated:
                        tagFilterDto.IsEscalated = true;
                        break;
                    case LeadTagEnum.IsIntegrationLead:
                        tagFilterDto.IsIntegrationLead = true;
                        break;
                    case LeadTagEnum.IsHighlighted:
                        tagFilterDto.IsHighlighted = true;
                        break;
                    case LeadTagEnum.IsWarmLead:
                        tagFilterDto.IsWarmLead = true;
                        break;
                    case LeadTagEnum.IsColdLead:
                        tagFilterDto.IsColdLead = true;
                        break;
                }
            return tagFilterDto;
        }
    }
    public enum FirstLevelFilter
    {
        All = 0,
        Active,
        NotInterested,
        Dropped,
        Booked,
        BookingCancel,
        Invoiced
    }
    public enum SecondLevelFilter
    {
        None = 0,
        New,
        Pending,
        Scheduled,
        Overdue,
        Booked,
        All,
        DifferentRequirements,
        PlanPostponed,
        DifferentLocation,
        UnmatchedBudget,
        NotLooking,
        WrongOrInvalidNo,
        PurchasedFromOthers,
        RingingNotReceived,
        ExpressionOfInterest,
        Invoiced
    }
    public enum ScheduledDateTypeFilter
    {
        All = 0,
        Today,
        Tomorrow,
        NextTwoDays,
        Upcoming
    }
    public enum ScheduledType
    {
        None = 0,
        Meeting,
        SiteVisit,
        Callback
    }



   
}
