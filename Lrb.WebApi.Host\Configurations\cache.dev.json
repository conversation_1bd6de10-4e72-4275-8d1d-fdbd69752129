﻿{
  "CacheSettings": {
    "UseDistributedCache": false,
    "PreferRedis": false,
    "RedisURL": "localhost:10420"
  },
  "RedisStackSettings": {
    "ConnectionString": "redis-10420.crce182.ap-south-1-1.ec2.redns.redis-cloud.com:10420,User=default,Password=zslBjP0i4YtANuvHctE5trkxOT9YwbzI",
    "DefaultDatabase": 0,
    "SSL": false,
    "AbortOnConnectFail": false,
    "ConnectTimeout": 5000,
    "ConnectRetry": 3,
    "KeepAlive": 180,
    "SyncTimeout": 5000
  }
}