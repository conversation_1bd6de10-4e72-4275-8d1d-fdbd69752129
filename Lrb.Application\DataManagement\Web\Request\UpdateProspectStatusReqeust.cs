﻿using DocumentFormat.OpenXml.Spreadsheet;
using Grpc.Net.Client.Configuration;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.DailyStatusUpdates.Dtos;
using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.DataManagement.Web.Mapping;
using Lrb.Application.DataManagement.Web.Request.CommonHandler;
using Lrb.Application.DataManagement.Web.Specs;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Identity.Users;
using Lrb.Domain.Entities;
using Microsoft.Extensions.DependencyInjection;

namespace Lrb.Application.DataManagement.Web.Request
{
    public class UpdateProspectStatusReqeust : IRequest<Response<bool>>
    {
        public Guid Id { get; set; }
        public Guid StatusId { get; set; }
        public DateTime? ScheduleDate { get; set; }
        public string? Notes { get; set; }
    }

    public class UpdateProspectStatusReqeustHandler : DataStatusHand<PERSON>, IRequestHandler<UpdateProspectStatusReqeust, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Prospect> _prospectRepo;
        private readonly IRepositoryWithEvents<CustomProspectStatus> _prospectStatusRepo;
        private readonly IRepositoryWithEvents<ProspectHistory> _prospcetHistoryRepo;
        private readonly IUserService _userService;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.MasterProspectSource> _prospectSourceRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.MasterData.MasterPropertyType> _propertyTypeRepo;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<ProspectHistory> _prospectHistoryRepo;
        private IServiceProvider _serviceProvider;
        public UpdateProspectStatusReqeustHandler(
            IRepositoryWithEvents<Prospect> prospectRepo,
            IRepositoryWithEvents<CustomProspectStatus> prospectStatusRepo,
            IRepositoryWithEvents<ProspectHistory> prospcetHistoryRepo,
            IUserService userService,
            IRepositoryWithEvents<Lrb.Domain.Entities.MasterProspectSource> prospectSourceRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.MasterData.MasterPropertyType> propertyTypeRepo,
            ICurrentUser currentUser,
            IRepositoryWithEvents<ProspectHistory> prospectHistoryRepo,
            IServiceProvider serviceProvider
            ) : base(serviceProvider)
        {
            _prospectRepo = prospectRepo;
            _prospectStatusRepo = prospectStatusRepo;
            _propertyTypeRepo = propertyTypeRepo;
            _userService = userService;
            _prospcetHistoryRepo = prospcetHistoryRepo;
            _prospectSourceRepo = prospectSourceRepo;
            _currentUser = currentUser;
            _prospectHistoryRepo = prospectHistoryRepo;
        }
        public async Task<Response<bool>> Handle(UpdateProspectStatusReqeust request, CancellationToken cancellationToken)
        {
            var existingProspect = await _prospectRepo.FirstOrDefaultAsync(new GetProspectByIdSpecs(request.Id), cancellationToken);
            var oldProspect = existingProspect?.Adapt<ViewProspectDto>();
            if (existingProspect == null)
            {
                throw new NotFoundException("No Prospect Found By This Id");
            }
            if (request.StatusId == default)
            {
                throw new InvalidOperationException("The Status Id is not valid");
            }
            var chaildStatuses = await _prospectStatusRepo.ListAsync(new GetProspectStatusByBaseIdSpecs(request.StatusId));
            if (chaildStatuses?.Any() ?? false)
            {
                throw new ArgumentException("Please provide child status id.");
            }
            var status = await _prospectStatusRepo.GetByIdAsync(request.StatusId, cancellationToken);
            var currentUser = _currentUser.GetUserId();
            if (status?.Status == "qualified")
            {
                existingProspect.QualifiedDate = DateTime.UtcNow;
                existingProspect.QualifiedBy = currentUser;
            }
            existingProspect.ScheduleDate = request.ScheduleDate;
            existingProspect.Notes = request.Notes;
            existingProspect.Status = status;
            await _prospectRepo.UpdateAsync(existingProspect);

            #region History
            var userIds = new List<string?>
            {
                existingProspect.AssignTo.ToString(),
                existingProspect.LastModifiedBy.ToString(),
                existingProspect.AssignedFrom.ToString(),
                existingProspect.SourcingManager.ToString(),
                existingProspect.ClosingManager.ToString(),
            };
            var userDetails = await _userService.GetListOfUsersByIdsAsync(userIds, cancellationToken);
            var currentUserId = _currentUser.GetUserId();
            var statuses = await _prospectStatusRepo.ListAsync();
            var prospectVM = existingProspect.Adapt<ViewProspectDto>();
            prospectVM = await ProspectHistoryHelper.SetUserViewForProspectV1(prospectVM, userDetails, cancellationToken);
            oldProspect = await ProspectHistoryHelper.SetUserViewForProspectV1(oldProspect, userDetails, cancellationToken);
            var histories = await ProspectHistoryHelper.UpdateProspectHistoryForVM(prospectVM, oldProspect, currentUserId, 1, statuses, null, null, _userService, cancellationToken);
            await _prospectHistoryRepo.AddRangeAsync(histories);
            var globalSetting = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec());
            if (request.ScheduleDate != null)
            {
                await SendProspectStatusChangeNotificationsAsync(existingProspect, prospectVM,globalSetting, currentUserId: currentUser);
            }

            #endregion
            return new(true);
        }

    }
}
