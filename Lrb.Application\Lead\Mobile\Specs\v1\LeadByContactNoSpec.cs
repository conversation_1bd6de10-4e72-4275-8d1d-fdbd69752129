﻿namespace Lrb.Application.Lead.Mobile
{
    public class LeadByContactNoSpec : Specification<Domain.Entities.Lead>
    {
        public LeadByContactNoSpec(List<string> contactNos) =>
            Query.Where(i => !i.IsDeleted && !i.IsArchived && contactNos.Contains(i.ContactNo ?? string.Empty)).Where(i => !i.IsArchived);

    }
    public class CheckLeadByContactNoSpec : Specification<Domain.Entities.Lead>
    {
        public CheckLeadByContactNoSpec(string contactNo)
        {
            Query.Where(i => !i.IsDeleted
                && !i.IsArchived
                && ((i.ContactNo != null && (i.ContactNo.Contains(contactNo)))
                        ||
                   (i.AlternateContactNo != null && (i.AlternateContactNo.Contains(contactNo)))
                   ));
        }
        public CheckLeadByContactNoSpec(string contactNo, string countryCode)
        {
            if(contactNo != null && countryCode != null)
            {
                string formattedContactNo = contactNo.StartsWith(countryCode) ? contactNo.Substring(countryCode.Length) : countryCode + contactNo;

                Query.Where(i => !i.IsDeleted
                    && !i.IsArchived
                    && ((i.ContactNo != null && (i.ContactNo == formattedContactNo))
                            ||
                            (i.AlternateContactNo != null && (i.AlternateContactNo == formattedContactNo))
                       ));
            }
        }
    }
    public class CheckParentLeadByContactNoSpec : Specification<Domain.Entities.Lead>
    {
        public CheckParentLeadByContactNoSpec(string contactNo, string countryCode)
        {
            string formattedContactNo = contactNo.StartsWith(countryCode) ? contactNo.Substring(countryCode.Length) : contactNo;

            Query.Where(i => !i.IsDeleted
                && !i.IsArchived && i.RootId == null
                && ((i.ContactNo != null && (i.ContactNo.Contains(contactNo) || i.ContactNo.Contains(formattedContactNo)))
                        ||
                        (i.AlternateContactNo != null && (i.AlternateContactNo.Contains(contactNo) || i.AlternateContactNo.Contains(formattedContactNo)))
                   ));
        }
        public CheckParentLeadByContactNoSpec(List<string> contactNos, List<string>? alternateNos)
        {
            Query.Where(i => !i.IsDeleted && i.RootId == null
                && !i.IsArchived
                && ((i.ContactNo != null && (contactNos.Contains(i.ContactNo))) || (i.AlternateContactNo != null && (alternateNos != null && alternateNos.Any() && alternateNos.Contains(i.AlternateContactNo))) || (i.ContactNo != null && (alternateNos != null && alternateNos.Any() && alternateNos.Contains(i.ContactNo))) || (i.AlternateContactNo != null && (contactNos.Contains(i.AlternateContactNo)))));
        }

    }
}
