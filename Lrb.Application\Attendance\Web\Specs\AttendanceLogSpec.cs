﻿using Lrb.Application.Attendance.Web.Dtos;
using Lrb.Application.Attendance.Web.Requests;
using Lrb.Application.Reports.Web;

namespace Lrb.Application.Attendance.Web.Specs
{
    public class AttendanceLogSpec : Specification<Domain.Entities.AttendanceLog>
    {
        public AttendanceLogSpec(GetAttendanceLogsRequest filter, DateTime fromDate, DateTime toDate)
        {
            Query.Where(i => !i.IsDeleted);
            if(filter.UserIds?.Any() ?? false)
            {
                Query.Where(i => filter.UserIds.Contains(i.UserId));
            }
            Query.Where(i => (i.ClockInTime >= fromDate && i.ClockInTime <= toDate)
                                                               || (i.ClockOutTime >= fromDate && i.ClockOutTime <= toDate));
        }
        public AttendanceLogSpec(ExportAttendanceDto filter, DateTime fromDate, DateTime toDate)
        {
            Query.Where(i => !i.IsDeleted && i.IsPunchInLocation != true);
            if (filter.UserIds?.Any() ?? false)
            {
                Query.Where(i => filter.UserIds.Contains(i.UserId));
            }
            Query.Where(i => (i.ClockInTime >= fromDate && i.ClockInTime <= toDate)
                                                               || (i.ClockOutTime >= fromDate && i.ClockOutTime <= toDate));
        }
        public AttendanceLogSpec(DateTime fromDate, DateTime toDate)
        {
            Query.Where(i => !i.IsDeleted && i.IsPunchInLocation != true);
            Query.Where(i => (i.ClockInTime >= fromDate && i.ClockInTime <= toDate)
                                                               || (i.ClockOutTime >= fromDate && i.ClockOutTime <= toDate));
        }
    }



    public class AttendanceLogByReporteeUserSpec : Specification<Domain.Entities.AttendanceLog>
    {
        public AttendanceLogByReporteeUserSpec(GetAttendanceLogsByReporteeUserRequest filter, DateTime fromDate, DateTime toDate)
        {
            Query.Where(i => !i.IsDeleted && i.IsPunchInLocation != true);
            if (filter.UserIds?.Any() ?? false)
            {
                Query.Where(i => filter.UserIds.Contains(i.UserId));
            }
            Query.Where(i => (i.ClockInTime >= fromDate && i.ClockInTime <= toDate)
                                                               || (i.ClockOutTime >= fromDate && i.ClockOutTime <= toDate));
        }
        public AttendanceLogByReporteeUserSpec(ExportAttendanceDto filter, DateTime fromDate, DateTime toDate)
        {
            Query.Where(i => !i.IsDeleted && i.IsPunchInLocation != true);
            if (filter.UserIds?.Any() ?? false)
            {
                Query.Where(i => filter.UserIds.Contains(i.UserId));
            }
            Query.Where(i => (i.ClockInTime >= fromDate && i.ClockInTime <= toDate)
                                                               || (i.ClockOutTime >= fromDate && i.ClockOutTime <= toDate));
        }
        public AttendanceLogByReporteeUserSpec(DateTime fromDate, DateTime toDate)
        {
            Query.Where(i => !i.IsDeleted && i.IsPunchInLocation != true);
            Query.Where(i => (i.ClockInTime >= fromDate && i.ClockInTime <= toDate)
                                                               || (i.ClockOutTime >= fromDate && i.ClockOutTime <= toDate));
        }
    }
}
