namespace Lrb.Application.Identity.Tokens;

public record DeviceInfo(string Username, string Password, string? DeviceModel, string? DeviceUDID, string? DeviceName, Platform? PlatForm, Guid? LoginId);
public record TokenRequest(string Username, string Password, string? DeviceModel, string? DeviceUDID, string? DeviceName, Platform? PlatForm, Guid? LoginId) : DeviceInfo(Username, Password, DeviceModel, DeviceUDID,  DeviceName, PlatForm, LoginId);

public class TokenRequestValidator : CustomValidator<TokenRequest>
{
    public TokenRequestValidator()
    {
        //RuleFor(p => p.Email).Cascade(CascadeMode.Stop)
        //    .NotEmpty()
        //    .EmailAddress()
        //        .WithMessage("Invalid Email Address.");

        RuleFor(p => p.Username).Cascade(CascadeMode.Stop)
            .NotEmpty();

        RuleFor(p => p.Password).Cascade(CascadeMode.Stop)
            .NotEmpty();
    }
}


