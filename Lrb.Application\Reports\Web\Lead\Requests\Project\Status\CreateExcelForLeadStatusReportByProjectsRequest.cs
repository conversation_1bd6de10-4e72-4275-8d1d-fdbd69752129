﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Utils;
using Lrb.Shared.Extensions;
using Newtonsoft.Json;

namespace Lrb.Application.Reports.Web
{
    public class CreateExcelForLeadStatusReportByProjectsRequest : IRequest<Response<string>>
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public DateType? DateType { get; set; }
        public List<string>? Projects { get; set; }
        public bool IsWithTeam { get; set; }
        public List<Guid>? UserIds { get; set; }
        public string? SearchText { get; set; }
        public List<LeadSource>? Sources { get; set; }
        public List<string>? AgencyNames { get; set; }
        public List<string>? SubSources { get; set; }
        public ReportPermission? ExportPermission { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = int.MaxValue;
        public List<string>? Localites { get; set; }
        public List<string>? States { get; set; }
        public List<string>? Cities { get; set; }
        public DateTime? ToDateForProject { get; set; }
        public DateTime? FromDateForProject { get; set; }
        public string? TimeZoneId { get; set; }
        public TimeSpan BaseUTcOffset { get; set; }
    }
    public class CreateExcelForLeadStatusReportByProjectsRequestHandler : IRequestHandler<CreateExcelForLeadStatusReportByProjectsRequest, Response<string>>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly IBlobStorageService _blobStorageService;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<ExportReportsTracker> _exportReportsTrackerRepo;
        public const string TYPE = "statusreportbyproject";
        public CreateExcelForLeadStatusReportByProjectsRequestHandler(IDapperRepository dapperRepository,
            IBlobStorageService blobStorageService,
            ICurrentUser currentUser,
            IRepositoryWithEvents<ExportReportsTracker> exportReportsTrackerRepo)
        {
            _dapperRepository = dapperRepository;
            _blobStorageService = blobStorageService;
            _currentUser = currentUser;
            _exportReportsTrackerRepo = exportReportsTrackerRepo;
        }
        public async Task<Response<string>> Handle(CreateExcelForLeadStatusReportByProjectsRequest request, CancellationToken cancellationToken)
        {
            ExportReportsTracker tracker = new();
            var tenantId = _currentUser.GetTenant();
            var userId = _currentUser.GetUserId();
            List<Guid> teamUserIds = new();
            List<Guid> permittedUserIds = new();
            var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
            if (isAdmin)
            {
                permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
            }
            else if (request.ExportPermission != null)
            {
                switch (request.ExportPermission)
                {
                    case ReportPermission.All:
                        permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        break;
                    case ReportPermission.Reportees:
                        permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                        break;
                }
            }
            if (request?.UserIds?.Any() ?? false)
            {
                if (request?.IsWithTeam ?? false)
                {
                    teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                }
                else
                {
                    teamUserIds = request?.UserIds ?? new List<Guid>();
                }
            }
            else
            {
                if (!isAdmin)
                {
                    //request.UserIds = new List<Guid>() { userId };
                    //teamUserIds = (await _dapperRepository.GetSubordinateIdsAsync(request.UserIds, tenantId ?? string.Empty)).ToList();

                    teamUserIds = permittedUserIds;
                }
            }
            if (teamUserIds.Any())
            {
                teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
            }
            else
            {
                teamUserIds = permittedUserIds;
            }
            List<ProjectReportV2Dto> leadsReportByProjects = new List<ProjectReportV2Dto>();
            List<ProjectReportDto> newReportByProjects = new List<ProjectReportDto>();
            request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
            request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
            var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<ProjectsReportDto>("LeadratBlack", "GetLeadReportByProject", new
            {
                fromdate = request.FromDate,
                todate = request.ToDate,
                datetype = request.DateType,
                tenantid = tenantId,
                userids = teamUserIds,
                sources = request?.Sources?.ConvertAll(i => (int)i),
                projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                agencynames = request?.AgencyNames?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                pagesize = request.PageSize,
                pagenumber = request.PageNumber,
                localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                states = request?.States?.ConvertAll<string>(i => i.LrbNormalize()),
                todateforproject = request?.ToDateForProject,
                fromdateforproject = request?.FromDateForProject

            })).ToList();
            res.ForEach(i => leadsReportByProjects.Add(JsonConvert.DeserializeObject<ProjectReportV2Dto>(i.Report ?? string.Empty) ?? new ProjectReportV2Dto()));
            var groupedResult = leadsReportByProjects.GroupBy(i => i?.Project ?? new Projects()).ToDictionary(i => i.Key, j => j.SelectMany(i => i?.Status).ToList());
            foreach (var group in groupedResult)
            {
                newReportByProjects.Add(new()
                {
                    ProjectId = group.Key.Id,
                    ProjectTitle = group.Key.Name,
                    AllCount = group.Value?.Sum(i => i.Count) ?? 0,
                    ActiveCount = group.Value?.Where(i => i.BaseStatus != "dropped" && i.BaseStatus != "not_interested" && i.BaseStatus != "booked" && i.BaseStatus != "booking_cancel").Sum(i => i.Count) ?? 0,
                    MeetingDoneCount = group.Value?.Sum(i => i.MeetingDoneCount) ?? 0,
                    OverdueCount = group.Value?.Sum(i => i.OverdueCount) ?? 0,
                    MeetingNotDoneCount = group.Value?.Sum(i => i.MeetingNotDoneCount) ?? 0,
                    SiteVisitDoneCount = group.Value?.Sum(i => i.SiteVisitDoneCount) ?? 0,
                    SiteVisitNotDoneCount = group.Value?.Sum(i => i.SiteVisitNotDoneCount) ?? 0,
                    CallbackCount = group.Value?.Where(i => i.BaseStatus == "callback" && i.SubStatus != "callback")?.Sum(i => i.Count) ?? 0,
                    BookedCount = group.Value?.Where(i => i.BaseStatus == "booked")?.Sum(i => i.Count) ?? 0,
                    NewCount = group.Value?.FirstOrDefault(i => i.BaseStatus == "new")?.Count ?? 0,
                    DroppedCount = group.Value?.Where(i => i.BaseStatus == "dropped" && i.SubStatus != "dropped")?.Sum(i => i.Count) ?? 0,
                    MeetingScheduledCount = group.Value?.Where(i => i.BaseStatus == "meeting_scheduled" && i.SubStatus != "meeting_scheduled")?.Sum(i => i.Count) ?? 0,
                    SiteVisitScheduledCount = group.Value?.Where(i => i.BaseStatus == "site_visit_scheduled" && i.SubStatus != "site_visit_scheduled")?.Sum(i => i.Count) ?? 0,
                    NotInterestedCount = group.Value?.Where(i => i.BaseStatus == "not_interested" && i.SubStatus != "not_interested")?.Sum(i => i.Count) ?? 0,
                    PendingCount = group.Value?.FirstOrDefault(i => i.BaseStatus == "pending")?.Count ?? 0,
                    MeetingDoneUniqueCount = group.Value?.Sum(i => i.MeetingDoneUniqueCount) ?? 0,
                    MeetingNotDoneUniqueCount = group.Value?.Sum(i => i.MeetingNotDoneUniqueCount) ?? 0,
                    SiteVisitDoneUniqueCount = group.Value?.Sum(i => i.SiteVisitDoneUniqueCount) ?? 0,
                    SiteVisitNotDoneUniqueCount = group.Value?.Sum(i => i.SiteVisitNotDoneUniqueCount) ?? 0,
                    BookingCancelCount = group.Value?.Where(i => i.BaseStatus == "booking_cancel")?.Sum(i => i.Count) ?? 0,
                    ExpressionOfInterestLeadCount = group.Value?.Where(i => i.BaseStatus == "expression_of_interest")?.Sum(i => i.Count) ?? 0
                });
            }
            var fileBytes = ExcelHelper.CreateExcelFromList(newReportByProjects, new List<string>(), new List<string>() { "ProjectId" },request.TimeZoneId,request.BaseUTcOffset).ToArray();
            var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", $"Reports/{tenantId ?? "Default"}", $"Report-{DateTime.Now}.xlsx", fileBytes);
            var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
            tracker.Count = res.Count();
            tracker.Type = TYPE;
            tracker.Request = JsonConvert.SerializeObject(request);
            tracker.S3BucketKey = presignedUrl;
            await _exportReportsTrackerRepo.AddAsync(tracker, cancellationToken);
            return new(presignedUrl, null);
        }
    }
}
