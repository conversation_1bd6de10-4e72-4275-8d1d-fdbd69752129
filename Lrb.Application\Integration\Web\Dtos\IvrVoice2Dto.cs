﻿namespace Lrb.Application.Integration.Web.Dtos
{
    public class IvrVoice2Dto : IDto
    {
        public string? campaign_id { get; set; }
        public string? user_id { get; set; }
        public string? cli { get; set; }
        public string? mobile { get; set; }
        public string? answer_status { get; set; }
        public string? dtmf { get; set; }
        public string? call_sid { get; set; }
        public string? start_time { get; set; }
        public string? answer_time { get; set; }
        public string? end_time { get; set; }
        public string? billing_duration { get; set; }
        public string? hangup_cause { get; set; }
        public string? patch_duration { get; set; }
        public string? bridge_ring_duration { get; set; }
        public string? campaign_name { get; set; }
        public string? retry_count { get; set; }
        public string? agent_number { get; set; }
    }
}
