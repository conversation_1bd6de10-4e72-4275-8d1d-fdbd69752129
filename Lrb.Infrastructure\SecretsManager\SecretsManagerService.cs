﻿using Amazon;
using Amazon.SecretsManager;
using Amazon.SecretsManager.Model;
using Lrb.Application.Common.SecretsManager;
using Lrb.Infrastructure.BlobStorage;
using Microsoft.Extensions.Options;

namespace Lrb.Infrastructure.SecretsManager
{
    public class SecretsManagerService : ISecretsManagerService
    {
        private readonly AwsSecretManagerSettings _settings;

        public SecretsManagerService(IOptions<AwsSecretManagerSettings> awsOptions)
        {
            _settings = awsOptions.Value;
        }
        public async Task<string> GetSecretAsync(string secretName)
        {
            string secret = string.Empty;
            try
            {
                IAmazonSecretsManager client = new AmazonSecretsManagerClient(_settings.AWSAccessToken, _settings.AWSSecret, RegionEndpoint.GetBySystemName(_settings.Region));
                GetSecretValueRequest request = new GetSecretValueRequest();
                request.SecretId = secretName;
                request.VersionStage = "AWSCURRENT"; // VersionStage defaults to AWSCURRENT if unspecified.

                GetSecretValueResponse response = null;

                // In this sample we only handle the specific exceptions for the 'GetSecretValue' API.
                // See https://docs.aws.amazon.com/secretsmanager/latest/apireference/API_GetSecretValue.html
                // We rethrow the exception by default.

                try
                {
                    response = await client.GetSecretValueAsync(request);
                }
                catch (DecryptionFailureException e)
                {
                    // Secrets Manager can't decrypt the protected secret text using the provided KMS key.
                    // Deal with the exception here, and/or rethrow at your discretion.
                    throw;
                }
                catch (InternalServiceErrorException e)
                {
                    // An error occurred on the server side.
                    // Deal with the exception here, and/or rethrow at your discretion.
                    throw;
                }
                catch (InvalidParameterException e)
                {
                    // You provided an invalid value for a parameter.
                    // Deal with the exception here, and/or rethrow at your discretion
                    throw;
                }
                catch (InvalidRequestException e)
                {
                    // You provided a parameter value that is not valid for the current state of the resource.
                    // Deal with the exception here, and/or rethrow at your discretion.
                    throw;
                }
                catch (ResourceNotFoundException e)
                {
                    // We can't find the resource that you asked for.
                    // Deal with the exception here, and/or rethrow at your discretion.
                    throw;
                }
                catch (System.AggregateException ae)
                {
                    // More than one of the above exceptions were triggered.
                    // Deal with the exception here, and/or rethrow at your discretion.
                    throw;
                }

                // Decrypts secret using the associated KMS key.
                // Depending on whether the secret is a string or binary, one of these fields will be populated.
                if (response.SecretString != null)
                {
                    secret = response.SecretString;
                }
                else
                {
                    MemoryStream memoryStream = response.SecretBinary;
                    StreamReader reader = new StreamReader(memoryStream);
                    secret = System.Text.Encoding.UTF8.GetString(Convert.FromBase64String(reader.ReadToEnd()));
                }
                return secret;
            }
            catch
            {
                return secret;
            }
        }
    }
}
