﻿using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;
using Serilog;
using System.Collections.Concurrent;

namespace Lrb.Application.Lead.Web.Requests
{
    public class GetAllLeadCountsRequest : GetAllLeadsParameters, IRequest<Response<LeadCountDto>>
    {
    }
    public class GetAllLeadsOnlyRequestHandler : IRequestHandler<GetAllLeadCountsRequest, Response<LeadCountDto>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        private readonly IUserService _userService;
        private readonly ICurrentUser _currentUser;
        private readonly ILogger _logger;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectsRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Property> _propertyRepo;
        private readonly IDapperRepository _dapperRepository;
        private readonly IRepositoryWithEvents<Domain.Entities.LeadHistory> _leadHistoryRepo;
        private readonly IRepositoryWithEvents<Address> _addressRepo;
        private readonly ILeadRepository _efLeadRepository;
        private readonly ILeadHistoryRepository _leadHistoryRepository;
        //private readonly IRepositoryWithEvents<MasterLeadStatus> _leadStatusRepo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customLeadStatusRepo;
        public GetAllLeadsOnlyRequestHandler(
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepo,
            IUserService userService,
            ICurrentUser currentUser,
            Serilog.ILogger logger,
            IRepositoryWithEvents<Lrb.Domain.Entities.Project> projectsRepo,
            IRepositoryWithEvents<Domain.Entities.Property> propertyRepo,
            IDapperRepository dapperRepository,
            IRepositoryWithEvents<LeadHistory> leadHistoryRepo,
            IRepositoryWithEvents<Address> addressRepo,
            ILeadRepository efLeadRepository,
            ILeadHistoryRepository leadHistoryRepository,
            //IRepositoryWithEvents<MasterLeadStatus> leadStatusRepo,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<CustomMasterLeadStatus> customLeadStatusRepo)
        {
            _leadRepo = leadRepo;
            _userService = userService;
            _currentUser = currentUser;
            _logger = logger;
            _projectsRepo = projectsRepo;
            _propertyRepo = propertyRepo;
            _dapperRepository = dapperRepository;
            _leadHistoryRepo = leadHistoryRepo;
            _addressRepo = addressRepo;
            _efLeadRepository = efLeadRepository;
            _leadHistoryRepository = leadHistoryRepository;
            //_leadStatusRepo = leadStatusRepo;
            _leadRepositoryAsync = leadRepositoryAsync;
            _customLeadStatusRepo = customLeadStatusRepo;
        }
        public async Task<Response<LeadCountDto>> Handle(GetAllLeadCountsRequest request, CancellationToken cancellationToken)
        {
            try
            {
                if (request?.LeadTags?.Any() ?? false)
                {
                    request.TagFilterDto = GetLeadTagFilter(request);
                    request.LeadTags = null;
                }
                var userId = _currentUser.GetUserId();
                var tenantId = _currentUser.GetTenant();
                var isAdmin = _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty).Result;
                List<Guid> leadHistoryIds = new();
                List<Guid> subIds = new();
                try
                {
                    if (request?.AssignTo?.Any() ?? false)
                    {
                        if (request?.IsWithTeam ?? false)
                        {
                            subIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.AssignTo, tenantId ?? string.Empty)).ToList();
                        }
                        else
                        {
                            subIds = request?.AssignTo ?? new List<Guid>();
                        }
                    }
                    else
                    {
                        subIds = (await _dapperRepository.GetSubordinateIdsAsync(userId, tenantId ?? string.Empty, request?.CanAccessAllLeads))?.ToList() ?? new();
                    }
                }
                catch (Exception ex)
                {
                    throw ex;
                }
                if (request?.SubStatusIds?.Any() ?? false)
                {
                    var statuses = await _customLeadStatusRepo.ListAsync(new LeadStatusSpec(request.SubStatusIds), cancellationToken);
                    request?.StatusIds?.RemoveAll(l => statuses.Select(i => i.BaseId).Contains(l));
                }
                var filterType = request.FilterType;
                LeadCountDto leadsCount = new();
                var filterTypeCounts = new ConcurrentDictionary<LeadFilterTypeWeb, int>();
                var filterTypes = new ConcurrentBag<LeadFilterTypeWeb>()
            {
                LeadFilterTypeWeb.All , LeadFilterTypeWeb.New, LeadFilterTypeWeb.Today, LeadFilterTypeWeb.Overdue, LeadFilterTypeWeb.NotInterested, LeadFilterTypeWeb.Dropped, LeadFilterTypeWeb.Escalated, LeadFilterTypeWeb.Pending, LeadFilterTypeWeb.Booked, LeadFilterTypeWeb.Upcoming
            };
                var lockObject = new object();
                Parallel.ForEach(filterTypes, filterType =>
                {
                    var req = request.Adapt<GetAllLeadsRequest>();
                    req.FilterType = filterType;
                    filterTypeCounts.TryAdd(filterType, _efLeadRepository.GetLeadsCountForWebAsync(req, subIds, userId, isAdmin: isAdmin).Result);
                });
                leadsCount.AllLeadsCount = filterTypeCounts[LeadFilterTypeWeb.All];
                leadsCount.NewLeadsCount = filterTypeCounts[LeadFilterTypeWeb.New];
                leadsCount.TodayLeadsCount = filterTypeCounts[LeadFilterTypeWeb.Today];
                leadsCount.OverdueLeadsCount = filterTypeCounts[LeadFilterTypeWeb.Overdue];
                leadsCount.NotInterestedLeadsCount = filterTypeCounts[LeadFilterTypeWeb.NotInterested];
                leadsCount.DroppedLeadsCount = filterTypeCounts[LeadFilterTypeWeb.Dropped];
                leadsCount.EscalatedLeadsCount = filterTypeCounts[LeadFilterTypeWeb.Escalated];
                leadsCount.PendingLeadsCount = filterTypeCounts[LeadFilterTypeWeb.Pending];
                leadsCount.BookedLeadsCount = filterTypeCounts[LeadFilterTypeWeb.Booked];
                leadsCount.UpcomingLeadsCount = filterTypeCounts[LeadFilterTypeWeb.Upcoming];

                request.FilterType = filterType;

                var leadTagsFilters = request.LeadTags;
                var leadTagFilterCounts = new ConcurrentDictionary<LeadTagEnum, int>();
                var leadtags = new ConcurrentBag<LeadTagEnum>() { LeadTagEnum.IsEscalated, LeadTagEnum.IsHot, LeadTagEnum.IsWarmLead, LeadTagEnum.IsColdLead, LeadTagEnum.IsAboutToConvert, LeadTagEnum.IsHighlighted };
                var lockObject2 = new object();
                Parallel.ForEach(leadtags, tag =>
                {
                    var req = request.Adapt<GetAllLeadsRequest>();
                    req.TagFilterDto = null;
                    req.LeadTags = new List<LeadTagEnum> { tag };
                    leadTagFilterCounts.TryAdd(tag, _efLeadRepository.GetLeadsCountForWebAsync(req, subIds, userId, isAdmin: isAdmin).Result);
                });
                leadsCount.EscalatedLeadsFlagCount = leadTagFilterCounts[LeadTagEnum.IsEscalated];
                leadsCount.HotLeadsFlagCount = leadTagFilterCounts[LeadTagEnum.IsHot];
                leadsCount.WarmLeadsFlagCount = leadTagFilterCounts[LeadTagEnum.IsWarmLead];
                leadsCount.ColdLeadsFlagCount = leadTagFilterCounts[LeadTagEnum.IsColdLead];
                leadsCount.AboutToConvertLeadsFlagCount = leadTagFilterCounts[LeadTagEnum.IsAboutToConvert];
                leadsCount.HighlightedLeadsFlagCount = leadTagFilterCounts[LeadTagEnum.IsHighlighted];

                request.LeadTags = leadTagsFilters;
                request.TagFilterDto = GetLeadTagFilter(request);
                var unAssignedRequest = request.Adapt<GetAllLeadsRequest>();
                unAssignedRequest.LeadVisibility = BaseLeadVisibility.UnassignLead;
                leadsCount.UnassignLeadsCount = await _efLeadRepository.GetLeadsCountForWebAsync(unAssignedRequest, subIds, userId, leadHistoryIds, isAdmin: isAdmin);
                var deletedRequest = request.Adapt<GetAllLeadsRequest>();
                deletedRequest.LeadVisibility = BaseLeadVisibility.DeletedLeads;
                leadsCount.DeletedLeadsCount = await _efLeadRepository.GetLeadsCountForWebAsync(deletedRequest, subIds, userId, leadHistoryIds, isAdmin: isAdmin);
                var reEnquiredRequest = request.Adapt<GetAllLeadsRequest>();
                reEnquiredRequest.LeadVisibility = BaseLeadVisibility.ReEnquired;
                leadsCount.ReEnquiredLeadsCount = await _efLeadRepository.GetLeadsCountForWebAsync(reEnquiredRequest, subIds, userId, leadHistoryIds, isAdmin: isAdmin);
                return new(leadsCount);
            }
            catch (Exception ex)
            {
                return new() { Message = ex.Message };
            }
        }
        public LeadTagFilterDto? GetLeadTagFilter(GetAllLeadCountsRequest request)
        {
            LeadTagFilterDto? tagFilterDto = null;
            if (request.LeadTags?.Any() ?? false)
            {
                foreach (var tag in request.LeadTags)
                    switch (tag)
                    {
                        case LeadTagEnum.IsHot:
                            tagFilterDto.IsHotLead = true;
                            break;
                        case LeadTagEnum.IsAboutToConvert:
                            tagFilterDto.IsAboutToConvert = true;
                            break;
                        case LeadTagEnum.IsEscalated:
                            tagFilterDto.IsEscalated = true;
                            break;
                        case LeadTagEnum.IsIntegrationLead:
                            tagFilterDto.IsIntegrationLead = true;
                            break;
                        case LeadTagEnum.IsHighlighted:
                            tagFilterDto.IsHighlighted = true;
                            break;
                        case LeadTagEnum.IsWarmLead:
                            tagFilterDto.IsWarmLead = true;
                            break;
                        case LeadTagEnum.IsColdLead:
                            tagFilterDto.IsColdLead = true;
                            break;
                    }
            }
            return tagFilterDto;
        }
    }
}
