﻿using LrbIntegrationBackgroundJobs.Repositories;
using LrbIntegrationBackgroundJobs.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.IO;

namespace LrbIntegrationBackgroundJobs
{
    public class Startup
    {
        private readonly IConfigurationRoot Configuration;

        public Startup(string env)
        {
            Configuration = new ConfigurationBuilder() // ConfigurationBuilder() method requires Microsoft.Extensions.Configuration NuGet package
                .SetBasePath(Directory.GetCurrentDirectory())  // SetBasePath() method requires Microsoft.Extensions.Configuration.FileExtensions NuGet package
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true) // AddJsonFile() method requires Microsoft.Extensions.Configuration.Json NuGet package
                .AddJsonFile($"appsettings.{env}.json", optional: true, reloadOnChange: true)
                .AddEnvironmentVariables() // AddEnvironmentVariables() method requires Microsoft.Extensions.Configuration.EnvironmentVariables NuGet package
                .Build();
        }

        public IServiceProvider ConfigureServices()
        {
            var services = new ServiceCollection(); // ServiceCollection require Microsoft.Extensions.DependencyInjection NuGet package

            ConfigureLoggingAndConfigurations(services);

            ConfigureApplicationServices(services);

            IServiceProvider provider = services.BuildServiceProvider();

            return provider;
        }


        private void ConfigureLoggingAndConfigurations(ServiceCollection services)
        {

            // Add configuration service
            services.AddSingleton<IConfiguration>(Configuration);

            // Add logging service
            services.AddLogging(loggingBuilder =>  // AddLogging() requires Microsoft.Extensions.Logging NuGet package
            {
                loggingBuilder.ClearProviders();
                loggingBuilder.AddConsole();
                // AddConsole() requires Microsoft.Extensions.Logging.Console NuGet package
            });

            services.AddOptions<DatabaseSettings>()
                .BindConfiguration(nameof(DatabaseSettings));

            services.AddOptions<WorkingEndPoint>()
                .BindConfiguration(nameof(WorkingEndPoint));
        }

        private void ConfigureApplicationServices(ServiceCollection services)
        {
            services.AddTransient<ITenantIndependentRepository, TenantIndependentRepository>();

            services.AddTransient<IDbRepositoryAsync, DbRepositoryAsync>();

            services.AddTransient<IJustLeadService, JustLeadService>();

            services.AddTransient<ICommonFloorService, CommonFloorService>();

            services.AddTransient<ICosmosService, CosmosService>();

            services.AddTransient<ILrbAPIService, LrbAPIService>();

            services.AddTransient<IFacebookDataRepositoryAsync, FacebookDataRepositoryAsync>();

            services.AddTransient<IAsputilityService, AsputilityService>();

            services.AddTransient<IBayutService, BayutService>();

            services.AddTransient<IPropertyFinderService, PropertyFinderService>();

            services.AddTransient<IPropertyListingRepository, PropertyListingRepository>();

            services.AddTransient<IDubizzleService, DubizzleService>();

            services.AddTransient<IGmailService, GmailService>();

            services.AddTransient<IGmailRepository, GmailRepository>();

            services.AddTransient<IPropertyFinderServiceV2, PropertyFinderServiceV2>();
        }
    }
}
