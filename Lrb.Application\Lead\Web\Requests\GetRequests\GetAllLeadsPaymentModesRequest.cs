﻿namespace Lrb.Application.Lead.Web.Requests.GetRequests
{
    public class GetAllLeadsPaymentModesRequest : IRequest<Response<Dictionary<PaymentMode, string>>>
    {
    }
    public class GetAllLeadsPaymentModesRequestHandler : IRequestHandler<GetAllLeadsPaymentModesRequest, Response<Dictionary<PaymentMode, string>>>
    {
        public async Task<Response<Dictionary<PaymentMode, string>>> Handle(GetAllLeadsPaymentModesRequest request, CancellationToken cancellationToken)
        {
            var paymentModes = Enum.GetValues<PaymentMode>().Cast<PaymentMode>().Where(mode => mode != PaymentMode.None)
                 .Select(mode =>
                 {
                     return Enum.TryParse<PaymentMode>(mode.ToString(), out var parsedMode)
                         ? new KeyValuePair<PaymentMode, string>(parsedMode, parsedMode.ToString())
                         : default;
                 }).Where(kv => kv.Key != default).ToDictionary(kv => kv.Key, kv => kv.Value);
            return new Response<Dictionary<PaymentMode, string>>(paymentModes);
        }
    }
}
