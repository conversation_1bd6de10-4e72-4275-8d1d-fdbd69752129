﻿using Lrb.Application.Identity.Users;
using Lrb.Application.UserDetails.Web;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.ChannelPartner.Web.Request
{
    public class GetAllChannelPartnerCreatedByUsersRequest : IRequest<Response<List<ReportUserDto>>>
    {

    }
    public class GetAllChannelPartnerCreatedByUsersRequestHandler : IRequestHandler<GetAllChannelPartnerCreatedByUsersRequest, Response<List<ReportUserDto>>>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        private readonly IUserService _userService;
        public GetAllChannelPartnerCreatedByUsersRequestHandler(IDapperRepository dapperRepository,
            ICurrentUser currentUser,
            IUserService userService)
        {
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
            _userService = userService;
        }
        public async Task<Response<List<ReportUserDto>>> Handle(GetAllChannelPartnerCreatedByUsersRequest request, CancellationToken cancellationToken)
        {
            var userIds = (await _dapperRepository.GetAllCreatedByIdsOfChannelPartnerAsync(_currentUser.GetTenant() ?? string.Empty));
            var stringuserIds = userIds.Select(i => i.ToString()).ToList();
            if (stringuserIds?.Any() ?? false)
            {
                var users = await _userService.GetListOfUsersByIdsAsync(stringuserIds, cancellationToken);
                List<ReportUserDto> viewUserDtos = users.Adapt<List<ReportUserDto>>();
                return new(viewUserDtos);
            }
            return new();
        }
    }

}
