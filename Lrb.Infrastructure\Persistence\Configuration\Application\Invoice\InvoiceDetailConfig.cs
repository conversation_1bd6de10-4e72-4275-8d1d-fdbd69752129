﻿using Finbuckle.MultiTenant.EntityFrameworkCore;
using Lrb.Domain.Entities.Invoice;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Lrb.Infrastructure.Persistence.Configuration.Application.Invoice
{
    public class InvoiceDetailConfig : IEntityTypeConfiguration<Lrb.Domain.Entities.Invoice.InvoiceDetail>
    {
        public void Configure(EntityTypeBuilder<InvoiceDetail> builder)
        {
            builder.IsMultiTenant();
        }
    }
}