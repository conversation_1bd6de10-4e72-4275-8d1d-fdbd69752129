﻿using Lrb.Application.Agency.Web;
using Lrb.Application.ChannelPartner.Web.Dtos;
using Lrb.Application.ChannelPartner.Web.Specs;
using Lrb.Application.Dashboard.Web;
using Lrb.Shared.Extensions;

namespace Lrb.Application.ChannelPartner.Web.Request
{

    public class GetChannelPartnersRequest : PaginationFilter, IRequest<PagedResponse<ViewChannelPartnerDto, string>>
    {
        public List<string>? SearchByName { get; set; }
        public string? SearchText { get; set; }
        public DateType? DateType { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<string>? Localites { get; set; }
        public List<string>? States { get; set; }
        public List<string>? Cities { get; set; }
        public List<string>? Location { get; set; }
        public string? LeadsMinCount { get; set; }
        public string? LeadsMaxCount { get; set; }
        public string? ProspectMinCount { get; set; }
        public string? ProspectMaxCount { get; set; }
        public List<Guid>? CreatedByIds { get; set; }
        public List<string>? Projects { get; set; }
    }

    public class GetChannelPartnersRequestHandler : IRequestHandler<GetChannelPartnersRequest, PagedResponse<ViewChannelPartnerDto, string>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.ChannelPartner> _channelPartnerRepo;
        private readonly ICurrentUser _currentUser;
        private readonly IDapperRepository _dapperRepository;
        public GetChannelPartnersRequestHandler(IRepositoryWithEvents<Domain.Entities.ChannelPartner> channelPartnerRepo, IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _channelPartnerRepo = channelPartnerRepo;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        public async Task<PagedResponse<ViewChannelPartnerDto, string>> Handle(GetChannelPartnersRequest request, CancellationToken cancellationToken)
        {
            var tenantId = _currentUser.GetTenant();
            var cpCount = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<ViewChannelPartnerDto>("LeadratBlack", "ChannelPartners_Affiliated_Count", new
            {
                tenant_id = tenantId,
                channelpartners = request.SearchByName?.Select(name => name.Replace(" ", "").ToLower()).ToList(),
                leads_min = request.LeadsMinCount,
                leads_max = request.LeadsMaxCount,
                prospects_min = request.ProspectMinCount,
                prospects_max = request.ProspectMaxCount
            }))?.ToList() ?? new List<ViewChannelPartnerDto>();
            var channelPartners = await _channelPartnerRepo.ListAsync(new ChannelPartnerAffiliatedCountFiltersSpec(request, cpCount.Select(a => a.Id).ToList()), cancellationToken);
            request.PageSize = int.MaxValue;
            var totalCount = await _channelPartnerRepo.CountAsync(new ChannelPartnerAffiliatedCountFiltersSpec(request, cpCount.Select(a => a.Id).ToList()), cancellationToken);
            var channelPartnersWithCount = cpCount
            .Where(channelPartner => channelPartners.Any(i => i.Id == channelPartner.Id))
            .Select(channelPartner =>
            {
                var originalLeadsCount = channelPartner.LeadsCount;
                var originalProspectCount = channelPartner.ProspectCount;
                var matchedExistingAgency = channelPartners.FirstOrDefault(i => i.Id == channelPartner.Id);
                if (matchedExistingAgency != null)
                {
                    matchedExistingAgency.Adapt(channelPartner);
                    channelPartner.LeadsCount = originalLeadsCount;
                    channelPartner.ProspectCount = originalProspectCount;
                }
                return channelPartner;
            }).ToList();
            return new(channelPartnersWithCount ?? new List<ViewChannelPartnerDto>(), totalCount);
        }
    }
}
