namespace Lrb.Infrastructure.RateLimiting.Models;

/// <summary>
/// Represents a request that has been queued due to rate limiting
/// </summary>
public class QueuedRequest
{
    /// <summary>
    /// Unique identifier for this queued request
    /// </summary>
    public string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// Client identifier (IP, user ID, etc.)
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// Time when the request was queued
    /// </summary>
    public DateTime QueuedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Time when the request should expire from the queue
    /// </summary>
    public DateTime ExpiresAt { get; set; }

    /// <summary>
    /// Task completion source to signal when the request can proceed
    /// </summary>
    public TaskCompletionSource<bool> CompletionSource { get; set; } = new();

    /// <summary>
    /// Cancellation token for the request
    /// </summary>
    public CancellationToken CancellationToken { get; set; }

    /// <summary>
    /// Whether the request has been processed or cancelled
    /// </summary>
    public bool IsCompleted => CompletionSource.Task.IsCompleted;

    /// <summary>
    /// Whether the request has expired
    /// </summary>
    public bool IsExpired => DateTime.UtcNow > ExpiresAt;

    /// <summary>
    /// Time the request has been waiting in the queue
    /// </summary>
    public TimeSpan WaitTime => DateTime.UtcNow - QueuedAt;

    /// <summary>
    /// Complete the request successfully
    /// </summary>
    public void Complete()
    {
        if (!IsCompleted)
        {
            CompletionSource.SetResult(true);
        }
    }

    /// <summary>
    /// Cancel the request
    /// </summary>
    public void Cancel()
    {
        if (!IsCompleted)
        {
            CompletionSource.SetCanceled();
        }
    }

    /// <summary>
    /// Fail the request with an exception
    /// </summary>
    public void Fail(Exception exception)
    {
        if (!IsCompleted)
        {
            CompletionSource.SetException(exception);
        }
    }
}