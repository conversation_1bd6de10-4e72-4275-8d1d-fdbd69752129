﻿using Lrb.Application.Utils;

namespace Lrb.Application.Dashboard.Web.Requests
{
    public class GetNewLeadTrackerRequest : GetAllDashboardParameterFilter, IRequest<Response<Dictionary<string, grouppedLeadtrackerDto>>>
    {

        public DateType? DateType { get; set; }
        public bool IsCustomDate { get; set; }
        public LeadFrequency? Frequency { get; set; }
        public bool? IsWithTeam { get; set; }
    }
    public class GetNewLeadTrackerRequestHander : IRequestHandler<GetNewLeadTrackerRequest, Response<Dictionary<string, grouppedLeadtrackerDto>>>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        public GetNewLeadTrackerRequestHander(IDapperRepository dapperRepository,
                                                  ICurrentUser currentUser)
        {
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        public async Task<Response<Dictionary<string, grouppedLeadtrackerDto>>> Handle(GetNewLeadTrackerRequest request, CancellationToken cancellationToken)
        {
            Guid currentUserId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            List<Guid>? filterIds = new();
            List<Guid>? teamUserIds = new();
            if (request.UserIds?.Any() ?? false)
            {
                filterIds.AddRange(request.UserIds);
                if (request.IsWithTeam ?? false)
                {
                    teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty))?.ToList() ?? new();
                    filterIds.AddRange(teamUserIds);
                }
            }
            else
            {
                switch (request.LeadVisibility)
                {
                    case LeadVisibility.Me:
                        filterIds = new() { currentUserId };
                        break;
                    case LeadVisibility.MyTeam:
                        filterIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { currentUserId }, tenantId))?.ToList() ?? new();
                        break;
                    case LeadVisibility.Organization:
                        filterIds = (await _dapperRepository.GetSubordinateIdsForDashboardAsync(new List<Guid>() { currentUserId }, tenantId))?.ToList() ?? new();
                        break;
                }
            }

            request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
            request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
            var Leadtrackerdata = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<LeadtrackerDto>("LeadratBlack", "func_dashboard_lead_tracker_2_new_1", new
            {
                from_date = request.FromDate,
                to_date = request.ToDate,
                user_ids = filterIds,
                date_type = request.DateType,
                tenant_id = tenantId
            }, 300));
            Dictionary<string, grouppedLeadtrackerDto> groupedCount = new Dictionary<string, grouppedLeadtrackerDto>();

            switch (request.Frequency)
            {
                case LeadFrequency.Hour:
                    groupedCount = GroupLeadsByHours(Leadtrackerdata);
                    break;
                case LeadFrequency.Day:
                    groupedCount = GroupLeadsByDay(Leadtrackerdata);
                    break;
                case LeadFrequency.Week:
                    groupedCount = GroupLeadsByWeek(Leadtrackerdata);
                    break;

                case LeadFrequency.Month:
                    groupedCount = GroupLeadsByMonth(Leadtrackerdata);
                    break;
                case LeadFrequency.Quarter:
                    groupedCount = GroupLeadsByQuarter(Leadtrackerdata);
                    break;
                case LeadFrequency.Year:
                    groupedCount = GroupLeadsByYear(Leadtrackerdata);
                    break;

                default:

                    groupedCount = GroupLeadsByHours(Leadtrackerdata);
                    break;
            }
            return new(groupedCount);
        }

        static Dictionary<string, grouppedLeadtrackerDto> GroupLeadsByDay(IEnumerable<LeadtrackerDto> Leadtrackerdata)
        {
            Func<DateTime, string> GetDayKey = dt => dt.Date.ToString("dd-MM-yyyy");

            var smsLeadDayMap = Leadtrackerdata
                .Where(lead => lead.SMSLead > 0)
                .GroupBy(lead => lead.LeadId)
                .ToDictionary(
                    g => g.Key,
                    g => GetDayKey(g.Min(x => x.CreatedOn))
                );

            var callLeadDayMap = Leadtrackerdata
                .Where(lead => lead.CallLead > 0)
                .GroupBy(lead => lead.LeadId)
                .ToDictionary(
                    g => g.Key,
                    g => GetDayKey(g.Min(x => x.CreatedOn))
                );

            var emailLeadDayMap = Leadtrackerdata
                .Where(lead => lead.EmailLead > 0)
                .GroupBy(lead => lead.LeadId)
                .ToDictionary(
                    g => g.Key,
                    g => GetDayKey(g.Min(x => x.CreatedOn))
                );

            var whatsappLeadDayMap = Leadtrackerdata
                .Where(lead => lead.WhatsAppLead > 0)
                .GroupBy(lead => lead.LeadId)
                .ToDictionary(
                    g => g.Key,
                    g => GetDayKey(g.Min(x => x.CreatedOn))
                );

            return Leadtrackerdata
                .GroupBy(lead => GetDayKey(lead.CreatedOn))
                .ToDictionary(
                    dayKey => dayKey.Key,
                    dayGroup =>
                    {
                        var key = dayGroup.Key;

                        return new grouppedLeadtrackerDto
                        {
                            SMSLeadUniqueCount = smsLeadDayMap.Count(kvp => kvp.Value == key),
                            CallLeadUniqueCount = callLeadDayMap.Count(kvp => kvp.Value == key),
                            EmailLeadUniqueCount = emailLeadDayMap.Count(kvp => kvp.Value == key),
                            WhatsAppLeadUniqueCount = whatsappLeadDayMap.Count(kvp => kvp.Value == key),

                            TotalLead = dayGroup.Select(lead => lead.LeadId).Distinct().Count(),

                            Call = dayGroup.Sum(lead => lead.CallEvent),
                            SMS = dayGroup.Sum(lead => lead.SMSEvent),
                            Email = dayGroup.Sum(lead => lead.EmailEvent),
                            WhatsApp = dayGroup.Sum(lead => lead.WhatsAppEvent),
                            TotalEvent = dayGroup.Sum(lead => lead.TotalEvent)
                        };
                    });
        }



        static Dictionary<string, grouppedLeadtrackerDto> GroupLeadsByWeek(IEnumerable<LeadtrackerDto> Leadtrackerdata)
        {
            Func<DateTime, string> GetWeekKey = dt => GetWeekStart(dt).ToString("dd-MM-yyyy");

            var smsLeadWeekMap = Leadtrackerdata
                .Where(lead => lead.SMSLead > 0)
                .GroupBy(lead => lead.LeadId)
                .ToDictionary(
                    g => g.Key,
                    g => GetWeekKey(g.Min(x => x.CreatedOn))
                );

            var callLeadWeekMap = Leadtrackerdata
                .Where(lead => lead.CallLead > 0)
                .GroupBy(lead => lead.LeadId)
                .ToDictionary(
                    g => g.Key,
                    g => GetWeekKey(g.Min(x => x.CreatedOn))
                );

            var emailLeadWeekMap = Leadtrackerdata
                .Where(lead => lead.EmailLead > 0)
                .GroupBy(lead => lead.LeadId)
                .ToDictionary(
                    g => g.Key,
                    g => GetWeekKey(g.Min(x => x.CreatedOn))
                );

            var whatsappLeadWeekMap = Leadtrackerdata
                .Where(lead => lead.WhatsAppLead > 0)
                .GroupBy(lead => lead.LeadId)
                .ToDictionary(
                    g => g.Key,
                    g => GetWeekKey(g.Min(x => x.CreatedOn))
                );

            return Leadtrackerdata
                .GroupBy(lead => GetWeekKey(lead.CreatedOn))
                .ToDictionary(
                    weekKey => weekKey.Key,
                    weekGroup =>
                    {
                        var key = weekGroup.Key;

                        return new grouppedLeadtrackerDto
                        {
                            SMSLeadUniqueCount = smsLeadWeekMap.Count(kvp => kvp.Value == key),
                            CallLeadUniqueCount = callLeadWeekMap.Count(kvp => kvp.Value == key),
                            EmailLeadUniqueCount = emailLeadWeekMap.Count(kvp => kvp.Value == key),
                            WhatsAppLeadUniqueCount = whatsappLeadWeekMap.Count(kvp => kvp.Value == key),

                            TotalLead = weekGroup.Select(lead => lead.LeadId).Distinct().Count(),

                            Call = weekGroup.Sum(lead => lead.CallEvent),
                            SMS = weekGroup.Sum(lead => lead.SMSEvent),
                            Email = weekGroup.Sum(lead => lead.EmailEvent),
                            WhatsApp = weekGroup.Sum(lead => lead.WhatsAppEvent),
                            TotalEvent = weekGroup.Sum(lead => lead.TotalEvent)
                        };
                    });
        }


        static DateTime GetWeekStart(DateTime date)
        {
            int daysToSubtract = (int)date.DayOfWeek;
            return date.Date.AddDays(-daysToSubtract);
        }

        static Dictionary<string, grouppedLeadtrackerDto> GroupLeadsByMonth(IEnumerable<LeadtrackerDto> Leadtrackerdata)
        {
            Func<DateTime, string> GetMonthKey = dt => $"{dt.Month:D2}-{dt.Year}";

            var smsLeadMonthMap = Leadtrackerdata
                .Where(lead => lead.SMSLead > 0)
                .GroupBy(lead => lead.LeadId)
                .ToDictionary(
                    g => g.Key,
                    g => GetMonthKey(g.Min(x => x.CreatedOn))
                );

            var callLeadMonthMap = Leadtrackerdata
                .Where(lead => lead.CallLead > 0)
                .GroupBy(lead => lead.LeadId)
                .ToDictionary(
                    g => g.Key,
                    g => GetMonthKey(g.Min(x => x.CreatedOn))
                );

            var emailLeadMonthMap = Leadtrackerdata
                .Where(lead => lead.EmailLead > 0)
                .GroupBy(lead => lead.LeadId)
                .ToDictionary(
                    g => g.Key,
                    g => GetMonthKey(g.Min(x => x.CreatedOn))
                );

            var whatsappLeadMonthMap = Leadtrackerdata
                .Where(lead => lead.WhatsAppLead > 0)
                .GroupBy(lead => lead.LeadId)
                .ToDictionary(
                    g => g.Key,
                    g => GetMonthKey(g.Min(x => x.CreatedOn))
                );

            return Leadtrackerdata
                .GroupBy(lead => new { lead.CreatedOn.Year, lead.CreatedOn.Month })
                .ToDictionary(
                    monthGroup =>
                    {
                        var key = monthGroup.Key;
                        return $"{key.Month:D2}-{key.Year}";
                    },
                    monthGroup =>
                    {
                        var monthKey = $"{monthGroup.Key.Month:D2}-{monthGroup.Key.Year}";

                        return new grouppedLeadtrackerDto
                        {
                            SMSLeadUniqueCount = smsLeadMonthMap.Count(kvp => kvp.Value == monthKey),
                            CallLeadUniqueCount = callLeadMonthMap.Count(kvp => kvp.Value == monthKey),
                            EmailLeadUniqueCount = emailLeadMonthMap.Count(kvp => kvp.Value == monthKey),
                            WhatsAppLeadUniqueCount = whatsappLeadMonthMap.Count(kvp => kvp.Value == monthKey),

                            TotalLead = monthGroup.Select(lead => lead.LeadId).Distinct().Count(),

                            Call = monthGroup.Sum(lead => lead.CallEvent),
                            SMS = monthGroup.Sum(lead => lead.SMSEvent),
                            Email = monthGroup.Sum(lead => lead.EmailEvent),
                            WhatsApp = monthGroup.Sum(lead => lead.WhatsAppEvent),
                            TotalEvent = monthGroup.Sum(lead => lead.TotalEvent)
                        };
                    });
        }






        static Dictionary<string, grouppedLeadtrackerDto> GroupLeadsByQuarter(IEnumerable<LeadtrackerDto> Leadtrackerdata)
        {
            Func<DateTime, string> GetQuarterKey = dt =>
            {
                int quarter = (dt.Month - 1) / 3 + 1;
                return $"Q{quarter} {dt.Year}";
            };

            var smsLeadQuarterMap = Leadtrackerdata
                .Where(lead => lead.SMSLead > 0)
                .GroupBy(lead => lead.LeadId)
                .ToDictionary(
                    g => g.Key,
                    g => GetQuarterKey(g.Min(x => x.CreatedOn))
                );

            var callLeadQuarterMap = Leadtrackerdata
                .Where(lead => lead.CallLead > 0)
                .GroupBy(lead => lead.LeadId)
                .ToDictionary(
                    g => g.Key,
                    g => GetQuarterKey(g.Min(x => x.CreatedOn))
                );

            var emailLeadQuarterMap = Leadtrackerdata
                .Where(lead => lead.EmailLead > 0)
                .GroupBy(lead => lead.LeadId)
                .ToDictionary(
                    g => g.Key,
                    g => GetQuarterKey(g.Min(x => x.CreatedOn))
                );

            var whatsappLeadQuarterMap = Leadtrackerdata
                .Where(lead => lead.WhatsAppLead > 0)
                .GroupBy(lead => lead.LeadId)
                .ToDictionary(
                    g => g.Key,
                    g => GetQuarterKey(g.Min(x => x.CreatedOn))
                );

            return Leadtrackerdata
                .GroupBy(lead => GetQuarterKey(lead.CreatedOn))
                .ToDictionary(
                    group => group.Key,
                    group =>
                    {
                        var quarterKey = group.Key;

                        return new grouppedLeadtrackerDto
                        {
                            SMSLeadUniqueCount = smsLeadQuarterMap.Count(kvp => kvp.Value == quarterKey),
                            CallLeadUniqueCount = callLeadQuarterMap.Count(kvp => kvp.Value == quarterKey),
                            EmailLeadUniqueCount = emailLeadQuarterMap.Count(kvp => kvp.Value == quarterKey),
                            WhatsAppLeadUniqueCount = whatsappLeadQuarterMap.Count(kvp => kvp.Value == quarterKey),

                            TotalLead = group.Select(lead => lead.LeadId).Distinct().Count(),

                            Call = group.Sum(lead => lead.CallEvent),
                            SMS = group.Sum(lead => lead.SMSEvent),
                            Email = group.Sum(lead => lead.EmailEvent),
                            WhatsApp = group.Sum(lead => lead.WhatsAppEvent),
                            TotalEvent = group.Sum(lead => lead.TotalEvent)
                        };
                    });
        }



        static Dictionary<string, grouppedLeadtrackerDto> GroupLeadsByYear(IEnumerable<LeadtrackerDto> Leadtrackerdata)
        {
            var smsLeadYearMap = Leadtrackerdata
                .Where(lead => lead.SMSLead > 0)
                .GroupBy(lead => lead.LeadId)
                .ToDictionary(
                    g => g.Key,
                    g => g.Min(x => x.CreatedOn.Year)
                );

            var callLeadYearMap = Leadtrackerdata
                .Where(lead => lead.CallLead > 0)
                .GroupBy(lead => lead.LeadId)
                .ToDictionary(
                    g => g.Key,
                    g => g.Min(x => x.CreatedOn.Year)
                );

            var emailLeadYearMap = Leadtrackerdata
                .Where(lead => lead.EmailLead > 0)
                .GroupBy(lead => lead.LeadId)
                .ToDictionary(
                    g => g.Key,
                    g => g.Min(x => x.CreatedOn.Year)
                );

            var whatsappLeadYearMap = Leadtrackerdata
                .Where(lead => lead.WhatsAppLead > 0)
                .GroupBy(lead => lead.LeadId)
                .ToDictionary(
                    g => g.Key,
                    g => g.Min(x => x.CreatedOn.Year)
                );

            return Leadtrackerdata
                .GroupBy(lead => lead.CreatedOn.Year)
                .ToDictionary(
                    yearGroup => yearGroup.Key.ToString(),
                    yearGroup =>
                    {
                        var year = yearGroup.Key;

                        return new grouppedLeadtrackerDto
                        {
                            SMSLeadUniqueCount = smsLeadYearMap.Count(kvp => kvp.Value == year),
                            CallLeadUniqueCount = callLeadYearMap.Count(kvp => kvp.Value == year),
                            EmailLeadUniqueCount = emailLeadYearMap.Count(kvp => kvp.Value == year),
                            WhatsAppLeadUniqueCount = whatsappLeadYearMap.Count(kvp => kvp.Value == year),

                            TotalLead = yearGroup.Select(lead => lead.LeadId).Distinct().Count(),

                            Call = yearGroup.Sum(lead => lead.CallEvent),
                            SMS = yearGroup.Sum(lead => lead.SMSEvent),
                            Email = yearGroup.Sum(lead => lead.EmailEvent),
                            WhatsApp = yearGroup.Sum(lead => lead.WhatsAppEvent),
                            TotalEvent = yearGroup.Sum(lead => lead.TotalEvent)
                        };
                    });
        }


        static Dictionary<string, grouppedLeadtrackerDto> GroupLeadsByHours(IEnumerable<LeadtrackerDto> Leadtrackerdata)
        {
            var smsLeadHourMap = Leadtrackerdata
                .Where(lead => lead.SMSLead > 0)
                .GroupBy(lead => lead.LeadId)
                .ToDictionary(g => g.Key, g => g.Min(x => x.CreatedOn.Hour));

            var callLeadHourMap = Leadtrackerdata
                .Where(lead => lead.CallLead > 0)
                .GroupBy(lead => lead.LeadId)
                .ToDictionary(g => g.Key, g => g.Min(x => x.CreatedOn.Hour));

            var emailLeadHourMap = Leadtrackerdata
                .Where(lead => lead.EmailLead > 0)
                .GroupBy(lead => lead.LeadId)
                .ToDictionary(g => g.Key, g => g.Min(x => x.CreatedOn.Hour));

            var whatsappLeadHourMap = Leadtrackerdata
                .Where(lead => lead.WhatsAppLead > 0)
                .GroupBy(lead => lead.LeadId)
                .ToDictionary(g => g.Key, g => g.Min(x => x.CreatedOn.Hour));

            return Leadtrackerdata
                .GroupBy(lead => lead.CreatedOn.Hour)
                .ToDictionary(
                    hourGroup => $"{hourGroup.Key:D2} Hour",
                    hourGroup =>
                    {
                        var hour = hourGroup.Key;

                        return new grouppedLeadtrackerDto
                        {
                            SMSLeadUniqueCount = smsLeadHourMap.Count(kvp => kvp.Value == hour),
                            CallLeadUniqueCount = callLeadHourMap.Count(kvp => kvp.Value == hour),
                            EmailLeadUniqueCount = emailLeadHourMap.Count(kvp => kvp.Value == hour),
                            WhatsAppLeadUniqueCount = whatsappLeadHourMap.Count(kvp => kvp.Value == hour),

                            TotalLead = hourGroup.Select(lead => lead.LeadId).Distinct().Count(),
                            Call = hourGroup.Sum(lead => lead.CallEvent),
                            SMS = hourGroup.Sum(lead => lead.SMSEvent),
                            Email = hourGroup.Sum(lead => lead.EmailEvent),
                            WhatsApp = hourGroup.Sum(lead => lead.WhatsAppEvent),
                            TotalEvent = hourGroup.Sum(lead => lead.TotalEvent)
                        };
                    });
        }



    }
}
