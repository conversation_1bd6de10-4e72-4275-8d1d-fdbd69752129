﻿using Lrb.Application.Property.Web;
using Lrb.Application.Utils;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Entities;
using Lrb.Domain.Enums;
using Lrb.Infrastructure.Persistence.Context;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using System.Diagnostics;

namespace Lrb.Infrastructure.Persistence.Repository.New_Implementation
{
    public partial class PropertyRepository
    {
        public async Task<(IEnumerable<Lrb.Domain.Entities.Property>, int)> GetAllPropertiesForMobileV2Async(Lrb.Application.Property.Mobile.GetAllPropertyRequest filter, Guid? masterPropertyAttributeId, Guid? masterPropertyAmenityId, Guid? masterPropertyTypeId, List<Guid>? propertyDimensionIds, Lrb.Application.Property.Mobile.NumericAttributesDto numericAttributesDto, List<Guid>? userIds, bool showAllProperties, string? tenantId = null, List<Guid>? propertyIds = null, List<CustomPropertyAttributeDto>? attributes = null)
        {
         
            try
            {
                var query = BuildQueryForGetAllPropertiesMobileV2(filter, masterPropertyAttributeId, masterPropertyAmenityId, masterPropertyTypeId, propertyDimensionIds, numericAttributesDto, userIds, showAllProperties, tenantId, propertyIds, attributes);
                int totalCount = query.Count();
                var properties = await query
           .Where(i => !i.IsDeleted && !i.IsArchived)
           .Skip(filter.PageSize * (filter.PageNumber - 1))
           .Take(filter.PageSize)
            .Select(i => new Property
            {
                Id = i.Id,
                Title = i.Title,
                EnquiredFor = i.EnquiredFor,
                SaleType = i.SaleType,
                Notes = i.Notes ?? string.Empty,
                FurnishStatus = i.FurnishStatus,
                Status = i.Status,               
                NoOfBHKs = i.NoOfBHKs,
                BHKType = i.BHKType,               
                SerialNo = i.SerialNo,
                ProjectId = i.ProjectId,
                IsWaterMarkEnabled = i.IsWaterMarkEnabled,
                LastModifiedBy = i.LastModifiedBy,
                LastModifiedOn = i.LastModifiedOn,
                CreatedBy = i.CreatedBy,
                CreatedOn = i.CreatedOn,
                ListingStatus = i.ListingStatus,
                ShortUrl = i.ShortUrl,
                PropertyType = i.PropertyType != null ? new MasterPropertyType
                {
                    Id = i.PropertyType.Id,
                    DisplayName = i.PropertyType.DisplayName,
                    BaseId = i.PropertyType.BaseId,
                    Level = i.PropertyType.Level,
                    Type = i.PropertyType.Type,
                } : null,
                MonetaryInfo =   i.MonetaryInfo != null ?  new PropertyMonetaryInfo
                {
                    Id = i.MonetaryInfo.Id,
                    Currency = i.MonetaryInfo.Currency,
                    ExpectedPrice = i.MonetaryInfo.ExpectedPrice,
                    Brokerage = i.MonetaryInfo.Brokerage,
                    BrokerageCurrency = i.MonetaryInfo.BrokerageCurrency,
                    PropertyId = i.MonetaryInfo.PropertyId
                } : null,
                Dimension = i.Dimension != null ? new PropertyDimension
                {
                    Id = i.Dimension.Id,
                    Area = i.Dimension.Area,
                    AreaUnitId = i.Dimension.AreaUnitId,
                    PropertyId = i.Dimension.PropertyId
                } : null,
                Galleries = i.Galleries != null ? i.Galleries.Select(g => new PropertyGallery
                {
                    Id = g.Id,
                    ImageKey = g.ImageKey,
                    ImageFilePath = g.ImageFilePath,
                    IsCoverImage = g.IsCoverImage,
                    PropertyId = g.PropertyId
                })
    .ToList() : null
            })
                .ToListAsync();


                return (properties, totalCount);
            }
            catch(Exception ex)
            {
                return new();
            }
            finally
            {
            }
        }
        private IQueryable<Lrb.Domain.Entities.Property> BuildQueryForGetAllPropertiesMobileV2(
Lrb.Application.Property.Mobile.GetAllPropertyRequest filter, Guid? masterPropertyAttributeId, Guid? masterPropertyAmenityId, Guid? masterPropertyTypeId, List<Guid>? propertyDimensionIds, Lrb.Application.Property.Mobile.NumericAttributesDto numericAttributesDto, List<Guid>? userIds, bool showAllProperties, string? tenantId = null, List<Guid>? propertyIds = null, List<CustomPropertyAttributeDto>? attributes = null)
        {
            tenantId = tenantId ?? _currentUser.GetTenant();
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            if ((filter.MinLeadCount != null || filter.MaxLeadCount != null ||
             filter.MinProspectCount != null || filter.MaxProspectCount != null)
            && (propertyIds?.Any() != true))
            {
                return context.Properties.Where(_ => false);
            }
            IQueryable<Lrb.Domain.Entities.Property> query = null;
            query = context.Properties.Where(i => !i.IsDeleted)
            .OrderBy(i => i.Status)
            .ThenByDescending(i => i.LastModifiedOn)
            .Where(i => !i.IsDeleted && !i.IsArchived);
            if ((userIds?.Any() ?? false) && !showAllProperties)
            {
                query = query.Where(i => i.PropertyAssignments.Any(i => userIds.Contains((Guid)i.AssignedTo) && i.IsCurrentlyAssigned));
            }
            if (filter.EnquiredFor != null)
            {
                query = query.Where(i => i.EnquiredFor == filter.EnquiredFor);
            }
            if (filter.PropertyStatus != null)
            {
                query = query.Where(i => i.Status == filter.PropertyStatus);
            }
            if (filter.Locations?.Any() ?? false)
            {

                filter.Locations = filter.Locations.Select(i => i.ToLower()).ToList();
                query = query.Where(i => i.Address != null &&
                   (
                       (i.Address.SubLocality != null && i.Address.Locality != null &&
                        filter.Locations.Contains(i.Address.SubLocality.ToLower() + ", " + i.Address.Locality.ToLower())) ||
                       (i.Address.SubLocality == null && i.Address.Locality != null &&
                        filter.Locations.Contains(", " + i.Address.Locality.ToLower())) ||
                       (i.Address.SubLocality != null && i.Address.Locality == null &&
                        filter.Locations.Contains(i.Address.SubLocality.ToLower() + ", "))
                   )
               );

            }

            if (!string.IsNullOrEmpty(filter.SerialNo))
            {
                query = query.Where(i => i.SerialNo.Contains(filter.SerialNo));
            }

            if (filter.Cities?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.City != null && filter.Cities.Select(i => i.ToLower()).Contains(i.Address.City.ToLower()));
            }
            if (filter.States?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.State != null && filter.States.Select(i => i.ToLower()).Contains(i.Address.State.ToLower()));
            }
            if (filter.Countries?.Any() ?? false)
            {
                query = query.Where(i => i.Address != null && i.Address.Country != null && filter.Countries.Select(i => i.ToLower()).Contains(i.Address.Country.ToLower()));
            }
            if (filter?.BHKTypes?.Any() ?? false)
            {
                query = query.Where(i => filter.BHKTypes.Contains(i.BHKType));
            }
            if (filter?.FurnishStatuses?.Any() ?? false)
            {
                query = query.Where(i => filter.FurnishStatuses.Contains(i.FurnishStatus));
            }

            if (filter.Facing != null)
            {
                query = query.Where(i => i.Facing == filter.Facing);

            }
            if (!string.IsNullOrEmpty(filter.PropertyTitle))
            {
                string propertyTitle = filter.PropertyTitle.ToLower().Replace(" ", "");
                query = query.Where(i => i.Title.Replace(" ", "").ToLower() == propertyTitle);
            }
            if (!string.IsNullOrEmpty(filter.OwnerName))
            {
                string ownerName = filter.OwnerName.ToLower().Replace(" ", "");
                query = query.Where(i => i.OwnerDetails.Name.Replace(" ", "").ToLower() == ownerName);
            }
            if (filter?.OwnerNames?.Any() ?? false)
            {
                filter.OwnerNames = filter.OwnerNames.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(e => e.PropertyOwnerDetails.Any(i => filter.OwnerNames.Contains(i.Name.ToLower().Trim().Replace(" ", "") ?? string.Empty)));
            }
            //if (filter?.Projects?.Any() ?? false)
            //{
            //    var projectNames = filter.Projects.ConvertAll(i => i.ToLower());
            //    Query.Where(i => i.Projects != null && i.Projects.Any(i => projectNames.Contains(i.Name.ToLower())));
            //}
            if (filter?.Projects?.Any() ?? false)
            {
                var projectNames = filter.Projects.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Project != null && projectNames.Contains(i.Project.Name.ToLower()));
            }
            if (filter.NoOfBHK != default)
            {
                query = query.Where(i => i.NoOfBHKs == filter.NoOfBHK);
            }
            if (filter?.BHKs?.Any() ?? false)
            {
                query = query.Where(i => filter.BHKs.Contains(i.NoOfBHKs));
            }
            if (filter.Ratings != default)
            {
                query = query.Where(i => i.Rating == filter.Ratings);
            }
            if (filter.FloorNumber != default)
            {
                query = query.Where(i => i.Attributes != null && i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c146a273-e342-4d66-aeee-6b2e6106d225") && i.Value == filter.FloorNumber.ToString()));

            }
            if (filter.BasePropertyTypeId != default)
            {
                query = query.Where(i => i.PropertyType != null && i.PropertyType.BaseId == filter.BasePropertyTypeId);
            }
            if (filter.PropertyTypes?.Any() ?? false)
            {
                query = query.Where(i => i.PropertyType != null && i.PropertyType.BaseId != null && filter.PropertyTypes.Contains(i.PropertyType.BaseId ?? default));
            }
            if (filter.PropertySubTypes?.Any() ?? false)
            {
                query = query.Where(i => i.PropertyType != null && filter.PropertySubTypes.Contains(i.PropertyType.Id));
            }
            if (filter.SubPropertyTypeIds?.Any() ?? false)
            {
                query = query.Where(i => i.PropertyType != null && filter.SubPropertyTypeIds.Contains(i.PropertyType.Id));
            }
            if (filter.MinBudget != null && filter.MaxBudget != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinBudget && i.MonetaryInfo.ExpectedPrice <= filter.MaxBudget);
            }
            else if (filter.MinBudget != null && filter.MaxBudget == null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinBudget);
            }
            else if (filter.MinBudget == null && filter.MaxBudget != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice <= filter.MaxBudget);
            }

            if (filter.FromPossessionDate != default || filter.ToPossessionDate != default)
            {

                if (filter.FromPossessionDate != null && filter.ToPossessionDate != null)
                {
                    query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromPossessionDate.Value && i.PossessionDate.Value < filter.ToPossessionDate.Value);
                }
                else if (filter.FromPossessionDate != null && filter.ToPossessionDate == null)
                {
                    query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromPossessionDate.Value);
                }
                else if (filter.FromPossessionDate == null && filter.ToPossessionDate != null)
                {
                    query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value <= filter.ToPossessionDate.Value);
                }
            }

            if (filter.MinPrice != null && filter.MaxPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinPrice && i.MonetaryInfo.ExpectedPrice <= filter.MaxPrice);
            }
            else if (filter.MinPrice != null && filter.MaxPrice == null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinPrice);
            }
            else if (filter.MinPrice == null && filter.MaxPrice != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice <= filter.MaxPrice);
            }
            if (filter.Amenities != null && filter.Amenities.Any())
            {
                query = query.Where(i => i.Amenities.Any(a => filter.Amenities.Contains(a.MasterPropertyAmenityId)));
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.Area != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }
                else if (filter.PropertySize.Area != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.Area == filter.PropertySize.Area || i.Dimension.AreaInSqMtr == filter.PropertySize.Area);
                }
            }
            if (filter.DateType.HasValue && (filter.FromDate != default || filter.ToDate != default))
            {
                filter.FromDate = filter.FromDate.HasValue ? filter.FromDate.Value.ConvertFromDateToUtc() : null;
                filter.ToDate = filter.ToDate.HasValue ? filter.ToDate.Value.ConvertToDateToUtc() : null;

                switch (filter.DateType)
                {
                    case Lrb.Application.Property.Mobile.PropertyDateType.CreatedDate:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.CreatedOn <= filter.ToDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            query = query.Where(i => i.CreatedOn >= filter.FromDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.CreatedOn >= filter.FromDate.Value && i.CreatedOn <= filter.ToDate.Value);
                        }
                        break;
                    case Lrb.Application.Property.Mobile.PropertyDateType.ModifiedDate:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value <= filter.ToDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value && i.LastModifiedOn.Value <= filter.ToDate.Value);
                        }
                        break;
                    case Lrb.Application.Property.Mobile.PropertyDateType.PossessionDate:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value <= filter.ToDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromDate.Value && i.PossessionDate.Value < filter.ToDate.Value.AddDays(1));
                        }
                        break;
                    case Lrb.Application.Property.Mobile.PropertyDateType.None:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            query = query.Where(i => i.CreatedOn <= filter.ToDate.Value ||
                                             (i.LastModifiedOn.HasValue && i.LastModifiedOn.Value <= filter.ToDate.Value) ||
                                             (i.PossessionDate.HasValue && i.PossessionDate.Value <= filter.ToDate.Value));
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            query = query.Where(i => i.CreatedOn >= filter.FromDate.Value ||
                                             (i.LastModifiedOn.HasValue && i.LastModifiedOn.Value >= filter.FromDate.Value) ||
                                             (i.PossessionDate.HasValue && i.PossessionDate.Value >= filter.FromDate.Value));
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            query = query.Where(i => (i.CreatedOn >= filter.FromDate.Value && i.CreatedOn <= filter.ToDate.Value) ||
                                             (i.LastModifiedOn.HasValue && i.LastModifiedOn.Value >= filter.FromDate.Value && i.LastModifiedOn.Value <= filter.ToDate.Value) ||
                                             (i.PossessionDate.HasValue && i.PossessionDate.Value >= filter.FromDate.Value && i.PossessionDate.Value <= filter.ToDate.Value));
                        }
                        break;
                    default:
                        break;
                }
            }


            if (!string.IsNullOrWhiteSpace(filter.PropertySearch))
            {

                query = query.Where(
                i => (i.Title + " " +
                i.OwnerDetails.Name + " " +
                i.OwnerDetails.Phone + " " +
                i.OwnerDetails.Email
                //i.AboutProperty + " " +
                //i.SerialNo + " "
                ).ToLower().Contains(filter.PropertySearch.ToLower()));
            }
            if (filter.NoOfFloors?.Any() ?? false && numericAttributesDto?.NoOfFloors?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "Total Floors");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfFloors.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfFloors.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0" && i.Value != "Ground Floor"));
                    }
                    else
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                           numericAttributesDto.NoOfFloors.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.NoOfBedrooms?.Any() ?? false && numericAttributesDto?.NoOfBedrooms?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Bed Rooms");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfBedrooms.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfBedrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                           numericAttributesDto.NoOfBedrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.NoOfKitchens?.Any() ?? false && numericAttributesDto?.NoOfKitchens?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Kitchens");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfKitchens.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfKitchens.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                           numericAttributesDto.NoOfKitchens.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }

            if (filter.NoOfUtilites?.Any() ?? false && numericAttributesDto?.NoOfUtilites?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Utilities");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfUtilites.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfUtilites.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                           numericAttributesDto.NoOfUtilites.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.NoOfLivingrooms?.Any() ?? false && numericAttributesDto?.NoOfLivingrooms?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Drawing or Living Rooms");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfLivingrooms.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfLivingrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                           numericAttributesDto.NoOfLivingrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }

            }
            if (filter.NoOfBalconies?.Any() ?? false && numericAttributesDto?.NoOfBalconies?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Balconies");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfBalconies.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfBalconies.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                           numericAttributesDto.NoOfBalconies.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.NoOfBathrooms?.Any() ?? false && numericAttributesDto?.NoOfBathrooms?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Bath Rooms");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfBathrooms.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfBathrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0" && i.Value != "Ground Floor"));
                    }
                    else
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                           numericAttributesDto.NoOfBathrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.Parking?.Any() ?? false && numericAttributesDto?.Parking?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "Parking");
                if (attribute != null)
                {
                    if (numericAttributesDto.Parking.IsMaxValueIncluded)
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                     !numericAttributesDto.Parking.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        query = query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                           numericAttributesDto.Parking.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.CarpetArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }
                else if (filter.PropertySize.CarpetArea == 999)
                {
                    query = query.Where(i => i.Dimension.CarpetArea <= filter.PropertySize.CarpetArea || i.Dimension.AreaInSqMtr <= filter.PropertySize.CarpetArea);

                }
                else if (filter.PropertySize.CarpetArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.CarpetArea >= filter.PropertySize.CarpetArea || i.Dimension.AreaInSqMtr >= filter.PropertySize.CarpetArea);
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.BuildUpArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.BuildUpArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.BuildUpArea == filter.PropertySize.BuildUpArea || i.Dimension.AreaInSqMtr == filter.PropertySize.BuildUpArea);
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {

                if (filter.PropertySize.SaleableArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.SaleableArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.SaleableArea == filter.PropertySize.SaleableArea || i.Dimension.AreaInSqMtr == filter.PropertySize.SaleableArea);
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {

                if (filter.PropertySize.NetArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    query = query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.NetArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    query = query.Where(i => i.Dimension.NetArea == filter.PropertySize.NetArea || i.Dimension.AreaInSqMtr == filter.PropertySize.NetArea);
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.MinCarpetArea != null && filter.PropertySize.MaxCarpetArea != null)
                {
                    query = query.Where(i => i.Dimension.CarpetArea >= filter.PropertySize.MinCarpetArea && i.Dimension.CarpetArea <= filter.PropertySize.MaxCarpetArea);
                    if (filter?.PropertySize.CarpetAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.CarpetAreaId == filter.PropertySize.CarpetAreaId);
                    }
                }
                else if (filter.PropertySize.MaxCarpetArea != null)
                {
                    query = query.Where(i => i.Dimension.CarpetArea <= filter.PropertySize.MaxCarpetArea);
                    if (filter?.PropertySize.CarpetAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.CarpetAreaId == filter.PropertySize.CarpetAreaId);
                    }
                }
                else if (filter.PropertySize.MinCarpetArea != null)
                {
                    query = query.Where(i => i.Dimension.CarpetArea >= filter.PropertySize.MinCarpetArea);
                    if (filter?.PropertySize.CarpetAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.CarpetAreaId == filter.PropertySize.CarpetAreaId);
                    }
                }

                if (filter.PropertySize.MinBuildUpArea != null && filter.PropertySize.MaxBuildUpArea != null)
                {
                    query = query.Where(i => i.Dimension.BuildUpArea >= filter.PropertySize.MinBuildUpArea && i.Dimension.BuildUpArea <= filter.PropertySize.MaxBuildUpArea);
                    if (filter.PropertySize?.BuildUpAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.BuildUpAreaId == filter.PropertySize.BuildUpAreaId);
                    }
                }
                else if (filter.PropertySize.MaxBuildUpArea != null)
                {
                    query = query.Where(i => i.Dimension.BuildUpArea <= filter.PropertySize.MaxPropertyArea);
                    if (filter.PropertySize?.BuildUpAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.BuildUpAreaId == filter.PropertySize.BuildUpAreaId);
                    }
                }
                else if (filter.PropertySize.MinBuildUpArea != null)
                {
                    query = query.Where(i => i.Dimension.BuildUpArea >= filter.PropertySize.MinBuildUpArea);
                    if (filter.PropertySize?.BuildUpAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.BuildUpAreaId == filter.PropertySize.BuildUpAreaId);
                    }
                }

                if (filter.PropertySize.MinSaleableArea != null && filter.PropertySize.MaxSaleableArea != null)
                {
                    query = query.Where(i => i.Dimension.SaleableArea >= filter.PropertySize.MinSaleableArea && i.Dimension.SaleableArea <= filter.PropertySize.MaxSaleableArea);

                    if (filter.PropertySize?.SaleableAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.SaleableAreaId == filter.PropertySize.SaleableAreaId);
                    }
                }
                else if (filter.PropertySize.MaxSaleableArea != null)
                {
                    query = query.Where(i => i.Dimension.SaleableArea <= filter.PropertySize.MaxSaleableArea);

                    if (filter.PropertySize?.SaleableAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.SaleableAreaId == filter.PropertySize.SaleableAreaId);
                    }
                }
                else if (filter.PropertySize.MinSaleableArea != null)
                {
                    query = query.Where(i => i.Dimension.SaleableArea >= filter.PropertySize.MinSaleableArea);

                    if (filter.PropertySize?.SaleableAreaId != null)
                    {
                        query = query.Where(i => i.Dimension.SaleableAreaId == filter.PropertySize.SaleableAreaId);
                    }
                }

                if (filter.PropertySize.MinPropertyArea != null && filter.PropertySize.MaxPropertyArea != null)
                {
                    query = query.Where(i => i.Dimension.Area >= filter.PropertySize.MinPropertyArea && i.Dimension.Area <= filter.PropertySize.MaxPropertyArea);
                    if (filter.PropertySize?.PropertyAreaUnitId != null)
                    {
                        query = query.Where(i => i.Dimension.AreaUnitId == filter.PropertySize.PropertyAreaUnitId);
                    }
                }
                else if (filter.PropertySize.MaxPropertyArea != null)
                {
                    query = query.Where(i => i.Dimension.Area <= filter.PropertySize.MaxPropertyArea);
                    if (filter.PropertySize?.PropertyAreaUnitId != null)
                    {
                        query = query.Where(i => i.Dimension.AreaUnitId == filter.PropertySize.PropertyAreaUnitId);
                    }
                }
                else if (filter.PropertySize.MinPropertyArea != null)
                {
                    query = query.Where(i => i.Dimension.Area >= filter.PropertySize.MinPropertyArea);
                    if (filter.PropertySize?.PropertyAreaUnitId != null)
                    {
                        query = query.Where(i => i.Dimension.AreaUnitId == filter.PropertySize.PropertyAreaUnitId);
                    }
                }

                if (filter.PropertySize.MinNetArea != null && filter.PropertySize.MaxNetArea != null)
                {
                    query = query.Where(i => i.Dimension.NetArea >= filter.PropertySize.MinNetArea && i.Dimension.NetArea <= filter.PropertySize.MaxNetArea);

                    if (filter.PropertySize?.NetAreaUnitId != null)
                    {
                        query = query.Where(i => i.Dimension.NetAreaUnitId == filter.PropertySize.NetAreaUnitId);
                    }
                }
                else if (filter.PropertySize.MaxNetArea != null)
                {
                    query = query.Where(i => i.Dimension.NetArea <= filter.PropertySize.MaxNetArea);

                    if (filter.PropertySize?.NetAreaUnitId != null)
                    {
                        query = query.Where(i => i.Dimension.NetAreaUnitId == filter.PropertySize.NetAreaUnitId);
                    }
                }
                else if (filter.PropertySize.MinNetArea != null)
                {
                    query = query.Where(i => i.Dimension.NetArea >= filter.PropertySize.MinNetArea);

                    if (filter.PropertySize?.NetAreaUnitId != null)
                    {
                        query = query.Where(i => i.Dimension.NetAreaUnitId == filter.PropertySize.NetAreaUnitId);
                    }
                }
            }
            if (filter.Currency != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.Currency == filter.Currency);
            }
            if (filter?.SaleTypes?.Any() ?? false)
            {
                query = query.Where(i => filter.SaleTypes.Contains(i.SaleType));
            }
            if (filter.ListingOnBehalf?.Any() ?? false)
            {
                query = query.Where(i => i.ListingOnBehalf != null && i.ListingOnBehalf.Any(j => filter.ListingOnBehalf.Contains(j)));
            }
            #region Property Area
            if (filter.MinPropertySize != null && filter.MaxPropertySize != null)
            {
                query = query.Where(i => i.Dimension.Area >= filter.MinPropertySize && i.Dimension.Area <= filter.MaxPropertySize);
                if (filter?.PropertySizeUnit != null)
                {
                    query = query.Where(i => i.Dimension.AreaUnitId == filter.PropertySizeUnit);
                }
            }
            else if (filter.MaxPropertySize != null)
            {
                query = query.Where(i => i.Dimension.Area <= filter.MaxPropertySize);
                if (filter?.PropertySizeUnit != null)
                {
                    query = query.Where(i => i.Dimension.AreaUnitId == filter.PropertySizeUnit);
                }
            }
            else if (filter.MinPropertySize != null)
            {
                query = query.Where(i => i.Dimension.Area >= filter.MinPropertySize);
                if (filter?.PropertySizeUnit != null)
                {
                    query = query.Where(i => i.Dimension.AreaUnitId == filter.PropertySizeUnit);
                }
            }
            #endregion

            #region Carpet Area
            if (filter.MinCarpetArea != null && filter.MaxCarpetArea != null)
            {
                query = query.Where(i => i.Dimension.CarpetArea >= filter.MinCarpetArea && i.Dimension.CarpetArea <= filter.MaxCarpetArea);
                if (filter?.CarpetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.CarpetAreaId == filter.CarpetAreaUnit);
                }
            }
            else if (filter.MaxCarpetArea != null)
            {
                query = query.Where(i => i.Dimension.CarpetArea <= filter.MaxCarpetArea);
                if (filter?.CarpetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.CarpetAreaId == filter.CarpetAreaUnit);
                }
            }
            else if (filter.MinCarpetArea != null)
            {
                query = query.Where(i => i.Dimension.CarpetArea >= filter.MinCarpetArea);
                if (filter?.CarpetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.CarpetAreaId == filter.CarpetAreaUnit);
                }
            }
            #endregion

            #region BuildUp Area
            if (filter.MinBuitUpArea != null && filter.MaxBuitUpArea != null)
            {
                query = query.Where(i => i.Dimension.BuildUpArea >= filter.MinBuitUpArea && i.Dimension.BuildUpArea <= filter.MaxBuitUpArea);
                if (filter?.BuitUpAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.BuildUpAreaId == filter.BuitUpAreaUnit);
                }
            }
            else if (filter.MaxBuitUpArea != null)
            {
                query = query.Where(i => i.Dimension.BuildUpArea <= filter.MaxPropertySize);
                if (filter?.BuitUpAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.BuildUpAreaId == filter.BuitUpAreaUnit);
                }
            }
            else if (filter.MinBuitUpArea != null)
            {
                query = query.Where(i => i.Dimension.BuildUpArea >= filter.MinBuitUpArea);
                if (filter?.BuitUpAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.BuildUpAreaId == filter.BuitUpAreaUnit);
                }
            }
            #endregion

            #region Saleable Area
            if (filter.MinSaleableArea != null && filter.MaxSaleableArea != null)
            {
                query = query.Where(i => i.Dimension.SaleableArea >= filter.MinSaleableArea && i.Dimension.SaleableArea <= filter.MaxSaleableArea);

                if (filter?.SaleableAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.SaleableAreaId == filter.SaleableAreaUnit);
                }
            }
            else if (filter.MaxSaleableArea != null)
            {
                query = query.Where(i => i.Dimension.SaleableArea <= filter.MaxSaleableArea);

                if (filter?.SaleableAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.SaleableAreaId == filter.SaleableAreaUnit);
                }
            }
            else if (filter.MinSaleableArea != null)
            {
                query = query.Where(i => i.Dimension.SaleableArea >= filter.MinSaleableArea);

                if (filter?.SaleableAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.SaleableAreaId == filter.SaleableAreaUnit);
                }
            }
            #endregion
            if (filter.MinNetArea != null && filter.MaxNetArea != null)
            {
                query = query.Where(i => i.Dimension.NetArea >= filter.MinNetArea && i.Dimension.NetArea <= filter.MaxNetArea);

                if (filter?.NetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.NetAreaUnitId == filter.NetAreaUnit);
                }
            }
            else if (filter.MaxNetArea != null)
            {
                query = query.Where(i => i.Dimension.NetArea <= filter.MaxNetArea);

                if (filter?.NetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.NetAreaUnitId == filter.NetAreaUnit);
                }
            }
            else if (filter.MinNetArea != null)
            {
                query = query.Where(i => i.Dimension.NetArea >= filter.MinNetArea);

                if (filter?.NetAreaUnit != null)
                {
                    query = query.Where(i => i.Dimension.NetAreaUnitId == filter.NetAreaUnit);
                }
            }
            if (propertyIds?.Any() ?? false)
            {
                query = query.Where(i => propertyIds.Contains(i.Id));
            }
            if (filter?.PossesionType != null && filter?.PossesionType != PossesionType.None)
            {
                switch (filter?.PossesionType)
                {
                    case PossesionType.UnderConstruction:
                        query = query.Where(i => i.PossesionType == PossesionType.UnderConstruction);
                        break;

                    case PossesionType.SixMonth:
                        query = query.Where(i => i.PossesionType == PossesionType.SixMonth);
                        break;

                    case PossesionType.Year:
                        query = query.Where(i => i.PossesionType == PossesionType.Year);
                        break;

                    case PossesionType.TwoYears:
                        query = query.Where(i => i.PossesionType == PossesionType.TwoYears);
                        break;

                    case PossesionType.CustomDate:
                        DateTime? tempToPossesionDate = filter?.ToPossesionDate?.ConvertToDateToUtc();
                        DateTime? tempFrompossesionDate = filter?.FromPossesionDate?.ConvertFromDateToUtc();
                        query = query.Where(i => i.PossessionDate != null && i.PossessionDate >= tempFrompossesionDate.Value && i.PossessionDate <= tempToPossesionDate.Value);

                        break;
                }
            }

            return query;
        }
       
    }
}

