﻿//using Amazon.Runtime.Internal.Util;
using Lrb.Application.Agency.Web;
using Lrb.Application.Attendance.Web.Requests;
using Lrb.Application.ChannelPartner.Mobile.Specs;
using Lrb.Application.Common.GooglePlaces;
using Lrb.Application.Common.NotificationService;
using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.Common.ServiceBus;
using Lrb.Application.Common.SMS;
using Lrb.Application.Identity.Users;
using Lrb.Application.Integration.Mobile;
using Lrb.Application.Lead.Mobile.Dtos;
using Lrb.Application.Lead.Mobile.Dtos.v1;
using Lrb.Application.Lead.Mobile.Mappings;
using Lrb.Application.Lead.Mobile.Mappings.v1;
using Lrb.Application.Lead.Mobile.Specs;
using Lrb.Application.Lead.Mobile.Specs.v1;
using Lrb.Application.Lead.Mobile.v2;
using Lrb.Application.Project.Mobile;
using Lrb.Application.Project.Mobile.Specs;
using Lrb.Application.Lead.Mobile.Specs.v2;
using Lrb.Application.Property.Mobile;
using Lrb.Application.ZonewiseLocation.Mobile.Helpers;
using Lrb.Application.ZonewiseLocation.Mobile.Requests;
using Lrb.Application.ZonewiseLocation.Mobile.Specs;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Lrb.Shared.Extensions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;
using Serilog;
using System.Security.Claims;
using Lrb.Application.Campaigns.Spec;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;

namespace Lrb.Application.Lead.Mobile.Requests
{
    public class LeadCommonRequestHandler
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly string _className;
        private readonly string _methodName;
        protected readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        protected readonly IReadRepository<MasterAreaUnit> _masterAreaUnitRepo;
        protected readonly IRepositoryWithEvents<LeadEnquiry> _leadEnquiryRepo;
        protected readonly IRepositoryWithEvents<Address> _addressRepo;
        protected readonly IRepositoryWithEvents<MasterPropertyType> _propertyTypeRepo;
        protected readonly IRepositoryWithEvents<CustomMasterLeadStatus> _leadStatusRepo;
        protected readonly IRepositoryWithEvents<PropertyDimension> _dimensionRepo;
        protected readonly IRepositoryWithEvents<LeadTag> _leadTagRepo;
        protected readonly IReadRepository<CustomMasterLeadStatus> _masterLeadStatusRepo;
        protected readonly IRepositoryWithEvents<LeadHistory> _leadHistoryRepo;
        protected readonly IGooglePlacesService _googlePlacesService;
        protected readonly ICurrentUser _currentUserRepo;
        protected readonly IUserService _userService;
        protected readonly INotificationSenderService _notificationSenderService;
        protected readonly Common.NotificationService.INotificationService _notificationService;
        protected readonly IRepositoryWithEvents<Domain.Entities.Property> _propertyRepo;
        protected readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalsettingRepo;
        protected readonly IEmailService _emailService;
        protected readonly ITextLocalSmsService _textLocalSmsService;
        protected readonly IHostEnvironment _environment;
        protected readonly IDapperRepository _dapperRepository;
        protected readonly ICurrentUser _currentUser;
        protected readonly IMediator _mediator;
        protected readonly IRepositoryWithEvents<DuplicateLeadFeatureInfo> _duplicateInfoRepo;
        protected readonly IRepositoryWithEvents<Location> _locationRepo;
        protected readonly ILeadRepositoryAsync _leadRepositoryAsync;
        protected readonly IRepositoryWithEvents<Lrb.Domain.Entities.ChannelPartner> _cpRepository;
        protected readonly IRepositoryWithEvents<LeadAppointment> _appointmentRepo;
        protected readonly INpgsqlRepository _npgsqlRepo;
        protected readonly IRepositoryWithEvents<LeadCommunication> _communicationRepo;
        protected readonly IRepositoryWithEvents<LeadBookedDetail> _leadBookedDetailRepo;
        protected readonly IRepositoryWithEvents<Flag> _flagRepository;
        protected readonly ILogger _logger;
        protected readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        protected readonly IServiceBus _serviceBus;
        protected readonly IRepositoryWithEvents<Domain.Entities.Agency> _agencyRepo;
        protected readonly IRepositoryWithEvents<UnitType> _unitType;
        protected readonly IRepositoryWithEvents<Domain.Entities.QRFormTemplate> _qrFormTemplateRepo;
        protected readonly IRepositoryWithEvents<LeadAssignment> _leadAssignmentRepo;
        protected readonly IRepositoryWithEvents<Domain.Entities.Campaign> _campaignRepo;
        protected readonly IRepositoryWithEvents<FacebookAuthResponse> _fbAuthResponseRepo;


        public LeadCommonRequestHandler(
            IServiceProvider serviceProvider,
            string className,
            string methodName)
        {
            _serviceProvider = serviceProvider;
            _className = className;
            _methodName = methodName;
            _leadRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.Lead>>();
            _masterAreaUnitRepo = _serviceProvider.GetRequiredService<IReadRepository<MasterAreaUnit>>();
            _leadEnquiryRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<LeadEnquiry>>();
            _addressRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Address>>();
            _propertyTypeRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<MasterPropertyType>>();
            _leadStatusRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<CustomMasterLeadStatus>>();
            _dimensionRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<PropertyDimension>>();
            _leadTagRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<LeadTag>>();
            _masterLeadStatusRepo = _serviceProvider.GetRequiredService<IReadRepository<CustomMasterLeadStatus>>();
            _leadHistoryRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<LeadHistory>>();
            _googlePlacesService = _serviceProvider.GetRequiredService<IGooglePlacesService>();
            _currentUserRepo = _serviceProvider.GetRequiredService<ICurrentUser>();
            _userService = _serviceProvider.GetRequiredService<IUserService>();
            _notificationSenderService = _serviceProvider.GetRequiredService<INotificationSenderService>();
            _notificationService = _serviceProvider.GetRequiredService<Common.NotificationService.INotificationService>();
            _propertyRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.Property>>();
            _globalsettingRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.GlobalSettings>>();
            _emailService = _serviceProvider.GetRequiredService<IEmailService>();
            _textLocalSmsService = _serviceProvider.GetRequiredService<ITextLocalSmsService>();
            _environment = _serviceProvider.GetRequiredService<IHostEnvironment>();
            _dapperRepository = _serviceProvider.GetRequiredService<IDapperRepository>();
            _currentUser = _serviceProvider.GetRequiredService<ICurrentUser>();
            _mediator = _serviceProvider.GetRequiredService<IMediator>();
            _duplicateInfoRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<DuplicateLeadFeatureInfo>>();
            _locationRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Location>>();
            _leadRepositoryAsync = _serviceProvider.GetRequiredService<ILeadRepositoryAsync>();
            _cpRepository = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Lrb.Domain.Entities.ChannelPartner>>();
            _appointmentRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<LeadAppointment>>();
            _npgsqlRepo = _serviceProvider.GetRequiredService<INpgsqlRepository>();
            _communicationRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<LeadCommunication>>();
            _flagRepository = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Flag>>();
            _leadBookedDetailRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<LeadBookedDetail>>();
            _logger = _serviceProvider.GetRequiredService<ILogger>();
            _serviceBus = _serviceProvider.GetRequiredService<IServiceBus>();
            _projectRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.Project>>();
            _agencyRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.Agency>>();
            _unitType = _serviceProvider.GetRequiredService<IRepositoryWithEvents<UnitType>>();
            _qrFormTemplateRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.QRFormTemplate>>();
            _leadAssignmentRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<LeadAssignment>>();
            _campaignRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.Campaign>>();
            _fbAuthResponseRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.FacebookAuthResponse>>();
        }

        public async Task<Domain.Entities.Lead> InitializeLeadAsync(Domain.Entities.Lead lead, LeadTag? leadTags = null, Address? address = null, CancellationToken cancellationToken = default)
        {
            lead ??= new();
            await InitializeLeadStatusAsync(lead, cancellationToken);
            SetLeadNameAndNumber(lead, cancellationToken);
            leadTags ??= new();
            if (leadTags != null)
            {
                lead.TagInfo = leadTags;
            }
            if (address != null)
            {
                lead.Address = address;
            }

            // Set OriginalOwner to the assigned user when first assigned
            if (lead.AssignTo != Guid.Empty && lead.OriginalOwner == null)
            {
                lead.OriginalOwner = lead.AssignTo;
            }

            return lead;
        }

        public async Task<Domain.Entities.Lead> GetExistingLeadAsync(Guid id, string contactNo, CancellationToken cancellationToken = default)
        {
            var existingFullLead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(id), cancellationToken) ?? throw new NotFoundException("No Lead found by this Id");
            //var duplicateLeads = await _leadRepo.ListAsync(new LeadByContactNoSpec(new List<string>() { contactNo.Substring(contactNo.Length - 10) }), cancellationToken);
            //if (duplicateLeads != null && duplicateLeads.Any(i => i.Id != existingFullLead.Id))
            //{
            //    throw new DuplicateLeadException("Duplicate Lead Found With Ids : " + JsonConvert.SerializeObject(duplicateLeads.Where(i => i.Id != existingFullLead.Id).Select(i => i.Id).ToArray()));
            //}
            return existingFullLead;
        }
        public static void SetLeadEnquiry(Domain.Entities.Lead lead, CreateLeadEnquiryDto? enquiryDto, Address? address = null, MasterPropertyType? propertyType = null, List<Address>? addresses = null, CancellationToken cancellationToken = default, List<MasterPropertyType>? propertyTypes = null)
        {
            var enquiry = enquiryDto?.Adapt<LeadEnquiry>();
            enquiry ??= new();
            enquiry.NoOfBHKs = enquiryDto?.NoOfBHK ?? default;
            enquiry.BHKs=enquiryDto?.BHKs ?? default;
            enquiry.IsPrimary = true;
            enquiry.Address = address;
            enquiry.Addresses = addresses;
            enquiry.PropertyType = propertyType;
            enquiry.PropertyTypes = propertyTypes;

            enquiry.CarpetAreaInSqMtr = ((enquiryDto?.CarpetArea ?? 0) * (enquiryDto?.ConversionFactor ?? 0));
            enquiry.BuiltUpAreaInSqMtr = ((enquiryDto?.BuiltUpArea ?? 0) * (enquiryDto?.BuiltUpAreaConversionFactor ?? 0));
            enquiry.SaleableAreaInSqMtr = ((enquiryDto?.SaleableArea ?? 0) * (enquiryDto?.SaleableAreaConversionFactor ?? 0));
            enquiry.PropertyAreaInSqMtr = ((enquiryDto?.PropertyArea ?? 0) * (enquiryDto?.PropertyAreaConversionFactor ?? 0));
            enquiry.NetAreaInSqMtr = ((enquiryDto?.NetArea ?? 0) * (enquiryDto?.NetAreaConversionFactor ?? 0));


            lead.Enquiries ??= new List<LeadEnquiry>();
            lead.Enquiries.Add(enquiry);
        }
        public async Task UpdateLeadEnquiryAsync(Domain.Entities.Lead lead, CreateLeadEnquiryDto? enquiryDto, Address? address,List<Address>? addresses,MasterPropertyType? propertyType = null, CancellationToken cancellationToken = default, List<MasterPropertyType>? propertyTypes = null)
        {
            LeadEnquiry leadEnquiry = enquiryDto?.Adapt<LeadEnquiry>() ?? new();
            leadEnquiry.NoOfBHKs = enquiryDto?.NoOfBHK ?? default;
            leadEnquiry.BHKs = enquiryDto?.BHKs ?? default;
            leadEnquiry.Address = address;
            leadEnquiry.Addresses = addresses;
            leadEnquiry.PropertyType = propertyType;
            leadEnquiry.IsPrimary = true;
            leadEnquiry.PropertyTypes = propertyTypes;
           
            if (enquiryDto?.CarpetArea != null )
            {
                leadEnquiry.CarpetAreaInSqMtr = (enquiryDto.CarpetArea * enquiryDto.ConversionFactor);
            }
            if (enquiryDto?.BuiltUpArea != null)
            {
                leadEnquiry.BuiltUpAreaInSqMtr = (enquiryDto.BuiltUpArea * enquiryDto.BuiltUpAreaConversionFactor);
            }
            if (enquiryDto?.SaleableArea != null)
            {
                leadEnquiry.SaleableAreaInSqMtr = (enquiryDto.SaleableArea * enquiryDto.SaleableAreaConversionFactor);
            }
            if (enquiryDto?.NetArea != null)
            {
                leadEnquiry.NetAreaInSqMtr = (enquiryDto.NetArea * enquiryDto.NetAreaConversionFactor);
            }
            if (enquiryDto?.PropertyArea != null)
            {
                leadEnquiry.PropertyAreaInSqMtr = (enquiryDto.PropertyArea * enquiryDto.PropertyAreaConversionFactor);
            }
            await UpdateLeadSubSourceASync(leadEnquiry, cancellationToken);
            var existingEnquiry = lead.Enquiries.FirstOrDefault();
            if (existingEnquiry is not null)
            {
                var propertyTypeIds = new List<Guid>()
                {
                    Guid.Parse("7d583139-e87d-4d2e-9d04-3bffebf82942"),
                    Guid.Parse("8ba96762-16f2-4735-bc5a-138573081a19"),
                    Guid.Parse("8d178b31-8008-4e19-8eeb-5189f7578044"),

                };
                if (existingEnquiry.PropertyType != null && propertyTypeIds.Contains(existingEnquiry.PropertyType.Id) && !propertyTypeIds.Contains(leadEnquiry.PropertyType?.Id ?? Guid.Empty))
                {
                    //leadEnquiry.NoOfBHKs = 0;
                    //leadEnquiry.BHKType = BHKType.None;
                    leadEnquiry.BHKs = new List<double>();
                    leadEnquiry.BHKTypes = new List<BHKType>();
                }
                if (existingEnquiry.PropertyTypes != null && existingEnquiry.PropertyTypes.Select(i => i.Id).Any(id => propertyTypeIds.Contains(id)) && leadEnquiry.PropertyTypes != null&& !leadEnquiry.PropertyTypes.Select(i => i.Id).Any(id => propertyTypeIds.Contains(id)))
                {
                    leadEnquiry.BHKs = new List<double>();
                    leadEnquiry.BHKTypes = new List<BHKType>();
                }

                existingEnquiry.Update(leadEnquiry);
                await _leadEnquiryRepo.UpdateAsync(existingEnquiry, cancellationToken);
            }
            else
            {
                leadEnquiry.LeadId = lead.Id;
                await _leadEnquiryRepo.AddAsync(leadEnquiry);
            }
        }
        public async void SetLeadAssignedTo(Domain.Entities.Lead lead, CancellationToken cancellationToken = default)
        {
            var currentUserId = _currentUserRepo.GetUserId();

            if (lead.AssignTo == default || lead.AssignTo == Guid.Empty)
            {
                //lead.AssignedFrom = currentUserId;
                lead.AssignTo = currentUserId;
            }
            if (lead.AssignTo == Guid.Empty)
            {
                lead.SecondaryUserId = Guid.Empty;
            }

            // Set OriginalOwner to the assigned user when first assigned
            if (lead.AssignTo != Guid.Empty && lead.OriginalOwner == null)
            {
                lead.OriginalOwner = lead.AssignTo;
            }
        }

        public void SetLeadAssignedTo(Domain.Entities.Lead lead, Guid? userId, CancellationToken cancellationToken = default)
        {
            var currentUserId = _currentUserRepo.GetUserId();

            if (lead.AssignTo == default || lead.AssignTo == Guid.Empty)
            {
                lead.AssignedFrom = lead.AssignTo;
                lead.AssignTo = currentUserId;
            }
            else
            {
                //lead.AssignedFrom = lead.AssignTo;
                lead.AssignTo = userId ?? Guid.Empty;   
            }
            if (lead.AssignTo == Guid.Empty)
            {
                lead.SecondaryUserId = Guid.Empty;
            }
        }

        public async Task UpdateLeadSubSourceASync(LeadEnquiry leadEnquiry, CancellationToken cancellationToken = default)
        {
            if (!string.IsNullOrEmpty(leadEnquiry.SubSource) &&
                leadEnquiry.LeadSource != LeadSource.Facebook &&
                leadEnquiry.LeadSource != LeadSource.GoogleAds &&
                leadEnquiry.LeadSource != LeadSource.Gmail)
            {
                var sourceDtos = (await _dapperRepository.GetAllIntegrationSubSourceAsync<SourceDto>(_currentUser.GetTenant() ?? string.Empty)).Where(i => i.SubSource != null &&
                i.LeadSource != LeadSource.Facebook && i.LeadSource != LeadSource.GoogleAds && i.LeadSource != LeadSource.Gmail && i.LeadSource == leadEnquiry.LeadSource).ToList();
                if (!sourceDtos.Any(i => i.SubSource?.ToLower()?.Trim()?.Contains(leadEnquiry.SubSource?.ToLower()?.Trim() ?? "Invalid") ?? false))
                {
                    try
                    {
                        await _mediator.Send(new CreateIntegrationRequest()
                        {
                            AccountName = leadEnquiry.SubSource,
                            Source = leadEnquiry.LeadSource
                        });
                    }
                    catch (Exception ex)
                    {
                        await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                    }
                }
            }
        }
        public async Task SetLeadProjectsAsync(Domain.Entities.Lead lead, List<string>? projectList, Domain.Entities.GlobalSettings? globalSettings, CancellationToken cancellationToken = default)
        {
            try
            {
                List<Lrb.Domain.Entities.Project>? projects = new();
                //Domain.Entities.GlobalSettings? globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                projectList = (projectList?.Any() ?? false) ? projectList.Where(i => !string.IsNullOrWhiteSpace(i)).ToList() : null;
                if (projectList?.Any() ?? false)
                {
                    foreach (var newProject in projectList)
                    {
                        Lrb.Domain.Entities.Project? existingProject = (await _projectRepo.ListAsync(new GetProjectByIdSpecsV2(newProject), cancellationToken)).FirstOrDefault();
                        if (existingProject != null)
                        {
                            projects.Add(existingProject);
                        }
                        else
                        {
                            Domain.Entities.Project newProjects = new() 
                            { 
                                Name = newProject,
                                 MonetaryInfo = new ProjectMonetaryInfo
                                 {
                                     Currency = globalSettings?.Countries?.FirstOrDefault()?.DefaultCurrency ?? "INR"
                                 }
                            };
                            newProjects = await _projectRepo.AddAsync(newProjects, cancellationToken);
                            projects.Add(newProjects);
                        }
                    }
                    lead.Projects = projects;
                }
                else if ((lead?.Projects?.Any() ?? false) && projectList == null)
                {
                    lead.Projects = projects;
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
            }
        }
        public async Task SetLeadPropertiesAsync(Domain.Entities.Lead lead, List<string>? propertyList, Domain.Entities.GlobalSettings? globalSettings, CancellationToken cancellationToken = default)
        {
            try
            {
                List<Domain.Entities.Property>? properties = new();
                //Domain.Entities.GlobalSettings? globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);

                propertyList = (propertyList?.Any() ?? false) ? propertyList.Where(i => !string.IsNullOrWhiteSpace(i))?.ToList() : null;
                if (propertyList?.Any() ?? false)
                {
                    foreach (var newProperty in propertyList)
                    {
                        var existingProperty = (await _propertyRepo.ListAsync(new GetPropertyByTitleSpec(newProperty), cancellationToken)).FirstOrDefault();
                        if (existingProperty != null)
                        {
                            properties.Add(existingProperty);
                        }
                        else
                        {
                            Domain.Entities.Property property = new() 
                            { 
                                Title = newProperty,
                                MonetaryInfo = new PropertyMonetaryInfo
                                {
                                    Currency = globalSettings?.Countries?.FirstOrDefault()?.DefaultCurrency ?? "INR"
                                }
                            };
                            property = await _propertyRepo.AddAsync(property, cancellationToken);
                            properties.Add(property);
                        }
                    }
                    lead.Properties = properties;
                }
                else if ((lead?.Properties?.Any() ?? false) && propertyList == null)
                {
                    lead.Properties = properties;
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
            }
        }
        public async Task SetChannelPartnersAsync(Domain.Entities.Lead lead, List<string>? channelPartnersList, CancellationToken cancellationToken = default)
        {
            try
            {
                List<Lrb.Domain.Entities.ChannelPartner> channelPartners = new();
                channelPartnersList = (channelPartnersList?.Any() ?? false) ? channelPartnersList.Where(i => !string.IsNullOrWhiteSpace(i)).ToList() : null;
                if (channelPartnersList?.Any() ?? false)
                {
                    foreach (var newChannelPartner in channelPartnersList)
                    {
                        var existingChannelPartner = (await _cpRepository.ListAsync(new GetChannelPartnerByNameSpecs(newChannelPartner), cancellationToken)).FirstOrDefault();
                        if (existingChannelPartner != null)
                        {
                            channelPartners.Add(existingChannelPartner);
                        }
                        else
                        {
                            Lrb.Domain.Entities.ChannelPartner channelPartner = new() { FirmName = newChannelPartner };
                            channelPartner = await _cpRepository.AddAsync(channelPartner, cancellationToken);
                            channelPartners.Add(channelPartner);
                        }
                    }
                    lead.ChannelPartners = channelPartners;
                }
                else if ((lead?.ChannelPartners?.Any() ?? false) && channelPartnersList == null)
                {
                    lead.ChannelPartners = channelPartners;
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
            }
        }

        public async Task SetDuplicateTagAsync(Domain.Entities.Lead lead, CancellationToken cancellationToken = default)
        {
            var parentLead = await _leadRepo.FirstOrDefaultAsync(new GetParentLeadSpec(lead.ContactNo ?? string.Empty,lead.AlternateContactNo ?? string.Empty,lead.Id), cancellationToken);
            if (parentLead != null)
            {
                lead.AddDuplicateDetail(parentLead.ChildLeadsCount, parentLead.Id);
                parentLead.ChildLeadsCount += 1;
                try
                {
                    await _leadRepo.UpdateAsync(parentLead);
                }
                catch (Exception ex)
                {
                    await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                }
            }
        }

        public async Task CreateLeadHistoryAsync(Domain.Entities.Lead lead, CancellationToken cancellationToken = default)
        {
            var leadDto = await GetFullLeadDtoAsync(lead, cancellationToken);
            var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
            await _leadHistoryRepo.AddAsync(leadHistory);
        }
        protected async Task UpdateLeadHistoryV2Async(Domain.Entities.Lead lead, ViewLeadDto? leadDto = null, AppointmentType? appointmentType = null, CancellationToken cancellationToken = default, bool? shouldUpdateContactRecord = null)
        {
            try
            {
                var data = new Lrb.Application.Common.ServiceBus.LeadHistoryDto() { Lead = lead, AppointmentType = appointmentType, LeadDto = leadDto, ShouldUpdateContactRecord = shouldUpdateContactRecord };
                var payload = new InputPayload(_currentUser.GetTenant() ?? string.Empty, _currentUserRepo.GetUserId(), "leadhistory", data);
                await _serviceBus.RunLeadHistoryJobAsync(payload);
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task SendLeadAssignmentNotificationsV2Async(Domain.Entities.Lead lead, CancellationToken cancellationToken = default)
        {
            try
            {
                if (lead.AssignTo != default && lead.AssignTo != Guid.Empty && lead.AssignTo != _currentUserRepo.GetUserId())
                {
                    var leadDto = await GetFullLeadDtoAsync(lead, cancellationToken);
                    var data = new SendNotificationDto() { @event = Event.LeadAssignment, Entity = lead, AssignTo = lead.AssignTo, UserName = leadDto.AssignedFromUser?.Name, Topics = new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, CurrentUserId = _currentUserRepo.GetUserId(), TenantId = _currentUser.GetTenant() ?? string.Empty };
                    var payload = new InputPayload(_currentUser.GetTenant() ?? string.Empty, _currentUserRepo.GetUserId(), "notification", data);
                    await _serviceBus.RunNotificationJobAsync(payload);
                }
                else if (lead != null && lead.AssignTo == Guid.Empty)
                {
                    List<Guid> adminIds = (await _npgsqlRepo.GetAdminIdsAsync(_currentUser.GetTenant() ?? string.Empty)).Where(i => i != _currentUserRepo.GetUserId()).ToList();
                    if (adminIds.Any())
                    {
                        foreach (var adminId in adminIds)
                        {
                            var adminDetails = await _userService.GetAsync(adminId.ToString(), cancellationToken);
                            if (adminDetails != null)
                            {
                                var data = new SendNotificationDto() { @event = Event.LeadMovedToUnassigned, Entity = lead, AssignTo = adminId, UserName = null, CurrentUserId = _currentUserRepo.GetUserId(), NoOfEntities = 1 };
                                var payload = new InputPayload(_currentUser.GetTenant() ?? string.Empty, _currentUserRepo.GetUserId(), "notification", data);
                                await _serviceBus.RunNotificationJobAsync(payload);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task UpdateLeadHistoryAsync(Domain.Entities.Lead lead, ViewLeadDto? leadDto = null, AppointmentType? appointmentType = null, CancellationToken cancellationToken = default, bool? shouldUpdateContactRecord = null, Guid? currentUserId = null, bool shouldUpdateNotes = false, Guid? previousAssignedUser = null)
        {
            try
            {
                var userId = currentUserId ?? _currentUser.GetUserId();
                leadDto ??= await GetFullLeadDtoAsync(lead, cancellationToken, currentUserId);
                var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto, appointmentType, shouldUpdateContactRecord: shouldUpdateContactRecord);
                if (userId != Guid.Empty && lead.AssignTo == Guid.Empty && userId != lead.AssignTo)
                {
                    leadHistory.UserId = previousAssignedUser ?? lead.AssignedFrom ?? userId;
                    var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, userId)) ?? await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, previousAssignedUser ?? lead.AssignedFrom ?? Guid.Empty));
                    if (existingLeadHistory != null)
                    {
                        if (appointmentType != null)
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, appointmentType, shouldUpdateNotes: shouldUpdateNotes), cancellationToken);
                        }
                        else
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, shouldUpdateNotes: shouldUpdateNotes), cancellationToken);
                        }
                    }
                    else
                    {
                        await _leadHistoryRepo.AddAsync(leadHistory, cancellationToken);
                    }
                }
                else
                {
                    var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignTo)) ??  await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, previousAssignedUser ?? userId));
                    if (existingLeadHistory != null)
                    {
                        if (appointmentType != null)
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, appointmentType, shouldUpdateNotes: shouldUpdateNotes), cancellationToken);
                        }
                        else
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, shouldUpdateNotes: shouldUpdateNotes), cancellationToken);
                        }
                    }
                    else
                    {
                        existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignedFrom ?? Guid.Empty));
                        if (existingLeadHistory != null)
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory), cancellationToken);
                        }
                        else
                        {
                            await _leadHistoryRepo.AddAsync(leadHistory, cancellationToken);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        public async Task<ViewLeadDto> GetFullLeadDtoAsync(Domain.Entities.Lead lead, CancellationToken cancellationToken = default,Guid? currentUserId = null)
        {
            var leadDto = lead.Adapt<ViewLeadDto>();
            await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken,currentUserId:currentUserId);
            return leadDto;
        }

        public async Task SendLeadAssignmentNotificationsAsync(Domain.Entities.Lead lead, Domain.Entities.GlobalSettings? globalSettings, CancellationToken cancellationToken = default)
        {
            if (lead.AssignTo != default && lead.AssignTo != Guid.Empty && lead.AssignTo != _currentUserRepo.GetUserId())
            {
                var leadDto = await GetFullLeadDtoAsync(lead, cancellationToken);
                try
                {
                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadAssignment, lead, lead.AssignTo, leadDto.AssignedUser?.Name, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, globalSettings : globalSettings);
                    var tenantId = _currentUserRepo.GetTenant();
                    var currentUserId = _currentUserRepo.GetUserId();
                    await SendNotificationToAdminsAndReporteesAsync(lead, leadDto, globalSettings, tenantId, currentUserId);
                }
                catch (Exception ex)
                {
                    await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                }
            }
        }

        public async Task SendLeadUpdateNotificationsAsync(Domain.Entities.Lead lead, Domain.Entities.GlobalSettings? globalSettings, CancellationToken cancellationToken)
        {
            try
            {
                var leadDto = await GetFullLeadDtoAsync(lead, cancellationToken);
                var currentUserId = _currentUserRepo.GetUserId();
                await lead.SendLeadStatusChangeNotificationsAsync(leadDto, _notificationSenderService, globalSettings, _currentUserRepo.GetUserId());
                if (lead != null && lead.AssignTo != currentUserId)
                {
                    var userDetails = (await _userService.GetListOfUsersByIdsAsync(new List<string>() { lead.AssignTo.ToString() }, cancellationToken)).FirstOrDefault();
                    if (userDetails != null)
                    {
                        await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadInfoUpdate, lead, userDetails.Id, userDetails.FirstName + " " + userDetails.LastName, globalSettings: globalSettings);
                        var tenantId = _currentUserRepo.GetTenant();
                        var userInfo = (await _dapperRepository.GetUserDetailsAsync(tenantId ?? string.Empty, new List<Guid> { lead.AssignTo }))?.FirstOrDefault();
                        await SendNotificationToAdminsAndReporteesAsync(lead, leadDto, globalSettings, tenantId, currentUserId);
                    }
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
            }
        }

        protected async Task<(string ContactNo, string? AltContactNo)> ValidateContactNoAsync(string contactNo, string? alternateContactNo, Domain.Entities.GlobalSettings? globalSettings, CancellationToken cancellationToken)
        {
            //var globalSettingInfo = await _globalsettingRepo.ListAsync(new GetGlobalSettingsSpec(), cancellationToken);
            var hasInternationalSupportEnabled = globalSettings?.HasInternationalSupport ?? false;
            if (contactNo.Length == 10 && !hasInternationalSupportEnabled)
            {
                contactNo = $"+91{contactNo.Trim()}";
            }
            bool IsValidContactNo = PhoneNumberValidationHelper.ValidateContactNoUsingRegex(contactNo, hasInternationalSupportEnabled);
            if (!IsValidContactNo) { throw new Exception("Invalid ContactNo"); }
            if (alternateContactNo != null)
            {
                if (alternateContactNo.Length == 10 && !hasInternationalSupportEnabled)
                {
                    alternateContactNo = $"+91{alternateContactNo}";
                }
                IsValidContactNo = PhoneNumberValidationHelper.ValidateContactNoUsingRegex(alternateContactNo, hasInternationalSupportEnabled);
                if (!IsValidContactNo) { throw new Exception("Invalid AltNumber"); }
            }
            return (contactNo, alternateContactNo);
        }

        //protected async Task<MasterPropertyType?> ValidatePropertyTypeAsync(Guid? propertyTypeId, CancellationToken cancellationToken)
        //{
        //    MasterPropertyType? propertyType = null;
        //    if (propertyTypeId != Guid.Empty && propertyTypeId != null)
        //    {
        //        propertyType = await _propertyTypeRepo.GetByIdAsync(propertyTypeId ?? Guid.Empty, cancellationToken);
        //        if (propertyType == null)
        //        {
        //            throw new InvalidDataException("Property type Id does not belong to master data");
        //        }
        //    }
        //    return propertyType;
        //}
        protected async Task<List<MasterPropertyType?>> ValidatePropertyTypesAsync(List<Guid> propertyTypeIds, CancellationToken cancellationToken = default)
        {
            try
            {
                List<MasterPropertyType>? propertyType = null;

                if (propertyTypeIds != null && propertyTypeIds.Any())
                {
                    propertyType = await _propertyTypeRepo.ListAsync(new GetMasterPropertyTypeSpec(propertyTypeIds), cancellationToken);
                }

                return propertyType;
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task<Address?> CreateAddressAsync(AddressDto? addressDto, CancellationToken cancellationToken = default)
        {
            try
            {
                Address? address = null;
                if (addressDto?.LocationId != null && addressDto?.LocationId != Guid.Empty)
                {
                    address = await _addressRepo.FirstOrDefaultAsync(new AddressByLocationIdSpec(new() { addressDto?.LocationId ?? Guid.Empty }), cancellationToken);
                    if (address == null)
                    {
                        var location = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(addressDto?.LocationId ?? Guid.Empty), cancellationToken);
                        if (location != null)
                        {
                            address = location.MapToAddress();
                            if (address != null)
                            {
                                address.Id = Guid.Empty;
                                address = await _addressRepo.AddAsync(address);
                            }
                        }
                    }
                }
                else if (!string.IsNullOrWhiteSpace(addressDto?.PlaceId) && address == null)
                {
                    address = (await _addressRepo.ListAsync(new GetAddressByPlaceIdSpec(addressDto.PlaceId), cancellationToken)).FirstOrDefault();
                    if (address == null)
                    {
                        try
                        {
                            address = (await _googlePlacesService.GetPlaceDetailsByPlaceIdAsync(addressDto.PlaceId))?.Adapt<Address>() ?? null;
                        }
                        catch (Exception ex)
                        {
                            await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                        }
                        if (address != null)
                        {
                            address = await _addressRepo.AddAsync(address);
                            await MapAddressToLocationAndSaveAsync(address);
                        }
                    }
                }
                else if (double.TryParse(addressDto?.Longitude ?? "0", out double lng) && lng > 0 && double.TryParse(addressDto?.Latitude ?? "0", out double lat) && lat > 0)
                {
                    var places = await _googlePlacesService.GetPlaceDetailsByCoordinatesAsync(lat, lng);
                    var place = places.FirstOrDefault();
                    if (place != null && place.PlaceId != null)
                    {
                        address = (await _addressRepo.ListAsync(new GetAddressByPlaceIdSpec(place.PlaceId), cancellationToken)).FirstOrDefault();
                        if (address == null)
                        {
                            try
                            {
                                address = (await _googlePlacesService.GetPlaceDetailsByPlaceIdAsync(place.PlaceId))?.Adapt<Address>() ?? null;
                            }
                            catch (Exception ex)
                            {
                                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                            }
                            if (address != null)
                            {
                                address = await _addressRepo.AddAsync(address);
                                await MapAddressToLocationAndSaveAsync(address);
                            }
                        }
                    }
                    else if (place != null)
                    {
                        address = place.Adapt<Address>();
                        address = await _addressRepo.AddAsync(address);
                        await MapAddressToLocationAndSaveAsync(address);
                    }
                }
                else if (address == null && (addressDto?.Adapt<Address>()?.ValidateAddress(out Address? newAddress) ?? false))
                {
                    //if (newAddress != null)
                    //{
                    //    address = await _addressRepo.AddAsync(newAddress ?? new());
                    //    await MapAddressToLocationAndSaveAsync(address);
                    //}
                    if (newAddress != null)
                    {
                        var existingAddress = await _addressRepo.GetByIdAsync(newAddress.Id);
                        if (existingAddress == null)
                        {
                            address = await _addressRepo.AddAsync(newAddress);
                        }
                        else
                        {
                            address = existingAddress;
                        }
                        await MapAddressToLocationAndSaveAsync(address);
                    }
                }
                return address;
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }

        }
        protected async Task<List<Address>?> CreateAddressesAsync(List<AddressDto>? addressDtos, CancellationToken cancellationToken = default)
        {
            try
            {
                List<Address?> addresses = new List<Address?>();
                if (addressDtos != null)
                {
                    foreach (var addressDto in addressDtos)
                    {
                        Address? address = null;

                        if (addressDto?.LocationId != null && addressDto?.LocationId != Guid.Empty)
                        {
                            address = await _addressRepo.FirstOrDefaultAsync(new AddressByLocationIdSpec(new() { addressDto?.LocationId ?? Guid.Empty }), cancellationToken);
                            if (address == null)
                            {
                                var location = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(addressDto?.LocationId ?? Guid.Empty), cancellationToken);
                                if (location != null)
                                {
                                    address = location.MapToAddress();
                                    if (address != null)
                                    {
                                        address.Id = Guid.Empty;
                                        address = await _addressRepo.AddAsync(address);
                                    }
                                }
                                else if (address == null && (addressDto?.Adapt<Address>()?.ValidateAddress(out Address? newAddress) ?? false))
                                {
                                    if (newAddress != null)
                                    {
                                        newAddress.LocationId = null;
                                        var existingAddress = await _addressRepo.GetByIdAsync(newAddress.Id);
                                        if (existingAddress == null)
                                        {
                                            address = await _addressRepo.AddAsync(newAddress);
                                        }
                                        else
                                        {
                                            address = existingAddress;
                                        }
                                        await MapAddressToLocationAndSaveAsync(address);
                                    }
                                }
                            }
                        }
                        else if (!string.IsNullOrWhiteSpace(addressDto?.PlaceId) && address == null)
                        {
                            address = (await _addressRepo.ListAsync(new GetAddressByPlaceIdSpec(addressDto.PlaceId), cancellationToken)).FirstOrDefault();
                            if (address == null)
                            {
                                try
                                {
                                    address = (await _googlePlacesService.GetPlaceDetailsByPlaceIdAsync(addressDto.PlaceId))?.Adapt<Address>() ?? null;
                                }
                                catch (Exception ex)
                                {
                                    await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                                }
                                if (address != null)
                                {
                                    address = await _addressRepo.AddAsync(address);
                                    await MapAddressToLocationAndSaveAsync(address);
                                }
                            }
                        }
                        else if (double.TryParse(addressDto?.Longitude ?? "0", out double lng) && lng > 0 && double.TryParse(addressDto?.Latitude ?? "0", out double lat) && lat > 0)
                        {
                            var places = await _googlePlacesService.GetPlaceDetailsByCoordinatesAsync(lat, lng);
                            var place = places.FirstOrDefault();
                            if (place != null && place.PlaceId != null)
                            {
                                address = (await _addressRepo.ListAsync(new GetAddressByPlaceIdSpec(place.PlaceId), cancellationToken)).FirstOrDefault();
                                if (address == null)
                                {
                                    try
                                    {
                                        address = (await _googlePlacesService.GetPlaceDetailsByPlaceIdAsync(place.PlaceId))?.Adapt<Address>() ?? null;
                                    }
                                    catch (Exception ex)
                                    {
                                        await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                                    }
                                    if (address != null)
                                    {
                                        address = await _addressRepo.AddAsync(address);
                                        await MapAddressToLocationAndSaveAsync(address);
                                    }
                                }
                            }
                            else if (place != null)
                            {
                                address = place.Adapt<Address>();
                                address = await _addressRepo.AddAsync(address);
                                await MapAddressToLocationAndSaveAsync(address);
                            }
                        }
                        else if (address == null && (addressDto?.Adapt<Address>()?.ValidateAddress(out Address? newAddress) ?? false))
                        {
                            if (newAddress != null)
                            {
                                var existingAddress = await _addressRepo.GetByIdAsync(newAddress.Id);
                                if (existingAddress == null)
                                {
                                    address = await _addressRepo.AddAsync(newAddress);
                                }
                                else
                                {
                                    address = existingAddress;
                                }
                                await MapAddressToLocationAndSaveAsync(address);
                            }
                        }
                        if (address != null)
                        {
                            addresses.Add(address);
                        }
                    }
                }
                return addresses;
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        public static LeadTag CreateLeadTags(LeadTagDto? leadTags)
        {
            return leadTags?.Adapt<LeadTag>() ?? new();
        }

        public async Task<List<Domain.Entities.Lead>> SearchForDuplicateLeadsAsync(DuplicateLeadSpecDto lead, CancellationToken cancellationToken)
        {
            List<Domain.Entities.Lead> duplicateLeads = new();
            var duplicateFeatureInfo = (await _duplicateInfoRepo.ListAsync(cancellationToken)).FirstOrDefault();
            if (duplicateFeatureInfo != null && duplicateFeatureInfo.IsFeatureAdded)
            {
                if (!duplicateFeatureInfo.AllowAllDuplicates)
                {
                    duplicateLeads = await _leadRepo.ListAsync(new DuplicateFeatureSpec(duplicateFeatureInfo, lead), cancellationToken);
                    if (duplicateLeads.Any())
                    {
                        bool isDuplicate = true;
                        try
                        {
                            if (duplicateFeatureInfo.IsSourceBased && (duplicateFeatureInfo.Sources?.Any(i => i == (int)lead.LeadSource) ?? false))
                            {
                                isDuplicate = false;
                            }
                            if (duplicateFeatureInfo.IsProjectBased && lead?.ProjectsList?.Count > 1)
                            {
                                isDuplicate = lead.ProjectsList.All(item => duplicateLeads.Any(i => i.Projects != null && i.Projects.Select(i => i.Name?.ToLower() ?? string.Empty).Contains(item.ToLower())));
                            }
                        }
                        catch (Exception ex)
                        {
                            isDuplicate = false;
                            await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                        }
                        if (isDuplicate)
                        {
                            JsonSerializerSettings settings = new()
                            {
                                ReferenceLoopHandling = ReferenceLoopHandling.Ignore
                            };
                            throw new Exception("Lead with Same Enquiry is already present->> " + JsonConvert.SerializeObject(duplicateLeads, settings));
                        }
                    }
                }
            }
            else
            {
                duplicateLeads = await _leadRepo.ListAsync(new LeadByContactNoSpec(new List<string>() { lead.ContactNo != null && lead.ContactNo.Length >= 10 ? lead.ContactNo.Substring(lead.ContactNo.Length - 10) : "Invalid Number" }), cancellationToken);
                if (duplicateLeads.Any())
                {
                    JsonSerializerSettings settings = new()
                    {
                        ReferenceLoopHandling = ReferenceLoopHandling.Ignore
                    };
                    throw new Exception("Duplicate Lead ->> " + JsonConvert.SerializeObject(duplicateLeads, settings));
                }
            }
            return duplicateLeads;
        }


        public async Task InitializeLeadStatusAsync(Domain.Entities.Lead lead, CancellationToken cancellationToken)
        {
            if (lead != null)
            {
                var newStatus = await _leadStatusRepo.FirstOrDefaultAsync(new LeadStatusSpec("new"), cancellationToken);
                lead.CustomLeadStatus = newStatus;
            }
        }

        public async Task SetLeadStatusAsync(Domain.Entities.Lead lead, Guid statusId, CancellationToken cancellationToken = default)
        {
            var chaildStatuses = await _leadStatusRepo.ListAsync(new LeadStatusSpec(statusId), cancellationToken);
            if (chaildStatuses?.Any() ?? false)
            {
                throw new ArgumentException("Please provide child status id.");
            }
            var leadStatus = await _leadStatusRepo.GetByIdAsync(statusId, cancellationToken);
            if (leadStatus == null)
            {
                throw new ArgumentException("The Status Id is not valid.");
            }
            lead.CustomLeadStatus = leadStatus;
            lead.CustomLeadStatusId = leadStatus.Id;
            if (leadStatus != null && leadStatus.BaseId == Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
            {
                lead.ScheduledDate = null;
            }
        }

        public void SetLeadNameAndNumber(Domain.Entities.Lead lead, CancellationToken cancellationToken)
        {
            if (lead != null)
            {
                string name = string.IsNullOrWhiteSpace(lead.Name?.Trim()) ? "Unknwon" : lead.Name.Trim();
                lead.LeadNumber = name[0].ToString().ToUpper() + new Random().Next(1000, 9999).ToString() + DateTime.UtcNow.ToString("FFFFFFF");
            }
        }

        public async Task UpdateLeadTagInfoAsync(Domain.Entities.Lead lead, LeadTagDto? leadTagDto, CancellationToken cancellationToken)
        {
            var leadTags = leadTagDto?.Adapt<LeadTag>() ?? new();
            var existingTags = lead.TagInfo;
            if (existingTags != null)
            {
                existingTags.Update(leadTags);
                await _leadTagRepo.UpdateAsync(existingTags, cancellationToken);
            }
            else
            {
                leadTags.LeadId = lead.Id;
                await _leadTagRepo.AddAsync(leadTags, cancellationToken);
            }
        }

        public async Task AddLrbErrorAsync(Exception ex, string moduleName)
        {
            var error = new LrbError()
            {
                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                ErrorSource = ex?.Source,
                StackTrace = ex?.StackTrace,
                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                ErrorModule = moduleName
            };
            await _leadRepositoryAsync.AddErrorAsync(error);
        }
        private async Task MapAddressToLocationAndSaveAsync(Address address)
        {
            var location = address.MapToLocationRequest();
            if (location != null)
            {
                var locationRes = await _mediator.Send(location.Adapt<AddLocationRequest>());
                var createdLocation = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(locationRes.Data), default);
                address.Location = createdLocation;
                await _addressRepo.UpdateAsync(address);
            }
        }
        protected async Task<bool> ShouldUpdatePickedDate(Domain.Entities.Lead existingLead, PickedLeadDto? pickedLeadDto, bool? isLeadFormUpdated = null, Guid? currentUserId = null)
        {
            if (pickedLeadDto?.AssignTo != null && pickedLeadDto.AssignTo != Guid.Empty && existingLead.AssignTo != pickedLeadDto.AssignTo)
            {
                existingLead.PickedDate = null;
                existingLead.IsPicked = false;
                return false;
            }
            bool? shouldUpdatePickedDate = null;
            currentUserId = currentUserId ?? _currentUserRepo.GetUserId();
            var originalLead = existingLead.CreateDeepCopy();
            if (originalLead != null &&
                pickedLeadDto != null &&
                (currentUserId != default && currentUserId != Guid.Empty) &&
                originalLead.AssignTo != default &&
                ((originalLead.AssignTo == currentUserId || originalLead.SecondaryUserId == currentUserId)) &&
                originalLead.PickedDate == null)
            {
                shouldUpdatePickedDate = originalLead.GetPickedDateAsync(pickedLeadDto, isLeadFormUpdated: isLeadFormUpdated).Result;
            }
            return shouldUpdatePickedDate ?? false;
        }
        protected async Task UpdateLeadContactRecordsCountAsync(Domain.Entities.Lead lead, ContactType contactType, CancellationToken cancellationToken = default, bool? shouldUpdateContactRecord = null)
        {
            try
            {
                if (lead.ContactRecords != null && lead.ContactRecords.ContainsKey(contactType))
                {
                    lead.ContactRecords[contactType] += 1;
                }
                else if (lead.ContactRecords == null)
                {
                    lead.ContactRecords = new Dictionary<ContactType, int>() { { contactType, 1 } };
                }
                else
                {
                    lead.ContactRecords[contactType] = 1;
                }
                await _leadRepo.UpdateAsync(lead, cancellationToken);
                var leadDto = lead.Adapt<ViewLeadDto>();
                leadDto.ContactRecords = leadDto.ContactRecords?.Where(i => i.Key == contactType).ToDictionary(i => i.Key, i => i.Value);
                await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken);
                await UpdateLeadHistoryAsync(lead, leadDto, cancellationToken: cancellationToken, shouldUpdateContactRecord: shouldUpdateContactRecord);
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task<List<Domain.Entities.Lead>> MakeLeadsAsUnAssignedAsync(List<Domain.Entities.Lead> leads, CancellationToken cancellationToken, Guid? currentUserId = null)
        {
            leads = leads.Where(i => i.AssignTo != Guid.Empty).ToList();

            #region UpdatingPickedDate
            leads.ForEach(lead =>
            {
                lead.ShouldUpdatePickedDate = false;
                lead.PickedDate = null;
                lead.IsPicked = false;
            });
            #endregion

            foreach (var lead in leads)
            {
                lead.AssignedFrom = lead.AssignTo;
                lead.AssignTo = Guid.Empty;
                lead.SecondaryUserId = Guid.Empty;
                await _leadRepo.UpdateAsync(lead, cancellationToken);
                await UpdateLeadHistoryAsync(lead, cancellationToken: cancellationToken, currentUserId: currentUserId);
            }
            await SendLeadAssignmentNotificationsAsync(leads[0], leads.Count, cancellationToken);
            return leads;
        }
        protected async Task SendLeadAssignmentNotificationsAsync(Domain.Entities.Lead lead, int leadCount, CancellationToken cancellationToken = default)
        {
            try
            {
                if (lead != null && lead.AssignTo == Guid.Empty)
                {
                    List<Guid> adminIds = (await _npgsqlRepo.GetAdminIdsAsync(_currentUser.GetTenant() ?? string.Empty)).Where(i => i != _currentUserRepo.GetUserId()).ToList();
                    if (adminIds.Any())
                    {
                        foreach (var adminId in adminIds)
                        {
                            var adminDetails = await _userService.GetAsync(adminId.ToString(), cancellationToken);
                            if (adminDetails != null)
                            {
                                List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadMovedToUnassigned, lead, adminId, null, noOfEntities: leadCount);
                            }
                        }
                    }
                }
                else
                {
                    var assignedUser = await _userService.GetAsync(lead.AssignTo.ToString() ?? string.Empty, cancellationToken);
                    if (assignedUser != null && _currentUser.GetUserId() != assignedUser.Id)
                    {
                        if (leadCount > 1)
                        {
                            List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.MultipleLeadAssignment, lead, lead.AssignTo, assignedUser.FirstName + " " + assignedUser.LastName, null, leadCount);
                        }
                        else
                        {
                            List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadAssignment, lead, lead.AssignTo, assignedUser.FirstName + " " + assignedUser.LastName);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task<Domain.Entities.Lead> V2MakeLeadsAsUnAssignedAsync(Domain.Entities.Lead lead, Domain.Entities.GlobalSettings? globalSettings, CancellationToken cancellationToken)
        {

            #region UpdatingPickedDate

            lead.ShouldUpdatePickedDate = false;
            lead.PickedDate = null;
            lead.IsPicked = false;

            #endregion
            lead.AssignedFrom = lead.AssignTo;
            lead.AssignTo = Guid.Empty;
            lead.SecondaryUserId = Guid.Empty;
            await _leadRepo.UpdateAsync(lead, cancellationToken);
            await UpdateLeadHistoryAsync(lead, cancellationToken: cancellationToken);
            await SendLeadAssignmentNotificationsAsync(lead, globalSettings, cancellationToken);
            return lead;
        }


        protected async Task<(Domain.Entities.Lead Lead, List<DuplicateLeadAssigmentResponseDto> SkippedLeadsInfo)> V2ReassignLeadsAsync(Domain.Entities.Lead lead, LeadAssignmentDto assignmentDto, List<UserDetailsDto>? users, Domain.Entities.GlobalSettings? globalSettings, CancellationToken cancellationToken = default)
        {
            try
            {
                if (assignmentDto.UserIds.All(i => i == Guid.Empty))
                {
                    lead = await V2MakeLeadsAsUnAssignedAsync(lead, globalSettings, cancellationToken);
                    return (lead, new());
                }
                else
                {
                    if (users?.Any() ?? false)
                    {
                        List<DuplicateLeadAssigmentResponseDto> skippedLeadsDtos = new();
                        int i = 0;
                        var user = users[i];

                        if (lead.AssignTo != user.Id && lead.SecondaryUserId != user.Id)
                        {
                            #region UpdatingPickedDate

                            lead.ShouldUpdatePickedDate = false;
                            lead.PickedDate = null;
                            lead.IsPicked = false;

                            #endregion

                            lead.AssignedFrom = lead.AssignTo;
                            lead.AssignTo = user.Id;

                            await SetReassignedLeadDetailsAsync(lead, assignmentDto, cancellationToken);

                            await _leadRepo.UpdateAsync(lead, cancellationToken);

                            await UpdateReassignedLeadHistoryAsync(lead, assignmentDto.AssignmentType, users, cancellationToken);

                            i = i == users.Count - 1 ? 0 : i + 1;
                        }
                        else
                        {
                            if (skippedLeadsDtos.Any(i => i.User?.Id == user.Id))
                            {
                                skippedLeadsDtos.FirstOrDefault(i => i.User?.Id == user.Id)?.Leads?.Add(lead.Adapt<DuplicateAssigmentLeadDto>());
                            }
                            else
                            {
                                var skippedLeadsDto = new DuplicateLeadAssigmentResponseDto()
                                {
                                    User = new()
                                    {
                                        Id = user.Id,
                                        Name = user.FirstName + " " + user.LastName
                                    },
                                    Leads = new() { lead.Adapt<DuplicateAssigmentLeadDto>() }
                                };
                                skippedLeadsDtos.Add(skippedLeadsDto);
                            }
                        }

                        return (lead, skippedLeadsDtos);
                    }
                    else
                    {
                        throw new NotFoundException("No users found by the provided user ids.");
                    }
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task<(List<Domain.Entities.Lead> Leads, List<DuplicateLeadAssigmentResponseDto> SkippedLeadsInfo)> ReassignLeadsAsync(List<Domain.Entities.Lead> leads, LeadAssignmentDto assignmentDto, List<UserDetailsDto>? users, CancellationToken cancellationToken = default, Guid? currentUserId = null)
        {
            try
            {
                if (assignmentDto.UserIds.All(i => i == Guid.Empty))
                {
                    leads = await MakeLeadsAsUnAssignedAsync(leads, cancellationToken, currentUserId);
                    return (leads, new());
                }
                else
                {
                    if (users?.Any() ?? false)
                    {
                        List<DuplicateLeadAssigmentResponseDto> skippedLeadsDtos = new();
                        Dictionary<Guid, List<Domain.Entities.Lead>> groupedAssignedLeads = new();
                        int i = 0;
                        foreach (var lead in leads)
                        {
                            var user = users[i];

                            if (lead.AssignTo != user.Id && lead.SecondaryUserId != user.Id)
                            {
                                #region UpdatingPickedDate

                                lead.ShouldUpdatePickedDate = false;
                                lead.PickedDate = null;
                                lead.IsPicked = false;

                                #endregion
                                var previousAssignedUser = lead.AssignedFrom;
                                lead.AssignedFrom = lead.AssignTo;
                                lead.AssignTo = user.Id;

                                groupedAssignedLeads[lead.AssignTo] = new();
                                groupedAssignedLeads[lead.AssignTo].Add(lead);

                                await SetReassignedLeadDetailsAsync(lead, assignmentDto, cancellationToken);

                                await _leadRepo.UpdateAsync(lead, cancellationToken);

                                await UpdateReassignedLeadHistoryAsync(lead, assignmentDto.AssignmentType, users, cancellationToken, currentUserId: currentUserId,previousAssignedFrom:previousAssignedUser);

                                i = i == users.Count - 1 ? 0 : i + 1;
                            }
                            else
                            {
                                if (skippedLeadsDtos.Any(i => i.User?.Id == user.Id))
                                {
                                    skippedLeadsDtos.FirstOrDefault(i => i.User?.Id == user.Id)?.Leads?.Add(lead.Adapt<DuplicateAssigmentLeadDto>());
                                }
                                else
                                {
                                    var skippedLeadsDto = new DuplicateLeadAssigmentResponseDto()
                                    {
                                        User = new()
                                        {
                                            Id = user.Id,
                                            Name = user.FirstName + " " + user.LastName
                                        },
                                        Leads = new() { lead.Adapt<DuplicateAssigmentLeadDto>() }
                                    };
                                    skippedLeadsDtos.Add(skippedLeadsDto);
                                }
                            }
                        }

                        foreach (var group in groupedAssignedLeads)
                        {
                            await SendLeadAssignmentNotificationsAsync(group.Value[0], group.Value.Count, cancellationToken);
                        }

                        return (leads, skippedLeadsDtos);
                    }
                    else
                    {
                        throw new NotFoundException("No users found by the provided user ids.");
                    }
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task SetReassignedLeadDetailsAsync(Domain.Entities.Lead lead, LeadAssignmentDto assignmentDto, CancellationToken cancellationToken)
        {
            try
            {
                switch (assignmentDto.AssignmentType)
                {
                    case LeadAssignmentType.WithoutHistory:
                        if (lead.CustomFlags != null)
                        {
                            lead.CustomFlags = null;
                        }
                        await CreateLeadAssignmentHistory(lead, LeadAssignmentType.WithoutHistory, assignmentDto, cancellationToken);
                        break;
                    case LeadAssignmentType.WithoutHistoryWithNewStatus:
                        await InitializeLeadStatusAsync(lead, cancellationToken);
                        lead.ScheduledDate = null;
                        if (lead.CustomFlags != null)
                        {
                            lead.CustomFlags = null;
                        }
                        await CreateLeadAssignmentHistory(lead, LeadAssignmentType.WithoutHistoryWithNewStatus, assignmentDto, cancellationToken);
                        break;
                    case LeadAssignmentType.WithHistory:
                       // await UpdateLeadAppointmentAndCommunicationAsync(lead, cancellationToken);
                        await CreateLeadAssignmentHistory(lead, LeadAssignmentType.WithHistory, assignmentDto, cancellationToken);
                        break;
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        public async Task UpdateLeadAppointmentAndCommunicationAsync(Domain.Entities.Lead lead, CancellationToken cancellationToken = default)
        {
            try
            {
                if (lead.Appointments?.Where(i => i.UserId == lead.AssignedFrom).Any() ?? false)
                {
                    List<LeadAppointment> leadAppointments = new();
                    leadAppointments = lead.Appointments.Where(i => i.UserId == lead.AssignedFrom).ToList();
                    leadAppointments.ForEach(i => { i.Id = Guid.NewGuid(); i.UserId = lead.AssignTo; });
                    await _appointmentRepo.AddRangeAsync(leadAppointments, cancellationToken);
                }
                var leadcommunications = await _communicationRepo.ListAsync(new LeadCommunicationSpec(lead.Id, lead.AssignedFrom), cancellationToken);
                if (leadcommunications?.Any() ?? false)
                {
                    List<LeadCommunication> communications = new();
                    communications = leadcommunications;
                    communications.ForEach(i => { i.Id = Guid.NewGuid(); i.UserId = lead.AssignTo; });
                    await _communicationRepo.AddRangeAsync(communications, cancellationToken);
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task SetLeadAgencyAsync(Domain.Entities.Lead lead, List<string>? agencyList, CancellationToken cancellationToken = default)
        {
            try
            {
                lead.Agencies = null;
                List<Lrb.Domain.Entities.Agency>? agencies = new();
                agencyList = (agencyList?.Any() ?? false) ? agencyList.Where(i => !string.IsNullOrWhiteSpace(i)).Distinct().ToList() : null;
                
                if(lead.Enquiries.FirstOrDefault()?.LeadSource == LeadSource.QRCode)
                {
                    if (!string.IsNullOrWhiteSpace(lead.AgencyName))
                    {
                        if (agencyList?.Any() ?? false)
                        {
                            agencyList?.Add(lead.AgencyName);
                        }
                        else
                        {
                            agencyList = new() { lead.AgencyName };
                        }
                    }
                }

                if (agencyList?.Any() ?? false)
                {
                    foreach (var newAgency in agencyList)
                    {
                        Lrb.Domain.Entities.Agency? existingAgency = (await _agencyRepo.FirstOrDefaultAsync(new GetAgencyByNameSpec(newAgency), cancellationToken));
                        if (existingAgency != null)
                        {
                            agencies.Add(existingAgency);
                        }
                        else
                        {
                            Lrb.Domain.Entities.Agency agency = new() { Name = newAgency };
                            agency = await _agencyRepo.AddAsync(agency, cancellationToken);
                            agencies.Add(agency);
                        }
                    }
                    lead.Agencies = agencies;
                }
                else if ((lead?.Agencies?.Any() ?? false) && agencyList == null)
                {
                    lead.Agencies = agencies;
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task SetLeadCampaignAsync(Domain.Entities.Lead lead, List<string>? campaignList, Guid? templateId = null, CancellationToken cancellationToken = default)
        {
            try
            {
                lead.Campaigns = null;
                var template = await _qrFormTemplateRepo.GetByIdAsync(templateId ?? Guid.Empty, cancellationToken);

                if (!string.IsNullOrWhiteSpace(template?.Campaign?.Name ?? string.Empty))
                {
                    if (campaignList?.Any() ?? false)
                    {
                        campaignList?.Add(template.Campaign.Name);
                    }
                    else
                    {
                        campaignList = new() { template.Campaign.Name };
                    }
                }
                List<Lrb.Domain.Entities.Campaign>? campaigns = new();
                campaignList = (campaignList?.Any() ?? false) ? campaignList.Where(i => !string.IsNullOrWhiteSpace(i)).Distinct().ToList() : null;
                if (campaignList?.Any() ?? false)
                {
                    foreach (var newCampaign in campaignList)
                    {
                        Lrb.Domain.Entities.Campaign? existingCampaign = (await _campaignRepo.FirstOrDefaultAsync(new GetCampaignByNameSpec(newCampaign), cancellationToken));
                        if (existingCampaign != null)
                        {
                            campaigns.Add(existingCampaign);
                        }
                        else
                        {
                            Lrb.Domain.Entities.Campaign campaign = new() { Name = newCampaign };
                            campaign = await _campaignRepo.AddAsync(campaign, cancellationToken);
                            campaigns.Add(campaign);
                        }
                    }
                    lead.Campaigns = campaigns;
                }
                else if ((lead?.Campaigns?.Any() ?? false) && campaignList == null)
                {
                    lead.Campaigns = campaigns;
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task UpdateReassignedLeadHistoryAsync(Domain.Entities.Lead lead, LeadAssignmentType assignmentType, List<UserDetailsDto> users, CancellationToken cancellationToken = default, Guid? currentUserId = null,Guid? previousAssignedFrom = null)
        {
            var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignTo)) ?? await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, previousAssignedFrom ?? currentUserId ?? Guid.Empty));
            try
            {
                if (currentUserId != null && currentUserId != default)
                {
                    var currentUser = await _userService.GetAsync(currentUserId?.ToString() ?? string.Empty, cancellationToken);
                    if (currentUser != null && users.Any(i => i.Id != currentUser.Id))
                    {
                        users.Add(currentUser);
                    }
                }
            }
            catch (Exception ex) { }
            var leadDto = lead.Adapt<ViewLeadDto>();
            await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken);
            var newLeadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
            if (existingLeadHistory != null)
            {
                await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, newLeadHistory));
            }
            else
            {
                existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignedFrom ?? Guid.Empty));
                if (existingLeadHistory != null)
                {
                    switch (assignmentType)
                    {
                        case LeadAssignmentType.WithoutHistory:
                            await AddNewHistoryForLeadReassignmentAsync(existingLeadHistory, newLeadHistory, users, leadDto, false, cancellationToken, currentUserId);
                            break;
                        case LeadAssignmentType.WithoutHistoryWithNewStatus:
                            await AddNewHistoryForLeadReassignmentAsync(existingLeadHistory, newLeadHistory, users, leadDto, true, cancellationToken, currentUserId);
                            break;
                        case LeadAssignmentType.WithHistory:
                            await KeepOldHistoryForLeadReassignmentAsync(existingLeadHistory, leadDto, users, cancellationToken);
                            break;
                    }
                }
            }
        }
        public async Task KeepOldHistoryForLeadReassignmentAsync(LeadHistory existingLeadHistory, ViewLeadDto leadDto, List<UserDetailsDto> users, CancellationToken cancellationToken)
        {
            try
            {
                await leadDto.SetUsersInViewLeadDtoAsync(users, CancellationToken.None);
                var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                if (existingLeadHistory != null)
                {
                    await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory));
                }
                else
                {
                    await _leadHistoryRepo.AddAsync(leadHistory);
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }

        }

        public async Task KeepOldHistoryForLeadReassignmentAsync(LeadHistory existingLeadHistory, ViewLeadDto leadDto, List<UserDetailsDto> users, AppointmentType appointmentType, CancellationToken cancellationToken)
        {
            try
            {
                await leadDto.SetUsersInViewLeadDtoAsync(users, CancellationToken.None);
                var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                if (existingLeadHistory != null)
                {
                    await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, appointmentType));
                }
                else
                {
                    await _leadHistoryRepo.AddAsync(leadHistory);
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }

        }
        protected async Task<(Domain.Entities.Lead, string)> UpdateLeadCustomFlagsByIdAsync(Domain.Entities.Lead lead, Flag flag, CancellationToken cancellationToken = default)
        {
            try
            {
                var result = string.Empty;
                var userId = _currentUser.GetUserId();
                if (lead.CustomFlags?.Any(i => i.FlagId == flag.Id) ?? false)
                {
                    var leadFlag = lead.CustomFlags?.FirstOrDefault(i => i.FlagId == flag.Id);
                    if (leadFlag?.IsSelected ?? false)
                    {
                        lead.CustomFlags?.Remove(leadFlag);
                    }
                    result = "DeFlagged";
                }
                else
                {
                    var customFlag = new CustomFlag() { Lead = lead, LeadId = lead.Id, Flag = flag, FlagId = flag.Id, IsSelected = true };
                    customFlag.UserId = userId;
                    if (lead.CustomFlags != null)
                    {
                        lead.CustomFlags.Add(customFlag);
                    }
                    else
                    {
                        lead.CustomFlags = new List<CustomFlag>() { customFlag };
                    }
                    result = "Flagged";
                }
                return new(lead, result);
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }

        }
        protected async Task SendOnlyLeadInfoUpdateNotificationAsync(Domain.Entities.Lead lead, CancellationToken cancellationToken = default)
        {
            try
            {
                var currentUserId = _currentUserRepo.GetUserId();
                if (lead != null && lead.AssignTo != Guid.Empty && lead.AssignTo != currentUserId)
                {
                    var user = await _userService.GetAsync(lead.AssignTo.ToString(), cancellationToken);
                    if (user != null)
                    {
                        List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadInfoUpdate, lead, user.Id, user.FirstName + " " + user.LastName);
                    }
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task AddNewHistoryForLeadReassignmentAsync(LeadHistory existingLeadHistory, LeadHistory newHistory, List<UserDetailsDto> users, ViewLeadDto fullLeadDto, bool withNewStatus, CancellationToken cancellationToken = default, Guid? currentUserId = null)
        {
            try
            {
                var currentUser = users?.FirstOrDefault(i => i.Id == (currentUserId ?? _currentUser.GetUserId()));
                var assignedFromUser = users?.FirstOrDefault(i => i.Id == (fullLeadDto?.AssignedFrom ?? Guid.Empty));
                var assignToUser = users?.FirstOrDefault(i => i.Id == (fullLeadDto?.AssignTo ?? Guid.Empty));
                var leadHistory = existingLeadHistory.MapV1LeadHistory(currentUser, assignedFromUser, assignToUser, withNewStatus);
                leadHistory = LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, newHistory, leadHistory);
                await _leadHistoryRepo.AddAsync(leadHistory, cancellationToken);

                leadHistory = await UpdateHistoryForLeadReassignmentAsync(existingLeadHistory, assignedFromUser, assignToUser, currentUser);
                if (leadHistory != null)
                {
                    await _leadHistoryRepo.UpdateAsync(leadHistory, cancellationToken);
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        public async Task<LeadHistory?> UpdateHistoryForLeadReassignmentAsync(LeadHistory leadHistory, UserDetailsDto? assignedFromUser, UserDetailsDto? assignToUser, UserDetailsDto? currentUser)
        {
            try
            {
                if (leadHistory != null)
                {
                    var version = leadHistory.CurrentVersion + 1;
                    leadHistory.LastModifiedBy?.Add(version, $"{currentUser?.FirstName}{currentUser?.LastName}" ?? string.Empty);
                    leadHistory.AssignedTo?.Add(version, assignToUser?.Id ?? default);
                    leadHistory.AssignedFromUser?.Add(version, $"{assignedFromUser?.FirstName}{assignedFromUser?.LastName}" ?? string.Empty);
                    leadHistory.AssignedToUser?.Add(version, $"{assignToUser?.FirstName} {assignToUser?.LastName}" ?? string.Empty);
                    leadHistory.ModifiedDate?.Add(version, DateTime.UtcNow);
                    leadHistory.CurrentVersion = version;
                }
                return leadHistory;
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task SetReassignedLeadEnquiryAsync(Domain.Entities.Lead lead, LeadAssignmentDto assignmentDto, CancellationToken cancellationToken = default)
        {
            try
            {
                if (lead.Enquiries != null && lead.Enquiries.Any())
                {
                    var primaryEnquiry = lead.Enquiries.FirstOrDefault(e => e.IsPrimary);
                    if (primaryEnquiry != null)
                    {
                        if (assignmentDto.UpdateSubSource && !string.IsNullOrEmpty(assignmentDto.SubSource))
                        {
                            primaryEnquiry.SubSource = assignmentDto.SubSource;
                        }
                        if (assignmentDto.UpdateSource)
                        {
                            primaryEnquiry.LeadSource = assignmentDto.LeadSource;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }

        }

        protected async Task V2UpdateLeadStatusAsync(V2UpdateLeadStatusRequest request, Guid? currentUserId, CancellationToken cancellationToken = default,string? tenantId = null)
        {
            try
            {
                Domain.Entities.GlobalSettings? globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                var existingLead = (await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(request.Id), cancellationToken));
                if (existingLead != null)
                {
                    await UpdateAppointmentsAsync(request, existingLead, cancellationToken);
                    List<Address> diffrentLocationAddresses = new List<Address>();
                    if(request?.Addresses?.Any() ?? false)
                    {
                        foreach (var address in request.Addresses)
                        {
                            Address? diffrentLocationAddress = null;

                            if (address.LocationId != null && address.LocationId != Guid.Empty)
                            {
                                diffrentLocationAddress = await _addressRepo.FirstOrDefaultAsync(new AddressByLocationIdSpec(new() { address.LocationId ?? Guid.Empty }), cancellationToken);
                                if (diffrentLocationAddress == null)
                                {
                                    var location = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(address.LocationId ?? Guid.Empty), cancellationToken);
                                    if (location != null)
                                    {
                                        diffrentLocationAddress = location.MapToAddress();
                                        if (diffrentLocationAddress != null)
                                        {
                                            diffrentLocationAddress.Id = Guid.Empty;
                                            diffrentLocationAddress = await _addressRepo.AddAsync(diffrentLocationAddress);
                                        }
                                    }
                                }
                            }
                            else if (!string.IsNullOrEmpty(address.PlaceId) && diffrentLocationAddress == null)
                            {
                                diffrentLocationAddress = (await _addressRepo.ListAsync(new GetAddressByPlaceIdSpec(address.PlaceId), cancellationToken)).FirstOrDefault();
                                if (diffrentLocationAddress == null)
                                {
                                    try
                                    {
                                        diffrentLocationAddress = (await _googlePlacesService.GetPlaceDetailsByPlaceIdAsync(address.PlaceId))?.Adapt<Address>() ?? null;
                                    }
                                    catch (Exception ex) { }
                                    diffrentLocationAddress = await _addressRepo.AddAsync(diffrentLocationAddress);
                                    
                                }
                            }

                            else if (diffrentLocationAddress == null && (address.Adapt<Address>()?.Validate(out Address? newAddress) ?? false))
                            {
                                if (newAddress != null)
                                {
                                    var existingAddress = await _addressRepo.GetByIdAsync(address?.Id ?? Guid.Empty);
                                    if (existingAddress == null)
                                    {
                                        diffrentLocationAddress = await _addressRepo.AddAsync(newAddress);
                                    }
                                    else
                                    {
                                        diffrentLocationAddress = existingAddress;
                                    }
                                    await MapAddressToLocationAndSaveAsync(diffrentLocationAddress);
                                }
                            }
                            diffrentLocationAddresses.Add(diffrentLocationAddress);
                        }

                    }
                    
                    if (existingLead?.Enquiries?.FirstOrDefault(i => i.IsPrimary) !=null && (diffrentLocationAddresses?.Any() ?? false))
                    {
                        existingLead.Enquiries.FirstOrDefault(i => i.IsPrimary).Addresses = diffrentLocationAddresses;
                    }
                    #region projects
                    if (request.ProjectsList?.Any() ?? false)
                    {
                        List<Lrb.Domain.Entities.Project>? projects = new();
                        request.ProjectsList = request.ProjectsList.Where(i => !string.IsNullOrWhiteSpace(i)).ToList();
                        if (request?.ProjectsList?.Any() ?? false)
                        {
                            foreach (var newProject in request.ProjectsList)
                            {
                                Lrb.Domain.Entities.Project? existingProject = (await _projectRepo.ListAsync(new GetProjectByIdSpecsV2(newProject), cancellationToken)).FirstOrDefault();
                                if (existingProject != null)
                                {
                                    projects.Add(existingProject);
                                }
                                else
                                {
                                    Domain.Entities.Project project = new() { Name = newProject };
                                    project = await _projectRepo.AddAsync(project, cancellationToken);
                                    projects.Add(project);
                                }
                            }
                        }
                        existingLead.Projects = projects;
                    }

                    #endregion

                    #region Properties

                    if (request?.PropertiesList?.Any() ?? false)
                    {
                        List<Domain.Entities.Property>? properties = new();
                        foreach (var newProperty in request.PropertiesList)
                        {
                            var existingProperty = (await _propertyRepo.ListAsync(new GetPropertyByTitleSpec(newProperty), cancellationToken)).FirstOrDefault();
                            if (existingProperty != null)
                            {
                                properties.Add(existingProperty);
                            }
                            else
                            {
                                Domain.Entities.Property property = new() { Title = newProperty };
                                property = await _propertyRepo.AddAsync(property, cancellationToken);
                                properties.Add(property);
                            }
                        }
                        existingLead.Properties = properties;
                    }
                    #endregion
                    var baseLeadStatus = await _masterLeadStatusRepo?.GetByIdAsync(existingLead.CustomLeadStatus.BaseId, cancellationToken);
                    if (baseLeadStatus != null && baseLeadStatus?.Status == "booked" || existingLead.CustomLeadStatus?.Status == "booked" ||  existingLead.CustomLeadStatus?.Status == "invoiced" )
                    {
                        existingLead.BookedBy = currentUserId ?? Guid.Empty;
                        var userName = await _userService.GetAsync(existingLead.BookedBy.ToString() ?? string.Empty, cancellationToken);
                        var bookedDetailInfo = await _leadBookedDetailRepo.FirstOrDefaultAsync(new GetBookedDetailsByIdSpec(existingLead.Id), cancellationToken);
                        if (bookedDetailInfo != null)
                        {
                            try
                            {
                                bookedDetailInfo.BookedDate = existingLead?.BookedDate;
                                bookedDetailInfo.BookedBy = existingLead?.BookedBy;
                                if (userName != null)
                                {
                                    bookedDetailInfo.BookedByUser = userName.FirstName + " " + userName.LastName;
                                }
                                bookedDetailInfo.BookedUnderName = existingLead?.BookedUnderName;
                                bookedDetailInfo.UserId = existingLead?.AssignTo ?? Guid.Empty;
                                bookedDetailInfo.SoldPrice = request?.SoldPrice;
                                bookedDetailInfo.Notes = request?.Notes;
                                bookedDetailInfo.ProjectsList = request?.ProjectsList;
                                bookedDetailInfo.PropertiesList = request?.PropertiesList;
                                bookedDetailInfo.AgreementValue = request?.AgreementValue ?? default;
                                bookedDetailInfo.Properties = await _propertyRepo.ListAsync(new GetPropertiesByIdspecs(request?.PropertyIds ?? new()));
                                bookedDetailInfo.Projects = await _projectRepo.ListAsync(new ProjectByIdsSpec(request?.ProjectIds ?? new()), cancellationToken);
                                bookedDetailInfo.IsChoosenProperty = request?.IsChoosenProperty ?? false;
                                bookedDetailInfo.Currency = request?.Currency ?? default;
                                bookedDetailInfo.UnitType = await _unitType.FirstOrDefaultAsync(new GetUnitInfoSpecs(request?.UnitTypeId ?? Guid.Empty)) ?? null;
                                bookedDetailInfo.IsBookingCompleted = request?.IsBookingCompleted ?? bookedDetailInfo.IsBookingCompleted;
                                await _leadBookedDetailRepo.UpdateAsync(bookedDetailInfo);
                                if (request?.AssignTo != null)
                                {
                                    existingLead.AssignTo = request?.AssignTo ?? existingLead.AssignTo;                                   
                                }
                                if (request?.SecondaryUserId != null)
                                {
                                    existingLead.SecondaryUserId = request?.SecondaryUserId ?? existingLead.SecondaryUserId;                                   
                                }
                                await _leadRepo.UpdateAsync(existingLead);
                            }
                            catch (Exception ex)
                            {
                                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                                throw;
                            }

                        }
                        else
                        {
                            try
                            {
                                LeadBookedDetail bookedDetail = new();
                                bookedDetail.LeadId = existingLead.Id;
                                bookedDetail.BookedDate = existingLead?.BookedDate;
                                bookedDetail.BookedBy = existingLead?.BookedBy;
                                if (userName != null)
                                {
                                    bookedDetail.BookedByUser = userName?.FirstName + " " + userName?.LastName;
                                }
                                bookedDetail.BookedUnderName = existingLead?.BookedUnderName;
                                bookedDetail.UserId = existingLead?.AssignTo ?? Guid.Empty;
                                bookedDetail.SoldPrice = request?.SoldPrice;
                                bookedDetail.Notes = request?.Notes;
                                bookedDetail.ProjectsList = request?.ProjectsList;
                                bookedDetail.PropertiesList = request?.PropertiesList;
                                bookedDetail.AgreementValue = request?.AgreementValue ?? default;
                                bookedDetail.Properties = await _propertyRepo.ListAsync(new GetPropertiesByIdspecs(request?.PropertyIds ?? new()));
                                bookedDetail.Projects = await _projectRepo.ListAsync(new ProjectByIdsSpec(request?.ProjectIds ?? new()), cancellationToken);
                                bookedDetail.IsChoosenProperty = request?.IsChoosenProperty ?? false;
                                bookedDetail.Currency = request?.Currency ?? default;
                                bookedDetail.UnitType = await _unitType.FirstOrDefaultAsync(new GetUnitInfoSpecs(request?.UnitTypeId ?? Guid.Empty)) ?? null;
                                bookedDetail.IsBookingCompleted = request?.IsBookingCompleted ?? false;
                                existingLead?.BookedDetails?.Add(bookedDetail);
                                await _leadBookedDetailRepo.AddAsync(bookedDetail);
                                if (request?.AssignTo != null)
                                {
                                    existingLead.AssignTo = request?.AssignTo ?? existingLead.AssignTo;
                                }
                                if (request?.SecondaryUserId != null)
                                {
                                    existingLead.SecondaryUserId = request?.SecondaryUserId ?? existingLead.SecondaryUserId;
                                }
                                await _leadRepo.UpdateAsync(existingLead);
                            }
                            catch (Exception ex)
                            {
                                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                                throw;
                            }
                        }
                    }

                    if ((request?.IsFullyCompleted ?? false) && (request?.Projects?.Any() ?? false))
                    {
                        var appointments = await _appointmentRepo.ListAsync(new GetAppointmentsByProjectsSpec(request?.Id ?? Guid.Empty, (request?.Projects?.ConvertAll(i => i.Trim().ToLower()) ?? new List<string>())));
                        appointments?.ForEach(i => i.IsFullyCompleted = true);
                        if (appointments?.Any() ?? false)
                        {
                            try
                            {
                                await _appointmentRepo.UpdateRangeAsync(appointments);
                            }
                            catch (Exception ex)
                            {
                                var error = new LrbError()
                                {
                                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                    ErrorSource = ex?.Source,
                                    StackTrace = ex?.StackTrace,
                                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                    ErrorModule = "UpdateSiteVisitOrMeetingDoneRequestHandler -> Handle()"
                                };
                                await _leadRepositoryAsync.AddErrorAsync(error);
                            }
                        }
                    }
                    var fullLead = (await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(request.Id), cancellationToken));
                    var leadDto = fullLead?.Adapt<ViewLeadDto>();
                    if((leadDto?.Status != null) && (string.IsNullOrEmpty(leadDto?.Status?.DisplayName)))
                    {
                        leadDto.Status.DisplayName = baseLeadStatus?.DisplayName;
                    }
                    await leadDto?.SetUsersInViewLeadDtoAsync(_userService, cancellationToken,currentUserId:currentUserId);
                    await UpdateLeadHistoryAsync(fullLead, leadDto, currentUserId: currentUserId, cancellationToken: cancellationToken, shouldUpdateNotes: request.IsNotesUpdated ?? false);

                    try
                    {
                        await existingLead.SendLeadStatusChangeNotificationsAsync(leadDto, _notificationSenderService, globalSettings, currentUserId ?? Guid.Empty);
                        Event? @event = null;
                        if (request.MeetingOrSiteVisit != AppointmentType.None)
                        {
                            switch (request.MeetingOrSiteVisit)
                            {
                                case AppointmentType.SiteVisit:
                                    switch (request.IsDone)
                                    {
                                        case true:
                                            @event = Event.LeadSiteVisitDone;
                                            break;
                                        case false:
                                            @event = Event.LeadSiteVisitNotDone;
                                            break;
                                    }
                                    break;
                                case AppointmentType.Meeting:
                                    switch (request.IsDone)
                                    {
                                        case true:
                                            @event = Event.LeadMeetingDone;
                                            break;
                                        case false:
                                            @event = Event.LeadMeetingNotDone;
                                            break;
                                    }
                                    break;
                            }
                            if (@event != null)
                            {
                                Event updatedEvent = (Event)@event;
                                var userDetails = await _userService.GetAsync(existingLead.AssignTo.ToString(), cancellationToken);
                                if (userDetails.Id != currentUserId && currentUserId != Guid.Empty)
                                {
                                    await _notificationSenderService.ScheduleNotificationsAsync(updatedEvent, existingLead, userDetails.Id, userDetails.FirstName + " " + userDetails.LastName, currentUserId: currentUserId);
                                    await SendNotificationToAdminsAndReporteesAsync(existingLead, leadDto, globalSettings, tenantId, currentUserId ?? leadDto.LastModifiedBy);
                                }

                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "UpdateLeadStatusRequestHandler -> Handle()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("UpdateLeadStatusRequestHandler exception details  => " + ex.Serialize());
                Log.Information("UpdateLeadStatusRequestHandler exception details  => " + ex.Serialize());
                throw;
            }
        }

        public async Task UpdateAppointmentsAsync(V2UpdateLeadStatusRequest request, Domain.Entities.Lead existingLead, CancellationToken cancellationToken)
        {
            #region Updating Meeting and SiteVisit Done
            if (request.MeetingOrSiteVisit != AppointmentType.None)
            {
                try
                {
                    var appointment = request.Adapt<LeadAppointment>();
                    appointment.Id = Guid.Empty;
                    appointment.UserId = existingLead.AssignTo;
                    switch (request.MeetingOrSiteVisit)
                    {
                        case AppointmentType.Meeting:
                            existingLead.IsMeetingDone = request.IsDone;
                            appointment.Type = AppointmentType.Meeting;
                            var places = await _googlePlacesService.GetPlaceDetailsByCoordinatesAsync(request.Latitude, request.Longitude);
                            var place = places.FirstOrDefault();
                            if (place != null && place.PlaceId != null)
                            {
                                var address = (await _addressRepo.ListAsync(new GetAddressByPlaceIdSpec(place.PlaceId), cancellationToken)).FirstOrDefault();
                                if (address != null)
                                {
                                    existingLead.MeetingLocation = address.Id;
                                    appointment.Location = address;
                                }
                                else
                                {
                                    address = place.Adapt<Address>();
                                    address = await _addressRepo.AddAsync(address);
                                    existingLead.MeetingLocation = address.Id;
                                    appointment.Location = address;
                                }
                            }
                            break;
                        case AppointmentType.SiteVisit:
                            existingLead.IsSiteVisitDone = request.IsDone;
                            appointment.Type = AppointmentType.SiteVisit;
                            places = await _googlePlacesService.GetPlaceDetailsByCoordinatesAsync(request.Latitude, request.Longitude);
                            place = places.FirstOrDefault();
                            if (place != null && place.PlaceId != null)
                            {
                                var address = (await _addressRepo.ListAsync(new GetAddressByPlaceIdSpec(place.PlaceId), cancellationToken)).FirstOrDefault();
                                if (address != null)
                                {
                                    existingLead.SiteLocation = address.Id;
                                    appointment.Location = address;
                                }
                                else
                                {
                                    address = place.Adapt<Address>();
                                    address = await _addressRepo.AddAsync(address);
                                    existingLead.SiteLocation = address.Id;
                                    appointment.Location = address;
                                }
                            }
                            break;
                    }
                    if (existingLead.Appointments?.Any() ?? false)
                    {
                        existingLead.Appointments.Add(appointment);
                    }
                    else
                    {
                        existingLead.Appointments = new List<LeadAppointment>() { appointment };
                    }
                }
                catch (Exception ex)
                {
                    var error = new LrbError()
                    {
                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                        ErrorSource = ex?.Source,
                        StackTrace = ex?.StackTrace,
                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                        ErrorModule = "UpdateLeadStatusRequestHandler -> Handle()"
                    };
                    await _leadRepositoryAsync.AddErrorAsync(error);
                }
            }
            #endregion
        }
        protected async Task V2SeedAttendanceSettingAsync(AttendanceSettingObject request, Guid? currentUserId,string tenantId, CancellationToken cancellationToken = default)
        {
            var isSuccess = await _userService.UpdateOrAddAttendanceClaimAsync(request?.IsEnabledForAllUsers, request?.UserIds, request.Claim, tenantId, cancellationToken);
        }

        protected async Task SetQRSubSourceAsync(Domain.Entities.Lead lead, Guid? templateId, CancellationToken cancellationToken)
        {
            var enquiry = lead.Enquiries.FirstOrDefault();
            if (templateId != null && templateId != Guid.Empty && enquiry?.LeadSource == LeadSource.QRCode)
            {
                var template = await _qrFormTemplateRepo.GetByIdAsync(templateId ?? Guid.Empty, cancellationToken);
                if (template != null)
                {
                    enquiry.SubSource = template.Name?.ToLower();
                }
            }
        }
        public record InputPayload(string TenantId, Guid CurrentUserId, string Type, object Entity);
        protected async Task<bool> CreateLeadAssignmentHistory(Domain.Entities.Lead lead, LeadAssignmentType assignmentType, LeadAssignmentDto? assignmentDto = null, CancellationToken cancellationToken = default, bool isDuplicate = default)
        {
            var leadAssignmentHistories = await _leadAssignmentRepo.ListAsync(new GetLeadAssignmentsByIdSpecs(lead.Id));
            if(leadAssignmentHistories == null)
            {
                var assignment = new LeadAssignment()
                {
                    AssignTo = lead.AssignTo,
                    AssignedFrom = lead.AssignedFrom,
                    Notes = lead.Notes,
                    LeadId = lead.Id,
                    UserId = lead.AssignTo,
                    LeadAssignmentType = assignmentType,
                    AssignmentDate = DateTime.UtcNow,
                };
                if (lead.SecondaryUserId != null || lead.SecondaryUserId != Guid.Empty)
                {
                    assignment.SecondaryAssignTo = lead.SecondaryUserId;
                }
                if (lead.SecondaryFromUserId != null || lead.SecondaryFromUserId != Guid.Empty)
                {
                    assignment.SecondaryAssignFrom = lead.SecondaryFromUserId;
                }
                if (assignmentType == LeadAssignmentType.WithHistory && assignmentDto != null)
                {
                    assignment.ProjectName = assignmentDto?.Projects?.FirstOrDefault();
                    assignment.SourceName = assignmentDto?.LeadSource.ToString();
                }
                if (isDuplicate)
                {
                    assignment.IsDuplicate = true;
                }
                await _leadAssignmentRepo.AddAsync(assignment, cancellationToken);
            }
            else
            {
                var leadLastAssignment = leadAssignmentHistories.LastOrDefault();
                if(leadLastAssignment?.AssignTo != lead?.AssignTo)
                {
                    var assignment = new LeadAssignment()
                    {
                        AssignTo = lead.AssignTo,
                        AssignedFrom = lead.AssignedFrom,
                        Notes = lead.Notes,
                        LeadId = lead.Id,
                        UserId = lead.AssignTo,
                        LeadAssignmentType = assignmentType,
                        AssignmentDate = DateTime.UtcNow,
                    };
                    if (lead.SecondaryUserId != null || lead.SecondaryUserId != Guid.Empty)
                    {
                        assignment.SecondaryAssignTo = lead.SecondaryUserId;
                    }
                    if (lead.SecondaryFromUserId != null || lead.SecondaryFromUserId != Guid.Empty)
                    {
                        assignment.SecondaryAssignFrom = lead.SecondaryFromUserId;
                    }
                    if (assignmentType == LeadAssignmentType.WithHistory && assignmentDto != null)
                    {
                        assignment.ProjectName = assignmentDto?.Projects?.FirstOrDefault();
                        assignment.SourceName = assignmentDto?.LeadSource.ToString();
                    }
                    if (isDuplicate)
                    {
                        assignment.IsDuplicate = true;
                    }
                    await _leadAssignmentRepo.AddAsync(assignment, cancellationToken);
                }
                else if (lead?.SecondaryUserId != null && leadLastAssignment?.SecondaryAssignTo != lead.SecondaryUserId)
                {
                    var assignment = new LeadAssignment()
                    {
                        AssignTo = lead.AssignTo,
                        AssignedFrom = lead.AssignedFrom,
                        Notes = lead.Notes,
                        LeadId = lead.Id,
                        UserId = lead.AssignTo,
                        LeadAssignmentType = assignmentType,
                        AssignmentDate = DateTime.UtcNow,
                    };
                    if (lead.SecondaryUserId != null || lead.SecondaryUserId != Guid.Empty)
                    {
                        assignment.SecondaryAssignTo = lead.SecondaryUserId;
                    }
                    if (lead.SecondaryFromUserId != null || lead.SecondaryFromUserId != Guid.Empty)
                    {
                        assignment.SecondaryAssignFrom = lead.SecondaryFromUserId;
                    }
                    if (assignmentType == LeadAssignmentType.WithHistory && assignmentDto != null)
                    {
                        assignment.ProjectName = assignmentDto?.Projects?.FirstOrDefault();
                        assignment.SourceName = assignmentDto?.LeadSource.ToString();
                    }
                    if (isDuplicate)
                    {
                        assignment.IsDuplicate = true;
                    }
                    await _leadAssignmentRepo.AddAsync(assignment, cancellationToken);
                }
            }
            return true;
        }
        public async Task SendNotificationToAdminsAndReporteesAsync(Domain.Entities.Lead lead, ViewLeadDto? leadDto, Domain.Entities.GlobalSettings globalSettings, string tenantId, Guid currentUserId)
        {
            leadDto ??= await GetFullLeadDtoAsync(lead, CancellationToken.None, currentUserId);
            var userInfo = (await _dapperRepository.GetUserDetailsAsync(tenantId ?? string.Empty, new List<Guid> { lead.AssignTo }))?.FirstOrDefault();
            List<Guid> adminIds = (await _npgsqlRepo.GetAdminIdsAsync(tenantId ?? string.Empty)).Where(i => i != (currentUserId)).ToList();
            var notificationSetting = JsonConvert.DeserializeObject<NotificationSettings>(globalSettings?.NotificationSettings ?? string.Empty) ?? new();
            if ((notificationSetting != null) && notificationSetting.IsAdminEnabled && (adminIds?.Any() ?? false))
            {
                List<string> notificationSchduleResponses = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, lead, null, leadDto.AssignedUser?.Name, globalSettings: globalSettings, userIds: adminIds);
            }
            if ((notificationSetting != null) && notificationSetting.IsManagerEnabled && (userInfo != null) && (userInfo?.ReportsTo != null) && (userInfo.ReportsTo.Id != Guid.Empty) && !(adminIds != null && adminIds.Any(i => userInfo.ReportsTo.Id == i)) && (userInfo?.ReportsTo.Id != currentUserId))
            {
                List<Guid> userIds = new() { userInfo.ReportsTo.Id };
                List<string> notificationSchduleResponses = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, lead, null, leadDto.AssignedUser?.Name, globalSettings: globalSettings, userIds: userIds);
            }
            if ((notificationSetting != null) && notificationSetting.IsGeneralManagerEnabled && (userInfo != null) && (userInfo?.GeneralManager != null) && (userInfo.GeneralManager.Id != Guid.Empty) && !(adminIds != null && adminIds.Any(i => userInfo.GeneralManager.Id == i)) && (userInfo.GeneralManager.Id != currentUserId))
            {
                List<Guid> userIds = new() { userInfo.GeneralManager.Id };
                List<string> notificationSchduleResponses = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, lead, null, leadDto.AssignedUser?.Name, globalSettings: globalSettings, userIds: userIds);
            }
        }

    }
}
