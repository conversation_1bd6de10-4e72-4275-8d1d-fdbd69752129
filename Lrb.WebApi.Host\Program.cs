using Amazon.XRay.Recorder.Core;
using AWS.Logger;
using AWS.Logger.SeriLog;
using FluentValidation.AspNetCore;
using Lrb.Application;
using Lrb.Application.Common.Services;
using Lrb.Infrastructure;
using Lrb.Infrastructure.Common;
using Lrb.Shared.Logger;
using Lrb.WebApi.Host.Configurations;
using Microsoft.AspNetCore.Http.Connections;
using Serilog;
using System.Text.Json.Serialization;
using Azure.Identity;
using StackExchange.Redis;
using Lrb.Infrastructure.Caching;
using Microsoft.Extensions.Options;
using Lrb.Infrastructure.Middleware;

StaticLogger.EnsureInitialized();
Log.Information("Server Booting Up...");
try
{
    var builder = WebApplication.CreateBuilder(args);

    builder.Host.AddConfigurations();
    #region Logger
    //builder.Host.UseSerilog((_, config) =>
    //{
    //    config.WriteTo.Console()
    //    .ReadFrom.Configuration(builder.Configuration);
    //});
    AWSLoggerConfig configuration = new AWSLoggerConfig()
    {
        Region = builder.Configuration["Serilog:Region"],
        Credentials = new SerilogAWSCredentials(),
        LogGroup = builder.Configuration["Serilog:LogGroup"],
    };

    //Initialize Logger
    var logger = Log.Logger = new LoggerConfiguration()
        .ReadFrom.Configuration(builder.Configuration)
        .WriteTo.AWSSeriLog(configuration)
        .CreateLogger();
    builder.Logging.AddSerilog(logger);
    builder.Host.UseSerilog(Log.Logger);

    // Bind Redis settings
    builder.Services.Configure<RedisStackSettings>(
    builder.Configuration.GetSection("RedisStackSettings"));

   
    #endregion
    builder.Services.AddControllersWithViews()
    .AddJsonOptions(options => options.JsonSerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles);

    builder.Services.AddControllers()
        .AddXmlSerializerFormatters();

    builder.Services.AddApplicationInsightsTelemetry();

    builder.Services.AddFluentValidation();
    //builder.Services.AddFluentValidationAutoValidation().AddFluentValidationClientsideAdapters();
    builder.Services.AddInfrastructureForWeb(builder.Configuration);
    builder.Services.AddApplication();
    AWSXRayRecorder.InitializeInstance(builder.Configuration);
    builder.Services.AddResponseCaching();
    // builder.Services.AddHttpClient("XRayTraced").AddHttpMessageHandler<TracingMessageHandler>(); 
    builder.Services.AddSignalR(o =>
    {
        //o.AddFilter<TenantHubFilter>();
        o.EnableDetailedErrors = true;
    });

    var app = builder.Build();
    await app.Services.InitializeDatabasesAsync();
    app.Services.ConfigureMappings();
    app.Services.Conig();
    app.UseInfrastructure(builder.Configuration);
    app.UseDuplicateRequestMiddleware();
    app.UseResponseCaching();
    app.MapEndpoints();
    app.MapHub<DialerHub>("/dialer-hub", options =>
    {
        options.Transports =
            HttpTransportType.WebSockets |
            HttpTransportType.LongPolling;
    });
    app.Run();
}
catch (Exception ex) when (!ex.GetType().Name.Equals("StopTheHostException", StringComparison.Ordinal))
{
    StaticLogger.EnsureInitialized();
    Log.Fatal(ex, "Unhandled exception");
}
finally
{
    StaticLogger.EnsureInitialized();
    Log.Information("Server Shutting down...");
    Log.CloseAndFlush();
}