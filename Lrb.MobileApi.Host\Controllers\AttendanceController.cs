﻿using Lrb.Application.Attendance.Mobile;
using Lrb.Application.Attendance.Mobile.Dtos;
using Lrb.Application.Attendance.Mobile.Requests;
using Lrb.Domain.Entities;
using Mapster;
using MediatR;

namespace Lrb.MobileApi.Host.Controllers
{
    [Authorize]
    public class AttendanceController : VersionedApiController
    {
        private readonly IMediator _mediator;
        public AttendanceController(IMediator mediator)
        {
            _mediator = mediator;
        }
        [HttpPost("clockin")]
        [TenantIdHeader]
        [OpenApiOperation("ClockIn", "")]
        public async Task<IActionResult> ClockIn([FromBody] CreateClockInRequest request)
        {
            //CreateClockInRequest request = clockInData.Adapt<CreateClockInRequest>();
            var result = await Mediator.Send(request);
            return Ok(result);
        }

        [HttpPost("clockout")]
        [TenantIdHeader]
        [OpenApiOperation("ClockOut", "")]
        public async Task<IActionResult> ClockOut([FromBody] ClockInOutDTO clockOutData)
        {
            CreateClockOutRequest request = clockOutData.Adapt<CreateClockOutRequest>();
            var result = await Mediator.Send(request);
            return Ok(result);
        }

        [HttpGet("{userId}")]
        [TenantIdHeader]
        [OpenApiOperation("Get Logs", "")]
        public async Task<IActionResult> GetTodaysAttendanceLogs(string timeZoneId)
        {
            GetTodaysAttendanceLogByUserIdRequest request = new GetTodaysAttendanceLogByUserIdRequest()
            {
                TimeZoneId = timeZoneId
            };
            var result = await Mediator.Send(request);
            return Ok(result);
        }
        [HttpPut("logs")]
        [TenantIdHeader]
        [OpenApiOperation("Update Attendance Logs.", "")]
        public async Task<IActionResult> UpdateLogs(UpdateAttendanceLogsRequest request)
        {
            var time = DateTime.Now;
            return Ok(await Mediator.Send(request));
        }
        [HttpGet("count/{userId}")]
        [TenantIdHeader]
        [OpenApiOperation("Get Logs Count", "")]
        public async Task<IActionResult> GetTodaysAttendanceLogsCount(string timeZoneId)
        {
            GetTodaysAttendanceLogCountByUserIdRequest request = new GetTodaysAttendanceLogCountByUserIdRequest()
            {
                TimeZoneId = timeZoneId
            };
            var result = await Mediator.Send(request);
            return Ok(result);
        }
        [HttpGet("dapper/{userId}")]
        [TenantIdHeader]
        [OpenApiOperation("Get Logs by Dapper", "")]
        public async Task<IActionResult> GetTodaysAttendanceLogsByDapper(string timeZoneId)
        {
            GetTodaysAttendanceLogByUserIdDapperRequest request = new GetTodaysAttendanceLogByUserIdDapperRequest()
            {
                TimeZoneId = timeZoneId
            };
            var result = await Mediator.Send(request);
            return Ok(result);
        }
        [HttpGet("logs/all")]
        [TenantIdHeader]
        [OpenApiOperation("Get All Logs By User", "")]
        public async Task<IActionResult> GetAllLogs([FromQuery] GetAllAttendanceLogsRequest request)
        {
            return Ok(await _mediator.Send(request));
        }

        [HttpGet("log")]
        [TenantIdHeader]
        [OpenApiOperation("Get single log", "")]
        public async Task<Response<AttendanceLogDto>> GetTodayLog()
        {
            return await _mediator.Send(new GetTodaysAttendanceLogRequest());
        }
        [HttpGet("settings")]
        [TenantIdHeader]
        // [MustHavePermission(LrbAction.ViewAll, LrbResource.Attendance)]
        [OpenApiOperation("Get Attendance Settings.", "")]
        public async Task<Response<AttendanceSettingsDto>> GetAttendanceSettingAsync()
        {
            return await _mediator.Send(new GetAttendanceSettingsRequest());
        }
        [HttpPut("settings")]
        [TenantIdHeader]
        // [MustHavePermission(LrbAction.ViewAll, LrbResource.Attendance)]
        [OpenApiOperation("Update Attendance Settings.", "")]
        public async Task<Response<bool>> UpdateAttendanceSettingAsync(UpdateAttendanceSettingsRequest request)
        {
            return await _mediator.Send(request);
        }
        [HttpGet("notification")]
        [TenantIdHeader]
        // [MustHavePermission(LrbAction.ViewAll, LrbResource.Attendance)]
        [OpenApiOperation("Get Notification Based On User Shift Timings.", "")]
        public async Task<Response<UserBasisNotificationDto>> GetNotificationAsync()
        {
            return await _mediator.Send(new GetUserBasisShiftTimingNotificationRequest());
        }
        [HttpGet("dapper/timezone/{userId}")]
        [TenantIdHeader]
        [OpenApiOperation("Get Logs according to timezone by Dapper", "")]
        public async Task<IActionResult> GetTodaysAttendanceLogsByDapperWithTimeZone([FromQuery] GetTodaysAttendanceLogByUserIdDapperWithTimeZoneRequest request)
        {           
            var result = await Mediator.Send(request);
            return Ok(result);
        }
    }
}