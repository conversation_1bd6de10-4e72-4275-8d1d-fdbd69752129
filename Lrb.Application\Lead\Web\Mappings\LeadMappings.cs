﻿using DocumentFormat.OpenXml.Spreadsheet;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Mappings;
using Lrb.Application.Lead.Web.Requests;
using Lrb.Application.Lead.Web.Requests.UpdationRequests;
using Lrb.Application.Property.Web;
using Lrb.Application.Reports.Web.Dtos.FiltersName;
using Lrb.Application.Team.Web;
using Lrb.Domain.Entities.MasterData;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;

namespace Lrb.Application.Lead.Web
{
    public static class LeadMappings
    {
        public static IRepository<MasterPropertyType> _masterPropertyTypeRepo = null;
        public static IRepository<MasterAreaUnit> _masterAreaUnitRepo = null;
        //public static IRepository<MasterLeadStatus> _masterLeadStatusRepo = null;



        public static async void Configure(IServiceProvider serviceProvider)
        {
            _masterPropertyTypeRepo = (IRepository<MasterPropertyType>)serviceProvider.GetRequiredService(typeof(IRepository<>).MakeGenericType(typeof(MasterPropertyType)));
            _masterAreaUnitRepo = (IRepository<MasterAreaUnit>)serviceProvider.GetRequiredService(typeof(IRepository<>).MakeGenericType(typeof(MasterAreaUnit)));
            //_masterLeadStatusRepo = (IRepository<MasterLeadStatus>)serviceProvider.GetRequiredService(typeof(IRepository<>).MakeGenericType(typeof(MasterLeadStatus)));
            List<MasterPropertyType>? propertytypes = null;
            if (_masterPropertyTypeRepo != null)
            {
                propertytypes = _masterPropertyTypeRepo.ListAsync().Result;
            }
            List<MasterAreaUnit>? areaUnits = null;
            if (_masterAreaUnitRepo != null)
            {
                areaUnits = _masterAreaUnitRepo.ListAsync().Result;
            }
            List<LeadStatusDto>? masterLeadStatuses = new();
            List<CreateLeadEnquiryDto> leadEnquiries = new();
            TypeAdapterConfig<Domain.Entities.Lead, ViewLeadDto>
                .NewConfig()
                .Map(dest => dest.LeadTags, src => src.TagInfo)
                .Map(dest => dest.Status, src => MapLeadStatus(src, masterLeadStatuses))
                .Map(dest => dest.Enquiry, src => src.Enquiries != null && src.Enquiries.Any() ? src.Enquiries.FirstOrDefault(i => i.IsPrimary) : null)
                .Map(dest => dest.CallRecordingUrls, src => (src.CallRecordingUrls != null && src.CallRecordingUrls.Any()) ? src.CallRecordingUrls.GroupBy(i => i.Key.Year).ToDictionary(i => i.Key, i => i.GroupBy(i => i.Key.Month).ToDictionary(i => i.Key, i => i.ToList().ToDictionary(i => i.Key, i => i.Value))) : null)
                .Map(dest => dest.Documents, src => src.Documents)
               // .Map(dest => dest.Appointments, src => src.Appointments != null && src.Appointments.Any() ? src.Appointments.Adapt<List<LeadAppointmentDto>>() : new())
                .Map(dest => dest.ChannelPartners, src => src.ChannelPartners.Adapt<List<ChannelPartnerDto>>())
                .Map(dest => dest.BookedDetails, src => src.BookedDetails != null ? src.BookedDetails.Adapt<List<BookedDetailsDto>>() : new());
            //.Ignore(i => i.ContactRecords);
            TypeAdapterConfig<Domain.Entities.Lead, PullViewLeadDto>
                .NewConfig()
                .Map(dest => dest.Status, src => MapLeadStatus(src, masterLeadStatuses))
                .Map(dest => dest.CallRecordingUrls, src => (src.CallRecordingUrls != null && src.CallRecordingUrls.Any()) ? src.CallRecordingUrls.GroupBy(i => i.Key.Year).ToDictionary(i => i.Key, i => i.GroupBy(i => i.Key.Month).ToDictionary(i => i.Key, i => i.ToList().ToDictionary(i => i.Key, i => i.Value))) : null)
                .Map(dest => dest.Enquiry, src => src.Enquiries != null && src.Enquiries.Any() ? src.Enquiries.FirstOrDefault(i => i.IsPrimary) : null);
            TypeAdapterConfig<ViewLeadDto, Domain.Entities.Lead>
               .NewConfig()
               .Map(dest => dest.CallRecordingUrls, src => (src.CallRecordingUrls != null && src.CallRecordingUrls.Any()) ? src.CallRecordingUrls.SelectMany(i => i.Value).SelectMany(i => i.Value).ToList().ToDictionary(i => i.Key, j => j.Value) : null)
               .Ignore(i => i.BookedDetails);

            TypeAdapterConfig<Domain.Entities.Lead, LeadAppointmentsByIdDto>
                .NewConfig()
                .Map(dest => dest.Appointments, src => src.Appointments != null && src.Appointments.Any() ? src.Appointments.Adapt<List<LeadAppointmentsByIdDto>>() : new());
            TypeAdapterConfig<LeadEnquiry, ViewLeadEnquiryDto>
                .NewConfig()
                .Map(dest => dest.PropertyType, src => src.PropertyType != null ? new PropertyTypeDto
                {
                    Id = src.PropertyType.BaseId != null ? src.PropertyType.BaseId.Value : default,
                    BaseId = null,
                    DisplayName = src.PropertyType.BaseId != null ? propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).DisplayName : default,
                    Type = src.PropertyType.BaseId != null ? propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).Type : default,
                    Level = 0,
                    ChildType = new()
                    {
                        Id = src.PropertyType.Id,
                        BaseId = src.PropertyType.BaseId != null ? src.PropertyType.BaseId : default,
                        DisplayName = src.PropertyType.DisplayName,
                        Level = src.PropertyType.Level,
                        Type = src.PropertyType.Type,
                        ChildType = null
                    }

                } : null)
                 
              .Map(dest => dest.CarpetAreaUnit, src => src.CarpetAreaUnitId != default && areaUnits != null && areaUnits.FirstOrDefault(i => i.Id == src.CarpetAreaUnitId) != null ? areaUnits.FirstOrDefault(i => i.Id == src.CarpetAreaUnitId).Unit : default)
              .Map(dest => dest.NoOfBHK, src => src.NoOfBHKs)
              .Map(dest => dest.BuiltUpAreaUnit, src => src.BuiltUpAreaUnitId != default && areaUnits != null && areaUnits.FirstOrDefault(i => i.Id == src.BuiltUpAreaUnitId) != null ? areaUnits.FirstOrDefault(i => i.Id == src.BuiltUpAreaUnitId).Unit : default)
              .Map(dest => dest.SaleableAreaUnit, src => src.SaleableAreaUnitId != default && areaUnits != null && areaUnits.FirstOrDefault(i => i.Id == src.SaleableAreaUnitId) != null ? areaUnits.FirstOrDefault(i => i.Id == src.SaleableAreaUnitId).Unit : default)
              .Map(dest => dest.PropertyAreaUnit, src => src.PropertyAreaUnitId != default && areaUnits != null && areaUnits.FirstOrDefault(i => i.Id == src.PropertyAreaUnitId) != null ? areaUnits.FirstOrDefault(i => i.Id == src.PropertyAreaUnitId).Unit : default)
              .Map(dest => dest.NetAreaUnit, src => src.NetAreaUnitId != default && areaUnits != null && areaUnits.FirstOrDefault(i => i.Id == src.NetAreaUnitId) != null ? areaUnits.FirstOrDefault(i => i.Id == src.NetAreaUnitId).Unit : default)
             .Map(dest => dest.PropertyTypes, src => src.PropertyTypes != null ? src.PropertyTypes.Select(pt => new PropertyTypeDto
             {
                 Id = pt.BaseId.HasValue ? pt.BaseId.Value : default,
                 BaseId = null,
                 DisplayName = pt.BaseId.HasValue ? (propertytypes.FirstOrDefault(i => i.Id == pt.BaseId.Value) != null ? propertytypes.FirstOrDefault(i => i.Id == pt.BaseId.Value).DisplayName : default) : default,
                 Type = pt.BaseId.HasValue ? (propertytypes.FirstOrDefault(i => i.Id == pt.BaseId.Value) != null ? propertytypes.FirstOrDefault(i => i.Id == pt.BaseId.Value).Type : default) : default,
                 Level = 0,
                 ChildType = new PropertyTypeDto
                 {
                     Id = pt.Id,
                     BaseId = pt.BaseId,
                     DisplayName = pt.DisplayName,
                     Level = pt.Level,
                     Type = pt.Type,
                     ChildType = null
                 }
             }).ToList() : null);

            TypeAdapterConfig<UpdateLeadRequest, Domain.Entities.Lead>
                .NewConfig()
                .Ignore(i => i.Address)
                .Ignore(i => i.Enquiries)
                .Ignore(i => i.TagInfo);

            TypeAdapterConfig<LeadWithEnquiryModel, ViewLeadDto>
                 .NewConfig()
                 .Map(dest => dest.LeadTags, src => src.TagInfo != null ? JsonConvert.DeserializeObject<LeadTag>(src.TagInfo).Adapt<LeadTagDto>() : default)
                 .Map(dest => dest.Status, src => JsonConvert.DeserializeObject<LeadStatusDto>(src.Status).Adapt<LeadStatusDto>().BaseId == null || JsonConvert.DeserializeObject<LeadStatusDto>(src.Status).Adapt<LeadStatusDto>().BaseId == default
                                             ? JsonConvert.DeserializeObject<LeadStatusDto>(src.Status).Adapt<LeadStatusDto>()
                                             : new LeadStatusDto()
                                             {
                                                 Id = JsonConvert.DeserializeObject<LeadStatusDto>(src.Status).Adapt<LeadStatusDto>().BaseId.Value,
                                                 BaseId = null,
                                                 Status = masterLeadStatuses.FirstOrDefault(i => i.Id == JsonConvert.DeserializeObject<LeadStatusDto>(src.Status).Adapt<LeadStatusDto>().BaseId).Status,
                                                 DisplayName = masterLeadStatuses.FirstOrDefault(i => i.Id == JsonConvert.DeserializeObject<LeadStatusDto>(src.Status).Adapt<LeadStatusDto>().BaseId).DisplayName,
                                                 ActionName = masterLeadStatuses.FirstOrDefault(i => i.Id == JsonConvert.DeserializeObject<LeadStatusDto>(src.Status).Adapt<LeadStatusDto>().BaseId).ActionName,
                                                 Level = 0,
                                                 ChildType = JsonConvert.DeserializeObject<LeadStatusDto>(src.Status).Adapt<LeadStatusDto>()
                                             }
                                             ?? null)
                 .Map(dest => dest.Enquiry, src => src.Enquiries != null && src.Enquiries.Any() ? JsonConvert.DeserializeObject<List<LeadEnquiry>>(src.Enquiries).FirstOrDefault(i => i.IsPrimary) : null)
                 .Map(dest => dest.CallRecordingUrls, src => (src.CallRecordingUrls != null && src.CallRecordingUrls.Any()) ? JsonConvert.DeserializeObject<Dictionary<DateTime, string>>(src.CallRecordingUrls).GroupBy(i => i.Key.Year).ToDictionary(i => i.Key, i => i.GroupBy(i => i.Key.Month).ToDictionary(i => i.Key, i => i.ToList().ToDictionary(i => i.Key, i => i.Value))) : null); ;
            TypeAdapterConfig<LeadWithEnquiryModel, ViewLeadEnquiryDto>
                .NewConfig()
                .Map(dest => dest.PropertyType, src => JsonConvert.DeserializeObject<MasterPropertyType>(src.PropertyType) != null ? new PropertyTypeDto
                {
                    Id = JsonConvert.DeserializeObject<MasterPropertyType>(src.PropertyType).BaseId != null ? JsonConvert.DeserializeObject<MasterPropertyType>(src.PropertyType).BaseId.Value : default,
                    BaseId = null,
                    DisplayName = JsonConvert.DeserializeObject<MasterPropertyType>(src.PropertyType).BaseId != null ? propertytypes.FirstOrDefault(i => i.Id == JsonConvert.DeserializeObject<MasterPropertyType>(src.PropertyType).BaseId.Value).DisplayName : default,
                    Type = JsonConvert.DeserializeObject<MasterPropertyType>(src.PropertyType).BaseId != null ? propertytypes.FirstOrDefault(i => i.Id == JsonConvert.DeserializeObject<MasterPropertyType>(src.PropertyType).BaseId.Value).Type : default,
                    Level = 0,
                    ChildType = new()
                    {
                        Id = JsonConvert.DeserializeObject<MasterPropertyType>(src.PropertyType).Id,
                        BaseId = JsonConvert.DeserializeObject<MasterPropertyType>(src.PropertyType).BaseId != null ? JsonConvert.DeserializeObject<MasterPropertyType>(src.PropertyType).BaseId : default,
                        DisplayName = JsonConvert.DeserializeObject<MasterPropertyType>(src.PropertyType).DisplayName,
                        Level = JsonConvert.DeserializeObject<MasterPropertyType>(src.PropertyType).Level,
                        Type = JsonConvert.DeserializeObject<MasterPropertyType>(src.PropertyType).Type,
                        ChildType = null
                    }

                } : null)
                .Map(dest => dest.AreaUnit, src => areaUnits.FirstOrDefault(i => i.Id == src.AreaUnitId))
                //.Map(dest => dest.Address, src => JsonConvert.DeserializeObject<Address>(src.Address))
                .Map(dest => dest.Addresses, src => JsonConvert.DeserializeObject<List<Address>>(src.Addresses));

            TypeAdapterConfig<CreateBulkLeadEnquiryDto, LeadEnquiry>
               .NewConfig()
               .Map(dest => dest.IsPrimary, src => true)
               //.Map(dest => dest.PropertyType, src => src.PropertyTypeId != default ? propertytypes.FirstOrDefault(i => i.Id == src.PropertyTypeId) : null)
               //.Map(dest => dest.Address, src => src.Address != null ? src.Address.Adapt<Address>() : null)
               .Map(dest => dest.Addresses, src => src.Addresses != null ? src.Addresses.Adapt<Address>() : null);

            TypeAdapterConfig<CreateBulkLeadDto, Domain.Entities.Lead>
               .NewConfig()
               .Map(dest => dest.Name, src => src.Name.Trim())
               .Map(dest => dest.LeadNumber, src => src.Name.Trim()[0].ToString().ToUpper() + new Random().Next(1000, 9999).ToString() + DateTime.UtcNow.ToString("FFFFFFF"))
               .Map(dest => dest.TagInfo, src => src.LeadTags.Adapt<LeadTag>() ?? new LeadTag())
               .Map(dest => dest.Enquiries, src => src.Enquiry.Adapt<List<LeadEnquiry>>())
               .AfterMapping((dest, src) => src.Enquiries.Add(dest.Enquiry.Adapt<LeadEnquiry>()));
            TypeAdapterConfig<UserDetailsDto, Team.Web.UserDto>
             .NewConfig()
             .Map(dest => dest.Name, src => src.FirstName + " " + src.LastName)
             .Map(dest => dest.ContactNo, src => src.PhoneNumber);

            TypeAdapterConfig<Domain.Entities.Lead, Application.Lead.Web.Export.ExportLeadDto>
            .NewConfig()
            .Map(dest => dest.IsHighlighted, src => src.TagInfo.IsHighlighted)
            .Map(dest => dest.IsEscalated, src => src.TagInfo.IsEscalated)
            .Map(dest => dest.IsAboutToConvert, src => src.TagInfo.IsAboutToConvert)
            .Map(dest => dest.IsHotLead, src => src.TagInfo.IsHotLead)
            .Map(dest => dest.IsIntegrationLead, src => src.TagInfo.IsIntegrationLead)
            .Map(dest => dest.IsWarmLead, src => src.TagInfo.IsWarmLead)
            .Map(dest => dest.IsColdLead, src => src.TagInfo.IsColdLead)
            .Map(dest => dest.Status, src => src.CustomLeadStatus != null ? src.CustomLeadStatus.Status : string.Empty)
            .Map(dest => dest.PropertyType, src => (src.Enquiries != null && src.Enquiries.Any()) ? src.Enquiries.FirstOrDefault(i => i.IsPrimary).PropertyType != null ? src.Enquiries.FirstOrDefault(i => i.IsPrimary).PropertyType.DisplayName : string.Empty : string.Empty)
            //.Map(dest => dest.SubLocality, src => src.Enquiries != null && src.Enquiries.Any() ? src.Enquiries.FirstOrDefault(i => i.IsPrimary).Address != null ? src.Enquiries.FirstOrDefault(i => i.IsPrimary).Address.SubLocality : null : null)
            .Map(dest => dest.SubLocality, src => src.Enquiries != null && src.Enquiries.Any() ? String.Join(",", src.Enquiries.FirstOrDefault(i => i.IsPrimary).Addresses.Select(i => i.SubLocality)) : null)
            //.Map(dest => dest.Locality, src => src.Enquiries != null && src.Enquiries.Any() ? src.Enquiries.FirstOrDefault(i => i.IsPrimary).Address != null ? src.Enquiries.FirstOrDefault(i => i.IsPrimary).Address.Locality : null : null)
            .Map(dest => dest.Locality, src => src.Enquiries != null && src.Enquiries.Any() ? String.Join(",", src.Enquiries.FirstOrDefault(i => i.IsPrimary).Addresses.Select(i => i.Locality)) : null)
            //.Map(dest => dest.District, src => src.Enquiries != null && src.Enquiries.Any() ? src.Enquiries.FirstOrDefault(i => i.IsPrimary).Address != null ? src.Enquiries.FirstOrDefault(i => i.IsPrimary).Address.District : null : null)
             .Map(dest => dest.District, src => src.Enquiries != null && src.Enquiries.Any() ? String.Join(",", src.Enquiries.FirstOrDefault(i => i.IsPrimary).Addresses.Select(i => i.District)) : null)
            //.Map(dest => dest.City, src => src.Enquiries != null && src.Enquiries.Any() ? src.Enquiries.FirstOrDefault(i => i.IsPrimary).Address != null ? src.Enquiries.FirstOrDefault(i => i.IsPrimary).Address.City : null : null)
             .Map(dest => dest.City, src => src.Enquiries != null && src.Enquiries.Any() ? String.Join(",", src.Enquiries.FirstOrDefault(i => i.IsPrimary).Addresses.Select(i => i.City)) : null)
            //.Map(dest => dest.State, src => src.Enquiries != null && src.Enquiries.Any() ? src.Enquiries.FirstOrDefault(i => i.IsPrimary).Address != null ? src.Enquiries.FirstOrDefault(i => i.IsPrimary).Address.State : null : null)
            .Map(dest => dest.State, src => src.Enquiries != null && src.Enquiries.Any() ? String.Join(",", src.Enquiries.FirstOrDefault(i => i.IsPrimary).Addresses.Select(i => i.State)) : null)
            //.Map(dest => dest.Country, src => src.Enquiries != null && src.Enquiries.Any() ? src.Enquiries.FirstOrDefault(i => i.IsPrimary).Address != null ? src.Enquiries.FirstOrDefault(i => i.IsPrimary).Address.Country : null : null)
             .Map(dest => dest.Country, src => src.Enquiries != null && src.Enquiries.Any() ? String.Join(",", src.Enquiries.FirstOrDefault(i => i.IsPrimary).Addresses.Select(i => i.Country)) : null)
            //.Map(dest => dest.PostalCode, src => src.Enquiries != null && src.Enquiries.Any() ? src.Enquiries.FirstOrDefault(i => i.IsPrimary).Address != null ? src.Enquiries.FirstOrDefault(i => i.IsPrimary).Address.PostalCode : null : null)
            .Map(dest => dest.PostalCode, src => src.Enquiries != null && src.Enquiries.Any() ? String.Join(",", src.Enquiries.FirstOrDefault(i => i.IsPrimary).Addresses.Select(i => i.PostalCode)) : null)
            .Map(dest => dest.EnquiredFor, src => src.Enquiries != null && src.Enquiries.Any() ? src.Enquiries.FirstOrDefault(i => i.IsPrimary).EnquiredFor : default)
            .Map(dest => dest.EnquiryTypes, src => src.Enquiries != null && src.Enquiries.Any() ? src.Enquiries.FirstOrDefault(i => i.IsPrimary).EnquiryTypes : default)
            .Map(dest => dest.SaleType, src => src.Enquiries != null && src.Enquiries.Any() ? src.Enquiries.FirstOrDefault(i => i.IsPrimary).SaleType : default)
            .Map(dest => dest.LeadSource, src => src.Enquiries != null && src.Enquiries.Any() ? src.Enquiries.FirstOrDefault(i => i.IsPrimary).LeadSource : default)
            .Map(dest => dest.LowerBudget, src => src.Enquiries != null && src.Enquiries.Any() ? src.Enquiries.FirstOrDefault(i => i.IsPrimary).LowerBudget : null)
            .Map(dest => dest.UpperBudget, src => src.Enquiries != null && src.Enquiries.Any() ? src.Enquiries.FirstOrDefault(i => i.IsPrimary).UpperBudget : null)
            .Map(dest => dest.NoOfBHK, src => src.Enquiries != null && src.Enquiries.Any() ? src.Enquiries.FirstOrDefault(i => i.IsPrimary).NoOfBHKs : default)
            .Map(dest => dest.BHKs, src => src.Enquiries != null && src.Enquiries.Any() ? src.Enquiries.FirstOrDefault(i => i.IsPrimary).BHKs : default)
            .Map(dest => dest.BHKType, src => src.Enquiries != null && src.Enquiries.Any() ? src.Enquiries.FirstOrDefault(i => i.IsPrimary).BHKType : default)
            .Map(dest => dest.BHKTypes, src => src.Enquiries != null && src.Enquiries.Any() ? src.Enquiries.FirstOrDefault(i => i.IsPrimary).BHKTypes : default)
            .Map(dest => dest.Area, src => src.Enquiries != null && src.Enquiries.Any() ? src.Enquiries.FirstOrDefault(i => i.IsPrimary).Area : default)
            .Map(dest => dest.SubSource, src => src.Enquiries != null && src.Enquiries.Any() ? src.Enquiries.FirstOrDefault(i => i.IsPrimary).SubSource : null)
            .Map(dest => dest.OfferType, src => src.Enquiries != null && src.Enquiries.Any() ? src.Enquiries.FirstOrDefault(i => i.IsPrimary).OfferType : default)
            .Map(dest => dest.Purpose, src => src.Enquiries != null && src.Enquiries.Any() ? src.Enquiries.FirstOrDefault(i => i.IsPrimary).Purpose : default)
            .Map(dest => dest.Beds, src => src.Enquiries != null && src.Enquiries.Any() ? src.Enquiries.FirstOrDefault(i => i.IsPrimary).Beds : default);

            TypeAdapterConfig<UpdateSiteVisitOrMeetingDoneRequest, LeadAppointment>
                .NewConfig()
                .Map(dest => dest.Type, src => src.MeetingOrSiteVisit).TwoWays();

            TypeAdapterConfig<UpdateLeadStatusRequest, LeadAppointment>
                .NewConfig()
                .Map(dest => dest.Type, src => src.MeetingOrSiteVisit).TwoWays();

            TypeAdapterConfig<CreateLeadEnquiryDto, LeadEnquiry>
                .NewConfig()
                .Map(dest => dest.NoOfBHKs, src => src.NoOfBHK ?? 0)
                .Map(dest => dest.SubSource, src => string.IsNullOrWhiteSpace(src.SubSource) ? null : src.SubSource.Trim().ToLower())
                .Map(dest => dest.BHKType, src => src.BHKType ?? default);

            TypeAdapterConfig<UpdateLeadEnquiryDto, LeadEnquiry>
                .NewConfig()
                .Map(dest => dest.NoOfBHKs, src => src.NoOfBHK ?? 0)
                .Map(dest => dest.SubSource, src => string.IsNullOrWhiteSpace(src.SubSource) ? null : src.SubSource.Trim().ToLower())
                .Map(dest => dest.BHKType, src => src.BHKType ?? default);
            TypeAdapterConfig<UpdateLeadStatusRequest, Domain.Entities.Lead>
                .NewConfig()
                .Ignore(src => src.Address)
                .Ignore(src => src.Projects)
                .Map(dest => dest.ScheduledDate, src => src.PostponedDate != null && src.PostponedDate != default ? src.PostponedDate : src.ScheduledDate)
                .Ignore(i => i.AssignTo);
            TypeAdapterConfig<LeadFilterDto, LeadFormettedFilterDto>
               .NewConfig()
                .MapWith(src => new LeadFormettedFilterDto
                {

                    Source = ConvertEnumListTostring(src.Source),
                    SubSources = ConvertListStringToString(src.SubSources),
                    EnquiredFor = ConvertEnumListTostring(src.EnquiredFor),
                    AgencyNames = ConvertListStringToString(src.AgencyNames),
                    NoOfBHKs = ConvertListDoubletostring(src.NoOfBHKs),
                    BHKTypes = ConvertEnumListTostring(src.BHKTypes),
                    Locations = ConvertListStringToString(src.Locations),
                    Zones = ConvertListStringToString(src.Zones),
                    Cities = ConvertListStringToString(src.Cities),
                    Projects = ConvertListStringToString(src.Projects),
                    Properties = ConvertListStringToString(src.Properties),
                    Profession = ConvertEnumListTostring(src.Profession),
                    IsUntouched=src.IsUntouched,
                    IsWithHistory=src.IsWithHistory,
                });;

            TypeAdapterConfig<AddDocumentsRequest, PickedLeadDto>
                .NewConfig()
                .Map(dest => dest.Documents, src => src.Documents);
            TypeAdapterConfig<AddProjectsInLeadRequest, PickedLeadDto>
                .NewConfig()
                .Map(dest => dest.ProjectNames, src => src.Projects);
            TypeAdapterConfig<DeleteDocumentRequest, PickedLeadDto>
                .NewConfig()
                .Map(dest => dest.Documents, src => src.DocumentIds);
            TypeAdapterConfig<UpdateLeadStatusRequest, PickedLeadDto>
                .NewConfig()
                .Map(dest => dest.Projects, src => (src.Projects != null && src.Projects.Any()) ? src.Projects.Select(i => new TempProjectsDto() { Name = i }).ToList() : new());
            TypeAdapterConfig<UpdateLeadRequest, PickedLeadDto>
                .NewConfig();
            TypeAdapterConfig<UpdateLeadTagRequest, PickedLeadDto>
                .NewConfig();
            TypeAdapterConfig<UpdateSiteVisitOrMeetingDoneRequest, PickedLeadDto>
                .NewConfig()
                .Map(dest => dest.Appointments, src => new List<LeadAppointmentDto>(){ new() {
                   Type = src.MeetingOrSiteVisit,
                   IsDone = src.IsDone,
                   Longitude = src.Longitude,
                   Latitude = src.Latitude,
                   ProjectName = src.ProjectName,
                   ExecutiveName = src.ExecutiveName,
                   ExecutiveContactNo = src.ExecutiveContactNo,
                   Image = src.Image,
                   ImagesWithName = src.ImagesWithName,
                   IsManual = src.IsManual,
                   Notes = src.Notes,
                   Location = src.Address
                } });
            TypeAdapterConfig<UploadLeadDocumentRequest, PickedLeadDto>
                .NewConfig();
            TypeAdapterConfig<UpdateBulkLeadSourceRequest, PickedLeadDto>
                .NewConfig()
                .Map(dest => dest.Enquiry, src => new CreateLeadEnquiryDto()
                {
                    LeadSource = src.LeadSource,
                    SubSource = src.SubSource,
                });
            TypeAdapterConfig<LeadBookedDetail, BookedDetailsDto>
                .NewConfig()
                .Map(dest => dest.PropertyName, src => src.Properties != null && src.Properties.Any() ? src.Properties.FirstOrDefault().Title ?? string.Empty : string.Empty)
                .Map(dest => dest.ProjectName, src => src.Projects != null && src.Projects.Any() ? src.Projects.FirstOrDefault().Name ?? string.Empty: string.Empty);
                TypeAdapterConfig<Lrb.Domain.Entities.Property, BasicPropertyInfoDto>
                .NewConfig()
                .Map(dest => dest.Dimension, src => src.Dimension != null ? src.Dimension : default);
            TypeAdapterConfig<UpdateLeadStatusRequest, ProcessBulkStatusUpdateRequest>
               .NewConfig()
               .Map(dest => dest.LeadIds, src => new List<Guid> { src.Id });
            TypeAdapterConfig<UpdateBulkLeadStatusRequest, PickedLeadDto>
               .NewConfig()
               .Map(dest => dest.Projects, src => (src.Projects != null && src.Projects.Any()) ? src.Projects.Select(i => new TempProjectsDto() { Name = i }).ToList() : new());
            TypeAdapterConfig<UpdateBulkLeadStatusRequest, Domain.Entities.Lead>
                .NewConfig()
                .Ignore(src => src.Address)
                .Ignore(src => src.Projects)
                .Map(dest => dest.ScheduledDate, src => src.PostponedDate != null && src.PostponedDate != default ? src.PostponedDate : src.ScheduledDate)
                .Ignore(i => i.AssignTo);
            TypeAdapterConfig<UpdateBulkLeadStatusRequest, LeadAppointment>
                .NewConfig()
                .Map(dest => dest.Type, src => src.MeetingOrSiteVisit).TwoWays();
            TypeAdapterConfig<CreateLeadRequest, DuplicateLeadSpecDto>
              .NewConfig()
              .Map(dest => dest.LeadSource, src => src.Enquiry != null ? src.Enquiry.LeadSource : LeadSource.Direct);
            TypeAdapterConfig<Domain.Entities.Lead, DuplicateAssigmentLeadDto>
             .NewConfig()
             .Map(dest => dest.Source, src => src.Enquiries != null && src.Enquiries.Any() ? src.Enquiries.FirstOrDefault().LeadSource : LeadSource.Direct);

            TypeAdapterConfig<LeadHistoryHot, LeadHistoryDto>
               .NewConfig()
               .Map(dest => dest.UpdatedOn, src => src.ModifiedOn)
               .Map(dest => dest.UpdatedBy, src => src.ModifiedBy)
               .Map(dest => dest.AuditActionType, src => src.Version == 1 ? AuditActionType.Created.ToString() : AuditActionType.Updated.ToString());
        }
        private static string ConvertListDoubletostring(List<double>? noOfBHKs)
        {
            if (noOfBHKs == null || noOfBHKs.Count == 0)
                return null;
            List<string> stringValues = noOfBHKs.Select(e => e.ToString()).ToList();
            return string.Join(", ", stringValues);
        }

        private static string ConvertEnumListTostring(List<Profession>? profession)
        {
            if (profession == null || profession.Count == 0)
                return null;
            List<string> enumStringList = profession.Select(e => e.ToString()).ToList();
            return string.Join(", ", enumStringList);
        }

        private static string ConvertEnumListTostring(List<BHKType>? bHKTypes)
        {
            if (bHKTypes == null || bHKTypes.Count == 0)
                return null;
            List<string> enumStringList = bHKTypes.Select(e => e.ToString()).ToList();
            return string.Join(", ", enumStringList);
        }


        private static string ConvertEnumListTostring(List<EnquiryType>? enquiredFor)
        {
            if (enquiredFor == null || enquiredFor.Count == 0)
                return null;
            List<string> enumStringList = enquiredFor.Select(e => e.ToString()).ToList();
            return string.Join(", ", enumStringList);
        }

        private static string ConvertListStringToString(List<string>? subSources)
        {
            if (subSources == null || subSources.Count == 0)
                return null;
            List<string> forstringsList = subSources.Select(e => e.ToString()).ToList();
            return string.Join(", ", forstringsList);
        }

        private static string ConvertEnumListTostring(List<LeadSource>? sources)
        {
            if (sources == null || sources.Count == 0)
                return null;
            List<string> enumStringList = sources.Select(e => e.ToString()).ToList();
            return string.Join(", ", enumStringList);
        }

        public static LeadStatusDto? MapLeadStatus(Lrb.Domain.Entities.Lead src, List<LeadStatusDto>? masterLeadStatuses)
        {
            if (src.CustomLeadStatus != null)
            {
                if (!masterLeadStatuses?.Any() ?? false)
                {
                    IRepository<CustomMasterLeadStatus> _customMasterLeadStatusRepo = null;
                    var ServiceProvider = ServiceLocator.GetService();
                    _customMasterLeadStatusRepo = (IRepository<CustomMasterLeadStatus>)ServiceProvider.GetRequiredService(typeof(IRepository<>).MakeGenericType(typeof(CustomMasterLeadStatus)));
                    masterLeadStatuses = _customMasterLeadStatusRepo.ListAsync().Result.Adapt<List<LeadStatusDto>>();
                }
                if (masterLeadStatuses != null)
                {
                    if (src?.CustomLeadStatus?.BaseId == null || src.CustomLeadStatus.BaseId == default)
                    {
                        return src?.CustomLeadStatus.Adapt<LeadStatusDto>();
                    }
                    else
                    {
                        var parentStatus = masterLeadStatuses.FirstOrDefault(i => i.Id == src.CustomLeadStatus.BaseId);
                        return new LeadStatusDto()
                        {
                            Id = src.CustomLeadStatus.BaseId.Value,
                            BaseId = null,
                            Status = parentStatus?.Status,
                            DisplayName = parentStatus?.DisplayName,
                            ActionName = parentStatus?.ActionName,
                            Level = 0,
                            ChildType = src.CustomLeadStatus.Adapt<LeadStatusDto>(),
                            IsDefault = parentStatus?.IsDefault ?? false,
                            IsLrbStatus = parentStatus?.IsLrbStatus ?? false,
                            IsDefaultChild = parentStatus?.IsDefaultChild ?? false,
                            ShouldUseForBooking = parentStatus?.ShouldUseForBooking ?? false,
                            ShouldUseForBookingCancel = parentStatus?.ShouldUseForBookingCancel ?? false,
                            ShouldOpenAppointmentPage = parentStatus?.ShouldOpenAppointmentPage ?? false,
                            ShouldUseForMeeting = parentStatus?.ShouldUseForMeeting,
                            IsScheduled  = parentStatus?.IsScheduled ?? false,
                            WhatsAppTemplateInfoIds = parentStatus?.WhatsAppTemplateInfoIds,
                            ShouldUseForInvoice = parentStatus?.ShouldUseForInvoice
                        };
                    }
                }
            }
            return null;
        }
    }
}


