﻿using Lrb.Application.Reports.Web.Lead.Dtos.UserVsSource;
using Lrb.Application.Reports.Web.Lead.Dtos.UserVsSubSource;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Reports.Web.Lead.Dtos.Revenue
{
        public class RevenueSourceDto
        {
            public Guid? Id { get; set; }
            public double? Ammount { get; set; }
            public string? SubSource { get; set; }
            public string? BaseSource { get; set; }
           public double? LeadCount { get; set; }
    }
    public class RevenueUserVsSourceReportDto : IDto
    {
        public string? UserName { get; set; }
        public Guid UserId { get; set; }
        public string? Source { get; set; }
        public List<RevenueSourceDto>? SourcesDtos { get; set; }
    }


    public class RevenueUserSourceReportDto : IDto
    {
        public User? User { get; set; }
        public List<UserRevenueSourceDto>? Source { get; set; }
    }
    public class UserRevenueSourceDto
    {
        public string? DisplayName { get; set; }
        public double? Ammount { get; set; }
        public double? LeadCount { get; set; }
    }


    public class RevenueUservsSubSourceReportDto : IDto
    {
        public string? UserName { get; set; }
        public Guid UserId { get; set; }
        public Dictionary<string, object>? SourceWithSubSourceRevenue { get; set; }
        public List<UserRevenueSourceDto>? Source { get; set; }
    }

    public class RevenueSourceReportByUserDto : IDto
    {
        public Guid UserId { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public List<UserRevenueSourceDto>? Source { get; set; }
    }
}
