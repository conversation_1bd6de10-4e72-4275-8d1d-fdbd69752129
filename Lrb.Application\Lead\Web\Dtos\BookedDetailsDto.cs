﻿using Lrb.Application.Project.Web;
using Lrb.Application.Property.Web;

namespace Lrb.Application.Lead.Web.Dtos
{
    public class BookedDetailsDto
    {
        public Guid? Id { get; set; }
        public DateTime? BookedDate { get; set; }
        public Guid? BookedBy { get; set; }
        public string? BookedByName {  get; set; }
        public Guid? SecondaryOwner { get; set; }
        public string? SecondaryOwnerName { get; set; }
        public string? BookedByUser { get; set; }
        public string? BookedUnderName { get; set; }
        public Guid? UserId { get; set; }
        public string? UserName { get; set; }
        public string? SoldPrice { get; set; }
        public string? Notes { get; set; }
        public List<string>? ProjectsList { get; set; }
        public List<string>? PropertiesList { get; set; }
        public Guid? TeamHead { get; set; }
        public string? TeamHeadName { get; set; }
        public double? AgreementValue { get; set; }
        public IList<DocumentsDto>? Documents { get; set; }
        public double? CarParkingCharges { get; set; }
        public double? AdditionalCharges { get; set; }
        public double? TokenAmount { get; set; }
        public TokenType PaymentMode { get; set; }
        public double? Discount { get; set; }
        public string? DiscountUnit { get; set; }
        public DiscountType DiscountMode { get; set; }
        public double? RemainingAmount { get; set; }
        public PaymentType PaymentType { get; set; }
        public Guid? LeadBrokerageInfoId { get; set; }
        //public string? ChoosenUnit { get; set; }
        public LeadBrokerageInfoDto? BrokerageInfo { get; set; }
        public BasicPropertyInfoDto? Property { get; set;}
        public string? PropertyName { get; set; }
        public string? ProjectName { get; set; }
        public BasicProjectDto? Projects { get; set; }
        public bool? IsBookingCompleted { get; set; }
        public DateTime LastModifiedOn { get; set; }
        public string? Currency { get; set; }
        public UnitTypeDto? UnitType { get; set; }
        public string? LastModifiedByUser { get; set; }
        public Guid? LastModifiedBy { get; set; }
        public List<string>? InvoiceUrl { get; set; }
    }
}
