﻿using Lrb.Application.Common.Persistence.New_Implementation;

namespace Lrb.Application.Project.Mobile
{
    public class GetProjectByIdRequest : IRequest<Response<ViewProjectDto>>
    {
        public Guid Id { get; set; }
        public GetProjectByIdRequest(Guid id)
        {
            Id = id;
        }
    }
    public class GetProjectByIdRequestHandler : IRequestHandler<GetProjectByIdRequest, Response<ViewProjectDto>>
    {
        public readonly IRepositoryWithEvents<Domain.Entities.Project> _projectRepo;
        private readonly IRepositoryWithEvents<CustomMasterAmenity> _projectAmenitiesRepo;
        private readonly IRepositoryWithEvents<ProjectGallery> _projectGalleriesRepo;
        private readonly IRepositoryWithEvents<ProjectAmenity> _amenitiesRepo;
        private readonly IRepositoryWithEvents<AssociatedBank> _projectAssociatedBanksRepo;
        private readonly IRepositoryWithEvents<UnitType> _projectUnitsRepo;
        private readonly IRepositoryWithEvents<Block> _projectBlockRepo;

        public GetProjectByIdRequestHandler(IRepositoryWithEvents<Domain.Entities.Project> leadRepo, IRepositoryWithEvents<CustomMasterAmenity> projectAmenitiesRepo, IRepositoryWithEvents<ProjectGallery> projectGalleriesRepo,
            IRepositoryWithEvents<ProjectAmenity> amenitiesRepo, IRepositoryWithEvents<AssociatedBank> projectAssociatedBanksRepo, IRepositoryWithEvents<UnitType> projectUnitsRepo, IRepositoryWithEvents<Block> projectBlockRepo)
        {
            _projectRepo = leadRepo;
            _projectAmenitiesRepo = projectAmenitiesRepo;
            _amenitiesRepo = amenitiesRepo;
            _projectGalleriesRepo = projectGalleriesRepo;
            _projectAssociatedBanksRepo = projectAssociatedBanksRepo;
            _projectUnitsRepo = projectUnitsRepo;
            _projectBlockRepo = projectBlockRepo;
        }

        public async Task<Response<ViewProjectDto>> Handle(GetProjectByIdRequest request, CancellationToken cancellationToken)
        {
            var project = (await _projectRepo.FirstOrDefaultAsync(new GetNewProjectByIdSpec(request.Id), cancellationToken));
            
            if (project == null) { throw new NotFoundException("No Project found by this Id"); }
            var galleries = await _projectGalleriesRepo.ListAsync(new ProjectGalleryByProjectIdSpec(request.Id), cancellationToken);
            var banks = await _projectAssociatedBanksRepo.ListAsync(new AssociatedBankByProjectIdSpec(request.Id), cancellationToken);
            var projectAmenities = await _amenitiesRepo.ListAsync(new ProjectAmenityByProjectIdSpec(request.Id), cancellationToken);
            var units = await _projectUnitsRepo.ListAsync(new UnitTypesByProjectIdSpec(request.Id), cancellationToken);
            var blocks = await _projectBlockRepo.ListAsync(new BlocksByProjectIdSpec(request.Id), cancellationToken);
            project.ProjectGalleries =  galleries;
            project.AssociatedBanks = banks;
            project.Amenities = projectAmenities;
            project.UnitTypes = units;
            project.Blocks =  blocks;
            var projectDto = project.Adapt<ViewProjectDto>();
            if (project.Amenities?.Any() ?? false)
            {
                var amenitiesIds = project.Amenities.Select(i => i.MasterProjectAmenityId).ToList();
                var amenities = await _projectAmenitiesRepo.ListAsync(new GetCustomAmenitiesByIdsSpec(amenitiesIds), cancellationToken);
                projectDto.ProjectAmenities = amenities.Select(i => i.Id).ToList();
            }
            return new Response<ViewProjectDto>(projectDto);
        }
    }

}
