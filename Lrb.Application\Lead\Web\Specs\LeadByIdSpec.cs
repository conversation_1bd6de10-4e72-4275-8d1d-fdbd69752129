﻿namespace Lrb.Application.Lead.Web
{
    public class LeadByIdSpec : Specification<Domain.Entities.Lead>
    {
        public LeadByIdSpec(Guid id, bool? isArchived = false)
        {
            Query.Where(i => !i.IsDeleted)
                .Where(i => i.Id == id)
                .Include(i => i.TagInfo)
               .Include(i => i.CustomLeadStatus)
                .Include(i => i.Enquiries)
                    .ThenInclude(i => i.Addresses)
                        .ThenInclude(i => i.Location)
                .Include(i => i.Enquiries)
                    .ThenInclude(i => i.PropertyType)
                .Include(i => i.Appointments)
                    .ThenInclude(i => i.Location)
                .Include(i => i.Projects)
                .Include(i => i.Properties)
                .ThenInclude(i => i.Dimension)
                .Include(i => i.ChannelPartners)
                .Include(i => i.CustomFlags)
                .ThenInclude(i => i.Flag)
                .Include(i => i.Agencies)
                .Include(i => i.BookedDetails)
                  .ThenInclude(i => i.Properties)
                    .ThenInclude(i => i.Dimension)
                     .Include(i => i.Address)
                .Include(i => i.BookedDetails)
                   .ThenInclude(i => i.Documents)
                   .Include(i => i.BookedDetails)
                   .ThenInclude(i => i.BrokerageInfo)
                .Include(i => i.LeadCallLogs)
                 .Include(i => i.CustomFlags)
                  .ThenInclude(i => i.Flag)
                 .Include(i => i.Agencies)
                 .Include(i => i.Campaigns)
                 .Include(i => i.ChannelPartners)
                 .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyTypes);
            if (isArchived == null || !isArchived.Value)
            {
                Query.Where(i => !i.IsArchived);
            }
        }
        public LeadByIdSpec(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted).Where(i => !i.IsArchived)
                .Where(i => ids.Contains(i.Id))
                .Include(i => i.TagInfo)
               .Include(i => i.CustomLeadStatus)
                .Include(i => i.Enquiries)
                    //.ThenInclude(i => i.Address)
                    .ThenInclude(i => i.Addresses)
                        .ThenInclude(i => i.Location)
                .Include(i => i.Enquiries)
                    .ThenInclude(i => i.PropertyType)
                     .Include(i => i.Enquiries)
                   .ThenInclude(i => i.PropertyTypes)
                .Include(i => i.Appointments)
                    .ThenInclude(i => i.Location)
                .Include(i => i.Projects)
                .Include(i => i.Properties)
                .ThenInclude(i => i.Dimension)
                .Include(i => i.Address)
                .Include(i => i.ChannelPartners)
                .Include(i => i.Campaigns)
                .Include(i => i.CustomFlags)
                .ThenInclude(i => i.Flag)
                .Include(i => i.Agencies)
                .Include(i => i.BookedDetails)
                  .ThenInclude(i => i.Properties)
                    .ThenInclude(i => i.Dimension)
                .Include(i => i.BookedDetails)
                   .ThenInclude(i => i.Documents)
                   .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyTypes);
        }
    }
    public class ArchiveLeadSpec : Specification<Domain.Entities.Lead>
    {
        public ArchiveLeadSpec(Guid id)
        {
            Query.Where(i => !i.IsDeleted).Where(i => i.IsArchived)
               .Where(i => i.Id == id)
               .Include(i => i.TagInfo)
               //.Include(i => i.Status)
               .Include(i => i.Enquiries)
              .Include(i => i.Enquiries)
                  .ThenInclude(i => i.Addresses)
                      .ThenInclude(i => i.Location)
                          .ThenInclude(i => i.Zone)
              .Include(i => i.Enquiries)
                  .ThenInclude(i => i.Addresses)
                      .ThenInclude(i => i.Location)
                          .ThenInclude(i => i.City)
              .Include(i => i.Enquiries)
                  .ThenInclude(i => i.PropertyType)
                   .Include(i => i.Enquiries)
                   .ThenInclude(i => i.PropertyTypes)
              .Include(i => i.Appointments)
                  .ThenInclude(i => i.Location)
              .Include(i => i.Projects)
              .Include(i => i.Properties)
              .ThenInclude(i => i.Dimension)
              .Include(i => i.Address)
              .Include(i => i.ChannelPartners)
              .Include(i => i.CustomFlags)
              .ThenInclude(i => i.Flag)
              .Include(i => i.Agencies)
              .Include(i => i.BookedDetails)
                .ThenInclude(i => i.Properties)
                  .ThenInclude(i => i.Dimension)
              .Include(i => i.BookedDetails)
                 .ThenInclude(i => i.Documents)
                 .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyTypes);
        }
        public ArchiveLeadSpec(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted).Where(i => i.IsArchived)
               .Where(i => ids.Contains(i.Id))
               .Include(i => i.TagInfo)
               //.Include(i => i.Status)
               .Include(i => i.Enquiries)
               .Include(i => i.Enquiries)
                    //.ThenInclude(i => i.Address)
                    .ThenInclude(i => i.Addresses)
                        .ThenInclude(i => i.Location)
                            .ThenInclude(i => i.Zone)
                .Include(i => i.Enquiries)
                    //.ThenInclude(i => i.Address)
                    .ThenInclude(i => i.Addresses)
                        .ThenInclude(i => i.Location)
                            .ThenInclude(i => i.City)
               .Include(i => i.Enquiries)
                    .ThenInclude(i => i.PropertyType)
                     .Include(i => i.Enquiries)
                   .ThenInclude(i => i.PropertyTypes)
               .Include(i => i.Appointments)
               .ThenInclude(i => i.Location)
               .Include(i => i.Projects)
               .Include(i => i.Properties)
                .Include(i => i.CustomFlags)
                .ThenInclude(i => i.Flag)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyTypes);
        }
    }

    public class GetLeadForAutoAssignmentByLeadIdsSpecs : Specification<Domain.Entities.Lead>
    {
        public GetLeadForAutoAssignmentByLeadIdsSpecs(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted).Where(i => !i.IsArchived)
               .Where(i => ids.Contains(i.Id))
               .Include(i => i.CustomLeadStatus);
        }
    }
    public class LeadByIdForHistorySpec : Specification<Domain.Entities.Lead>
    {
        public LeadByIdForHistorySpec(Guid id)
        {
            Query.Where(i => !i.IsDeleted).Where(i => i.Id == id);
        }
    }

    public class LeadAppintmentsByIdSpec : Specification<Domain.Entities.Lead>
    {
        public LeadAppintmentsByIdSpec(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted).Where(i => !i.IsArchived)
                .Where(i => ids.Contains(i.Id))
                .Include(i => i.Appointments)
                    .ThenInclude(i => i.Location);
        }
    }
    }
