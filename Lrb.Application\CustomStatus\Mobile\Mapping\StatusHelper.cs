﻿using Lrb.Application.CustomFields.Mobile;
using Lrb.Application.Lead.Mobile;
using Lrb.Domain.Entities.MasterData;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.CustomStatus.Mobile
{
    public static class StatusHelper
    {
        public static List<ViewCustomStatusDto> GetCustomStatus(List<ViewCustomStatusDto> filters)
        {
            var customStatus = filters.OrderBy(i => i.OrderRank).Where(i => i.BaseId == null || i.BaseId == Guid.Empty).ToList();
            GetChildData(customStatus, filters);
            return customStatus;
        }
        public static void GetChildData(List<ViewCustomStatusDto> childTypes, List<ViewCustomStatusDto> filters)
        {
            childTypes?.ForEach(child =>
            {
                if (TryGetChildData(child, filters, out List<ViewCustomStatusDto>? childType))
                {
                    child.ChildTypes = childType ?? new List<ViewCustomStatusDto>();
                    GetChildData(childType ?? new List<ViewCustomStatusDto>(), filters);
                }
            });
        }
        public static bool TryGetChildData(ViewCustomStatusDto filter, List<ViewCustomStatusDto> filters, out List<ViewCustomStatusDto>? childDatas)
        {
            var data = filters.Where(i => filter.Id == i.BaseId)?.OrderBy(i => i.OrderRank).ToList();
            childDatas = data;
            return data?.Any() ?? false;
        }
        public static List<StatusNamesDto> GetCustomStatus(List<StatusNamesDto> filters)
        {
            var customStatus = filters.OrderBy(i => i.OrderRank).Where(i => i.BaseId == null || i.BaseId == Guid.Empty).ToList();
            GetChildData(customStatus, filters);
            return customStatus;
        }
        public static void GetChildData(List<StatusNamesDto> childTypes, List<StatusNamesDto> filters)
        {
            childTypes?.ForEach(child =>
            {
                if (TryGetChildData(child, filters, out List<StatusNamesDto>? childType))
                {
                    child.ChildTypes = childType ?? new List<StatusNamesDto>();
                    GetChildData(childType ?? new List<StatusNamesDto>(), filters);
                }
            });
        }
        public static bool TryGetChildData(StatusNamesDto filter, List<StatusNamesDto> filters, out List<StatusNamesDto>? childDatas)
        {
            var data = filters.Where(i => filter.Id == i.BaseId)?.OrderBy(i => i.OrderRank).ToList();
            childDatas = data;
            return data?.Any() ?? false;
        }
        public static List<ViewCustomStatusDto> GetCustomFields(List<ViewCustomStatusDto> statusDtos)
        {
            var scheduleDate = GetCustomField("ScheduledDate", true);
            var projectNotRequired = GetCustomField("Projects", false);
            var projectRequired = GetCustomField("Projects", true);
            var property = GetCustomField("Properties", false);
            var bookingUnderName = GetCustomField("Booking Under Name", true);
            var bookingdate = GetCustomField("Booking Date", true);
            var aggregateValue = GetCustomField("Agreement Value", false);
            var leadBudget = GetCustomField("Lead Budget", false);
            var locality = GetCustomField("Locality", false);
            var city = GetCustomField("City", false);
            var state = GetCustomField("State", false);
            var purchasedFromOther = GetCustomField("Purchased From Whom", false);

            if (statusDtos.Any())
            {
                statusDtos.Where(i => i.Status == "callback").FirstOrDefault()?.CustomFields?.Add(scheduleDate);
                statusDtos.Where(i => i.Status == "meeting_scheduled").FirstOrDefault()?.CustomFields?.Add(scheduleDate);
                statusDtos.Where(i => i.Status == "site_visit_scheduled").FirstOrDefault()?.CustomFields?.Add(scheduleDate);
                statusDtos.Where(i => i.Status == "site_visit_scheduled").FirstOrDefault()?.CustomFields?.Add(projectNotRequired);
                statusDtos.Where(i => i.Status == "booked").FirstOrDefault()?.CustomFields?.Add(bookingUnderName);
                statusDtos.Where(i => i.Status == "booked").FirstOrDefault()?.CustomFields?.Add(bookingdate);
                statusDtos.Where(i => i.Status == "booked").FirstOrDefault()?.CustomFields?.Add(aggregateValue);
                statusDtos.Where(i => i.Status == "booked").FirstOrDefault()?.CustomFields?.Add(property);
                statusDtos.Where(i => i.Status == "booked").FirstOrDefault()?.CustomFields?.Add(projectRequired);
                statusDtos.Where(i => i.Status == "not_interested").FirstOrDefault()?.ChildTypes?.Where(i => i.Status == "unmatched_budget").FirstOrDefault()?.CustomFields?.Add(leadBudget);
                statusDtos.Where(i => i.Status == "not_interested").FirstOrDefault()?.ChildTypes?.Where(i => i.Status == "different_location").FirstOrDefault()?.CustomFields?.Add(locality);
                statusDtos.Where(i => i.Status == "not_interested").FirstOrDefault()?.ChildTypes?.Where(i => i.Status == "different_location").FirstOrDefault()?.CustomFields?.Add(city);
                statusDtos.Where(i => i.Status == "not_interested").FirstOrDefault()?.ChildTypes?.Where(i => i.Status == "different_location").FirstOrDefault()?.CustomFields?.Add(state);
                statusDtos.Where(i => i.Status == "dropped").FirstOrDefault()?.ChildTypes?.Where(i => i.Status == "purchased_from_others").FirstOrDefault()?.CustomFields?.Add(purchasedFromOther);
            }
            return statusDtos;
        }

        public static CustomFieldDto GetCustomField(string? fieldName,bool? isRequired)
        {
            return new CustomFieldDto()
            {
                IsRequired = isRequired ?? false,
                Field = new()
                {
                    Name = fieldName ?? string.Empty,
                    OrderRank = 0,
                    Module = "leads"
                }
            };
        }

    }
}
