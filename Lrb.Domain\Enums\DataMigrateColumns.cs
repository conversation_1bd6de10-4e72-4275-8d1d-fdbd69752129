﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Domain.Enums
{
    public enum DataMigrateColumns
    {
        Name = 0,
        ContactNo,
        AlternateContactNo,
        CreatedDate,
        ScheduledDate,
        Email,
        Notes,
        Source,
        SubSource,
        AssignToUser,
        BaseStatus,
        SubStatus,
        SiteVisitDone,
        SiteVisitNotDone,
        MeetingDone,
        MeetingNotDone,
        EnquiryTypes,
        City,
        State,
        Location,
        BasePropertyType,
        SubPropertyType,
        BHKTypes,
        BHKs,
        Budget,
        UpperBudget,
        LowerBudget,
        Property,
        Project,
        AgencyName,
        CountryCode,
        AlternativeNoCountryCode,
        Currency,
        ChannelPartnerName,
        Community,
        SubCommunity,
        TowerName,
        ReferralEmail,
        Baths,
        Beds,
        FurnishStatus,
        OfferingType,
        Country,
        PreferredFloor,
        ReferralContactNo,
        ReferralName,
        ChannelPartnerExecutiveName,
        ChannelPartnerContactNo,
        Designation,
        CarpetArea,
        CarpetAreaUnit,
        PropertyArea,
        PropertyAreaUnit,
        NetArea,
        NetAreaUnit,
        BuiltUpArea,
        BuiltUpAreaUnit,
        SaleableArea,
        SaleableAreaUnit,
        UnitName,
        ClusterName,
        Nationality,
        CampaignName,
        Purpose,
        PostalCode,
        CompanyName,
        CustomerCity,
        CustomerState,
        CustomerLocation,
        CustomerCommunity,
        CustomerSubCommunity,
        CustomerTowerName,
        CustomerCountry,
        CustomerPostalCode,
        SourcingManager,
        ClosingManager,
        Profession,
        PossesionType,
        PossessionDate,
        LandLine,
        Gender,
        DateOfBirth,
        MaritalStatus,
        AnniversaryDate
    }
}
