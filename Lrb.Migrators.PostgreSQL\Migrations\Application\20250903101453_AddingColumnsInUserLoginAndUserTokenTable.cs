﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class AddingColumnsInUserLoginAndUserTokenTable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "IdToken",
                schema: "Identity",
                table: "UserTokens",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RefreshToken",
                schema: "Identity",
                table: "UserTokens",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "TokenType",
                schema: "Identity",
                table: "UserTokens",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "UserLoginId",
                schema: "Identity",
                table: "UserTokens",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DeviceModel",
                schema: "Identity",
                table: "UserLogins",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DeviceName",
                schema: "Identity",
                table: "UserLogins",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DeviceUDID",
                schema: "Identity",
                table: "UserLogins",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Platform",
                schema: "Identity",
                table: "UserLogins",
                type: "integer",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IdToken",
                schema: "Identity",
                table: "UserTokens");

            migrationBuilder.DropColumn(
                name: "RefreshToken",
                schema: "Identity",
                table: "UserTokens");

            migrationBuilder.DropColumn(
                name: "TokenType",
                schema: "Identity",
                table: "UserTokens");

            migrationBuilder.DropColumn(
                name: "UserLoginId",
                schema: "Identity",
                table: "UserTokens");

            migrationBuilder.DropColumn(
                name: "DeviceModel",
                schema: "Identity",
                table: "UserLogins");

            migrationBuilder.DropColumn(
                name: "DeviceName",
                schema: "Identity",
                table: "UserLogins");

            migrationBuilder.DropColumn(
                name: "DeviceUDID",
                schema: "Identity",
                table: "UserLogins");

            migrationBuilder.DropColumn(
                name: "Platform",
                schema: "Identity",
                table: "UserLogins");
        }
    }
}
