﻿using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Common.ServiceBus;
using Lrb.Application.DataManagement.Mobile.Dtos;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Mobile.Dtos.v1;
using static Lrb.Application.Lead.Mobile.Requests.v1.GetAllLeadsOfflineRequestHandler;

namespace Lrb.Application.Lead.Mobile.Requests.v1
{
    public class GetAllLeadsByLastModifiedRequest : PaginationFilter, IRequest<Response<List<GetAllOfflineLeadsDto>>>
    {
        public DateTime? DateRangeFrom { get; set; }
        public DateTime? DateRangeTo { get; set; }
        public Guid? UserId { get; set; }
        public bool? SendOnlyAssignedLeads { get; set; }
        public string? TenantId { get; set; }
    }

    public class GetAllLeadsByLastModifiedRequestHandler : IRequestHandler<GetAllLeadsByLastModifiedRequest, Response<List<GetAllOfflineLeadsDto>>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _efLeadRepository;
        private readonly IUserService _userService;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        private readonly IServiceBus _serviceBus;
        public GetAllLeadsByLastModifiedRequestHandler(IRepositoryWithEvents<Domain.Entities.Lead> efLeadRepository,
        IUserService userService,
        IDapperRepository dapperRepository,
        ICurrentUser currentUser,
        IServiceBus serviceBus)
        {
            _efLeadRepository = efLeadRepository;
            _userService = userService;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
            _serviceBus = serviceBus;
        }

        public async Task<Response<List<GetAllOfflineLeadsDto>>> Handle(GetAllLeadsByLastModifiedRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var tenantId = request.TenantId ?? _currentUser.GetTenant();
                if (request.PageNumber < 0 && request.PageSize < 0)
                {
                    throw new ArgumentException("Pagination is required: PageNumber and PageSize must be greater than 0.");
                }
                if (request.UserId != null)
                {
                    var user = await _userService.GetAsync(request.UserId.ToString() ?? "", cancellationToken);
                    if (user.IsActive)
                    {
                        return new(await _dapperRepository.GetAllOfflineLeadsAsync(tenantId ?? string.Empty, request.UserId, request.SendOnlyAssignedLeads ?? false, request.DateRangeFrom, request.DateRangeTo, pageNumber: request.PageNumber, pageSize: request.PageSize, false));
                    }
                    return new("UnAuthorized");
                }
                return null;
            }
            catch (Exception ex)
            {
                return null;
            }
        }
    }
}
