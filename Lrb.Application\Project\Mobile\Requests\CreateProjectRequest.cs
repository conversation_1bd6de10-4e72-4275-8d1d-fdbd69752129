﻿using Lrb.Application.Common.GooglePlaces;
using Lrb.Application.GlobalSettings.Mobile;
using Lrb.Application.Project.Mobile.Requests.CommonHandler;
using Lrb.Application.Project.Mobile.Specs;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;

namespace Lrb.Application.Project.Mobile
{
    public class CreateProjectRequest : CreateProjectDto, IRequest<Response<Guid>>
    {
    }
    public class CreateProjectRequestHandler : ProjectCommonRequestHandler, IRequestHandler<CreateProjectRequest, Response<Guid>>
    {
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<MasterProjectType> _masterProjectTypeRepo;
        private readonly IRepositoryWithEvents<Block> _projectBlockRepo;
        private readonly IRepositoryWithEvents<UnitType> _unitTypeRepo;
        private readonly IRepositoryWithEvents<CustomMasterProjectType> _customProjectTypeRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalsettingRepo;



        public CreateProjectRequestHandler(
            IRepositoryWithEvents<Address> addressRepo,
            IRepositoryWithEvents<Domain.Entities.Project> projectRepo,
            IRepositoryWithEvents<Block> blockRepo,
            IRepositoryWithEvents<UnitType> unitTypeRepo,
            IGooglePlacesService googlePlacesService,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<Location> locationRepository,
            IRepositoryWithEvents<MasterProjectType> masterProjectTypeRepo,
            IMediator mediator,
            IServiceProvider serviceProvider,
            IRepositoryWithEvents<CustomMasterProjectType> customProjectTypeRepo,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalsettingRepo
            ) : base(serviceProvider)
        {
            _projectRepo = projectRepo;
            _leadRepositoryAsync = leadRepositoryAsync;
            _masterProjectTypeRepo = masterProjectTypeRepo;
            _projectBlockRepo = blockRepo;
            _unitTypeRepo = unitTypeRepo;
            _customProjectTypeRepo = customProjectTypeRepo;
            _globalsettingRepo = globalsettingRepo;
        }

        public async Task<Response<Guid>> Handle(CreateProjectRequest request, CancellationToken cancellationToken)
        {
            MasterProjectType? masterProjectType = null;
            Domain.Entities.GlobalSettings? globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
            var existingProject = await _projectRepo.FirstOrDefaultAsync(new GetProjectByNameSpecsV1(request.Name), cancellationToken);
            if (existingProject != null)
            {
                request.Name = $"{existingProject.Name}_COPY_{(existingProject.NumberOfClones ?? 0) + 1}";
            }
            if (request.ProjectTypeId != Guid.Empty && request.ProjectTypeId != default)
            {
                masterProjectType = await _masterProjectTypeRepo.GetByIdAsync(request.ProjectTypeId ?? Guid.Empty, cancellationToken);
                if (masterProjectType == null)
                {
                    throw new InvalidOperationException("Project type id does not belong to Master data.");
                }
            }

            #region Address

            var address = await CreateAddressAsync(request.Address, cancellationToken);

            #endregion

            Domain.Entities.Project project = request.Adapt<Domain.Entities.Project>();
            project.Address = address;
            project.ProjectType = masterProjectType;
            project.MonetaryInfo ??= new();
            project.MonetaryInfo.Currency = request?.MonetaryInfo?.Currency ?? globalSettings?.Countries?.FirstOrDefault()?.DefaultCurrency ?? "INR";

            try
            {
                project = await _projectRepo.AddAsync(project, cancellationToken);
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CreateProjectRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
            return new Response<Guid>(project.Id);

        }
    }
}
