﻿namespace Lrb.Domain.Enums
{
    public enum DataColumns
    {
        Name = 0,
        ContactNo,
        AlternateContactNo,
        ScheduledDate,
        SoldPrice,
        Email,
        Notes,
        Rating,
        Documents,
        Budget,
        City,
        State,
        Location,
        EnquiredFor,
        Property,
        Project,
        BasePropertyType,
        SubPropertyType,
        BHKType,
        NoOfBHK,
        Source,
        SubSource,
        AgencyName,
        ReferralContactNo,
        ReferralName,
        #region Custom
        CreatedDate,
        AssignToUser,
        CustomPropertyType,
        BaseStatus,
        SubStatus,
        UpperBudget,
        LowerBudget,
        Tag,
        PossessionDate,
        Profession,
        Designation,
        ChannelPartnerExecutiveName,
        ChannelPartnerName,
        ChannelPartnerContactNo,
        CountryCode,
        AlternativeNoCountryCode,
        Currency,
        SiteVisitDoneCount,
        SiteVisitNotDoneCount,
        MeetingDoneCount,
        MeetingNotDoneCount,
        CompanyName,
        Community,
        SubCommunity,
        TowerName,
        ReferralEmail,
        Baths,
        Beds,
        FurnishStatus,
        OfferingType,
        Country,
        PreferredFloor,
        CarpetArea,
        CarpetAreaUnit,
        PropertyArea,
        PropertyAreaUnit,
        NetArea,
        NetAreaUnit,
        BuiltUpArea,
        BuiltUpAreaUnit,
        SaleableArea,
        SaleableAreaUnit,
        UnitName,
        ClusterName,
        Nationality,
        CampaignName,
        Purpose,
        PostalCode,
        CustomerCity,
        CustomerState,
        CustomerLocation,
        CustomerCommunity,
        CustomerSubCommunity,
        CustomerTowerName,
        CustomerCountry,
        CustomerPostalCode,
        SourcingManager,
        ClosingManager,
        AssignToSecondaryUser,
        PossesionType,
        LandLine,
        Gender,
        DateOfBirth,
        MaritalStatus,
        AnniversaryDate
        #endregion
    }
}