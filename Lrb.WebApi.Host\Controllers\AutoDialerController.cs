﻿using Lrb.Application.AutoDialer.Web.Dtos;
using Lrb.Application.AutoDialer.Web.Requests;
using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Lead.Web;
using MediatR;

namespace Lrb.WebApi.Host.Controllers
{
    [Authorize]

    public class AutoDialerController : VersionedApiController
    {
        private readonly Lrb.Application.Common.ServiceBus.IServiceBus _serviceBus;
        private readonly ICurrentUser _currentUser;
        public AutoDialerController( Lrb.Application.Common.ServiceBus.IServiceBus serviceBus, ICurrentUser currentUser)
        {
            _serviceBus = serviceBus;
            _currentUser = currentUser;
        }
        [HttpGet]
        [TenantIdHeader]
        [OpenApiOperation("Get all leads in autodialer", "")]
        public async Task<PagedResponse<AutoDialerLeadDto, string>> GetBucketLeadsAsync([FromQuery] GetAllBucketLeadsRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("AddLeads")]
        [TenantIdHeader]
        [OpenApiOperation("Add leads to bucket", "")]
        public async Task<Response<bool>> AddLeadsToBucketAsync(ProcessAddLeadsToBucketRequest request)
        {
            if (request.LeadDetails?.Count >= 50)
            {
                var tenantId = _currentUser.GetTenant();
                var currentUser = _currentUser.GetUserId();
                request.CurrentUserId = currentUser;
                request.TenantId = tenantId;
                var payload = new InputPayload(tenantId ?? string.Empty, currentUser, request);
                await _serviceBus.RunAutoDialerBulkAddJobAsync(payload);
                return new() { Succeeded = true, Message = "Bulk Leads successfully added " };
            }
            else
            {
                return await Mediator.Send(request);
            }
        }

        [HttpGet("Config")]
        [TenantIdHeader]
        [OpenApiOperation("Get AutoDialer Configuration", "")]
        public async Task<Response<AutoDialerConfigDto>> GetAutoDialerConfigAsync()
        {
            return await Mediator.Send(new GetAutoDialerConfigurationRequest());
        }

        [HttpPut("Config")]
        [TenantIdHeader]
        [OpenApiOperation("Update AutoDialer Configuration", "")]
        public async Task<Response<bool>> UpdateAutoDialerConfigAsync(UpdateAutoDialerConfigurationRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("UserStatus")]
        [TenantIdHeader]
        [OpenApiOperation("Get CallingStatus Configuration", "")]
        public async Task<Response<bool>> GetCallingStatusAsync()
        {
            return await Mediator.Send(new GetUserCallingStatusRequest());
        }

        [HttpPut("UserStatus")]
        [TenantIdHeader]
        [OpenApiOperation("Update CallingStatus Configuration", "")]
        public async Task<Response<bool>> UpdateCallingStatusAsync(UpdateUserCallingStatusRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("StatusesCount")]
        [TenantIdHeader]
        [OpenApiOperation("Get Bucket LeadStatuses Count", "")]
        public async Task<Response<List<StatusDisplayCountDto>>> GetLeadStatusesCountAsync([FromQuery] GetBucketLeadStatusCountsRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpDelete("ClearBucket")]
        [TenantIdHeader]
        [OpenApiOperation("Remove Bucket items", "")]
        public async Task<Response<bool>> RemoveBucketItemsAsync(RemoveBucketItemsRequest request)
        {
            return await Mediator.Send(request);
        }
    }
}