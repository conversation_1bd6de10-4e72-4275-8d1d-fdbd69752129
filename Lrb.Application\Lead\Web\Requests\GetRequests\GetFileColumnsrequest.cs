﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Utils;
using Lrb.Domain.Entities.ErrorModule;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using Serilog;

namespace Lrb.Application.Lead.Web
{
    public class GetFileColumnsRequest : IRequest<Response<FileColumnDto>>
    {
        public IFormFile? File { get; set; }
        public GetFileColumnsRequest(IFormFile? file)
        {
            File = file;
        }
    }
    public class GetFileColumnsRequestHandler : IRequestHandler<GetFileColumnsRequest, Response<FileColumnDto>>
    {
        private readonly IBlobStorageService _blobStorageService;
        private readonly ILogger _logger;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;

        public GetFileColumnsRequestHandler(IBlobStorageService blobStorageService,
            Serilog.ILogger logger,
            ILeadRepositoryAsync leadRepositoryAsync)
        {
            _blobStorageService = blobStorageService;
            _logger = logger;
            _leadRepositoryAsync = leadRepositoryAsync;
        }
        public async Task<Response<FileColumnDto>> Handle(GetFileColumnsRequest request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.Information("GetFileColumnsRequestHandler -> Request: " + JsonConvert.SerializeObject(request));
                var file = request.File;
                if (file == null) { throw new ArgumentNullException(nameof(file)); }
                string fileExtension = string.Empty;
                if (!ExcelHelper.IsValidFile(file, out fileExtension))
                {
                    throw new InvalidOperationException("File format is invalid");
                }
                var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", "Lead", file);

                List<string> columns = new List<string>();
                if (key.Split('.').LastOrDefault() == "csv")
                {
                    columns = await CSVHelper.GetCSVColumns(file);
                }
                else
                {
                    columns = await ExcelHelper.GetExcelColumnsAsync(file);
                }
                FileColumnDto excelColumnsViewModel = new()
                {
                    S3BucketKey = key,
                    ColumnNames = columns,
                };
                return new Response<FileColumnDto>(excelColumnsViewModel);
            }
            catch(Exception e)
            {
                _logger.Error("GetFileColumnsRequestHandler -> Error: " + JsonConvert.SerializeObject(e));
                var error = new LrbError()
                {
                    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                    ErrorSource = e?.Source,
                    StackTrace = e?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "GetFileColumnsRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
            
        }
    }
}
