﻿using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using Google.Apis.Drive.v3.Data;
using Lrb.Application.DailyStatusUpdates.Dtos;
using Lrb.Application.Identity.Users;
using Lrb.Application.Reports.Web;
using Lrb.Application.Team.Web;
using Microsoft.Graph;
using System.Data;
using System.Reflection;
using System.Text.RegularExpressions;

namespace Lrb.Application.Lead.Web
{
    public static class CreateLeadHelper
    {
        public static List<CreateBulkLeadDto> GetCreateLeadRequests(this DataTable table, Dictionary<DataColumns, string> keyValues, List<string> columns)
        {
            List<CreateBulkLeadDto> leads = new List<CreateBulkLeadDto>();
            leads = table.AsEnumerable().Select(dataRow => new CreateBulkLeadDto()
            {
                Name = !keyValues.ContainsKey(DataColumns.Name) || string.IsNullOrEmpty(keyValues[DataColumns.Name]) ? string.Empty : dataRow[keyValues[DataColumns.Name]].ToString(),
                ContactNo = !keyValues.ContainsKey(DataColumns.ContactNo) || string.IsNullOrEmpty(keyValues[DataColumns.ContactNo]) ? string.Empty : dataRow[keyValues[DataColumns.ContactNo]].ToString(),
                Email = !keyValues.ContainsKey(DataColumns.Email) || string.IsNullOrEmpty(keyValues[DataColumns.Email]) ? string.Empty : dataRow[keyValues[DataColumns.Email]].ToString(),
                Notes = (!keyValues.ContainsKey(DataColumns.Notes) || string.IsNullOrEmpty(keyValues[DataColumns.Notes]) ? string.Empty : "Note" + " - " + dataRow[keyValues[DataColumns.Notes]].ToString() + ", \n") + string.Join(", \n", columns.Select(column => !string.IsNullOrEmpty(dataRow[column].ToString()) ? column + " - " + dataRow[column] : null).Where(i => i != null)),
                Rating = !keyValues.ContainsKey(DataColumns.Rating) || string.IsNullOrEmpty(keyValues[DataColumns.Rating]) ? string.Empty : dataRow[keyValues[DataColumns.Rating]].ToString(),
                Enquiry = new()
                {
                    LowerBudget = !keyValues.ContainsKey(DataColumns.Budget) || string.IsNullOrEmpty(keyValues[DataColumns.Budget]) ? 0 : long.TryParse(dataRow[keyValues[DataColumns.Budget]].ToString(), out long convertedLowerBudget) ? convertedLowerBudget : 0,
                    UpperBudget = !keyValues.ContainsKey(DataColumns.Budget) || string.IsNullOrEmpty(keyValues[DataColumns.Budget]) ? 0 : long.TryParse(dataRow[keyValues[DataColumns.Budget]].ToString(), out long convertedUpperBudget) ? convertedUpperBudget : 0,
                    Address = new()
                    {
                        City = !keyValues.ContainsKey(DataColumns.City) || string.IsNullOrEmpty(keyValues[DataColumns.City]) ? string.Empty : dataRow[keyValues[DataColumns.City]].ToString(),
                        State = !keyValues.ContainsKey(DataColumns.State) || string.IsNullOrEmpty(keyValues[DataColumns.State]) ? string.Empty : dataRow[keyValues[DataColumns.State]].ToString(),
                        SubLocality = !keyValues.ContainsKey(DataColumns.Location) || string.IsNullOrEmpty(keyValues[DataColumns.Location]) ? string.Empty : dataRow[keyValues[DataColumns.Location]].ToString(),
                    }
                },
                AlternateContactNo = !keyValues.ContainsKey(DataColumns.AlternateContactNo) || string.IsNullOrEmpty(keyValues[DataColumns.AlternateContactNo]) ? string.Empty : dataRow[keyValues[DataColumns.AlternateContactNo]].ToString(),
                //PropertiesList =

            }).ToList();
            return leads;
        }

        public static async Task<ViewLeadDto?> SetUsersInViewLeadDtoAsync(this ViewLeadDto? leadDto, IUserService _userService, CancellationToken cancellationToken, Guid? currentUserId = null, LeadSource? source = null)
        {
            if (leadDto == null) { return null; }
            var userIds = new List<string>()
            {
                leadDto?.AssignTo?.ToString() ?? string.Empty,
                leadDto?.SecondaryUserId?.ToString() ?? string.Empty,
                leadDto?.LastModifiedBy.ToString() ?? string.Empty,
                leadDto?.AssignedFrom.ToString() ?? string.Empty,
                leadDto?.SourcingManager?.ToString() ?? string.Empty,
                leadDto?.ClosingManager?.ToString() ?? string.Empty,
                leadDto?.BookedBy?.ToString() ?? string.Empty,
                currentUserId?.ToString() ?? string.Empty,
                leadDto?.SecondaryFromUserId?.ToString() ?? string.Empty,
            };
            var users = await _userService.GetListOfUsersByIdsAsync(userIds?.Distinct()?.ToList() ?? new List<string>(), cancellationToken);
            UserDetailsDto? assignedUser = users?.FirstOrDefault(i => i.Id == (leadDto?.AssignTo ?? Guid.Empty));
            //try
            //{
            //    assignedUser = await _userService.GetAsync(leadDto?.AssignTo?.ToString() ?? string.Empty, cancellationToken);
            //}
            //catch (NotFoundException e) { }
            if (assignedUser != null)
            {
                leadDto.AssignedUser = assignedUser?.Adapt<UserDto>();
                leadDto.AssignedUser.Name = assignedUser?.FirstName + " " + assignedUser?.LastName;

            }
            UserDetailsDto? secondaryUser = users?.FirstOrDefault(i => i.Id == (leadDto?.SecondaryUserId ?? Guid.Empty));
            //try
            //{
            //    secondaryUser = await _userService.GetAsync(leadDto?.SecondaryUserId?.ToString() ?? string.Empty, cancellationToken);
            //}
            //catch (NotFoundException e) { }
            if (secondaryUser != null)
            {
                leadDto.SecondaryUser = secondaryUser?.Adapt<UserDto>();
                leadDto.SecondaryUser.Name = secondaryUser?.FirstName + " " + secondaryUser?.LastName;

            }
            //Guid lastModifierId = (leadDto?.LastModifiedBy != null && leadDto.LastModifiedBy != Guid.Empty)? leadDto.LastModifiedBy: currentUserId ?? Guid.Empty;
            //UserDetailsDto? lastModifiedByUser = users?.FirstOrDefault(i => i.Id == lastModifierId);

            UserDetailsDto? lastModifiedByUser = users?.FirstOrDefault(i => i.Id == (currentUserId ?? leadDto?.LastModifiedBy));
            //try
            //{
            //    lastModifiedByUser = await _userService.GetAsync(leadDto?.LastModifiedBy.ToString() ?? string.Empty, cancellationToken);
            //}
            //catch (NotFoundException e) { }

            if (lastModifiedByUser != null)
            {
                leadDto.LastModifiedByUser = lastModifiedByUser?.Adapt<UserDto>();
                leadDto.LastModifiedByUser.Name = lastModifiedByUser?.FirstName + " " + lastModifiedByUser?.LastName;
            }
            else if (source.HasValue)
            {
                leadDto.LastModifiedByUser = new UserDto();
                leadDto.LastModifiedByUser.Name = "Integration";
            }
            else
            {
                leadDto.LastModifiedByUser = new UserDto();
                leadDto.LastModifiedByUser.Name = "System";
            }

            UserDetailsDto? secondaryFromUser = users?.FirstOrDefault(i => i.Id == (leadDto?.SecondaryFromUserId ?? Guid.Empty));
            if (secondaryFromUser != null)
            {
                leadDto.SecondaryFromUser = secondaryFromUser?.Adapt<UserDto>();
                leadDto.SecondaryFromUser.Name = secondaryFromUser?.FirstName + " " + secondaryFromUser?.LastName;
            }

            UserDetailsDto? assignedFromUser = users?.FirstOrDefault(i => i.Id == (leadDto?.AssignedFrom ?? Guid.Empty));
            //try
            //{
            //    assignedFromUser = await _userService.GetAsync(leadDto?.AssignedFrom.ToString() ?? string.Empty, cancellationToken);
            //}
            //catch (NotFoundException e) { }
            if (assignedFromUser != null)
            {
                leadDto.AssignedFromUser = assignedFromUser?.Adapt<UserDto>();
                leadDto.AssignedFromUser.Name = assignedFromUser?.FirstName + " " + assignedFromUser?.LastName;
            }
            UserDetailsDto? sourcingManager = users?.FirstOrDefault(i => i.Id == (leadDto?.SourcingManager ?? Guid.Empty));
            //try
            //{
            //    sourcingManager = await _userService.GetAsync(leadDto?.SourcingManager?.ToString() ?? string.Empty, cancellationToken);
            //}
            //catch (NotFoundException e) { }
            if (sourcingManager != null)
            {
                leadDto.SourcingManagerUser = sourcingManager?.Adapt<UserDto>();
                leadDto.SourcingManagerUser.Name = sourcingManager?.FirstName + " " + sourcingManager?.LastName;
            }
            UserDetailsDto? closingManager = users?.FirstOrDefault(i => i.Id == (leadDto?.ClosingManager ?? Guid.Empty));
            //try
            //{
            //    closingManager = await _userService.GetAsync(leadDto?.ClosingManager?.ToString() ?? string.Empty, cancellationToken);
            //}
            //catch (NotFoundException e) { }
            if (closingManager != null)
            {
                leadDto.ClosingManagerUser = closingManager?.Adapt<UserDto>();
                leadDto.ClosingManagerUser.Name = closingManager?.FirstName + " " + closingManager?.LastName;
            }
            UserDetailsDto? bookedByUser = users?.FirstOrDefault(i => i.Id == (leadDto?.BookedBy ?? Guid.Empty));
            //try
            //{
            //    bookedByUser = await _userService.GetAsync(leadDto?.BookedBy?.ToString() ?? string.Empty, cancellationToken);

            //}
            //catch (NotFoundException e) { }
            if (bookedByUser != null)
            {
                leadDto.BookedByUser = bookedByUser?.Adapt<UserDto>();
                leadDto.BookedByUser.Name = bookedByUser?.FirstName + " " + bookedByUser?.LastName;
            }
            return leadDto;
        }

        public static async Task<ViewLeadDto?> SetUsersInViewLeadDtoAsync(this ViewLeadDto? leadDto, List<UserDetailsDto> users, CancellationToken cancellationToken)
        {
            if (leadDto == null) { return null; }
            UserDetailsDto? assignedUser = null;
            try
            {
                assignedUser = users?.FirstOrDefault(i => i.Id == (leadDto.AssignTo ?? Guid.Empty)); // await _userService.GetAsync(leadDto?.AssignTo?.ToString() ?? string.Empty, cancellationToken);
            }
            catch (NotFoundException e) { }
            if (assignedUser != null)
            {
                leadDto.AssignedUser = assignedUser?.Adapt<UserDto>();
                leadDto.AssignedUser.Name = assignedUser?.FirstName + " " + assignedUser?.LastName;

            }
            UserDetailsDto? secondaryUser = null;
            try
            {
                secondaryUser = users?.FirstOrDefault(i => i.Id == (leadDto.SecondaryUserId ?? Guid.Empty)); // await _userService.GetAsync(leadDto?.AssignTo?.ToString() ?? string.Empty, cancellationToken);
            }
            catch (NotFoundException e) { }
            if (secondaryUser != null)
            {
                leadDto.SecondaryUser = secondaryUser?.Adapt<UserDto>();
                leadDto.SecondaryUser.Name = secondaryUser?.FirstName + " " + secondaryUser?.LastName;

            }
            UserDetailsDto? lastModifiedByUser = null;
            try
            {
                lastModifiedByUser = users?.FirstOrDefault(i => i.Id == leadDto.LastModifiedBy); //await _userService.GetAsync(leadDto?.LastModifiedBy.ToString() ?? string.Empty, cancellationToken);
            }
            catch (NotFoundException e) { }
            if (lastModifiedByUser != null)
            {
                leadDto.LastModifiedByUser = lastModifiedByUser?.Adapt<UserDto>();
                leadDto.LastModifiedByUser.Name = lastModifiedByUser?.FirstName + " " + lastModifiedByUser?.LastName;
            }
            UserDetailsDto? secondaryFromUser = null;
            try
            {
                secondaryFromUser = users?.FirstOrDefault(i => i.Id == leadDto.SecondaryFromUserId);
            }
            catch (NotFoundException e) { }
            if(secondaryFromUser != null)
            {
                leadDto.SecondaryFromUser = secondaryFromUser?.Adapt<UserDto>();
                leadDto.SecondaryFromUser.Name=secondaryFromUser?.FirstName + " " + secondaryFromUser?.LastName;
            }
            UserDetailsDto? assignedFromUser = null;
            try
            {
                assignedFromUser = users?.FirstOrDefault(i => i.Id == leadDto.AssignedFrom);
            }
            catch (NotFoundException e) { }
            if (assignedFromUser != null)
            {
                leadDto.AssignedFromUser = assignedFromUser?.Adapt<UserDto>();
                leadDto.AssignedFromUser.Name = assignedFromUser?.FirstName + " " + assignedFromUser?.LastName;
            }
            UserDetailsDto? sourcingManager = null;
            try
            {
                sourcingManager = users?.FirstOrDefault(i => i.Id == leadDto.SourcingManager);
            }
            catch (NotFoundException e) { }
            if(sourcingManager != null)
            {
                leadDto.SourcingManagerUser = sourcingManager?.Adapt<UserDto>();
                leadDto.SourcingManagerUser.Name = sourcingManager?.FirstName + " "+ sourcingManager?.LastName;
            }
            UserDetailsDto? closingManager = null;
            try
            {
                closingManager = users?.FirstOrDefault(i => i.Id == leadDto.ClosingManager);
            }
            catch (NotFoundException e) { }
            if (closingManager != null)
            {
                leadDto.ClosingManagerUser = closingManager?.Adapt<UserDto>();
                leadDto.ClosingManagerUser.Name = closingManager?.FirstName + " " + closingManager?.LastName;
            }
            UserDetailsDto? bookedByUser = null;
            try
            {
                bookedByUser = users?.FirstOrDefault(i => i.Id == leadDto.BookedBy);
            }
            catch (NotFoundException e) { }
            if (bookedByUser != null)
            {
                leadDto.BookedByUser = assignedUser?.Adapt<UserDto>();
                leadDto.BookedByUser.Name = bookedByUser?.FirstName + " " + bookedByUser?.LastName;
            }
            return leadDto;
        }


        public static MemoryStream CreateExcelData(List<InvalidData> inValidLeads)
        {
            using MemoryStream stream = new MemoryStream();
            SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Create(stream, SpreadsheetDocumentType.Workbook);
            WorkbookPart workbookpart = spreadsheetDocument.AddWorkbookPart();

            //Add stylesheet for wrap text and column and row auto height and width
            //var styleSheet = AddStyleSheet(spreadsheetDocument);

            workbookpart.Workbook = new DocumentFormat.OpenXml.Spreadsheet.Workbook();
            WorksheetPart worksheetPart = workbookpart.AddNewPart<WorksheetPart>();
            worksheetPart.Worksheet = new Worksheet(new SheetData());
            Sheets sheets = spreadsheetDocument.WorkbookPart.Workbook.AppendChild<Sheets>(new Sheets());
            Sheet sheet = new Sheet()
            {
                Id = spreadsheetDocument.WorkbookPart.GetIdOfPart(worksheetPart),
                SheetId = 1,
                Name = "mySheet"
            };
            sheets.Append(sheet);
            Worksheet worksheet = worksheetPart.Worksheet;
            

            SheetData sheetData = worksheet.GetFirstChild<SheetData>();
            List<string> headers = new();
            var obj = inValidLeads.FirstOrDefault();
            Type objType = obj.GetType();
            System.Reflection.PropertyInfo[] properties = objType.GetProperties();
            foreach (System.Reflection.PropertyInfo property in properties)
            {
                headers.Add(property.Name);
            }
            Row row1 = new Row();
            foreach (var item in headers)
            {
                Cell cell1 = new Cell()
                {
                    CellValue = new CellValue(item),
                    DataType = CellValues.String,

                };
                row1.Append(cell1);
            }
            sheetData.Append(row1);
            foreach (var inValidLead in inValidLeads)
            {
                Row row = new Row();
                CreateCell(row, inValidLead.Created.ToString(), CellValues.Date);
                CreateCell(row, inValidLead.Name);
                CreateCell(row, inValidLead.ContactNo);
                CreateCell(row, inValidLead.Email);
                //CreateCell(row, inValidLead.Status);
                //CreateCell(row, inValidLead.SubStatus);
                CreateCell(row, inValidLead.AssignTo);
                CreateCell(row, inValidLead.Source);
                CreateCell(row, inValidLead.SubSource);
                CreateCell(row, inValidLead?.Notes ?? null);
                //CreateCell(row, inValidLead.Rating);
                //CreateCell(row, inValidLead.Budget);
                //CreateCell(row, inValidLead.City);
                //CreateCell(row, inValidLead.State);
                //CreateCell(row, inValidLead.Location);
                CreateCell(row, inValidLead.Errors);
                //CreateCell(row, inValidLead.RepeatedCount.ToString(), CellValues.Number);
                sheetData.Append(row);

            }
            worksheetPart.Worksheet.Save();
            workbookpart.Workbook.Save();
            spreadsheetDocument.Close();
            return stream;
        }
        public static MemoryStream V2CreateExcelData(List<V2InvalidData> inValidLeads)
        {
            using MemoryStream stream = new MemoryStream();
            SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Create(stream, SpreadsheetDocumentType.Workbook);
            WorkbookPart workbookpart = spreadsheetDocument.AddWorkbookPart();

            //Add stylesheet for wrap text and column and row auto height and width
            //var styleSheet = AddStyleSheet(spreadsheetDocument);

            workbookpart.Workbook = new DocumentFormat.OpenXml.Spreadsheet.Workbook();
            WorksheetPart worksheetPart = workbookpart.AddNewPart<WorksheetPart>();
            worksheetPart.Worksheet = new Worksheet(new SheetData());
            Sheets sheets = spreadsheetDocument.WorkbookPart.Workbook.AppendChild<Sheets>(new Sheets());
            Sheet sheet = new Sheet()
            {
                Id = spreadsheetDocument.WorkbookPart.GetIdOfPart(worksheetPart),
                SheetId = 1,
                Name = "mySheet"
            };
            sheets.Append(sheet);
            Worksheet worksheet = worksheetPart.Worksheet;


            SheetData sheetData = worksheet.GetFirstChild<SheetData>();
            List<string> headers = new();
            var obj = inValidLeads.FirstOrDefault();
            Type objType = obj.GetType();
            System.Reflection.PropertyInfo[] properties = objType.GetProperties();
            foreach (System.Reflection.PropertyInfo property in properties)
            {
                headers.Add(property.Name);
            }
            Row row1 = new Row();
            foreach (var item in headers)
            {
                Cell cell1 = new Cell()
                {
                    CellValue = new CellValue(item),
                    DataType = CellValues.String,

                };
                row1.Append(cell1);
            }
            sheetData.Append(row1);
            foreach (var inValidLead in inValidLeads)
            {
                Row row = new Row();
                CreateCell(row, inValidLead.Created.ToString(), CellValues.Date);
                CreateCell(row, inValidLead.Name);
                CreateCell(row, inValidLead.ContactNo);
                CreateCell(row, inValidLead.Email);
                //CreateCell(row, inValidLead.Status);
                //CreateCell(row, inValidLead.SubStatus);
                CreateCell(row, inValidLead.Source);
                CreateCell(row, inValidLead.SubSource);
                CreateCell(row, inValidLead?.Notes ?? null);
                //CreateCell(row, inValidLead.Rating);
                //CreateCell(row, inValidLead.Budget);
                //CreateCell(row, inValidLead.City);
                //CreateCell(row, inValidLead.State);
                //CreateCell(row, inValidLead.Location);
                CreateCell(row, inValidLead.Errors);
                //CreateCell(row, inValidLead.RepeatedCount.ToString(), CellValues.Number);
                sheetData.Append(row);

            }
            worksheetPart.Worksheet.Save();
            workbookpart.Workbook.Save();
            spreadsheetDocument.Close();
            return stream;
        }
        public static void CreateCell(Row row, string value, CellValues type = CellValues.String)
        {
            Cell cell = new Cell()
            {
                CellValue = new CellValue(value),
                DataType = type,
            };
            row.Append(cell);
        }


        public static List<string> GetColumnNames(this DataTable table, Dictionary<DataColumns, string> keyValues)
        {
            List<string> columns = new();
            if (keyValues.ContainsKey(DataColumns.Notes))
            {
                if (!string.IsNullOrEmpty(keyValues[DataColumns.Notes]))
                {
                    columns.Add(keyValues[DataColumns.Notes]);
                    keyValues.Remove(DataColumns.Notes);
                }

            }
            foreach (DataColumn column in table.Columns)
            {
                if (!keyValues.ContainsValue(column.ColumnName) && !column.ColumnName.Contains("S. No"))
                {
                    if (!columns.Contains(column.ColumnName))
                    {
                        columns.Add(column.ColumnName);
                    }
                }
            }
            return columns;
        }

        public static double GetNoOfBHK(string noOfBHK)
        {
            try
            {
                Regex regex = new Regex(@"^\d+");
                Match match = regex.Match(noOfBHK);
                double integer = 0;

                if (match.Success)
                {
                    integer = double.Parse(match.Value);
                }
                return integer;
            }
            catch (Exception e)
            {
                throw;
            }
           

        }
        public static int Get(string beds)
        {
            try
            {
                Regex regex = new Regex(@"^\d+");
                Match match = regex.Match(beds);
                int integer = 0;

                if (match.Success)
                {
                    integer = int.Parse(match.Value);
                }
                return integer;
            }
            catch (Exception e)
            {
                return 0;
            }

        }

        public static async Task<ViewLeadDto?> SetUsersInViewNewLeadHistoryDtoAsync(this ViewLeadDto? leadDto, List<UserDetailsDto> users, CancellationToken cancellationToken, Guid? currentUserId = null, LeadSource? source = null)
        {
            if (leadDto == null) { return null; }
            UserDetailsDto? assignedUser = users?.FirstOrDefault(i => i.Id == (leadDto?.AssignTo ?? Guid.Empty));
            if (assignedUser != null)
            {
                leadDto.AssignedUser = assignedUser?.Adapt<UserDto>();
                leadDto.AssignedUser.Name = assignedUser?.FirstName + " " + assignedUser?.LastName;

            }
            UserDetailsDto? secondaryUser = users?.FirstOrDefault(i => i.Id == (leadDto?.SecondaryUserId ?? Guid.Empty));
            if (secondaryUser != null)
            {
                leadDto.SecondaryUser = secondaryUser?.Adapt<UserDto>();
                leadDto.SecondaryUser.Name = secondaryUser?.FirstName + " " + secondaryUser?.LastName;

            }
            UserDetailsDto? lastModifiedByUser = users?.FirstOrDefault(i => i.Id == (currentUserId ?? leadDto?.LastModifiedBy));
            if (lastModifiedByUser != null)
            {
                leadDto.LastModifiedByUser = lastModifiedByUser?.Adapt<UserDto>();
                leadDto.LastModifiedByUser.Name = lastModifiedByUser?.FirstName + " " + lastModifiedByUser?.LastName;
            }
            else if (source.HasValue)
            {
                leadDto.LastModifiedByUser = new UserDto();
                leadDto.LastModifiedByUser.Name = "Integration";
            }
            else
            {
                leadDto.LastModifiedByUser = new UserDto();
                leadDto.LastModifiedByUser.Name = "System";
            }

            UserDetailsDto? secondaryFromUser = users?.FirstOrDefault(i => i.Id == (leadDto?.SecondaryFromUserId ?? Guid.Empty));
            if (secondaryFromUser != null)
            {
                leadDto.SecondaryFromUser = secondaryFromUser?.Adapt<UserDto>();
                leadDto.SecondaryFromUser.Name = secondaryFromUser?.FirstName + " " + secondaryFromUser?.LastName;
            }

            UserDetailsDto? assignedFromUser = users?.FirstOrDefault(i => i.Id == (leadDto?.AssignedFrom ?? Guid.Empty));
            if (assignedFromUser != null)
            {
                leadDto.AssignedFromUser = assignedFromUser?.Adapt<UserDto>();
                leadDto.AssignedFromUser.Name = assignedFromUser?.FirstName + " " + assignedFromUser?.LastName;
            }
            UserDetailsDto? sourcingManager = users?.FirstOrDefault(i => i.Id == (leadDto?.SourcingManager ?? Guid.Empty));
            if (sourcingManager != null)
            {
                leadDto.SourcingManagerUser = sourcingManager?.Adapt<UserDto>();
                leadDto.SourcingManagerUser.Name = sourcingManager?.FirstName + " " + sourcingManager?.LastName;
            }
            UserDetailsDto? closingManager = users?.FirstOrDefault(i => i.Id == (leadDto?.ClosingManager ?? Guid.Empty));
            if (closingManager != null)
            {
                leadDto.ClosingManagerUser = closingManager?.Adapt<UserDto>();
                leadDto.ClosingManagerUser.Name = closingManager?.FirstName + " " + closingManager?.LastName;
            }
            UserDetailsDto? bookedByUser = users?.FirstOrDefault(i => i.Id == (leadDto?.BookedBy ?? Guid.Empty));
            if (bookedByUser != null)
            {
                leadDto.BookedByUser = bookedByUser?.Adapt<UserDto>();
                leadDto.BookedByUser.Name = bookedByUser?.FirstName + " " + bookedByUser?.LastName;
            }
            return leadDto;
        }
    }
}
