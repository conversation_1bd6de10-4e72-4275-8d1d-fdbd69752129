﻿{
  "HangfireSettings": {
    "Route": "/jobs",
    "Dashboard": {
      "AppPath": "/",
      "StatsPollingInterval": 2000,
      "DashboardTitle": "Jobs"
    },
    "Server": {
      "HeartbeatInterval": "00:00:30",
      "Queues": [
        "default",
        "notdefault"
      ],
      "SchedulePollingInterval": "00:00:15",
      "ServerCheckInterval": "00:05:00",
      "ServerName": null,
      "ServerTimeout": "00:05:00",
      "ShutdownTimeout": "00:00:15",
      "WorkerCount": 1
    },
    "Storage": {
      "StorageProvider": "postgresql",
      // "ConnectionString": "Host=lrb-pgsql-sbx.cu22ll1a4z3g.ap-south-1.rds.amazonaws.com;Port=5432;Database=leadratBlackApp;Username=dbmasteruser;Password=********************;Pooling=true;MinPoolSize=3;MaxPoolSize=500;",
      //"ConnectionString": "Host=ls-e721c9cb6fe76c20e99b4da2632fd51e40d87358.coluzekxwdtv.ap-south-1.rds.amazonaws.com;Port=5432;Database=leadratBlackApp;Username=dbmasteruser;Password=************`H9hTJK5ojM)C=$C6w,0;Minimum Pool Size=5;Maximum Pool Size=100;Connection Idle Lifetime=10;",
      "ConnectionString": "Host=lrb-dev-new.postgres.database.azure.com;Port=5432;Database=leadratBlackApp;Username=dbmasteruser;Password=************`H9hTJK5ojM)C=$C6w,0;Pooling=true;MinPoolSize=3;MaxPoolSize=1000;",
      "Options": {
        "CommandBatchMaxTimeout": "00:05:00",
        "QueuePollInterval": "00:00:01",
        "UseRecommendedIsolationLevel": true,
        "SlidingInvisibilityTimeout": "00:05:00",
        "DisableGlobalLocks": true
      }
    },
    "Credentials": {
      "User": "Admin",
      "Password": "S3(r3tP@55w0rd"
    }
  },
  "PushNotification": {
    "SendVia": "awsPinpoint"
  }
}