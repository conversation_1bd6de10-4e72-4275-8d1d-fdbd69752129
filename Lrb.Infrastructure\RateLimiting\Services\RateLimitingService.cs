using System.Collections.Concurrent;
using Lrb.Infrastructure.RateLimiting.Configuration;
using Lrb.Infrastructure.RateLimiting.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Lrb.Infrastructure.RateLimiting.Services;

/// <summary>
/// Thread-safe implementation of rate limiting service
/// </summary>
public class RateLimitingService : IRateLimitingService, IDisposable
{
    private readonly RateLimitingSettings _settings;
    private readonly ILogger<RateLimitingService> _logger;
    private readonly ConcurrentDictionary<string, RateLimitInfo> _rateLimitStore;
    private readonly ConcurrentDictionary<string, ConcurrentQueue<QueuedRequest>> _requestQueues;
    private readonly Timer _cleanupTimer;
    private readonly Timer _queueProcessorTimer;
    private readonly SemaphoreSlim _semaphore;
    private bool _disposed;

    public RateLimitingService(
        IOptions<RateLimitingSettings> settings,
        ILogger<RateLimitingService> logger)
    {
        _settings = settings.Value;
        _logger = logger;
        _rateLimitStore = new ConcurrentDictionary<string, RateLimitInfo>();
        _requestQueues = new ConcurrentDictionary<string, ConcurrentQueue<QueuedRequest>>();
        _semaphore = new SemaphoreSlim(1, 1);

        // Setup cleanup timer
        var cleanupInterval = TimeSpan.FromMinutes(_settings.CleanupIntervalMinutes);
        _cleanupTimer = new Timer(async _ => await CleanupExpiredEntriesAsync(), null, cleanupInterval, cleanupInterval);

        // Setup queue processor timer (runs every second)
        _queueProcessorTimer = new Timer(async _ => await ProcessQueuedRequestsAsync(), null, TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(1));

        _logger.LogInformation("Rate limiting service initialized with {RequestLimit} requests per {TimeWindow} seconds",
            _settings.RequestLimit, _settings.TimeWindowSeconds);
    }

    public async Task<RateLimitResult> CheckRateLimitAsync(string clientId, CancellationToken cancellationToken = default)
    {
        if (!_settings.Enabled)
        {
            return new RateLimitResult { IsAllowed = true };
        }

        await _semaphore.WaitAsync(cancellationToken);
        try
        {
            var rateLimitInfo = GetOrCreateRateLimitInfo(clientId);
            var windowDuration = TimeSpan.FromSeconds(_settings.TimeWindowSeconds);

            // Update rate limit info based on window type
            if (_settings.WindowType == TimeWindowType.Sliding)
            {
                rateLimitInfo.CleanupOldTimestamps(windowDuration);
            }
            else // Fixed window
            {
                if (DateTime.UtcNow > rateLimitInfo.WindowEnd)
                {
                    rateLimitInfo.Reset(DateTime.UtcNow, windowDuration);
                }
            }

            // Check if request can be allowed immediately
            if (!rateLimitInfo.IsLimitExceeded)
            {
                rateLimitInfo.AddRequest();
                _logger.LogDebug("Request allowed for client {ClientId}. Count: {Count}/{Limit}",
                    clientId, rateLimitInfo.RequestCount, rateLimitInfo.RequestLimit);

                return new RateLimitResult
                {
                    IsAllowed = true,
                    RateLimitInfo = rateLimitInfo
                };
            }

            // Rate limit exceeded - check if queuing is enabled
            if (!_settings.EnableRequestQueuing)
            {
                _logger.LogWarning("Rate limit exceeded for client {ClientId}. Request rejected.",
                    clientId);

                return new RateLimitResult
                {
                    IsAllowed = false,
                    RateLimitInfo = rateLimitInfo,
                    RetryAfterSeconds = (int)rateLimitInfo.TimeUntilReset.TotalSeconds
                };
            }

            // Queue the request
            var queuedRequest = await QueueRequestAsync(clientId, cancellationToken);
            if (queuedRequest == null)
            {
                _logger.LogWarning("Queue is full for client {ClientId}. Request rejected.",
                    clientId);

                return new RateLimitResult
                {
                    IsAllowed = false,
                    RateLimitInfo = rateLimitInfo,
                    RetryAfterSeconds = (int)rateLimitInfo.TimeUntilReset.TotalSeconds
                };
            }

            var queuePosition = GetQueuePosition(clientId, queuedRequest.Id);
            var estimatedWaitTime = EstimateWaitTime(queuePosition);

            _logger.LogInformation("Request queued for client {ClientId}. Position: {Position}, Estimated wait: {Wait}",
                clientId, queuePosition, estimatedWaitTime);

            return new RateLimitResult
            {
                IsAllowed = false,
                IsQueued = true,
                RateLimitInfo = rateLimitInfo,
                QueuePosition = queuePosition,
                EstimatedWaitTime = estimatedWaitTime,
                QueueTask = queuedRequest.CompletionSource.Task
            };
        }
        finally
        {
            _semaphore.Release();
        }
    }

    public async Task ResetRateLimitAsync(string clientId)
    {
        await _semaphore.WaitAsync();
        try
        {
            if (_rateLimitStore.TryGetValue(clientId, out var info))
            {
                var windowDuration = TimeSpan.FromSeconds(_settings.TimeWindowSeconds);
                info.Reset(DateTime.UtcNow, windowDuration);
                _logger.LogInformation("Rate limit reset for client {ClientId}", clientId);
            }

            // Clear any queued requests for this client
            if (_requestQueues.TryGetValue(clientId, out var queue))
            {
                while (queue.TryDequeue(out var queuedRequest))
                {
                    queuedRequest.Cancel();
                }
            }
        }
        finally
        {
            _semaphore.Release();
        }
    }

    public async Task CleanupExpiredEntriesAsync()
    {
        await _semaphore.WaitAsync();
        try
        {
            var expiredKeys = new List<string>();
            var cutoffTime = DateTime.UtcNow.AddMinutes(-_settings.CleanupIntervalMinutes * 2);

            foreach (var kvp in _rateLimitStore)
            {
                if (kvp.Value.LastUpdated < cutoffTime)
                {
                    expiredKeys.Add(kvp.Key);
                }
            }

            foreach (var key in expiredKeys)
            {
                _rateLimitStore.TryRemove(key, out _);
                _requestQueues.TryRemove(key, out _);
            }

            if (expiredKeys.Count > 0)
            {
                _logger.LogDebug("Cleaned up {Count} expired rate limit entries", expiredKeys.Count);
            }
        }
        finally
        {
            _semaphore.Release();
        }
    }

    private RateLimitInfo GetOrCreateRateLimitInfo(string clientId)
    {
        return _rateLimitStore.GetOrAdd(clientId, _ =>
        {
            var windowDuration = TimeSpan.FromSeconds(_settings.TimeWindowSeconds);
            var now = DateTime.UtcNow;
            return new RateLimitInfo
            {
                ClientId = clientId,
                RequestLimit = _settings.RequestLimit,
                WindowStart = now,
                WindowEnd = now.Add(windowDuration),
                LastUpdated = now
            };
        });
    }

    private async Task<QueuedRequest?> QueueRequestAsync(string clientId, CancellationToken cancellationToken)
    {
        var queue = _requestQueues.GetOrAdd(clientId, _ => new ConcurrentQueue<QueuedRequest>());

        if (queue.Count >= _settings.MaxQueueSize)
        {
            return null;
        }

        var queuedRequest = new QueuedRequest
        {
            ClientId = clientId,
            ExpiresAt = DateTime.UtcNow.AddSeconds(_settings.MaxQueueWaitTimeSeconds),
            CancellationToken = cancellationToken
        };

        queue.Enqueue(queuedRequest);
        return queuedRequest;
    }

    private int GetQueuePosition(string clientId, string requestId)
    {
        if (!_requestQueues.TryGetValue(clientId, out var queue))
        {
            return 0;
        }

        var position = 1;
        foreach (var request in queue)
        {
            if (request.Id == requestId)
            {
                return position;
            }
            position++;
        }

        return 0;
    }

    private TimeSpan EstimateWaitTime(int queuePosition)
    {
        if (queuePosition <= 0)
        {
            return TimeSpan.Zero;
        }

        // Estimate based on rate limit settings
        var requestsPerSecond = (double)_settings.RequestLimit / _settings.TimeWindowSeconds;
        var estimatedSeconds = queuePosition / requestsPerSecond;
        return TimeSpan.FromSeconds(Math.Max(1, estimatedSeconds));
    }

    private async Task ProcessQueuedRequestsAsync()
    {
        if (_disposed)
            return;

        try
        {
            await _semaphore.WaitAsync();
            try
            {
                foreach (var kvp in _requestQueues.ToList())
                {
                    var clientId = kvp.Key;
                    var queue = kvp.Value;

                    await ProcessClientQueueAsync(clientId, queue);
                }
            }
            finally
            {
                _semaphore.Release();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing queued requests");
        }
    }

    private async Task ProcessClientQueueAsync(string clientId, ConcurrentQueue<QueuedRequest> queue)
    {
        var rateLimitInfo = GetOrCreateRateLimitInfo(clientId);
        var windowDuration = TimeSpan.FromSeconds(_settings.TimeWindowSeconds);

        // Update rate limit info
        if (_settings.WindowType == TimeWindowType.Sliding)
        {
            rateLimitInfo.CleanupOldTimestamps(windowDuration);
        }
        else if (DateTime.UtcNow > rateLimitInfo.WindowEnd)
        {
            rateLimitInfo.Reset(DateTime.UtcNow, windowDuration);
        }

        // Process requests while capacity is available
        while (!rateLimitInfo.IsLimitExceeded && queue.TryPeek(out var nextRequest))
        {
            if (nextRequest.IsExpired)
            {
                // Remove expired request
                if (queue.TryDequeue(out var expiredRequest))
                {
                    expiredRequest.Cancel();
                    _logger.LogDebug("Expired queued request for client {ClientId}", clientId);
                }
                continue;
            }

            if (nextRequest.CancellationToken.IsCancellationRequested)
            {
                // Remove cancelled request
                if (queue.TryDequeue(out var cancelledRequest))
                {
                    cancelledRequest.Cancel();
                    _logger.LogDebug("Cancelled queued request for client {ClientId}", clientId);
                }
                continue;
            }

            // Process the request
            if (queue.TryDequeue(out var processedRequest))
            {
                rateLimitInfo.AddRequest();
                processedRequest.Complete();
                _logger.LogDebug("Processed queued request for client {ClientId}. Count: {Count}/{Limit}",
                    clientId, rateLimitInfo.RequestCount, rateLimitInfo.RequestLimit);
            }
        }
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;

        try
        {
            _cleanupTimer?.Dispose();
            _queueProcessorTimer?.Dispose();
            _semaphore?.Dispose();

            // Cancel all queued requests
            foreach (var queue in _requestQueues.Values)
            {
                while (queue.TryDequeue(out var request))
                {
                    request.Cancel();
                }
            }

            _logger.LogInformation("Rate limiting service disposed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disposing rate limiting service");
        }
    }
}