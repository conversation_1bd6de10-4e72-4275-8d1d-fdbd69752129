﻿using Amazon;
using Amazon.CognitoIdentityProvider;
using Amazon.CognitoIdentityProvider.Model;
using Amazon.DynamoDBv2.DataModel;
using Amazon.Extensions.CognitoAuthentication;
using Lrb.Application.Common.Caching;
using Lrb.Application.Common.Identity;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Identity.Cognito;
using Lrb.Application.Identity.Tokens;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Infrastructure.Auth.Cognito;
using Lrb.Shared.Authorization;
using Lrb.Shared.Extensions;
using Lrb.Shared.Utils;
using Mapster;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System.Security.Claims;
using System.Text;

namespace Lrb.Infrastructure.Identity
{
    internal class CognitoService<T> : ICognitoService<T> where T : Client
    {
        private readonly AmazonCognitoIdentityProviderClient _provider;
        private readonly CognitoUserPool _userPool;
        private readonly CognitoJwtSettings _cognitoConfig;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IDynamoDBContext _context;
        private readonly Serilog.ILogger _logger;

        public CognitoService(IOptions<CognitoJwtSettings> cognitoConfig, ILeadRepositoryAsync leadRepositoryAsync, IDynamoDBContext context, Serilog.ILogger logger)
        {
            _cognitoConfig = cognitoConfig.Value;
            _leadRepositoryAsync = leadRepositoryAsync;
            _context = context;
            _logger = logger;
            _provider = new AmazonCognitoIdentityProviderClient(
                             _cognitoConfig.AccessKeyId,
                             _cognitoConfig.AccessSecretKey,
                             RegionEndpoint.GetBySystemName(_cognitoConfig.Region));
            if (typeof(T).Name == typeof(MobileClient).Name)
            {
                _userPool = new CognitoUserPool(
                            _cognitoConfig.UserPoolId,
                            _cognitoConfig.MobileAppClientId,
                            _provider);
            }
            else if (typeof(T).Name == typeof(WebClient).Name)
            {
                _userPool = new CognitoUserPool(
                            _cognitoConfig.UserPoolId,
                            _cognitoConfig.WebAppClientId,
                            _provider);
            }

        }

        public async Task<CreateCognitoUserResponse> CreateUserAsync(CreateCognitoUserRequest request, CancellationToken cancellationToken)
        {
            //var passwordGenerator = new PasswordGenerator();
            // create a SignUpRequest
            var signUpRequest = new SignUpRequest
            {
                ClientId = typeof(T).Name == typeof(MobileClient).Name ? _cognitoConfig.MobileAppClientId : _cognitoConfig.WebAppClientId,
                Password = request.Password,
                Username = request.UserName
            };

            // add all the attributes 
            // you want to add to the New User
            if (!string.IsNullOrEmpty(request.Email))
            {
                signUpRequest.UserAttributes.Add(new AttributeType
                {
                    Name = "email",
                    Value = request.Email
                });
            }
            //given_name is a required field in AWS Cognito pool.
            if (!string.IsNullOrEmpty(request.FirstName))
            {
                signUpRequest.UserAttributes.Add(new AttributeType
                {
                    Name = "given_name",
                    Value = request.FirstName
                });
            }
            //family_name is a required field in AWS Cognito pool.
            if (!string.IsNullOrEmpty(request.FirstName) || !string.IsNullOrEmpty(request.LastName))
            {
                signUpRequest.UserAttributes.Add(new AttributeType
                {
                    Name = "family_name",
                    Value = string.IsNullOrEmpty(request.LastName) ? request.FirstName : request.LastName
                });
            }
            if (!string.IsNullOrEmpty(request.PhoneNumber))
            {
                signUpRequest.UserAttributes.Add(new AttributeType
                {
                    Name = "phone_number",
                    Value = request.PhoneNumber
                });
            }
            // preferred_username is a required field in AWS Cognito pool.
            if (!string.IsNullOrEmpty(request.UserName))
            {
                signUpRequest.UserAttributes.Add(new AttributeType
                {
                    Name = "preferred_username",
                    Value = request.UserName
                });
            }

            //Add Tenant Id from tenant/org repo

            signUpRequest.UserAttributes.Add(new AttributeType
            {
                Name = "custom:tenant_id",
                Value = request.TenantId
            });

            //Add User Id 

            signUpRequest.UserAttributes.Add(new AttributeType
            {
                Name = "custom:user_id",
                Value = $"{request.TenantId}_{request.UserName}"
            });

            signUpRequest.UserAttributes.Add(new AttributeType
            {
                Name = "custom:pass_stamp",
                Value = request.PasswordTimeStamp.ConvertDateTimeToBase64()
            });

            signUpRequest.UserAttributes.Add(new()
            {
                Name = "custom:mfa_enabled",
                Value = request.IsMFAEnabled.ToString()
            });

            try
            {
                // call SignUpAsync() method
                SignUpResponse response = await _provider.SignUpAsync(signUpRequest, cancellationToken);
                await _provider.AdminConfirmSignUpAsync(new AdminConfirmSignUpRequest()
                {
                    Username = request.UserName,
                    UserPoolId = _cognitoConfig.UserPoolId,
                });
                var responseObj = request.Adapt<CreateCognitoUserResponse>();
                responseObj.UUID = response.UserSub;
                return responseObj;
            }
            catch (UsernameExistsException)
            {
                throw;
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CognitoService -> CreateUserAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
        }
        public async Task<CognitoTokenResponse> TryLoginAsync(string cognitoUserId, string password, CancellationToken cancellationToken)
        {
            try
            {
                CognitoUser user = new(cognitoUserId, _userPool.ClientID ?? (typeof(T).Name == typeof(MobileClient).Name ? _cognitoConfig.MobileAppClientId : _cognitoConfig.WebAppClientId), _userPool, _provider);
                var result = await _provider.InitiateAuthAsync(new InitiateAuthRequest()
                {
                    AuthFlow = new AuthFlowType("USER_PASSWORD_AUTH"),
                    AuthParameters = new Dictionary<string, string>()
                {
                    { "USERNAME", cognitoUserId },
                    { "PASSWORD", password }
                },
                    ClientId = typeof(T).Name == typeof(MobileClient).Name ? _cognitoConfig.MobileAppClientId : _cognitoConfig.WebAppClientId
                });

                return new CognitoTokenResponse(result.AuthenticationResult.IdToken,
                    result.AuthenticationResult.AccessToken,
                    result.AuthenticationResult.RefreshToken,
                    result.Session);
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CognitoService -> TryLoginAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
        }

        //for OTP based login
        public async Task<(Guid UserId, string SessionId, string SecretLoginCode)> TryLoginAsync(string cognitoUserId, CancellationToken cancellationToken)
        {
            CognitoUser user = new(cognitoUserId, _userPool.ClientID ?? (typeof(T).Name == typeof(MobileClient).Name ? _cognitoConfig.MobileAppClientId : _cognitoConfig.WebAppClientId), _userPool, _provider);
            try
            {
                Dictionary<string, string> authParms = new()
                {
                    { "USERNAME", cognitoUserId }
                };
                Dictionary<string, string> clientMetadata = new();
                InitiateCustomAuthRequest authRequest = new()
                {
                    AuthParameters = authParms,
                    ClientMetadata = clientMetadata
                };
                AuthFlowResponse authResponse = await user.StartWithCustomAuthAsync(authRequest);
                return new(Guid.Parse(authResponse.ChallengeParameters["UserId"]), authResponse.SessionID, authResponse.ChallengeParameters["SecretLoginCode"]);
            }
            catch (Exception e)
            {
                throw;
            }
        }

        public async Task<AuthFlowResponse> VerifyAuthChallenge(string userId, string sessionId, string otp, CancellationToken cancellationToken)
        {
            try
            {
                CognitoUser user = new(userId, _userPool.ClientID ?? (typeof(T).Name == typeof(MobileClient).Name ? _cognitoConfig.MobileAppClientId : _cognitoConfig.WebAppClientId), _userPool, _provider);
                RespondToCustomChallengeRequest respondToCustomChallengeRequest = new();
                Dictionary<string, string> challengeParams = new();
                challengeParams.Add("ANSWER", otp);
                challengeParams.Add("USERNAME", userId);
                respondToCustomChallengeRequest.ChallengeParameters = challengeParams;
                respondToCustomChallengeRequest.SessionID = sessionId;
                return await user.RespondToCustomAuthAsync(respondToCustomChallengeRequest);

            }
            catch
            {
                throw;
            }
        }
        public async Task<CognitoTokenResponse> GetTokensFromRefreshToken(string refreshToken, string cognitoUserId)
        {
            CognitoUser user = new(cognitoUserId, typeof(T).Name == typeof(MobileClient).Name ? _cognitoConfig.MobileAppClientId : _cognitoConfig.WebAppClientId, _userPool, _provider);
            user.SessionTokens = new CognitoUserSession(null, null, refreshToken, DateTime.Now, DateTime.Now.AddHours(1));
            var result = await user.StartWithRefreshTokenAuthAsync(new InitiateRefreshTokenAuthRequest()
            {
                AuthFlowType = AuthFlowType.REFRESH_TOKEN_AUTH
            });

            return new CognitoTokenResponse(result.AuthenticationResult.IdToken,
                result.AuthenticationResult.AccessToken,
                result.AuthenticationResult.RefreshToken,
                result.SessionID);
        }
        private async Task<AdminGetUserResponse> GetUserAsync(string username, CancellationToken cancellationToken)
        {
            AdminGetUserRequest request = new()
            {
                Username = username,
                UserPoolId = _userPool?.PoolID ?? _cognitoConfig.UserPoolId
            };
            var response = await _provider.AdminGetUserAsync(request, cancellationToken);
            return response;
        }
        private async Task<Lrb.Domain.Entities.AdminUserRecord> GetAdminUserAsync(string username, CancellationToken cancellationToken)
        {
            AdminGetUserRequest request = new()
            {
                Username = username,
                UserPoolId = _userPool?.PoolID ?? _cognitoConfig.UserPoolId
            };
            var response = await _provider.AdminGetUserAsync(request, cancellationToken);
            var cacheResponce = response.Adapt<Lrb.Domain.Entities.AdminUserRecord>();
            cacheResponce.Id = GetCacheKey(username);
            return cacheResponce;
        }
        private string GetCacheKey(string username)
        {
            var env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
            if(env?.Equals("prd") ?? false)
            {
                return (username + LrbClaims.AdminUser).ToLower().Trim();
            }
            else
            {
                return (username + env + LrbClaims.AdminUser).ToLower().Trim();
            }
        }
        private async Task<bool> RemoveAdminGetUserInCacheAsync(string username)
        {
            try
            {
                await _context.DeleteAsync<Lrb.Domain.Entities.AdminUserRecord>(GetCacheKey(username), CancellationToken.None);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Information("Exception details while calling the RemoveAdminGetUserInCacheAsync " + ex.Message ?? ex.InnerException?.Message ?? string.Empty);
                return false;
            }
        }
        private async Task<Lrb.Domain.Entities.AdminUserRecord> SetOrGetUserInCacheServiceAsync(string username)
        {
            try
            {
                var user = await _context.GetOrSetAsync<Lrb.Domain.Entities.AdminUserRecord>(GetCacheKey(username), () => GetAdminUserAsync(username, CancellationToken.None), _logger, TimeSpan.FromHours(6), CancellationToken.None);
                return user ?? await GetAdminUserAsync(username, CancellationToken.None);
            }
            catch (Exception ex)
            {
                _logger.Information("Exception details while calling the SetOrGetUserInCacheServiceAsync " + ex.Message ?? ex.InnerException?.Message ?? string.Empty);
                return await GetAdminUserAsync(username, CancellationToken.None);
            }
        }
        public async Task<bool> UpdateUserAttributesAsync(UpdateCognitoAttributesRequest request, CancellationToken cancellationToken)
        {
            AdminGetUserResponse user = await GetUserAsync(request.UserName, cancellationToken);
            List<AttributeType>? attributes = new();
            if (!string.IsNullOrWhiteSpace(request?.FirstName) && request?.FirstName != user?.UserAttributes?.FirstOrDefault(i => i.Name == "given_name")?.Value)
            {
                attributes.Add(new AttributeType
                {
                    Name = "given_name",
                    Value = request?.FirstName
                });
            }
            if (!string.IsNullOrWhiteSpace(request?.LastName) && request?.LastName != user?.UserAttributes?.FirstOrDefault(i => i.Name == "family_name")?.Value)
            {
                attributes.Add(new AttributeType
                {
                    Name = "family_name",
                    Value = request?.LastName
                });
            }
            if (!string.IsNullOrWhiteSpace(request?.Email) && request?.Email != user?.UserAttributes?.FirstOrDefault(i => i.Name == "email")?.Value)
            {
                attributes.Add(new AttributeType
                {
                    Name = "email",
                    Value = request?.Email
                });
            }
            if (!string.IsNullOrWhiteSpace(request?.PhoneNumber) && request?.PhoneNumber != null && request?.PhoneNumber != user?.UserAttributes?.FirstOrDefault(i => i.Name == "phone_number")?.Value)
            {
                attributes.Add(new AttributeType
                {
                    Name = "phone_number",
                    Value = request?.PhoneNumber
                });
            }
            await RemoveAdminGetUserInCacheAsync(user?.Username ?? string.Empty);
            if (attributes?.Any() ?? false)
            {
                AdminUpdateUserAttributesRequest updateRequest = new()
                {
                    Username = user?.Username,
                    UserPoolId = _userPool?.PoolID ?? _cognitoConfig.UserPoolId,
                    UserAttributes = attributes
                };
                var response = await _provider.AdminUpdateUserAttributesAsync(updateRequest);
                return response.HttpStatusCode == System.Net.HttpStatusCode.OK;
            }
            return true;
        }
        public async Task<bool> DisableUserAsync(string username)
        {
            AdminDisableUserRequest request = new()
            {
                Username = username,
                UserPoolId = _userPool?.PoolID ?? _cognitoConfig.UserPoolId
            };
            await RemoveAdminGetUserInCacheAsync(username);
            AdminDisableUserResponse response = await _provider.AdminDisableUserAsync(request);
            return response.HttpStatusCode == System.Net.HttpStatusCode.OK;
        }

        public async Task<bool> DeleteUserAsync(string username)
        {
            AdminDeleteUserRequest request = new()
            {
                Username = username,
                UserPoolId = _userPool?.PoolID ?? _cognitoConfig.UserPoolId
            };
            await RemoveAdminGetUserInCacheAsync(username);
            AdminDeleteUserResponse response = await _provider.AdminDeleteUserAsync(request);
            return response.HttpStatusCode == System.Net.HttpStatusCode.OK;
        }

        public async Task<bool> EnableUserAsync(string username)
        {
            AdminEnableUserRequest request = new()
            {
                Username = username,
                UserPoolId = _userPool?.PoolID ?? _cognitoConfig.UserPoolId
            };
            await RemoveAdminGetUserInCacheAsync(username);
            AdminEnableUserResponse response = await _provider.AdminEnableUserAsync(request);
            return response.HttpStatusCode == System.Net.HttpStatusCode.OK;
        }
        public async Task<bool> ResetPasswordAsync(string username, string password, DateTime passwordTimeStamp)
        {
            AdminSetUserPasswordRequest request = new()
            {
                Username = username,
                UserPoolId = _userPool?.PoolID ?? _cognitoConfig.UserPoolId,
                Password = password,
                Permanent = true
            };
            AdminSetUserPasswordResponse response = await _provider.AdminSetUserPasswordAsync(request);

            if (response?.HttpStatusCode == System.Net.HttpStatusCode.OK)
            {
                AdminUpdateUserAttributesRequest updateRequest = new()
                {
                    Username = username,
                    UserPoolId = _userPool?.PoolID ?? _cognitoConfig.UserPoolId,
                    UserAttributes = new List<AttributeType>()
                    {
                        new() {
                            Name = "custom:pass_stamp",
                            Value = passwordTimeStamp.ConvertDateTimeToBase64()
                        }
                    }
                };
                await RemoveAdminGetUserInCacheAsync(username);
                var passStampResponse = await _provider.AdminUpdateUserAttributesAsync(updateRequest);
                return passStampResponse?.HttpStatusCode == System.Net.HttpStatusCode.OK;
            }
            return response?.HttpStatusCode == System.Net.HttpStatusCode.OK;
        }

        public async Task<(bool, bool)> ToggleMFAAsync(string userName, CancellationToken cancellationToken)
        {
            AdminGetUserResponse user = await GetUserAsync(userName, cancellationToken);
            var isEnabled = user.UserAttributes.FirstOrDefault(attr => attr.Name == "custom:mfa_enabled")?.Value;
            AdminUpdateUserAttributesRequest request = new()
            {
                Username = userName,
                UserPoolId = _userPool?.PoolID ?? _cognitoConfig.UserPoolId
            };
            if (string.IsNullOrWhiteSpace(isEnabled) || !bool.TryParse(isEnabled, out bool currentValue))
            {
                request.UserAttributes = new List<AttributeType>()
                    {
                        new()
                        {
                            Name = "custom:mfa_enabled",
                            Value = true.ToString()
                        }
                    };
                await RemoveAdminGetUserInCacheAsync(userName);
                var response = await _provider.AdminUpdateUserAttributesAsync(request);
                return (response.HttpStatusCode == System.Net.HttpStatusCode.OK, true);
            }
            else
            {
                request.UserAttributes = new List<AttributeType>()
                    {
                        new()
                        {
                            Name = "custom:mfa_enabled",
                            Value = (!currentValue).ToString()
                        }
                    };
                await RemoveAdminGetUserInCacheAsync(userName);
                var response = await _provider.AdminUpdateUserAttributesAsync(request);
                return (response.HttpStatusCode == System.Net.HttpStatusCode.OK, (!currentValue));
            }
        }
        public async Task<bool> IsUserEnabled(string username)
        {
            var response = await GetUserAsync(username, CancellationToken.None);
            return response.Enabled;
        }
        public async Task<DateTime> GetPasswordTimeStampAsync(string username)
        {
            var response = await GetUserAsync(username, CancellationToken.None);
            return response.UserAttributes.FirstOrDefault(i => i.Name == "custom:pass_stamp")?.Value?.ConvertBase64ToDateTime() ?? default;
        }

        public string GetUserPoolId()
        {
            return _userPool?.PoolID ?? _cognitoConfig.UserPoolId;
        }
        public async Task<bool> AddAttendanceSettingClaimAync(string username, Claim claim)
        {
            AdminUpdateUserAttributesRequest updateRequest = new()
            {
                Username = username,
                UserPoolId = _userPool?.PoolID ?? _cognitoConfig.UserPoolId,
                UserAttributes = new List<AttributeType>()
                    {
                        new() {
                            Name = IdTokenClaimsKey.ShiftTime,
                            Value = claim.Value,
                        }
                    }
            };
            await RemoveAdminGetUserInCacheAsync(username);
            var result = await _provider.AdminUpdateUserAttributesAsync(updateRequest);
            return result?.HttpStatusCode == System.Net.HttpStatusCode.OK;

        }
        public async Task<bool?> GetUserShiftTimeAsync(Lrb.Domain.Entities.AdminUserRecord? response, string? userName)
        {
            try
            {
                response ??= await GetUserAsync(userName ?? string.Empty);
                var claimValue = response.UserAttributes.FirstOrDefault(i => i.Name == IdTokenClaimsKey.ShiftTime)?.Value ?? default;
                if (!string.IsNullOrEmpty(claimValue))
                {
                    var data = Convert.FromBase64String(claimValue);
                    var decompressedData = ObjectExensions.DecompressBytes(data);
                    string jsonData = Encoding.UTF8.GetString(decompressedData);
                    var attendanceSettings = JsonConvert.DeserializeObject<Lrb.Domain.Entities.Attendance.AttendenceSettings>(jsonData);
                    var IsShiftTimingEnabled = false;
                    if (attendanceSettings != null)
                    {
                        IsShiftTimingEnabled = attendanceSettings.IsShiftTimeEnabled;
                    }
                    if (IsShiftTimingEnabled)
                    {
                        var shiftTimingsOfWeek = attendanceSettings?.UserShiftTimings ?? null;
                        DateTime utcNow = DateTime.UtcNow;
                        var shiftTimingsOfToday = shiftTimingsOfWeek?.FirstOrDefault();
                        if (shiftTimingsOfToday == null)
                        {
                            return false;
                        }
                        var timeZoneId = shiftTimingsOfToday != null ? shiftTimingsOfToday.TimeZoneId : "India Standard Time";
                        TimeZoneInfo timeZone = TimeZoneInfo.FindSystemTimeZoneById(timeZoneId);
                        DateTime localTimeNow = TimeZoneInfo.ConvertTimeFromUtc(utcNow, timeZone);
                        DayOfWeek dayOfWeek = localTimeNow.DayOfWeek;
                        shiftTimingsOfToday = shiftTimingsOfWeek?.FirstOrDefault(i => i.Day == dayOfWeek);
                        bool? IsNotAllowed = (shiftTimingsOfToday != null && shiftTimingsOfToday.IsEnabled) ? shiftTimingsOfToday?.Validate(timeZone) : true;
                        return IsNotAllowed;
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CognitoService ->Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                return false;
            }

        }
        public async Task<Lrb.Domain.Entities.AdminUserRecord> GetUserAsync(string username)
        {
            //return await GetUserAsync(username, CancellationToken.None);
            return await SetOrGetUserInCacheServiceAsync(username);
        }

        public async Task<bool> AddUserLockedSettingClaimAync(string username, bool isLoked)
        {
            try
            {
                var settings = new JsonSerializerSettings
                {
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                    ContractResolver = new DefaultContractResolver
                    {
                        NamingStrategy = new CamelCaseNamingStrategy() // Optional: Convert property names to camel case
                    }
                };
                var lockedInfo = new UserLockedInfo
                {
                    IsLocked = isLoked
                };

                var claim = new Claim(IdTokenClaimsKey.ShiftTime, string.Empty);
                string jsonData = JsonConvert.SerializeObject(lockedInfo, settings);
                byte[] jsonBytes = Encoding.UTF8.GetBytes(jsonData);
                byte[] compressedBytes = ObjectExensions.Compress(jsonBytes);
                var result = Convert.ToBase64String(compressedBytes);
                claim = new Claim(IdTokenClaimsKey.IsLocked, result);

                AdminUpdateUserAttributesRequest updateRequest = new()
                {
                    Username = username,
                    UserPoolId = _userPool?.PoolID ?? _cognitoConfig.UserPoolId,
                    UserAttributes = new List<AttributeType>()
                    {
                        new() {
                            Name = IdTokenClaimsKey.IsLocked,
                            Value = claim.Value,
                        }
                    }
                };
                string userJson = JsonConvert.SerializeObject(updateRequest);
                await RemoveAdminGetUserInCacheAsync(username);
                var updated = await _provider.AdminUpdateUserAttributesAsync(updateRequest);
                return updated?.HttpStatusCode == System.Net.HttpStatusCode.OK;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool?> GetUserLockedInfoAsync(Lrb.Domain.Entities.AdminUserRecord? response, string? userName)
        {
            try
            {
                response ??= await GetUserAsync(userName ?? string.Empty);
                var claimValue = response.UserAttributes.FirstOrDefault(i => i.Name == IdTokenClaimsKey.IsLocked)?.Value ?? default;
                if (!string.IsNullOrEmpty(claimValue))
                {
                    var data = Convert.FromBase64String(claimValue);
                    var decompressedData = ObjectExensions.DecompressBytes(data);
                    string jsonData = Encoding.UTF8.GetString(decompressedData);
                    var lockedInfo = JsonConvert.DeserializeObject<UserLockedInfo>(jsonData);
                    if (lockedInfo != null)
                    {
                        return lockedInfo.IsLocked;
                    }
                }
                return false;
            }
            catch
            {
                return false;
            }
        }
        public async Task<bool> GlobalSignOutUser(string refreshToken)
        {
            try
            {
                var request = new GlobalSignOutRequest
                {
                    AccessToken = refreshToken,
                };
                var result = await _provider.GlobalSignOutAsync(request);
                return result.Equals(true);
            }
            catch { return false; } 
        }
    }

    public class UserLockedInfo
    {
        public bool IsLocked { get; set; }
    }
}
