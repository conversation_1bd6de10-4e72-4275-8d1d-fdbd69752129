﻿using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.LeadGenRequests;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using System.Text;

namespace Lrb.Application.Integration.Web.Requests.IVR
{
    public class Voice2IVRIntegrationRequest : IRequest<Response<bool>>
    {
        public string ApiKey { get; set; }
        public HttpRequest HttpRequest { get; set; }
        public string TenantId { get; set; }

        public Voice2IVRIntegrationRequest(HttpRequest request, string tenant, string base64)
        {
            HttpRequest = request;
            TenantId = tenant;
            ApiKey = base64;
        }

        public class Voice2IVRIntegrationRequestHandler : IRequestHandler<Voice2IVRIntegrationRequest, Response<bool>>
        {
            private readonly IMediator _mediator;
            private readonly Serilog.ILogger _logger;
            private readonly IRepositoryWithEvents<IntegrationAccountInfo> _intAccRepo;

            public Voice2IVRIntegrationRequestHandler(
                IMediator mediator,
                Serilog.ILogger logger,
                IRepositoryWithEvents<IntegrationAccountInfo> intAccRepo)
            {
                _mediator = mediator;
                _logger = logger;
                _intAccRepo = intAccRepo;
            }

            public async Task<Response<bool>> Handle(Voice2IVRIntegrationRequest request, CancellationToken cancellationToken)
            {
                var accountId = request.ApiKey.GetAccountId();
                var integrationAccount = await _intAccRepo.GetByIdAsync(accountId, cancellationToken);

                if (integrationAccount == null)
                {
                    _logger.Error("Voice2IVRIntegrationRequest -> Integration Account not found for ApiKey: {ApiKey}", request.ApiKey);
                    return new(false, "Integration Account not found");
                }

                var httpRequest = request.HttpRequest;
                string bodyInString;
                IvrVoice2Dto? payload = null;

                if (httpRequest.HasFormContentType)
                {
                    var form = await httpRequest.ReadFormAsync();
                    bodyInString = JsonConvert.SerializeObject(form.ToDictionary(x => x.Key, x => x.Value.ToString()));
                    payload = JsonConvert.DeserializeObject<IvrVoice2Dto>(bodyInString);
                }
                else if (httpRequest.QueryString.HasValue)
                {
                    bodyInString = JsonConvert.SerializeObject(httpRequest.Query.ToDictionary(x => x.Key, x => x.Value.ToString()));
                    payload = JsonConvert.DeserializeObject<IvrVoice2Dto>(bodyInString);
                }
                else
                {
                    using var reader = new StreamReader(httpRequest.Body, Encoding.UTF8);
                    bodyInString = await reader.ReadToEndAsync();
                    payload = JsonConvert.DeserializeObject<IvrVoice2Dto>(bodyInString);
                }

                if (payload == null)
                {
                    _logger.Error("Voice2IVRIntegrationRequest -> Invalid or empty payload received. Body: {Body}", bodyInString);
                    return new(false, "Invalid or empty payload");
                }

                _logger.Information("Voice2IVRIntegrationRequest -> Payload: {Payload}", JsonConvert.SerializeObject(payload));

                CreateLeadGenRequest leadGenRequest = new(LeadSource.IVR, payload);
                await _mediator.Send(leadGenRequest);

                var lead = new ServetelInboundIntegrationRequest
                {
                    call_id = payload.call_sid,
                    caller_id_number = payload.mobile,
                    virtual_number = payload.cli,
                    start_stamp = GetUtcDateTimeFromUnix(payload.start_time),
                    end_stamp = GetUtcDateTimeFromUnix(payload.end_time),
                    answer_stamp = GetUtcDateTimeFromUnix(payload.answer_time),
                    billsec = payload.billing_duration,
                    hangup_cause = payload.hangup_cause,
                    answered_agent_number = payload.agent_number,
                    ApiKey = request.ApiKey,
                    call_status = payload.answer_status,
                };

                await _mediator.Send(lead);

                return new(true);
            }
        }

        public static string? GetUtcDateTimeFromUnix(string? unixTimestamp)
        {
            if (string.IsNullOrWhiteSpace(unixTimestamp))
                return null;

            if (long.TryParse(unixTimestamp, out long seconds))
            {
                var dateTime = DateTimeOffset.FromUnixTimeSeconds(seconds).UtcDateTime;
                return dateTime.ToString("yyyy-MM-ddTHH:mm:ssZ");
            }
            if (DateTime.TryParse(unixTimestamp, out var parsed))
            {
                return parsed.ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ");
            }
            return null;
        }
    }
}