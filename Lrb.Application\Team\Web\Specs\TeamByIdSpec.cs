﻿using Lrb.Application.Team.Web.Dtos;
using Microsoft.EntityFrameworkCore;
using Microsoft.Identity.Client;
using Newtonsoft.Json;

namespace Lrb.Application.Team.Web
{
    public class TeamByIdSpec : Specification<Domain.Entities.Team>
    {
        public TeamByIdSpec(Guid id)
        {
            Query.Where(i => !i.IsDeleted && i.Id == id)
                .Include(i => i.Statuses)
                .Include(i => i.Configurations);

        }
        public TeamByIdSpec(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted && ids.Contains(i.Id))
                .Include(i => i.Configurations);
        }
    }

    public class GetTeamByNameSpecs : Specification<Domain.Entities.Team>
    {
        public GetTeamByNameSpecs(string name)
        {
            Query.Where(i => !i.IsDeleted && i.Name.ToLower().Trim() == name.ToLower().Trim());
        }
    }

    public class GetAllTeamsSpec : Specification<Domain.Entities.Team>
    {
        public GetAllTeamsSpec()
        {
            Query.Where(i => !i.IsDeleted)
                .Include(i => i.Configurations);
        }
    }
    public class GetAllTeamWithConfigurationSpec : Specification<Domain.Entities.Team>
    {
        public GetAllTeamWithConfigurationSpec()
        {
            Query.Where(i => !i.IsDeleted && i.Configurations.Any(j => j.IsForRetention == true))
                .Include(i => i.Configurations.Where(j => !j.IsDeleted && j.IsForRetention == true))
                .Include(i => i.Statuses);
        }
        public GetAllTeamWithConfigurationSpec(Guid id)
        {
            Query.Where(i => !i.IsDeleted && i.Id == id)
                .Include(i => i.Configurations.Where(j => !j.IsDeleted && j.IsForRetention == true));
        }
    }

    public class GetTeamConfigurationByIdSpec : Specification<Domain.Entities.TeamConfiguration>
    {
        public GetTeamConfigurationByIdSpec(Guid id)
        {
            Query.Where(i => !i.IsDeleted && i.TeamId == id);
        }
    }
    public class GetDeletedTeamConfigurationByIdSpec : Specification<Domain.Entities.TeamConfiguration>
    {
        public GetDeletedTeamConfigurationByIdSpec(Guid id)
        {
            Query.Where(i => i.IsDeleted && i.TeamId == id);
        }
    }
    public class GetTeamByLeadStatusIdSpecs : Specification<Domain.Entities.Team>
    {
        public GetTeamByLeadStatusIdSpecs(Guid statusId)
        {
            Query.Where(i => !i.IsDeleted && i.Statuses.Any(i => !i.IsDeleted && i.Id == statusId))
                .Include(i => i.Statuses)
                .Include(i => i.Configurations.Where(j => !j.IsDeleted && j.IsForRetention == true));

        }
    }

    public class GetTeamByLeadStatusId : Specification<Domain.Entities.Team>
    {
        public GetTeamByLeadStatusId(Guid statusId)
        {
            Query.Where(i => !i.IsDeleted && i.Statuses.Any(i => !i.IsDeleted && i.Id == statusId))
                .Include(i => i.Statuses);
        }
    }
    public class GetLeadRotationGroupByAccountSpecs : Specification<Domain.Entities.Team>
    { 
        public GetLeadRotationGroupByAccountSpecs(Guid? accountId)
        {
            Query.Where(i => !i.IsDeleted && i.Configurations.Any(j => (j.IsForRetention == false && j.IntegrationAccountIds != null && j.IntegrationAccountIds.Contains(accountId ?? Guid.Empty))))
                 .Include(i => i.Configurations.Where(config => config.IsForRetention == false));
        }
    }
    public class GetLeadRotationGroupBySourceSpecs : Specification<Domain.Entities.Team>
    {
        public GetLeadRotationGroupBySourceSpecs(LeadSource source)
        {
            Query.Where(i => !i.IsDeleted && i.Configurations.Any(j => (j.IsForRetention == false && j.LeadSources != null && j.LeadSources.Contains(source))))
                 .Include(i => i.Configurations.Where(config => config.IsForRetention == false));
        }
    }
    public class GetTeamByIdSpec : Specification<Domain.Entities.Team>
    {
        public GetTeamByIdSpec(Guid id)
        {
            Query.Where(i => !i.IsDeleted && i.Id == id)
                .Include(i => i.Configurations);
        }
        public GetTeamByIdSpec(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted && ids.Contains(i.Id))
                            .Include(i => i.Configurations);

        }
    }
    public class GetTeamsWithConfigurationSpec : Specification<Domain.Entities.Team, ViewTeamLeadRotationInfoDto>
    {
        public GetTeamsWithConfigurationSpec()
        {
            Query.Where(i => !i.IsDeleted && i.Configurations != null && i.Configurations.Count() > 0)
                .Include(i => i.Configurations.Where(i => !i.IsDeleted && i.IsForRetention == false));
        }
        public GetTeamsWithConfigurationSpec(Guid id)
        {
            Query.Where(team => !team.IsDeleted && team.Id == id && team.Configurations != null && team.Configurations.Count() > 0)
                 .Include(team => team.Configurations
                     .Where(config => !config.IsDeleted && config.IsForRetention == false));
        }
    }
    public class GetTeamConfigurationByByAccountIdSpec : Specification<Domain.Entities.TeamConfiguration>
    {
        public GetTeamConfigurationByByAccountIdSpec(Guid id)
        {
            Query.Where(i => !i.IsDeleted && i.IntegrationAccountIds.Any(j => j == id));
        }
    }
    public class GetTeamConfigurationByAccountSpecs : Specification<Domain.Entities.TeamConfiguration>
    {
        public GetTeamConfigurationByAccountSpecs(Guid? accountId)
        {
            Query.Where(i => !i.IsDeleted && i.IsForRetention == false && i.IntegrationAccountIds != null && i.IntegrationAccountIds.Contains(accountId ?? Guid.Empty))
                 .Include(team => team.Team);
        }
    }
    public class GetTeamConfigurationBySourceSpecs : Specification<Domain.Entities.TeamConfiguration>
    {
        public GetTeamConfigurationBySourceSpecs(LeadSource source)
        {
            Query.Where(i => !i.IsDeleted && i.IsForRetention == false && i.LeadSources != null && i.LeadSources.Contains(source))
                 .Include(team => team.Team);

        }
    }
}
