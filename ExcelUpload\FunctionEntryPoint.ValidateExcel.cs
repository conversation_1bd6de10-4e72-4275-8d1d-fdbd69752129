﻿using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.Utils;
using Lrb.Domain.Constants;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using Microsoft.Graph;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace ExcelUpload
{
    public partial class FunctionEntryPoint
    {
        public async Task ValidateExcelHandler(InputPayload input)
        {

            CancellationToken cancellationToken = CancellationToken.None;
            var excelValidateTracker = await _excelRepo.GetByIdAsync(input.TrackerId);
            if (excelValidateTracker != null)
            {
                try
                {

                    excelValidateTracker.MappedColumnsData = excelValidateTracker.MappedColumnsData?.ToDictionary(i => i.Key, j => j.Value?.Trim() ?? string.Empty);
                    excelValidateTracker.Status = UploadStatus.Started;
                    excelValidateTracker.LastModifiedBy = input.CurrentUserId;
                    excelValidateTracker.CreatedBy = input.CurrentUserId;
                    excelValidateTracker.SheetName = excelValidateTracker.ExcelS3BucketKey.Split('/').Last() + "/" + excelValidateTracker.SheetName;

                    #region featching required data 
                    var existingLeads = await _leadRepo.ListAsync(new DuplicateLeadCheckSpec(), cancellationToken);
                    var users = new List<Lrb.Application.Identity.Users.UserDetailsDto>(await _userService.GetListAsync(cancellationToken));
                    //var leadStatuses = new List<MasterLeadStatus>(await _leadStatusRepo.ListAsync(cancellationToken));
                    var customStatuses = new List<CustomMasterLeadStatus>(await _customMastereadStatus.ListAsync(cancellationToken));
                    var existingContactNos = existingLeads.Select(i => i.ContactNo).ToList();
                    #endregion

                    DataTable dataTable = new();
                    Stream fileStream = await _blobStorageService.GetObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", excelValidateTracker.ExcelS3BucketKey);
                    if (excelValidateTracker.ExcelS3BucketKey.Split('.').LastOrDefault() == "csv")
                    {
                        using MemoryStream memoryStream = new();
                        fileStream.CopyTo(memoryStream);
                        dataTable = CSVHelper.CSVToDataTable(memoryStream);
                    }
                    else
                    {
                        dataTable = EPPlusExcelHelper.ConvertExcelToDataTable(fileStream);
                    }
                    dataTable.Columns.Add("Tag", typeof(string));
                    dataTable.Columns.Add("Created On", typeof(DateTime));
                    dataTable.Columns.Add("Existing Status", typeof(string));
                    dataTable.Columns.Add("Existing SubStatus", typeof(string));
                    dataTable.Columns.Add("Existing LeadSource", typeof(string));
                    dataTable.Columns.Add("Existing Subsource", typeof(string));
                    dataTable.Columns.Add("Assign To", typeof(string));
                    int totalRows = dataTable.Rows.Count;
                    for (int i = totalRows - 1; i >= 0; i--)
                    {
                        var row = dataTable.Rows[i];
                        if (row.ItemArray.All(i => string.IsNullOrEmpty(i.ToString())))
                        {
                            row.Delete();
                        }
                        else
                        {
                            var contactNo = row[excelValidateTracker.MappedColumnsData[DataColumns.ContactNo]].ToString();
                            var leadName = row[excelValidateTracker.MappedColumnsData[DataColumns.Name]].ToString();
                            bool Isvalid = Regex.IsMatch(contactNo ?? string.Empty, RegexPatterns.IndianPhoneNumberPattern);
                            if (!Isvalid)
                            {
                                row["Tag"] = "Invalid Contact No";
                            }
                            else if (string.IsNullOrEmpty(leadName))
                            {
                                row["Tag"] = "Invalid Lead Name";
                            }
                            else if (!string.IsNullOrEmpty(leadName) && existingContactNos.Any(i => !string.IsNullOrWhiteSpace(contactNo) && contactNo.Length >= 10 && i.Contains(contactNo[^10..])))
                            {

                                row["Tag"] = "Duplicate Lead";
                                var lead = existingLeads.FirstOrDefault(i => i.ContactNo.Contains(contactNo[^10..]));
                                if (lead != null)
                                {
                                    var user = users.FirstOrDefault(i => i.Id == lead.AssignTo);
                                    row["Assign To"] = user != null ? user.FirstName + " " +user.LastName : "Un Assigned";
                                    if (lead?.CustomLeadStatus?.BaseId != null || lead?.CustomLeadStatus?.BaseId != default)
                                    {
                                        row["Existing Status"] = customStatuses?.FirstOrDefault(i => i.Id == lead?.CustomLeadStatus?.BaseId)?.Status ?? string.Empty;
                                        row["Existing SubStatus"] = lead?.CustomLeadStatus?.DisplayName ?? string.Empty;
                                    }
                                    else
                                    {
                                        row["Existing Status"] = lead?.CustomLeadStatus?.DisplayName ?? string.Empty;
                                    }
                                    row["Existing LeadSource"] = lead?.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.LeadSource.ToString() ?? lead?.Enquiries?.FirstOrDefault()?.LeadSource.ToString();
                                    row["Existing Subsource"] = lead?.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.SubSource ?? lead?.Enquiries?.FirstOrDefault()?.SubSource;
                                    row["Created On"] = lead?.CreatedOn.Date;
                                }
                            }
                            else
                            {
                                row["Tag"] = "Valid";
                            }
                        }
                    }
                    if (dataTable.Rows.Count <= 0)
                    {
                        throw new Exception("Excel sheet is empty. Please fill some data in the excel sheet template.");
                    }
                    var tenantId = _currentUser.GetTenant();
                    var stream = EPPlusExcelHelper.GenarateExcelbyDataTable(dataTable);
                    var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", $"ValidatedExcel/{tenantId ?? "Default"}", $"Report-{DateTime.Now}.xlsx", stream);
                    excelValidateTracker.Status = UploadStatus.Completed;
                    excelValidateTracker.ValidatedExcelS3BucketKey = key;
                    //var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                    await _excelRepo.UpdateAsync(excelValidateTracker);
                }
                catch (Exception ex)
                {
                    excelValidateTracker.Status = UploadStatus.Failed;
                    excelValidateTracker.Message = ex.Message;
                    excelValidateTracker.LastModifiedBy = input.CurrentUserId;
                    excelValidateTracker.CreatedBy = input.CurrentUserId;
                    await _excelRepo.UpdateAsync(excelValidateTracker);
                }

            }
        }
    }
}