﻿using DocumentFormat.OpenXml.Spreadsheet;
using Lrb.Application.Common.Persistence;
using Lrb.Application.DailyStatusUpdates.Dtos;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Requests.CommonHandler;
using Lrb.Application.Reports.Web;
using Lrb.Application.UserDetails.Web;
using Lrb.Domain.Entities;
using Lrb.Shared.Extensions;
using Microsoft.Extensions.DependencyInjection;
using System.Collections.Concurrent;
using System.Collections.Generic;

namespace Lrb.Application.Lead.Web.Requests
{
    public class BulkLeadAssignmentRequest : BulkAssignmetDto, IRequest<PagedResponse<DuplicateLeadAssigmentResponseDto, CountDto>>
    {
       
    }
    public class BulkLeadAssignmentRequestHandler : BulkCommonHandler, IRequestHandler<BulkLeadAssignmentRequest, PagedResponse<DuplicateLeadAssigmentResponseDto, CountDto>>
    {
        private readonly IRepositoryWithEvents<BulkCommonTracker> _bulkCommonTracker;
        protected readonly ILeadRepositoryAsync _leadRepositoryAsync;
        public BulkLeadAssignmentRequestHandler(
            IRepositoryWithEvents<BulkCommonTracker> bulkCommonTracker,
            ILeadRepositoryAsync leadRepositoryAsync,
            IServiceProvider serviceProvider


            ) : base(serviceProvider, typeof(BulkLeadAssignmentRequestHandler).Name, "Handle")
        {
            _bulkCommonTracker = bulkCommonTracker;
            _leadRepositoryAsync = leadRepositoryAsync;
        }
        public async Task<PagedResponse<DuplicateLeadAssigmentResponseDto, CountDto>> Handle(BulkLeadAssignmentRequest request, CancellationToken cancellationToken)
        {

            try
            {
                List<Domain.Entities.Project>? projects = new List<Domain.Entities.Project>();
                Domain.Entities.GlobalSettings? globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                if (request.UpdateProject && request.Projects != null && request.Projects.Any())
                {
                    projects = await GetAllProjectsAsync(request.Projects, cancellationToken,globalSetting: globalSettings);
                }
                if (request?.LeadIds != null && request.LeadIds.Any())
                {
                    int skippedLeadCount = 0;
                    int totalLeadCount = request.LeadIds.Count();
                    var unAssignedCount = 0;
                    List<DuplicateLeadAssigmentResponseDto>? SkippedLeadsInfo = new();
                    List<Domain.Entities.Lead>? Leads = new List<Domain.Entities.Lead>();
                    List<Task> tasks = new List<Task>();
                    var leads = await _leadRepo.ListAsync(new LeadByIdSpec(request.LeadIds ?? new()));
                    var oldLeadViewDtos = leads.Adapt<List<ViewLeadDto>>();
                    List<LeadHistoryHot> newLeadHistory = new();
                    var assignedUsers = request.Users ??= await _userService.GetListOfUsersByIdsAsync(request.UserIds.Select(i => i.ToString()).ToList(), cancellationToken);

                    if (leads != null && leads.Any())
                    {
                        var result = await ReassignLeadsAsync(leads, request, request.Users, assignedUsers, cancellationToken, request.AdminDetails, request.CurrentUserId, projects, globalSetting: globalSettings, userBasicDetails: request.UserBasicDetails);
                        skippedLeadCount += result.SkippedLeadsInfo.FirstOrDefault()?.Leads?.Count ?? default;
                        unAssignedCount += result.SkippedLeadsInfo.DefaultIfEmpty()?.Select(i => i?.Leads?.Count ?? 0)?.Aggregate((a, b) => a + b) ?? 0;
                        Leads.AddRange(result.Leads);
                        SkippedLeadsInfo = result.SkippedLeadsInfo;

                    }

                    try
                    {
                        
                        await _leadRepo.UpdateRangeAsync(Leads, cancellationToken);
                    }
                    catch (Exception ex)
                    {
                    }
                    if (Leads?.Any() ?? false)
                    {
                        #region Create And Update Lead New Lead History
                        var currentUserId = request.CurrentUserId ?? _currentUserService.GetUserId();
                        var leadIds = Leads.Select(i => i.Id).ToList();
                        var newUpdateLeads = await _leadRepo.ListAsync(new LeadByIdSpec(leadIds));
                        var newLeadViewDtos = newUpdateLeads.Adapt<List<ViewLeadDto>>();
                        var users = await _userService.GetListAsync(cancellationToken);
                        newLeadViewDtos.ForEach(newLeadDto =>
                        {
                            newLeadDto.SetUsersInViewNewLeadHistoryDtoAsync(users, cancellationToken, currentUserId);
                        });
                        oldLeadViewDtos.ForEach(oldLeadDto =>
                        {
                            oldLeadDto.SetUsersInViewNewLeadHistoryDtoAsync(users, cancellationToken, currentUserId);
                        });
                        var leadStatues = await _customStatusRepo.ListAsync(cancellationToken);
                        var masterPropertyTypes = await _propertyTypeRepo.ListAsync(cancellationToken);
                        var leadsVersions = await _dapperRepository.GetLatestLeadHistoryVersionAsync(leadIds);
                        var histories = await CreateLeadSenerioBasedAssignmentHistoryAsync(false, newUpdateLeads, newLeadViewDtos, oldLeadViewDtos, request.AssignmentType, leadStatues, masterPropertyTypes, currentUserId, leadsVersions);
                        newLeadHistory.AddRange(histories);
                        #endregion
                    }
                    if (newLeadHistory.Any())
                    {
                        await _newLeadHistoryRepo.AddRangeAsync(newLeadHistory, cancellationToken);
                    }

                    var countDetails = new CountDto
                    {
                        UsersCount = request.Users?.Count ?? 0,
                        AssignedUsersCount = totalLeadCount - skippedLeadCount ,
                        UnAssignedCount = skippedLeadCount
                    };
                    return new(SkippedLeadsInfo, skippedLeadCount, countDetails);
                }
            }
            catch (Exception ex)
            {
                var tracker = await _bulkCommonTracker.GetByIdAsync(request.TrackerId, cancellationToken);
                if (tracker != null)
                {
                    tracker.Message = ex.Message;
                    await _bulkCommonTracker.UpdateAsync(tracker);
                }
                await AddLrbErrorAsync(ex, $"{typeof(BulkLeadAssignmentRequestHandler).Name} - Handle()");
                throw;
            }
            return new();
            //return new(false);
        }



    }

}
