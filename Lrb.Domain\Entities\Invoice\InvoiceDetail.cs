﻿using System.ComponentModel.DataAnnotations.Schema;

namespace Lrb.Domain.Entities.Invoice
{
    public class InvoiceDetail : AuditableEntity, IAggregateRoot
    {
        public string? TemplateName { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<string, string>? MappingVaribles { get; set; }
        public string? BaseTemplateUrl { get; set; }
        public bool? IsActive { get; set; }
        public bool? IsDefault { get; set; }
        public string? FileSize { get; set; }
    }
}