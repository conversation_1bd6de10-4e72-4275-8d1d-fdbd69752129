﻿using System.Text.Json.Serialization;

namespace Lrb.Application.Lead.Web.Export
{
    public class ExportLeadDto : ExportBaseLeadDto
    {
        public DateTime? LastModifiedOn { get; set; }
        public DateTime CreatedOn { get; set; }
        public DateTime ReceivedOn { get; set; }
        public string? Status { get; set; }
        public string? PropertyType { get; set; }
        public EnquiryType EnquiredFor { get; set; }
        public List<EnquiryType> EnquiryTypes { get; set; }
        public SaleType? SaleType { get; set; }
        public LeadSource LeadSource { get; set; }
        public long? LowerBudget { get; set; }
        public long? UpperBudget { get; set; }
        public double NoOfBHK { get; set; }
        public List<double> BHKs { get; set; }
        public BHKType BHKType { get; set; }
        public List<BHKType> BHKTypes { get; set; }
        public double Area { get; set; }
        public string? AreaUnit { get; set; }
        public string? SubSource { get; set; }
        public string? SubLocality { get; set; }
        public string? Locality { get; set; }
        public string? District { get; set; }
        public string? City { get; set; }
        public string? State { get; set; }
        public string? Country { get; set; }
        public string? PostalCode { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? PhoneNumber { get; set; }
        public OfferType OfferType { get; set; }
        public Purpose Purpose { get; set; }
        public List<int> Beds { get; set; }


    }
    public class ExportBaseLeadDto : IDto
    {
        public Guid Id { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public DateTime ReceivedOn { get; set; }
        public DateTime? ScheduledDate { get; set; }
        public DateTime? RevertDate { get; set; }
        public DateTime? PostponedDate { get; set; }
        public string? BookedUnderName { get; set; }
        public int ShareCount { get; set; }
        public string? SoldPrice { get; set; }
        public string? Rating { get; set; }
        public long? UnmatchedBudget { get; set; }
        public string? PurchasedFrom { get; set; }
        public string? ReferralName { get; set; }
        public string? ReferralContactNo { get; set; }
        public string? ReferralEmail { get; set; }

        public long? MeetingsDoneCount { get; set; }
        public long? MeetingsNotDoneCount { get; set; }
        public long? SiteVisitsDoneCount { get; set; }
        public long? SiteVisitsNotDoneCount { get; set; }
        public bool IsHighlighted { get; set; }
        public bool IsEscalated { get; set; }
        public bool IsAboutToConvert { get; set; }
        public bool IsHotLead { get; set; }
        public bool IsIntegrationLead { get; set; }
        public bool IsWarmLead { get; set; }
        public bool IsColdLead { get; set; }
        public string? Notes { get; set; }
        public Guid AssignTo { get; set; }
    }
    public class ExportLeadV2Dto : ExportBaseLeadDto
    {
        public string? Name { get; set; }
        public string? ContactNo { get; set; }
        public string? AlternateContactNo { get; set; }
        public LeadSource LeadSource { get; set; }
        public string? SubSource { get; set; }
        public string? AgencyName { get; set; }
        public string? Email { get; set; }
        public string? Status { get; set; }
        public string? Reason { get; set; }
        public EnquiryType EnquiredFor { get; set; }
        public string? PropertyType { get; set; }
        public string? PropertySubType { get; set; }
        //public BHKType BHKType { get; set; }
        public double NoOfBHK { get; set; }
        public long? LowerBudget { get; set; }
        public long? UpperBudget { get; set; }
        public double? CarpetArea { get; set; }
        public string? CarpetAreaUnit { get; set; }
        public double Area { get; set; }
        public string? AreaUnit { get; set; }
        public DateTime? PossessionDate { get; set; }
        public string? SubLocality { get; set; }
        public string? Locality { get; set; }
        public string? District { get; set; }
        public string? City { get; set; }
        public string? State { get; set; }
        public string? Country { get; set; }
        public string? PostalCode { get; set; }
        public string? AssignedUserName { get; set; }
        public string? AssignedUserEmail { get; set; }
        public string? AssignedUserPhoneNumber { get; set; }
        public string? Projects { get; set; }
        public string? Properties { get; set; }
        public string? SerialNumbers { get; set; }
        public OfferType OfferType { get; set; }
        public Purpose Purpose { get; set; }
    }
    public class ExportLeadFormattedDto
    {
        public string? Name { get; set; } 
        public string? ContactNo { get; set; }
        public string? AlternateContactNo { get; set; }
        public LeadSource LeadSource { get; set; }
        public string? SubSource { get; set; }
        public string? AgencyName { get; set; }
        public string? Email { get; set; }
        public string? Status { get; set; }
        public string? Reason { get; set; }
        public string? EnquiryTypes { get; set; }
        public string? PropertyType { get; set; }
        public string? PropertySubType { get; set; }
        public string? BHKTypes { get; set; }
        public string? BHKs { get; set; }
        public long? LowerBudget { get; set; }
        public long? UpperBudget { get; set; }
        public double? CarpetArea { get; set; }
        public string? CarpetAreaUnit { get; set; }
        public double Area { get; set; }
        public string? AreaUnit { get; set; }
        public DateTime? PossessionDate { get; set; }
        public string? SubLocality { get; set; }
        public string? Locality { get; set; }
        public string? District { get; set; }
        public string? City { get; set; }
        public string? State { get; set; }
        public string? Country { get; set; }
        public string? PostalCode { get; set; }
        public string? AssignedUserName { get; set; }
        public string? AssignedUserEmail { get; set; }
        public string? AssignedUserPhoneNumber { get; set; }
        public string? Projects { get; set; }
        public string? Properties { get; set; }
        public string? SerialNumbers { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public DateTime? ReceivedOn { get; set; }
        public DateTime? ScheduledDate { get; set; }
        public DateTime? RevertDate { get; set; }
        public DateTime? PostponedDate { get; set; }
        public string? BookedUnderName { get; set; }
        public int ShareCount { get; set; }
        public string? SoldPrice { get; set; }
        public string? Rating { get; set; }
        public long? UnmatchedBudget { get; set; }
        public string? PurchasedFrom { get; set; }
        public string? ReferralName { get; set; }
        public string? ReferralContactNo { get; set; }
        public string? ReferralEmail { get; set; }
        public long? MeetingsDoneCount { get; set; }
        public long? MeetingsNotDoneCount { get; set; }
        public long? SiteVisitsDoneCount { get; set; }
        public long? SiteVisitsNotDoneCount { get; set; }
        public DateTime? PickedDate { get; set; }
        public double? NetArea { get; set; }
        public double? PropertyArea { get; set; }
        public double? BuiltUpArea { get; set; }
        public double? SaleableArea { get; set; }
        public string? UnitName { get; set; }
        public string? ClusterName { get; set; }
        public string? Nationality { get; set; }
        public OfferType OfferType { get; set; }
        public Purpose Purpose { get; set; }
        public string? Community { get; set; }
        public string? SubCommunity  { get; set; }
        public string? CustomerLocality { get; set; }
        public string? CustomerCity { get; set; }
        public string? CustomerState { get; set; }
        public string? CustomerCountry { get; set; }
        public string? CustomerPostalCode { get; set; }
        public string? CustomerCommunity { get; set; }
        public string? CustomerSubCommunity { get; set; }
        public string? CustomerTowerName { get; set; }
        public string? TowerName { get; set; }
        public string? OriginalUser{ get; set; }
        public PossesionType PossesionType { get; set; }
        public string? Beds { get; set; }
        public string? LandLine { get; set; }
        public Gender Gender { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public MaritalStatusType MaritalStatus { get; set; }
        public string? SecondaryUserName { get; set; }
        [JsonIgnore]
        public bool? ShouldRenameSiteVisitColumn { get; set; }

        public long? ReferralTaken =>
        ShouldRenameSiteVisitColumn == true ? SiteVisitsDoneCount : null;
        public long? ReferralNotTaken => ShouldRenameSiteVisitColumn == true ? SiteVisitsNotDoneCount : null;
        public string? ChannelPartnerName { get; set; }
        public string? CampaignNames { get; set; }
        public string? ChannelPartnerMobile { get; set; }
        public string? ChannelPartnerEmail { get; set; }
        public int CallCount { get; set; }
        public string? LastCallDoneBy { get; set; }
        public string? LastCallDoneOn { get; set; }
        public int WhatsAppCount { get; set; }
        public string? LastWhatsAppDoneBy { get; set; }
        public string? LastWhatsAppDoneOn { get; set; }
        public int EmailCount { get; set; }
        public string? LastEmailDoneBy { get; set; }
        public string? LastEmailDoneOn { get; set; }
        public  string? BookedDate { get; set; }
        public string? Tags { get; set; }
        public DateTime? AnniversaryDate { get; set; }

        public List<FacebookDetailsDto>? FacebookProperties { get; set; }
        public List<NotesDetails>? NotesDetails { get; set; }
        public string? SiteVisitDone { get; set; }
    }

    public class NotesDetails
    {
        public Guid? LeadId { get; set; }
        public DateTime? ModifiedOn { get; set; }
        public string? Notes { get; set; }
        public string? LastModifiedByUser { get; set; }
    }

    public class NotesDetailsV1
    {
        public Guid? LeadId { get; set; }
        public string? ModifiedOn { get; set; }

        public string? Notes { get; set; }
        public string? LastModifiedByUser { get; set; }
    }

    public class CommunicationDetails
    {
        public Guid? LeadId { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public string? LastDoneByUser { get; set; }
        public ContactType? ContactType { get; set; }
        public int LeadCount { get; set; }
    }
    public class FacebookDetailsDto
    {
        public string? AdId { get; set; }
        public string? Name { get; set; }
        public string? AdName { get; set; }
        public string? PageId { get; set; }
        public string? Status { get; set; }
        public string? AdSetId { get; set; }
        public string? AdSetName { get; set; }
        public string? LeadgenId { get; set; }
        public string? CampaignId { get; set; }
        public string? FacebookId { get; set; }
        public string? AdAccountId { get; set; }
        public string? CreatedTime { get; set; }
        public string? CampaignName { get; set; }
        public string? AdAccountName { get; set; }
    }
    public class BookingDetails
    {
        public Guid? LeadId { get; set; }
        public DateTime? BookedDate { get; set; }
    }

}

