﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Utils;
using Microsoft.AspNetCore.Http;

namespace Lrb.Application.Lead.Mobile
{
    public class GetFileColumnsRequest : IRequest<Response<FileColumnDto>>
    {
        public IFormFile? File { get; set; }
        public GetFileColumnsRequest(IFormFile? file)
        {
            File = file;
        }
    }
    public class GetFileColumnsRequestHandler : IRequestHandler<GetFileColumnsRequest, Response<FileColumnDto>>
    {
        private readonly IBlobStorageService _blobStorageService;

        public GetFileColumnsRequestHandler(IBlobStorageService blobStorageService)
        {
            _blobStorageService = blobStorageService;
        }
        public async Task<Response<FileColumnDto>> Handle(GetFileColumnsRequest request, CancellationToken cancellationToken)
        {
            var file = request.File;
            if (file == null) { throw new ArgumentNullException(nameof(file)); }
            string fileExtension = string.Empty;
            if (!ExcelHelper.IsValidFile(file, out fileExtension))
            {
                throw new InvalidOperationException("File format is invalid");
            }
            var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", "Lead", file);
            List<string> columns = new List<string>();
            if (key.Split('.').LastOrDefault() == "csv")
            {
                columns = await CSVHelper.GetCSVColumns(file);
            }
            else
            {
                columns = await ExcelHelper.GetExcelColumnsAsync(file);
            }
            FileColumnDto excelColumnsViewModel = new()
            {
                S3BucketKey = key,
                ColumnNames = columns,
            };
            return new Response<FileColumnDto>(excelColumnsViewModel);
        }
    }
}
