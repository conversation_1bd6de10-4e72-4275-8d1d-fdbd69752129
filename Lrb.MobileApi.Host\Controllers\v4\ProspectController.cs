﻿using Lrb.Application.DataManagement.Mobile.Dtos;
using Lrb.Application.DataManagement.Mobile.Requests;
using MediatR;

namespace Lrb.MobileApi.Host.Controllers.v4
{
    [Authorize]
    [Route("api/v4/[controller]")]
    [ApiVersionNeutral]
    public class ProspectController:BaseApiController
    {
        private readonly IMediator _mediator;
        public ProspectController(IMediator mediator)
        {
            _mediator = mediator;
        }

        [HttpGet]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get all Prospects.", "")]
        public async Task<PagedResponse<ViewProspectDto, long>> GetAllProspectAsync([FromQuery] GetAllProspectRequest request)
        {
            return await _mediator.Send(request);
        }

        [HttpGet("count")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get All Prospect Count By Status Filter")]
        public async Task<Response<ProspectCountByFilterDto>> GetCountAsync([FromQuery] GetProspectCountByFilterRequest request)
        {
            return await _mediator.Send(request);
        }
    }
}
