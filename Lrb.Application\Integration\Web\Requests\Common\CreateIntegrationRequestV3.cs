﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Domain.Entities.ErrorModule;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Integration.Web.Requests
{
    public class CreateIntegrationRequestV3 : IRequest<Response<string>>
    {
        public string? AccountName { get; set; }
        public LeadSource Source { get; set; }
    }
    public class CreateIntegrationRequestV3Handler : IRequestHandler<CreateIntegrationRequestV3, Response<string>>
    {
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccountInfoRepositoryAsync;
        private readonly IBlobStorageService _blobStorageService;
        private readonly ICurrentUser _currentUser;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        public CreateIntegrationRequestV3Handler(IRepositoryWithEvents<IntegrationAccountInfo> integrationAccountInfoRepositoryAsync, IBlobStorageService blobStorageService, ICurrentUser currentUser, ILeadRepositoryAsync leadRepositoryAsync)
        {
            _integrationAccountInfoRepositoryAsync = integrationAccountInfoRepositoryAsync;
            _blobStorageService = blobStorageService;
            _currentUser = currentUser;
            _leadRepositoryAsync = leadRepositoryAsync;
        }

        public async Task<Response<string>> Handle(CreateIntegrationRequestV3 request, CancellationToken cancellationToken)
        {
            var tenant = _currentUser.GetTenant();
            //Todo fix user and tenant
            Guid userId = Guid.NewGuid();
            IntegrationAccountInfo? integrationAccount = null;
            IDictionary<string, string> data = null;
            integrationAccount = CreateIntegrationEntity(request, userId);
            integrationAccount.ApiKey = ApiKeyHelper.GenerateApiKey(integrationAccount.Id);
            data = new Dictionary<string, string>(IntegrationTemplateBuilder.CreateIntegrationTemplate2(tenant, integrationAccount));
            try
            {
                await _integrationAccountInfoRepositoryAsync.AddAsync(integrationAccount);

            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CreateIntegrationRequestV3Handler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            string key = string.Empty;
            byte[] bytes = IntegrationExcelHelper.CreateExcelData(data).ToArray();
            string fileName = $"{tenant}-{integrationAccount.Id}-{DateTime.Now.ToString("yyyy_MM_dd-HH_mm_ss")}.xlsx";
            string folder = "Integration";
            key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", folder, fileName, bytes);
            string fileUrl = await _blobStorageService.GetPreSignedURL(_blobStorageService?.BucketName ?? "prd-leadratblack", key);
            integrationAccount.FileUrl = key;
            await _integrationAccountInfoRepositoryAsync.UpdateAsync(integrationAccount);
            return new(fileUrl, default);
        }
        private IntegrationAccountInfo CreateIntegrationEntity(CreateIntegrationRequestV3 command, Guid userId)
        {
            return new IntegrationAccountInfo()
            {
                Id = Guid.NewGuid(),
                AccountName = command.AccountName,
                LeadSource = command.Source,
                LicenseId = Guid.NewGuid(),
                JsonTemplate = IntegrationTemplateBuilder.GetRequestBodyJsonFromFile(command.Source),
                CreatedBy = userId,
            };
        }
    }
}
