﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Common.Vimeo;
using Lrb.Infrastructure.Vimeo;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Lrb.Infrastructure.BlobStorage
{
    public static class Startup
    {
        public static IServiceCollection AddBlobStorage(this IServiceCollection services, IConfiguration config)
        {
            services.Configure<AWSSettings>(config.GetSection(nameof(AWSSettings)));
            services.Configure<VimeoSettings>(config.GetSection(nameof(VimeoSettings)));
            services.AddSingleton<IBlobStorageService, BlobStorageService>();
            services.Configure<S3Settings>(config.GetSection(nameof(S3Settings)));

            return services;
        }
    }
}
