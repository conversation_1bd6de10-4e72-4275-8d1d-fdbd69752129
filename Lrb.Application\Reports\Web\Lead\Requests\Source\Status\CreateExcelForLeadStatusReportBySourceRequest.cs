﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Utils;
using Newtonsoft.Json;
using Lrb.Shared.Extensions;

namespace Lrb.Application.Reports.Web
{
    public class CreateExcelForLeadStatusReportBySourceRequest : IRequest<Response<string>>
    {
        public string? SearchText { get; set; }
        public List<LeadSource>? Sources { get; set; } = new();
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public DateType? DateType { get; set; }
        public bool IsWithTeam { get; set; }
        public List<string>? Projects { get; set; }
        public List<Guid>? UserIds { get; set; }
        public List<string>? AgencyNames { get; set; }
        public List<string>? SubSources { get; set; }
        public ReportPermission? ExportPermission { get; set; }
        public List<string>? Localites { get; set; }
        public List<string>? States { get; set; }
        public List<string>? Cities { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = int.MaxValue;
        public DateTime? FromDateForSource { get; set; }
        public DateTime? ToDateForSource { get; set; }
    }
    public class CreateExcelForLeadStatusReportBySourceRequestHandler : IRequestHandler<CreateExcelForLeadStatusReportBySourceRequest, Response<string>>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        private readonly IBlobStorageService _blobStorageService;
        private readonly IRepositoryWithEvents<ExportReportsTracker> _exportReportsTrackerRepo;
        public const string TYPE = "statusreportbysource";
        public CreateExcelForLeadStatusReportBySourceRequestHandler(IDapperRepository dapperRepository,
            ICurrentUser currentUser,
            IBlobStorageService blobStorageService,
            IRepositoryWithEvents<ExportReportsTracker> exportReportsTrackerRepo)
        {
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
            _blobStorageService = blobStorageService;
            _exportReportsTrackerRepo = exportReportsTrackerRepo;
        }

        public async Task<Response<string>> Handle(CreateExcelForLeadStatusReportBySourceRequest request, CancellationToken cancellationToken)
        {
            ExportReportsTracker tracker = new();
            var tenantId = _currentUser.GetTenant();
            var userId = _currentUser.GetUserId();
            List<Guid> teamUserIds = new();
            List<Guid> permittedUserIds = new();
            var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
            if (isAdmin)
            {
                permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
            }
            else if (request.ExportPermission != null)
            {
                switch (request.ExportPermission)
                {
                    case ReportPermission.All:
                        permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        break;
                    case ReportPermission.Reportees:
                        permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                        break;
                }
            }
            if (request?.UserIds?.Any() ?? false)
            {
                if (request?.IsWithTeam ?? false)
                {
                    teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                }
                else
                {
                    teamUserIds = request?.UserIds ?? new List<Guid>();
                }
            }
            else
            {
                if (!isAdmin)
                {
                    //request.UserIds = new List<Guid>() { userId };
                    //teamUserIds = (await _dapperRepository.GetSubordinateIdsAsync(request.UserIds, tenantId ?? string.Empty)).ToList();

                    teamUserIds = permittedUserIds;
                }
            }
            if (teamUserIds.Any())
            {
                teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
            }
            else
            {
                teamUserIds = permittedUserIds;
            }
            if (!string.IsNullOrEmpty(request?.SearchText))
            {
                var result = ReportHelper.GetLeadSourceInfo(request.SearchText);
                if (result?.IsValidInfo ?? false)
                {
                    request?.Sources?.Add(result.LeadSource);
                    request.SearchText = null;
                }
            }
            List<LeadsSourceReportDto> newReportBySourceDtos = new List<LeadsSourceReportDto>();
            request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
            request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
            request.FromDateForSource = request.FromDateForSource.HasValue ? request.FromDateForSource.Value.ConvertFromDateToUtc() : null;
            request.ToDateForSource = request.ToDateForSource.HasValue ? request.ToDateForSource.Value.ConvertToDateToUtc() : null;
            var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<SourceReportDto>("LeadratBlack", "GetLeadSourceReport", new
            {
                fromdate = request.FromDate,
                todate = request.ToDate,
                datetype = request.DateType,
                sources = request?.Sources?.ConvertAll(i => (int)i),
                userids = teamUserIds,
                projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                tenantid = tenantId,
                searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                pagesize = request.PageSize,
                pagenumber = request.PageNumber,
                subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                agencynames = request?.AgencyNames?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                states = request?.States?.ConvertAll<string>(i => i.LrbNormalize()),
                fromdateforsource = request?.FromDateForSource,
                todateforsource = request?.ToDateForSource
            }))?.ToList() ?? new List<SourceReportDto>();
            res.ForEach(i => i.StatusCount = JsonConvert.DeserializeObject<List<StatusCountDto>>(i?.Report ?? string.Empty));
            var groupedResult = res.GroupBy(i => i?.Source ?? LeadSource.Direct).ToDictionary(i => i.Key, j => j.SelectMany(i => i?.StatusCount).ToList());
            foreach (var group in groupedResult)
            {
                newReportBySourceDtos.Add(new()
                {
                    Source = group.Key,
                    AllCount = group.Value?.Sum(i => i.Count) ?? 0,
                    ActiveCount = group.Value?.Where(i => i.BaseStatus != "dropped" && i.BaseStatus != "not_interested" && i.BaseStatus != "booked" && i.BaseStatus != "booking_cancel").Sum(i => i.Count) ?? 0,
                    MeetingDoneCount = group.Value?.Sum(i => i.MeetingDoneCount) ?? 0,
                    OverdueCount = group.Value?.Sum(i => i.OverdueCount) ?? 0,
                    MeetingNotDoneCount = group.Value?.Sum(i => i.MeetingNotDoneCount) ?? 0,
                    SiteVisitDoneCount = group.Value?.Sum(i => i.SiteVisitDoneCount) ?? 0,
                    SiteVisitNotDoneCount = group.Value?.Sum(i => i.SiteVisitNotDoneCount) ?? 0,
                    CallbackCount = group.Value?.Where(i => i.BaseStatus == "callback" && i.SubStatus != "callback")?.Sum(i => i.Count) ?? 0,
                    BookedCount = group.Value?.Where(i => i.BaseStatus == "booked")?.Sum(i => i.Count) ?? 0,
                    NewCount = group.Value?.FirstOrDefault(i => i.BaseStatus == "new")?.Count ?? 0,
                    DroppedCount = group.Value?.Where(i => i.BaseStatus == "dropped" && i.SubStatus != "dropped")?.Sum(i => i.Count) ?? 0,
                    MeetingScheduledCount = group.Value?.Where(i => i.BaseStatus == "meeting_scheduled" && i.SubStatus != "meeting_scheduled")?.Sum(i => i.Count) ?? 0,
                    SiteVisitScheduledCount = group.Value?.Where(i => i.BaseStatus == "site_visit_scheduled" && i.SubStatus != "site_visit_scheduled")?.Sum(i => i.Count) ?? 0,
                    NotInterestedCount = group.Value?.Where(i => i.BaseStatus == "not_interested" && i.SubStatus != "not_interested")?.Sum(i => i.Count) ?? 0,
                    PendingCount = group.Value?.FirstOrDefault(i => i.BaseStatus == "pending")?.Count ?? 0,
                    MeetingDoneUniqueCount = group.Value?.Sum(i => i.MeetingDoneUniqueCount) ?? 0,
                    MeetingNotDoneUniqueCount = group.Value?.Sum(i => i.MeetingNotDoneUniqueCount) ?? 0,
                    SiteVisitDoneUniqueCount = group.Value?.Sum(i => i.SiteVisitDoneUniqueCount) ?? 0,
                    SiteVisitNotDoneUniqueCount = group.Value?.Sum(i => i.SiteVisitNotDoneUniqueCount) ?? 0,
                    BookingCancelCount = group.Value?.Where(i => i.BaseStatus == "booking_cancel")?.Sum(i => i.Count) ?? 0,
                    ExpressionOfInterestCount = group.Value?.Where(i => i.BaseStatus == "expression_of_interest")?.Sum(i => i.Count) ?? 0
                });
            }

            var fileBytes = ExcelHelper.CreateExcelFromList(newReportBySourceDtos).ToArray();
            var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", $"Reports/{tenantId ?? "Default"}", $"Report-{DateTime.Now}.xlsx", fileBytes);
            var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
            tracker.Count = res.Count();
            tracker.Type = TYPE;
            tracker.Request = JsonConvert.SerializeObject(request);
            tracker.S3BucketKey = presignedUrl;
            await _exportReportsTrackerRepo.AddAsync(tracker, cancellationToken);
            return new(presignedUrl, null);
        }
    }
}
