﻿using Lrb.Application.Email.Web;
using Lrb.Application.Email.Web.Dtos;
using Lrb.Application.Email.Web.Specs;
using Lrb.Application.Lead.Web.Export;
using Lrb.Application.ListingManagement.Web.Specs;
using Lrb.Application.Property.Web;
using Lrb.Application.Property.Web.Requests;
using Lrb.Application.Property.Web.Specs;
using Lrb.Application.Property.Web.V2.Dtos;
using Lrb.Application.Reports.Web.Dtos.ExportTrackerDto;
using Lrb.Application.Reports.Web.Dtos.FiltersName;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.AmenitiesAttributes;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using Mapster;
using Newtonsoft.Json;

namespace ExcelUpload
{
    public partial class FunctionEntryPoint
    {
        public async Task ExportPropertiesListingHandler(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            List<Lrb.Domain.Entities.GlobalSettings> globalSettings1 = await _globalSettingsRepository.ListAsync(cancellationToken);
            var globalSettings = globalSettings1.FirstOrDefault();
            var awsBaseUrl = _awsSettings.AWSS3BucketUrl;
            if (globalSettings != null && globalSettings.IsPropertiesExportEnabled)
            {
                ExportPropertyTracker? exportTracker = await _exportPropertyRepo.GetByIdAsync(input.TrackerId, cancellationToken);
                RunAWSBatchForExportPropertiesForListingManagementRequest? requestforFileName = JsonConvert.DeserializeObject<RunAWSBatchForExportPropertiesForListingManagementRequest>(exportTracker?.Request ?? string.Empty);
                var propetyTypes = new List<MasterPropertyType>(await _propertyTypeRepo.ListAsync(cancellationToken));
                var serviceProvider = (await _masterEmailServiceProviderRepo.ListAsync(new GetLREmailServiceProviderSpec(), CancellationToken.None)).FirstOrDefault();
                var errorEmailTemplate = (await _masterEmailTemplatesRepo.ListAsync(new GetMasterEmailTemplatesByEventSpec(Event.ErrorMessage), CancellationToken.None)).FirstOrDefault();
                var exportEmailTemplate = (await _masterEmailTemplatesRepo.ListAsync(new GetMasterEmailTemplatesByEventSpec(Event.ExportLead), CancellationToken.None)).FirstOrDefault();
                EmailSenderDto emailSenderDto = new EmailSenderDto();
                bool isSent = false;

                Guid CreatredById = exportTracker.CreatedBy;
                string trackerIdString = CreatredById.ToString();
                ExportTrackerDto? tracker = exportTracker?.Adapt<ExportTrackerDto>();
                var userDetails = _userService.GetAsync(trackerIdString, cancellationToken);
                string firstName = userDetails.Result.FirstName;
                string lastName = userDetails.Result.LastName;
                string createdBy = $"{firstName} {lastName}";
                tracker.CreatedBy = createdBy;
                var exportTracker1 = new ExportTrackerDto
                {
                    CreatedBy = createdBy,
                };
                try
                {
                    if (exportTracker != null && serviceProvider != null && exportEmailTemplate != null)
                    {
                        #region Fetch all required MasterData and Other data
                        GetAllPropertyForListingManagementRequest? request = JsonConvert.DeserializeObject<GetAllPropertyForListingManagementRequest>(exportTracker?.Request ?? string.Empty);
                        if (request != null)
                        {
                            PropertyExportFilterForListingManagement? filtersDto = request.Adapt<PropertyExportFilterForListingManagement>();
                            PropertyFormettedExportFilterForListingManagementV2 formattedFiltersDto = filtersDto.Adapt<PropertyFormettedExportFilterForListingManagementV2>();
                            var users = new List<Lrb.Application.Identity.Users.UserDetailsDto>(await _userService.GetListAsync(cancellationToken));
                            CustomMasterAttribute? masterPropertyAttribute = null;
                            CustomMasterAmenity? masterPropertyAmenity = null;
                            MasterPropertyType? masterPropertyType = null;
                            var masterAreaunites = await _masterAreaUnitRepo.ListAsync();
                            List<Guid>? allUserIds = request?.UserIds;
                            List<Guid> propertySubTypeId = request?.PropertySubTypes;
                            if (allUserIds?.Any() ?? false)
                            {
                                List<string> userIdsInString = allUserIds.Select(guid => guid.ToString()).ToList();
                                var usersDetails = await _userService.GetListOfUsersByIdsAsync(userIdsInString, cancellationToken);
                                string userNames = "";
                                if (userDetails != null && usersDetails.Count > 0)
                                {
                                    foreach (var userDetail in usersDetails)
                                    {
                                        string userName = $"{userDetail.FirstName} {userDetail.LastName}";
                                        userNames += userName + ",";
                                    }
                                    userNames = userNames.TrimEnd(',');
                                }
                                formattedFiltersDto.UserNames = userNames;
                            }
                            var masterPropertyAttributes = (await _masterPropertyAttributeRepo.ListAsync(new GetAllCustomMasterAttributeSpec(), cancellationToken));
                            var customMasterAmenities = (await _masterPropertyAmenityRepo.ListAsync(new GetMasterPropertyAmenitySpec(string.Empty), cancellationToken));
                            var customListingSources = await _customListingSource.ListAsync(new GetAllListingSourceCountSpecs());
                            if (!string.IsNullOrWhiteSpace(request?.PropertySearch))
                            {
                                var propertySearch = request?.PropertySearch?.Trim().ToLower() ?? string.Empty;
                                masterPropertyAttribute = masterPropertyAttributes.FirstOrDefault(i => i.AttributeName != null && i.AttributeName.ToLower().Contains(propertySearch) || i.AttributeDisplayName != null && i.AttributeDisplayName.ToLower().Contains(propertySearch));
                                masterPropertyAmenity = customMasterAmenities.FirstOrDefault(i => i.AmenityName != null && i.AmenityName.ToLower().Contains(propertySearch) || i.AmenityDisplayName != null && i.AmenityDisplayName.ToLower().Contains(propertySearch));
                                masterPropertyType = (await _masterPropertyTypeRepo.FirstOrDefaultAsync(new GetMasterPropertyTypeSpec(request?.PropertySearch?.Trim().ToLower() ?? string.Empty), cancellationToken));
                            }
                            List<Guid>? propertyDimensionIds = new();
                            if (request != null && (request?.BHKs?.Any() ?? false))
                            {
                                formattedFiltersDto.BR = string.Join(", ", request.BHKs.Select(i => i.ToString()).ToList()) ?? string.Empty;
                            }
                            if (propertySubTypeId != null && propertySubTypeId.Count > 0)
                            {
                                var displayName = propetyTypes
                                    .Where(i => propertySubTypeId.Contains(i?.Id ?? Guid.Empty) && i?.Level == 1)
                                    .Select(status => status?.DisplayName)
                                    .ToList();

                                formattedFiltersDto.PropertySubTypes = string.Join(", ", displayName);
                            }

                            PropertyTypeBaseId propertyTypeIds = new();
                            propertyTypeIds.ResidentialBaseId = (await _masterPropertyTypeRepo.FirstOrDefaultAsync(new GetMasterPropertyTypeByTypeSpec("residential")))?.Id;
                            propertyTypeIds.AgricultureBaseId = (await _masterPropertyTypeRepo.FirstOrDefaultAsync(new GetMasterPropertyTypeByTypeSpec("agricultural")))?.Id;
                            propertyTypeIds.CommercialBaseId = (await _masterPropertyTypeRepo.FirstOrDefaultAsync(new GetMasterPropertyTypeByTypeSpec("commercial")))?.Id;

                            try
                            {

                                NumericAttributesDto numericAttributeDto = new NumericAttributesDto();
                                var tenantId = input?.TenantId;
                                var currentUserId = input?.CurrentUserId ?? Guid.Empty;
                                List<Guid>? userIds = new();
                                List<Guid>? filterIds = new();
                                List<Guid>? teamUserIds = new();
                                bool showAllProperties = false;

                                try
                                {
                                    switch (request.Permission)
                                    {
                                        case ViewAssignmentsPermission.View:
                                            if (request.UserIds?.Any() ?? false)
                                            {
                                                filterIds.AddRange(request.UserIds);
                                                if (request.IsWithTeam ?? false)
                                                {
                                                    teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty))?.ToList() ?? new();
                                                    filterIds.AddRange(teamUserIds);
                                                }
                                                userIds.AddRange(filterIds);
                                            }
                                            else
                                            {
                                                userIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                                                showAllProperties = true;
                                            }
                                            break;
                                        case ViewAssignmentsPermission.ViewAssigned:
                                            userIds.Add(currentUserId);
                                            break;
                                    }
                                }
                                catch (Exception ex)
                                {
                                    var error = new LrbError()
                                    {
                                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                        ErrorSource = ex?.Source,
                                        StackTrace = ex?.StackTrace,
                                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                        ErrorModule = "GetAllPropertyRequestHandler -> Handle()"
                                    };
                                    await _leadRepositoryAsync.AddErrorAsync(error);
                                }
                                //var properties = await _propertyRepo.ListAsync(new PropertyListingByCustomFilterSpec(request, masterPropertyAttribute?.Id ?? null, masterPropertyAmenity?.Id ?? null, masterPropertyType?.Id ?? null, propertyDimensionIds, propertyTypeIds, numericAttributeDto, userIds, showAllProperties), cancellationToken);
                                List<Guid> propertyIds = new();
                                if (request.MinLeadCount != null || request.MaxLeadCount != null || request.MinProspectCount != null || request.MaxProspectCount != null)
                                {
                                    var property = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<PropertyIdsDto>("LeadratBlack", "Lead&Prospects_PropertiesAssociatedCountFilter", new
                                    {
                                        tenantid = tenantId,
                                        minprospectcount = request.MinProspectCount,
                                        maxprospectcount = request.MaxProspectCount,
                                        minleadcount = request.MinLeadCount,
                                        maxleadcount = request.MaxLeadCount
                                    })).FirstOrDefault()?.PropertyIds ?? new List<Guid>();
                                    propertyIds = property.ToList();
                                }
                                var properties = await _propertyRepository.GetAllPropertiesListingExportAsyncV2(request, propertyDimensionIds, numericAttributeDto, userIds, propertyTypeIds, showAllProperties, tenantId, propertyIds);
                                var ids = properties.Select(p => p.Id).ToList();
                                var propAmenities = await _amenityRepo.ListAsync(new PropertyAmenitiesByIdsSpecV2(ids), cancellationToken);
                                properties.ToList().ForEach(p =>
                                {
                                    p.Amenities = propAmenities
                                        .Where(a => a.PropertyId == p.Id)
                                        .ToList();
                                });
                                List<ViewPropertyDtoV2> propertyDtos = new List<ViewPropertyDtoV2>();
                                try
                                {
                                    propertyDtos = properties.Adapt<List<ViewPropertyDtoV2>>();
                                }
                                catch (Exception e)
                                {

                                }

                                List<ViewFormattedPropertyListingDtoV2> resultPropertyDtos = new List<ViewFormattedPropertyListingDtoV2>();
                                foreach (var propertyDto in propertyDtos)
                                {
                                    if (propertyDto != null && (propertyDto.Attributes?.Any() ?? false) && (propertyDto.Amenities?.Any() ?? false))
                                    {
                                        List<PropertyAttributeDto> attributes = new();
                                        foreach (var attribute in propertyDto.Attributes)
                                        {
                                            var masterAttribute = masterPropertyAttributes.Where(i => i.Id == attribute.MasterPropertyAttributeId).FirstOrDefault();
                                            if (masterAttribute != null)
                                            {
                                                attribute.AttributeName = masterAttribute.AttributeDisplayName;
                                                attributes.Add(attribute);
                                            }
                                            else
                                            {
                                                attributes.Add(attribute);
                                            }
                                        }
                                        propertyDto.Attributes = attributes;
                                        List<string> amenities = new();
                                        foreach (var amenity in propertyDto.Amenities)
                                        {
                                            var amenityNames = customMasterAmenities.FirstOrDefault(i => i.Id == amenity)?.AmenityDisplayName;
                                            if (!string.IsNullOrEmpty(amenityNames))
                                            {
                                                amenities.Add(amenityNames);
                                            }
                                        }
                                        ViewFormattedPropertyListingDtoV2 formattedProperty = ListingMappingV2(propertyDto, awsBaseUrl, customListingSources, users, amenities);
                                        resultPropertyDtos.Add(formattedProperty);
                                    }
                                    else if (propertyDto != null && (propertyDto.Attributes?.Any() ?? false))
                                    {
                                        List<PropertyAttributeDto> attributes = new();
                                        foreach (var attribute in propertyDto.Attributes)
                                        {
                                            var masterAttribute = masterPropertyAttributes.Where(i => i.Id == attribute.MasterPropertyAttributeId).FirstOrDefault();
                                            if (masterAttribute != null)
                                            {
                                                attribute.AttributeName = masterAttribute.AttributeDisplayName;
                                                attributes.Add(attribute);
                                            }
                                            else
                                            {
                                                attributes.Add(attribute);
                                            }
                                        }
                                        propertyDto.Attributes = attributes;
                                        ViewFormattedPropertyListingDtoV2 formattedProperty = ListingMappingV2(propertyDto, awsBaseUrl, customListingSources, users);
                                        resultPropertyDtos.Add(formattedProperty);
                                    }
                                    else if (propertyDto != null)
                                    {
                                        ViewFormattedPropertyListingDtoV2 formattedProperty = ListingMappingV2(propertyDto, awsBaseUrl, customListingSources, users);
                                        resultPropertyDtos.Add(formattedProperty);
                                    }
                                }
                                if (request != null && request.Permission == ViewAssignmentsPermission.None)
                                {
                                    resultPropertyDtos = null;
                                }

                                var exportTemplate = await _exportTemplateRepo.GetByIdAsync(exportTracker?.TemplateId ?? Guid.Empty);
                                var fileBytes = Lrb.Application.Utils.ExcelGeneration<ViewFormattedPropertyListingDtoV2>.GenerateExcel<ViewFormattedPropertyListingDtoV2, PropertyFormettedExportFilterForListingManagementV2, ExportTrackerDto>(resultPropertyDtos, "Export Listings", formattedFiltersDto, tracker, requestforFileName.TimeZoneId, requestforFileName.BaseUTcOffset);
                                var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Properties/{input.TenantId ?? "Default"}", $"Export_Properties_" + input.TenantId + requestforFileName.FileName + ".xlsx", fileBytes, 0);
                                var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                                List<string> toEmails = new();
                                List<string> ccEamils = new();
                                List<string> bccEamils = new();
                                if (exportTracker?.ToRecipients?.Any() ?? false)
                                {
                                    toEmails.AddRange(exportTracker.ToRecipients);
                                }
                                if (exportTracker?.CcRecipients?.Any() ?? false)
                                {
                                    ccEamils.AddRange(exportTracker.CcRecipients);
                                }
                                if (exportTracker?.BccRecipients?.Any() ?? false)
                                {
                                    bccEamils.AddRange(exportTracker.BccRecipients);
                                }
                                var template = ExportLeadHelper.ReplaceVariables(exportEmailTemplate?.Body ?? string.Empty, new Dictionary<string, string>() { { string.Format("#PresignedUrl#"), presignedUrl } });
                                emailSenderDto.To = toEmails;
                                emailSenderDto.Cc = ccEamils;
                                emailSenderDto.Bcc = bccEamils;
                                emailSenderDto.BodyType = Microsoft.Graph.BodyType.Html;
                                emailSenderDto.EmailBody = template;
                                emailSenderDto.SenderEmailAddress = serviceProvider?.SenderEmailAddress ?? string.Empty;
                                emailSenderDto.Subject = exportEmailTemplate?.Subject ?? string.Empty;
                                await _graphEmailService.SendEmail(emailSenderDto);
                                isSent = true;
                                exportTracker.Count = properties.Count();
                                exportTracker.S3BucketKey = presignedUrl;
                                exportTracker.Template = JsonConvert.SerializeObject(exportTemplate);
                                exportTracker.LastModifiedBy = input.CurrentUserId;
                                exportTracker.FileName = $"Export_Properties_" + requestforFileName.FileName + ".xlsx";
                                exportTracker.CreatedBy = input.CurrentUserId;
                                await _exportPropertyRepo.UpdateAsync(exportTracker, cancellationToken);
                            }
                            catch (Exception ex)
                            {
                                var error = new LrbError()
                                {
                                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                    ErrorSource = ex?.Source,
                                    StackTrace = ex?.StackTrace,
                                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                    ErrorModule = "GetAllPropertyRequestHandler -> Handle()"
                                };
                                await _leadRepositoryAsync.AddErrorAsync(error);
                            }
                        }
                        #endregion
                    }
                }
                catch (Exception ex)
                {
                }
            }
        }

        #region Listing Management

        #region Dormanted
        public static ViewFormattedPropertyListingDto ListingMapping(ViewPropertyDto viewPropertyDto, string? awsBaseUrl)
        {
            if (viewPropertyDto == null)
            {
                return null;
            }
            var formattedProperty = new ViewFormattedPropertyListingDto
            {
                Title = viewPropertyDto.Title,
                SaleType = viewPropertyDto.SaleType.ToString(),
                EnquiredFor = viewPropertyDto.EnquiredFor.ToString(),
                Notes = viewPropertyDto.Notes,
                FurnishStatus = viewPropertyDto.FurnishStatus.ToString(),
                Status = viewPropertyDto.Status.ToString(),
                Rating = viewPropertyDto.Rating,
                ShareCount = viewPropertyDto.ShareCount,
                PossessionDate = viewPropertyDto.PossessionDate,
                Facing = viewPropertyDto.Facing.ToString(),
                BHK = viewPropertyDto.NoOfBHK,
                //BRType = viewPropertyDto.BHKType.ToString(),
                AboutProperty = viewPropertyDto.AboutProperty,
                Budget = viewPropertyDto?.MonetaryInfo?.ExpectedPrice,
                Location = viewPropertyDto?.Address?.Locality + " " + viewPropertyDto?.Address?.SubLocality,
                Attributes = ConvertListToString(viewPropertyDto?.Attributes),
                AreaWithUnit = $"{viewPropertyDto?.Dimension?.Area} {viewPropertyDto?.Dimension?.Unit}" ?? string.Empty,
                ThirdPartyURL = ConvertListStringToString(viewPropertyDto?.Links),
                ListingLevel = viewPropertyDto?.ListingLevel.ToString() ?? string.Empty,
                OfferingType = viewPropertyDto?.OfferingType.ToString() ?? string.Empty,
                CompletionStatus = viewPropertyDto?.CompletionStatus.ToString(),
                SubCommunity = viewPropertyDto?.Address?.SubCommunity,
                Community = viewPropertyDto?.Address?.Community,
                TowerName = viewPropertyDto?.Address?.TowerName,
                View360Url = ConvertListStringToString(viewPropertyDto?.View360Url),
                ImageUrls = ConvertListStringToString(viewPropertyDto?.ImageUrls?.SelectMany(kvp => kvp.Value.Select(img => AddBaseUrl(awsBaseUrl, img.ImageFilePath))).Where(url => !string.IsNullOrWhiteSpace(url)).ToList()),
                Videos = ConvertListStringToString(viewPropertyDto?.Videos?.Select(v => AddBaseUrl(awsBaseUrl, v.ImageFilePath)).Where(url => !string.IsNullOrWhiteSpace(url)).ToList()),
                Brochures = ConvertListStringToString(viewPropertyDto?.Brochures?.Select(b => AddBaseUrl(awsBaseUrl, b.URL)).Where(url => !string.IsNullOrWhiteSpace(url)).ToList()),
                PossesionType = viewPropertyDto?.PossesionType,

            };
            formattedProperty.DeveloperName = viewPropertyDto.PropertyOwnerDetails != null ? string.Join(", ", viewPropertyDto.PropertyOwnerDetails.Select(x => x.Name)) : null;
            formattedProperty.DeveloperPhoneNo = viewPropertyDto.PropertyOwnerDetails != null ? string.Join(", ", viewPropertyDto.PropertyOwnerDetails.Select(x => x.Phone)) : null;
            formattedProperty.DeveloperEmail = viewPropertyDto.PropertyOwnerDetails != null ? string.Join(", ", viewPropertyDto.PropertyOwnerDetails.Select(x => x.Email)) : null;
            formattedProperty.DeveloperAlternateNo = viewPropertyDto.PropertyOwnerDetails != null ? string.Join(", ", viewPropertyDto.PropertyOwnerDetails.Select(x => x.AlternateContactNo)) : null;
            formattedProperty.PropertyType = viewPropertyDto?.PropertyType?.DisplayName?.ToString() + ", " + viewPropertyDto?.PropertyType?.ChildType?.DisplayName?.ToString();
            formattedProperty.Project = viewPropertyDto?.Project;
            return formattedProperty;
        }
        #endregion

        public static ViewFormattedPropertyListingDtoV2 ListingMappingV2(ViewPropertyDtoV2 viewPropertyDto, string? awsBaseUrl, List<CustomListingSource> sources, List<Lrb.Application.Identity.Users.UserDetailsDto> users, List<string>? amenitiesName = null)
        {
            if (viewPropertyDto == null)
            {
                return null;
            }

            var refInfo = sources.Where(i => (viewPropertyDto.SourceReferenceIds?.Any() ?? false) && viewPropertyDto.SourceReferenceIds.ContainsKey(i.Id))
                .Select(s => new ReferenceInfo()
                {
                    PortalName = s.DisplayName,
                    ReferenceNo = viewPropertyDto?.SourceReferenceIds?[s.Id].ReferenceId
                }).ToList();

            var refInfoWithPortal = JsonConvert.SerializeObject(refInfo);

            var user = users.FirstOrDefault(i => i.Id == viewPropertyDto.AssignedTo?.FirstOrDefault());

            var formattedProperty = new ViewFormattedPropertyListingDtoV2
            {
                ReferenceInfo = !string.IsNullOrEmpty(refInfoWithPortal) ? refInfoWithPortal : viewPropertyDto.SerialNo,
                UaeEmirate = viewPropertyDto.UaeEmirate.ToString(),
                Portals = string.Join(", ", viewPropertyDto.ListingSources?.Select(i => i.DisplayName)?.Where(name => !string.IsNullOrEmpty(name)) ?? Enumerable.Empty<string>()),
                PermitNumber = viewPropertyDto.Compliance?.ListingAdvertisementNumber,
                PermitType = viewPropertyDto.Compliance?.Type?.ToString(),
                Title = viewPropertyDto.Title,
                EnquiredFor = viewPropertyDto.EnquiredFor.ToString(),
                Notes = viewPropertyDto.Notes,
                Price = viewPropertyDto.MonetaryInfo?.ExpectedPrice,
                Downpayment = (long?)viewPropertyDto.MonetaryInfo?.Downpayment,
                FurnishStatus = viewPropertyDto.FurnishStatus.ToString(),
                ShareCount = viewPropertyDto.ShareCount,
                PossessionDate = viewPropertyDto.PossessionDate,
                PossesionType = viewPropertyDto?.PossesionType,
                Facing = viewPropertyDto?.Facing.ToString(),
                BR = viewPropertyDto?.NoOfBHK == 0.5 ? "studio" : viewPropertyDto?.NoOfBHK.ToString(),
                AboutProperty = viewPropertyDto?.AboutProperty,
                Attributes = ConvertListToStringV2(viewPropertyDto?.Attributes),
                AreaWithUnit = $"{viewPropertyDto?.Dimension?.Area} {viewPropertyDto?.Dimension?.Unit}" ?? string.Empty,
                ThirdPartyURL = ConvertListStringToString(viewPropertyDto?.Links),
                ListingLevel = viewPropertyDto?.ListingLevel.ToString() ?? string.Empty,
                OfferingType = viewPropertyDto?.OfferingType.ToString() ?? string.Empty,
                CompletionStatus = viewPropertyDto?.CompletionStatus.ToString(),
                SubCommunity = viewPropertyDto?.ListingSourceAddresses?.FirstOrDefault()?.SubCommunity,
                Community = viewPropertyDto?.ListingSourceAddresses?.FirstOrDefault()?.Community,
                TowerName = viewPropertyDto?.ListingSourceAddresses?.FirstOrDefault()?.TowerName,
                City = viewPropertyDto?.ListingSourceAddresses?.FirstOrDefault()?.City,
                View360Url = ConvertListStringToString(viewPropertyDto?.View360Url),
                ImageUrls = ConvertListStringToString(viewPropertyDto?.ImageUrls?.SelectMany(kvp => kvp.Value.Select(img => AddBaseUrl(awsBaseUrl, img.ImageFilePath))).Where(url => !string.IsNullOrWhiteSpace(url)).ToList()),
                Videos = ConvertListStringToString(viewPropertyDto?.Videos?.Select(v => AddBaseUrl(awsBaseUrl, v.ImageFilePath)).Where(url => !string.IsNullOrWhiteSpace(url)).ToList()),
                Brochures = ConvertListStringToString(viewPropertyDto?.Brochures?.Select(b => AddBaseUrl(awsBaseUrl, b.URL)).Where(url => !string.IsNullOrWhiteSpace(url)).ToList()),
                Age = viewPropertyDto?.Age,
                AgentName = user?.FirstName + " " + user?.LastName,
                AgentPhoneNo = user?.PhoneNumber,
                AgentEmail = user?.Email,
                Amenities = (amenitiesName?.Any() ?? false) ? string.Join(", ", amenitiesName) : string.Empty
            };
            formattedProperty.DeveloperName = viewPropertyDto?.PropertyOwnerDetails != null ? string.Join(", ", viewPropertyDto.PropertyOwnerDetails.Select(x => x.Name)) : null;
            formattedProperty.DeveloperPhoneNo = viewPropertyDto?.PropertyOwnerDetails != null ? string.Join(", ", viewPropertyDto.PropertyOwnerDetails.Select(x => x.Phone)) : null;
            formattedProperty.DeveloperEmail = viewPropertyDto?.PropertyOwnerDetails != null ? string.Join(", ", viewPropertyDto.PropertyOwnerDetails.Select(x => x.Email)) : null;
            formattedProperty.DeveloperAlternateNo = viewPropertyDto?.PropertyOwnerDetails != null ? string.Join(", ", viewPropertyDto.PropertyOwnerDetails.Select(x => x.AlternateContactNo)) : null;
            formattedProperty.PropertyType = viewPropertyDto?.PropertyType?.DisplayName?.ToString() + ", " + viewPropertyDto?.PropertyType?.ChildType?.DisplayName?.ToString();
            formattedProperty.Project = viewPropertyDto?.Project;
            return formattedProperty;
        }

        private static string ConvertListToStringV2(IEnumerable<PropertyAttributeDto>? attributes)
        {
            if (attributes == null)
            {
                return string.Empty;
            }
            string result = string.Join(", ", attributes.Where(i => !string.IsNullOrEmpty(i?.Value)).Select(i => $"{i.AttributeName ?? ""}: {i.Value}"));
            return result;
        }
        #endregion
    }
}
