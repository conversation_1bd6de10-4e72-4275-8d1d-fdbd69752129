﻿namespace Lrb.Domain.Enums
{
    public enum PropertyDataColumns
    {
        Title,
        SaleType,
        EnquiredFor,
        Notes,
        FurnishStatus,
        Status,
        Rating,
        BrokerageAmount,
        PossessionDate,
        Facing,
        NoOfBHK,
        BHKType,
        BasePropertyType,
        SubPropertyType,
        AboutProperty,
        TotalPrice, 
        City,
        State,
        Location,
        OwnerName,
        OwnerPhoneNumber,
        OwnerEmail,
        Balconies,
        BathRooms,
        TotalFloors,
        BedRooms,
        FloorNumber,
        Utilities,
        Kitchen,
        DrawingOrLivingRooms,
        PropertySize,
        AreaUnit,
        IsNegotiable,
        CountryCode,
        Currency,
        BrokerageCurrency,
        Project,
        Community,
        SubCommunity,
        SecurityDeposit,
        RentOrLeaseAmountPerMonth,
        LockInPeriod,
        NoticePeriod,
        Escalation,
        TenantPOCName,
        TenantPOCDesignation,
        TenantPOCNumber,
        TenantPOCNumberCountryCode,
        CoWorkingOperator,
        CoWorkingOperatorPOCName,
        CoWorkingOperatorPOCNumber,
        CoWorkingOperatorPOCNumberCountryCode,
        DLDPermitNumber,
        DTCMPermit,
        OfferingType,
        CompletionStatus,
        PaymentFrequency,
        TowerName,
        NetArea,
        NetAreaUnit,
        ListedByUser,
        ImageUrls,
        ListingPortal,
        IsListed,
        ListingOnBehalf,
        OwnerAltContactNo,
        SecurityDepositAmount,
        SecurityDepositUnit,
        PossesionType,
        FinishingType,
        UaeEmirate,
        Age,
        ComplianceType,
        DownPayment,
        Parking,
        Country,
        CarpetArea,
        CarpetAreaUnit,
        BuildUpArea,
        BuildUpAreaUnit,
        ThirdPartyUrls,
        View360Url

    }
}
