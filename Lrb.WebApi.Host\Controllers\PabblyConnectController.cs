﻿//using Microsoft.AspNetCore.Mvc;

//namespace Lrb.WebApi.Host.Controllers
//{
//    public class PabblyConnectController : Controller
//    {
//        public IActionResult Index()
//        {
//            return View();
//        }
//    }
//}

using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.Integration.Web.Specs;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.LeadGenRequests;
using Lrb.Domain.Entities;
using Lrb.Domain.Enums;
using Lrb.Infrastructure.OpenApi;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
//using Swashbuckle.AspNetCore.Annotations;
using System.Text;

namespace Lrb.WebApi.Host.Controllers
{
    /// <summary>
    /// Controller for Pabbly Connect integration endpoints
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class PabblyConnectController : VersionedApiController
    {
        private readonly IReadRepository<IntegrationAccountInfo> _integrationAccRepo;
        private readonly IRepository<Lead> _leadRepo;
        private readonly ISerializerService _serializer;
        private readonly Serilog.ILogger _logger;

        public PabblyConnectController(
            IReadRepository<IntegrationAccountInfo> integrationAccRepo,
            IRepository<Lead> leadRepo,
            ISerializerService serializer,
            Serilog.ILogger logger)
        {
            _integrationAccRepo = integrationAccRepo;
            _leadRepo = leadRepo;
            _serializer = serializer;
            _logger = logger;
        }

        /// <summary>
        /// Endpoint for Pabbly Connect to create or update leads in LeadRat CRM
        /// Trigger: New Row Added in Google Sheet via Pabbly → Action: Create or Update Lead in LeadRat CRM
        /// </summary>
        /// <param name="leadData">Lead data from Pabbly Connect</param>
        /// <returns>Response with lead creation/update status</returns>
        [ApiKey]
        [AllowAnonymous]
        [HttpPost("leads")]
        //[SwaggerOperation("Create or update lead from Pabbly Connect", "This endpoint receives lead data from Pabbly Connect and creates or updates leads in LeadRat CRM")]
        public async Task<ActionResult<PabblyResponseDto>> CreateOrUpdateLead([FromBody] PabblyLeadDto leadData)
        {
            try
            {
                _logger.Information("PabblyConnectController -> CreateOrUpdateLead -> called, Data: {LeadData}", JsonConvert.SerializeObject(leadData));

                // Get API key from header
                var apiKey = HttpContext.Request.Headers["API-Key"].FirstOrDefault();
                if (string.IsNullOrEmpty(apiKey))
                {
                    return BadRequest(new PabblyResponseDto
                    {
                        Success = false,
                        Error = "API-Key header is required"
                    });
                }

                // Validate required fields
                if (string.IsNullOrWhiteSpace(leadData.Name) || string.IsNullOrWhiteSpace(leadData.ContactNo))
                {
                    return BadRequest(new PabblyResponseDto
                    {
                        Success = false,
                        Error = "Name and ContactNo are required fields"
                    });
                }

                // Set API key and lead source
                leadData.ApiKey = apiKey;
                leadData.LeadSource = LeadSource.Pabbly;

                // Create lead generation request for tracking
                CreateLeadGenRequest leadGenRequest = new(LeadSource.Pabbly, leadData);
                await Mediator.Send(leadGenRequest);

                // Process the lead creation/update
                var result = await ProcessPabblyLead(leadData);

                _logger.Information("PabblyConnectController -> CreateOrUpdateLead -> completed, Result: {Result}", JsonConvert.SerializeObject(result));

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "PabblyConnectController -> CreateOrUpdateLead -> Error: {Message}", ex.Message);
                return StatusCode(500, new PabblyResponseDto
                {
                    Success = false,
                    Error = $"Internal server error: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// Webhook endpoint to get lead data for Pabbly Connect
        /// Trigger: New Lead Created in LeadRat CRM → Action: Add Lead to Google Sheet via Pabbly
        /// </summary>
        /// <param name="leadId">ID of the lead to retrieve</param>
        /// <returns>Lead data formatted for Pabbly Connect</returns>
        [ApiKey]
        [AllowAnonymous]
        [HttpGet("leads/{leadId:guid}")]
        //[SwaggerOperation("Get lead data for Pabbly Connect webhook", "This endpoint provides lead data when a new lead is created in LeadRat CRM")]
        public async Task<ActionResult<PabblyWebhookDto>> GetLeadForPabbly(Guid leadId)
        {
            try
            {
                _logger.Information("PabblyConnectController -> GetLeadForPabbly -> called, LeadId: {LeadId}", leadId);

                // Get API key from header
                var apiKey = HttpContext.Request.Headers["API-Key"].FirstOrDefault();
                if (string.IsNullOrEmpty(apiKey))
                {
                    return BadRequest("API-Key header is required");
                }

                // Validate API key and get integration account
                var integrationAccount = await _integrationAccRepo.FirstOrDefaultAsync(
                    new GetIntegrationAccountByApiKeySpec(apiKey));

                if (integrationAccount == null)
                {
                    return Unauthorized("Invalid API key");
                }

                // Get lead by ID
                var lead = await _leadRepo.GetByIdAsync(leadId);
                if (lead == null)
                {
                    return NotFound($"Lead with ID {leadId} not found");
                }

                // Convert lead to Pabbly webhook format
                var webhookData = ConvertLeadToPabblyWebhook(lead);

                _logger.Information("PabblyConnectController -> GetLeadForPabbly -> completed, LeadId: {LeadId}", leadId);

                return Ok(webhookData);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "PabblyConnectController -> GetLeadForPabbly -> Error: {Message}", ex.Message);
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        #region Private Helper Methods

        /// <summary>
        /// Process lead creation or update from Pabbly Connect
        /// </summary>
        private async Task<PabblyResponseDto> ProcessPabblyLead(PabblyLeadDto leadData)
        {
            try
            {
                // Validate API key and get integration account
                var integrationAccount = await _integrationAccRepo.FirstOrDefaultAsync(
                    new GetIntegrationAccountByApiKeySpec(leadData.ApiKey ?? string.Empty));

                if (integrationAccount == null)
                {
                    return new PabblyResponseDto
                    {
                        Success = false,
                        Error = "Invalid API key or integration account not found"
                    };
                }

                // Check for existing lead by contact number
                var existingLeads = await _leadRepo.ListAsync(
                    new LeadByContactNoSpec(new List<string> { leadData.ContactNo }));

                var existingLead = existingLeads?.FirstOrDefault();

                Lead lead;
                bool isUpdate = false;

                if (existingLead != null)
                {
                    // Update existing lead
                    lead = await UpdateExistingLead(existingLead, leadData);
                    isUpdate = true;
                }
                else
                {
                    // Create new lead
                    lead = await CreateNewLead(leadData, integrationAccount);
                    await _leadRepo.AddAsync(lead);
                }

                await _leadRepo.SaveChangesAsync();

                return new PabblyResponseDto
                {
                    Success = true,
                    Message = isUpdate ? "Lead updated successfully" : "Lead created successfully",
                    LeadId = lead.Id,
                    Data = new
                    {
                        LeadNumber = lead.LeadNumber,
                        ContactNo = lead.ContactNo,
                        Name = lead.Name,
                        Id = lead.Id
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "ProcessPabblyLead -> Error processing lead: {Message}", ex.Message);
                return new PabblyResponseDto
                {
                    Success = false,
                    Error = $"Error processing lead: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Create a new lead from Pabbly data
        /// </summary>
        private async Task<Lead> CreateNewLead(PabblyLeadDto leadData, IntegrationAccountInfo integrationAccount)
        {
            // Build notes from custom fields and unmapped data
            var notes = BuildNotesFromPabblyData(leadData);

            // Generate lead number
            var leadNumber = GenerateLeadNumber(leadData.Name);

            var lead = new Lead
            {
                Id = Guid.NewGuid(),
                Name = leadData.Name,
                ContactNo = leadData.ContactNo,
                AlternateContactNo = leadData.AlternateContactNo,
                LandLine = leadData.LandLine,
                Email = leadData.Email,
                Notes = notes,
                ScheduledDate = leadData.ScheduledDate,
                RevertDate = leadData.RevertDate,
                ChosenProject = leadData.ChosenProject,
                ChosenProperty = leadData.ChosenProperty,
                BookedUnderName = leadData.BookedUnderName,
                LeadNumber = leadNumber,
                SoldPrice = leadData.SoldPrice,
                Rating = leadData.Rating,
                PreferredLocation = leadData.PreferredLocation,
                AgencyName = leadData.AgencyName,
                CompanyName = leadData.CompanyName,
                ChannelPartnerName = leadData.ChannelPartnerName,
                ChannelPartnerExecutiveName = leadData.ChannelPartnerExecutiveName,
                ChannelPartnerContactNo = leadData.ChannelPartnerContactNo,
                Designation = leadData.Designation,
                CreatedOnPortal = leadData.CreatedOnPortal ?? DateTime.UtcNow,
                ApiKey = leadData.ApiKey,
                AccountId = integrationAccount.Id,
                AssignTo = Guid.Empty // Will be set by lead rotation or default assignment
            };

            // Set address if provided
            if (!string.IsNullOrWhiteSpace(leadData.Address) ||
                !string.IsNullOrWhiteSpace(leadData.City) ||
                !string.IsNullOrWhiteSpace(leadData.State))
            {
                lead.Address = new Address
                {
                    //AddressLine1 = leadData.Address,
                    City = leadData.City,
                    State = leadData.State,
                    Country = leadData.Country
                };
            }

            return lead;
        }

        /// <summary>
        /// Update existing lead with Pabbly data
        /// </summary>
        private async Task<Lead> UpdateExistingLead(Lead existingLead, PabblyLeadDto leadData)
        {
            // Update fields that are provided
            if (!string.IsNullOrWhiteSpace(leadData.Name))
                existingLead.Name = leadData.Name;

            if (!string.IsNullOrWhiteSpace(leadData.AlternateContactNo))
                existingLead.AlternateContactNo = leadData.AlternateContactNo;

            if (!string.IsNullOrWhiteSpace(leadData.LandLine))
                existingLead.LandLine = leadData.LandLine;

            if (!string.IsNullOrWhiteSpace(leadData.Email))
                existingLead.Email = leadData.Email;

            // Append new notes to existing notes
            var newNotes = BuildNotesFromPabblyData(leadData);
            if (!string.IsNullOrWhiteSpace(newNotes))
            {
                existingLead.Notes = string.IsNullOrWhiteSpace(existingLead.Notes)
                    ? newNotes
                    : $"{existingLead.Notes}\n\n--- Updated from Pabbly Connect ---\n{newNotes}";
            }

            if (leadData.ScheduledDate.HasValue)
                existingLead.ScheduledDate = leadData.ScheduledDate;

            if (leadData.RevertDate.HasValue)
                existingLead.RevertDate = leadData.RevertDate;

            if (!string.IsNullOrWhiteSpace(leadData.ChosenProject))
                existingLead.ChosenProject = leadData.ChosenProject;

            if (!string.IsNullOrWhiteSpace(leadData.ChosenProperty))
                existingLead.ChosenProperty = leadData.ChosenProperty;

            if (!string.IsNullOrWhiteSpace(leadData.PreferredLocation))
                existingLead.PreferredLocation = leadData.PreferredLocation;

            if (!string.IsNullOrWhiteSpace(leadData.AgencyName))
                existingLead.AgencyName = leadData.AgencyName;

            if (!string.IsNullOrWhiteSpace(leadData.CompanyName))
                existingLead.CompanyName = leadData.CompanyName;

            return existingLead;
        }

        /// <summary>
        /// Build notes from Pabbly data including custom fields and unmapped data
        /// </summary>
        private string BuildNotesFromPabblyData(PabblyLeadDto leadData)
        {
            var notes = new List<string>();

            // Add original notes if provided
            if (!string.IsNullOrWhiteSpace(leadData.Notes))
            {
                notes.Add(leadData.Notes);
            }

            // Add custom fields to notes
            if (leadData.CustomFields?.Any() == true)
            {
                notes.Add("--- Additional Information ---");
                foreach (var field in leadData.CustomFields)
                {
                    notes.Add($"{field.Key}: {field.Value}");
                }
            }

            // Add unmapped fields to notes
            var unmappedFields = new List<string>();
            if (!string.IsNullOrWhiteSpace(leadData.ExternalReferenceId))
                unmappedFields.Add($"External Reference: {leadData.ExternalReferenceId}");
            if (!string.IsNullOrWhiteSpace(leadData.PostalCode))
                unmappedFields.Add($"Postal Code: {leadData.PostalCode}");
            if (leadData.Budget.HasValue)
                unmappedFields.Add($"Budget: {leadData.Budget}");
            if (leadData.NoOfBHK.HasValue)
                unmappedFields.Add($"BHK: {leadData.NoOfBHK}");
            if (!string.IsNullOrWhiteSpace(leadData.PropertyType))
                unmappedFields.Add($"Property Type: {leadData.PropertyType}");
            if (!string.IsNullOrWhiteSpace(leadData.Source))
                unmappedFields.Add($"Original Source: {leadData.Source}");
            if (!string.IsNullOrWhiteSpace(leadData.Status))
                unmappedFields.Add($"Original Status: {leadData.Status}");

            if (unmappedFields.Any())
            {
                notes.Add("--- Source System Data ---");
                notes.AddRange(unmappedFields);
            }

            return string.Join("\n", notes);
        }

        /// <summary>
        /// Generate lead number
        /// </summary>
        private string GenerateLeadNumber(string name)
        {
            var firstChar = !string.IsNullOrWhiteSpace(name) ? name[0].ToString().ToUpper() : "L";
            var randomNumber = new Random().Next(1000, 9999);
            var timestamp = DateTime.UtcNow.ToString("FFFFFFF");
            return $"{firstChar}{randomNumber}{timestamp}";
        }

        /// <summary>
        /// Convert Lead entity to Pabbly webhook format
        /// </summary>
        private PabblyWebhookDto ConvertLeadToPabblyWebhook(Lead lead)
        {
            return new PabblyWebhookDto
            {
                Id = lead.Id,
                Name = lead.Name,
                ContactNo = lead.ContactNo,
                AlternateContactNo = lead.AlternateContactNo,
                LandLine = lead.LandLine,
                Email = lead.Email,
                Notes = lead.Notes,
                ScheduledDate = lead.ScheduledDate,
                RevertDate = lead.RevertDate,
                ChosenProject = lead.ChosenProject,
                ChosenProperty = lead.ChosenProperty,
                LeadNumber = lead.LeadNumber,
                Status = lead.CustomLeadStatus?.DisplayName,
                Source = LeadSource.Pabbly.ToString(),
                CreatedOn = lead.CreatedOn,
                LastModifiedOn = lead.LastModifiedOn,
                Rating = lead.Rating,
                AgencyName = lead.AgencyName,
                CompanyName = lead.CompanyName,
                //Address = lead.Address?.AddressLine1,
                EventType = "created"
            };
        }

        #endregion
    }
}
