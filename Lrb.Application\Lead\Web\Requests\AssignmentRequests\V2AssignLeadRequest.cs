﻿using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Lead.Web.Mappings;
using Lrb.Application.Lead.Web.Requests;
using Lrb.Domain.Entities.ErrorModule;
using Newtonsoft.Json;

namespace Lrb.Application.Lead.Web
{
    public class V2AssignLeadRequest : IRequest<Response<bool>>
    {
        public List<Guid> LeadIds { get; set; }
        public Guid? UserId { get; set; }
        public Guid? SecondaryUserId { get; set; }
    }
    public class V2AssignLeadRequestHandler : LeadCommonRequestHandler, IRequestHandler<V2AssignLeadRequest, Response<bool>>
    {
        public V2AssignLeadRequestHandler(IServiceProvider serviceProvider) : base(serviceProvider, typeof(V2AssignLeadRequest).Name, "Handle")
        {
        }
        public async Task<Response<bool>> Handle(V2AssignLeadRequest request, CancellationToken cancellationToken)
        {
            Domain.Entities.GlobalSettings? globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
            if ((request?.SecondaryUserId != null && request?.SecondaryUserId != Guid.Empty)
                && (request?.UserId == null || request?.UserId == Guid.Empty))
            {
                throw new InvalidOperationException("UserId is required!");
            }
            //var currentUserId = _currentUser.GetUserId();
            //var userIds = new List<string>() { currentUserId.ToString(), (request?.UserId ?? Guid.Empty).ToString(), (request?.SecondaryUserId ?? Guid.Empty).ToString() };
            //var users = await _userService.GetListOfUsersByIdsAsync(userIds, cancellationToken);
            //var user = users?.FirstOrDefault(i => i.Id == (request?.UserId ?? Guid.Empty));
            //var secondaryUser = users?.FirstOrDefault(i => i.Id == (request?.SecondaryUserId ?? Guid.Empty));
            var leads = await _leadRepo.ListAsync(new LeadsByIdsSpec(request?.LeadIds ?? new()));
            if (leads?.Any() ?? false)
            {
                foreach (var lead in leads)
                {
                    lead.SecondaryFromUserId = (lead.SecondaryUserId != request?.SecondaryUserId && request?.SecondaryUserId != Guid.Empty) ? lead.SecondaryUserId : lead.SecondaryFromUserId;
                    lead.SecondaryUserId = request?.SecondaryUserId ?? Guid.Empty;
                    var previousAssignedFromUser = ((request?.UserId == null || request?.UserId == Guid.Empty) && lead.AssignTo != Guid.Empty) ? lead.AssignTo : lead.AssignedFrom;
                    if (request?.UserId != null && request?.UserId != Guid.Empty && (request?.UserId != lead.AssignTo))
                    {
                        AssignLeadsBasedOnScenariosRequest assignmentRequest = new()
                        {
                            LeadIds = new() { lead.Id },
                            UserIds = new() { request?.UserId ?? Guid.Empty },
                            AssignmentType = LeadAssignmentType.WithHistory,
                            LeadSource = lead.Enquiries.FirstOrDefault(i => i.IsPrimary)?.LeadSource ?? default,
                        };
                        var assignmentResponse = await _mediator.Send(assignmentRequest);
                    }
                    else
                    {
                        //lead.AssignedFrom = lead.AssignTo;
                        lead.AssignedFrom = (lead.AssignTo == request?.UserId) ? lead.AssignedFrom : (request?.UserId == Guid.Empty) ? lead.AssignTo : (request?.UserId == null) ? lead.AssignTo : lead.AssignedFrom;
                        lead.AssignTo = request?.UserId ?? Guid.Empty;
                        if (lead.AssignTo != lead.AssignedFrom)
                        {
                            lead.PickedDate = null;
                            lead.IsPicked = false;
                            lead.ShouldUpdatePickedDate = false;
                        }
                        await _leadRepo.UpdateAsync(lead);
                        await CreateLeadAssignmentHistory(lead, LeadAssignmentType.WithHistory);
                    }

                    // Set OriginalOwner to the assigned user when first assigned
                    if ((lead.OriginalOwner == null || lead.OriginalOwner == Guid.Empty) && lead.AssignTo != Guid.Empty)
                    {
                        lead.OriginalOwner = lead.AssignTo;
                    }

                    var fullLead = (await _leadRepo.ListAsync(new LeadByIdSpec(lead.Id), cancellationToken))?.FirstOrDefault();
                    var leadDto = fullLead?.Adapt<ViewLeadDto>();
                    await leadDto?.SetUsersInViewLeadDtoAsync(_userService, cancellationToken);
                    await UpdateLeadHistoryAsync(lead, leadDto, cancellationToken: cancellationToken,previousAssignedFrom: previousAssignedFromUser);
                    try
                    {
                        await lead.SendLeadStatusChangeNotificationsAsync(leadDto, _notificationSenderService, globalSettings, _currentUser.GetUserId());
                        if (lead.AssignTo == Guid.Empty && lead.SecondaryUserId == Guid.Empty)
                        {
                           await _notificationSenderService.DeleteScheduledNotificationsAsync(lead);
                        }
                    }
                    catch (Exception ex)
                    {
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "AssigLeadRequestHandler -> Handle() ->SendLeadStatusChangeNotificationsAsync()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                }
            }
            return new(true);
        }
    }
}
