﻿using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.DataModel;
using Amazon.Runtime;
using Lrb.Application.Common.DynamoDb;
using Lrb.Infrastructure.BlobStorage;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Lrb.Infrastructure.DynamoDb
{
    public static class Startup
    {
        public static IServiceCollection AddDynamoDb(this IServiceCollection services, IConfiguration config)
        {
            var awsCreds = config.GetSection(nameof(DynamoDbAWSSettings));
            services.AddDefaultAWSOptions(new Amazon.Extensions.NETCore.Setup.AWSOptions
            {
                Credentials = new BasicAWSCredentials(awsCreds.GetValue<string>("AWSAccessToken"), awsCreds.GetValue<string>("AWSSecret")),
                Region = Amazon.RegionEndpoint.APSouth1
            });
            services.AddAWSService<IAmazonDynamoDB>();
            services.AddScoped<IDynamoDBContext, DynamoDBContext>();
            services.AddScoped(typeof(IDynamoDbService<>), typeof(DynamoDbService<>));
            return services;
        }
    }
}
