﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Application.ListingManagement.Web.Dtos;
using Lrb.Application.Property.Web;
using Lrb.Application.PropertyRefrenceInfomation.Web.Dtos;
using Lrb.Application.Utils;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using Serilog;

namespace Lrb.Application.PropertyRefrenceInfomation.Web.Requests
{
    public class GetRefrenceIdBulkUploadRequest : IRequest<Response<FileColumnRefrenceIdDto>>
    {
        public IFormFile? File { get; set; }
        public GetRefrenceIdBulkUploadRequest(IFormFile file)
        {
            File = file;
        }
    }

    public class GetRefrenceIdBulkUploadRequestHandler : IRequestHandler<GetRefrenceIdBulkUploadRequest, Response<FileColumnRefrenceIdDto>>
    {
        private readonly IBlobStorageService _blobStorageService;
        private readonly ILogger _logger;
        public GetRefrenceIdBulkUploadRequestHandler(IBlobStorageService blobStorageService, ILogger logger)
        {
            _blobStorageService = blobStorageService;
            _logger = logger;
        }
        public async Task<Response<FileColumnRefrenceIdDto>> Handle(GetRefrenceIdBulkUploadRequest request, CancellationToken cancellationToken)
        {
            _logger.Information("GetListingSourceAddressUploadRequestHandler -> Handle: " + JsonConvert.SerializeObject(request));
            var file = request.File;
            if (file == null)
            {
                throw new ArgumentNullException(nameof(file));
            }
            var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", "Property", file);
            List<string> columns = new List<string>();
            Dictionary<string, List<string>> multiSheetColumns = new();
            if (key.Split('.').LastOrDefault() == "csv")
            {
                columns = await CSVHelper.GetCSVColumns(file);
                multiSheetColumns.Add("Default", columns);
            }
            else
            {
                columns = EPPlusExcelHelper.GetFileColumns(file);
                multiSheetColumns = EPPlusExcelHelper.GetFileColumnsOfMultiSheets(file);
            }
            FileColumnRefrenceIdDto excelColumnsViewModel = new()
            {
                S3BucketKey = key,
                ColumnNames = columns,
                MultiSheetColumnNames = multiSheetColumns,
            };
            return new(excelColumnsViewModel);
        }
    }
}
