using Lrb.Application.Common.Caching;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.LeadCallLog.Web;
using Lrb.Application.Utils;
using Lrb.Application.WhatsAppCloudApi.Web;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Lrb.Application.Lead.Web.Requests.GetRequests
{
    public class GetLeadHistoriesByIdRequestV2 : IRequest<Response<List<LeadHistoryDto>>>
    {
        public Guid LeadId { get; set; }
        public GetLeadHistoriesByIdRequestV2(Guid id) => LeadId = id;
        public bool? CanAccessAllLeads { get; set; }
    }

    public class GetLeadHistoriesByIdRequestV2Handler : IRequestHandler<GetLeadHistoriesByIdRequestV2, Response<List<LeadHistoryDto>>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.LeadHistoryHot> _leadHistoryRepo;
        private readonly IReadRepository<LeadAppointment> _leadAppointmentRepo;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        private readonly ICacheService _cache;
        private readonly ICacheKeyService _cacheKeys;
        private readonly ILogger<GetLeadHistoriesByIdRequestV2Handler> _logger;
        private readonly IReadRepository<LeadCommunication> _leadCommunicationRepo;
        private readonly IReadRepository<Domain.Entities.WhatsAppCommunication> _whatsAppCommunicationRepo;
        private readonly IRepositoryWithEvents<ServetelCallLog> _servetelCallLogs;
        private readonly IReadRepository<Domain.Entities.IVRCommonCallLog> _ivrCommonCallLogRepo;
        private readonly IUserService _userService;
        private readonly IReadRepository<LeadBookedDetail> _bookedInfoRepo;

        public GetLeadHistoriesByIdRequestV2Handler(
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepo,
            IRepositoryWithEvents<Domain.Entities.LeadHistoryHot> leadHistoryRepo,
            IDapperRepository dapperRepository,
            ICurrentUser currentUser,
            ICacheService cache,
            ICacheKeyService cacheKeys,
            ILogger<GetLeadHistoriesByIdRequestV2Handler> logger,
            IReadRepository<LeadAppointment> leadAppointmentRepo,
            IReadRepository<LeadCommunication> leadCommunicationRepo,
            IReadRepository<WhatsAppCommunication> whatsAppCommunicationRepo,
            IRepositoryWithEvents<ServetelCallLog> servetelCallLogs,
            IUserService userService,
            IReadRepository<LeadBookedDetail> bookedInfoRepo)
        {
            _leadRepo = leadRepo;
            _leadHistoryRepo = leadHistoryRepo;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
            _cache = cache;
            _cacheKeys = cacheKeys;
            _logger = logger;
            _leadAppointmentRepo = leadAppointmentRepo;
            _leadCommunicationRepo = leadCommunicationRepo;
            _whatsAppCommunicationRepo = whatsAppCommunicationRepo;
            _servetelCallLogs = servetelCallLogs;
            _userService = userService;
            _bookedInfoRepo = bookedInfoRepo;
        }

        public async Task<Response<List<LeadHistoryDto>>> Handle(GetLeadHistoriesByIdRequestV2 request, CancellationToken cancellationToken)
        {
            try
            {
                var currentUserId = _currentUser.GetUserId();
                var tenantId = _currentUser.GetTenant() ?? string.Empty;

                var cacheKey = _cacheKeys.GetCacheKey("LeadHistoryV2", $"{request.LeadId}_{currentUserId}_{request.CanAccessAllLeads}");

                var cachedResult = await _cache.GetAsync<CachedLeadHistoryData>(cacheKey, cancellationToken);

                var shouldRefreshCache = await ShouldRefreshCacheAsync(request.LeadId, tenantId, cachedResult, cancellationToken);

                if (cachedResult != null && !shouldRefreshCache)
                {
                    return new Response<List<LeadHistoryDto>>(cachedResult.HistoryData);
                }

                var historyData = await GetLeadHistoryFromDatabaseAsync(request, currentUserId, tenantId, cancellationToken);

                var cacheData = new CachedLeadHistoryData
                {
                    HistoryData = historyData.Item1,
                    LastModified = DateTime.UtcNow,
                    LeadId = request.LeadId,
                    LatestVersion = historyData.Item2,
                };

                await _cache.SetAsync(cacheKey, cacheData, TimeSpan.FromHours(8), cancellationToken);

                return new Response<List<LeadHistoryDto>>(historyData.Item1);
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        #region Refresh Cache Logic
        private async Task<bool> ShouldRefreshCacheAsync(Guid leadId, string tenantId, CachedLeadHistoryData? cachedData, CancellationToken cancellationToken)
        {
            if (cachedData == null)
                return true;

            try
            {
                // Check if there are any recent changes to LeadHistoryHot table for this lead
                // This provides efficient change detection
                var latestModificationVersion = await _dapperRepository.GetLatestLeadHistoryVersionAsync(leadId);

                // If we can't determine the latest modification time, refresh the cache to be safe
                if (latestModificationVersion == null)
                    return true;

                // Refresh if the latest modification is newer than our cached data
                return latestModificationVersion > cachedData.LatestVersion;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error checking cache freshness for LeadId: {LeadId}, refreshing cache", leadId);
                return true; // Refresh cache on error to ensure data consistency
            }
        }
        #endregion

        #region Get History from Database
        private async Task<(List<LeadHistoryDto>, int)> GetLeadHistoryFromDatabaseAsync(
            GetLeadHistoriesByIdRequestV2 request,
            Guid currentUserId,
            string tenantId,
            CancellationToken cancellationToken)
        {
            var lead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(request.LeadId, true));
            if (lead == null)
            {
                throw new NotFoundException("Lead not found by this id");
            }

            var isAdmin = await _dapperRepository.IsAdminAsync(currentUserId, tenantId);

            List<LeadHistoryHot> leadHistories;
            List<Guid> subIds = new();

            if (isAdmin)
            {
                leadHistories = await _leadHistoryRepo.ListAsync(new HotLeadHistorySpecV2(request.LeadId));
            }
            else
            {
                subIds = (await _dapperRepository.GetSubordinateIdsAsync(currentUserId, tenantId, request?.CanAccessAllLeads, isAdmin))?.ToList() ?? new();
                subIds.AddRange(new List<Guid>() { currentUserId, lead.AssignTo });

                if (lead?.SecondaryUserId != null && lead.SecondaryUserId != Guid.Empty)
                {
                    subIds.Add(lead.SecondaryUserId.Value);
                }

                subIds = subIds?.Where(i => i != Guid.Empty)?.Distinct()?.ToList() ?? new List<Guid>();
                leadHistories = await _leadHistoryRepo.ListAsync(new HotLeadHistorySpecV2(request.LeadId, subIds));
            }

            if (!leadHistories?.Any() ?? true)
            {
                return (null, 0);
            }

            #region Communication And Appointment History
            List<LeadAppointment> leadAppointments = await _leadAppointmentRepo.ListAsync(new LeadAppointmentByLeadIdSpec(lead?.Id ?? Guid.Empty, subIds), cancellationToken);
            var leadComunications = await _leadCommunicationRepo.ListAsync(new GetCommunicationDetailsByLeadId(request.LeadId, subIds), cancellationToken);
            List<WhatsAppCommunication>? whatsAppCommunications = await _whatsAppCommunicationRepo.ListAsync(new GetWhatsAppCommunicationSpec(lead.Id), cancellationToken);
            var leadWithCallLogs = (await _leadRepo.ListAsync(new LeadCallLogByLeadIdSpec(lead.Id), cancellationToken)).FirstOrDefault();
            List<LeadCallLogDto>? callLogDtos = leadWithCallLogs?.LeadCallLogs?.ToList().Adapt<List<LeadCallLogDto>>();
            List<ServetelCallLog> servetelCallLogs = new();
            List<IVRCommonCallLog> ivrCommonCallLogs = new();
            if (isAdmin)
            {
                servetelCallLogs = await _servetelCallLogs.ListAsync(new ServetelCallLogByLeadAndUserSpec(lead.Id));
                ivrCommonCallLogs = await _ivrCommonCallLogRepo.ListAsync(new IVRCommonCallLogByLeadIdSpec(lead.Id));
            }
            else
            {
                servetelCallLogs = await _servetelCallLogs.ListAsync(new ServetelCallLogByLeadAndUserSpec(lead.Id, lead.AssignTo));
                ivrCommonCallLogs = await _ivrCommonCallLogRepo.ListAsync(new IVRCommonCallLogByLeadIdSpec(lead.Id, lead.AssignTo));
                callLogDtos = callLogDtos?.Where(i => i.UserId == lead.AssignTo).ToList();
            }

            List<Guid> userIds = leadAppointments.Select(i => i.LastModifiedBy).ToList();
            userIds.AddRange(leadComunications.Select(i => i.LastModifiedBy).ToList());
            userIds.AddRange(whatsAppCommunications.Select(i => i.LastModifiedBy).ToList());
            userIds.AddRange(lead.BookedDetails.Select(i => i.LastModifiedBy).ToList());
            userIds.AddRange(lead.BookedDetails.Where(i => i.BookedBy.HasValue).Select(i => i.BookedBy.Value).ToList());
            userIds.AddRange(lead.BookedDetails.Where(i => i.UserId.HasValue).Select(i => i.UserId.Value).ToList());
            userIds.AddRange(lead.BookedDetails.Where(i => i.SecondaryOwner.HasValue).Select(i => i.SecondaryOwner.Value).ToList());
            userIds.AddRange(lead.BookedDetails.Where(i => i.TeamHead.HasValue).Select(i => i.TeamHead.Value).ToList());
            userIds.AddRange(callLogDtos.Where(i => i.LastModifiedBy.HasValue).Select(i => i.LastModifiedBy.Value).ToList());
            var users = _userService.GetListAsync(cancellationToken).Result.Where(i => userIds.Contains(i.Id)).ToList();
            List<LeadAppointmentDto> appointmentDto = leadAppointments.Adapt<List<LeadAppointmentDto>>();
            appointmentDto.ForEach(i =>
            {
                if (!string.IsNullOrWhiteSpace(i.Image))
                {
                    i.ImagesWithName ??= new();
                    i.ImagesWithName.Add(new LeadDocument() { FilePath = i.Image });
                }
            });
            appointmentDto = appointmentDto.Where(i => i.Type != AppointmentType.None).ToList();
            List<AppointmentDataDto> leadAppointmentDtos = appointmentDto.Adapt<List<AppointmentDataDto>>();
            leadAppointmentDtos.ForEach(i =>
            {
                var user = users.FirstOrDefault(j => j.Id == i.LastModifiedBy);
                i.LastModifiedByUser = user?.FirstName + " " + user?.LastName;
            });

            var communicationDtos = leadComunications.Adapt<List<LeadCommunicationDto>>();
            communicationDtos.ForEach(i =>
            {
                var user = users.FirstOrDefault(j => j.Id == i.LastModifiedBy);
                i.LastModifiedByUser = user?.FirstName + " " + user?.LastName;
            });


            var bookDetails = await GetBookedDetailsAsync(lead, cancellationToken, isAdmin, currentUserId, users);
            List<WhatsAppCommunicationDto> whatsAppCommunicationDtos = whatsAppCommunications.Adapt<List<WhatsAppCommunicationDto>>();
            if (!isAdmin)
            {
                whatsAppCommunicationDtos.RemoveAll(i => i.UserId != currentUserId || i.UserId == Guid.Empty);
            }
            whatsAppCommunicationDtos.ForEach(wc =>
            {
                var user = users.FirstOrDefault(user => user.Id == wc.UserId);
                wc.LastModifiedByUser = user?.FirstName + " " + user?.LastName;
                wc.LastModifiedBy = user?.Id ?? default;
            });

            #endregion

            var histories = new List<LeadHistoryHot>();
            foreach (var history in leadHistories)
            {
                if ((!ExcludedFieldTypes.Contains(history?.FieldType ?? string.Empty)) && (!ExcludedFields.Contains(history?.FieldName ?? string.Empty)))
                {
                    if (history?.NewValue != null || history?.OldValue != null)
                    {
                        if (history.FieldName == "Created On" || history.FieldName == "Scheduled Date")
                        {
                            history.NewValue = GetLocalDateOfHistory(history.NewValue ?? string.Empty);
                            if (!string.IsNullOrEmpty(history.OldValue))
                            {
                                history.OldValue = GetLocalDateOfHistory(history.OldValue ?? string.Empty);
                            }
                        }
                        histories.Add(history);
                    }
                }
            }

            if (leadAppointmentDtos?.Any() ?? false)
            {
                var appointments = AddAppointmentDetails(leadAppointmentDtos);
                histories.AddRange(appointments);
            }
            if (communicationDtos?.Any() ?? false)
            {
                var communications = AttachCommunicationDetails(communicationDtos);
                histories.AddRange(communications);
            }
            if (callLogDtos?.Any() ?? false)
            {
                var historyItemsWithCallLogs = AddIVRCallLogDetails(callLogDtos, users);
                histories.AddRange(historyItemsWithCallLogs);
            }
            if (whatsAppCommunicationDtos?.Any() ?? false)
            {
                var historyItemsWithCallLogs = AddWhatsAppCommunications(whatsAppCommunicationDtos);
                histories.AddRange(historyItemsWithCallLogs);
            }
            if (bookDetails.bookedDetailsInfo?.Any() ?? false)
            {
                var historyItemsWithBookedDetails = AddBookedDetails(bookDetails.bookedDetailsInfo);
                histories.AddRange(historyItemsWithBookedDetails);
            }
            if (bookDetails.leadBrokerageInfo?.Any() ?? false)
            {
                var historyItemsWithDocumentDetails = AddLeadBrokerageDetails(bookDetails.leadBrokerageInfo, users);
                histories.AddRange(historyItemsWithDocumentDetails);
            }

            var datas = histories.OrderByDescending(i => i.ModifiedOn);
            var maxVersion = datas.Max(i => i.Version);
            histories.OrderByDescending(i => i.ModifiedOn);
            var historyViewModel = histories.OrderByDescending(i => i.ModifiedOn);

            var dto = historyViewModel.Adapt<List<LeadHistoryDto>>();

            return (dto, maxVersion);
        }
        #endregion

        #region History Helpers
        private static string? GetLocalDateOfHistory(string date)
        {
            DateTime? parseDate = null;
            if (!string.IsNullOrEmpty(date))
            {
                string[] dateFormats = { "dd-MM-yy HH:mm:ss", "dd-MM-yyyy HH:mm:ss", "dd/MM/yy HH:mm:ss", "dd/MM/yyyy HH:mm:ss", "yyyy-MM-dd HH:mm:ss", "MM/dd/yyyy HH:mm:ss", "dd-MMM-yyyy HH:mm:ss", "dddd, MMMM dd, yyyy HH:mm:ss" };
                if (DateTime.TryParseExact(date, dateFormats, null, System.Globalization.DateTimeStyles.None, out DateTime dateTime))
                {
                    parseDate = dateTime;
                }

                if (parseDate != null)
                {
                    return (DateTimeExtensions.ToIndianStandardTime(parseDate ?? default)).ToString();
                }
                else
                {
                    return null;
                }
            }
            else
            {
                return null;
            }

        }

        private static readonly HashSet<string> ExcludedFields = new()
        {
            "Last Modified On",
            "Last Modified By User",
            "Lead Number",
            "Serial Number",
            "Created On",
            "Sourcing Manager",
            "Closing Manager",
            "Assigned From",
            "Assign To",
            "Id"
        };

        private static readonly HashSet<string> ExcludedFieldTypes = new()
        {
            "Guid", "Boolean"
        };

        #region Appointment History
        private static List<LeadHistoryHot> AddAppointmentDetails(List<AppointmentDataDto> leadAppointments)
        {
            List<LeadHistoryHot> leadHistoryDtos = new();
            foreach (var appointment in leadAppointments)
            {
                var newValue = JsonConvert.SerializeObject(appointment.Adapt<ViewAppointmentDataDto>());
                var leadHistoryDto = new LeadHistoryHot()
                {
                    FieldName = "Lead Appointment",
                    NewValue = newValue,
                    OldValue = null,
                    ModifiedBy = appointment.LastModifiedByUser,
                    ModifiedOn = appointment.CreatedOn,
                };
                leadHistoryDtos.Add(leadHistoryDto);
            }
            return leadHistoryDtos;
        }
        #endregion

        #region Communication History
        private static List<LeadHistoryHot> AttachCommunicationDetails(List<LeadCommunicationDto>? leadCommunicationDetails)
        {
            List<LeadHistoryHot> leadHistoryDtos = new();
            leadCommunicationDetails = leadCommunicationDetails?.Where(i => i.ContactType != ContactType.Call).ToList() ?? new();
            foreach (var communicationDetail in leadCommunicationDetails)
            {
                var newValue = JsonConvert.SerializeObject(communicationDetail.Adapt<ViewLeadCommunicationDto>());
                var leadHistoryDto = new LeadHistoryHot()
                {
                    FieldName = "Lead Communication",
                    NewValue = newValue,
                    OldValue = null,
                    ModifiedBy = communicationDetail.LastModifiedByUser,
                    ModifiedOn = communicationDetail.CreatedOn,
                };
                leadHistoryDtos.Add(leadHistoryDto);
            }
            return leadHistoryDtos;
        }
        #endregion

        #region IVR Call Log History
        private static List<LeadHistoryHot> AddIVRCallLogDetails(List<LeadCallLogDto>? leadCallLogs, List<UserDetailsDto> userDtos)
        {
            List<LeadHistoryHot> leadHistoryDtos = new();
            if (leadCallLogs?.Any() ?? false)
            {
                foreach (var callLog in leadCallLogs)
                {
                    var user = userDtos.FirstOrDefault(i => i.Id == callLog.UserId);

                    LeadHistoryHot historyDto = new()
                    {
                        FieldName = "LeadCallLog",
                        NewValue = $"{(string.IsNullOrWhiteSpace(callLog.UpdatedCallDirection) ? callLog.CallDirection : callLog.UpdatedCallDirection)} Call -> {(string.IsNullOrWhiteSpace(callLog.UpdatedCallStatus) ? callLog.CallStatus : callLog.UpdatedCallStatus)} -> {FormatDuration(callLog.CallDuration)}. , CallRecordingUrl -> {(string.IsNullOrWhiteSpace(callLog.CallRecordingUrl) ? string.Empty : callLog.CallRecordingUrl)}",
                        ModifiedOn = (DateTime)(callLog.LastModifiedOn ?? DateTime.MinValue),
                        ModifiedBy = user?.FirstName + " " + user?.LastName
                    };
                    leadHistoryDtos.Add(historyDto);
                }
            }
            return leadHistoryDtos;
        }
        #endregion

        #region WhatsApp Communication History
        private static List<LeadHistoryHot> AddWhatsAppCommunications(List<WhatsAppCommunicationDto> whatsAppCommunications)
        {
            List<LeadHistoryHot> leadHistoryDtos = new();
            foreach (var waComm in whatsAppCommunications)
            {
                var newValue = JsonConvert.SerializeObject(waComm.Adapt<ViewWhatsAppCommunicationDto>());
                var leadHistoryDto = new LeadHistoryHot()
                {
                    FieldName = "WhatsApp Communication",
                    NewValue = newValue,
                    OldValue = null,
                    ModifiedBy = waComm.LastModifiedByUser,
                    ModifiedOn = waComm.CreatedOn,
                };
                leadHistoryDtos.Add(leadHistoryDto);
            }
            return leadHistoryDtos;
        }
        #endregion

        #region Booked Details History
        private static List<LeadHistoryHot> AddBookedDetails(List<BookedDetailsDto> bookedDetails)
        {
            List<LeadHistoryHot> leadHistoryDtos = new();
            foreach (var bookData in bookedDetails)
            {

                if (bookData != null)
                {
                    var newValue = JsonConvert.SerializeObject(bookData);
                    var leadHistoryDto = new LeadHistoryHot()
                    {
                        FieldName = "Booked Details Information",
                        NewValue = newValue,
                        OldValue = null,
                        ModifiedBy = bookData.LastModifiedByUser,
                        ModifiedOn = bookData.LastModifiedOn,
                    };
                    leadHistoryDtos.Add(leadHistoryDto);
                }
            }
            return leadHistoryDtos;
        }
        #endregion

        #region Brokerage Details History
        private static List<LeadHistoryHot> AddLeadBrokerageDetails(List<LeadBrokerageInfoDto> brokerageDetails, List<UserDetailsDto>? users)
        {
            List<LeadHistoryHot> leadHistoryDtos = new();
            foreach (var brokerageDetail in brokerageDetails)
            {
                if (brokerageDetail != null)
                {
                    var userName = $"{users?.FirstOrDefault(u => u.Id == brokerageDetail.LastModifiedBy)?.FirstName ?? string.Empty}  {users?.FirstOrDefault(u => u.Id == brokerageDetail.LastModifiedBy)?.LastName ?? string.Empty}";
                    var newValue = JsonConvert.SerializeObject(brokerageDetail);
                    var leadHistoryDto = new LeadHistoryHot()
                    {
                        FieldName = "Lead Brokerage Details Information",
                        NewValue = newValue,
                        OldValue = null,
                        ModifiedBy = userName,
                        ModifiedOn = brokerageDetail.LastModifiedOn,
                    };
                    leadHistoryDtos.Add(leadHistoryDto);
                }
            }
            return leadHistoryDtos;
        }
        #endregion

        #region Get Booked Lead Details
        private async Task<(List<BookedDetailsDto>? bookedDetailsInfo, List<List<DocumentsDto>>? documentsInfo, List<LeadBrokerageInfoDto>? leadBrokerageInfo)> GetBookedDetailsAsync(Domain.Entities.Lead lead, CancellationToken cancellationToken, bool? isAdmin, Guid? currentUserId, List<UserDetailsDto>? users)
        {
            List<LeadBookedDetail> bookDetails = await _bookedInfoRepo.ListAsync(new GetBookedDetailsByIdSpec(lead.Id), cancellationToken);
            List<BookedDetailsDto> bookDetailsDto = bookDetails.Adapt<List<BookedDetailsDto>>();
            List<List<DocumentsDto>> listOfDocuments = new List<List<DocumentsDto>>();
            List<LeadBrokerageInfoDto> leadBrokerageList = new();
            if (!isAdmin ?? false)
            {
                bookDetailsDto.RemoveAll(i => i.UserId != currentUserId || i.UserId == Guid.Empty);
            }
            foreach (var bookedDetail in bookDetailsDto)
            {
                List<DocumentsDto> documents = new List<DocumentsDto>();
                LeadBrokerageInfoDto leadBrokerage = new();
                try
                {
                    var teamHeadDetails = users?.Find(i => i.Id == bookedDetail.TeamHead);
                    if (teamHeadDetails != null)
                    {
                        bookedDetail.TeamHeadName = teamHeadDetails.FirstName + " " + teamHeadDetails.LastName;
                    }
                }
                catch (Exception ex) { }
                try
                {
                    var bookedByDetails = users?.Find(i => i.Id == bookedDetail.BookedBy);
                    if (bookedByDetails != null)
                    {
                        bookedDetail.BookedByName = bookedByDetails.FirstName + " " + bookedByDetails.LastName;
                    }
                }
                catch (Exception ex) { }
                try
                {
                    var secondaryOwnerDetails = users?.Find(i => i.Id == bookedDetail.SecondaryOwner);
                    if (secondaryOwnerDetails != null)
                    {
                        bookedDetail.SecondaryOwnerName = secondaryOwnerDetails.FirstName + " " + secondaryOwnerDetails.LastName;
                    }
                }
                catch { }
                try
                {
                    var userDetails = users?.Find(i => i.Id == bookedDetail.UserId);
                    if (userDetails != null)
                    {
                        bookedDetail.UserName = userDetails.FirstName + " " + userDetails.LastName;
                    }
                }
                catch (Exception ex) { }
                try
                {
                    var LastModifiedByUser = users?.Find(i => i.Id == bookedDetail.LastModifiedBy);
                    if (LastModifiedByUser != null)
                    {
                        bookedDetail.LastModifiedByUser = LastModifiedByUser.FirstName + " " + LastModifiedByUser.LastName;
                    }
                }
                catch (Exception ex) { }
                if (bookedDetail.Documents?.Any() ?? false)
                {
                    documents = bookedDetail?.Documents?.ToList() ?? default;
                    listOfDocuments.Add(documents);
                }
                if (bookedDetail.BrokerageInfo != null)
                {
                    leadBrokerage = bookedDetail?.BrokerageInfo ?? default;
                    leadBrokerageList.Add(leadBrokerage);
                }
            }
            return (bookDetailsDto, listOfDocuments, leadBrokerageList);
        }
        #endregion

        private static string FormatDuration(string durationStr)
        {
            if (double.TryParse(durationStr, out double duration))
            {
                int durationInSeconds = (int)Math.Floor(duration); // or Math.Round(duration)
                int minutes = durationInSeconds / 60;
                int seconds = durationInSeconds % 60;

                if (minutes > 0 && seconds > 0)
                    return $"{minutes} min {seconds} sec";
                else if (minutes > 0)
                    return $"{minutes} min";
                else
                    return $"{seconds} sec";
            }

            return durationStr; // fallback if parsing fails
        }
        #endregion
    }

    public class CachedLeadHistoryData
    {
        public List<LeadHistoryDto> HistoryData { get; set; } = new();
        public DateTime LastModified { get; set; }
        public Guid LeadId { get; set; }
        public int LatestVersion { get; set; }
    }

    public class LeadHistoryLatestVersion
    {
        public Guid LeadId { get; set; }
        public int Version { get; set; }
    }
}
