﻿using Lrb.Application.Identity.Users;

namespace Lrb.Application.UserDetails.Web.Request
{
    public class GetAllEngageToUserInfoRequest : PaginationFilter, IRequest<PagedResponse<EngageToUserDto, string>>
    {

    }
    public class GetAllEngageToUserInfoRequestHandler : IRequestHandler<GetAllEngageToUserInfoRequest, PagedResponse<EngageToUserDto, string>>
    {
        private readonly IUserService _userService;
        public GetAllEngageToUserInfoRequestHandler(IUserService userService)
        {
            _userService = userService;
        }
        public async Task<PagedResponse<EngageToUserDto, string>> Handle(GetAllEngageToUserInfoRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var users = await _userService.GetListAsync(request.PageNumber, request.PageSize, cancellationToken);
                var totalCount = await _userService.GetCountAsync(cancellationToken);
                List<EngageToUserDto> engageUserDtos = users.Adapt<List<EngageToUserDto>>();
                return new PagedResponse<EngageToUserDto, string>(engageUserDtos.OrderBy(i => (i.FirstName + i.LastName)).ToList(),totalCount);
            }
            catch (Exception ex)
            {
                return new() { Message = ex.Message };
            }
        }

    }
}