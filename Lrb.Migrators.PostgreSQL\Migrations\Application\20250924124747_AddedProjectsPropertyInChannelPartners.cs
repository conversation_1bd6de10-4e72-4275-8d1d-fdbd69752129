﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class AddedProjectsPropertyInChannelPartners : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ChannelPartnerProject",
                schema: "LeadratBlack",
                columns: table => new
                {
                    ChannelPartnersId = table.Column<Guid>(type: "uuid", nullable: false),
                    ProjectsId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ChannelPartnerProject", x => new { x.ChannelPartnersId, x.ProjectsId });
                    table.ForeignKey(
                        name: "FK_ChannelPartnerProject_ChannelPartners_ChannelPartnersId",
                        column: x => x.ChannelPartnersId,
                        principalSchema: "LeadratBlack",
                        principalTable: "ChannelPartners",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ChannelPartnerProject_Projects_ProjectsId",
                        column: x => x.ProjectsId,
                        principalSchema: "LeadratBlack",
                        principalTable: "Projects",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ChannelPartnerProject_ProjectsId",
                schema: "LeadratBlack",
                table: "ChannelPartnerProject",
                column: "ProjectsId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ChannelPartnerProject",
                schema: "LeadratBlack");
        }
    }
}
