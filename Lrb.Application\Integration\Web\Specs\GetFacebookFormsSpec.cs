﻿using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Office2010.Excel;
using Lrb.Application.Automation.Dtos;
using Lrb.Application.Integration.Web.Requests;
using Lrb.Application.Integration.Web.Requests.Facebook;
using Lrb.Application.UserDetails.Web.Dtos;
using Lrb.Application.UserDetails.Web.Request;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.User;

namespace Lrb.Application.Integration.Web
{
    public class GetFacebookFormsSpec : EntitiesByPaginationFilterSpec<FacebookFormInfo>
    {
        public GetFacebookFormsSpec(GetAllFacebookFormRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted);
        }
    }
    public class CountFacebookFormsSpec : Specification<FacebookFormInfo>
    {
        public CountFacebookFormsSpec()
        {
            Query.Where(i => !i.IsDeleted);
        }
    }

    //New Implementation

    public class GetAllFacebookIntegrationAccountsSpec : EntitiesByPaginationFilterSpec<FacebookAuthResponse>
    {
        public GetAllFacebookIntegrationAccountsSpec(GetAllFacebookIntegrationAccountsRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted);
        }
        public GetAllFacebookIntegrationAccountsSpec(GetAllFbIntegrationAccountsWithLeadGenFormsRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted)
                .Include(i => i.Statuses);
        }
        public GetAllFacebookIntegrationAccountsSpec(GetAllFbIntegrationAccountsWithSegregatedLeadGenFormsRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted);
        }
        public GetAllFacebookIntegrationAccountsSpec(GetAllFbAdsRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted && filter.AccountId == i.Id);
        }
        public GetAllFacebookIntegrationAccountsSpec(GetAllFacebookAccountsRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted);
        }
        public GetAllFacebookIntegrationAccountsSpec(GetAllFbFormsRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted && filter.AccountId == i.Id);
        }

    }
    public class GetAllFacebookIntegrationAccountsWithSPSpec : EntitiesByPaginationFilterSpec<FacebookAuthResponse>
    {
        public GetAllFacebookIntegrationAccountsWithSPSpec(GetAllFbIntegrationAccountsWithLeadGenFormsWithSPRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted);
        }
        public GetAllFacebookIntegrationAccountsWithSPSpec(GetAllFbIntegrationAccountsWithLeadGenFormsRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted);
        }
    }

    public class CountFacebookIntegrationAccountSpec : Specification<FacebookAuthResponse>
    {
        public CountFacebookIntegrationAccountSpec()
        {
            Query.Where(i => !i.IsDeleted);
        }
    }

    public class GetFacebookAuthResoponseAccountSpec : Specification<FacebookAuthResponse>
    {
        public GetFacebookAuthResoponseAccountSpec(string pixedId)
        {
            if (!string.IsNullOrEmpty(pixedId))
            {
                Query.Where(i => !i.IsDeleted && i.PixelId == pixedId);
            }
        }
    }

    public class GetFacebookAuthResponseByFBIdSpec : Specification<FacebookAuthResponse>
    {
        public GetFacebookAuthResponseByFBIdSpec(string facebookId)
        {
            Query.Where(i => i.FacebookUserId == facebookId && !i.IsDeleted)
                .Include(i => i.Statuses);
        }
    }
    public class GetFacebookAuthResponseByIdSpec : Specification<FacebookAuthResponse>
    {
        public GetFacebookAuthResponseByIdSpec(Guid id)
        {
            Query.Where(i => i.Id == id && !i.IsDeleted);
        }
    }


    public class AssignedFacebookAuthResponseByIdSpec : Specification<FacebookAuthResponse, BaseAssignedEntityDto>
    {
        public AssignedFacebookAuthResponseByIdSpec(Guid id)
        {
            Query.Where(i => !i.IsDeleted && i.Id == id);
        }
        public AssignedFacebookAuthResponseByIdSpec(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted && ids.Contains(i.Id));
        }
    }
    public class GetFacebookConnectedPageAccountByFBIdSpec : Specification<FacebookConnectedPageAccount>
    {
        public GetFacebookConnectedPageAccountByFBIdSpec(string facebookId)
        {
            Query.Include(i => i.FacebookAuthResponse).Where(i => (i.FacebookId == facebookId) && !i.IsDeleted)
                .OrderByDescending(i=>i.LastModifiedOn);
        }
    }
    public class GetFacebookConnectedPageAccountSpec : Specification<FacebookConnectedPageAccount>
    {
        public GetFacebookConnectedPageAccountSpec(Guid id)
        {
            Query.Include(i => i.FacebookAuthResponse).
                Include(i => i.FBLeadGenForms).
                ThenInclude(i => i.Agency).
                Where(i => i.Id == id && !i.IsDeleted);
        }
        public GetFacebookConnectedPageAccountSpec()
        {
            Query.Include(i => i.FacebookAuthResponse).Include(i => i.FBLeadGenForms).ThenInclude(i => i.Agency).Where(i => !i.IsDeleted);
        }
    }
    public class GetFacebookConnectedPageAccountSpecV1 : Specification<FacebookConnectedPageAccount>
    {
        public GetFacebookConnectedPageAccountSpecV1(Guid id)
        {
            Query.Include(i => i.FacebookAuthResponse).
                Include(i => i.FBLeadGenForms).
                Where(i => i.FacebookAuthResponse.Id == id && !i.IsDeleted);
        }

    }
    public class GetFacebookConnectedPageAccountByFBAuthIdSpec : Specification<FacebookConnectedPageAccount>
    {
        public GetFacebookConnectedPageAccountByFBAuthIdSpec(Guid id)
        {
            Query.Include(i => i.FacebookAuthResponse).Where(i => i.FacebookAuthResponseId == id && !i.IsDeleted)
                .OrderByDescending(i => i.LastModifiedOn);
        }
    }

    public class FacebookLeadGenFormByFormIdSpec : Specification<FacebookLeadGenForm>
    {
        public FacebookLeadGenFormByFormIdSpec(string formId)
        {
            Query.Where(i => !i.IsDeleted && i.FacebookId == formId)
                  .Include(i => i.UserAssignment)
                    .ThenInclude(i => i.UserAssignmentConfigurations)
                .Include(i => i.Agency)
                .OrderByDescending(i => i.LastModifiedOn);
        }
    }
    public class FacebookLeadGenFormByIdSpec : Specification<FacebookLeadGenForm>
    {
        public FacebookLeadGenFormByIdSpec(Guid id)
        {
            Query
                .Include(i => i.UserAssignment)
                     .ThenInclude(i => i.UserAssignmentConfigurations)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Location)
                        .ThenInclude(i => i.UserAssignment)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Project)
                        .ThenInclude(i => i.UserAssignment)
                 .Include(i => i.Assignment)
                    .ThenInclude(i => i.Agency)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.ChannelPartner)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Property)
                 .Include(i => i.Assignment)
                    .ThenInclude(i => i.Campaign)
                .Where(i => !i.IsDeleted && i.Id == id);
        }
        public FacebookLeadGenFormByIdSpec(List<Guid> ids)
        {
            Query
                .Include(i => i.UserAssignment)
                     .ThenInclude(i => i.UserAssignmentConfigurations)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Location)
                        .ThenInclude(i => i.UserAssignment)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Project)
                        .ThenInclude(i => i.UserAssignment)
                 .Include(i => i.Agency)
                .Where(i => !i.IsDeleted && ids.Contains(i.Id));
        }
    }
    public class FacebookLeadGenFormByConnectedAccIdSpec : Specification<FacebookLeadGenForm>
    {
        public FacebookLeadGenFormByConnectedAccIdSpec(Guid fbConnectedAccId)
        {
            Query.Where(i => i.FacebookConnectedPageAccountId == fbConnectedAccId && !i.IsDeleted);
        }
    }
    public class FacebookLeadGenFormsByAutomationIdsSpec : Specification<FacebookLeadGenForm, AssignmentEntityDto>
    {
        public FacebookLeadGenFormsByAutomationIdsSpec(List<Guid> ids)
        {
            Query.Where(i => ids.Contains(i.AutomationId) && !i.IsDeleted);
        }
        public FacebookLeadGenFormsByAutomationIdsSpec(List<Guid> ids, Guid authResponseId)
        {
            Query.Include(i => i.FacebookConnectedPageAccount)
                    .ThenInclude(i => i.FacebookAuthResponse)
                .Where(i => ids.Contains(i.AutomationId) && !i.IsDeleted && (i.FacebookConnectedPageAccount != null && i.FacebookConnectedPageAccount.FacebookAuthResponse != null && i.FacebookConnectedPageAccount.FacebookAuthResponse.Id == authResponseId));
        }
    }

    public class FacebookAdsByFbAccountIdSpec : Specification<FacebookAdsInfo>
    {
        public FacebookAdsByFbAccountIdSpec(Guid fbAccId, string? searchText=null)
        {
            if (!string.IsNullOrWhiteSpace(searchText))
            {
                Query.Where(i => !i.IsDeleted && i.FacebookAuthResponseId == fbAccId && (i.AdId == searchText || i.AdName.ToLower().Contains(searchText.ToLower())
                || i.AdSetId == searchText || i.AdSetName.ToLower().Contains(searchText.ToLower()) || i.CampaignId == searchText || i.CampaignName.ToLower().Contains(searchText.ToLower()))).Include(i => i.Agency);
            }
            else
            {
                Query.Where(i => !i.IsDeleted && i.FacebookAuthResponseId == fbAccId).Include(i => i.Agency);
            }
        }
        public FacebookAdsByFbAccountIdSpec(List<Guid> fbAccId)
        {
            Query.Where(i => !i.IsDeleted && fbAccId.Contains(i.FacebookAuthResponseId)).Include(i => i.Agency);
        }

    }
    public class FacebookAdsByFbAccountRequestSpec : EntitiesByPaginationFilterSpec<FacebookAdsInfo>
    {
        public FacebookAdsByFbAccountRequestSpec(GetAllFbAdsRequest filter) : base(filter)
        {
            if (!string.IsNullOrWhiteSpace(filter.SearchText))
            {
                var searchTextLower = filter.SearchText.ToLower();
                Query.Where(i => !i.IsDeleted && i.FacebookAuthResponseId == filter.AccountId &&
                                 (i.AdId == filter.SearchText ||
                                  i.AdName.ToLower().Contains(searchTextLower) ||
                                  i.AdSetId == filter.SearchText ||
                                  i.AdSetName.ToLower().Contains(searchTextLower) ||
                                  i.CampaignId == filter.SearchText ||
                                  i.CampaignName.ToLower().Contains(searchTextLower)))
                     .OrderBy(i => i.Status)
                     .ThenBy(i => !i.IsSubscribed)
                     .ThenBy(i => i.LastModifiedOn)
                     .Include(i => i.Agency);
            }
            else
            {
                Query.Where(i => !i.IsDeleted && i.FacebookAuthResponseId == filter.AccountId)
                             .OrderBy(i => i.Status).ThenBy(i => !i.IsSubscribed).ThenBy(i => i.LastModifiedOn)
                             .Include(i => i.Agency);
            }
        }
    }
    public class FacebookAdsByAdIdSpec : Specification<FacebookAdsInfo>
    {
        public FacebookAdsByAdIdSpec(string adId)
        {
            Query.Where(i => !i.IsDeleted && i.AdId == adId)
                .Include(i => i.Agency)
                .OrderByDescending(i => i.LastModifiedOn);
        }
    }
    public class FacebookAdsByIdsSpec : Specification<FacebookAdsInfo>
    {
        public FacebookAdsByIdsSpec(List<Guid> ids)
        {
            Query.Where(i => ids.Contains(i.Id)).Include(i => i.Agency);
        }
    }
    public class AssignedFbAdsByIdSpec : Specification<FacebookAdsInfo, BaseAssignedEntityDto>
    {
        public AssignedFbAdsByIdSpec(List<Guid> ids, Guid facebookAuthResponseId)
        {
            Query.Where(i => !i.IsDeleted && ids.Contains(i.Id) && i.FacebookAuthResponseId == facebookAuthResponseId);
        }
        public AssignedFbAdsByIdSpec(Guid id, Guid facebookAuthResponseId)
        {
            Query.Where(i => !i.IsDeleted && i.Id == id && i.FacebookAuthResponseId == facebookAuthResponseId);
        }
    }
    public class FacebookAdsByAutomationIdsSpec : Specification<FacebookAdsInfo, AssignmentEntityDto>
    {
        public FacebookAdsByAutomationIdsSpec(List<Guid> ids)
        {
            Query.Where(i => ids.Contains(i.AutomationId) && !i.IsDeleted);
        }
        public FacebookAdsByAutomationIdsSpec(List<Guid> ids, Guid authResponseId)
        {
            Query.Where(i => ids.Contains(i.AutomationId) && !i.IsDeleted && i.FacebookAuthResponseId == authResponseId);
        }
    }
    public class FacebookAdsInfoByIdSpec : Specification<FacebookAdsInfo>
    {
        public FacebookAdsInfoByIdSpec(Guid id)
        {
            Query
                .Include(i => i.UserAssignment)
                     .ThenInclude(i => i.UserAssignmentConfigurations)            
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Location)
                        .ThenInclude(i => i.UserAssignment)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Project)
                        .ThenInclude(i => i.UserAssignment)
                        .Include(i => i.Assignment)
                        .ThenInclude(i => i.Agency)
                .Include(i => i.Assignment)
                        .ThenInclude(i => i.ChannelPartner)
                .Include(i => i.Assignment)
                        .ThenInclude(i => i.Property)
                .Include(i => i.Assignment)
                        .ThenInclude(i => i.Campaign)
                .Where(i => i.Id == id && !i.IsDeleted);
        }
        public FacebookAdsInfoByIdSpec(List<Guid> ids)
        {
            Query
                .Include(i => i.UserAssignment)
                       .ThenInclude(i => i.UserAssignmentConfigurations)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Location)
                        .ThenInclude(i => i.UserAssignment)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Project)
                        .ThenInclude(i => i.UserAssignment)
                .Where(i => ids.Contains(i.Id) && !i.IsDeleted);
        }
    }
    public class FacebookFormByIdSpec : Specification<FacebookLeadGenForm>
    {
        public FacebookFormByIdSpec(Guid id)
        {
            Query
                .Include(i => i.UserAssignment)
                    .ThenInclude(i => i.UserAssignmentConfigurations)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Location)
                        .ThenInclude(i => i.UserAssignment)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Project)
                        .ThenInclude(i => i.UserAssignment)
                        .Include(i => i.Assignment)
                        .ThenInclude(i => i.Agency)
                .Include(i => i.Assignment)
                        .ThenInclude(i => i.ChannelPartner)
                .Include(i => i.Assignment)
                        .ThenInclude(i => i.Property)
                .Include(i => i.Assignment)
                        .ThenInclude(i => i.Campaign)
                .Where(i => i.Id == id && !i.IsDeleted);
        }
        public FacebookFormByIdSpec(List<Guid> ids)
        {
            Query
                .Include(i => i.UserAssignment)
                       .ThenInclude(i => i.UserAssignmentConfigurations)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Location)
                        .ThenInclude(i => i.UserAssignment)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Project)
                        .ThenInclude(i => i.UserAssignment)
                .Where(i => ids.Contains(i.Id) && !i.IsDeleted);
        }
    }
    public class AssignedFbFormByIdSpec : Specification<FacebookLeadGenForm, BaseAssignedEntityDto>
    {
        public AssignedFbFormByIdSpec(Guid id, Guid facebookAuthResponseId)
        {
            Query.Include(i => i.FacebookConnectedPageAccount).Where(i => i.Id == id && !i.IsDeleted && i.FacebookConnectedPageAccount.FacebookAuthResponseId == facebookAuthResponseId);
        }
        public AssignedFbFormByIdSpec(List<Guid> ids, Guid facebookAuthResponseId)
        {
            Query.Include(i => i.FacebookConnectedPageAccount)
                .Where(i => ids.Contains(i.Id) && !i.IsDeleted && i.FacebookConnectedPageAccount.FacebookAuthResponseId == facebookAuthResponseId);
        }
    }
    public class GetAllFacebookAccountsSpec : Specification<FacebookAuthResponse>
    {
        public GetAllFacebookAccountsSpec(Guid accountId) 
        {
            Query.Where(i => !i.IsDeleted && accountId == i.Id);
        }
    }



    public class GetAllFacebookFormsSpec : EntitiesByPaginationFilterSpec<FacebookLeadGenForm>
    {
        public GetAllFacebookFormsSpec(GetAllFbFormsRequest filter) : base(filter)
        {
            if (!string.IsNullOrEmpty(filter.SearchText))
            {
                Query.Where(i => i.FacebookConnectedPageAccount.FacebookAuthResponse.Id == filter.AccountId && !i.IsDeleted &&
                (i.Name.ToLower().Contains(filter.SearchText.ToLower()) ||i.FacebookId== filter.SearchText ||i.PageId== filter.SearchText))
                .OrderBy(i => i.Status).ThenBy(i => !i.IsSubscribed).ThenBy(i => i.LastModifiedOn)
            .Include(i => i.FacebookConnectedPageAccount)
            .ThenInclude(i => i.FacebookAuthResponse)
            .Include(i=>i.Agency);
            }
            else
            {
                Query.Where(i => i.FacebookConnectedPageAccount.FacebookAuthResponse.Id == filter.AccountId && !i.IsDeleted)
                     .OrderBy(i => i.Status).ThenBy(i => !i.IsSubscribed).ThenBy(i => i.LastModifiedOn)
                 .Include(i => i.FacebookConnectedPageAccount)
                 .ThenInclude(i => i.FacebookAuthResponse)
                 .Include(i => i.Agency);
            }
        }

    }
    public class GetFacebookFormsCountByAccIdSpec : Specification<FacebookLeadGenForm>
    {
        public GetFacebookFormsCountByAccIdSpec(Guid accId,string? searchtext=null)
        {
            if (!string.IsNullOrWhiteSpace(searchtext))
            {
                Query.Where(i => i.FacebookConnectedPageAccount.FacebookAuthResponse.Id == accId && !i.IsDeleted && (i.Name.ToLower().Contains(searchtext.ToLower()) || i.FacebookId == searchtext || i.PageId == searchtext));
            }
            else
            {
                Query.Where(i => i.FacebookConnectedPageAccount.FacebookAuthResponse.Id == accId && !i.IsDeleted);

            }
        }

    }
    public class FacebookCampaignssByFbAccountRequestSpec : EntitiesByPaginationFilterSpec<FacebookAdsInfo>
    {
        public FacebookCampaignssByFbAccountRequestSpec(GetAllFbCampaignsRequest filter) : base(filter)
        {
            if (!string.IsNullOrWhiteSpace(filter.SearchText))
            {
                var searchTextLower = filter.SearchText.ToLower();
                Query.Where(i => !i.IsDeleted && i.FacebookAuthResponseId == filter.AccountId &&
                                 (i.CampaignId == filter.SearchText ||
                                  i.CampaignName.ToLower().Contains(searchTextLower)));
            }
            else
            {
                Query.Where(i => !i.IsDeleted && i.FacebookAuthResponseId == filter.AccountId);
            }
        }
    }
}
