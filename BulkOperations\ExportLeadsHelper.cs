﻿using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Domain.Enums;

namespace ExcelUpload
{
    public static class ExportLeadsHelper
    {
        public static string ConvertToString(BaseLeadVisibility? filter)
        {
            switch (filter)
            {
                case BaseLeadVisibility.SelfWithReportee:
                    return "AllLeads";
                case BaseLeadVisibility.Self:
                    return "MyLeads";
                case BaseLeadVisibility.Reportee:
                    return "TeamLeads";
                case BaseLeadVisibility.UnassignLead:
                    return "UnassignLeads";
                case BaseLeadVisibility.DeletedLeads:
                    return "DeletedLeads";
                case BaseLeadVisibility.ReEnquired:
                    return "ReEnquired";
                default:
                    return string.Empty;
            }
        }
        public static LeadTagFilterDto GetLeadTagFilter(GetAllLeadsByNewFiltersRequest request)
        {
            LeadTagFilterDto tagFilterDto = new LeadTagFilterDto();
            foreach (var tag in request?.LeadTags)
                switch (tag)
                {
                    case LeadTagEnum.IsHot:
                        tagFilterDto.IsHotLead = true;
                        break;
                    case LeadTagEnum.IsAboutToConvert:
                        tagFilterDto.IsAboutToConvert = true;
                        break;
                    case LeadTagEnum.IsEscalated:
                        tagFilterDto.IsEscalated = true;
                        break;
                    case LeadTagEnum.IsIntegrationLead:
                        tagFilterDto.IsIntegrationLead = true;
                        break;
                    case LeadTagEnum.IsHighlighted:
                        tagFilterDto.IsHighlighted = true;
                        break;
                    case LeadTagEnum.IsWarmLead:
                        tagFilterDto.IsWarmLead = true;
                        break;
                    case LeadTagEnum.IsColdLead:
                        tagFilterDto.IsColdLead = true;
                        break;
                }
            return tagFilterDto;
        }
        
    }
}
