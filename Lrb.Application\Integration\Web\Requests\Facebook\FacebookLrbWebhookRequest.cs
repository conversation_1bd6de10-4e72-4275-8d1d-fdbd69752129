﻿using Lrb.Application.Agency.Web;
using Lrb.Application.Automation.Dtos;
using Lrb.Application.Automation.Helpers;
using Lrb.Application.Campaigns.Spec;
using Lrb.Application.Common.Atomation;
using Lrb.Application.Common.LeadRotation;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.CustomStatus.Web;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Integration.Web.Automation;
using Lrb.Application.Integration.Web.Helpers;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Mappings;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.UserDetails.Web.Specs;
using Lrb.Application.ZonewiseLocation.Mobile.Specs;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.Integration;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;
using Serilog;
using static Lrb.Application.CustomMasterLeadSubStatus.Web.Request.MasterLeadSubStatusByLevelAndBaseIdSpec;

namespace Lrb.Application.Integration.Web.Requests
{
    public class FacebookLrbWebhookRequest : IRequest<Response<bool>>
    {
        public string? AdId { get; set; }
        public string? FormId { get; set; }
        public string? PageId { get; set; }
        public string? Name { get; set; }
        public string? State { get; set; }
        public string? City { get; set; }
        public string? Location { get; set; }
        public string? Budget { get; set; }
        public string? Notes { get; set; }
        public string? Email { get; set; }
        public string? AgencyName { get; set; }
        public string? CountryCode { get; set; }
        public string? Mobile { get; set; }
        public string? Project { get; set; }
        public string? Property { get; set; }
        public string? LeadExpectedBudget { get; set; }
        public string? PropertyType { get; set; }
        public string? SubmittedDate { get; set; }
        public string? SubmittedTime { get; set; }
        public LeadSource LeadSource { get; set; }
        public IDictionary<string, string>? AdditionalProperties { get; set; }
        // public Guid? TenantId { get; set; }
        public Guid AccountId { get; set; }
        public string? ApiKey { get; set; }
        public string? PrimaryUser { get; set; }
        public string? SecondaryUser { get; set; }
        public string? CampaignName { get; set; }
        public string? ChannelPartnerName { get; set; }
        public string? ChannelPartnerMobile { get; set; }
        public string? ChannelPartnerEmail { get; set; }
    }
    public class FacebookLrbWebhookRequestHandler : IRequestHandler<FacebookLrbWebhookRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<FacebookAuthResponse> _facebookAuthResponseRepo;
        private readonly IRepositoryWithEvents<FacebookConnectedPageAccount> _facebookConnectedPageAccountRepo;
        private readonly IRepositoryWithEvents<FacebookLeadGenForm> _facebookLeadGenFormRepo;
        private readonly ILogger _logger;
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccInfoRepo;
        private readonly IRepositoryWithEvents<IntegrationAssignmentInfo> _integrationAssignmentInfoRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Property> _propertyRepo;
        private readonly INpgsqlRepository _npgsqlRepo;
        private readonly INotificationSenderService _notificationSenderService;
        private readonly IUserService _userService;
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetailsRepo;
        private readonly IRepositoryWithEvents<DuplicateLeadFeatureInfo> _duplicateInfoRepo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<FacebookAdsInfo> _fbAdsInfoRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        private readonly IRepositoryWithEvents<IntegrationLeadInfo> _integrationLeadInfoRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        private readonly IRepositoryWithEvents<LeadEnquiry> _leadEnquiryRepo;
        private readonly IRepositoryWithEvents<Address> _addressRepo;
        //private readonly IRepositoryWithEvents<MasterLeadStatus> _leadStatusRepo;
        private readonly IRepositoryWithEvents<LeadHistory> _leadHistoryRepo;
        private readonly IRepositoryWithEvents<IntegrationAssignment> _integrationAssignmentRepo;
        private readonly IRepositoryWithEvents<AssignmentModule> _assignmentModuleRepo;
        private readonly IRepositoryWithEvents<UserAssignment> _userAssignmentRepo;
        private bool _isDupicateUnassigned = false;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingsRepo;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customLeadStatusRepo;
        private readonly IRepositoryWithEvents<Location> _locationRepo;
        private readonly ILeadRotationService _leadRotationService;
        private readonly IRepositoryWithEvents<Domain.Entities.Agency> _agencyRepo;
        private readonly IRepositoryWithEvents<UserView> _userViewRepo;
        private readonly IRepositoryWithEvents<LeadAssignment> _leadAssignmentRepo;
        private readonly IUserAssignmentMetricsService _userAssignmentMetricsService;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.ChannelPartner> _channelpartner;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Agency> _agencyrepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Campaign> _campaignRepo;

        public FacebookLrbWebhookRequestHandler(
            IRepositoryWithEvents<FacebookAuthResponse> facebookAuthResponseRepo,
            IRepositoryWithEvents<FacebookConnectedPageAccount> facebookConnectedPageAccountRepo,
            IRepositoryWithEvents<FacebookLeadGenForm> facebookLeadGenFormRepo,
            ILogger logger,
            IRepositoryWithEvents<IntegrationAccountInfo> integrationAccInfoRepo,
            IRepositoryWithEvents<IntegrationAssignmentInfo> integrationAssignmentInfoRepo,
            IRepositoryWithEvents<FacebookAdsInfo> fbAdsInfoRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.Project> projectRepo,
            IRepositoryWithEvents<IntegrationLeadInfo> integrationLeadInfoRepo,
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepo,
            IRepositoryWithEvents<LeadEnquiry> leadEnquiryRepo,
            IRepositoryWithEvents<Address> addressRepo,
            //IRepositoryWithEvents<MasterLeadStatus> leadStatusRepo,
            IRepositoryWithEvents<LeadHistory> leadHistoryRepo,
            IRepositoryWithEvents<Domain.Entities.Property> propertyRepo,
            INpgsqlRepository npgsqlRepo,
            INotificationSenderService notificationSenderService,
            IUserService userService,
            IRepositoryWithEvents<Domain.Entities.UserDetails> userDetailsRepo,
            IRepositoryWithEvents<DuplicateLeadFeatureInfo> duplicateInfoRepo,
            IRepositoryWithEvents<IntegrationAssignment> integrationAssignmentRepo,
            IRepositoryWithEvents<AssignmentModule> assignmentModuleRepo,
            IRepositoryWithEvents<UserAssignment> userAssignmentRepo,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<CustomMasterLeadStatus> customLeadStatusRepo,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingsRepo,
            IRepositoryWithEvents<Location> locationRepo,
            ILeadRotationService leadRotationService,
            IRepositoryWithEvents<Domain.Entities.Agency> agencyRepo,
            IRepositoryWithEvents<UserView> userViewRepo,
            IRepositoryWithEvents<LeadAssignment> leadAssignmentRepo,
            IUserAssignmentMetricsService userAssignmentMetricsService,
             IRepositoryWithEvents<Lrb.Domain.Entities.ChannelPartner> channelpartner,
             IRepositoryWithEvents<Lrb.Domain.Entities.Agency> agencyrepo,
             IRepositoryWithEvents<Lrb.Domain.Entities.Campaign> campaignRepo)
        {
            _facebookAuthResponseRepo = facebookAuthResponseRepo;
            _facebookConnectedPageAccountRepo = facebookConnectedPageAccountRepo;
            _facebookLeadGenFormRepo = facebookLeadGenFormRepo;
            _logger = logger;
            _integrationAccInfoRepo = integrationAccInfoRepo;
            _integrationAssignmentInfoRepo = integrationAssignmentInfoRepo;
            _fbAdsInfoRepo = fbAdsInfoRepo;
            _projectRepo = projectRepo;
            _integrationLeadInfoRepo = integrationLeadInfoRepo;
            _leadRepo = leadRepo;
            _leadEnquiryRepo = leadEnquiryRepo;
            _addressRepo = addressRepo;
            //_leadStatusRepo = leadStatusRepo;
            _leadHistoryRepo = leadHistoryRepo;
            _integrationAssignmentInfoRepo = integrationAssignmentInfoRepo;
            _propertyRepo = propertyRepo;
            _npgsqlRepo = npgsqlRepo;
            _notificationSenderService = notificationSenderService;
            _userService = userService;
            _userDetailsRepo = userDetailsRepo;
            _duplicateInfoRepo = duplicateInfoRepo;
            _integrationAssignmentRepo = integrationAssignmentRepo;
            _assignmentModuleRepo = assignmentModuleRepo;
            _userAssignmentRepo = userAssignmentRepo;
            _leadRepositoryAsync = leadRepositoryAsync;
            _globalSettingsRepo = globalSettingsRepo;
            _customLeadStatusRepo = customLeadStatusRepo;
            _locationRepo = locationRepo;
            _leadRotationService = leadRotationService; 
            _agencyRepo = agencyRepo;
            _userViewRepo = userViewRepo;
            _leadAssignmentRepo = leadAssignmentRepo;
            _userAssignmentMetricsService = userAssignmentMetricsService;
            _channelpartner = channelpartner;
            _agencyrepo = agencyrepo;
            _campaignRepo = campaignRepo;

        }
        public async Task<Response<bool>> Handle(FacebookLrbWebhookRequest request, CancellationToken cancellationToken)
        {
            request.LeadSource = LeadSource.Facebook;
            request.AccountId = AccountIdHelper.GetAccountId(request.ApiKey ?? string.Empty);
            var integrationAccountInfo = (await _integrationAccInfoRepo.FirstOrDefaultAsync(new GetIntegrationAccInfoWithAgencySpec(request.AccountId), cancellationToken)) ?? throw new NotFoundException("The API key id expired or invalid.");
            //var integrationAccountInfo = await _integrationAccInfoRepo.GetByIdAsync(request.AccountId) ?? throw new NotFoundException("The API key id expired or invalid.");
            var ad = await _fbAdsInfoRepo.FirstOrDefaultAsync(new FacebookAdsByAdIdSpec(request.AdId ?? string.Empty), cancellationToken);
            var formData = await _facebookLeadGenFormRepo.FirstOrDefaultAsync(new FacebookLeadGenFormByFormIdSpec(request.FormId ?? string.Empty), cancellationToken);
            var pageId = !string.IsNullOrWhiteSpace(request.PageId) ? request.PageId : !string.IsNullOrWhiteSpace(ad?.PageId) ? ad.PageId : !string.IsNullOrWhiteSpace(formData?.PageId) ? formData.PageId : string.Empty;
            var fbPageAccount = await _facebookConnectedPageAccountRepo.FirstOrDefaultAsync(new GetFacebookConnectedPageAccountByFBIdSpec(pageId));
            var fbAuthResponse = fbPageAccount?.FacebookAuthResponse ?? await _facebookAuthResponseRepo.GetByIdAsync(integrationAccountInfo.FacebookAccountId);
            var mobileWithCountryCode = ListingSitesHelper.ConcatenatePhoneNumber(request.CountryCode, request.Mobile);
            List<Domain.Entities.Lead>? duplicateLeads = null;
            var duplicateFeatureInfo = (await _duplicateInfoRepo.ListAsync(cancellationToken)).FirstOrDefault();
            if (duplicateFeatureInfo != null && duplicateFeatureInfo.IsFeatureAdded)
            {
                if (!duplicateFeatureInfo.AllowAllDuplicates)
                {
                    var duplicateLeadSpecDto = request.Adapt<DuplicateLeadSpecDto>();
                    duplicateLeadSpecDto.SubSource = integrationAccountInfo.AccountName?.ToLower() ?? string.Empty;
                    duplicateLeads = await _leadRepo.ListAsync(new DuplicateFeatureSpec(duplicateFeatureInfo, duplicateLeadSpecDto), cancellationToken);
                    if ((duplicateLeads?.Any() ?? false) && (duplicateFeatureInfo.IsSourceBased) && (duplicateFeatureInfo.Sources?.Any(i => i == (int)duplicateLeadSpecDto.LeadSource) ?? false))
                    {
                        duplicateLeads = new();
                    }
                }
            }
            else
            {
                duplicateLeads ??= new();
                var duplicateLead = await _leadRepo.FirstOrDefaultAsync(new LeadByContactNoSpec((mobileWithCountryCode?.Length >= 1 ? mobileWithCountryCode : "invalid ContactNo") ?? "invalid ContactNo", request.Mobile ?? "invalid ContactNo"), cancellationToken);
                if (duplicateLead != null)
                {
                    duplicateLeads.Add(duplicateLead);
                }
            }
            if (!duplicateLeads?.Any() ?? true)
            {
                var leadInfo = request.Adapt<IntegrationLeadInfo>();
                var address = leadInfo.Adapt<Address>();
                var enquiry = leadInfo.Adapt<LeadEnquiry>();
                Domain.Entities.Lead lead = leadInfo.Adapt<Domain.Entities.Lead>();
                if (!string.IsNullOrWhiteSpace(leadInfo.PrimaryUser))
                {
                    var primaryUserDetails = await _userViewRepo.FirstOrDefaultAsync(new GetUserByNameSpec(leadInfo.PrimaryUser), cancellationToken);
                    lead.AssignTo = primaryUserDetails?.Id ?? Guid.Empty;
                }
                await _integrationLeadInfoRepo.AddAsync(leadInfo);
                var customStatus = await _customLeadStatusRepo.FirstOrDefaultAsync(new GetDefaultStatusSpec(), cancellationToken);
                string name = string.IsNullOrWhiteSpace(lead.Name?.Trim()) ? "Facebook Enquiry" : lead.Name.Trim();
                lead.CreatedOnPortal = ListingSitesHelper.GetUtcSubmittedDateTime(leadInfo.SubmittedDate, leadInfo.SubmittedTime);
                lead.LeadNumber = name[0].ToString().ToUpper() + new Random().Next(1000, 9999).ToString() + DateTime.UtcNow.ToString("FFFFFFF");
                lead.CustomLeadStatus = customStatus ?? (await _customLeadStatusRepo.FirstOrDefaultAsync(new MasterLeadSubStatusByNameSpec(new List<string>() { "new" }), cancellationToken));

                lead.AccountId = integrationAccountInfo.Id;
                lead.TagInfo = new();
                lead.AgencyName = integrationAccountInfo.AgencyName;
                lead.Agencies = integrationAccountInfo.Agency != null ? new List<Domain.Entities.Agency>() { integrationAccountInfo.Agency } : null;
                try
                {
                    if (!string.IsNullOrEmpty(request.Project))
                    {
                        var existingNewProjects = await _projectRepo.ListAsync(new GetAllNewProjectsV2Spec());
                        List<Lrb.Domain.Entities.Project> projectsList = new();
                        var newProjects = request.Project.Split(',');
                        var existingTempProjectsNames = existingNewProjects.Select(i => i.Name?.ToLower()).ToList();
                        try
                        {
                            if (newProjects != null && newProjects.Length > 0)
                            {
                                foreach (var newProject in newProjects.Distinct())
                                {

                                    var existingProject = existingNewProjects.FirstOrDefault(i => i.Name.ToLower() == newProject.ToLower());
                                    if (existingProject != null)
                                    {
                                        if (lead.Projects != null)
                                        {
                                            lead.Projects.Add(existingProject);
                                        }
                                        else
                                        {
                                            lead.Projects = new List<Lrb.Domain.Entities.Project>() { existingProject };
                                        }
                                    }
                                    else
                                    {
                                        Domain.Entities.Project tempProjects = new() { Name = newProject };
                                        tempProjects = await _projectRepo.AddAsync(tempProjects, cancellationToken);
                                        if (lead.Projects != null)
                                        {
                                            lead.Projects.Add(tempProjects);
                                        }
                                        else
                                        {
                                            lead.Projects = new List<Lrb.Domain.Entities.Project>() { tempProjects };
                                        }
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            var error = new LrbError()
                            {
                                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                ErrorSource = ex?.Source,
                                StackTrace = ex?.StackTrace,
                                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                ErrorModule = "FacebookLrbWebhookRequestHandler ->Handle()"
                            };
                            await _leadRepositoryAsync.AddErrorAsync(error);
                        }
                    }
                }
                catch (Exception ex)
                {
                    var error = new LrbError()
                    {
                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                        ErrorSource = ex?.Source,
                        StackTrace = ex?.StackTrace,
                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                        ErrorModule = "FacebookLrbWebhookRequestHandler ->Handle()"
                    };
                    await _leadRepositoryAsync.AddErrorAsync(error);
                }

                try
                {
                    if (!string.IsNullOrEmpty(request.Property))
                    {
                        var existingProperties = await _propertyRepo.ListAsync(new GetAllDistinctPropertiesSpec());
                        var properties = request.Property.Split(',');
                        var existingPropertyNames = existingProperties.Select(i => i.Title?.ToLower()).ToList();
                        try
                        {
                            if (properties != null && properties.Length > 0)
                            {
                                foreach (var newProperty in properties.Distinct())
                                {

                                    var existingProperty = existingProperties.FirstOrDefault(i => i.Title?.ToLower() == newProperty.ToLower());
                                    if (existingProperty != null)
                                    {
                                        if (lead.Properties != null)
                                        {
                                            lead.Properties.Add(existingProperty);
                                        }
                                        else
                                        {
                                            lead.Properties = new List<Domain.Entities.Property>() { existingProperty };
                                        }
                                    }
                                    else
                                    {
                                        Domain.Entities.Property property = new() { Title = newProperty };
                                        property = await _propertyRepo.AddAsync(property, cancellationToken);
                                        if (lead.Properties != null)
                                        {
                                            lead.Properties.Add(property);
                                        }
                                        else
                                        {
                                            lead.Properties = new List<Domain.Entities.Property>() { property };
                                        }
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            var error = new LrbError()
                            {
                                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                ErrorSource = ex?.Source,
                                StackTrace = ex?.StackTrace,
                                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                ErrorModule = "FacebookLrbWebhookRequestHandler ->Handle()"
                            };
                            await _leadRepositoryAsync.AddErrorAsync(error);
                        }

                    }

                }
                catch (Exception ex)
                {
                    var error = new LrbError()
                    {
                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                        ErrorSource = ex?.Source,
                        StackTrace = ex?.StackTrace,
                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                        ErrorModule = "FacebookLrbWebhookRequestHandler -> Handle()"
                    };
                    await _leadRepositoryAsync.AddErrorAsync(error);
                }
                try
                {
                    if (!string.IsNullOrWhiteSpace(request.ChannelPartnerName))
                    {
                        Lrb.Domain.Entities.ChannelPartner channelPartner = await _channelpartner.FirstOrDefaultAsync(new GetChannelPartnerByNameSpecs(request.ChannelPartnerName ?? string.Empty), cancellationToken) ?? new();

                        if (channelPartner.Id == Guid.Empty)
                        {
                            channelPartner = new()
                            {
                                FirmName = request.ChannelPartnerName,
                                Email = string.IsNullOrWhiteSpace(request.ChannelPartnerEmail) ? string.Empty : request.ChannelPartnerEmail,
                                ContactNo = string.IsNullOrWhiteSpace(request.ChannelPartnerMobile) ? string.Empty : request.ChannelPartnerMobile,
                                CreatedOn = DateTime.UtcNow
                            };
                            channelPartner = await _channelpartner.AddAsync(channelPartner, cancellationToken);
                        }
                        lead.ChannelPartners = new List<Lrb.Domain.Entities.ChannelPartner>() { channelPartner };
                    }
                }
                catch (Exception ex) { }
                try
                {
                    if (!string.IsNullOrWhiteSpace(request.CampaignName))
                    {
                        Lrb.Domain.Entities.Campaign campaign = await _campaignRepo.FirstOrDefaultAsync(new GetCampaignByNameSpecs(request.CampaignName ?? string.Empty), cancellationToken) ?? new();

                        if (campaign.Id == Guid.Empty)
                        {
                            campaign = new()
                            {
                                Name = request.CampaignName,
                                CreatedOn = DateTime.UtcNow
                            };
                            campaign = await _campaignRepo.AddAsync(campaign, cancellationToken);
                        }
                        lead.Campaigns = new List<Lrb.Domain.Entities.Campaign>() { campaign };
                    }
                }
                catch (Exception ex) { }
                try
                {
                    if (!string.IsNullOrWhiteSpace(request.AgencyName))
                    {
                        Lrb.Domain.Entities.Agency agencyname = await _agencyrepo.FirstOrDefaultAsync(new GetAgencyByNameSpec(request.AgencyName ?? string.Empty), cancellationToken) ?? new();

                        if (agencyname.Id == Guid.Empty)
                        {
                            agencyname = new()
                            {
                                Name = request.AgencyName,
                                CreatedOn = DateTime.UtcNow
                            };
                            agencyname = await _agencyrepo.AddAsync(agencyname, cancellationToken);
                        }
                        lead.Agencies = new List<Lrb.Domain.Entities.Agency>() { agencyname };
                    }
                }
                catch (Exception ex) { }
                if (!string.IsNullOrEmpty(request.Budget))
                {
                    var budget = BudgetHelper.ConvertBugetV2(request.Budget);
                    enquiry.UpperBudget = budget;
                    enquiry.LowerBudget = budget;
                }
                #region Notes
                lead.Notes = !string.IsNullOrEmpty(lead.Notes) ? "Note - " + lead.Notes + "\n" : string.Empty;
                lead.Notes += !string.IsNullOrEmpty(leadInfo.LeadExpectedBudget) ? "Lead Expected Budget - " + leadInfo.LeadExpectedBudget + ", \n" : string.Empty;
                lead.Notes += !string.IsNullOrEmpty(leadInfo.PropertyType) ? "Property Type - " + leadInfo.PropertyType + ", \n" : string.Empty;
                lead.Notes += !string.IsNullOrEmpty(leadInfo.SubmittedDate) ? "Submitted Date - " + leadInfo.SubmittedDate + ", \n" : string.Empty;
                lead.Notes += !string.IsNullOrEmpty(leadInfo.SubmittedTime) ? "Submitted Time - " + leadInfo.SubmittedTime + ", \n" : string.Empty;
                if (!long.TryParse(request.Budget, out long result))
                {
                    lead.Notes += !string.IsNullOrEmpty(leadInfo.Budget) ? "Budget - " + leadInfo.Budget + ", \n" : string.Empty;
                }
                if (leadInfo.AdditionalProperties?.Any() ?? false)
                {
                    lead.Notes += string.Join(",\n", leadInfo.AdditionalProperties.Select(i => i.Key + ": " + i.Value));
                }
                if (string.IsNullOrWhiteSpace(request.PageId))
                {
                    lead.ConfidentialNotes += "\nPage Id is not provided in the request.";
                }
                else if (fbPageAccount == null)
                {
                    lead.ConfidentialNotes += $"\nNo integrated page found by the given Page Id: {request.PageId}.";
                }
                else if (fbPageAccount != null)
                {
                    lead.ConfidentialNotes += $"\nPage Name: {fbPageAccount.Name}";
                }
                if (string.IsNullOrWhiteSpace(request.AdId))
                {
                    lead.ConfidentialNotes += "\nAd Id is not provided in the request.";
                }
                else if (ad == null)
                {
                    lead.ConfidentialNotes += $"\nNo integrated ad found by the given ad Id: {request.AdId}.";
                }
                else if (ad != null && !ad.IsSubscribed)
                {
                    lead.ConfidentialNotes += $"\nAd: {ad.AdName} is not subscribed.";
                }
                else if (ad != null)
                {
                    lead.ConfidentialNotes += $"\nAd Name: {ad.AdName}";
                }
                if (string.IsNullOrWhiteSpace(request.FormId))
                {
                    lead.ConfidentialNotes += "\nForm Id is not provided in the request.";
                }
                else if (formData == null)
                {
                    lead.ConfidentialNotes += $"\nNo integrated form found by the given form Id: {request.FormId}.";
                }
                else if (formData != null && !formData.IsSubscribed)
                {
                    lead.ConfidentialNotes += $"\nForm: {formData.Name} is not subscribed.";
                }
                else if (formData != null)
                {
                    lead.ConfidentialNotes += $"\nForm Name: {formData.Name}";
                }
                #endregion

                var agency = await _agencyRepo.FirstOrDefaultAsync(new GetAgencyByNameSpec(request.AgencyName ?? string.Empty), cancellationToken);
                var agencyToAdd = agency ?? ad?.Agency ?? formData?.Agency ?? integrationAccountInfo?.Agency ?? null;
                lead.Agencies = agencyToAdd != null ? new List<Domain.Entities.Agency>() { agencyToAdd } : lead.Agencies;
                
                lead.AgencyName = request.AgencyName ?? ad?.AgencyName ?? formData?.AgencyName ?? integrationAccountInfo?.AgencyName ?? string.Empty;
                var leads = await _leadRepo.ListAsync(new LeadByContactNoSpec(new List<string>() { lead.ContactNo }));
                #region Automation
                (UserAssignment? UserAssignment, Lrb.Domain.Entities.Project? Project, int? Priority) userAssignmentAndProject = new();
                var globalSetting = (await _globalSettingsRepo.ListAsync(new GetGlobalSettingsSpec(), cancellationToken)).FirstOrDefault();
                var projectInPayload = lead.Projects?[0];
                var projectWithAssignment = await _projectRepo.FirstOrDefaultAsync(new ProjectsByIdSpecV2(projectInPayload?.Id ?? Guid.Empty), cancellationToken);
                var locationIdInPayload = lead.Enquiries?[0].Address?.LocationId;
                var locationWithAssignment  = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(locationIdInPayload ?? Guid.Empty), cancellationToken);
                if (ad != null)
                {
                    userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(ad.Id, LeadSource.Facebook, _integrationAssignmentRepo, _assignmentModuleRepo, globalSetting,fbAdsRepo: _fbAdsInfoRepo, projectWithAssignment: projectWithAssignment, locationWithUserAssignment: locationWithAssignment);
                }
                else if (formData != null)
                {
                    userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(formData.Id, LeadSource.Facebook, _integrationAssignmentRepo, _assignmentModuleRepo, globalSetting, fbFormRepo: _facebookLeadGenFormRepo, projectWithAssignment: projectWithAssignment, locationWithUserAssignment: locationWithAssignment);
                }
                else if (integrationAccountInfo != null)
                {
                    userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(integrationAccountInfo.Id, LeadSource.Facebook, _integrationAssignmentRepo, _assignmentModuleRepo, globalSetting, integrationAccRepo: _integrationAccInfoRepo, projectWithAssignment: projectWithAssignment, locationWithUserAssignment: locationWithAssignment);
                }
                //userAssignmentAndProject.UserAssignment ??= projectWithAssignment?.UserAssignment;
                var existingLead = await _leadRepo.FirstOrDefaultAsync(new GetRootLeadSpec(lead.ContactNo, lead.AlternateContactNo), cancellationToken);
                UserDetailsDto? assignedUser = null;
                if (existingLead != null && existingLead.AssignTo != Guid.Empty)
                {
                    try
                    {
                        assignedUser = await _userService.GetAsync(existingLead?.AssignTo.ToString() ?? Guid.Empty.ToString(), cancellationToken);
                    }
                    catch (Exception ex)
                    {
                    }
                }

                if ((globalSetting?.IsStickyAgentEnabled ?? false) && existingLead != null && existingLead.AssignTo != default && assignedUser?.IsActive == true)
                {
                    lead.AssignTo = existingLead.AssignTo;
                }
                else
                {
                    List<Domain.Entities.Lead> existingLeads = await _leadRepo.ListAsync(new LeadByContactNoSpec(new List<string>() { lead.ContactNo ?? "Invalid Number" })) ?? new();
                    (Guid AssignTo, bool IsDupicateUnassigned) assignToRes = userAssignmentAndProject.UserAssignment != null ? await userAssignmentAndProject.UserAssignment.GetUserIdAsync(_userAssignmentRepo, _userDetailsRepo, _userService, existingLeads) : (Guid.Empty, false);
                    if (userAssignmentAndProject.UserAssignment?.CategoryType == AssignmentCategoryType.PercentageBased)
                    {
                        try
                        {
                            if (ad != null && ad?.UserAssignment != null)
                            {
                                ad.UserAssignment.TotalLeadsCount = (ad?.UserAssignment?.TotalLeadsCount ?? 0) + 1;
                                var assignTo = await _userAssignmentMetricsService.DetermineUserAndSaveInfoAsync(ad.Adapt<AccountInfoDto>());
                                lead.AssignTo = lead.AssignTo == Guid.Empty ? assignTo ?? lead.AssignTo : lead.AssignTo;
                            }
                            else if (formData != null && formData?.UserAssignment != null)
                            {
                                formData.UserAssignment.TotalLeadsCount = (formData?.UserAssignment?.TotalLeadsCount ?? 0) + 1;
                                var assignTo = await _userAssignmentMetricsService.DetermineUserAndSaveInfoAsync(formData.Adapt<AccountInfoDto>());
                                lead.AssignTo = lead.AssignTo == Guid.Empty ? assignTo ?? lead.AssignTo : lead.AssignTo;
                            }
                            else if (integrationAccountInfo != null && integrationAccountInfo?.UserAssignment != null)
                            {
                                integrationAccountInfo.UserAssignment.TotalLeadsCount = (integrationAccountInfo?.UserAssignment?.TotalLeadsCount ?? 0) + 1;
                                var assignTo = await _userAssignmentMetricsService.DetermineUserAndSaveInfoAsync(integrationAccountInfo.Adapt<AccountInfoDto>());
                                lead.AssignTo = lead.AssignTo == Guid.Empty ? assignTo ?? lead.AssignTo : lead.AssignTo;
                            }
                            else
                            {
                                lead.AssignTo = assignToRes.AssignTo;
                            }
                        }
                        catch (Exception ex) { }
                    }
                    else
                    {
                        var assignmentModules = (await _assignmentModuleRepo.ListAsync(default)).OrderBy(i => i.Priority).LastOrDefault();
                        if (assignToRes.AssignTo == Guid.Empty && userAssignmentAndProject.Priority < assignmentModules?.Priority)
                        {
                            bool isAssigned = true;
                            while (isAssigned)
                            {
                                if (ad != null)
                                {
                                    userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(ad.Id, LeadSource.Facebook, _integrationAssignmentRepo, _assignmentModuleRepo, globalSetting, fbAdsRepo: _fbAdsInfoRepo, projectWithAssignment: projectWithAssignment, locationWithUserAssignment: locationWithAssignment, priority: userAssignmentAndProject.Priority);
                                }
                                else if (formData != null)
                                {
                                    userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(formData.Id, LeadSource.Facebook, _integrationAssignmentRepo, _assignmentModuleRepo, globalSetting, fbFormRepo: _facebookLeadGenFormRepo, projectWithAssignment: projectWithAssignment, locationWithUserAssignment: locationWithAssignment, priority: userAssignmentAndProject.Priority);
                                }
                                else if (integrationAccountInfo != null)
                                {
                                    userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(integrationAccountInfo.Id, LeadSource.Facebook, _integrationAssignmentRepo, _assignmentModuleRepo, globalSetting, integrationAccRepo: _integrationAccInfoRepo, projectWithAssignment: projectWithAssignment, locationWithUserAssignment: locationWithAssignment, priority: userAssignmentAndProject.Priority);
                                }

                                assignToRes = userAssignmentAndProject.UserAssignment != null ? await userAssignmentAndProject.UserAssignment.GetUserIdAsync(_userAssignmentRepo, _userDetailsRepo, _userService, existingLeads) : (Guid.Empty, false);

                                if (assignToRes.AssignTo != Guid.Empty)
                                {
                                    isAssigned = false;
                                }
                                else if (assignToRes.AssignTo == Guid.Empty && userAssignmentAndProject.Priority < assignmentModules?.Priority && userAssignmentAndProject.Priority != null)
                                {
                                    userAssignmentAndProject.Priority = userAssignmentAndProject.Priority;
                                }
                                else
                                {
                                    isAssigned = false;
                                }
                            }
                        }
                        lead.AssignTo = lead.AssignTo == Guid.Empty ? assignToRes.AssignTo : lead.AssignTo;
                    }
                    var mobileWithCode = ListingSitesHelper.ConcatenatePhoneNumber(globalSetting?.Countries?.FirstOrDefault()?.DefaultCallingCode, leadInfo.Mobile);
                    // Set OriginalOwner to the assigned user when first assigned
                    if (lead.AssignTo != Guid.Empty && lead.OriginalOwner == null)
                    {
                        lead.OriginalOwner = lead.AssignTo;
                    }
                    #region dualOwnerShip
                    if ((globalSetting?.IsDualOwnershipEnabled ?? false) && (userAssignmentAndProject.UserAssignment?.IsDualAssignmentEnabled ?? false))
                    {
                        if (!string.IsNullOrWhiteSpace(leadInfo.SecondaryUser))
                        {
                            var secondaryUserDetails = await _userViewRepo.FirstOrDefaultAsync(new GetUserByNameSpec(leadInfo.SecondaryUser), cancellationToken);
                            lead.SecondaryUserId = secondaryUserDetails?.Id ?? Guid.Empty;
                        }
                        (Guid AssignTo, bool IsDupicateUnassigned) secondaryAssignTo = userAssignmentAndProject.UserAssignment != null ? await userAssignmentAndProject.UserAssignment?.GetSecondaryUserIdAsync(_userAssignmentRepo, _userDetailsRepo, _userService, _leadRepo, lead, mobileWithCode) : (Guid.Empty, false);
                        lead.SecondaryUserId = lead.SecondaryUserId == null || lead.SecondaryUserId == Guid.Empty ? secondaryAssignTo.AssignTo : lead.SecondaryUserId;
                    }
                    #endregion
                    _logger.Information("ProcessFacebookWebhookRequestHandler -> Mapped Lead after assignment : " + JsonConvert.SerializeObject(lead, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                    _isDupicateUnassigned = assignToRes.IsDupicateUnassigned;
                }
                try
                {
                    if (lead.Projects != null && userAssignmentAndProject.Project != null)
                    {
                        lead.Projects.Add(userAssignmentAndProject.Project);
                    }
                    else if (userAssignmentAndProject.Project != null)
                    {
                        lead.Projects ??= new List<Lrb.Domain.Entities.Project>() { userAssignmentAndProject.Project };
                    }
                    if (lead.Agencies != null && integrationAccountInfo?.Assignment?.Agency != null && integrationAccountInfo?.Assignment?.Agency?.IsDeleted == false)
                    {
                        lead.Agencies.Add(integrationAccountInfo?.Assignment?.Agency);
                    }
                    else if (integrationAccountInfo?.Assignment?.Agency != null && integrationAccountInfo?.Assignment?.Agency?.IsDeleted == false)
                    {
                        lead.Agencies ??= new List<Lrb.Domain.Entities.Agency>() { integrationAccountInfo?.Assignment?.Agency };
                    }

                    if (lead.Campaigns != null && integrationAccountInfo?.Assignment?.Campaign != null && integrationAccountInfo?.Assignment?.Campaign?.IsDeleted == false)
                    {
                        lead.Campaigns.Add(integrationAccountInfo?.Assignment?.Campaign);
                    }
                    else if (integrationAccountInfo?.Campaign != null && integrationAccountInfo?.Campaign?.IsDeleted == false)
                    {
                        lead.Campaigns ??= new List<Lrb.Domain.Entities.Campaign>() { integrationAccountInfo?.Assignment?.Campaign };
                    }
                    if (lead.ChannelPartners != null && integrationAccountInfo?.Assignment?.ChannelPartner != null && integrationAccountInfo?.Assignment?.ChannelPartner?.IsDeleted == false)
                    {
                        lead.ChannelPartners.Add(integrationAccountInfo?.Assignment?.ChannelPartner);
                    }
                    else if (integrationAccountInfo?.Assignment?.ChannelPartner != null && integrationAccountInfo?.Assignment?.IsDeleted == false)
                    {
                        lead.ChannelPartners ??= new List<Lrb.Domain.Entities.ChannelPartner>() { integrationAccountInfo?.Assignment?.ChannelPartner };
                    }

                    if (lead.Properties != null && integrationAccountInfo?.Assignment?.Property != null && integrationAccountInfo?.Assignment?.Property?.IsDeleted == false)
                    {
                        lead.Properties.Add(integrationAccountInfo?.Assignment?.Property);
                    }
                    else if (integrationAccountInfo?.Assignment?.Property != null && integrationAccountInfo?.Assignment?.Property?.IsDeleted == false && integrationAccountInfo?.Assignment?.Property?.IsArchived == false)
                    {
                        lead.Properties ??= new List<Lrb.Domain.Entities.Property>() { integrationAccountInfo?.Assignment?.Property };
                    }
                }
                catch
                {

                }

                #endregion
                #region DuplicateDetails
                var parentLead = await _leadRepo.FirstOrDefaultAsync(new GetRootLeadSpec(lead.ContactNo, lead.AlternateContactNo), cancellationToken);
                if (parentLead != null)
                {
                    lead = lead.AddDuplicateDetail(parentLead.ChildLeadsCount, parentLead.Id);
                    parentLead.ChildLeadsCount += 1;
                    try
                    {
                        await _leadRepo.UpdateAsync(parentLead);
                    }
                    catch (Exception e)
                    {
                        var error = new LrbError()
                        {
                            ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                            ErrorSource = e?.Source,
                            StackTrace = e?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "FacebookLrbWebhookRequestHandler -> Handle()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                }
                #endregion
                lead.ApiKey=request.ApiKey;
                if (lead.AssignTo != Guid.Empty)
                {
                    lead.AssignDate = DateTime.UtcNow;
                }
                await _leadRepo.AddAsync(lead);
                await _addressRepo.AddAsync(address);
                enquiry.LeadId = lead.Id;
                enquiry.SubSource = integrationAccountInfo?.AccountName?.ToLower();
                //enquiry.Address = address;
                if (address != null)
                {
                    enquiry.Addresses = new List<Address> { address };
                }
                enquiry.IsPrimary = true;
                enquiry.Currency = leadInfo?.Currency ?? globalSetting?.Countries?.FirstOrDefault()?.DefaultCurrency ?? "INR";
                await _leadEnquiryRepo.AddAsync(enquiry);
                #region DuplicateLeadCreation
                var contactWithCode = ListingSitesHelper.ConcatenatePhoneNumber(globalSetting?.Countries?.FirstOrDefault()?.DefaultCallingCode, leadInfo.Mobile);

                var totalLeadsCount = 0;
                try
                {
                    if (ad != null)
                    {
                        userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(ad.Id, LeadSource.Facebook, _integrationAssignmentRepo, _assignmentModuleRepo, globalSetting, fbAdsRepo: _fbAdsInfoRepo, projectWithAssignment: projectWithAssignment, locationWithUserAssignment: locationWithAssignment);
                    }
                    else if (formData != null)
                    {
                        userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(formData.Id, LeadSource.Facebook, _integrationAssignmentRepo, _assignmentModuleRepo, globalSetting, fbFormRepo: _facebookLeadGenFormRepo, projectWithAssignment: projectWithAssignment, locationWithUserAssignment: locationWithAssignment);
                    }
                    else if (integrationAccountInfo != null)
                    {
                        userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(integrationAccountInfo.Id, LeadSource.Facebook, _integrationAssignmentRepo, _assignmentModuleRepo, globalSetting, integrationAccRepo: _integrationAccInfoRepo, projectWithAssignment: projectWithAssignment, locationWithUserAssignment: locationWithAssignment);
                    }
                    if ((duplicateFeatureInfo?.IsFeatureAdded ?? false) && (!globalSetting?.IsStickyAgentEnabled ?? false) && ((userAssignmentAndProject.UserAssignment?.IsDuplicateAssignmentEnabled ?? false) || (formData?.UserAssignment?.IsDuplicateAssignmentEnabled ?? false)))
                    {
                        var duplicateLeadAssignmentsIds = userAssignmentAndProject.UserAssignment?.DuplicateUserIds != null ? await userAssignmentAndProject.UserAssignment?.GetUserIdListAsync(_userAssignmentRepo, _userDetailsRepo, _userService, lead) : (new List<Guid>());
                        if (duplicateLeadAssignmentsIds?.Any() ?? false)
                        {
                            if (userAssignmentAndProject.UserAssignment?.ShouldCreateMultipleDuplicates ?? false)
                            {
                                totalLeadsCount = await DuplicateLeadHelper.CreateDuplicateLeadsAsync(lead, lead.Id, _leadRepo, _leadEnquiryRepo, _leadRepositoryAsync, duplicateLeadAssignmentsIds, cancellationToken: cancellationToken, contactWithCode);
                            }
                            else
                            {
                                totalLeadsCount = await DuplicateLeadHelper.CreateDuplicateLeadAsync(userAssignmentAndProject.UserAssignment, lead, lead.Id, _leadRepo, _leadEnquiryRepo, _leadRepositoryAsync, duplicateLeadAssignmentsIds, cancellationToken: cancellationToken, contactWithCode);
                                await _userAssignmentRepo.UpdateAsync(userAssignmentAndProject.UserAssignment);

                            }
                        }

                    }
                }
                catch(Exception ex) 
                {
                    var error = new LrbError()
                    {
                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                        ErrorSource = ex?.Source,
                        StackTrace = ex?.StackTrace,
                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                        ErrorModule = "FacebookLrbWebhookRequestHandler -> Handle()"
                    };
                    await _leadRepositoryAsync.AddErrorAsync(error);
                }
                try
                {
                    if ((duplicateFeatureInfo?.IsFeatureAdded ?? false) && (!globalSetting?.IsStickyAgentEnabled ?? true) && (globalSetting?.IsDualOwnershipEnabled ?? false) && ((userAssignmentAndProject.UserAssignment?.IsDuplicateAssignmentEnabled ?? false) || (formData?.UserAssignment?.IsDuplicateAssignmentEnabled ?? false)))
                    {
                        var replicatedLeads = await _leadRepo.ListAsync(new GetDuplicateLeadSpec(lead.Id), cancellationToken);
                        try
                        {
                            if (replicatedLeads?.Any() ?? false && userAssignmentAndProject.UserAssignment != null)
                            {
                                await UserAssignmentHelper.AssignSecondaryUserIdsToDuplicateLeadsAsync(userAssignmentAndProject.UserAssignment, _userAssignmentRepo, _userDetailsRepo, _userService, _leadRepo, replicatedLeads, contactWithCode);
                                await _leadRepo.UpdateRangeAsync(replicatedLeads);
                            }
                        }
                        catch (Exception ex)
                        {
                            var error = new LrbError()
                            {
                                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                ErrorSource = ex?.Source,
                                StackTrace = ex?.StackTrace,
                                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                ErrorModule = "FacebookLrbWebhookRequestHandler -> Handle() -> AddAsync()"
                            };
                            await _leadRepositoryAsync.AddErrorAsync(error);
                        }
                    }
                }
                catch(Exception ex) { }
                #endregion
                if (ad != null)
                {
                    ad.LeadsCount++;
                    await _fbAdsInfoRepo.UpdateAsync(ad);
                }
                else if (formData != null)
                {
                    formData.TotalLeadsCount++;
                    await _facebookLeadGenFormRepo.UpdateAsync(formData);
                }
                if (integrationAccountInfo != null)
                {
                    integrationAccountInfo.LeadCount++;
                    integrationAccountInfo.TotalLeadCount = integrationAccountInfo?.TotalLeadCount + totalLeadsCount + 1;
                    await _integrationAccInfoRepo.UpdateAsync(integrationAccountInfo);
                }
                var fullLead = (await _leadRepo.ListAsync(new LeadByIdSpec(lead.Id), cancellationToken))?[0];
                var leadDto = fullLead.Adapt<ViewLeadDto>();
                await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken);
                var leadHsitory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                try
                {
                    await _leadHistoryRepo.AddAsync(leadHsitory);
                }
                catch (Exception e)
                {
                    var error = new LrbError()
                    {
                        ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                        ErrorSource = e?.Source,
                        StackTrace = e?.StackTrace,
                        InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                        ErrorModule = "FacebookLrbWebhookRequestHandler -> Handle()"
                    };
                    await _leadRepositoryAsync.AddErrorAsync(error);
                }
                #region DuplicateLead History
                try
                {
                    if ((duplicateFeatureInfo?.IsFeatureAdded ?? false) && (!globalSetting?.IsStickyAgentEnabled ?? true))
                    {
                        var totalDuplicateLeads = await _leadRepo.ListAsync(new GetDuplicateLeadSpec(lead.Id), cancellationToken);
                        if (totalDuplicateLeads?.Any() ?? false)
                        {
                            await DuplicateLeadHelper.CreateDuplicateLeadHistoryAsync(totalDuplicateLeads, _leadHistoryRepo, _leadRepositoryAsync, _userService, cancellationToken);
                        }
                    }
                }
                catch (Exception ex) { }
                #endregion

                #region Assignment History
                try
                {
                    if (fullLead.AssignTo != Guid.Empty)
                    {
                        await ListingSitesHelper.CreateLeadAssignmentHistory(lead, _leadAssignmentRepo, cancellationToken);
                    }
                }
                catch(Exception ex) { }
                #endregion

                #region Push Notification
                Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                try
                {
                    
                    NotificationSettings? notificationSettings = JsonConvert.DeserializeObject<NotificationSettings>(globalSettings?.NotificationSettings ?? string.Empty);
                    List<string> notificationResponses = new();
                    string? tenantId = await _npgsqlRepo.GetTenantId(request.AccountId);
                    List<Guid> adminIds = await _npgsqlRepo.GetAdminIdsAsync(tenantId ?? string.Empty);
                    if (lead.AssignTo == default || lead.AssignTo == Guid.Empty)
                    {
                        _logger.Information($"ProcessFacebookWebhookRequest -> tenantId : {tenantId} , adminIds : " + JsonConvert.SerializeObject(adminIds));
                        if (adminIds.Any())
                        {
                            List<string> notificationScheduleResponse = new();
                            if (_isDupicateUnassigned)
                            {
                                notificationScheduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.DuplicateUnAssigment, lead, null, null, null, null, null, adminIds);
                            }
                            else
                            {
                                notificationScheduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadFromIntegration, lead, null, null, null, null, null, adminIds);
                            }
                            notificationResponses.AddRange(notificationScheduleResponse);
                        }
                    }
                    else if (lead.AssignTo != Guid.Empty)
                    {
                        var user = await _userService.GetAsync(lead.AssignTo.ToString(), cancellationToken);
                        if (user != null)
                        {
                            List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadFromIntegration, lead, lead.AssignTo, user.FirstName + " " + user.LastName, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() });
                            notificationResponses.AddRange(notificationSchduleResponse);
                        }
                        List<Guid> userWithManagerIds = new();
                        if (notificationSettings?.IsManagerEnabled ?? false)
                        {
                            List<Guid> managerIds = await _npgsqlRepo.GetReportingManagerUserIdsAsync(new List<Guid> { lead.AssignTo });
                            userWithManagerIds.AddRange(managerIds);
                        }
                        if (notificationSettings?.IsAdminEnabled ?? false)
                        {
                            userWithManagerIds.AddRange(adminIds);
                        }
                        if (user != null && userWithManagerIds.Any())
                        {
                            userWithManagerIds = userWithManagerIds.Distinct().ToList();
                            userWithManagerIds.Remove(lead.AssignTo);
                            List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, lead, null, null, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, userIds: userWithManagerIds);
                            notificationResponses.AddRange(notificationSchduleResponse);
                        }
                    }
                    if ((duplicateFeatureInfo?.IsFeatureAdded ?? false) && (!globalSettings?.IsStickyAgentEnabled ?? true) && ((userAssignmentAndProject.UserAssignment?.IsDuplicateAssignmentEnabled ?? false) || (formData?.UserAssignment?.IsDuplicateAssignmentEnabled ?? false)))
                    {
                        var allduplicateLeads = await _leadRepo.ListAsync(new GetDuplicateLeadSpec(lead.Id), cancellationToken);
                        if (allduplicateLeads?.Any() ?? false)
                        {
                            foreach (var duplicatelead in allduplicateLeads)
                            {
                                try
                                {
                                    if (duplicatelead.AssignTo != Guid.Empty && duplicatelead.AssignTo != null)
                                    {
                                        var user = await _userService.GetAsync(lead.AssignTo.ToString(), cancellationToken);
                                        if (user != null)
                                        {
                                            List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadFromIntegration, duplicatelead, duplicatelead.AssignTo, user.FirstName + " " + user.LastName, topics: new List<string> { duplicatelead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() });
                                            notificationResponses.AddRange(notificationSchduleResponse);
                                        }
                                        List<Guid> userWithManagerIds = new();
                                        if (notificationSettings?.IsManagerEnabled ?? false)
                                        {
                                            List<Guid> managerIds = await _npgsqlRepo.GetReportingManagerUserIdsAsync(new List<Guid> { duplicatelead.AssignTo });
                                            userWithManagerIds.AddRange(managerIds);
                                        }
                                        if (notificationSettings?.IsAdminEnabled ?? false)
                                        {
                                            userWithManagerIds.AddRange(adminIds);
                                        }
                                        if (user != null && userWithManagerIds.Any())
                                        {
                                            userWithManagerIds = userWithManagerIds.Distinct().ToList();
                                            userWithManagerIds.Remove(duplicatelead.AssignTo);
                                            List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, duplicatelead, null, null, topics: new List<string> { duplicatelead.CreatedBy.ToString(), duplicatelead.LastModifiedBy.ToString() }, userIds: userWithManagerIds);
                                            notificationResponses.AddRange(notificationSchduleResponse);
                                        }
                                    }
                                    _logger.Information($"FacebookLrbWebhookRequestHandler -> NotificationSchedulingResponses JobIds : " + JsonConvert.SerializeObject(notificationResponses));


                                }
                                catch (Exception ex)
                                {
                                    _logger.Information($"FacebookLrbWebhookRequestHandler -> Exception -> PushNotification : " + JsonConvert.SerializeObject(ex, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                                    var error = new LrbError()
                                    {
                                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                        ErrorSource = ex?.Source,
                                        StackTrace = ex?.StackTrace,
                                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                    };
                                    await _leadRepositoryAsync.AddErrorAsync(error);

                                }
                            }
                        }
                    }
                    _logger.Information($"ProcessFacebookWebhookRequest -> NotificationSchedulingResponses JobIds : " + JsonConvert.SerializeObject(notificationResponses));
                }
                catch (Exception ex)
                {
                    _logger.Information($"ProcessFacebookWebhookRequest -> Exception -> PushNotification : " + JsonConvert.SerializeObject(ex, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                }
                #endregion

                #region Lead Rotation
                try
                {
                    List<string> modulesNames = new List<string>() { "Project", "SubSource", "City", "Zone", "Location" };
                    bool IsLeadRotationStarted = false;
                    if ((existingLead != null && existingLead.AssignTo == lead.AssignTo) && (globalSettings?.IsStickyAgentOverriddenEnabled ?? false) && (globalSettings?.IsLeadRotationEnabled ?? false))
                    {
                        if (lead.AssignTo != Guid.Empty && ((userAssignmentAndProject.UserAssignment != null) && userAssignmentAndProject.UserAssignment?.UserAssignmentType == UserAssignmentType.Team))
                        {
                            var assignmentModules = (await _assignmentModuleRepo.ListAsync(default)).Where(i => (i != null && modulesNames.Contains(i.Name))).OrderBy(i => i.Priority).ToList();
                            foreach (var assignmentModule in assignmentModules)
                            {
                                switch (assignmentModule.Name)
                                {
                                    case AssignmentModule.Project:
                                        if ((userAssignmentAndProject.Project != null && (userAssignmentAndProject.Project.UserAssignment != null) && userAssignmentAndProject.Project.UserAssignment.ShouldConfigureLeadRotation == true) && !IsLeadRotationStarted)
                                        {
                                            await _leadRotationService.ScheduleTeamLeadRotation(lead.Id, accountId: userAssignmentAndProject.Project?.UserAssignment?.EntityId);
                                            IsLeadRotationStarted = true;
                                        }
                                        break;
                                    case AssignmentModule.SubSource:
                                        if (lead.AssignTo != Guid.Empty && ((userAssignmentAndProject.UserAssignment != null) && userAssignmentAndProject.UserAssignment?.UserAssignmentType == UserAssignmentType.Team) && !IsLeadRotationStarted)
                                        {
                                            if (userAssignmentAndProject.UserAssignment.ShouldConfigureLeadRotation == true)
                                            {
                                                await _leadRotationService.ScheduleTeamLeadRotation(lead.Id, accountId: ad?.Id ?? formData?.Id ?? integrationAccountInfo?.Id);
                                            }
                                            else
                                            {
                                                await _leadRotationService.ScheduleTeamLeadRotation(lead.Id);
                                            }
                                            IsLeadRotationStarted = true;
                                        }
                                        break;
                                    case AssignmentModule.Location:
                                        if ((((userAssignmentAndProject.UserAssignment != null) && userAssignmentAndProject.UserAssignment.ShouldConfigureLeadRotation == true)) && !IsLeadRotationStarted)
                                        {
                                            await _leadRotationService.ScheduleTeamLeadRotation(lead.Id, accountId: userAssignmentAndProject.UserAssignment.EntityId);
                                            IsLeadRotationStarted = true;
                                        }
                                        break;
                                    case AssignmentModule.Zone:
                                        if ((((userAssignmentAndProject.UserAssignment != null) && userAssignmentAndProject.UserAssignment.ShouldConfigureLeadRotation == true)) && !IsLeadRotationStarted)
                                        {
                                            await _leadRotationService.ScheduleTeamLeadRotation(lead.Id, accountId: userAssignmentAndProject.UserAssignment.EntityId);
                                            IsLeadRotationStarted = true;
                                        }
                                        break;
                                    case AssignmentModule.City:
                                        if ((((userAssignmentAndProject.UserAssignment != null) && userAssignmentAndProject.UserAssignment.ShouldConfigureLeadRotation == true)) && !IsLeadRotationStarted)
                                        {
                                            await _leadRotationService.ScheduleTeamLeadRotation(lead.Id, accountId: userAssignmentAndProject.UserAssignment.EntityId);
                                            IsLeadRotationStarted = true;
                                        }
                                        break;
                                }
                            }
                        }
                    }
                    else if ((globalSettings != null && globalSettings.IsLeadRotationEnabled) && existingLead == null && ((userAssignmentAndProject.UserAssignment != null) && userAssignmentAndProject.UserAssignment?.UserAssignmentType == UserAssignmentType.Team))
                    {
                        if (lead.AssignTo != Guid.Empty)
                        {
                            var assignmentModules = (await _assignmentModuleRepo.ListAsync(default)).Where(i => (i != null && modulesNames.Contains(i.Name))).OrderBy(i => i.Priority).ToList();
                            foreach (var assignmentModule in assignmentModules)
                            {
                                switch (assignmentModule.Name)
                                {
                                    case AssignmentModule.Project:
                                        if ((userAssignmentAndProject.Project != null && (userAssignmentAndProject.Project.UserAssignment != null) && userAssignmentAndProject.Project.UserAssignment.ShouldConfigureLeadRotation == true) && !IsLeadRotationStarted)
                                        {
                                            await _leadRotationService.ScheduleTeamLeadRotation(lead.Id, accountId: userAssignmentAndProject.Project?.UserAssignment?.EntityId);
                                            IsLeadRotationStarted = true;
                                        }
                                        break;
                                    case AssignmentModule.SubSource:
                                        if (lead.AssignTo != Guid.Empty && ((userAssignmentAndProject.UserAssignment != null) && userAssignmentAndProject.UserAssignment?.UserAssignmentType == UserAssignmentType.Team) && !IsLeadRotationStarted)
                                        {
                                            if (userAssignmentAndProject.UserAssignment.ShouldConfigureLeadRotation == true)
                                            {
                                                await _leadRotationService.ScheduleTeamLeadRotation(lead.Id, accountId: ad?.Id ?? formData?.Id ?? integrationAccountInfo?.Id);
                                            }
                                            else
                                            {
                                                await _leadRotationService.ScheduleTeamLeadRotation(lead.Id);
                                            }
                                            IsLeadRotationStarted = true;
                                        }
                                        break;
                                    case AssignmentModule.Location:
                                        if ((((userAssignmentAndProject.UserAssignment != null) && userAssignmentAndProject.UserAssignment.ShouldConfigureLeadRotation == true)) && !IsLeadRotationStarted)
                                        {
                                            await _leadRotationService.ScheduleTeamLeadRotation(lead.Id, accountId: userAssignmentAndProject.UserAssignment.EntityId);
                                            IsLeadRotationStarted = true;
                                        }
                                        break;
                                    case AssignmentModule.Zone:
                                        if ((((userAssignmentAndProject.UserAssignment != null) && userAssignmentAndProject.UserAssignment.ShouldConfigureLeadRotation == true)) && !IsLeadRotationStarted)
                                        {
                                            await _leadRotationService.ScheduleTeamLeadRotation(lead.Id, accountId: userAssignmentAndProject.UserAssignment.EntityId);
                                            IsLeadRotationStarted = true;
                                        }
                                        break;
                                    case AssignmentModule.City:
                                        if ((((userAssignmentAndProject.UserAssignment != null) && userAssignmentAndProject.UserAssignment.ShouldConfigureLeadRotation == true)) && !IsLeadRotationStarted)
                                        {
                                            await _leadRotationService.ScheduleTeamLeadRotation(lead.Id, accountId: userAssignmentAndProject.UserAssignment.EntityId);
                                            IsLeadRotationStarted = true;
                                        }
                                        break;
                                }
                            }
                        }
                    }
                }
                catch { }
                #endregion

            }
            GetInvalidItemsModel InvalidData = new();
            if (duplicateLeads?.Any(i => i != null) ?? false)
            {
                List<DuplicateItem> duplicateItems = new();
                duplicateLeads.ToList().ForEach(i => duplicateItems.Add(new DuplicateItem(i.Name, i.ContactNo, DuplicateItemType.Lead)));
                InvalidData.DuplicateItems?.DuplicateItems.AddRange(duplicateItems);
                InvalidData.DuplicateItems.LeadCount = duplicateItems.Count;
            }
            if (InvalidData.DuplicateItems?.DuplicateItems.Any() ?? false)
            {
                return new Response<bool>(true, JsonConvert.SerializeObject(InvalidData));
            }
            return new(true);
        }

        private async Task<Domain.Entities.Lead> AutomateLeadAsync(Domain.Entities.Lead lead, Guid? automationId, List<Domain.Entities.Lead> duplicateLeads)
        {
            _logger.Information("FacebookLrbWebhookRequestHandler -> Mapped Lead before assignment : " + JsonConvert.SerializeObject(lead, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
            var assignmentInfo = await _integrationAssignmentInfoRepo.GetByIdAsync(automationId ?? Guid.Empty);
            if (assignmentInfo?.ProjectIds?.Any() ?? false)
            {
                var project = (await _projectRepo.ListAsync(new GetAllProjectsV2Spec(assignmentInfo?.ProjectIds ?? new()), CancellationToken.None))?.FirstOrDefault();
                lead.Projects ??= new List<Lrb.Domain.Entities.Project>();
                if (project != null)
                {
                    lead.Projects.Add(project);
                    if (project.IsAutomated || project.AutomationId != default)
                    {
                        var projAssignmentInfo = await _integrationAssignmentInfoRepo.GetByIdAsync(project.AutomationId);
                        if (projAssignmentInfo?.AssignedUserIds?.Any() ?? false)
                        {
                            assignmentInfo = projAssignmentInfo;
                        }
                    }
                }
            }
            var assignedUserIds = assignmentInfo?.AssignedUserIds;
            if (assignedUserIds?.Any() ?? false)
            {
                if (duplicateLeads?.Any() ?? false)
                {
                    var allMatched = assignedUserIds.All(i => duplicateLeads.Select(j => j.AssignTo).Any(j => j == i));
                    if (!allMatched)
                    {
                        foreach (var id in assignedUserIds)
                        {
                            lead = await lead.GetAssignedLead(assignmentInfo?.Id ?? Guid.Empty, _integrationAssignmentInfoRepo, _userDetailsRepo, _userService);
                            if (!duplicateLeads?.Any(i => i.AssignTo == lead.AssignTo) ?? false)
                            {
                                break;
                            }
                        }
                        if (duplicateLeads?.Any(i => i.AssignTo == lead.AssignTo) ?? false)
                        {
                            lead.AssignTo = Guid.Empty;
                            _isDupicateUnassigned = true;
                        }
                    }
                    else
                    {
                        _isDupicateUnassigned = true;
                    }
                }
                else
                {
                    lead = await lead.GetAssignedLead(assignmentInfo?.Id ?? Guid.Empty, _integrationAssignmentInfoRepo, _userDetailsRepo, _userService);
                }
            }
            _logger.Information("FacebookLrbWebhookRequestHandler -> Mapped Lead after assignment : " + JsonConvert.SerializeObject(lead, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
            return lead;
        }
    }
}
