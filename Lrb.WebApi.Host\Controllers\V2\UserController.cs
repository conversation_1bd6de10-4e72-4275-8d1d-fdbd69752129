﻿using Lrb.Application.UserDetails.Web;
using Lrb.Application.UserDetails.Web.Dtos;
using Lrb.Application.UserDetails.Web.Request;


namespace Lrb.WebApi.Host.Controllers.V2
{
    [Authorize]
    [Route("api/v2/[controller]")]
    [ApiVersionNeutral]
    public class UserController : VersionedApiController
    {
        [HttpPost("getAllUsers")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Users)]
        [OpenApiOperation("Get all user details.", "")]
        public Task<PagedResponse<UserDetailsDto, UserDetailsCountDto>> SearchAsync([FromBody] GetAllUserInfoViewRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpPost("lead-count")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Users)]
        [OpenApiOperation("Get all user's leads count.", "")]
        public Task<PagedResponse<UserLeadCountDto, string>> GetLeadCountAsync([FromBody] GetLeadCountForAllUsersRequest request)
        {
            return Mediator.Send(request);
        }

    }
}
