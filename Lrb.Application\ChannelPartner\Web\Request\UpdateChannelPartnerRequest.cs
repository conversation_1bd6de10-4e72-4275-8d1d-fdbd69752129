﻿using Lrb.Application.ChannelPartner.Web.Dtos;
using Lrb.Application.ChannelPartner.Web.Specs;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Lead.Web;
using Lrb.Application.Project.Web.Requests.CommonHandler;
using Lrb.Domain.Entities;


namespace Lrb.Application.ChannelPartner.Web.Request
{

    public class UpdateChannelPartnerRequest : CreateOrUpdateChannelPartnerDto, IRequest<Response<ViewChannelPartnerDto>>
    {
    }
    public class UpdateChannelPartnerRequestHandler : ProjectCommonRequestHandler, IRequestHandler<UpdateChannelPartnerRequest, Response<ViewChannelPartnerDto>>
    {
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.ChannelPartner> _cpRepository;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.GlobalSettings> _globalsettingRepo;
        public UpdateChannelPartnerRequestHandler(IRepositoryWithEvents<Domain.Entities.ChannelPartner> cpRepository,
            IServiceProvider serviceProvider,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalsettingRepo) : base(serviceProvider)
        {
            _cpRepository = cpRepository;
            _globalsettingRepo = globalsettingRepo;
        }

        public async Task<Response<ViewChannelPartnerDto>> Handle(UpdateChannelPartnerRequest request, CancellationToken cancellationToken)
        {
            if (request.Id == Guid.Empty || string.IsNullOrWhiteSpace(request.FirmName)) { throw new("Name is required!"); }
            var existingChannelPartner = (await _cpRepository.FirstOrDefaultAsync(new GetAllChannelPartnerSpecs(request.Id), cancellationToken)) ?? throw new NotFoundException("No agency found!");
            var createdon = existingChannelPartner.CreatedOn;
            var createdby = existingChannelPartner.CreatedBy;
            var address = await CreateAddressAsync(request.Address, cancellationToken);
            request.Address = null;
            var cp = request.Adapt(existingChannelPartner);
            cp.Address = address;
            cp.CreatedOn = createdon;
            cp.CreatedBy = createdby;
            List<Lrb.Domain.Entities.Project>? projects = new();
            Domain.Entities.GlobalSettings? globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
            var projectList = (request.ProjectsList?.Any() ?? false) ? request.ProjectsList?.Where(i => !string.IsNullOrWhiteSpace(i)).ToList() : null;
            if (projectList?.Any() ?? false)
            {
                foreach (var newProject in projectList)
                {
                    Lrb.Domain.Entities.Project? existingProject = (await _projectRepo.ListAsync(new GetNewProjectsByIdV2Spec(newProject), cancellationToken)).FirstOrDefault();
                    if (existingProject != null)
                    {
                        projects.Add(existingProject);
                    }
                    else
                    {
                        Lrb.Domain.Entities.Project project = new()
                        {
                            Name = newProject,
                            MonetaryInfo = new ProjectMonetaryInfo
                            {
                                Currency = globalSettings?.Countries?.FirstOrDefault()?.DefaultCurrency ?? "INR"
                            }
                        };
                        project = await _projectRepo.AddAsync(project, cancellationToken);
                        projects.Add(project);
                    }
                }
                cp.Projects = projects;
            }
            else if ((cp.Projects?.Any() ?? false) && projectList == null)
            {
                cp.Projects = projects;
            }
            await _cpRepository.UpdateAsync(cp);
            return new(cp.Adapt<ViewChannelPartnerDto>());
        }
    }
}
