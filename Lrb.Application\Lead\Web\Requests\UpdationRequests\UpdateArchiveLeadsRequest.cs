﻿using Lrb.Application.DailyStatusUpdates.Dtos;
using Lrb.Application.Lead.Web.Mappings;

namespace Lrb.Application.Lead.Web.Requests
{
    public class UpdateArchiveLeadsRequest : IRequest<Response<bool>>
    {
        public List<Guid> Ids { get; set; } = default!;
    }
    public class UpdateArchiveLeadsRequestHandler : LeadCommonRequest<PERSON><PERSON><PERSON>, IRequestHandler<UpdateArchiveLeadsRequest, Response<bool>>
    {
        public UpdateArchiveLeadsRequestHandler(IServiceProvider serviceProvider) : base(serviceProvider, typeof(UpdateArchiveLeadsRequestHandler).Name, "Handle")
        {
        }
        public async Task<Response<bool>> Handle(UpdateArchiveLeadsRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var currentUser = _currentUser.GetUserId();
                Domain.Entities.Lead leadForNotification = new();
                Dictionary<Guid, Guid> leadIdsWithUserId = new();
                List<LeadHistoryHot> newLeadHotHistories = new();
                var leadStatues = await _customLeadStatusRepo.ListAsync(cancellationToken);
                var masterPropertyTypes = await _propertyTypeRepo.ListAsync(cancellationToken);
                foreach (Guid leadId in request.Ids)
                {
                    var fullLead = (await _leadRepo.ListAsync(new ArchiveLeadSpec(leadId), cancellationToken)).FirstOrDefault() ?? throw new NotFoundException("No lead found by this Id");
                    var oldLeadDto = fullLead.Adapt<ViewLeadDto>();
                    fullLead.IsArchived = false;
                    fullLead.RestoredBy = currentUser;
                    fullLead.RestoredOn = DateTime.UtcNow;

                    await _leadRepo.UpdateAsync(fullLead, cancellationToken);

                    await UpdateLeadHistoryAsync(fullLead, cancellationToken: cancellationToken);

                    leadIdsWithUserId.Add(fullLead.Id, fullLead.AssignTo);
                    if (leadForNotification != null)
                    {
                        leadForNotification = fullLead;
                    }

                    #region Update New Lead History Table
                    var newLeadDto = fullLead.Adapt<ViewLeadDto>();
                    newLeadDto.LastModifiedBy = currentUser;
                    await newLeadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken, currentUser);
                    await oldLeadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken, currentUser);
                    var latestModificationVersion = await _dapperRepository.GetLatestLeadHistoryVersionAsync(newLeadDto.Id);
                    var histories = await LeadHistoryHelperV2.V2UpdateLeadHistoryForVM(newLeadDto, oldLeadDto, latestModificationVersion ?? 1, leadStatues, masterPropertyTypes, _userService, currentUser, cancellationToken);
                    newLeadHotHistories.AddRange(histories);
                    #endregion
                }

                if (newLeadHotHistories?.Any() ?? false)
                {
                    await _newLeadHistoryHotRepo.AddRangeAsync(newLeadHotHistories);
                }
                var groupedUsersWithLeadCount = leadIdsWithUserId.GroupBy(i => i.Value).ToDictionary(i => i.Key, j => j.ToList().Count());
                await SendLeadRestoreNotificationAsync(leadForNotification, cancellationToken, groupedUsersWithLeadCount: groupedUsersWithLeadCount);
                return new(true);
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{typeof(AddDocumentRequestHandler).Name} - Handle()");
                return new(false);
            }
        }
    }
}
