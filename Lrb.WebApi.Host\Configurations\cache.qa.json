﻿{
  "CacheSettings": {
    "UseDistributedCache": true,
    "PreferRedis": true,
    "RedisURL": "redis-18504.c53.west-us.azure.redns.redis-cloud.com:18504",
    "RedisPassword": "SoWu6s5aVJNBdVHHoRz33pCTxXJQlTYk"
  },
  "RedisStackSettings": {
    "ConnectionString": "redis-10420.crce182.ap-south-1-1.ec2.redns.redis-cloud.com:10420,User=default,Password=zslBjP0i4YtANuvHctE5trkxOT9YwbzI",
    "DefaultDatabase": 0,
    "SSL": false,
    "AbortOnConnectFail": false,
    "ConnectTimeout": 5000,
    "ConnectRetry": 3,
    "KeepAlive": 180,
    "SyncTimeout": 5000
  }
}
