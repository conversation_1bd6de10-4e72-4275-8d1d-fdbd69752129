﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class AddRetentionConfig : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "LeadAssignmentType",
                schema: "LeadratBlack",
                table: "Teams",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsReshuffleEnabled",
                schema: "LeadratBlack",
                table: "TeamConfigurations",
                type: "boolean",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "TotalReshuffleCount",
                schema: "LeadratBlack",
                table: "TeamConfigurations",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ReShuffleCount",
                schema: "LeadratBlack",
                table: "LeadRotationTrackers",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<Guid>(
                name: "TeamConfigurationId",
                schema: "LeadratBlack",
                table: "CustomMasterLeadStatuses",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_CustomMasterLeadStatuses_TeamConfigurationId",
                schema: "LeadratBlack",
                table: "CustomMasterLeadStatuses",
                column: "TeamConfigurationId");

            migrationBuilder.AddForeignKey(
                name: "FK_CustomMasterLeadStatuses_TeamConfigurations_TeamConfigurati~",
                schema: "LeadratBlack",
                table: "CustomMasterLeadStatuses",
                column: "TeamConfigurationId",
                principalSchema: "LeadratBlack",
                principalTable: "TeamConfigurations",
                principalColumn: "Id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CustomMasterLeadStatuses_TeamConfigurations_TeamConfigurati~",
                schema: "LeadratBlack",
                table: "CustomMasterLeadStatuses");

            migrationBuilder.DropIndex(
                name: "IX_CustomMasterLeadStatuses_TeamConfigurationId",
                schema: "LeadratBlack",
                table: "CustomMasterLeadStatuses");

            migrationBuilder.DropColumn(
                name: "LeadAssignmentType",
                schema: "LeadratBlack",
                table: "Teams");

            migrationBuilder.DropColumn(
                name: "IsReshuffleEnabled",
                schema: "LeadratBlack",
                table: "TeamConfigurations");

            migrationBuilder.DropColumn(
                name: "TotalReshuffleCount",
                schema: "LeadratBlack",
                table: "TeamConfigurations");

            migrationBuilder.DropColumn(
                name: "ReShuffleCount",
                schema: "LeadratBlack",
                table: "LeadRotationTrackers");

            migrationBuilder.DropColumn(
                name: "TeamConfigurationId",
                schema: "LeadratBlack",
                table: "CustomMasterLeadStatuses");
        }
    }
}
