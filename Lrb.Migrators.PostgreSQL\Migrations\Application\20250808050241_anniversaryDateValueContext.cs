﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class anniversaryDateValueContext : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "AnniversaryDate",
                schema: "LeadratBlack",
                table: "Prospects",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "AnniversaryDate",
                schema: "LeadratBlack",
                table: "Leads",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<IDictionary<int, DateTime?>>(
                name: "AnniversaryDate",
                schema: "LeadratBlack",
                table: "LeadHistories",
                type: "jsonb",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AnniversaryDate",
                schema: "LeadratBlack",
                table: "Prospects");

            migrationBuilder.DropColumn(
                name: "AnniversaryDate",
                schema: "LeadratBlack",
                table: "Leads");

            migrationBuilder.DropColumn(
                name: "AnniversaryDate",
                schema: "LeadratBlack",
                table: "LeadHistories");
        }
    }
}
