﻿namespace Lrb.Infrastructure.BlobStorage
{
    public class AWSSettings
    {
        public string? Region { get; set; }
        public string? AWSAccessToken { get; set; }
        public string? AWSSecret { get; set; }
        public string? AWSS3BucketName { get; set; }
        public string? PinpointApplicationId { get; set; }
        public string? AWSS3BucketUrl { get; set; }
        public string? TestKey { get; set; }
        public string? SecretsManagerName { get; set; }
    }
    public class AwsSecretManagerSettings
    {
        public string? Region { get; set; }
        public string? AWSAccessToken { get; set; }
        public string? AWSSecret { get; set; }
        public string? SecretsManagerName { get; set; }
    }
}
