﻿using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.Lead.Mobile.Dtos.v3;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;

namespace Lrb.Application.Lead.Mobile.v3
{
    public class V3GetAllLeadsRequest : PaginationFilter, IRequest<Response<V3CategoryDto>>
    {
        public LeadFilterTypeMobile? FilterType { get; set; }
        public BaseLeadVisibility LeadVisibility { get; set; }
        public bool? IsWithTeam { get; set; }
        public List<Guid>? AssignToIds { get; set; }
        public DateType? DateType { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? SearchByNameOrNumber { get; set; }
        public bool? IsPicked { get; set; }
        //public UserType? UserType { get; set; }
        public List<Guid>? SecondaryUsers { get; set; }
        public bool? IsDualOwnershipEnabled { get; set; }
        public List<Guid>? BookedIds { get; set; }
        public bool? IsUntouched { get; set; }
        public List<Guid>? DesignationsId { get; set; }
        public bool? CanAccessAllLeads { get; set; }
        public List<Guid>? SubStatuses { get; set; }
        public string? ConfidentialNotes { get; set; }
        public List<string>? CampaignNames { get; set; }
        public int? ChildLeadsCount { get; set; }
        public List<Guid>? UserIds { get; set; }
        public List<CallStatus>? CallStatuses { get; set; }
        public List<CallDirection>? CallDirections { get; set; }

        public List<Guid>? OriginalOwnerIds { get; set; }
        public bool? ShowOnlyParentLeads { get; set; }
        public List<LeadType>? LeadType { get; set; }
        public List<string>? CountryCode { get; set; }
        public List<string>? AltCountryCode { get; set; }
        public string? TimeZoneId { get; set; }
        public TimeSpan? BaseUTcOffset { get; set; }
        public DateTime? AnniversaryDate { get; set; }

    }
    public class V3GetAllLeadsRequestHandler : IRequestHandler<V3GetAllLeadsRequest, Response<V3CategoryDto>>
    {
        private readonly ICurrentUser _currentUser;
        private readonly ILeadRepository _efLeadRepository;
        private readonly IDapperRepository _dapperRepository;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _statuses;
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetails;

        public V3GetAllLeadsRequestHandler(
        ICurrentUser currentUser,
        ILeadRepository efLeadRepository,
        IDapperRepository dapperRepository,
        ILeadRepositoryAsync leadRepositoryAsync,
        IRepositoryWithEvents<CustomMasterLeadStatus> statuses,
        IRepositoryWithEvents<Domain.Entities.UserDetails> userDetails)
        {
            _currentUser = currentUser;
            _efLeadRepository = efLeadRepository;
            _dapperRepository = dapperRepository;
            _leadRepositoryAsync = leadRepositoryAsync;
            _statuses = statuses;
            _userDetails = userDetails;
        }

        public async Task<Response<V3CategoryDto>> Handle(V3GetAllLeadsRequest request, CancellationToken cancellationToken)
        {
            var userId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            //request.UserType = request?.UserType ?? UserType.None;
            List<Guid> subIds = new();
            try
            {
                if (request?.DesignationsId?.Any() ?? false)
                {
                    var users = await _userDetails.ListAsync(new Lrb.Application.Dashboard.Web.Specs.GetUsersByDesignationIdSpec(request.DesignationsId));
                    var userIds = users.Select(i => i.UserId).ToList();
                    if (request.AssignToIds == null)
                    {
                        request.AssignToIds = new List<Guid>();
                    }
                    request?.AssignToIds.AddRange(userIds);

                }
                if (request?.AssignToIds?.Any() ?? false)
                {
                    if (request?.IsWithTeam ?? false)
                    {
                        subIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.AssignToIds, tenantId ?? string.Empty)).ToList();
                    }
                    else
                    {
                        subIds = request?.AssignToIds ?? new List<Guid>();
                    }
                }
                else
                {
                    subIds = (await _dapperRepository.GetSubordinateIdsAsync(userId, tenantId ?? string.Empty, request?.CanAccessAllLeads))?.ToList() ?? new();
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "V2GetLeadCategoryRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
            if (request.IsDualOwnershipEnabled == null)
            {
                request.IsDualOwnershipEnabled = await _dapperRepository.GetDualOwnershipDetails(tenantId ?? string.Empty);
            }
            var statuses = await _statuses.ListAsync(cancellationToken);
            var leads = await _efLeadRepository.GetAllCategoryLeadsForMobileAsync(request.Adapt<V3GetAllLeadsRequest>(), userId, subIds, statuses);
            var totalCount = await _efLeadRepository.GetAllCategoryLeadsCountForMobileAsync(request.Adapt<V3GetAllLeadsRequest>(), userId, subIds, statuses);
            V3CategoryDto categoryDto = new()
            {
                Leads = leads.Adapt<List<V3LeadDto>>(),
                TotalCount = totalCount,
                LeadFilter = request?.FilterType ?? LeadFilterTypeMobile.All
            };
            return new Response<V3CategoryDto>(categoryDto);
        }
    }
}
