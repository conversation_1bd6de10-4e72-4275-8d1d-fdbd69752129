﻿using Lrb.Application.Agency.Mobile;
using Lrb.Application.ChannelPartner.Mobile.Dtos;
using Lrb.Application.DataCallLogs.Mobile.Dtos;
using Lrb.Application.Lead.Mobile;
using Lrb.Application.Property.Mobile;
using Lrb.Application.Team.Mobile;

namespace Lrb.Application.DataManagement.Mobile.Dtos
{
    public class UpdateProspectDto : CreateProspectDto
    {
        public Guid StatusId { get; set; }
    }

    public class CreateProspectDto : ProspectDto
    {
        public CreateProspectEnquiryDto? Enquiry { get; set; }
        public List<string>? PropertiesList { get; set; }
        public List<string>? ProjectsList { get; set; }
        public List<string>? ChannelPartnerList { get; set; }
        public List<string>? AgencyList { get; set; }
    }

    public class ViewProspectDto : ProspectDto
    {
        public DateTime? LastModifiedOn { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid LastModifiedBy { get; set; }
        public UserDto? CreatedByUser { get; set; }
        public UserDto? LastModifiedUser { get; set; }
        public UserDto? AssignedUser { get; set; }
        public UserDto? AssignedFromUser { get; set; }
        public UserDto? SourcingManagerUser { get; set; }
        public UserDto? ClosingManagerUser { get; set; }
        public List<PropertyDto>? Properties { get; set; }
        public ViewProspectEnquiryDto? Enquiry { get; set; }
        public List<ChannelPartnerDto>? ChannelPartners { get; set; }
        public List<ProjectDto>? Projects { get; set; }
        public CustomProspectStatusDto? Status { get; set; }
        public Dictionary<ContactType, int>? ContactRecords { get; set; }
        public List<ProspectCallLogDto>? ProspectCallLogs { get; set; }
        public Dictionary<int, Dictionary<int, Dictionary<DateTime, string>>>? CallRecordingUrls { get; set; }
    }


    public class ProspectDto : IDto
    {
        public Guid Id { get; set; }
        public string? Name { get; set; }
        public string? ContactNo { get; set; }
        public string? AlternateContactNo { get; set; }
        public string? Email { get; set; }
        public string? Notes { get; set; }
        public Guid? AssignTo { get; set; }
        public Guid? AssignedFrom { get; set; }
        public string? AgencyName { get; set; }
        public string? CompanyName { get; set; }
        public DateTime? PossesionDate { get; set; }
        public Profession Profession { get; set; }
        public Guid? ClosingManager { get; set; }
        public Guid? SourcingManager { get; set; }
        public AddressDto? AddressDto { get; set; }
        public string? ExecutiveName { get; set; }
        public string? ExecutiveContactNo { get; set; }
        public DateTime? ScheduleDate { get; set; }
        public bool IsQualified { get; set; }
        public string? Designation { get; set; }
        public DateTime? QualifiedDate { get; set; }
        public DateTime? ConvertedDate { get; set; }
        public bool IsConvertedToLead { get; set; }
        public List<AgencyDto>? Agencies { get; set; }
        public string? ReferralName { get; set; }
        public string? ReferralContactNo { get; set; }
        public string? ReferralEmail { get; set; }
        public string? Nationality { get; set; }
        public List<CampaignDto>? Campaigns { get; set; }
        public LeadAssignmentType? AssignmentType { get; set; }
        public string? LandLine {  get; set; }  
        public string? CountryCode { get; set; }
        public string? AltCountryCode { get; set; }
        public Gender? Gender { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public MaritalStatusType? MaritalStatus { get; set; }
        public DateTime? AnniversaryDate { get; set; }

    }
}
