﻿using Lrb.Application.Common.BlobStorage;

namespace Lrb.Application.Integration.Web
{
    public class GetIntegrationAccountInfoRequest : IRequest<Response<string>>
    {
        public Guid Id { get; set; }
        public GetIntegrationAccountInfoRequest(Guid id)
        {
            Id = id;
        }
    }
    public class GetIntegrationAccountInfoRequestHandler : IRequestHandler<GetIntegrationAccountInfoRequest, Response<string>>
    {
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationRepo;
        private readonly IBlobStorageService _blobStorageService;
        private readonly ICurrentUser _currentUser;

        public GetIntegrationAccountInfoRequestHandler(IRepositoryWithEvents<IntegrationAccountInfo> integrationRepo, IBlobStorageService blobStorageService, ICurrentUser currentUser)
        {
            _integrationRepo = integrationRepo;
            _blobStorageService = blobStorageService;
            _currentUser = currentUser;
        }
        public async Task<Response<string>> Handle(GetIntegrationAccountInfoRequest request, CancellationToken cancellationToken)
        {
            var account = await _integrationRepo.GetByIdAsync(request.Id) ?? throw new NotFoundException("No integration account found by this Id.");
            var tenant = _currentUser.GetTenant() ?? throw new InvalidOperationException("Tenant is not resolved.");
            if (string.IsNullOrWhiteSpace(account.FileUrl))
            {
                account.ApiKey = string.IsNullOrWhiteSpace(account.ApiKey) ? ApiKeyHelper.GenerateApiKey(account.Id) : account.ApiKey;
                IDictionary<string, string> data = new Dictionary<string, string>(IntegrationTemplateBuilder.CreateIntegrationTemplate(tenant, account));
                byte[] bytes = IntegrationExcelHelper.CreateExcelData(data).ToArray();
                string fileName = $"{tenant}-{account.LeadSource}-{account.Id}-{DateTime.Now.ToString("yyyy_MM_dd-HH_mm_ss")}.xlsx";
                string folder = "Integration";
                string key = await _blobStorageService.UploadObjectAsync(_blobStorageService.BucketName ?? "prd-leadratblack", folder, fileName, bytes);
                account.FileUrl = key;
                await _integrationRepo.UpdateAsync(account);
            }
            string fileUrl = await _blobStorageService.GetPreSignedURL(_blobStorageService?.BucketName ?? "prd-leadratblack", account.FileUrl);
            return new(fileUrl, default!);
        }
    }

}
