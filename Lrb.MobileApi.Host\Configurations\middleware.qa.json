﻿{
  "MiddlewareSettings": {
    "EnableHttpsLogging": false,
    "EnableLocalization": false
  },
  "RateLimitingSettings": {
    "Enabled": true,
    "RequestLimit": 50,
    "TimeWindowSeconds": 1,
    "WindowType": "Sliding",
    "MaxQueueSize": 100,
    "MaxQueueWaitTimeSeconds": 10,
    "TrackByIpAddress": true,
    "TrackByNetwork": false,
    "NetworkMask": 24,
    "TrackByUserId": false,
    "TrackByTenantId": false,
    "CustomIdentifierHeader": null,
    "CleanupIntervalMinutes": 5,
    "IncludeHeaders": true,
    "CustomMessage": "Rate limit exceeded. Please slow down your requests.",
    "StatusCode": 429,
    "ExcludedPaths": [
      "/health*",
      "/api/health*",
      "/admin/host/ping*",
      "api/v1/Integration/Gmail/messages*",
      "api/v1/Integration/facebook/webhook/process*"
    ],
    "WhitelistedIps": [],
    "WhitelistedUserAgents": [],
    "EnableRequestQueuing": false,
    "PersistQueueState": false,
    "RedisConnectionString": null,
    "RedisKeyPrefix": "rate_limit:",
    "UseDistributedCache": false
  }
}