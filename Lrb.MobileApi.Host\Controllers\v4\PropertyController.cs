﻿using Lrb.Application.Property.Mobile;
using Lrb.Application.Property.Mobile.Dtos;
using Lrb.Application.Property.Mobile.Requests;
using Mapster;
using MediatR;

namespace Lrb.MobileApi.Host.Controllers.v4
{
    [Authorize]
    [Route("api/v4/[controller]")]
    [ApiVersionNeutral]
    public class PropertyController : VersionedApiController
    {
        [HttpPost]
        [TenantIdHeader]
        // [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get all property details.", "")]
        public Task<PagedResponse<GetAllPropertyDTO, string>> SearchAsync([FromBody] GetAllPropertyRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpGet("all/listing")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get all property details.", "")]
        public Task<PagedResponse<GetAllPropertyForListingManagementDTO, string>> SearchAsync([FromBody] GetAllPropertyForListingManagementRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpPost("listing/count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get all property count for listing.", "")]
        public Task<Response<GetPropertyCountForListingManagementDto>> CountAsync([FromBody] GetPropertyTopLevelCountForListingManagementRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpPost("v2")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Properties)]
        [OpenApiOperation("Get all property details.", "")]
        public Task<PagedResponse<V2GetAllPropertyDTO, string>> SearchAsync([FromBody] V2GetAllPropertyRequest request)
        {
            return Mediator.Send(request);
        }

    }
}
