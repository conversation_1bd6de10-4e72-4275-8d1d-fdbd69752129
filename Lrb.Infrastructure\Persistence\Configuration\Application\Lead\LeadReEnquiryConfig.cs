﻿using Finbuckle.MultiTenant.EntityFrameworkCore;
using Lrb.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Lrb.Infrastructure.Persistence.Configuration.Application.Lead
{

    public class LeadReEnquiryConfig : IEntityTypeConfiguration<LeadReEnquiry>
    {
        public void Configure(EntityTypeBuilder<LeadReEnquiry> builder)
        {
            builder.IsMultiTenant();
            builder.HasOne(i => i.Lead).WithMany(i => i.ReEnquiries).HasForeignKey(i => i.LeadId);

        }
    }
}
