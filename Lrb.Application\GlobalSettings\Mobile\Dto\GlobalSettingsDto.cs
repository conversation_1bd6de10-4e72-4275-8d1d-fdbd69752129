﻿using Lrb.Application.GlobalSettings.Mobile.Dto;
namespace Lrb.Application.GlobalSettings.Mobile
{
    public class ViewGlobalSettingsDto : BaseGlobalSettingsDto
    {
        public bool? IsIVROutboundEnabled { get; set; }
        public bool? IsVirtualNumberRequiredForOutbound { get; set; }
    }
    public class UpdateGlobalSettingsDto : BaseGlobalSettingsDto
    {
    }
    public class BaseGlobalSettingsDto : IDto
    {
        public NotificationSettings? NotificationSettings { get; set; }
        public CallSettings? CallSettings { get; set; }
        public bool? HasInternationalSupport { get; set; }
        public bool? IsLeadsExportEnabled { get; set; }
        public bool? IsLeadSourceEditable { get; set; }
        public DateTime? DayStartTime { get; set; }
        public DateTime? DayEndTime { get; set; }
        public LeadNotesSetting? LeadNotesSetting { get; set; }
        public bool? IsWhatsAppEnabled { get; set; }
        public bool? IsCallDetectionActivated { get; set; }
        public bool IsMaskedLeadContactNo { get; set; }
        public bool IsMicrositeFeatureEnabled { get; set; }
        public bool IsPropertiesExportEnabled { get; set; }
        public bool IsExportDataEnabled { get; set; }
        public bool? IsDualOwnershipEnabled { get; set; }
        public bool? IsStickyAgentEnabled { get; set; }
        public LeadProjectSetting? LeadProjectSetting { get; set; }
        public LeadPropertySetting? LeadPropertySetting { get; set; }
        public OTPSettings? OTPSettings { get; set; }
        public bool? IsTimeZoneEnabled { get; set; }
        public List<CountryInfoDto>? Countries { get; set; }
        public bool? IsCopyPasteEnabled { get; set; }
        public bool? IsScreenshotEnabled { get; set; }
        public bool IsLeadRotationEnabled { get; set; }
        public bool IsStickyAgentOverriddenEnabled { get; set; }
        public bool? IsCustomStatusEnabled { get; set; }
        public DirectionOfLeadCreation? DirectionOfLeadCreation { get; set; }
        public DirectionOfLeadModification? DirectionOfLeadModification { get; set; }
        public DuplicateLeadFeatureInfo? DuplicateLeadFeatureInfo {  get; set; }
        public bool? ShouldHideDashBoard { get; set; }
        public bool? IsWhatsAppDeepIntegration { get; set; }
        public bool? IsAssignedCallLogsEnabled { get; set; }
        public bool? ShouldEnablePropertyListing { get; set; }
        public bool? CanAccessAnonymousApis {  get; set; }
        public bool? IsCustomLeadFormEnabled { get; set; }
        public bool? IsMobileCallEnabled { get; set; }
        public bool? IsProjectsExportEnabled { get; set; }
        public Dictionary<string, string>? DefaultValues { get; set; }
        public bool? ShouldEnableEnquiryForm { get; set; }
        public bool? ShowMoreMicrositeProperties { get; set; }
        public bool? ShouldRenameSiteVisitColumn { get; set; }
        public bool? IsPastDateSelectionEnabled { get; set; }
        public GeneralSettings? GeneralSettings {  get; set; }
        public bool? IsBrevoEnabled { get; set; }
        public ExportEmailNotificationSettings? ExportEmailNotificationSettings { get; set; }
    }
}
