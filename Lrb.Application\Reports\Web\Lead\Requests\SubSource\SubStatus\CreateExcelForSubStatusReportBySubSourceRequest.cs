﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Reports.Web.Dtos.SourcevsSubSource;
using Lrb.Application.Utils;
using Newtonsoft.Json;

namespace Lrb.Application.Reports.Web
{
    public class CreateExcelForSubStatusReportBySubSourceRequest : IRequest<Response<string>>
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public DateType? DateType { get; set; }
        public bool IsWithTeam { get; set; }
        public List<Guid>? UserIds { get; set; }
        public string? SearchText { get; set; }
        public List<LeadSource>? Sources { get; set; }
        public List<string>? Projects { get; set; }
        public List<string>? SubSources { get; set; }
        public UserStatus? UserStatus { get; set; }
        public ReportPermission? ExportPermission { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = int.MaxValue;
        public List<string>? Localites { get; set; }
        public List<string>? States { get; set; }
        public List<string>? Cities { get; set; }
    }
    public class CreateExcelForSubStatusReportRequestHandler : IRequestHandler<CreateExcelForSubStatusReportBySubSourceRequest, Response<string>>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        private readonly IBlobStorageService _blobStorageService;
        public CreateExcelForSubStatusReportRequestHandler(
            IDapperRepository dapperRepository,
            ICurrentUser currentUser,
            IBlobStorageService blobStorageService)
        {
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
            _blobStorageService = blobStorageService;

        }
        public async Task<Response<string>> Handle(CreateExcelForSubStatusReportBySubSourceRequest request, CancellationToken cancellationToken)
        {
            var tenantId = _currentUser.GetTenant();
            var userId = _currentUser.GetUserId();
            List<Guid> teamUserIds = new();
            List<Guid> permittedUserIds = new();
            var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
            if (isAdmin)
            {
                permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
            }
            else if (request.ExportPermission != null)
            {
                switch (request.ExportPermission)
                {
                    case ReportPermission.All:
                        permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        break;
                    case ReportPermission.Reportees:
                        permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                        break;
                }
            }
            if (request?.UserIds?.Any() ?? false)
            {
                if (request?.IsWithTeam ?? false)
                {
                    teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                }
                else
                {
                    teamUserIds = request?.UserIds ?? new List<Guid>();
                }
            }
            else
            {
                if (!isAdmin)
                {
                    teamUserIds = permittedUserIds;
                }
            }
            if (teamUserIds.Any())
            {
                teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
            }
            else
            {
                teamUserIds = permittedUserIds;
            }
            request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
            request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
            var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<LeadSubStatusBySubSourceDto>("LeadratBlack", "GetLeadSubStatusReportBySubSource", new
            {
                fromdate = request.FromDate,
                todate = request.ToDate,
                datetype = request.DateType,
                tenantid = tenantId,
                userids = teamUserIds,
                searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                sources = request?.Sources?.ConvertAll(i => (int)i),
                projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                userstatus = (request?.UserStatus ?? 0),
                pagesize = request.PageSize,
                pagenumber = request.PageNumber,
            })).ToList();
            res.ForEach(i => i.StatusDtos = JsonConvert.DeserializeObject<List<StatusDto>>(i?.Status ?? string.Empty));
            var groupedResult = res.GroupBy(i => i?.SubSource ?? string.Empty).ToDictionary(i => i.Key, j => j.SelectMany(i => i?.StatusDtos ?? new()).ToList());
            List<LeadSubStatusReportBySubSourceDto> subStatusBySubSourceDtos = new();
            foreach (var group in groupedResult)
            {
                subStatusBySubSourceDtos.Add(new LeadSubStatusReportBySubSourceDto()
                {
                    SubSource = group.Key,
                    All = group.Value?.Sum(i => i.Count) ?? 0,
                    Active = group.Value?.Where(i => i.BaseStatus != "dropped" && i.BaseStatus != "not_interested" && i.BaseStatus != "booked" && i.BaseStatus != "booking_cancel").Sum(i => i.Count) ?? 0,
                    Overdue = group.Value?.Sum(i => i.OverdueCount) ?? 0,
                    Callback = group.Value?.Where(i => i.BaseStatus == "callback" && i.SubStatus != "callback")?.Sum(i => i.Count) ?? 0,
                    Busy = group.Value?.Where(i => i.SubStatus == "busy")?.Sum(i => i.Count) ?? 0,
                    ToScheduleAMeeting = group.Value?.Where(i => i.SubStatus == "to_schedule_a_meeting")?.Sum(i => i.Count) ?? 0,
                    FollowUp = group.Value?.Where(i => i.SubStatus == "follow_up")?.Sum(i => i.Count) ?? 0,
                    ToScheduleSiteVisit = group.Value?.Where(i => i.SubStatus == "to_schedule_site_visit")?.Sum(i => i.Count) ?? 0,
                    PlanPostponed = group.Value?.Where(i => i.SubStatus == "plan_postponed")?.Sum(i => i.Count) ?? 0,
                    NeedMoreInfo = group.Value?.Where(i => i.SubStatus == "need_more_info")?.Sum(i => i.Count) ?? 0,
                    NotAnswered = group.Value?.Where(i => i.SubStatus == "not_answered")?.Sum(i => i.Count) ?? 0,
                    NotReachable = group.Value?.Where(i => i.SubStatus == "not_reachable")?.Sum(i => i.Count) ?? 0,
                    Dropped = group.Value?.Where(i => i.BaseStatus == "dropped" && i.SubStatus != "dropped")?.Sum(i => i.Count) ?? 0,
                    NotLooking = group.Value?.Where(i => i.SubStatus == "not_looking")?.Sum(i => i.Count) ?? 0,
                    RingingNotReceived = group.Value?.Where(i => i.SubStatus == "ringing_not_received")?.Sum(i => i.Count) ?? 0,
                    WrongOrInvalidNo = group.Value?.Where(i => i.SubStatus == "wrong/invalid_no.")?.Sum(i => i.Count) ?? 0,
                    PurchasedFromOthers = group.Value?.Where(i => i.SubStatus == "purchased_from_others")?.Sum(i => i.Count) ?? 0,
                    MeetingScheduled = group.Value?.Where(i => i.BaseStatus == "meeting_scheduled" && i.SubStatus != "meeting_scheduled")?.Sum(i => i.Count) ?? 0,
                    OnCall = group.Value?.Where(i => i.SubStatus == "on_call")?.Sum(i => i.Count) ?? 0,
                    Online = group.Value?.Where(i => i.SubStatus == "online")?.Sum(i => i.Count) ?? 0,
                    InPerson = group.Value?.Where(i => i.SubStatus == "in_person")?.Sum(i => i.Count) ?? 0,
                    Others = group.Value?.Where(i => i.SubStatus == "others")?.Sum(i => i.Count) ?? 0,
                    NotInterested = group.Value?.Where(i => i.BaseStatus == "not_interested" && i.SubStatus != "not_interested")?.Sum(i => i.Count) ?? 0,
                    DifferentLocation = group.Value?.Where(i => i.SubStatus == "different_location")?.Sum(i => i.Count) ?? 0,
                    DifferentRequirements = group.Value?.Where(i => i.SubStatus == "different_requirements")?.Sum(i => i.Count) ?? 0,
                    UnmatchedBudget = group.Value?.Where(i => i.SubStatus == "unmatched_budget")?.Sum(i => i.Count) ?? 0,
                    SiteVisitScheduled = group.Value?.Where(i => i.BaseStatus == "site_visit_scheduled" && i.SubStatus != "site_visit_scheduled")?.Sum(i => i.Count) ?? 0,
                    FirstVisit = group.Value?.Where(i => i.SubStatus == "first_visit")?.Sum(i => i.Count) ?? 0,
                    ReVisit = group.Value?.Where(i => i.SubStatus == "revisit")?.Sum(i => i.Count) ?? 0,
                    Pending = group.Value?.FirstOrDefault(i => i.BaseStatus == "pending")?.Count ?? 0,
                    Booked = group.Value?.Where(i => i.BaseStatus == "booked")?.Sum(i => i.Count) ?? 0,
                    New = group.Value?.FirstOrDefault(i => i.BaseStatus == "new")?.Count ?? 0,
                    SiteVisitDoneCount = group.Value?.Sum(i => i.SiteVisitDoneCount) ?? 0,
                    SiteVisitDoneUniqueCount = group.Value?.Sum(i => i.SiteVisitDoneUniqueCount) ?? 0,
                    SiteVisitNotDoneCount = group.Value?.Sum(i => i.SiteVisitNotDoneCount) ?? 0,
                    SiteVisitNotDoneUniqueCount = group.Value?.Sum(i => i.SiteVisitNotDoneUniqueCount) ?? 0,
                    MeetingDoneCount = group.Value?.Sum(i => i.MeetingDoneCount) ?? 0,
                    MeetingDoneUniqueCount = group.Value?.Sum(i => i.MeetingDoneUniqueCount) ?? 0,
                    MeetingNotDoneCount = group.Value?.Sum(i => i.MeetingNotDoneCount) ?? 0,
                    MeetingNotDoneUniqueCount = group.Value?.Sum(i => i.MeetingNotDoneUniqueCount) ?? 0,
                    BookingCancel = group.Value?.Where(i => i.BaseStatus == "booking_cancel")?.Sum(i => i.Count) ?? 0,
                    ExpressionOfInterestCount = group.Value?.Where(i => i.BaseStatus == "expression_of_interest")?.Sum(i => i.Count) ?? 0
                });
            }
            var dtos = subStatusBySubSourceDtos?.Adapt<List<SubStatusBySubSourceFormattedDto>>().ToList();

            /*            var fileBytes = ExcelGeneration<SubStatusBySubSourceFormattedDto>.GenerateExcel(dtos, "Visit and Meeting Reports ");
            */
            var fileBytes = ExcelHelper.CreateExcelFromListEPPlus(subStatusBySubSourceDtos, new(), new()).ToArray();
            var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", $"Reports/{tenantId ?? "Default"}", $"Report-{DateTime.Now}.xlsx", fileBytes);
            var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
            return new(presignedUrl);
        }
    }
}
