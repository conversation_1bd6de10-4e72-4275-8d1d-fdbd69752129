﻿using Lrb.Application.Common.Persistence;
using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.Project.Web.Requests;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using Lrb.Infrastructure.Persistence.Context;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Serilog;

namespace Lrb.Infrastructure.Persistence.Repository.New_Implementation
{
    public partial class ProjectRepository
    {
        public async Task<IEnumerable<Lrb.Domain.Entities.Project>> GetAllProjectsForMobileNewV2Async(Lrb.Application.Project.Mobile.GetAllProjectRequest request, List<Guid> projectIds)
        {
            var query = BuildQueryForMobileV2(request, projectIds);
            try
            {
                var projects = await query.Skip(request.PageSize * (request.PageNumber - 1))
                .Take(request.PageSize)
     .Where(i => !i.IsDeleted && !i.IsArchived)
     .Select(i => new Project
     {
         Id = i.Id,
         Name = i.Name,
         Area = i.Area,
         AreaUnitId = i.AreaUnitId,
         Status = i.Status,
         CurrentStatus = i.CurrentStatus,
         SerialNo = i.SerialNo,
         MinimumPrice = i.MinimumPrice,
         MaximumPrice = i.MaximumPrice,
         CreatedBy = i.CreatedBy,
         CreatedOn = i.CreatedOn,
         LastModifiedBy = i.LastModifiedBy,
         LastModifiedOn = i.LastModifiedOn,
         ShortUrl = i.ShortUrl,
         ProjectGalleries = i.ProjectGalleries != null ? i.ProjectGalleries
             .Where(g => !g.IsDeleted)
             .Select(g => new ProjectGallery
             {
                 Id = g.Id,
                 Name = g.Name,
                 ImageFilePath = g.ImageFilePath,
                 ImageKey = g.ImageKey,
                 IsCoverImage = g.IsCoverImage,
                 GalleryType = g.GalleryType,
             }).ToList() : null,
         ProjectType = i.ProjectType != null ? new MasterProjectType
         {
             Id = i.ProjectType.Id,
             DisplayName = i.ProjectType.DisplayName,

         } : null,
         Address = i.Address != null ? new Address
         {
             Id = i.Address.Id,
             PlaceId = i.Address.PlaceId,
             SubLocality = i.Address.SubLocality,
             Locality = i.Address.Locality,
             District = i.Address.District,
             City = i.Address.City,
             State = i.Address.State,
             Country = i.Address.Country,
             Community = i.Address.Community,
             SubCommunity = i.Address.SubCommunity,
             TowerName = i.Address.TowerName,
             Location = i.Address.Location != null ? new Location
             {
                 Id = i.Address.Location.Id,
                 Locality = i.Address.Location.Locality,
                 District = i.Address.Location.District,

                 Zone = i.Address.Location.Zone != null ? new Zone
                 {
                     Name = i.Address.Location.Zone.Name
                 } : null,
                 City = i.Address.Location.City != null ? new City
                 {
                     Name = i.Address.Location.City.Name
                 } : null
             } : null
         } : null,
         BuilderDetail = i.BuilderDetail != null ? new ProjectBuilderDetails
         {
             Id = i.BuilderDetail.Id,
             Name = i.BuilderDetail.Name,
         } : null
     }).AsNoTracking().AsQueryable().ToListAsync();
                return projects;
            }
            catch (Exception e)
            {
                throw;
            }
        }
        private IQueryable<Lrb.Domain.Entities.Project> BuildQueryForMobileV2(Lrb.Application.Project.Mobile.GetAllProjectRequest request, List<Guid> projectIds)
        {
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            if ((request.MinLeadCount != null || request.MaxLeadCount != null ||
                 request.MinProspectCount != null || request.MaxProspectCount != null)
                && (projectIds?.Any() != true))
            {
                return context.Projects.Where(_ => false);
            }
            IQueryable<Lrb.Domain.Entities.Project>? query = null;
            query = context.Projects.Where(i => !i.IsDeleted)
             .OrderBy(i => i.CurrentStatus)
             .ThenByDescending(i => i.LastModifiedOn - new DateTime(2000, 01, 01, 00, 00, 00, DateTimeKind.Utc)).AsQueryable();
            switch (request.ProjectVisiblity)
            {
                case Lrb.Application.Project.Mobile.ProjectVisiblityType.All:
                    query = query.Where(i => !i.IsArchived);
                    break;

                case Lrb.Application.Project.Mobile.ProjectVisiblityType.Residential:
                    query = query.Where(i => !i.IsArchived).Where(j => (j.ProjectType.Id == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c")) ||
                                                               (j.ProjectType.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c")));
                    break;

                case Lrb.Application.Project.Mobile.ProjectVisiblityType.Commercial:
                    query = query.Where(i => !i.IsArchived).Where(j => (j.ProjectType.Id == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573")) ||
                                                               (j.ProjectType.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573")));
                    break;

                case Lrb.Application.Project.Mobile.ProjectVisiblityType.Agriculture:
                    query = query.Where(i => !i.IsArchived).Where(j => (j.ProjectType.Id == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8")) ||
                                                               (j.ProjectType.BaseId == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8")));
                    break;

                case Lrb.Application.Project.Mobile.ProjectVisiblityType.Deleted:
                    query = query.Where(i => i.IsArchived);
                    break;
            }

            if (!string.IsNullOrWhiteSpace(request.Search))
            {
                request.Search = request.Search.ToLower().Trim();

                query = query.Where(i => (i.Name.ToLower().Trim().Contains(request.Search)) ||
                    (i.Certificates.ToLower().Trim().Contains(request.Search)) || (i.Address.SubLocality.ToLower().Trim().Contains(request.Search)) ||
                    (i.Address.City.ToLower().Trim().Contains(request.Search)) || (i.Address.Locality.ToLower().Trim().Contains(request.Search)) ||
                    (i.Address.State.ToLower().Trim().Contains(request.Search)) || (i.Address.Country.ToLower().Trim().Contains(request.Search)) ||
                    (i.Address.PostalCode.ToLower().Trim().Contains(request.Search)) || (i.ProjectType.DisplayName.ToLower().Trim().Contains(request.Search)) ||
                    (i.SerialNo.ToLower().Contains(request.Search)));
            }

            if (request.ProjectStatus != null)
            {
                query = query.Where(i => i.Status == request.ProjectStatus);
            }

            if (request.CurrentStatus != null)
            {
                query = query.Where(i => i.CurrentStatus == request.CurrentStatus);
            }

            if (request.ProjectType?.Any() ?? false)
            {
                query = query.Where(i => request.ProjectType.Contains(i.ProjectType.Id) || request.ProjectType.Contains(i.ProjectType.BaseId ?? Guid.Empty));
            }

            if (request.ProjectSubType?.Any() ?? false)
            {
                query = query.Where(i => request.ProjectSubType.Contains(i.ProjectType.Id));
            }

            if ((request.FromDate != null && request.FromDate != default) || (request.ToDate != null && request.ToDate != default))
            {
                DateTime? tempToDate = request?.ToDate?.ConvertToDateToUtc();
                DateTime? tempFromDate = request?.FromDate?.ConvertFromDateToUtc();

                if ((request?.FromDate != null && request.FromDate != default) && (request.ToDate != null && request.ToDate != default))
                {
                    query = query.Where(i => i.StartDate >= tempFromDate && i.EndDate <= tempToDate);
                }
                else if ((request?.FromDate != null && request.FromDate != default) && (request.ToDate == null && request.ToDate == default))
                {
                    query = query.Where(i => i.StartDate >= tempFromDate);
                }
                else if ((request?.FromDate == null && request?.FromDate == default) && (request?.ToDate != null && request.ToDate != default))
                {
                    query = query.Where(i => i.EndDate <= tempToDate);
                }
            }
            if (request?.Locations != null && request.Locations.Any())
            {
                request.Locations = request.Locations.ConvertAll(i => i.Replace(",", "").ToLower().Trim().Replace(" ", "")).ToList();
                query = query.Where(i => request.Locations.Contains((i.Address.SubLocality + "" +
                   i.Address.Locality + "" +
                   i.Address.Community + "" +
                   i.Address.SubCommunity + "" +
                   i.Address.TowerName + "" +
                   i.Address.District + "" +
                   i.Address.City + "" +
                   i.Address.State + "" +
                   i.Address.Country + "" +
                   i.Address.PostalCode + "").ToLower().Trim().Replace(" ", "")));
            }

            if (request?.Possesion != null)
            {
                switch (request?.Possesion)
                {
                    case PossesionType.UnderConstruction:
                        query = query.Where(i => i.PossesionType == PossesionType.UnderConstruction);
                        break;

                    case PossesionType.SixMonth:
                        query = query.Where(i => i.PossesionType == PossesionType.SixMonth);
                        break;

                    case PossesionType.Year:
                        query = query.Where(i => i.PossesionType == PossesionType.Year);
                        break;

                    case PossesionType.TwoYears:
                        query = query.Where(i => i.PossesionType == PossesionType.TwoYears);
                        break;

                    case PossesionType.CustomDate:
                        DateTime? tempToPossesionDate = request?.ToPossesionDate?.ConvertToDateToUtc();
                        DateTime? tempFrompossesionDate = request?.FromPossesionDate?.ConvertFromDateToUtc();
                        query = query.Where(i => i.PossessionDate >= tempFrompossesionDate && i.PossessionDate <= tempToPossesionDate);
                        break;
                }
            }

            if (request?.BuilderName?.Any() ?? false)
            {
                query = query.Where(i => i.BuilderDetail != null && request.BuilderName.Contains(i.BuilderDetail.Name));
            }

            if (request?.Facing != null)
            {
                switch (request.Facing)
                {
                    case Facing.North:
                        query = query.Where(i => i.Facing == Facing.North);
                        break;

                    case Facing.East:
                        query = query.Where(i => i.Facing == Facing.East);
                        break;
                    case Facing.West:
                        query = query.Where(i => i.Facing == Facing.West);
                        break;

                    case Facing.South:
                        query = query.Where(i => i.Facing == Facing.South);
                        break;

                    case Facing.SouthEast:
                        query = query.Where(i => i.Facing == Facing.SouthEast);
                        break;

                    case Facing.SouthWest:
                        query = query.Where(i => i.Facing == Facing.SouthWest);
                        break;

                    case Facing.NorthEast:
                        query = query.Where(i => i.Facing == Facing.NorthEast);
                        break;

                    case Facing.NorthWest:
                        query = query.Where(i => i.Facing == Facing.NorthWest);
                        break;
                }
            }

            if (request?.Facings != null)
            {
                query = query.Where(i => i.Facings != null && i.Facings.Any(i => request.Facings.Contains(i)));
            }

            if (request?.Budgets != null && request.Budgets.Any())
            {
                foreach (var budget in request.Budgets)
                {
                    switch (budget)
                    {
                        case Budget.UpToTenLakhs:
                            query = query.Where(i => i.MaximumPrice != null && i.MaximumPrice <= 1000000);
                            break;
                        case Budget.TenToTwentyLakhs:
                            query = query.Where(i => i.MaximumPrice != null && i.MaximumPrice > 1000000 && i.MaximumPrice <= 2000000);
                            break;
                        case Budget.TwentyToThirtyLakhs:
                            query = query.Where(i => i.MaximumPrice != null && i.MaximumPrice > 2000000 && i.MaximumPrice <= 3000000);
                            break;
                        case Budget.ThirtyToFourtyLakhs:
                            query = query.Where(i => i.MaximumPrice != null && i.MaximumPrice > 3000000 && i.MaximumPrice <= 4000000);
                            break;
                        case Budget.FourtyToFiftyLakhs:
                            query = query.Where(i => i.MaximumPrice != null && i.MaximumPrice > 4000000 && i.MaximumPrice <= 5000000);
                            break;
                        case Budget.FiftyToOneCrore:
                            query = query.Where(i => i.MaximumPrice != null && i.MaximumPrice > 5000000 && i.MaximumPrice <= 10000000);
                            break;
                        case Budget.MoreThanOneCrore:
                            query = query.Where(i => i.MaximumPrice != null && i.MaximumPrice > 10000000);
                            break;
                    }
                }
            }

            if (request?.AmenitesIds?.Any() ?? false)
            {
                query = query.Where(i => (i.Amenities != null && i.Amenities.Any(i => request.AmenitesIds.Contains(i.MasterProjectAmenityId))));
            }
            if (request?.Currency != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.Currency == request.Currency);
            }
            if (request.MinPrice != null || request.MaxPrice != null)
            {
                if (request.MinPrice != null && request.MaxPrice != null)
                {
                    query = query.Where(i => i.MinimumPrice >= request.MinPrice && i.MaximumPrice <= request.MaxPrice);
                }
                else if (request.MinPrice != null && request.MaxPrice == null)
                {
                    query = query.Where(i => i.MinimumPrice >= request.MinPrice);
                }
                else if (request.MinPrice == null && request.MaxPrice != null)
                {
                    query = query.Where(i => i.MaximumPrice <= request.MaxPrice);
                }
            }

            if (request.FromMinPrice != null || request.ToMinPrice != null || request.FromMaxPrice != null || request.ToMaxPrice != null)
            {
                if (request.FromMinPrice != null && request.ToMinPrice != null)
                {
                    query = query.Where(i => i.MinimumPrice >= request.FromMinPrice && i.MinimumPrice <= request.ToMinPrice);
                }
                else if (request.FromMinPrice != null && request.ToMinPrice == null)
                {
                    query = query.Where(i => i.MinimumPrice >= request.FromMinPrice);
                }
                else if (request.FromMinPrice == null && request.ToMinPrice != null)
                {
                    query = query.Where(i => i.MinimumPrice <= request.ToMinPrice);
                }

                if (request.FromMaxPrice != null && request.ToMaxPrice != null)
                {
                    query = query.Where(i => i.MaximumPrice >= request.FromMaxPrice && i.MaximumPrice <= request.ToMaxPrice);
                }
                else if (request.FromMaxPrice != null && request.ToMaxPrice == null)
                {
                    query = query.Where(i => i.MaximumPrice >= request.FromMaxPrice);
                }
                else if (request.FromMaxPrice == null && request.ToMaxPrice != null)
                {
                    query = query.Where(i => i.MaximumPrice <= request.ToMaxPrice);
                }
            }
            if ((request.MinCarpetArea != null || request.MaxCarpetArea != null) && request.CarpetAreaUnitId != default)
            {
                query = query.Where(i => i.AreaUnitId != null
                    && (request.MinCarpetArea == null || i.Area >= request.MinCarpetArea)
                    && (request.MaxCarpetArea == null || i.Area <= request.MaxCarpetArea)
                    && i.AreaUnitId == request.CarpetAreaUnitId);
            }

            else if (request.MinCarpetArea != null || request.MaxCarpetArea != null)
            {
                query = query.Where(i =>
                    (request.MinCarpetArea == null || i.Area >= request.MinCarpetArea) &&
                    (request.MaxCarpetArea == null || i.Area <= request.MaxCarpetArea));
            }

            if (projectIds?.Any() ?? false)
            {
                query = query.Where(i => projectIds.Contains(i.Id));
            }
            return query;
        }
    }
}
