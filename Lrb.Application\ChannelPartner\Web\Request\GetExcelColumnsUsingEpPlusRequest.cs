﻿using Lrb.Application.ChannelPartner.Web.Dtos;
using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.Utils;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using Serilog;

namespace Lrb.Application.ChannelPartner.Web.Request
{
    public class GetExcelColumnsUsingEpPlusRequest : IRequest<Response<FileColumnDto>>
    {
        public IFormFile? File { get; set; }
        public GetExcelColumnsUsingEpPlusRequest(IFormFile? file)
        {
            File = file;
        }
    }
    public class GetExcelColumnsUsingEpPlusRequestHandler : IRequestHandler<GetExcelColumnsUsingEpPlusRequest, Response<FileColumnDto>>
    {
        private readonly IBlobStorageService _blobStorageService;
        private readonly ILogger _logger;
        public GetExcelColumnsUsingEpPlusRequestHandler(IBlobStorageService blobStorageService, ILogger logger)
        {
            _blobStorageService = blobStorageService;
            _logger = logger;
        }

        public async Task<Response<FileColumnDto>> Handle(GetExcelColumnsUsingEpPlusRequest request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.Information("GetExcelColumnsUsingEPPlusRequestHandler -> Request: " + JsonConvert.SerializeObject(request));
                var file = request.File;
                if (file == null) { throw new ArgumentNullException(nameof(file)); }
                string fileExtension = string.Empty;
                if (!ExcelHelper.IsValidFile(file, out fileExtension))
                {
                    throw new InvalidOperationException("File format is invalid");
                }
                var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", "Lead", file);
                List<string> columns = new List<string>();
                Dictionary<string, List<string>> multiSheetColumns = new();
                if (key.Split('.').LastOrDefault() == "csv")
                {
                    columns = await CSVHelper.GetCSVColumns(file);
                    multiSheetColumns.Add("Default", columns);
                }
                else
                {
                    columns = EPPlusExcelHelper.GetFileColumns(file);
                    multiSheetColumns = EPPlusExcelHelper.GetFileColumnsOfMultiSheets(file);
                }
                FileColumnDto excelColumnsViewModel = new()
                {
                    S3BucketKey = key,
                    ColumnNames = columns,
                    MultiSheetColumnNames = multiSheetColumns,
                };
                return new Response<FileColumnDto>(excelColumnsViewModel);
            }
            catch (Exception ex)
            {
                _logger.Error("GetExcelColumnsUsingEPPlusRequestHandler -> Error: " + JsonConvert.SerializeObject(ex));
                throw;
            }
        }
    }
}
