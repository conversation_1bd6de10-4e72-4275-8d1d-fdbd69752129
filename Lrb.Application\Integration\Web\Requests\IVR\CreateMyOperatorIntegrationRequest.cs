﻿using Lrb.Application.Common.BlobStorage;

namespace Lrb.Application.Integration.Web.Requests
{
    public class CreateMyOperatorIntegrationRequest : IRequest<Response<string>>
    {
        public string? AccountName { get; set; }
        public LeadSource Source { get; set; }
        public IVRType CallType { get; set; }
        public Dictionary<string, string>? Credentials { get; set; }
    }
    public class CreateMyOperatorIntegrationRequestHandler : IRequestHandler<CreateMyOperatorIntegrationRequest, Response<string>>
    {
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccountInfoRepositoryAsync;
        private readonly IBlobStorageService _blobStorageService;
        private readonly ICurrentUser _currentUser;
        public CreateMyOperatorIntegrationRequestHandler(IRepositoryWithEvents<IntegrationAccountInfo> integrationAccountInfoRepositoryAsync,
            ICurrentUser currentUser,
            IBlobStorageService blobStorageService)
        {
            _integrationAccountInfoRepositoryAsync = integrationAccountInfoRepositoryAsync;
            _blobStorageService = blobStorageService;
            _currentUser = currentUser;
        }
        public async Task<Response<string>> Handle(CreateMyOperatorIntegrationRequest request, CancellationToken cancellationToken)
        {
            request.Source = LeadSource.IVR;
            var tenant = _currentUser.GetTenant();
            Guid userId = Guid.NewGuid();
            IntegrationAccountInfo? integrationAccount = null;
            IDictionary<string, string> data = null;
            integrationAccount = CreateIntegrationEntity(request, userId);
            var apiKey = ApiKeyHelper.GenerateApiKey(integrationAccount.Id);
            integrationAccount.ApiKey = apiKey;
            data = new Dictionary<string, string>(IntegrationTemplateBuilder.CreateIntegrationTemplate(tenant, integrationAccount, request.CallType, IVRServiceProvider.MyOperator));
            await _integrationAccountInfoRepositoryAsync.AddAsync(integrationAccount);
            string key = string.Empty;
            byte[] bytes = IntegrationExcelHelper.CreateExcelData(data).ToArray();
            string fileName = $"{tenant}-{integrationAccount.Id}-{DateTime.Now.ToString("yyyy_MM_dd-HH_mm_ss")}.xlsx";
            string folder = "Integration";
            key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", folder, fileName, bytes);
            string fileUrl = await _blobStorageService.GetPreSignedURL(_blobStorageService?.BucketName ?? "prd-leadratblack", key);
            integrationAccount.FileUrl = key;
            await _integrationAccountInfoRepositoryAsync.UpdateAsync(integrationAccount);
            return new(fileUrl, default);
        }
        private IntegrationAccountInfo CreateIntegrationEntity(CreateMyOperatorIntegrationRequest command, Guid userId)
        {
            return new IntegrationAccountInfo()
            {
                Id = Guid.NewGuid(),
                AccountName = command.AccountName,
                LeadSource = command.Source,
                LicenseId = Guid.NewGuid(),
                JsonTemplate = IntegrationTemplateBuilder.GetRequestBodyJsonFromFile(command.Source, IVRServiceProvider.MyOperator),
                CreatedBy = userId,
                Credentials = command.Credentials
            };
        }
    }
}
