﻿using Lrb.Application.Automation.Dtos;

namespace Lrb.Application.ZonewiseLocation.Web.Dtos
{
    public class LocationDto : IDto
    {
        public Guid Id { get; set; }
        public string? Locality { get; set; }

        public string? District { get; set; }

        public string? PostalCode { get; set; }

        public string? Longitude { get; set; }

        public string? Latitude { get; set; }

        public bool IsGoogleMapLocation { get; set; }

        public ZoneDto? Zone { get; set; }

        public CityDto? City { get; set; }

        public StateDto? State { get; set; }

        public CountryDto? Country { get; set; }

        public Guid CreatedBy { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid LastModifiedBy { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public DateTime? DeletedOn { get; set; }
        public Guid? DeletedBy { get; set; }
        public List<Guid>? UserIds { get; set; }
    }
    public class InvalidLocationDto : IDto
    {
        public string? Locality { get; set; }

        public string? District { get; set; }

        public string? PostalCode { get; set; }

        public string? Longitude { get; set; }

        public string? Latitude { get; set; }

        public string? Zone { get; set; }

        public string? City { get; set; }

        public string? State { get; set; }

        public string? Country { get; set; }

        public string? Errors { get; set; }
    }
    public class LocationLeadDto : IDto
    {
        public Guid Id { get; set; }
        public string? Location { get; set; }
        public string? PlaceId { get; set; }
    }
    public class LocationWithUserAssignmentDto : IDto
    {
        public Guid Id { get; set; }
        public string? Locality { get; set; }
        public ViewUserAssignmentDto? UserAssignment { get; set; }
    }
    public class CreateLocationDto : IDto
    {
        public string Locality { get; set; } = default!;

        public string? PlaceId { get; set; }

        public string? District { get; set; }

        public string? PostalCode { get; set; }

        public string? Longitude { get; set; }

        public string? Latitude { get; set; }

        //Parameters to find by names
        public string? City { get; set; }

        public string? State { get; set; }

        public string? Country { get; set; }

        public string? Zone { get; set; }

        //Parameters to find by Ids
        public Guid? CityId { get; set; }

        public Guid? StateId { get; set; }

        public Guid? CountryId { get; set; }

        public Guid? ZoneId { get; set; }

        public bool IsGoogleMapLocation { get; set; }
    }
}
