using System.ComponentModel.DataAnnotations;

namespace Lrb.Infrastructure.RateLimiting.Configuration;

/// <summary>
/// Configuration settings for rate limiting middleware
/// </summary>
public class RateLimitingSettings
{
    /// <summary>
    /// Whether rate limiting is enabled
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// Maximum number of requests allowed per time window
    /// </summary>
    [Range(1, int.MaxValue, ErrorMessage = "RequestLimit must be greater than 0")]
    public int RequestLimit { get; set; }

    /// <summary>
    /// Time window in seconds for rate limiting
    /// </summary>
    [Range(1, int.MaxValue, ErrorMessage = "TimeWindowSeconds must be greater than 0")]
    public int TimeWindowSeconds { get; set; }

    /// <summary>
    /// Type of time window (Fixed or Sliding)
    /// </summary>
    public TimeWindowType WindowType { get; set; }

    /// <summary>
    /// Maximum number of requests that can be queued when rate limit is exceeded
    /// </summary>
    [Range(0, int.MaxValue, ErrorMessage = "MaxQueueSize cannot be negative")]
    public int MaxQueueSize { get; set; }

    /// <summary>
    /// Maximum time in seconds a request can wait in the queue
    /// </summary>
    [Range(1, int.MaxValue, ErrorMessage = "MaxQueueWaitTimeSeconds must be greater than 0")]
    public int MaxQueueWaitTimeSeconds { get; set; }

    /// <summary>
    /// Whether to track by IP address
    /// </summary>
    public bool TrackByIpAddress { get; set; }

    /// <summary>
    /// Whether to track by network/subnet (CIDR notation)
    /// </summary>
    public bool TrackByNetwork { get; set; }

    /// <summary>
    /// Network mask for subnet tracking (e.g., 24 for /24 subnet)
    /// </summary>
    [Range(8, 32, ErrorMessage = "NetworkMask must be between 8 and 32")]
    public int NetworkMask { get; set; }

    /// <summary>
    /// Whether to track by user ID (requires authentication)
    /// </summary>
    public bool TrackByUserId { get; set; }

    /// <summary>
    /// Whether to track by tenant ID
    /// </summary>
    public bool TrackByTenantId { get; set; }

    /// <summary>
    /// Custom header name to use for client identification
    /// </summary>
    public string? CustomIdentifierHeader { get; set; }

    /// <summary>
    /// Cleanup interval in minutes for expired entries
    /// </summary>
    [Range(1, int.MaxValue, ErrorMessage = "CleanupIntervalMinutes must be greater than 0")]
    public int CleanupIntervalMinutes { get; set; }

    /// <summary>
    /// Whether to include rate limit headers in responses
    /// </summary>
    public bool IncludeHeaders { get; set; }

    /// <summary>
    /// Custom message to return when rate limit is exceeded
    /// </summary>
    public string? CustomMessage { get; set; }

    /// <summary>
    /// HTTP status code to return when rate limit is exceeded
    /// </summary>
    [Range(400, 599, ErrorMessage = "StatusCode must be between 400 and 599")]
    public int StatusCode { get; set; }

    /// <summary>
    /// Paths to exclude from rate limiting (supports wildcards)
    /// </summary>
    public List<string>? ExcludedPaths { get; set; }

    /// <summary>
    /// IP addresses to whitelist (exempt from rate limiting)
    /// </summary>
    public List<string>? WhitelistedIps { get; set; }

    /// <summary>
    /// User agents to whitelist (exempt from rate limiting)
    /// </summary>
    public List<string>? WhitelistedUserAgents { get; set; }

    /// <summary>
    /// Whether to enable request queuing when rate limit is exceeded
    /// </summary>
    public bool EnableRequestQueuing { get; set; }

    /// <summary>
    /// Whether to persist queue state across application restarts
    /// </summary>
    public bool PersistQueueState { get; set; }

    /// <summary>
    /// Redis connection string for distributed rate limiting (optional)
    /// </summary>
    public string? RedisConnectionString { get; set; }

    /// <summary>
    /// Redis key prefix for rate limiting data
    /// </summary>
    public string? RedisKeyPrefix { get; set; }

    /// <summary>
    /// Whether to use distributed rate limiting with Redis
    /// </summary>
    public bool UseDistributedCache { get; set; } = false;
}

/// <summary>
/// Type of time window for rate limiting
/// </summary>
public enum TimeWindowType
{
    /// <summary>
    /// Fixed time window - resets at fixed intervals
    /// </summary>
    Fixed,

    /// <summary>
    /// Sliding time window - moves with each request
    /// </summary>
    Sliding
}