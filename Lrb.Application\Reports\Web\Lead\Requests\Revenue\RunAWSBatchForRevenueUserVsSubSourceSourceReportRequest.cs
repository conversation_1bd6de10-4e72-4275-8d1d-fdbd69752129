﻿using Lrb.Application.Common.ServiceBus;
using Newtonsoft.Json;

namespace Lrb.Application.Reports.Web.Lead.Requests.Revenue
{
    public class RunAWSBatchForRevenueUserVsSubSourceSourceReportRequest : IRequest<Response<Guid>>
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public DateType? DateType { get; set; }
        public bool IsWithTeam { get; set; }
        public List<Guid>? UserIds { get; set; }
        public string? SearchText { get; set; }
        public List<LeadSource>? Sources { get; set; }
        public List<string>? SubSources { get; set; }
        public List<string>? Projects { get; set; }
        public UserStatus? UserStatus { get; set; }
        public ReportPermission? ReportPermission { get; set; }
        public List<string>? Localites { get; set; }
        public List<string>? States { get; set; }
        public List<string>? Cities { get; set; }
        public List<string>? Countries { get; set; }
        public List<TokenType>? PaymentModes { get; set; }
        public ReportPermission? ExportPermission { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = int.MaxValue;
        public List<string>? ToRecipients { get; set; } = new();
        public List<string>? CcRecipients { get; set; } = new();
        public List<string>? BccRecipients { get; set; } = new();
        public string? FileName { get; set; }
        public string? TimeZoneId { get; set; }
        public TimeSpan BaseUTcOffset { get; set; }
        public string? Currency { get; set; }
        public string? S3BucketKey { get; set; }
        public long? TotalCount { get; set; }
        public bool? IsWithAssociatedProjects { get; set; } = false;
    }
    public class RunAWSBatchForRevenueUserVsSubSourceSourceReportRequestHandler : IRequestHandler<RunAWSBatchForRevenueUserVsSubSourceSourceReportRequest, Response<Guid>>
    {
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<ExportReportsTracker> _exportRepo;
        public const string TYPE = "revenueuservssubsource";
        private readonly IServiceBus _serviceBus;

        public RunAWSBatchForRevenueUserVsSubSourceSourceReportRequestHandler(
            ICurrentUser currentUser,
            IRepositoryWithEvents<ExportReportsTracker> exportRepo,
            IServiceBus serviceBus)
        {
            _currentUser = currentUser;
            _exportRepo = exportRepo;
            _serviceBus = serviceBus;
        }

        public async Task<Response<Guid>> Handle(RunAWSBatchForRevenueUserVsSubSourceSourceReportRequest request, CancellationToken cancellationToken)
        {
            try
            {
                ExportReportsTracker tracker = new();
                tracker.Request = JsonConvert.SerializeObject(request);
                tracker.ToRecipients = request.ToRecipients;
                tracker.CcRecipients = request.CcRecipients;
                tracker.BccRecipients = request.BccRecipients;
                tracker.Type = TYPE;
                var tenantId = _currentUser.GetTenant();
                var currentUserId = _currentUser.GetUserId();
                tracker.S3BucketKey = request?.S3BucketKey ?? string.Empty;
                tracker.Count = request?.TotalCount;
                tracker.FileName = request?.FileName ?? string.Empty;
                await _exportRepo.AddAsync(tracker, cancellationToken);
                if (string.IsNullOrWhiteSpace(request?.S3BucketKey))
                {

                    InputPayload input = new(tracker.Id, tenantId ?? string.Empty, currentUserId, TYPE);
                    var stringArgument = JsonConvert.SerializeObject(input);
                    var cmdArgs = new List<string>() { stringArgument };
                    await _serviceBus.RunExcelExportJobAsync(cmdArgs);
                    return new(tracker.Id);
                }
                return new(tracker.Id);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Something went wrong.");
            }
        }

    }
}
