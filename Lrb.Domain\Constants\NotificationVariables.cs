﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Domain.Constants
{
    public static class NotificationVariables
    {
        public const string UserName = "#userName#";
        public const string MobileNumber = "#MobileNumber#";
        public const string LeadName = "#leadName#";
        public const string Date = "#date#";
        public const string Time = "#time#";
        public const string Note = "#note#";
        public const string LeadEmail = "#leadEmail#";
        public const string LeadContact = "#leadContact#";
        public const string LeadCreatedOn = "#leadCreatedOn#";
        public const string PropertyName = "#propertyName#";
        public const string NoOfLeads = "#noOfLeads#";
        public const string NoOfLeadsAdded = "#noOfLeadsAdded#";
        public const string NoOfCallbacks = "#noOfCallbacks#";
        public const string NoOfMeetingsAssigned = "#NoOfMeetingsAssigned#";
        public const string NoOfTasks = "#noOfTasks#";
        public const string NoOfTasksAdded = "#noOfTasksAdded#";
        public const string TaskTitle = "#taskTitle#";
        public const string TaskDescription = "#taskDescription#";
        public const string LastNumber = "#lastNumber#";
        public const string LeadSource = "#leadSource#";
        public const string NoOfProperties = "#noOfProperties#";
        public const string NoOfPropertiesAdded = "#noOfPropertiesAdded#";
        public const string ChallengeName = "#dusKaDum#";
        public const string ChallengedFeature = "#challengedFeature#";
        public const string EntityId = "#entityId#";
        public const string ValidityInDays = "#ValidityDays#";
        public const string RemainingDays = "#daysLeft#";
        public const string PlanStartDate = "#PlanStartDate#";
        public const string PlanEndDate = "#PlanEndDate#";
        public const string PlanAmount = "#planAmount#";
        public const string GstAmount = "#gstAmount#";
        public const string Discount = "#Discount#";
        public const string TotalAmount = "#totalAmount#";
        public const string DateAndTime = "#dateAndTime#";
        public const string TransactionId = "#TransactionId#";
        public const string ChosenProperty = "#chosenProperty#";
        public const string ScheduleDateTime = "#ScheduleDateTime#";
        public const string MinutesBefore = "#MinutesBefore#";
        public const string AssignedBy = "#AssignedBy#";
        public const string UpdatedBy = "#UpdatedBy#";
        public const string EnquiredFor = "#enquiredFor#";
        public const string LowerBudget = "#lowerBudget#";
        public const string UpperBudget = "#upperBudget#";
        public const string NoOfBhk = "#noOfBhk#";
        public const string BhkType = "#bhkType#";
        public const string Location = "#location#";
        public const string LeadDto = "#leaddto#";
        public const string AssignedTo = "#AssignedTo#";
        public const string AlternateContactNo = "#AlternateContactNo#";
        public const string StatusId = "#StatusId#";
        public const string LastModifiedBy = "#LastModifiedBy#";
        public const string LastModifiedOn = "#LastModifiedOn#";
        public const string LeadCreatedBy = "#leadCreatedBy#";
        public const string RotationTime = "#rotationTime#";
        public const string SerialNo = "#serialNo#";
        public const string EnquiredLocation = "#enquiredLocation#";
        public const string EnquiredType = "#enquiredType#";
        public const string TenantName = "#tenantName#";
        public const string UserEmail = "#userEmail#";
        public const string Project = "#Project#";
        public const string NoOfBHKs = "#noOfBhks#";
        public const string Status = "#Status#";
        public const string DataName = "#dataName#";

    }
}
