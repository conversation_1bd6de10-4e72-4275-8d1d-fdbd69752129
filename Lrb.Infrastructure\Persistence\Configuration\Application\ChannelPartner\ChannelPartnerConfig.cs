﻿using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Infrastructure.Persistence.Configuration.Application.ChannelPartner
{
    public class ChannelPartnerConfig : IEntityTypeConfiguration<Lrb.Domain.Entities.ChannelPartner>
    {
        public void Configure(EntityTypeBuilder<Domain.Entities.ChannelPartner> builder)
        {
            builder.IsMultiTenant();
            builder.HasMany(i => i.Leads)
                .WithMany(i => i.ChannelPartners);
            builder.HasMany(i => i.Prospects)
                .WithMany(i => i.ChannelPartners);
            builder.HasMany(i => i.Projects)
                .WithMany(i => i.ChannelPartners);
        }
    }
}
