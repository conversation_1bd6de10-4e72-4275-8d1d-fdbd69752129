﻿
using Lrb.Application.Dashboard.Mobile;
using Lrb.Application.Dashboard.Mobile.Dtos;
using Lrb.Application.Dashboard.Mobile.Requests;
using Lrb.Domain.Enums;
using MediatR;


namespace Lrb.MobileApi.Host.Controllers
{
    [Authorize]
    public class DashboardController : VersionedApiController
    {
        private readonly IMediator _mediator;
        public DashboardController(IMediator mediator)
        {
            _mediator = mediator;
        }
        [HttpGet]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get dashboard basic details.", "")]
        public async Task<Response<DashboardDto>> GetDashboardBasicDetailsAsync([FromQuery] GetDashboardBasicDetailsRequest request)
        {
            return new(await _mediator.Send(request));
        }
        [HttpGet("default")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get dashboard with default filters.", "")]
        public async Task<Response<NewDashboardDto>> GetDashboardWithDefaultFilterAsync([FromQuery] GetDashboardWithDefaultFilterRequest request)
        {
            return await _mediator.Send(request);
        }
        [HttpGet("source")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get Lead Count By Source.", "")]
        public async Task<Response<List<DashboardBySourceWithTotalCountDto>>> GetDashboardBySourceAsync([FromQuery] GetDashboardBySourceRequest request)
        {
            return await _mediator.Send(request);
        }
        [HttpGet("leadsInContactWith")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get Leads In Contact With.", "")]
        public async Task<Response<LeadsInContactWithDto>> GetLeadsInContatWithAsync([FromQuery] GetLeadsInContactWithRequest request)
        {
            return await _mediator.Send(request);
        }
       
        [HttpGet("upcomingEvents")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get Upcoming Events.", "")]
        public async Task<Response<List<UpcomingEventDto>>> GetUpcomingEventsAsync([FromQuery] GetUpcomingEventsRequest request)
        {
            return await _mediator.Send(request);
        }
        [HttpGet("leadReport")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get Lead Report.", "")]
        public async Task<Response<List<LeadReportDto>>> GetLeadReportAsync([FromQuery] GetLeadReportRequest request)
        {
            return await _mediator.Send(request);
        }
        [HttpGet("countByStatus")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get Count By Status.", "")]
        public async Task<Response<LeadCountByStatusForMyDashboardDto>> GetLeadCountByStatusAsync([FromQuery] GetDashboardByStatusRequest request)
        {
            return await _mediator.Send(request);
        }


        [HttpGet("countStatusByUser")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get  status Count By User.", "")]
        public async Task<Response<DashboardLeadCountByUserDto>> GetLeadCountStatusByUserAsync([FromQuery] GetDashboardByUserRequest request)
        {
            return await (_mediator.Send(request));
        }


        [HttpGet("countCommunicationDetails")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get communication Count By User.", "")]
        public async Task<Response<DashboardCommunicationCountDto>> GetCountCommucnicationDetails([FromQuery] GetDashBoardCommunicationReportRequest request)
        {
            return await (_mediator.Send(request));
        }



        [HttpGet("countLeadSourceByUser")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get lead source count.", "")]
        public async Task<Response<IEnumerable<DashBoardLeadSourceCountDto>>> GetCountLeadSourceByUser([FromQuery] GetDashboardLeadSourceCountRequest request)
        {
            return await (_mediator.Send(request));
        }



        #region New Endpoints
        [HttpGet("siteVisitStatusCount")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get Site Visit Scheduled Count", "")]
        public async Task<Response<SiteVisitDto>> GetSiteVisitStatusAsync([FromQuery] GetSiteVisitStatusRequest request)
        {
            return await _mediator.Send(request);
        }
        [HttpGet("meetingStatusCount")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get Meeting Scheduled Count", "")]
        public async Task<Response<MeetingDto>> GetMeetingStatusAsync([FromQuery] GetMeetingStatusRequest request)
        {
            return await _mediator.Send(request);
        }


        [HttpGet("leadsPipeline")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get lead pipelines details count.", "")]
        public async Task<Response<DashboardLeadsPipelineDto>> GetCountLeadLeadPipeline([FromQuery] GetDashboardPipelineDetailsRequest request)
        {
            return await (_mediator.Send(request));
        }

        [HttpGet("leadSource")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get lead sources.", "")]
        public async Task<Response<IEnumerable<LeadSourcesDto>>> GetCountLeadSourceByUser([FromQuery] GetMobileDashboardLeadSourceRequest request)
        {
            return await (_mediator.Send(request));
        }


        [HttpGet("sourceDetails")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get lead All Source details.", "")]
        public async Task<List<object>> GetAllSourceDetails([FromQuery] GetMobileDashboardLeadSourceDetailsRequest request)
        {
            return await (_mediator.Send(request));
        }
        [HttpGet("callReport")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get Leads Call Report.", "")]
        public async Task<Response<Application.Dashboard.Mobile.Dtos.DashboardCallGroupedReportDto>> GetLeadActivityAsync([FromQuery] GetDashboardCallDetailsRequest request)
        {
            return await _mediator.Send(request);
        }

        [HttpGet("leadTracker")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get Lead Tracker.", "")]
        public async Task<Response<NewLeadtrackerDto>> GetLeadTrackerAsync([FromQuery] GetLeadTrackerRequest request)
        {
            return await _mediator.Send(request);
        }


        [HttpGet("leadReceived")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get Received Leads .", "")]
        public async Task<Response<Dictionary<string, int>>> GeLeadsByYear([FromQuery] GetMobileDashboardLeadReceivedCountRequest request)
        {
            return await (_mediator.Send(request));
        }
        #endregion

      
        [HttpGet("leadSource/V1")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get lead sources.", "")]
        public async Task<PagedResponse<DashboardNewLeadSourceDto, int>> GetLeadSourceByUser([FromQuery] GetDashboardNewLeadSourceRequestV1 request)
        {
            return await (_mediator.Send(request));
        }
       
        [HttpGet("leadstatus/custom/new")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get leads count by status.", "")]
        public async Task<PagedResponse<ViewUsersWithStatusDto, string>> GetleadstatusAsync([FromQuery] GetLeadStatusDataRequestV1 request)
        {
            return await (_mediator.Send(request));
        }

        [HttpGet("Datastatus")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get data Count  based on users", "")]
        public async Task<PagedResponse<DataByUserDto, string>> GetDataAsync([FromQuery] DataStatusByUsersRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("Count/all/status/new")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get all leads count by status.", "")]
        public async Task<Dictionary<string, string>> GetAllCountstatusAsync([FromQuery] GetAllLeadStatusCountRequest request)
        {
            return await (_mediator.Send(request));
        }
        [HttpGet("WhatsApp/Chats")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get all leads count with WhatsAppChat.", "")]
        public async Task<PagedResponse<WhatsAppChatDto, string>> GetAsync([FromQuery] GetWhatsAppChatRequest request)
        {
            return await (_mediator.Send(request));
        }
        [HttpGet("calls")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get all leads count with callrecordings.", "")]
        public async Task<PagedResponse<CallRecordsDto, string>> GetAsync([FromQuery] GetCallCountRequest request)
        {
            return await (_mediator.Send(request));
        }
        [HttpGet("lead/status/custom-count")]
        [TenantIdHeader]
        [OpenApiOperation("Get lead status report based on users", "")]
        public async Task<int> GetReportAsync([FromQuery] GeLeadStatusCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("Count/all/status")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get all leads count by status.", "")]
        public async Task<Dictionary<string, long>> GetAllCountstatusAsyncV1([FromQuery] GetAllLeadStatusCountRequestV1 request)
        {
            return await (_mediator.Send(request));
        }
        [HttpGet("leadstatus/custom")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get leads count by status.", "")]
        public async Task<PagedResponse<ViewUsersWithStatusDtoV1, string>> GetleadstatusAsyncV1([FromQuery] GetLeadStatusDataRequestV2 request)
        {
            return await (_mediator.Send(request));
        }
        [HttpGet("revenue/source")]
        [TenantIdHeader]
        [OpenApiOperation("Get revenue by source.", "")]
        public async Task<Response<List<RevenueVsSourceDto>>> GetAsync([FromQuery] GetRevenueVsSourceRequest request)
        {
            return await _mediator.Send(request);
        }
        [HttpGet("revenue/user")]
        [TenantIdHeader]
        [OpenApiOperation("Get revenue by user.", "")]
        public async Task<Response<List<RevenueVsUserDto>>> GetAsync([FromQuery] GetRevenueVsUserRequest request)
        {
            return await (_mediator.Send(request));
        }
        [HttpGet("revenue/project")]
        [TenantIdHeader]
        [OpenApiOperation("Get revenue by project.", "")]
        public async Task<Response<List<RevenueVsProjectDto>>> GetAsync([FromQuery] GetRevenueVsProjectRequest request)
        {
            return await (_mediator.Send(request));
        }
        [HttpGet("revenue/channelpartner")]
        [TenantIdHeader]
        [OpenApiOperation("Get revenue by channelpartner.", "")]
        public async Task<Response<List<RevenueVsChannelPartnerDto>>> GetAsync([FromQuery] GetRevenueVsChannelPartnerRequest request)
        {
            return await (_mediator.Send(request));
        }
        [HttpGet("revenue/campaign ")]
        [TenantIdHeader]
        [OpenApiOperation("Get revenue by Campaign .", "")]
        public async Task<Response<List<RevenueVsCampaignDto>>> GetAsync([FromQuery] GetRevenueVsCampaignRequest request)
        {
            return await (_mediator.Send(request));
        }
        [HttpGet("revenue")]
        [TenantIdHeader]
        [OpenApiOperation("Get revenue by Campaign .", "")]
        public async Task<Response<RevenueDto>> GetAsync([FromQuery] GetRevenueRequest request)
        {
            return await (_mediator.Send(request));
        }
        [HttpGet("employee/contribution")]
        [TenantIdHeader]
        [OpenApiOperation("Get Employee contribution LeadBoard .", "")]
        public async Task<Response<List<EmployeeContributionDto>>> GetAsync([FromQuery] GetEmployeeContributionRequest request)
        {
            return await (_mediator.Send(request));
        }
        [HttpGet("agent/performance")]
        [TenantIdHeader]
        [OpenApiOperation("Get agent performance.", "")]
        public async Task<Response<List<AgentPerformanceDto>>> GetAsync([FromQuery] GetAgentPerformanceRequest request)
        {
            return await (_mediator.Send(request));
        }
        [HttpGet("user/call-logs")]
        [TenantIdHeader]
        [OpenApiOperation("Get call-log report based on users", "")]
        public async Task<PagedResponse<ViewCallLogReportDto, string>> GetReportAsync([FromQuery] V2GetCallLogReportByUserRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("user/call-logs/new-count")]
        [TenantIdHeader]
        [OpenApiOperation("Get call-log report based on users count", "")]
        public async Task<int> GetReportAsync([FromQuery] V2GetCallLogReportByUserCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("employee/contribution/count")]
        [TenantIdHeader]
        [OpenApiOperation("Get Employee contribution LeadBoard .", "")]
        public async Task<Response<int>> GetAsync([FromQuery] GetEmployeeContributionCountRequest request)
        {
            return await (_mediator.Send(request));
        }
    }
}
