﻿using Lrb.Application.Automation.Dtos;
using Lrb.Application.Project.Web.Dtos;

namespace Lrb.Application.Project.Web.Specs
{
    public class GetProjectByNameSpecs : Specification<Lrb.Domain.Entities.Project>
    {
        public GetProjectByNameSpecs(string? projectName) 
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived && i.Name.ToLower().Trim() == projectName.ToLower().Trim());
        }
    }

    public class GetProjectByProjectNamesSpecs : Specification<Lrb.Domain.Entities.Project>
    {
        public GetProjectByProjectNamesSpecs(List<string> projects)
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived && i.Name != null && projects.Contains(i.Name.Trim().ToLower()));
        }
    }
    public class GetProjectByNameSpecsV1 : Specification<Lrb.Domain.Entities.Project, ProjectTitleDto>
    {
        public GetProjectByNameSpecsV1(string? projectName)
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived && i.Name.ToLower().Trim() == projectName.ToLower().Trim());
        }
    }

}
