﻿using Lrb.Application.CustomStatus.Mobile.Specs;
using Lrb.Application.GlobalSettings.Mobile;
using Lrb.Application.Team.Mobile;
using Lrb.Domain.Entities.MasterData;

namespace Lrb.Application.CustomStatus.Mobile.Request
{
    public class GetAllStatusV2Request : PaginationFilter, IRequest<PagedResponse<ViewCustomStatusDto, string>>
    {
    }

    public class GetAllStatusV2RequestHandler : IRequestHandler<GetAllStatusV2Request, PagedResponse<ViewCustomStatusDto, string>>
    {
        private readonly IReadRepository<CustomMasterLeadStatus> _customRepo;
        private readonly ICurrentUser _currentUser;
        private readonly IDapperRepository _dapperRepository;
        private readonly IReadRepository<Lrb.Domain.Entities.Team> _teamRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingsRepo;

        public GetAllStatusV2RequestHandler(IReadRepository<CustomMasterLeadStatus> customRepo, ICurrentUser currentUser, IDapperRepository dapperRepository, IReadRepository<Domain.Entities.Team> teamRepo, IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingsRepo)
        {
            _customRepo = customRepo;
            _currentUser = currentUser;
            _dapperRepository = dapperRepository;
            _teamRepo = teamRepo;
            _globalSettingsRepo = globalSettingsRepo;
        }
        public async Task<PagedResponse<ViewCustomStatusDto, string>> Handle(GetAllStatusV2Request request, CancellationToken cancellationToken)
        {
            var currentUserId = _currentUser.GetUserId();
            Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
            var isAdmin = await _dapperRepository.IsAdminAsync((_currentUser?.GetUserId() ?? Guid.Empty), _currentUser?.GetTenant() ?? string.Empty);
            List<CustomMasterLeadStatus>? statusDtos = null;
            int count = 0;
            if (!isAdmin)
            {
                var team = await _teamRepo.FirstOrDefaultAsync(new GetTeamWithStatusSpec(currentUserId));
                if (team != null)
                {
                    statusDtos = await _customRepo.ListAsync(new GetAllStatusV2Spec(request, currentUserId), cancellationToken);
                    count = await _customRepo.CountAsync(new GetAllStatusCountSpec(currentUserId), cancellationToken);
                }
                else
                {
                    statusDtos = await _customRepo.ListAsync(new GetAllStatusV2Spec(request, Guid.Empty), cancellationToken);
                    count = await _customRepo.CountAsync(new GetAllStatusCountSpec(Guid.Empty), cancellationToken);
                }
            }
            else
            {
                statusDtos = await _customRepo.ListAsync(new GetAllStatusV2Spec(request, Guid.Empty), cancellationToken);
                count = await _customRepo.CountAsync(new GetAllStatusCountSpec(Guid.Empty), cancellationToken);
            }
            var result = StatusHelper.GetCustomStatus(statusDtos.Adapt<List<ViewCustomStatusDto>>());
            if (!globalSettings?.IsCustomStatusEnabled ?? true)
            {
                result = StatusHelper.GetCustomFields(result); 
            }
            return new(result, count);
        }
    }
}
