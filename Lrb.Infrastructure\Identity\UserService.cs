using Amazon.CognitoIdentityProvider.Model;
using Amazon.DynamoDBv2;
using Amazon.S3.Transfer;
using Ardalis.Specification;
using Ardalis.Specification.EntityFrameworkCore;
using DocumentFormat.OpenXml.Bibliography;
using Finbuckle.MultiTenant;
using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Common.Caching;
using Lrb.Application.Common.Email;
using Lrb.Application.Common.Events;
using Lrb.Application.Common.Exceptions;
using Lrb.Application.Common.FileStorage;
using Lrb.Application.Common.GoogleAuth;
using Lrb.Application.Common.Identity;
using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.Mailing;
using Lrb.Application.Common.Models;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Common.SMS;
using Lrb.Application.Common.Specification;
using Lrb.Application.Common.WhatsApp.Interakt.Services;
using Lrb.Application.Identity.Cognito;
using Lrb.Application.Identity.Roles;
using Lrb.Application.Identity.Users;
using Lrb.Application.Identity.Users.Password;
using Lrb.Application.Identity.Users.Web.Dto;
using Lrb.Application.OrgProfile.Web.Mappings;
using Lrb.Application.Subscription.Web.Requests;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.NewFolder;
using Lrb.Domain.Enums;
using Lrb.Domain.Identity;
using Lrb.Infrastructure.Auth;
using Lrb.Infrastructure.Identity;
using Lrb.Infrastructure.Persistence.Context;
using Lrb.Infrastructure.Persistence.Repository.Interface;
using Lrb.Infrastructure.Sms;
using Lrb.Shared.Authorization;
using Mapster;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using System.Security.Claims;
namespace Lrb.Infrastructure.Identity;
internal partial class UserService : IUserService
{
    private readonly SignInManager<ApplicationUser> _signInManager;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly RoleManager<ApplicationRole> _roleManager;
    public readonly ApplicationDbContext _db;
    private readonly IJobService _jobService;
    private readonly IMailService _mailService;
    private readonly IGraphEmailService _graphEmailService;
    private readonly SecuritySettings _securitySettings;
    private readonly IEmailTemplateService _templateService;
    private readonly IFileStorageService _fileStorage;
    private readonly IEventPublisher _events;
    private readonly ICacheService _cache;
    private readonly ICacheKeyService _cacheKeys;
    private readonly ITenantInfo _currentTenant;
    private readonly ICognitoService<WebClient> _cognitoService;
    private readonly ICurrentUser _currentUser;
    private readonly ITextLocalSmsService _localTextService;
    private readonly IConfiguration _config;
    private readonly IOptions<TextLocalCredentials> _smsSettings;
    private readonly IMediator _mediator;
    private readonly IReadRepository<ResetPasswordCredentialStore> _resetPasswordRepo;
    private readonly IRoleService _roleService;
    private readonly Serilog.ILogger _logger;
    private readonly ILeadRepositoryAsync _leadRepositoryAsync;
    private readonly INpgsqlRepository _npgsqlRepository;
    private readonly IDapperRepository _dapperRepository;
    private readonly IBlobStorageService _blobStorageService;
    private readonly IRepositoryWithEvents<OTPMessage> _otpMessageRepo;
    private readonly IRepositoryWithEvents<Profile> _orgProfileRepo;
    private readonly IInteraktService _interaktService;
    private readonly IMemoryCache _memoryCache;
    private readonly IUserRepository _userRepository;

    public UserService(
        SignInManager<ApplicationUser> signInManager,
        UserManager<ApplicationUser> userManager,
        RoleManager<ApplicationRole> roleManager,
        ApplicationDbContext db,
        IJobService jobService,
        IMailService mailService,
        IEmailTemplateService templateService,
        IFileStorageService fileStorage,
        IEventPublisher events,
        ICacheService cache,
        ICacheKeyService cacheKeys,
        ITenantInfo currentTenant,
        ICognitoService<WebClient> cognitoService,
        IGraphEmailService graphEmailService,
        IOptions<SecuritySettings> securitySettings,
        ICurrentUser currentUser,
        ITextLocalSmsService localTextService,
        IConfiguration config,
        IOptions<TextLocalCredentials> smsSettings,
        IMediator mediator,
        IReadRepository<ResetPasswordCredentialStore> resetPasswordRepo,
        IRoleService roleService,
        Serilog.ILogger logger,
        ILeadRepositoryAsync leadRepositoryAsync,
        INpgsqlRepository npgsqlRepository,
        IDapperRepository dapperRepository,
        IBlobStorageService blobStorageService,
        IRepositoryWithEvents<OTPMessage> otpMessageRepo,
        IRepositoryWithEvents<Profile> orgProfileRepo,
        IInteraktService interaktService,
        IMemoryCache memoryCache,
        IUserRepository userRepository
        )
    {
        _signInManager = signInManager;
        _userManager = userManager;
        _roleManager = roleManager;
        _db = db;
        _jobService = jobService;
        _mailService = mailService;
        _templateService = templateService;
        _fileStorage = fileStorage;
        _events = events;
        _cache = cache;
        _cacheKeys = cacheKeys;
        _currentTenant = currentTenant;
        _securitySettings = securitySettings.Value;
        _graphEmailService = graphEmailService;
        _cognitoService = cognitoService;
        _currentUser = currentUser;
        _localTextService = localTextService;
        _config = config;
        _smsSettings = smsSettings;
        _mediator = mediator;
        _resetPasswordRepo = resetPasswordRepo;
        _roleService = roleService;
        _logger = logger;
        _leadRepositoryAsync = leadRepositoryAsync;
        _npgsqlRepository = npgsqlRepository;
        _dapperRepository = dapperRepository;
        _blobStorageService = blobStorageService;
        _otpMessageRepo = otpMessageRepo;
        _orgProfileRepo = orgProfileRepo;
        _interaktService = interaktService;
        _memoryCache = memoryCache;
        _userRepository = userRepository;
    }

    public async Task<PagedResponse<UserDetailsDto, string>> SearchAsync(UserListFilter filter, CancellationToken cancellationToken)
    {
        var spec = new EntitiesByPaginationFilterSpec<ApplicationUser>(filter);
        var adminUserName = (_currentTenant.Id?.ToLower() ?? string.Empty) + "." + "admin";
        var users = await _userManager.Users
            .Where(u => !u.IsDeleted && (u.UserName.ToLower() != adminUserName.ToLower()))
            .OrderByDescending(i => i.LastModifiedOn ?? DateTime.MinValue)
            .WithSpecification(spec)
            .ProjectToType<UserDetailsDto>()
            .ToListAsync(cancellationToken);
        int count = await _userManager.Users
            .CountAsync(i => !i.IsDeleted && (i.UserName.ToLower() != adminUserName.ToLower()));

        return new PagedResponse<UserDetailsDto, string>(users, count);
    }

    public async Task<bool> ExistsWithNameAsync(string name)
    {
        EnsureValidTenant();
        return await _userManager.FindByNameAsync(name) is ApplicationUser user && user.Id != null && !user.IsDeleted;
    }

    public async Task<bool> ExistsWithEmailAsync(string email, string? exceptId = null)
    {
        EnsureValidTenant();
        return await _userManager.FindByEmailAsync(email.Normalize()) is ApplicationUser user && user.Id != exceptId && !user.IsDeleted;
    }

    public async Task<bool> ExistsWithPhoneNumberAsync(string phoneNumber, string? exceptId = null)
    {
        EnsureValidTenant();
        return await _userManager.Users.FirstOrDefaultAsync(x => x.PhoneNumber == phoneNumber && !x.IsDeleted) is ApplicationUser user && user.Id != exceptId;
    }

    private void EnsureValidTenant()
    {
        if (string.IsNullOrWhiteSpace(_currentTenant?.Id))
        {
            throw new Application.Common.Exceptions.UnauthorizedException("Invalid Tenant.");
        }
    }

    public async Task<List<UserDetailsDto>> GetListAsync(CancellationToken cancellationToken)
    {
        var adminUserName = (_currentTenant.Id?.ToLower() ?? string.Empty) + "." + "admin";
        return (await _userManager.Users
                .AsNoTracking()
                .Where(i => !i.IsDeleted && (i.UserName.ToLower() != adminUserName.ToLower()))
                .OrderByDescending(i => i.IsActive)
                .ToListAsync(cancellationToken))
                .Adapt<List<UserDetailsDto>>();
    }

    public List<UserDetailsDto> GetList()
    {
        var adminUserName = (_currentTenant.Id?.ToLower() ?? string.Empty) + "." + "admin";
        return (_userManager.Users
                .AsNoTracking()
                .Where(i => !i.IsDeleted && (i.UserName.ToLower() != adminUserName.ToLower()))
                .OrderByDescending(i => i.LastModifiedOn ?? DateTime.MinValue)
                .ToList())
                .Adapt<List<UserDetailsDto>>();
    }
    public async Task<List<UserDetailsDto>> GetListAsync(int pageNumber, int pageSize, CancellationToken cancellationToken)
    {
        var adminUserName = (_currentTenant.Id?.ToLower() ?? string.Empty) + "." + "admin";
        pageSize = pageSize <= 0 ? 10 : pageSize;
        pageNumber = pageNumber <= 0 ? 1 : pageNumber;
        return (await _userManager.Users
                .AsNoTracking()
                .Where(i => !i.IsDeleted && (i.UserName.ToLower() != adminUserName.ToLower()))
                .OrderByDescending(i => i.LastModifiedOn ?? DateTime.MinValue)
                .OrderByDescending(i => i.IsActive)
                .Skip(pageSize * (pageNumber - 1)).Take(pageSize)
                .ToListAsync(cancellationToken))
            .Adapt<List<UserDetailsDto>>();

    }
    public async Task<List<UserDetailsDto>> GetListAsync(int pageNumber, int pageSize)
    {
        var adminUserName = (_currentTenant.Id?.ToLower() ?? string.Empty) + "." + "admin";
        pageSize = pageSize <= 0 ? 10 : pageSize;
        pageNumber = pageNumber <= 0 ? 1 : pageNumber;
        return (await _userManager.Users
                .AsNoTracking()
                .Where(i => !i.IsDeleted && (i.UserName.ToLower() != adminUserName.ToLower()))
                .OrderBy(i => i.UserName)
                .Skip(pageSize * (pageNumber - 1)).Take(pageSize)
                .ToListAsync())
            .Adapt<List<UserDetailsDto>>();
    }
    public Task<int> GetCountAsync(CancellationToken cancellationToken)
    {
        var adminUserName = (_currentTenant.Id?.ToLower() ?? string.Empty) + "." + "admin";
        return _userManager.Users.AsNoTracking().Where(i => !i.IsDeleted && (i.UserName.ToLower() != adminUserName.ToLower())).CountAsync(cancellationToken);
    }
    public async Task<UserDetailsDto> GetAsync(string userId, CancellationToken cancellationToken)
    {
        var user = await _userManager.Users
            .AsNoTracking()
            .Where(u => u.Id == userId && !u.IsDeleted)
            .FirstOrDefaultAsync(cancellationToken);

        _ = user ?? throw new NotFoundException("User Not Found.");

        return user.Adapt<UserDetailsDto>();
    }
    public async Task<UserDetailsDto> GetByUserNameAsync(string userName, CancellationToken cancellationToken)
    {
        var user = await _userManager.Users
            .AsNoTracking()
            .Where(u => u.UserName.ToLower() == userName.ToLower() && !u.IsDeleted)
            .FirstOrDefaultAsync(cancellationToken);

        _ = user ?? throw new NotFoundException("User Not Found.");

        return user.Adapt<UserDetailsDto>();
    }
    public async Task<UserDetailsDto> GetByPhoneAsync(string phoneNumber, CancellationToken cancellationToken)
    {
        var user = await _userManager.Users
            .AsNoTracking()
            .Where(u => u.PhoneNumber.Substring(u.PhoneNumber.Length - 10) == phoneNumber.Substring(phoneNumber.Length - 10) && !u.IsDeleted &&
            (_currentTenant.Id + "." + "admin" != u.UserName))
            .FirstOrDefaultAsync(cancellationToken);
        _ = user ?? throw new NotFoundException("User Not Found.");
        return user.Adapt<UserDetailsDto>();
    }

    public async Task<UserDetailsDto> GetByEmailAsync(string emailAddress, CancellationToken cancellationToken)
    {
        var user = await _userManager.Users
            .AsNoTracking()
            .Where(u => u.Email.ToLower().Equals(emailAddress.ToLower()) && !u.IsDeleted &&
            (_currentTenant.Id + "." + "admin" != u.UserName))
            .FirstOrDefaultAsync(cancellationToken);
        _ = user ?? throw new NotFoundException("User Not Found.");
        return user.Adapt<UserDetailsDto>();
    }

    public async Task<List<UserDetailsDto>> GetListOfUsersByIdsAsync(List<string> userIds, CancellationToken cancellationToken)
    {
        var adminUserName = (_currentTenant.Id?.ToLower() ?? string.Empty) + "." + "admin";
        return (await _userManager.Users
             .AsNoTracking()
             .OrderByDescending(i => i.LastModifiedOn ?? DateTime.MinValue)
             .Where(u => userIds.Contains(u.Id) && !u.IsDeleted && (u.UserName.ToLower() != adminUserName.ToLower()))
             .ToListAsync(cancellationToken))
            .Adapt<List<UserDetailsDto>>();
    }
    public async Task<List<UserDetailsDto>> GetListOfActiveUsersByIdsAsync(List<string> userIds, CancellationToken cancellationToken)
    {
        var adminUserName = (_currentTenant.Id?.ToLower() ?? string.Empty) + "." + "admin";
        return (await _userManager.Users
             .AsNoTracking()
             .OrderByDescending(i => i.LastModifiedOn ?? DateTime.MinValue)
             .Where(u => userIds.Contains(u.Id) && !u.IsDeleted && u.IsActive == true && (u.UserName.ToLower() != adminUserName.ToLower()))
             .ToListAsync(cancellationToken))
            .Adapt<List<UserDetailsDto>>();
    }
    public async Task<List<UserDetailsDto>> GetListOfActiveandDeactiveUsersByIdsAsync(List<string> userIds, CancellationToken cancellationToken)
    {
        var adminUserName = (_currentTenant.Id?.ToLower() ?? string.Empty) + "." + "admin";
        return (await _userManager.Users
             .AsNoTracking()
             .OrderByDescending(i => i.LastModifiedOn ?? DateTime.MinValue)
             .Where(u => userIds.Contains(u.Id) && !u.IsDeleted && (u.UserName.ToLower() != adminUserName.ToLower()))
             .ToListAsync(cancellationToken))
            .Adapt<List<UserDetailsDto>>();
    }
    public async Task<List<UserDetailsDto>> GetAdminsMFAEnabledUsersAsync(List<string>? userIds, CancellationToken cancellationToken)
    {
        var adminUserName = (_currentTenant.Id?.ToLower() ?? string.Empty) + "." + "admin";
        return (await _userManager.Users
             .AsNoTracking()
             .OrderByDescending(i => i.LastModifiedOn ?? DateTime.MinValue)
             .Where(u => (userIds.Contains(u.Id) || u.IsMFAEnabled == true) && !u.IsDeleted && u.IsActive == true && (u.UserName.ToLower() != adminUserName.ToLower()))
             .ToListAsync(cancellationToken))
            .Adapt<List<UserDetailsDto>>();
    }
    public async Task<List<Guid>> GetListOfActiveUserIdsAsync(CancellationToken cancellationToken)
    {
        var adminUserName = (_currentTenant.Id?.ToLower() ?? string.Empty) + "." + "admin";
        return (await _userManager.Users
             .AsNoTracking()
             .OrderByDescending(i => i.LastModifiedOn ?? DateTime.MinValue)
             .Where(u => !u.IsDeleted && u.IsActive && (u.UserName.ToLower() != adminUserName.ToLower()))
             .ToListAsync(cancellationToken)).Select(i => (Guid.TryParse(i.Id, out Guid userId) ? userId : Guid.Empty)).ToList();
    }
    public async Task<List<Guid>> GetListOfInActiveUserIdsAsync(CancellationToken cancellationToken)
    {
        var adminUserName = (_currentTenant.Id?.ToLower() ?? string.Empty) + "." + "admin";
        return (await _userManager.Users
             .AsNoTracking()
             .Where(u => !u.IsDeleted && !u.IsActive && (u.UserName.ToLower() != adminUserName.ToLower()))
             .ToListAsync(cancellationToken)).Select(i => (Guid.TryParse(i.Id, out Guid userId) ? userId : Guid.Empty)).ToList();
    }
    public async Task<List<Guid>> GetListOfAllUserIdsAsync(CancellationToken cancellationToken)
    {
        var adminUserName = (_currentTenant.Id?.ToLower() ?? string.Empty) + "." + "admin";
        return (await _userManager.Users
             .AsNoTracking()
             .Where(u => !u.IsDeleted && (u.UserName.ToLower() != adminUserName.ToLower()))
             .ToListAsync(cancellationToken)).Select(i => (Guid.TryParse(i.Id, out Guid userId) ? userId : Guid.Empty)).ToList();
    }

    public async Task<List<Guid>> GetMFADisabledUserIdsAsync(CancellationToken cancellationToken)
    {
        var adminUserName = (_currentTenant.Id?.ToLower() ?? string.Empty) + "." + "admin";
        return (await _userManager.Users
             .AsNoTracking()
             .Where(u => !u.IsDeleted && (!u.IsMFAEnabled) && (u.UserName.ToLower() != adminUserName.ToLower()))
             .ToListAsync(cancellationToken)).Select(i => (Guid.TryParse(i.Id, out Guid userId) ? userId : Guid.Empty)).ToList();
    }

    public async Task ToggleStatusAsync(ToggleUserStatusRequest request, CancellationToken cancellationToken)
    {

        var tenantId = _currentTenant.Id;
        var user = await _userManager.Users.Where(u => u.Id == request.UserId && !u.IsDeleted).FirstOrDefaultAsync(cancellationToken);

        _ = user ?? throw new NotFoundException("User Not Found.");

        if (request.ActivateUser == true)
        {
            #region Subscription Threshold
            if (await _dapperRepository.CheckUserMoreThanLicenseBought(tenantId ?? string.Empty))
            {
                var admins = await _dapperRepository.GetAdminsNameAndNumberAsync(tenantId ?? string.Empty);
                var emailBody = await _dapperRepository.GetEmailTemplateBodyAsync();
                var subscriptionDetails = await _mediator.Send(new GetSubscriptionInfoRequest());

                await SubscriptionHelper.SendSubscriptionThresholdMails(tenantId ?? string.Empty, _currentTenant.Name ?? string.Empty, user.UserName, user.FirstName + " " + user.LastName, user.PhoneNumber, emailBody, admins, subscriptionDetails.Data, _graphEmailService);

                throw new ConflictException("Active users limit excceded");
            }
            #endregion
        }


        bool isAdmin = await _userManager.IsInRoleAsync(user, LrbRoles.Admin);
        if (isAdmin)
        {
            throw new ConflictException("Administrators Profile's Status cannot be toggled");
        }
        if (request.ActivateUser)
        {
            await _cognitoService.EnableUserAsync(user.UserName);
        }
        else
        {
            await _cognitoService.DisableUserAsync(user.UserName);
        }
        user.IsActive = request.ActivateUser;
        user.LastModifiedBy = request.CurrentUserId;

        user.LastModifiedOn = DateTime.UtcNow;

        await _userManager.UpdateAsync(user);

        await _events.PublishAsync(new ApplicationUserUpdatedEvent(user.Id));
    }

    public async Task<bool> DeleteAsync(string userId, CancellationToken cancellationToken)
    {
        var user = await _userManager.Users.Where(u => u.Id == userId && !u.IsDeleted).FirstOrDefaultAsync(cancellationToken);
        _ = user ?? throw new NotFoundException("User Not Found.");
        bool isAdmin = await _userManager.IsInRoleAsync(user, LrbRoles.Admin);
        if (isAdmin)
        {
            throw new ConflictException("Administrators Profile's cannot be delete");
        }
        user.IsDeleted = true;
        user.DeletedBy = _currentUser.GetUserId();
        user.DeletedOn = DateTime.UtcNow;
        user.IsActive = false;
        await _userManager.UpdateAsync(user);
        // var deleteUser = await _userManager.DeleteAsync(user);
        return await _cognitoService.DeleteUserAsync(user.UserName);

    }
    public async Task<object> PermanentDeleteAsync(string userId, CancellationToken cancellationToken)
    {
        var user = await _userManager.Users.Where(u => u.Id == userId && !u.IsDeleted).FirstOrDefaultAsync(cancellationToken);
        _ = user ?? throw new NotFoundException("User Not Found.");
        bool isAdmin = await _userManager.IsInRoleAsync(user, LrbRoles.Admin);
        if (isAdmin)
        {
            throw new ConflictException("Administrators Profile's cannot be delete");
        }
        user.IsDeleted = true;
        user.DeletedBy = _currentUser.GetUserId();
        user.DeletedOn = DateTime.UtcNow;
        user.IsActive = false;
        var userInfo = user.Adapt<ApplicationUser>();
        await _userManager.UpdateAsync(user);
        var deleteUser = await _userManager.DeleteAsync(user);
        var res = await _cognitoService.DeleteUserAsync(user.UserName);
        if (res)
        {
            return userInfo;
        }
        return null;
    }
    public async Task<Response<UserDetailsDto>> VerifyUserAsync(string name)
    {
        EnsureValidTenant();
        var user = await _userManager.FindByNameAsync(name);
        if (user is null)
        {
            return new("No UserName found.");
        }
        return new(user.Adapt<UserDetailsDto>());
    }

    public async Task<Response<bool>> VerifyOtpAsync(VerifyOtp modal)
    {
        Application.ResetPassword.Requests.GetResetPasswordCredentialsRequest GetRequest = new() { UserId = Guid.Parse(modal.UserId) };
        var resetCredentils = await _mediator.Send(GetRequest);
        var resetCredentil = resetCredentils.OrderByDescending(i => i.CreatedOn).FirstOrDefault();
        if (resetCredentil?.Otp?.IsNullOrEmpty() ?? true)
        {
            throw new InternalServerException("An Error has occurred!", null, ErrorActionCode.NoOp);
        }
        bool response = true;
        return new(string.Equals(resetCredentil.Otp, modal.Otp) ? response : !response);

    }
    public async Task<bool> UserNameExist(string userName)
    {
        try
        {
            var res = await _cognitoService.GetUserAsync(userName);
            if (res != null && !string.IsNullOrWhiteSpace(res.Username))
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        catch (UserNotFoundException)
        {
            return false;
        }
        catch (Exception ex) { return false; }
        //return await _npgsqlRepository.CheckUserNameExists(userName);
    }

    public async Task<(bool IsUpdated, string Message)> BulkToggleStatusAsync(BulkToggleUserStatusRequest request, CancellationToken cancellationToken)
    {
        try
        {
            if (request.UserIds?.Any() ?? false)
            {
                int adminCount = 0;

                foreach (var userId in request.UserIds)
                {
                    var user = await _userManager.Users.Where(u => u.Id == userId && !u.IsDeleted).FirstOrDefaultAsync(cancellationToken);
                    if (user != null)
                    {
                        if (request.ActivateUser != false)
                        {
                            #region Subscription Threshold
                            var tenantId = _currentTenant.Id;
                            if (await _dapperRepository.CheckUserMoreThanLicenseBought(tenantId ?? string.Empty))
                            {
                                var admins = await _dapperRepository.GetAdminsNameAndNumberAsync(tenantId ?? string.Empty);
                                var emailBody = await _dapperRepository.GetEmailTemplateBodyAsync();
                                var subscriptionDetails = await _mediator.Send(new GetSubscriptionInfoRequest());
                                await SubscriptionHelper.SendSubscriptionThresholdMails(tenantId ?? string.Empty, _currentTenant.Name ?? string.Empty, user.UserName, user.FirstName + " " + user.LastName, user.PhoneNumber, emailBody, admins, subscriptionDetails.Data, _graphEmailService);
                                return (false, "Active users limit excceded");
                            }
                            #endregion
                        }
                        bool isAdmin = await _userManager.IsInRoleAsync(user, LrbRoles.Admin);
                        if (isAdmin)
                        {
                            adminCount++;
                        }
                        else
                        {
                            if (request.ActivateUser && !user.IsActive)
                            {
                                await _cognitoService.EnableUserAsync(user.UserName);
                                user.IsActive = request.ActivateUser;
                            }
                            else if (!request.ActivateUser && user.IsActive)
                            {
                                await _cognitoService.DisableUserAsync(user.UserName);
                                user.IsActive = request.ActivateUser;
                            }
                            user.LastModifiedBy =  request.CurrentUserId ?? _currentUser.GetUserId(); 
                            user.LastModifiedOn = DateTime.UtcNow;
                            await _userManager.UpdateAsync(user);

                            await _events.PublishAsync(new ApplicationUserUpdatedEvent(user.Id));
                        }
                    }
                }
                if (!request.ActivateUser)
                {
                    if (adminCount > 0)
                    {
                        return (true, $"{adminCount} user(s) with admin role found, they can't be deactivated, \nOthers have been deactivated successfully!");
                    }
                    else
                    {
                        return (true, "Selected users have been deactivated successfully!");
                    }
                }
                else
                {
                    return (true, "Selected users have been activated successfully!");
                }
            }
            else
            {
                return (false, "Request does not contain any user Id.");
            }
        }
        catch
        {
            return (false, "Something went wrong!");
        }
    }
    public async Task<(bool IsUpdated, string Message)> BulkToggleMFAAsync(List<Guid> userIds, CancellationToken cancellationToken)
    {
        try
        {
            if (userIds?.Any() ?? false)
            {
                foreach (var userId in userIds)
                {
                    var user = await _userManager.Users.Where(u => u.Id == userId.ToString() && !u.IsDeleted).FirstOrDefaultAsync(cancellationToken);

                    (bool HTTPStatusCode, bool MFAStatus) cognitoResponse = new();

                    if (user != null)
                    {
                        cognitoResponse = await _cognitoService.ToggleMFAAsync(user.UserName, cancellationToken);
                    }

                    if (cognitoResponse.HTTPStatusCode && user != null)
                    {
                        user.IsMFAEnabled = cognitoResponse.MFAStatus;
                        await _userManager.UpdateAsync(user);
                    }
                }
                return (true, "Successfull!");
            }
            else
            {
                return (false, "Request does not contain any user Id.");
            }
        }
        catch
        {
            return (false, "Something went wrong!");
        }
    }

    public async Task<bool> UpdateOrAddAttendanceClaimAsync(bool? IsEnabledForAllUsers, List<Guid>? userIds, Claim claim, string tenantId, CancellationToken cancellationToken)
    {
        if (IsEnabledForAllUsers ?? false)
        {
            var userIdsWithoutAdmin = (await _dapperRepository.GetUserIdsWithoutAdminAsync(tenantId)).ToList();
            var adminUserIds = (await _dapperRepository.GetAllAdminIdsAsync(tenantId ?? string.Empty)).ToList();
            userIdsWithoutAdmin = userIdsWithoutAdmin.Where(i => !adminUserIds.Contains(i)).ToList();
            foreach (var userId in userIdsWithoutAdmin)
            {
                try
                {
                    var user = await _userManager.FindByIdAsync(userId.ToString());
                    var userClaims = await _userManager.GetClaimsAsync(user);
                    var existingClaim = userClaims.FirstOrDefault(uc => uc.Type.Contains(IdTokenClaimsKey.ShiftTime));
                    if (existingClaim != null)
                    {
                        await _userManager.ReplaceClaimAsync(user, existingClaim, claim);
                        await _cognitoService.AddAttendanceSettingClaimAync(user.UserName, claim);
                    }
                    else
                    {
                        await _userManager.AddClaimAsync(user, claim);
                        await _cognitoService.AddAttendanceSettingClaimAync(user.UserName, claim);
                    }
                }

                catch (Exception ex) { }

            }
        }
        else
        {
            var userIdsWithoutAdmin = (await _dapperRepository.GetUserIdsWithoutAdminAsync(tenantId)).ToList();
            var adminUserIds = (await _dapperRepository.GetAllAdminIdsAsync(tenantId ?? string.Empty)).ToList();
            userIdsWithoutAdmin = userIdsWithoutAdmin.Where(i => !adminUserIds.Contains(i)).ToList();
            foreach (var userId in userIdsWithoutAdmin)
            {
                try
                {
                    var user = await _userManager.FindByIdAsync(userId.ToString());
                    var claimWithEmptyValue = new Claim(IdTokenClaimsKey.ShiftTime, string.Empty);
                    var userClaims = await _userManager.GetClaimsAsync(user);
                    var existingClaim = userClaims.FirstOrDefault(uc => uc.Type.Contains(IdTokenClaimsKey.ShiftTime));
                    if (existingClaim != null)
                    {
                        var isAdded = await _cognitoService.AddAttendanceSettingClaimAync(user.UserName, claimWithEmptyValue);
                        await _userManager.RemoveClaimAsync(user, existingClaim);
                    }

                }

                catch (Exception ex) { }
            }
        }
        if (userIds?.Any() ?? false)
        {
            foreach (var userId in userIds)
            {
                try
                {
                    var user = _userManager.Users.FirstOrDefault(i => i.Id == userId.ToString());

                    var userClaims = await _userManager.GetClaimsAsync(user);
                    var existingClaim = userClaims.FirstOrDefault(uc => uc.Type.Contains(IdTokenClaimsKey.ShiftTime));
                    if (existingClaim != null)
                    {
                        await _userManager.ReplaceClaimAsync(user, existingClaim, claim);
                        await _cognitoService.AddAttendanceSettingClaimAync(user.UserName, claim);
                    }
                    else
                    {
                        await _userManager.AddClaimAsync(user, claim);
                        await _cognitoService.AddAttendanceSettingClaimAync(user.UserName, claim);
                    }


                }
                catch (Exception ex) { }
            }
        }
        return true;
    }
    public async Task<bool> UpdateUserShiftTimingClaimAsync(string? userId, Claim claim, string? tenantId, CancellationToken cancellationToken)
    {
        try
        {
            if (!string.IsNullOrEmpty(userId))
            {
                var user = _userManager.Users.FirstOrDefault(i => i.Id == userId);

                var userClaims = await _userManager.GetClaimsAsync(user);
                var existingClaim = userClaims.FirstOrDefault(uc => uc.Type.Contains(IdTokenClaimsKey.ShiftTime));
                if (existingClaim != null)
                {
                    await _userManager.RemoveClaimAsync(user, existingClaim);
                    await _userManager.AddClaimAsync(user, claim);
                    return await _cognitoService.AddAttendanceSettingClaimAync(user.UserName, claim);
                }
                else
                {
                    await _userManager.AddClaimAsync(user, claim);
                    return await _cognitoService.AddAttendanceSettingClaimAync(user.UserName, claim);
                }

            }
        }
        catch (Exception ex) { }
        return true;
    }
    public async Task<bool> RemoveClaimForAllAsync(Claim claim, string tenantId, CancellationToken cancellationToken)
    {
        var isSucceeded = false;
        var notAdminUserIds = (await _dapperRepository.GetUserIdsWithoutAdminAsync(tenantId)).ToList();
        var adminUserIds = (await _dapperRepository.GetAllAdminIdsAsync(tenantId ?? string.Empty)).ToList();
        notAdminUserIds = notAdminUserIds.Where(i => !adminUserIds.Contains(i)).ToList();
        foreach (var userId in notAdminUserIds)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId.ToString());
                claim = new Claim(IdTokenClaimsKey.ShiftTime, string.Empty);
                var userClaims = await _userManager.GetClaimsAsync(user);
                var existingClaim = userClaims.FirstOrDefault(uc => uc.Type.Contains(IdTokenClaimsKey.ShiftTime));
                if (existingClaim != null)
                {
                    isSucceeded = await _cognitoService.AddAttendanceSettingClaimAync(user.UserName, claim);
                    await _userManager.RemoveClaimAsync(user, existingClaim);
                }
            }

            catch (Exception ex) { }

        }
        return isSucceeded;
    }
    public async  Task<UserDetailsDto> GetUserAsync(string userId, CancellationToken cancellationToken)
    {
        var user = await _userManager.Users
            .AsNoTracking()
            .Where(u => u.Id == userId && !u.IsDeleted)
            .FirstOrDefaultAsync(cancellationToken);

        return user.Adapt<UserDetailsDto>();
    }
    public async Task<List<Lrb.Application.UserDetails.Web.UserDetailsDto>> GetUserBasicDetailsAsync(List<Guid>? userIds, CancellationToken cancellationToken)
    {
        try
        {
            if (userIds?.Any() ?? false)
            {
                return (await _dapperRepository.GetUserBasicDetailsAsync(userIds, _currentTenant.Id)).ToList();
            }
            else
            {
                return new();
            }
        }
        catch { return new(); }
    }
    public async Task<bool> RevokeUserRefreshToken(string userId, Guid? loginId, CancellationToken cancellationToken)
    {
        try
        {

            var refreshToken = await _dapperRepository.GetUserTokenDetailsByUserIdAndLoginIdAsync(userId, loginId, cancellationToken);
            var isSucceeded = await _cognitoService.GlobalSignOutUser(refreshToken);
            return isSucceeded;
        }
        catch (Exception ex) { return false; }
    }
}
    