﻿using Lrb.Application.Lead.Mobile;
using Lrb.Application.ListingManagement.Mobile.Dtos;
using Lrb.Application.Property.Mobile.Dtos;
using Lrb.Application.Property.Web;



namespace Lrb.Application.Property.Mobile
{
    public class UpdatePropertyDto : CreatePropertyDto
    {
    }
    public class CreatePropertyDto : BasePropertyDto
    {
        public string? PlaceId { get; set; }
        public Guid? PropertyTypeId { get; set; }
        public List<CreateListingAddressDto>? ListingAddresses { get; set; }
    }
    public class ViewPropertyDto : BasePropertyDto
    {
        public DateTime? LastModifiedOn { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid LastModifiedBy { get; set; }
        public PropertyTypeDto? PropertyType { get; set; }
        public string? MicrositeURL { get; set; }
        public string? SerialNo { get; set; }
        public ListingStatus? ListingStatus { get; set; }
        public DateTime? ListingExpireDate { get; set; }
        public List<CustomListingSourceDto>? ListingSources { get; set; }
        public List<ViewListingSourceAddressDto>? ListingSourceAddresses { get; set; }
        public string? ShortUrl { get; set; }
    }
    public class BasePropertyDto : IDto
    {
        public Guid Id { get; set; }
        public string? Title { get; set; }
        public SaleType SaleType { get; set; }
        public EnquiryType EnquiredFor { get; set; }
        public string? Notes { get; set; }
        public FurnishStatus FurnishStatus { get; set; }
        public PropertyStatus Status { get; set; }
        public string? Rating { get; set; }
        public int ShareCount { get; set; }
        public DateTime? PossessionDate { get; set; }
        public bool IsGOListingEnabled { get; set; }
        public Facing Facing { get; set; }
        //table references
        public Guid? GOPropertyId { get; set; }
        public double NoOfBHK { get; set; }
        public BHKType BHKType { get; set; }
        public PropertyMonetaryInfoDto? MonetaryInfo { get; set; }
        public PropertyOwnerDetailsDto? OwnerDetails { get; set; }
        public PropertyDimensionDto? Dimension { get; set; }
        public PropertyTagDto? TagInfo { get; set; }
        public  List<PropertyGalleryDto>? Images { get; set; }
        public Dictionary<string, List<string>>? ImageUrls { get; set; }
        public IEnumerable<PropertyAttributeDto>? Attributes { get; set; }
        public IEnumerable<Guid>? Amenities { get; set; }
        public List<BrochureDto>? Brochures { get; set; }
        public int WhatsAppShareCount { get; set; }
        public int CallShareCount { get; set; }
        public int EmailShareCount { get; set; }
        public int SMSShareCount { get; set; }
        public string? AboutProperty { get; set; }
        public AddressDto? Address { get; set; }
        public List<string>? Links { get; set; }
        public string? Project { get; set; }
        public List<Guid>? AssignedTo { get; set; }
        public bool? ShouldVisisbleOnListing { get; set; }
        public SecurityDeposit? SecurityDeposit { get; set; }
        public LockInPeriod? LockInPeriod { get; set; }
        public NoticePeriod? NoticePeriod { get; set; }
        public List<int>? NoOfFloorsOccupied { get; set; }
        public string? CoWorkingOperator { get; set; }
        public string? CoWorkingOperatorName { get; set; }
        public string? CoWorkingOperatorPhone { get; set; }
        public TenantContactInfoDto? TenantContactInfo { get; set; }
        public string? DLDPermitNumber { get; set; }
        public string? RefrenceNo { get; set; }
        public string? DTCMPermit { get; set; }
        public OfferingType? OfferingType { get; set; }
        public CompletionStatus? CompletionStatus { get; set; }
        public string? Language { get; set; }
        public string? TitleWithLanguage { get; set; }
        public string? AboutPropertyWithLanguage { get; set; }
        public List<string>? View360Url { get; set; }
        public TaxationMode TaxationMode { get; set; }
        public List<Guid>? ListingOnBehalf { get; set; }

        public List<PropertyOwnerDetailsDto>? PropertyOwnerDetails { get; set; }

        public PossesionType? PossesionType { get; set; }
        public double? SecurityDepositAmount { get; set; }
        public string? SecurityDepositUnit { get; set; }
        public Dictionary<Guid, SourceReferenceInfo>? SourceReferenceIds { get; set; }
    }
    public class BasicPropertyInfoDto
    {
        public Guid? Id { get; set; }
        public string? Title { get; set; }
        public EnquiryType EnquiredFor { get; set; }
        public Guid? PropertyTypeId { get; set; }
        public double NoOfBHK { get; set; }
        public double NoOfBHKs { get; set; }
        public BHKType BHKType { get; set; }
        public PropertyDimensionDto? Dimension { get; set; }
        public string? AboutProperty { get; set; }
        public string? Project { get; set; }
        public PropertyOwnerDetailsDto? OwnerDetails { get; set; }
        public PropertyMonetaryInfoDto? MonetaryInfo { get; set; }
    }
}
