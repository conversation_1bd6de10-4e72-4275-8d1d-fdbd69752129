﻿using Lrb.Application.Project.Mobile;
using Lrb.Application.Project.Mobile.Dtos;
using Lrb.Application.Project.Mobile.Requests;

namespace Lrb.MobileApi.Host.Controllers.v4
{
    [Authorize]
    [Route("api/v4/[controller]")]
    [ApiVersionNeutral]
    public class ProjectController : BaseApiController
    {
        [HttpPost]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Search, LrbResource.Projects)]
        [OpenApiOperation("Search projects using available filters.", "")]
        public async Task<PagedResponse<ViewProjectDto, string>> SearchAsync([FromBody] GetAllProjectRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("count")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get Project Top Level Count")]
        public async Task<Response<ProjectTopLevelCountDto>> GetProjectTopLevelCount([FromBody] GetProjectTopLevelCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("UnitInfos")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get all unit infos using available filters.", "")]
        public async Task<PagedResponse<ViewUnitTypeDto, string>> SearchAsync([FromBody] GetProjectAllUnitInfoRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("blocks")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Projects)]
        [OpenApiOperation("Get all blocks using available filters.", "")]
        public async Task<PagedResponse<ViewBlockDto, string>> SearchAsync([FromBody] GetProjectBlocksRequest request)
        {
            return await Mediator.Send(request);
        }
      

    }
}
