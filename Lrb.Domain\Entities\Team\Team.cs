﻿using Lrb.Domain.Entities.MasterData;

namespace Lrb.Domain.Entities
{
    public class Team : AuditableEntity, IAggregateRoot
    {
        public string Name { get; set; }
        public string? Description { get; set; }
        public List<Guid>? UserIds { get; set; }
        public Guid? Manager { get; set; }
        public List<CustomFilter>? CustomFilters { get; set; }
        public List<CustomMasterLeadStatus>? Statuses { get; set; }
        public int NoOfReassignment { get; set; }
        public TeamConfiguration? Configuration { get; set; }
        public bool? IsRotationEnabled { get; set; }
        public List<TeamConfiguration>? Configurations { get; set; }
        public LeadAssignmentType? LeadAssignmentType { get; set; }
    }
}
