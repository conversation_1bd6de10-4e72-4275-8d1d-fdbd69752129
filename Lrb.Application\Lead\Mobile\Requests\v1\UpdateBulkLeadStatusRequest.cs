﻿using Lrb.Application.Common.GooglePlaces;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Mobile.Dtos.v1;
using Lrb.Application.Lead.Mobile.Mappings;
using Lrb.Application.Lead.Mobile.Mappings.v1;
using Lrb.Application.Lead.Mobile.Requests;
using Lrb.Application.Lead.Mobile.Specs.v1;
using Lrb.Application.ZonewiseLocation.Mobile.Helpers;
using Lrb.Application.ZonewiseLocation.Mobile.Requests;
using Lrb.Application.ZonewiseLocation.Mobile.Specs;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;

namespace Lrb.Application.Lead.Mobile
{
    public class UpdateBulkLeadStatusRequest : IRequest<Response<bool>>
    {
        public List<UpdateLeadStatusRequest>? Requests { get; set; }
    }
    public class UpdateBulkLeadStatusRequestHndler : LeadCommonRequestHandler, IRequestHandler<UpdateBulkLeadStatusRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        private readonly IRepositoryWithEvents<LeadEnquiry> _leadEnquiryRepo;
        private readonly IReadRepository<MasterAreaUnit> _masterAreaUnitRepo;
        private readonly IRepositoryWithEvents<Address> _addressRepo;
        private readonly IRepositoryWithEvents<MasterPropertyType> _propertyTypeRepo;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _leadStatusRepo;
        private readonly IRepositoryWithEvents<PropertyDimension> _dimensionRepo;
        private readonly IRepositoryWithEvents<LeadTag> _leadTagRepo;
        private readonly IRepositoryWithEvents<LeadHistory> _leadHistoryRepo;
        private readonly IUserService _userService;
        private readonly INotificationSenderService _notificationSenderService;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<Domain.Entities.Property> _propertyRepo;
        private readonly IGooglePlacesService _googlePlacesServiceRepo;
        private readonly IRepositoryWithEvents<LeadAppointment> _appointmentRepo;
        private readonly IRepositoryWithEvents<Location> _locationRepo;
        private readonly IMediator _mediator;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        public UpdateBulkLeadStatusRequestHndler(
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepo,
            IRepositoryWithEvents<LeadTag> leadTagRepo,
            IReadRepository<MasterAreaUnit> masterAreaUnitRepo,
            IRepositoryWithEvents<LeadEnquiry> leadEnquiryRepo,
            IRepositoryWithEvents<Address> addressRepo,
            IRepositoryWithEvents<MasterPropertyType> propertyTypeRepo,
            IRepositoryWithEvents<CustomMasterLeadStatus> leadStatusRepo,
            IRepositoryWithEvents<LeadHistory> leadHistoryRepo,
            IRepositoryWithEvents<PropertyDimension> dimensionRepo,
            IUserService userService,
            INotificationSenderService notificationSenderService,
            ICurrentUser currentUser,
            IRepositoryWithEvents<Domain.Entities.Property> propertyRepo,
            IRepositoryWithEvents<LeadAppointment> appointmentRepo,
            IGooglePlacesService googlePlacesServiceRepo,
            IRepositoryWithEvents<Location> locationRepo,
            IMediator mediator,
            ILeadRepositoryAsync leadRepositoryAsync,
            IServiceProvider serviceProvider)
            : base(serviceProvider, nameof(UpdateBulkLeadStatusRequestHndler).ToString(), nameof(Handle).ToString())
        {
            _leadRepo = leadRepo;
            _masterAreaUnitRepo = masterAreaUnitRepo;
            _leadEnquiryRepo = leadEnquiryRepo;
            _addressRepo = addressRepo;
            _propertyTypeRepo = propertyTypeRepo;
            _leadStatusRepo = leadStatusRepo;
            _dimensionRepo = dimensionRepo;
            _leadTagRepo = leadTagRepo;
            _leadHistoryRepo = leadHistoryRepo;
            _userService = userService;
            _notificationSenderService = notificationSenderService;
            _currentUser = currentUser;
            _propertyRepo = propertyRepo;
            _googlePlacesServiceRepo = googlePlacesServiceRepo;
            _appointmentRepo = appointmentRepo;
            _locationRepo = locationRepo;
            _mediator = mediator;
            _leadRepositoryAsync = leadRepositoryAsync;
        }
        public async Task<Response<bool>> Handle(UpdateBulkLeadStatusRequest request, CancellationToken cancellationToken)
        {
            Domain.Entities.GlobalSettings? globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
            foreach (var statusRequest in request.Requests)
            {
                //var existingLead = await _leadRepo.GetByIdAsync(statusRequest.Id);
                var existingLead = (await _leadRepo.ListAsync(new LeadByIdSpec(statusRequest.Id), cancellationToken)).FirstOrDefault();
                if (existingLead != null)
                {
                    if (statusRequest.LeadStatusId == default)
                    {
                        throw new ArgumentException("The Status Id is not valid");
                    }
                    var parentStatus = (await _leadStatusRepo.ListAsync(new ParentLeadStatusSpecs(statusRequest.LeadStatusId), cancellationToken)).FirstOrDefault();
                    if (parentStatus != null)
                    {
                        var child = (await _leadStatusRepo.ListAsync(new LeadStatusSpec(statusRequest.LeadStatusId), cancellationToken));
                        if (parentStatus.Status.Contains("meeting_scheduled"))
                        {
                            var statusId = (child.Where(i => i.Status.Contains("others")).Select(i => i.Id)).FirstOrDefault();
                            statusRequest.LeadStatusId = statusId;
                        }
                        else if (parentStatus.Status.Contains("site_visit_scheduled"))
                        {
                            var statusId = (child.Where(i => i.Status.Contains("first_visit")).Select(i => i.Id)).FirstOrDefault();
                            statusRequest.LeadStatusId = statusId;
                        }
                    }
                    var leadStatus = await _leadStatusRepo.GetByIdAsync(statusRequest.LeadStatusId, cancellationToken);
                    if (leadStatus == null)
                    {
                        throw new ArgumentException("The Status Id is not valid");
                    }
                    #region PickedLead
                    try
                    {
                        existingLead.ShouldUpdatePickedDate = await ShouldUpdatePickedDate(existingLead, request.Adapt<PickedLeadDto>());
                    }
                    catch (Exception ex)
                    {
                        throw;
                    }
                    #endregion
                    existingLead = statusRequest.Adapt(existingLead);
                    #region Updating Meeting and SiteVisit Done
                    try
                    {
                        var appointment = request.Adapt<LeadAppointment>();
                        appointment.Id = Guid.Empty;
                        appointment.UserId = existingLead.AssignTo;
                        switch (statusRequest.MeetingOrSiteVisit)
                        {
                            case AppointmentType.Meeting:
                                existingLead.IsMeetingDone = statusRequest.IsDone;
                                appointment.Type = AppointmentType.Meeting;
                                var places = await _googlePlacesServiceRepo.GetPlaceDetailsByCoordinatesAsync(statusRequest.Latitude, statusRequest.Longitude);
                                var place = places.FirstOrDefault();
                                if (place != null && place.PlaceId != null)
                                {
                                    var address = (await _addressRepo.ListAsync(new GetAddressByPlaceIdSpec(place.PlaceId), cancellationToken)).FirstOrDefault();
                                    if (address != null)
                                    {
                                        existingLead.MeetingLocation = address.Id;
                                        appointment.Location = address;
                                    }
                                    else
                                    {
                                        address = place.Adapt<Address>();
                                        address = await _addressRepo.AddAsync(address);
                                        existingLead.MeetingLocation = address.Id;
                                        appointment.Location = address;
                                    }
                                }
                                break;
                            case AppointmentType.SiteVisit:
                                existingLead.IsSiteVisitDone = statusRequest.IsDone;
                                appointment.Type = AppointmentType.SiteVisit;
                                places = await _googlePlacesServiceRepo.GetPlaceDetailsByCoordinatesAsync(statusRequest.Latitude, statusRequest.Longitude);
                                place = places.FirstOrDefault();
                                if (place != null && place.PlaceId != null)
                                {
                                    var address = (await _addressRepo.ListAsync(new GetAddressByPlaceIdSpec(place.PlaceId), cancellationToken)).FirstOrDefault();
                                    if (address != null)
                                    {
                                        existingLead.SiteLocation = address.Id;
                                        appointment.Location = address;
                                    }
                                    else
                                    {
                                        address = place.Adapt<Address>();
                                        address = await _addressRepo.AddAsync(address);
                                        existingLead.SiteLocation = address.Id;
                                        appointment.Location = address;
                                    }
                                }
                                break;
                        }
                        if (existingLead.Appointments?.Any() ?? false)
                        {
                            existingLead.Appointments.Add(appointment);
                        }
                        else
                        {
                            existingLead.Appointments = new List<LeadAppointment>() { appointment };
                        }
                    }
                    catch (Exception ex)
                    {
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "UpdateBulkLeadStatusRequestHndler -> Handle()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                    #endregion
                    existingLead.CustomLeadStatus = leadStatus;
                    if (statusRequest.PostponedDate != null && statusRequest.PostponedDate != default)
                    {
                        existingLead.ScheduledDate = statusRequest.PostponedDate;
                    }
                    else if (leadStatus != null && leadStatus.BaseId == Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    {
                        existingLead.ScheduledDate = null;
                    }
                    //Address? diffrentLocationAddress = null;
                    //if (statusRequest?.LocationId != null && statusRequest?.LocationId != Guid.Empty)
                    //{
                    //    diffrentLocationAddress = await _addressRepo.FirstOrDefaultAsync(new AddressByLocationIdSpec(new() { statusRequest?.LocationId ?? Guid.Empty }), cancellationToken);
                    //    if (diffrentLocationAddress == null)
                    //    {
                    //        var location = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(statusRequest?.LocationId ?? Guid.Empty), cancellationToken);
                    //        if (location != null)
                    //        {
                    //            diffrentLocationAddress = location.MapToAddress();
                    //            if (diffrentLocationAddress != null)
                    //            {
                    //                diffrentLocationAddress.Id = Guid.Empty;
                    //                diffrentLocationAddress = await _addressRepo.AddAsync(diffrentLocationAddress);
                    //            }
                    //        }
                    //    }
                    //}
                    //else if (!string.IsNullOrEmpty(statusRequest.Address?.PlaceId) && diffrentLocationAddress == null)
                    //{
                    //    diffrentLocationAddress = (await _addressRepo.ListAsync(new GetAddressByPlaceIdSpec(statusRequest.Address.PlaceId), cancellationToken)).FirstOrDefault();
                    //    if (diffrentLocationAddress == null)
                    //    {
                    //        try
                    //        {
                    //            diffrentLocationAddress = (await _googlePlacesServiceRepo.GetPlaceDetailsByPlaceIdAsync(statusRequest.Address.PlaceId))?.Adapt<Address>() ?? null;
                    //        }
                    //        catch (Exception ex)
                    //        {
                    //            var error = new LrbError()
                    //            {
                    //                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    //                ErrorSource = ex?.Source,
                    //                StackTrace = ex?.StackTrace,
                    //                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    //                ErrorModule = "UpdateBulkLeadStatusRequestHndler -> Handle() -> GetPlaceDetailsByPlaceIdAsync()"
                    //            };
                    //            await _leadRepositoryAsync.AddErrorAsync(error);
                    //        }
                    //        diffrentLocationAddress = await _addressRepo.AddAsync(diffrentLocationAddress);
                    //    }

                    //}
                    //else if (diffrentLocationAddress == null && (statusRequest.Address?.Adapt<Address>()?.Validate(out Address? newAddress) ?? false))
                    //{
                    //    if (newAddress != null)
                    //    {
                    //        diffrentLocationAddress = await _addressRepo.AddAsync(newAddress);
                    //        await MapAddressToLocationAndSaveAsync(diffrentLocationAddress);
                    //    }
                    //}
                    List<Address> diffrentLocationAddresses = new List<Address>();
                    if(statusRequest?.Addresses?.Any() ?? false)
                    {
                        foreach (var address in statusRequest.Addresses)
                        {
                            Address? diffrentLocationAddress = null;
                            if (statusRequest?.LocationId != null && statusRequest?.LocationId != Guid.Empty)
                            {
                                diffrentLocationAddress = await _addressRepo.FirstOrDefaultAsync(new AddressByLocationIdSpec(new() { statusRequest?.LocationId ?? Guid.Empty }), cancellationToken);
                                if (diffrentLocationAddress == null)
                                {
                                    var location = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(statusRequest?.LocationId ?? Guid.Empty), cancellationToken);
                                    if (location != null)
                                    {
                                        diffrentLocationAddress = location.MapToAddress();
                                        if (diffrentLocationAddress != null)
                                        {
                                            diffrentLocationAddress.Id = Guid.Empty;
                                            diffrentLocationAddress = await _addressRepo.AddAsync(diffrentLocationAddress);
                                        }
                                    }
                                }
                            }
                            else if (!string.IsNullOrEmpty(address?.PlaceId) && diffrentLocationAddress == null)
                            {
                                diffrentLocationAddress = (await _addressRepo.ListAsync(new GetAddressByPlaceIdSpec(address.PlaceId), cancellationToken)).FirstOrDefault();
                                if (diffrentLocationAddress == null)
                                {
                                    try
                                    {
                                        diffrentLocationAddress = (await _googlePlacesServiceRepo.GetPlaceDetailsByPlaceIdAsync(address.PlaceId))?.Adapt<Address>() ?? null;
                                    }
                                    catch (Exception ex)
                                    {
                                        var error = new LrbError()
                                        {
                                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                            ErrorSource = ex?.Source,
                                            StackTrace = ex?.StackTrace,
                                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                            ErrorModule = "UpdateBulkLeadStatusRequestHndler -> Handle() -> GetPlaceDetailsByPlaceIdAsync()"
                                        };
                                        await _leadRepositoryAsync.AddErrorAsync(error);
                                    }
                                    diffrentLocationAddress = await _addressRepo.AddAsync(diffrentLocationAddress);
                                }

                            }
                            else if (diffrentLocationAddress == null && (address?.Adapt<Address>()?.ValidateAddress(out Address? newAddress) ?? false))
                            {
                                if (newAddress != null)
                                {
                                    diffrentLocationAddress = await _addressRepo.AddAsync(newAddress);
                                    await MapAddressToLocationAndSaveAsync(diffrentLocationAddress);
                                }
                            }

                            diffrentLocationAddresses.Add(diffrentLocationAddress);
                        }

                    }
                    //if (existingLead?.Enquiries?.FirstOrDefault(i => i.IsPrimary) != null && diffrentLocationAddress != null)
                    //{
                    //    existingLead.Enquiries.FirstOrDefault(i => i.IsPrimary).Address = diffrentLocationAddress;
                    //}
                    if ((existingLead?.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.Addresses?.Any() ?? false) && (diffrentLocationAddresses?.Any() ?? false))
                    {
                        existingLead.Enquiries.FirstOrDefault(i => i.IsPrimary).Addresses = diffrentLocationAddresses;
                    }
                    #region projects
                    if (statusRequest.ProjectsList?.Any() ?? false)
                    {
                        List<Lrb.Domain.Entities.Project>? projects = new();
                        statusRequest.ProjectsList = statusRequest.ProjectsList.Where(i => !string.IsNullOrWhiteSpace(i)).ToList();
                        if (statusRequest?.ProjectsList?.Any() ?? false)
                        {
                            foreach (var newProject in statusRequest.ProjectsList)
                            {
                                Lrb.Domain.Entities.Project? existingProject = (await _projectRepo.ListAsync(new GetProjectByIdSpecsV2(newProject), cancellationToken)).FirstOrDefault();
                                if (existingProject != null)
                                {
                                    projects.Add(existingProject);
                                }
                                else
                                {
                                    Domain.Entities.Project ProjectsV2 = new() { Name = newProject };
                                    ProjectsV2 = await _projectRepo.AddAsync(ProjectsV2, cancellationToken);
                                    projects.Add(ProjectsV2);
                                }
                            }
                        }
                        existingLead.Projects = projects;
                    }
                    #endregion
                    #region Properties

                    if (statusRequest?.PropertiesList?.Any() ?? false)
                    {
                        List<Domain.Entities.Property>? properties = new();
                        foreach (var newProperty in statusRequest.PropertiesList)
                        {
                            var existingProperty = (await _propertyRepo.ListAsync(new GetPropertyByTitleSpec(newProperty), cancellationToken)).FirstOrDefault();
                            if (existingProperty != null)
                            {
                                properties.Add(existingProperty);
                            }
                            else
                            {
                                Domain.Entities.Property property = new() { Title = newProperty };
                                property = await _propertyRepo.AddAsync(property, cancellationToken);
                                properties.Add(property);
                            }
                        }
                        existingLead.Properties = properties;
                    }
                    #endregion
                    var existingBookedDate = existingLead.BookedDate;
                    var existingBookedBy = existingLead.BookedBy;
                    var baseLeadStatus = await _leadStatusRepo?.GetByIdAsync(leadStatus.BaseId, cancellationToken);
                    if (baseLeadStatus != null && baseLeadStatus.Status == "booked" || existingLead.CustomLeadStatus?.Status == "booked")
                    {
                        if (statusRequest?.BookedDate != null)
                        {
                            existingLead.BookedDate = statusRequest?.BookedDate;
                        }
                        else
                        {
                            existingLead.BookedDate = DateTime.UtcNow;
                        }
                        existingLead.BookedBy = _currentUser.GetUserId();
                        //=============
                        var userName = await _userService.GetAsync(existingLead.BookedBy.ToString() ?? string.Empty, cancellationToken);
                        //bookedDetail.LeadId = existingLead?.Id ?? Guid.Empty;              
                        // List<Lrb.Domain.Entities.LeadBookedDetail> bookDetail = new();
                        LeadBookedDetail bookedDetail = new();
                        bookedDetail.LeadId = existingLead?.Id ?? Guid.Empty;
                        bookedDetail.BookedDate = existingLead?.BookedDate;
                        bookedDetail.BookedBy = existingLead?.BookedBy;
                        bookedDetail.BookedByUser = userName.FirstName + " " + userName.LastName;
                        bookedDetail.BookedUnderName = existingLead?.BookedUnderName;
                        bookedDetail.UserId = existingLead?.AssignTo ?? Guid.Empty;
                        bookedDetail.SoldPrice = statusRequest?.SoldPrice;
                        bookedDetail.Notes = statusRequest?.Notes;
                        bookedDetail.ProjectsList = statusRequest?.ProjectsList;
                        bookedDetail.PropertiesList = statusRequest?.PropertiesList;

                        existingLead?.BookedDetails?.Add(bookedDetail);
                        await _leadBookedDetailRepo.AddAsync(bookedDetail);
                    }

                    else
                    {
                        existingLead.BookedDate = existingBookedDate;
                        existingLead.BookedBy = existingBookedBy;
                    }

                    await _leadRepo.UpdateAsync(existingLead);
                    var fullLead = (await _leadRepo.ListAsync(new LeadByIdSpec(statusRequest.Id), cancellationToken))?.FirstOrDefault();
                    var leadDto = fullLead?.Adapt<ViewLeadDto>();
                    await leadDto?.SetUsersInViewLeadDtoAsync(_userService, cancellationToken);
                    var leadHsitory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                    var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(fullLead.Id, fullLead.AssignTo));
                    if (existingLeadHistory != null)
                    {
                        await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHsitory));
                    }
                    else
                    {
                        await _leadHistoryRepo.AddAsync(leadHsitory);
                    }
                    try
                    {
                        await existingLead.SendLeadStatusChangeNotificationsAsync(leadDto, _notificationSenderService, globalSettings, _currentUser.GetUserId());
                        if (existingLead.AssignTo == Guid.Empty)
                        {
                           await _notificationSenderService.DeleteScheduledNotificationsAsync(existingLead);
                        }
                    }
                    catch (Exception ex)
                    {
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "UpdateBulkLeadStatusRequestHndler -> Handle() -> SendLeadStatusChangeNotificationsAsync()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                }
            }
            return new Response<bool>(true);
        }
        private async Task MapAddressToLocationAndSaveAsync(Address address)
        {
            var location = address.MapToLocationRequest();
            if (location != null)
            {
                var locationRes = await _mediator.Send(location.Adapt<AddLocationRequest>());
                var createdLocation = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(locationRes.Data), default);
                address.Location = createdLocation;
                await _addressRepo.UpdateAsync(address);
            }
        }
    }
}
