﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.Utils;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using Serilog;

namespace Lrb.Application.DataManagement.Web.Request.Bulk_Upload
{
    public class GetExcelUploadRequest : IRequest<Response<FileColumnProspectDto>>
    {
        public IFormFile? File { get; set; }
        public GetExcelUploadRequest(IFormFile file)
        {
            File = file;
        }
    }

    public class GetExcelUploadRequestHandler : IRequestHandler<GetExcelUploadRequest, Response<FileColumnProspectDto>>
    {
        private readonly IBlobStorageService _blobStorageService;
        private readonly ILogger _logger;
        public GetExcelUploadRequestHandler(IBlobStorageService blobStorageService, ILogger logger)
        {
            _blobStorageService = blobStorageService;
            _logger = logger;
        }
        public async Task<Response<FileColumnProspectDto>> Handle(GetExcelUploadRequest request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.Information("GetExcelUploadRequestHandler -> Handle: " + JsonConvert.SerializeObject(request));
                var file = request.File;
                if (file == null)
                {
                    throw new ArgumentNullException(nameof(file));
                }
                var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "prd-leadratblack", "Prospects", file);
                List<string> columns = new List<string>();
                Dictionary<string, List<string>> multiSheetColumn = new();
                if (key.Split(".").LastOrDefault() == "csv")
                {
                    columns = await CSVHelper.GetCSVColumns(file);
                    multiSheetColumn.Add("Default", columns);
                }
                else
                {
                    columns = EPPlusExcelHelper.GetFileColumns(file);
                    multiSheetColumn = EPPlusExcelHelper.GetFileColumnsOfMultiSheets(file);
                }
                FileColumnProspectDto excelColumnsViewModel = new()
                {
                    S3BucketKey = key,
                    ColumnNames = columns,
                    MultiSheetColumnNames = multiSheetColumn,
                };
                return new(excelColumnsViewModel);
            }
            catch (Exception ex)
            {
                _logger.Error("GetExcelColumnsUsingEPPlusRequestHandler -> Error: " + JsonConvert.SerializeObject(ex));
                throw;
            }
        }
    }
}
