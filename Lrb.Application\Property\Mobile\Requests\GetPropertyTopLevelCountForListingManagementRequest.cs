﻿using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.Property.Mobile.Dtos;
using Lrb.Application.Property.Mobile.Specs;
using Lrb.Application.Property.Web;
using Lrb.Application.Property.Web.Requests;
using Lrb.Domain.Entities.AmenitiesAttributes;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;

namespace Lrb.Application.Property.Mobile
{
    public class GetPropertyTopLevelCountForListingManagementRequest : GetAllListingManagementParameter, IRequest<Response<GetPropertyCountForListingManagementDto>>
    {
    }

    public class GetPropertyTopLevelCountForListingManagementRequestHandler : IRequestHandler<GetPropertyTopLevelCountForListingManagementRequest, Response<GetPropertyCountForListingManagementDto>>
    {
        private readonly IReadRepository<Domain.Entities.Property> _propertyRepo;
        private readonly IReadRepository<CustomMasterAttribute> _masterPropertyAttributeRepo;
        private readonly IReadRepository<CustomMasterAmenity> _masterPropertyAmenityRepo;
        private readonly IReadRepository<MasterPropertyType> _masterPropertyTypeRepo;
        private readonly IReadRepository<MasterAreaUnit> _masterAreaUnitRepo;
        private readonly IReadRepository<PropertyDimension> _propertyDimensionRepo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        private readonly IPropertyRepository _propertyRepository;

        public GetPropertyTopLevelCountForListingManagementRequestHandler(
            IReadRepository<Domain.Entities.Property> propertyRepo,
            IReadRepository<CustomMasterAttribute> masterPropertyAttributeRepo,
            IReadRepository<CustomMasterAmenity> masterPropertyAmenityRepo,
            IReadRepository<MasterPropertyType> masterPropertyTypeRepo,
            IReadRepository<MasterAreaUnit> masterAreaUnitRepo,
            IReadRepository<PropertyDimension> propertyDimensionRepo,
            ILeadRepositoryAsync leadRepositoryAsync,
            IDapperRepository dapperRepository,
            ICurrentUser currentUser,
            IPropertyRepository propertyRepository
            )
        {
            _propertyRepo = propertyRepo;
            _masterPropertyAttributeRepo = masterPropertyAttributeRepo;
            _masterPropertyAmenityRepo = masterPropertyAmenityRepo;
            _masterPropertyTypeRepo = masterPropertyTypeRepo;
            _masterAreaUnitRepo = masterAreaUnitRepo;
            _propertyDimensionRepo = propertyDimensionRepo;
            _leadRepositoryAsync = leadRepositoryAsync;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
            _propertyRepository = propertyRepository;
        }
        public async Task<Response<GetPropertyCountForListingManagementDto>> Handle(GetPropertyTopLevelCountForListingManagementRequest request, CancellationToken cancellationToken)
        {
            if (request != null && request.Permission == ViewAssignmentsPermission.None)
            {
                return new (null);
            }
            CustomMasterAttribute? masterPropertyAttribute = new();
            CustomMasterAmenity? masterPropertyAmenity = new();
            MasterPropertyType? masterPropertyType = new();
            if (!string.IsNullOrWhiteSpace(request.PropertySearch))
            {
                masterPropertyAttribute = (await _masterPropertyAttributeRepo.ListAsync(new GetMasterPropertyAttributeSpec(request?.PropertySearch?.Trim().ToLower() ?? string.Empty), cancellationToken)).FirstOrDefault();
                masterPropertyAmenity = (await _masterPropertyAmenityRepo.ListAsync(new GetMasterPropertyAmenitySpec(request?.PropertySearch?.Trim().ToLower() ?? string.Empty), cancellationToken)).FirstOrDefault();
                masterPropertyType = (await _masterPropertyTypeRepo.ListAsync(new GetMasterPropertyTypeSpec(request?.PropertySearch?.Trim().ToLower() ?? string.Empty), cancellationToken)).FirstOrDefault();
            }

            List<Guid>? propertyDimensionIds = new();
            //if (request != null && request.PropertySize != null && request.PropertySize.AreaUnitId != default)
            //{
            //    var masterAreaUnit = await _masterAreaUnitRepo.GetByIdAsync(request.PropertySize.AreaUnitId);
            //    request.PropertySize.ConversionFactor = masterAreaUnit?.ConversionFactor ?? default;
            //    var area = request.PropertySize.Area * request.PropertySize.ConversionFactor;
            //    propertyDimensionIds = (await _propertyDimensionRepo.ListAsync()).Where(i => (i.Area * PropertySearchHelper.GetConversionFactor(i.AreaUnitId, _masterAreaUnitRepo).Result) == area).Select(i => i.Id).ToList();
            //}
            //if (request != null && request.PropertySize != null && request.PropertySize.CarpetAreaId != default)
            //{
            //    var masterAreaUnit = await _masterAreaUnitRepo.GetByIdAsync(request.PropertySize.CarpetAreaId);
            //    request.PropertySize.ConversionFactor = masterAreaUnit?.ConversionFactor ?? default;
            //    var Carpetarea = request.PropertySize.CarpetArea * request.PropertySize.ConversionFactor;
            //    propertyDimensionIds = (await _propertyDimensionRepo.ListAsync()).Where(i => (i.CarpetArea * PropertySearchHelper.GetConversionFactor(i.CarpetAreaId ?? Guid.Empty, _masterAreaUnitRepo).Result) == Carpetarea).Select(i => i.Id).ToList();
            //}

            //if (request != null && request.PropertySize != null && request.PropertySize.BuildUpAreaId != default)
            //{
            //    var masterAreaUnit = await _masterAreaUnitRepo.GetByIdAsync(request.PropertySize.BuildUpAreaId);
            //    request.PropertySize.ConversionFactor = masterAreaUnit?.ConversionFactor ?? default;
            //    var builduparea = request.PropertySize.BuildUpArea * request.PropertySize.ConversionFactor;
            //    propertyDimensionIds = (await _propertyDimensionRepo.ListAsync()).Where(i => (i.BuildUpArea * PropertySearchHelper.GetConversionFactor(i.BuildUpAreaId ?? Guid.Empty, _masterAreaUnitRepo).Result) == builduparea).Select(i => i.Id).ToList();
            //}
            //if (request != null && request.PropertySize != null && request.PropertySize.SaleableAreaId != default)
            //{
            //    var masterAreaUnit = await _masterAreaUnitRepo.GetByIdAsync(request.PropertySize.SaleableAreaId);
            //    request.PropertySize.ConversionFactor = masterAreaUnit?.ConversionFactor ?? default;
            //    var saleblearea = request.PropertySize.SaleableArea * request.PropertySize.ConversionFactor;
            //    propertyDimensionIds = (await _propertyDimensionRepo.ListAsync()).Where(i => (i.SaleableArea * PropertySearchHelper.GetConversionFactor(i.SaleableAreaId ?? Guid.Empty, _masterAreaUnitRepo).Result) == saleblearea).Select(i => i.Id).ToList();
            //}
            //if (request != null && request.PropertySize != null && request.PropertySize.NetAreaUnitId != default)
            //{
            //    var masterAreaUnit = await _masterAreaUnitRepo.GetByIdAsync(request.PropertySize.NetAreaUnitId);
            //    request.PropertySize.ConversionFactor = masterAreaUnit?.ConversionFactor ?? default;
            //    var netAreaId = request.PropertySize.NetArea * request.PropertySize.ConversionFactor;
            //    propertyDimensionIds = (await _propertyDimensionRepo.ListAsync()).Where(i => (i.NetArea * PropertySearchHelper.GetConversionFactor(i.NetAreaUnitId ?? Guid.Empty, _masterAreaUnitRepo).Result) == netAreaId).Select(i => i.Id).ToList();
            //}
            var tenantId = _currentUser.GetTenant();
            var currentUserId = _currentUser.GetUserId();
            List<Guid>? userIds = new();
            List<Guid>? filterIds = new();
            List<Guid>? teamUserIds = new();
            bool showAllProperties = false;
            try
            {
                switch (request.Permission)
                {
                    case ViewAssignmentsPermission.View:
                        if (request.UserIds?.Any() ?? false)
                        {
                            filterIds.AddRange(request.UserIds);
                            if (request.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty))?.ToList() ?? new();
                                filterIds.AddRange(teamUserIds);
                            }
                            userIds.AddRange(filterIds);
                        }
                        else
                        {
                            userIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                            showAllProperties = true;
                        }
                        break;
                    case ViewAssignmentsPermission.ViewAssigned:
                        userIds.Add(currentUserId);
                        break;
                    default:
                        if (request.UserIds?.Any() ?? false)
                        {
                            filterIds.AddRange(request.UserIds);
                            if (request.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty))?.ToList() ?? new();
                                filterIds.AddRange(teamUserIds);
                            }
                            userIds.AddRange(filterIds);
                        }
                        else
                        {
                            userIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                            showAllProperties = true;
                        }
                        break;
                }

            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "GetAllPropertyRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            List<int> noOfAttributes = Enumerable.Range(1, 5).ToList();
            NumericAttributesDto numericAttributeDto = InitializationOfNumericAttributes(noOfAttributes, request.Adapt<GetAllPropertyForListingManagementRequest>());
            List<Guid> propertyIds = new();
            if (request.MinLeadCount != null || request.MaxLeadCount != null ||
                request.MinProspectCount != null || request.MaxProspectCount != null)
            {
                var property = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<PropertyIdsDto>("LeadratBlack", "Lead&Prospects_PropertiesAssociatedCountFilter",
                    new
                    {
                        tenantid = tenantId,
                        minprospectcount = request.MinProspectCount,
                        maxprospectcount = request.MaxProspectCount,
                        minleadcount = request.MinLeadCount,
                        maxleadcount = request.MaxLeadCount
                    })).FirstOrDefault()?.PropertyIds ?? new List<Guid>();
                propertyIds = property.ToList();
            }
            List<CustomPropertyAttributeDto> attributes = new();
            if (request?.NoOfFloors != null ||  request?.NoOfKitchens != null || request?.NoOfUtilites != null || request?.NoOfBedrooms != null || request?.NoOfLivingrooms != null || request?.NoOfBalconies != null || request?.NoOfBathrooms != null || request?.Parking != null)
            {
                attributes = await _dapperRepository.GetAttributeDetails(tenantId ?? string.Empty);
            }
            Guid? propertyId = null;
            if (request?.ReferenceNo != null)
            {
                propertyId = await _dapperRepository.GetPropertyListingIdBySerialNoOrCustomRefNo(request?.ReferenceNo ?? string.Empty, tenantId);
            }
            //var properties = await _propertyRepo.ListAsync(new PropertyCountForListingManagementSpecs(request.Adapt<GetAllPropertyForListingManagementRequest>(), masterPropertyAttribute?.Id ?? null, masterPropertyAmenity?.Id ?? null, masterPropertyType?.Id ?? null, propertyDimensionIds, numericAttributeDto, userIds, showAllProperties));
            var properties = await _propertyRepository.GetAllPropertiesCountForMobileListingManagementAsync(request, masterPropertyAttribute?.Id ?? null, masterPropertyAmenity?.Id ?? null, masterPropertyType?.Id ?? null, propertyDimensionIds, numericAttributeDto, userIds, showAllProperties, tenantId, propertyIds, attributes, propertyId);
            request.PropertyVisiblity = PropertyVisiblity.Archived;
            //var deletedProperty = await _propertyRepo.ListAsync(new PropertyCountForListingManagementSpecs(request.Adapt<GetAllPropertyForListingManagementRequest>(), masterPropertyAttribute?.Id ?? null, masterPropertyAmenity?.Id ?? null, masterPropertyType?.Id ?? null, propertyDimensionIds, numericAttributeDto, userIds, showAllProperties));
            var deletedProperty = await _propertyRepository.GetAllPropertiesCountForMobileListingManagementAsync(request, masterPropertyAttribute?.Id ?? null, masterPropertyAmenity?.Id ?? null, masterPropertyType?.Id ?? null, propertyDimensionIds, numericAttributeDto, userIds, showAllProperties, tenantId, propertyIds, attributes, propertyId);
            var countDto = new GetPropertyCountForListingManagementDto()
            {
                AllCount = properties.Count(),
                ApprovedCount = properties.Where(i => i.ListingStatus == ListingStatus.Approved).Count(),
                DraftCount = properties.Where(i => i.ListingStatus == ListingStatus.Draft).Count(),
                RefusedCount = properties.Where(i => i.ListingStatus == ListingStatus.Refused).Count(),
                SoldCount = properties.Where(i => i.Status == PropertyStatus.Sold).Count(),
                ArchivedCount = deletedProperty.Count(),
            };
            return new(countDto);
        }

        private NumericAttributesDto InitializationOfNumericAttributes(List<int> noOfAttributes, GetAllPropertyForListingManagementRequest request)
        {
            return new NumericAttributesDto
            {
                NoOfFloor = FilterNumericAttributes(request.NoOfFloor, noOfAttributes),
                NoOfBathrooms = FilterNumericAttributes(request.NoOfBathrooms, noOfAttributes),
                NoOfBedrooms = FilterNumericAttributes(request.NoOfBedrooms, noOfAttributes),
                NoOfKitchens = FilterNumericAttributes(request.NoOfKitchens, noOfAttributes),
                NoOfUtilites = FilterNumericAttributes(request.NoOfUtilites, noOfAttributes),
                NoOfLivingrooms = FilterNumericAttributes(request.NoOfLivingrooms, noOfAttributes),
                NoOfBalconies = FilterNumericAttributes(request.NoOfBalconies, noOfAttributes),
                NoOfFloors = FilterNumericAttributesV1(request.NoOfFloors, noOfAttributes),
                Parking = FilterNumericAttributes(request.Parking, noOfAttributes),


            };
        }

        private NoOfAttributeFilterDto FilterNumericAttributesV1(List<string>? requestValues, List<int> noOfAttributes)
        {
            NoOfAttributeFilterDto noOfAttributesDto = new NoOfAttributeFilterDto();
            var noOfFloorFilterList = new List<int>();
            var selectedAttributes = new List<string>();
            if (requestValues != null)
            {
                if (requestValues.Contains("Ground Floor"))
                {
                    selectedAttributes.Add("Ground Floor");
                }
                if (requestValues.Contains("5"))
                {
                    noOfFloorFilterList = noOfAttributes.Where(i => !requestValues.Contains(i.ToString())).ToList();
                    selectedAttributes.AddRange(noOfFloorFilterList.Select(i => i.ToString()));
                    noOfAttributesDto.IsMaxValueIncluded = true;
                }
                else
                {
                    noOfFloorFilterList = noOfAttributes.Where(i => requestValues.Contains(i.ToString())).ToList();
                    selectedAttributes.AddRange(noOfFloorFilterList.Select(i => i.ToString()));
                    noOfAttributesDto.IsMaxValueIncluded = false;
                }
                noOfAttributesDto.NoOfAttributes = selectedAttributes;
            }

            return noOfAttributesDto;
        }

        private NoOfAttributeFilterDto FilterNumericAttributes(List<int>? requestValues, List<int> noOfAttributes)
        {
            NoOfAttributeFilterDto noOfAttributesDto = new NoOfAttributeFilterDto();
            var noOfFloorFilterList = new List<int>();

            if (requestValues != null)
            {
                if (requestValues.Contains(5))
                {
                    noOfFloorFilterList = noOfAttributes.Where(i => !requestValues.Contains(i)).ToList();
                    noOfAttributesDto.NoOfAttributes = noOfFloorFilterList.Select(i => i.ToString()).ToList();
                    noOfAttributesDto.IsMaxValueIncluded = true;
                }
                else
                {
                    noOfFloorFilterList = noOfAttributes.Where(i => requestValues.Contains(i)).ToList();
                    noOfAttributesDto.NoOfAttributes = noOfFloorFilterList.Select(i => i.ToString()).ToList();
                    noOfAttributesDto.IsMaxValueIncluded = false;
                }
            }

            return noOfAttributesDto;
        }
    }
}
