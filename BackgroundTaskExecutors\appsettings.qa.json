﻿{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "DatabaseSettings": {
    "DBProvider": "postgresql",
    //"ConnectionString": "Host=ls-d76bceb7cc3cad167845c334973fa18c0d93b476.coluzekxwdtv.ap-south-1.rds.amazonaws.com;Port=5432;Database=leadratBlackApp;Username=dbmasteruser;Password=********************************;Timeout=300;CommandTimeout=300;"
    "ConnectionString": "Host=lrb-qa-new.postgres.database.azure.com;Port=5432;Database=leadratBlackApp;Username=dbmasteruser;Password=********************************;Pooling=true;MinPoolSize=3;MaxPoolSize=5000;"
  }
}